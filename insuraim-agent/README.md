# Vue 3 + Vite

This template should help get you started developing with Vue 3 in Vite. The template uses Vue 3 `<script setup>` SFCs, check out the [script setup docs](https://v3.vuejs.org/api/sfc-script-setup.html#sfc-script-setup) to learn more.

Learn more about IDE Support for Vue in the [Vue Docs Scaling up Guide](https://vuejs.org/guide/scaling-up/tooling.html#ide-support).

## 国际化使用规范

### 1. 国际化翻译文件存放规范

项目使用 Vue I18n 实现国际化功能，翻译文件组织结构如下：

```
src/I18n/
├── index.js              # 国际化主入口文件
├── plugin.js             # 国际化插件和辅助函数
├── useI18n.js            # 组合式API钩子
└── message/              # 翻译文件目录
    ├── index.js          # 汇总所有翻译
    ├── common/           # 通用翻译
    │   └── index.js      # 通用翻译内容
    ├── company/          # 公司模块翻译
    │   └── index.js      # 公司相关翻译内容
    └── ...               # 其他业务模块翻译
```

#### 翻译文件组织原则：

1. **模块化管理**：按功能模块分类存放翻译文件
2. **分语言存储**：每个模块的翻译文件包含所有支持的语言
3. **命名规范**：
   - 模块文件夹使用小写英文
   - 翻译键名采用点号层级结构，如`company.name`
4. **新增模块**：创建新的功能模块时，在`message`目录下创建对应文件夹并实现`index.js`

### 2. 国际化翻译使用教程

#### 在模板中使用

```vue
<template>
  <!-- 基础用法 -->
  <div>{{ $t('company.name') }}</div>
  
  <!-- 带参数的翻译 -->
  <div>{{ $tp('common.totalItems', { total: 100 }) }}</div>
  
  <!-- 指令用法 -->
  <span v-t="'common.loading'"></span>
  
  <!-- 属性绑定 -->
  <a-input :placeholder="$t('common.search')" />
</template>
```

#### 在JavaScript中使用

```js
// 组合式API (推荐)
import { useI18n } from 'vue-i18n';

export default {
  setup() {
    // 获取国际化功能
    const { t, locale } = useI18n();
    
    // 基础翻译
    console.log(t('company.name'));
    
    // 带参数翻译
    const message = t('common.totalItems', { total: 100 });
    
    // 切换语言
    function changeLanguage(lang) {
      locale.value = lang;
    }
    
    return { t, changeLanguage };
  }
};
```

#### 全局辅助函数

通过全局混入，所有组件都可以访问以下功能：

```js
// 当前语言
console.log(this.currentLanguage); // 计算属性

// 可用语言列表
console.log(this.availableLanguages); // 计算属性

// 语言选项（用于下拉菜单）
console.log(this.languageOptions); // 计算属性

// 切换语言
this.toggleLanguage(); // 切换到下一种语言
this.toggleLanguage('en-US'); // 切换到指定语言

// 获取语言名称
this.getLanguageName('zh-CN'); // 返回"简体中文"
```

#### 在非Vue文件中使用

```js
import { t, getCurrentLocale, setLocale } from '@/I18n';

// 基础翻译
console.log(t('company.name'));

// 带参数翻译
console.log(t('common.totalItems', { total: 100 }));

// 获取当前语言
const currentLang = getCurrentLocale();

// 设置语言
setLocale('en-US');
```

#### 添加新的翻译

1. 找到对应模块的翻译文件（如`src/I18n/message/company/index.js`）
2. 在所有语言版本中添加相同的翻译键
3. 确保每种语言都有对应的翻译文本

```js
// 在company/index.js中添加新翻译
export default {
  'zh-CN': {
    company: {
      // 添加新翻译键
      newFeature: '新功能名称',
      // ...
    }
  },
  'en-US': {
    company: {
      // 确保所有语言都有对应翻译
      newFeature: 'New Feature Name',
      // ...
    }
  },
  'zh-HK': {
    company: {
      // 繁体中文翻译
      newFeature: '新功能名稱',
      // ...
    }
  }
};
```

#### 最佳实践

1. 使用嵌套结构管理翻译键，避免命名冲突
2. 在组件中使用`useI18n()`钩子获取翻译功能
3. 翻译键命名使用模块前缀，如`company.feature`
4. 对于通用翻译，放在`common`模块下
5. 翻译中的参数使用大括号占位符，如`{total}`
6. 确保所有语言的翻译键保持一致


