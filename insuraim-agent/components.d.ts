/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AAlert: typeof import('ant-design-vue/es')['Alert']
    AAnchor: typeof import('ant-design-vue/es')['Anchor']
    AAvatar: typeof import('ant-design-vue/es')['Avatar']
    ABadge: typeof import('ant-design-vue/es')['Badge']
    AButton: typeof import('ant-design-vue/es')['Button']
    AButtonGroup: typeof import('ant-design-vue/es')['ButtonGroup']
    ACard: typeof import('ant-design-vue/es')['Card']
    ACardMeta: typeof import('ant-design-vue/es')['CardMeta']
    ACarousel: typeof import('ant-design-vue/es')['Carousel']
    ACascader: typeof import('ant-design-vue/es')['Cascader']
    ACheckbox: typeof import('ant-design-vue/es')['Checkbox']
    ACheckboxGroup: typeof import('ant-design-vue/es')['CheckboxGroup']
    ACol: typeof import('ant-design-vue/es')['Col']
    ACollapse: typeof import('ant-design-vue/es')['Collapse']
    ACollapsePanel: typeof import('ant-design-vue/es')['CollapsePanel']
    AConfigProvider: typeof import('ant-design-vue/es')['ConfigProvider']
    ADatePicker: typeof import('ant-design-vue/es')['DatePicker']
    ADescriptions: typeof import('ant-design-vue/es')['Descriptions']
    ADescriptionsItem: typeof import('ant-design-vue/es')['DescriptionsItem']
    ADivider: typeof import('ant-design-vue/es')['Divider']
    ADrawer: typeof import('ant-design-vue/es')['Drawer']
    ADropdown: typeof import('ant-design-vue/es')['Dropdown']
    AEmpty: typeof import('ant-design-vue/es')['Empty']
    AForm: typeof import('ant-design-vue/es')['Form']
    AFormItem: typeof import('ant-design-vue/es')['FormItem']
    AiAnalysisReport: typeof import('./src/components/needs-analysis/AiAnalysisReport.vue')['default']
    AImage: typeof import('ant-design-vue/es')['Image']
    AInput: typeof import('ant-design-vue/es')['Input']
    AInputNumber: typeof import('ant-design-vue/es')['InputNumber']
    AInputPassword: typeof import('ant-design-vue/es')['InputPassword']
    AInputSearch: typeof import('ant-design-vue/es')['InputSearch']
    AList: typeof import('ant-design-vue/es')['List']
    AListItem: typeof import('ant-design-vue/es')['ListItem']
    AListItemMeta: typeof import('ant-design-vue/es')['ListItemMeta']
    AMenu: typeof import('ant-design-vue/es')['Menu']
    AMenuItem: typeof import('ant-design-vue/es')['MenuItem']
    AModal: typeof import('ant-design-vue/es')['Modal']
    APagination: typeof import('ant-design-vue/es')['Pagination']
    APopconfirm: typeof import('ant-design-vue/es')['Popconfirm']
    AProgress: typeof import('ant-design-vue/es')['Progress']
    AQrcode: typeof import('ant-design-vue/es')['QRCode']
    ARadio: typeof import('ant-design-vue/es')['Radio']
    ARadioButton: typeof import('ant-design-vue/es')['RadioButton']
    ARadioGroup: typeof import('ant-design-vue/es')['RadioGroup']
    ARangePicker: typeof import('ant-design-vue/es')['RangePicker']
    AResult: typeof import('ant-design-vue/es')['Result']
    ARow: typeof import('ant-design-vue/es')['Row']
    ASelect: typeof import('ant-design-vue/es')['Select']
    ASelectOption: typeof import('ant-design-vue/es')['SelectOption']
    ASlider: typeof import('ant-design-vue/es')['Slider']
    ASpace: typeof import('ant-design-vue/es')['Space']
    ASpin: typeof import('ant-design-vue/es')['Spin']
    AStep: typeof import('ant-design-vue/es')['Step']
    ASteps: typeof import('ant-design-vue/es')['Steps']
    ASwitch: typeof import('ant-design-vue/es')['Switch']
    ATable: typeof import('ant-design-vue/es')['Table']
    ATableColumn: typeof import('ant-design-vue/es')['TableColumn']
    ATabPane: typeof import('ant-design-vue/es')['TabPane']
    ATabs: typeof import('ant-design-vue/es')['Tabs']
    ATag: typeof import('ant-design-vue/es')['Tag']
    ATextarea: typeof import('ant-design-vue/es')['Textarea']
    ATimeline: typeof import('ant-design-vue/es')['Timeline']
    ATimelineItem: typeof import('ant-design-vue/es')['TimelineItem']
    ATooltip: typeof import('ant-design-vue/es')['Tooltip']
    AUpload: typeof import('ant-design-vue/es')['Upload']
    AUploadDragger: typeof import('ant-design-vue/es')['UploadDragger']
    CategoryTree: typeof import('./src/components/CategoryTree.vue')['default']
    CategoryTreeNode: typeof import('./src/components/CategoryTreeNode.vue')['default']
    FlashNewsList: typeof import('./src/components/news/FlashNewsList.vue')['default']
    ForwardPreview: typeof import('./src/components/news/ForwardPreview.vue')['default']
    IModal: typeof import('./src/components/iModal.vue')['default']
    IPicPreview: typeof import('./src/components/IPicPreview.vue')['default']
    ITable: typeof import('./src/components/iTable.vue')['default']
    ITalbe: typeof import('./src/components/iTalbe.vue')['default']
    LanguageSwitcher: typeof import('./src/components/LanguageSwitcher.vue')['default']
    Navbar: typeof import('./src/components/Navbar.vue')['default']
    NewsCard: typeof import('./src/components/news/NewsCard.vue')['default']
    NewsHeader: typeof import('./src/components/news/NewsHeader.vue')['default']
    NewsSearch: typeof import('./src/components/news/NewsSearch.vue')['default']
    ProposalChat: typeof import('./src/components/ProposalChat.vue')['default']
    RegularNewsList: typeof import('./src/components/news/RegularNewsList.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SideMenu: typeof import('./src/components/SideMenu.vue')['default']
    ThemeToggle: typeof import('./src/components/ThemeToggle.vue')['default']
    TimeDisplay: typeof import('./src/components/news/TimeDisplay.vue')['default']
  }
}
