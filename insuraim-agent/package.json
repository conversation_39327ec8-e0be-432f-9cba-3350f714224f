{"name": "baoxian-vue", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "server": "vite --host", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@iconify-json/material-symbols": "^1.2.22", "@iconify/vue": "^4.3.0", "@stomp/stompjs": "^7.1.1", "@tailwindcss/vite": "^4.0.17", "@vitejs/plugin-vue": "^5.2.1", "ant-design-vue": "^4.2.6", "ant-design-x-vue": "^1.2.5", "axios": "^1.8.4", "crypto-js": "^4.2.0", "echarts": "^5.4.3", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "financejs": "^4.1.0", "html2canvas": "^1.4.1", "html2canvas-pro": "^1.5.11", "jspdf": "^3.0.1", "markdown-it": "^14.1.0", "marked": "^15.0.12", "opencc-js": "^1.0.5", "pdf-vue3": "^1.0.12", "pinia": "^3.0.2", "sockjs-client": "^1.6.1", "tailwindcss": "^4.0.17", "uuid": "^11.1.0", "vue": "^3.5.13", "vue-i18n": "^9.14.4", "vue-router": "^4.5.0", "vuedraggable": "^4.1.0"}, "devDependencies": {"@iconify/json": "^2.2.322", "@iconify/tailwind": "^1.2.0", "@types/file-saver": "^2.0.7", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.2.0", "@vue/babel-plugin-jsx": "^1.4.0", "sass-embedded": "^1.89.0", "unplugin-auto-import": "^19.1.2", "unplugin-vue-components": "^28.4.1", "vite": "^6.3.5"}}