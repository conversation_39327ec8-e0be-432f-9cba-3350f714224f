<script setup>
import { useWebSocket } from './websocket';
import { computed, onMounted, watch } from 'vue';
import { ConfigProvider } from 'ant-design-vue';
import { useThemeStore, THEME_MODE } from './store/modules/theme';
import { generateAntdTheme } from './utils/generateAntdTheme';

// 初始化WebSocket连接
const { connected } = useWebSocket();

// 使用主题Store
const themeStore = useThemeStore();

// 计算当前主题的Ant Design Vue配置
const antdTheme = computed(() => {
  return generateAntdTheme(themeStore.isDarkTheme);
});

// 监听系统主题变化
onMounted(() => {
  // 检查是否支持prefers-color-scheme媒体查询
  if (window.matchMedia) {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    // 如果用户没有明确设置主题，则使用系统主题
    const savedTheme = localStorage.getItem('theme');
    if (!savedTheme) {
      themeStore.setTheme(mediaQuery.matches ? THEME_MODE.DARK : THEME_MODE.LIGHT);
    }

    // 监听系统主题变化
    const handleChange = (e) => {
      // 只有当用户没有明确设置主题时，才跟随系统变化
      if (!localStorage.getItem('theme')) {
        themeStore.setTheme(e.matches ? THEME_MODE.DARK : THEME_MODE.LIGHT);
      }
    };

    // 添加监听器
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleChange);
    } else {
      // 兼容旧版浏览器
      mediaQuery.addListener(handleChange);
    }
  }
});
</script>

<template>
  <a-config-provider :theme="antdTheme">
    <div class="theme-transition">
      <!-- 路由视图 -->
      <router-view></router-view>
    </div>
  </a-config-provider>
</template>

<style>
/* 全局样式 */
body {
  margin: 0;
  padding: 0;
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

/* 应用主题过渡 */
body,
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}
</style>
