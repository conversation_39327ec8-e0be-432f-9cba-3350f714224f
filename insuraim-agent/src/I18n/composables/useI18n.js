import { useI18n as vueUseI18n } from 'vue-i18n';
import { setLocale as changeLocale, getCurrentLocale } from '../index';
import { computed } from 'vue';

/**
 * 国际化组合式API
 * 扩展了vue-i18n的useI18n函数，添加了一些常用功能
 * @returns {Object} 国际化工具对象
 */
export function useI18n() {
    // 使用vue-i18n的useI18n函数
    const { t, locale, messages } = vueUseI18n();

    /**
     * 设置语言
     * @param {string} lang 语言代码
     */
    const setLocale = (lang) => {
        changeLocale(lang);
    };

    /**
     * 获取当前语言
     */
    const currentLocale = computed(() => {
        return locale.value || getCurrentLocale();
    });

    /**
     * 获取所有可用语言
     */
    const availableLocales = computed(() => {
        return Object.keys(messages.value);
    });

    /**
     * 获取当前语言的显示名称
     */
    const localeName = computed(() => {
        const localeMap = {
            'zh-CN': '简体中文',
            'en-US': 'English',
            // 可以添加更多语言
        };
        return localeMap[currentLocale.value] || currentLocale.value;
    });

    /**
     * 语言列表，用于选择语言
     */
    const localeOptions = computed(() => {
        return availableLocales.value.map(locale => {
            const localeMap = {
                'zh-CN': '简体中文',
                'en-US': 'English',
                // 可以添加更多语言
            };
            return {
                value: locale,
                label: localeMap[locale] || locale
            };
        });
    });

    return {
        t,  // 翻译函数
        locale, // 当前语言(响应式)
        currentLocale, // 计算属性：当前语言
        setLocale, // 设置语言
        localeName, // 计算属性：当前语言名称
        availableLocales, // 计算属性：可用语言列表
        localeOptions, // 计算属性：语言选项，适用于下拉菜单
    };
}

// 导出默认函数
export default useI18n; 