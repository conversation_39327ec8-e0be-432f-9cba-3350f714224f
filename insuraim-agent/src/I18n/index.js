import { createI18n } from 'vue-i18n';
import messages from './message';

// 从localStorage获取语言设置，默认为zh-CN
const getLocale = () => {
    const cachedLocale = localStorage.getItem('locale');
    return cachedLocale || 'zh-CN';
};

// 创建i18n实例
const i18n = createI18n({
    legacy: false, // 使用Composition API模式
    locale: getLocale(),
    fallbackLocale: 'zh-CN', // 回退语言
    messages,
    silentTranslationWarn: true, // 生产环境中禁用警告
    silentFallbackWarn: true,
    missingWarn: false
});

/**
 * 设置语言
 * @param {string} locale 语言代码
 */
export const setLocale = (locale) => {
    i18n.global.locale.value = locale;
    localStorage.setItem('locale', locale);
    document.querySelector('html').setAttribute('lang', locale);
};

/**
 * 翻译函数，用于在JS文件中使用
 * @param {string} key 翻译key
 * @param {Object} options 翻译参数
 * @returns {string} 翻译文本
 */
export const t = (key, options = {}) => {
    return i18n.global.t(key, options);
};

/**
 * 在JS文件中获取当前语言
 * @returns {string} 当前语言代码
 */
export const getCurrentLocale = () => {
    return i18n.global.locale.value;
};

// 导出i18n实例
export default i18n;
