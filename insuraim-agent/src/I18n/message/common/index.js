import { all } from "axios";

// 通用国际化文本
export default {
    'zh-CN': {
        common: {
            save: '保存',
            cancel: '取消',
            confirm: '确认',
            delete: '删除',
            noData: '暂无数据',
            all: '全部',
            edit: '编辑',
            add: '添加',
            search: '搜索',
            submit: '提交',
            action: '操作',
            loading: '加载中',
            noData: '暂无数据',
            resultTips: '计算结果仅供参考，具体以保险公司实际计算为准',
            pleaseEnter: '请输入',
            pleaseSelect: '请选择',
            success: '操作成功',
            fail: '操作失败',
            back: '返回',
            next: '下一步',
            prev: '上一步',
            welcome: '欢迎使用保险代理系统',
            refresh: '刷新',
            viewDetails: '查看详情',
            totalItems: '总共 {total} 条记录'
        },
        nav: {
            home: '首页',
            products: '产品管理',
            orders: '订单管理',
            customers: '客户管理',
            reports: '报表分析',
            settings: '系统设置'
        },
        // 错误消息
        errors: {
            networkError: '网络错误，请检查您的网络连接',
            serverError: '服务器错误，请稍后再试',
            invalidInput: '输入无效',
            requiredField: '此字段为必填项'
        }
    },
    'en-US': {
        common: {
            save: 'Save',
            cancel: 'Cancel',
            confirm: 'Confirm',
            delete: 'Delete',
            edit: 'Edit',
            all: 'All',
            noData: 'No Data',
            add: 'Add',
            search: 'Search',
            submit: 'Submit',
            loading: 'Loading',
            noData: 'No Data',
            action: 'Action',
            success: 'Operation Successful',
            fail: 'Operation Failed',
            back: 'Back',
            next: 'Next',
            prev: 'Previous',
            welcome: 'Welcome to Insurance Agent System',
            refresh: 'Refresh',
            viewDetails: 'View Details',
            totalItems: 'Total {total} items',
            resultTips: 'The calculation result is for reference only, and the actual calculation is subject to the insurance company',
        },
        nav: {
            home: 'Home',
            products: 'Product Management',
            orders: 'Order Management',
            customers: 'Customer Management',
            reports: 'Report Analysis',
            settings: 'System Settings'
        },
        // 错误消息
        errors: {
            networkError: 'Network error, please check your connection',
            serverError: 'Server error, please try again later',
            invalidInput: 'Invalid input',
            requiredField: 'This field is required'
        }
    },
    'zh-HK': {
        common: {
            save: '儲存',
            cancel: '取消',
            confirm: '確認',
            noData: '暫無數據',
            delete: '刪除',
            all: '全部',
            action: '操作',
            edit: '編輯',
            add: '新增',
            search: '搜尋',
            submit: '提交',
            loading: '載入中',
            noData: '暫無數據',
            success: '操作成功',
            fail: '操作失敗',
            back: '返回',
            next: '下一步',
            prev: '上一步',
            welcome: '歡迎使用保險代理系統',
            refresh: '刷新',
            viewDetails: '查看詳情',
            totalItems: '總共 {total} 條記錄',
            resultTips: '計算結果僅供參考，具體以保險公司實際計算為準',
        },
        nav: {
            home: '首頁',
            products: '產品管理',
            orders: '訂單管理',
            customers: '客戶管理',
            reports: '報表分析',
            settings: '系統設置'
        },
        // 錯誤消息
        errors: {
            networkError: '網絡錯誤，請檢查您的網絡連接',
            serverError: '伺服器錯誤，請稍後再試',
            invalidInput: '輸入無效',
            requiredField: '此欄位為必填項'
        }
    }
}; 