// 公司相关的国际化文本
export default {
    'zh-CN': {
        company: {
            name: '公司名称',
            code: '公司代码',
            companyList: '保险公司列表',
            companyRank: '排序',
            address: '公司地址',
            contact: '联系方式',
            email: '电子邮箱',
            phone: '电话号码',
            website: '官方网站',
            searchCompany: '搜索保险公司',
            established: '成立时间',
            description: '公司描述',
            type: '公司类型',
            status: '公司状态',
            region: '所在地区',
            loadError: '加载保险公司数据失败',

            // 新增字段 - 公司页面
            browseAndFind: '浏览和了解各保险公司的信息，助您选择合适的保险服务提供商。',
            statistics: '公司统计',
            totalCompanies: '公司总数',
            totalCompaniesDesc: '系统中的保险公司总数',
            regionDistribution: '地区分布',
            regionDistributionDesc: '保险公司覆盖的地区数量',
            companyTypes: '公司类型',
            companyTypesDesc: '系统中的保险公司类型数量',
            statusDistribution: '状态分布',
            statusDistributionDesc: '保险公司的不同状态数量',
            reset: '重置',

            // 公司类型选项
            typeOptions: {
                insurance: '保险公司',
                reinsurance: '再保险公司',
                broker: '保险经纪公司',
                agent: '保险代理公司'
            },
            // 状态选项
            statusOptions: {
                active: '正常营业',
                suspended: '暂停营业',
                closed: '已关闭'
            }
        }
    },
    'en-US': {
        company: {
            name: 'Company Name',
            code: 'Company Code',
            companyList: 'Company List',
            companyRank: 'Rank',
            address: 'Company Address',
            contact: 'Contact',
            searchCompany: 'Search Company',
            email: 'Email',
            phone: 'Phone Number',
            website: 'Official Website',
            established: 'Established Date',
            description: 'Company Description',
            type: 'Company Type',
            status: 'Company Status',
            region: 'Region',
            loadError: 'Failed to load company data',

            // 新增字段 - 公司页面
            browseAndFind: 'Browse and learn about insurance companies to help you choose the right insurance service provider.',
            statistics: 'Company Statistics',
            totalCompanies: 'Total Companies',
            totalCompaniesDesc: 'Total number of insurance companies in the system',
            regionDistribution: 'Region Distribution',
            regionDistributionDesc: 'Number of regions covered by insurance companies',
            companyTypes: 'Company Types',
            companyTypesDesc: 'Number of insurance company types in the system',
            statusDistribution: 'Status Distribution',
            statusDistributionDesc: 'Number of different insurance company statuses',
            reset: 'Reset',

            // 公司类型选项
            typeOptions: {
                insurance: 'Insurance Company',
                reinsurance: 'Reinsurance Company',
                broker: 'Insurance Broker',
                agent: 'Insurance Agent'
            },
            // 状态选项
            statusOptions: {
                active: 'Active',
                suspended: 'Suspended',
                closed: 'Closed'
            }
        }
    },
    'zh-HK': {
        company: {
            name: '公司名稱',
            code: '公司代碼',
            companyList: '公司列表',
            companyRank: '排序',
            address: '公司地址',
            contact: '聯絡方式',
            email: '電子郵箱',
            phone: '電話號碼',
            website: '官方網站',
            searchCompany: '搜尋保險公司',
            established: '成立時間',
            description: '公司描述',
            type: '公司類型',
            status: '公司狀態',
            region: '所在地區',
            loadError: '加載保險公司數據失敗',

            // 新增字段 - 公司页面
            browseAndFind: '瀏覽和了解各保險公司的信息，助您選擇合適的保險服務提供商。',
            statistics: '公司統計',
            totalCompanies: '公司總數',
            totalCompaniesDesc: '系統中的保險公司總數',
            regionDistribution: '地區分佈',
            regionDistributionDesc: '保險公司覆蓋的地區數量',
            companyTypes: '公司類型',
            companyTypesDesc: '系統中的保險公司類型數量',
            statusDistribution: '狀態分佈',
            statusDistributionDesc: '保險公司的不同狀態數量',
            reset: '重置',

            // 公司類型選項
            typeOptions: {
                insurance: '保險公司',
                reinsurance: '再保險公司',
                broker: '保險經紀公司',
                agent: '保險代理公司'
            },
            // 狀態選項
            statusOptions: {
                active: '正常營業',
                suspended: '暫停營業',
                closed: '已關閉'
            }
        }
    }
};
