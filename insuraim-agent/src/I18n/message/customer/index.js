export default {
    'zh-CN': {
        customer: {
            // 页面标题和描述
            customerList: '客户列表',
            browseAndManage: '浏览和管理您的客户信息，提供更好的服务体验。',

            // 统计卡片
            statistics: '客户统计',
            totalCustomers: '客户总数',
            totalCustomersDesc: '系统中的客户总数量',
            maleCustomers: '男性客户',
            maleCustomersDesc: '男性客户数量',
            femaleCustomers: '女性客户',
            updateTime: '更新时间',
            femaleCustomersDesc: '女性客户数量',
            activeCustomers: '活跃客户',
            activeCustomersDesc: '状态正常的客户数量',

            // 筛选和操作
            filter: '筛选',
            reset: '重置',
            addCustomer: '添加客户',
            refresh: '刷新数据',
            gender: '性别',
            status: '状态',
            search: '搜索客户姓名/手机号',
            all: '全部',
            male: '男',
            female: '女',
            active: '正常',
            inactive: '禁用',

            // 表格列
            serialNumber: '序号',
            name: '客户姓名',
            phone: '手机号',
            gender: '性别',
            birthday: '生日',
            idCard: '身份证号',
            status: '状态',
            createTime: '创建时间',
            action: '操作',

            // 详情抽屉
            basicInfo: '基本信息',
            address: '地址',
            remark: '备注',
            none: '无',
            relatedPolicies: '关联保单',
            viewAll: '查看全部',
            noPolicies: '暂无关联保单',
            policyName: '保单名称',
            policyNo: '保单号',

            // 操作
            editInfo: '编辑信息',
            viewDetails: '查看详情',

            // 提示消息
            loadFailed: '加载客户数据失败，请稍后重试',
            detailFailed: '获取客户详情失败，请稍后重试'
        }
    },
    'en-US': {
        customer: {
            // 页面标题和描述
            customerList: 'Customer List',
            browseAndManage: 'Browse and manage your customer information to provide better service experience.',

            // 统计卡片
            statistics: 'Customer Statistics',
            updateTime: 'Updated Time',
            totalCustomers: 'Total Customers',
            totalCustomersDesc: 'Total number of customers in the system',
            maleCustomers: 'Male Customers',
            maleCustomersDesc: 'Number of male customers',
            femaleCustomers: 'Female Customers',
            femaleCustomersDesc: 'Number of female customers',
            activeCustomers: 'Active Customers',
            activeCustomersDesc: 'Number of customers with active status',

            // 筛选和操作
            filter: 'Filter',
            reset: 'Reset',
            addCustomer: 'Add Customer',
            refresh: 'Refresh Data',
            gender: 'Gender',
            status: 'Status',
            search: 'Search by name/phone',
            all: 'All',
            male: 'Male',
            female: 'Female',
            active: 'Active',
            inactive: 'Inactive',

            // 表格列
            serialNumber: 'No.',
            name: 'Customer Name',
            phone: 'Phone',
            gender: 'Gender',
            birthday: 'Birthday',
            idCard: 'ID Number',
            status: 'Status',
            createTime: 'Created Time',
            action: 'Action',

            // 详情抽屉
            basicInfo: 'Basic Information',
            address: 'Address',
            remark: 'Remark',
            none: 'None',
            relatedPolicies: 'Related Policies',
            viewAll: 'View All',
            noPolicies: 'No related policies',
            policyName: 'Policy Name',
            policyNo: 'Policy Number',

            // 操作
            editInfo: 'Edit Info',
            viewDetails: 'View Details',

            // 提示消息
            loadFailed: 'Failed to load customer data, please try again later',
            detailFailed: 'Failed to get customer details, please try again later'
        }
    },
    'zh-HK': {
        customer: {
            // 页面标题和描述
            customerList: '客戶列表',
            browseAndManage: '瀏覽和管理您的客戶信息，提供更好的服務體驗。',

            // 统计卡片
            statistics: '客戶統計',
            totalCustomers: '客戶總數',
            totalCustomersDesc: '系統中的客戶總數量',
            maleCustomers: '男性客戶',
            maleCustomersDesc: '男性客戶數量',
            updateTime: '更新時間',
            femaleCustomers: '女性客戶',
            femaleCustomersDesc: '女性客戶數量',
            activeCustomers: '活躍客戶',
            activeCustomersDesc: '狀態正常的客戶數量',

            // 筛选和操作
            filter: '篩選',
            reset: '重置',
            addCustomer: '添加客戶',
            refresh: '刷新數據',
            gender: '性別',
            status: '狀態',
            search: '搜索客戶姓名/手機號',
            all: '全部',
            male: '男',
            female: '女',
            active: '正常',
            inactive: '禁用',

            // 表格列
            serialNumber: '序號',
            name: '客戶姓名',
            phone: '手機號',
            gender: '性別',
            birthday: '生日',
            idCard: '身份證號',
            status: '狀態',
            createTime: '創建時間',
            action: '操作',

            // 详情抽屉
            basicInfo: '基本信息',
            address: '地址',
            remark: '備註',
            none: '無',
            relatedPolicies: '關聯保單',
            viewAll: '查看全部',
            noPolicies: '暫無關聯保單',
            policyName: '保單名稱',
            policyNo: '保單號',

            // 操作
            editInfo: '編輯信息',
            viewDetails: '查看詳情',

            // 提示消息
            loadFailed: '加載客戶數據失敗，請稍後重試',
            detailFailed: '獲取客戶詳情失敗，請稍後重試'
        }
    }
} 