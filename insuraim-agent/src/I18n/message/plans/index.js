export default {
    'zh-CN': {
        plans: {
            // 公共标题和描述
            plansList: '我的计划书',
            planCreator: '计划书生成',
            browseAndFind: '浏览和管理您生成的所有计划书，快速查看详情和下载',
            createAndGenerate: '创建新的保险计划书，定制产品方案，生成专业展示文件',

            // 统计卡片
            statistics: '统计概览',
            totalPlans: '计划书总数',
            totalPlansDesc: '您已生成的计划书总数',
            completedPlans: '已完成计划书',
            completedPlansDesc: '已成功生成的计划书数量',
            processingPlans: '处理中计划书',
            processingPlansDesc: '正在生成中的计划书数量',
            downloadedPlans: '已下载计划书',
            downloadedPlansDesc: '已被下载的计划书数量',
            failedPlans: '失败计划书',
            failedPlansDesc: '生成失败的计划书数量',

            // 计划书列表页面
            search: '搜索',
            searchPlaceholder: '搜索计划书名称、产品名称或客户名称',
            status: '状态',
            allStatus: '全部状态',
            completed: '已完成',
            processing: '处理中',
            failed: '生成失败',
            sortOrder: '排序方式',
            createdTimeDesc: '创建时间降序',
            createdTimeAsc: '创建时间升序',
            updateTimeDesc: '更新时间降序',
            updateTimeAsc: '更新时间升序',
            createPlan: '创建计划书',
            productName: '产品名称',
            customerName: '客户姓名',
            proposalNo: '计划书编号',
            createAt: '创建时间',
            action: '操作',
            details: '详情',
            preview: '预览',
            analyze: 'AI分析',
            download: '下载',
            total: '共 {total} 条记录',
            proposalDetails: '计划书详情',
            updateAt: '更新时间',
            previewProposal: '预览计划书',
            downloadProposal: '下载计划书',

            // 计划书生成页面
            filterConditions: '筛选条件',
            region: '地区',
            company: '保司',
            category: '分类',
            currency: '货币',
            all: '全部',
            selectProduct: '选择产品',
            selectCurrency: '选择货币',
            customerInfo: '客户信息',
            name: '客户姓名',
            age: '年龄',
            gender: '性别',
            isSmoker: '是否吸烟',
            male: '男性',
            female: '女性',
            smoker: '吸烟',
            nonSmoker: '非吸烟',
            protectionInfo: '保障信息',
            amountType: '金额类型',
            premium: '保费',
            insuredAmount: '保额',
            amount: '金额',
            paymentMethod: '缴费方式',
            prepaymentYear: '预缴年份',
            customWithdrawal: '自定义现金提取',
            withdrawalDetails: '自定义提取金额详情',
            withdrawalAge: '领款年龄',
            from: '从',
            to: '至',
            initialWithdrawalAmount: '初始提取金额',
            withdrawalGrowthRate: '每年提取增长率',
            lockEndBonus: '锁定终期红利',
            lockEndBonusDetails: '锁定终期红利详情',
            exerciseAge: '行使权益时年龄',
            lockPercentage: '锁定比例',
            language: '计划书语言',
            languageSelection: '语言选择',
            generate: '生成计划书',
            reset: '重置',

            // 提示消息
            loadingPdf: '正在加载PDF...',
            preparingDownload: '正在准备下载...',
            downloadSuccess: '下载成功',
            generatingTask: '正在提交计划书任务...',
            generationSuccess: '计划书生成任务已提交，请等待计划书生成完成',
            generationFailure: '生成计划书失败，请稍后重试',
            incomplete: '计划书尚未生成完成',
            authFailure: '认证失败，请重新登录',
            previewFailure: '预览PDF失败',
            downloadFailure: '下载文件失败',
            invalidPdf: '返回的不是有效的PDF文件',
            openingAiChat: '打开AI聊天模态框失败',
            inputRequired: '请输入{field}',
            selectRequired: '请选择{field}',
            invalidAmount: '金额范围无效',
            ageError: '年龄范围无效',
            confirmModal: '本功能仅供线上演示使用，基于客户需求可生成示意计划书。所有正式合同请在香港面签时由持牌代理提供。'
        }
    },
    'en-US': {
        plans: {
            // Common titles and descriptions
            plansList: 'My Proposals',
            planCreator: 'Proposal Generator',
            browseAndFind: 'Browse and manage all your generated insurance proposals, quickly view details and download',
            createAndGenerate: 'Create new insurance proposals, customize product plans, and generate professional documents',

            // Statistics cards
            statistics: 'Statistics Overview',
            totalPlans: 'Total Proposals',
            totalPlansDesc: 'Total number of proposals you have generated',
            completedPlans: 'Completed Proposals',
            completedPlansDesc: 'Number of successfully generated proposals',
            processingPlans: 'Processing Proposals',
            processingPlansDesc: 'Number of proposals being generated',
            downloadedPlans: 'Downloaded Proposals',
            downloadedPlansDesc: 'Number of proposals that have been downloaded',
            failedPlans: 'Failed Proposals',
            failedPlansDesc: 'Number of proposals that failed to generate',

            // Proposal list page
            search: 'Search',
            searchPlaceholder: 'Search proposal name, product name, or customer name',
            status: 'Status',
            allStatus: 'All Status',
            completed: 'Completed',
            processing: 'Processing',
            failed: 'Failed',
            sortOrder: 'Sort Order',
            createdTimeDesc: 'Created Time (Desc)',
            createdTimeAsc: 'Created Time (Asc)',
            updateTimeDesc: 'Updated Time (Desc)',
            updateTimeAsc: 'Updated Time (Asc)',
            createPlan: 'Create',
            productName: 'Product Name',
            customerName: 'Customer Name',
            proposalNo: 'Proposal No.',
            createAt: 'Created At',
            action: 'Action',
            details: 'Details',
            preview: 'Preview',
            analyze: 'AI Analysis',
            download: 'Download',
            total: 'Total {total} records',
            proposalDetails: 'Proposal Details',
            updateAt: 'Updated At',
            previewProposal: 'Preview Proposal',
            downloadProposal: 'Download Proposal',

            // Proposal generation page
            filterConditions: 'Filter Conditions',
            region: 'Region',
            company: 'Company',
            category: 'Category',
            currency: 'Currency',
            all: 'All',
            selectProduct: 'Select Product',
            selectCurrency: 'Select Currency',
            customerInfo: 'Customer Information',
            name: 'Customer Name',
            age: 'Age',
            gender: 'Gender',
            isSmoker: 'Smoking Status',
            male: 'Male',
            female: 'Female',
            smoker: 'Smoker',
            nonSmoker: 'Non-smoker',
            protectionInfo: 'Protection Information',
            amountType: 'Amount Type',
            premium: 'Premium',
            insuredAmount: 'Insured Amount',
            amount: 'Amount',
            paymentMethod: 'Payment Method',
            prepaymentYear: 'Prepayment Year',
            customWithdrawal: 'Custom Cash Withdrawal',
            withdrawalDetails: 'Custom Withdrawal Details',
            withdrawalAge: 'Withdrawal Age',
            from: 'From',
            to: 'To',
            initialWithdrawalAmount: 'Initial Withdrawal Amount',
            withdrawalGrowthRate: 'Annual Withdrawal Growth Rate',
            lockEndBonus: 'Lock Terminal Bonus',
            lockEndBonusDetails: 'Lock Terminal Bonus Details',
            exerciseAge: 'Exercise Age',
            lockPercentage: 'Lock Percentage',
            language: 'Proposal Language',
            languageSelection: 'Language Selection',
            generate: 'Generate Proposal',
            reset: 'Reset',

            // Notification messages
            loadingPdf: 'Loading PDF...',
            preparingDownload: 'Preparing download...',
            downloadSuccess: 'Download successful',
            generatingTask: 'Submitting proposal task...',
            generationSuccess: 'Proposal generation task submitted, please wait for completion',
            generationFailure: 'Failed to generate proposal, please try again later',
            incomplete: 'Proposal generation is not yet complete',
            authFailure: 'Authentication failed, please login again',
            previewFailure: 'Failed to preview PDF',
            downloadFailure: 'Failed to download file',
            invalidPdf: 'The response is not a valid PDF file',
            openingAiChat: 'Failed to open AI chat modal',
            inputRequired: 'Please input {field}',
            selectRequired: 'Please select {field}',
            invalidAmount: 'Invalid amount range',
            ageError: 'Invalid age range',
            confirmModal: 'This feature is for online demonstration only, based on customer needs to generate indicative proposals. All formal contracts should be provided by licensed agents during face-to-face meetings in Hong Kong.'
        }
    },
    'zh-HK': {
        plans: {
            // 公共标题和描述
            plansList: '我的計劃書',
            planCreator: '計劃書生成',
            browseAndFind: '瀏覽和管理您生成的所有計劃書，快速查看詳情和下載',
            createAndGenerate: '創建新的保險計劃書，定制產品方案，生成專業展示文檔',

            // 统计卡片
            statistics: '統計概覽',
            totalPlans: '計劃書總數',
            totalPlansDesc: '您已生成的計劃書總數',
            completedPlans: '已完成計劃書',
            completedPlansDesc: '已成功生成的計劃書數量',
            processingPlans: '處理中計劃書',
            processingPlansDesc: '正在生成中的計劃書數量',
            downloadedPlans: '已下載計劃書',
            downloadedPlansDesc: '已被下載的計劃書數量',
            failedPlans: '失敗計劃書',
            failedPlansDesc: '生成失敗的計劃書數量',

            // 计划书列表页面
            search: '搜索',
            searchPlaceholder: '搜索計劃書名稱、產品名稱或客戶名稱',
            status: '狀態',
            allStatus: '全部狀態',
            completed: '已完成',
            processing: '處理中',
            failed: '生成失敗',
            sortOrder: '排序方式',
            createdTimeDesc: '創建時間降序',
            createdTimeAsc: '創建時間升序',
            updateTimeDesc: '更新時間降序',
            updateTimeAsc: '更新時間升序',
            createPlan: '創建計劃書',
            productName: '產品名稱',
            customerName: '客戶姓名',
            proposalNo: '計劃書編號',
            createAt: '創建時間',
            action: '操作',
            details: '詳情',
            preview: '預覽',
            analyze: 'AI分析',
            download: '下載',
            total: '共 {total} 條記錄',
            proposalDetails: '計劃書詳情',
            updateAt: '更新時間',
            previewProposal: '預覽計劃書',
            downloadProposal: '下載計劃書',

            // 计划书生成页面
            filterConditions: '篩選條件',
            region: '地區',
            company: '保司',
            category: '分類',
            currency: '貨幣',
            all: '全部',
            selectProduct: '選擇產品',
            selectCurrency: '選擇貨幣',
            customerInfo: '客戶信息',
            name: '客戶姓名',
            age: '年齡',
            gender: '性別',
            isSmoker: '是否吸煙',
            male: '男性',
            female: '女性',
            smoker: '吸煙',
            nonSmoker: '非吸煙',
            protectionInfo: '保障信息',
            amountType: '金額類型',
            premium: '保費',
            insuredAmount: '保額',
            amount: '金額',
            paymentMethod: '繳費方式',
            prepaymentYear: '預繳年份',
            customWithdrawal: '自定義現金提取',
            withdrawalDetails: '自定義提取金額詳情',
            withdrawalAge: '領款年齡',
            from: '從',
            to: '至',
            initialWithdrawalAmount: '初始提取金額',
            withdrawalGrowthRate: '每年提取增長率',
            lockEndBonus: '鎖定終期紅利',
            lockEndBonusDetails: '鎖定終期紅利詳情',
            exerciseAge: '行使權益時年齡',
            lockPercentage: '鎖定比例',
            language: '計劃書語言',
            languageSelection: '語言選擇',
            generate: '生成計劃書',
            reset: '重置',

            // 提示消息
            loadingPdf: '正在加載PDF...',
            preparingDownload: '正在準備下載...',
            downloadSuccess: '下載成功',
            generatingTask: '正在提交計劃書任務...',
            generationSuccess: '計劃書生成任務已提交，請等待計劃書生成完成',
            generationFailure: '生成計劃書失敗，請稍後重試',
            incomplete: '計劃書尚未生成完成',
            authFailure: '認證失敗，請重新登錄',
            previewFailure: '預覽PDF失敗',
            downloadFailure: '下載文件失敗',
            invalidPdf: '返回的不是有效的PDF文件',
            openingAiChat: '打開AI聊天模態框失敗',
            inputRequired: '請輸入{field}',
            selectRequired: '請選擇{field}',
            invalidAmount: '金額範圍無效',
            ageError: '年齡範圍無效',
            confirmModal: '本功能僅供線上演示使用，基於客戶需求可生成示意計劃書。所有正式合同請在香港面簽時由持牌代理提供。'
        }
    }
}; 