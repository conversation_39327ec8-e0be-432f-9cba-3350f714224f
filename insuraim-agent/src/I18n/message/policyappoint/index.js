import { all } from "axios";

// 通用国际化文本
export default {
    'zh-CN': {
        policyappoint: {
            // 页面标题和描述
            appointmentTitle: '客户预约',
            browseAndManage: '创建客户预约，安排客户签约，提高业务转化率。',

            // 表单操作
            resetForm: '重置表单',
            submitAppointment: '提交预约',

            // 表单标签页
            tabs: {
                overview: '预约概况',
                proposal: '计划书选择',
                policyholder: '投保人信息',
                insured: '被保人信息',
                beneficiary: '受益人信息'
            },

            // 提示信息
            formReferenceFailed: '表单引用获取失败',
            formValidationFailed: '表单验证失败，请检查输入',
            appointmentSubmitSuccess: '预约信息提交成功！',
            submitFailed: '提交失败，请稍后重试',

            // 邀请链接相关
            inviteLinkGenerated: '邀请链接已生成',
            scanQrOrShare: '扫描二维码或分享链接，邀请客户填写信息',
            downloadQrCode: '下载二维码',
            copy: '复制',
            linkValidUntil: '链接有效期至',
            linkCopied: '链接已复制到剪贴板',
            copyFailed: '复制失败，请手动复制',

            // 二维码相关
            qrCodeElementNotExist: '二维码元素不存在',
            qrCodeDownloaded: '二维码已下载',
            downloadQrCodeFailed: '下载二维码失败，请稍后重试',
            generatingQrCode: '正在生成二维码图片，请稍候...',
            inviteQrCode: '邀请二维码',
            generateQrCodeImageFailed: '生成二维码图片失败，请稍后重试',

            // 确认框
            tip: '提示',
            generateInviteConfirmation: '投保人信息、被保人信息、受益人信息可生成邀请链接由客户填写，是否生成？生成后，修改信息只能待客户填写后修改。',
            generateInviteFailed: '生成邀请链接失败，请稍后重试'
        }
    },
    'en-US': {
        policyappoint: {
            // 页面标题和描述
            appointmentTitle: 'Customer Appointment',
            browseAndManage: 'Create customer appointments, arrange customer signing, and improve business conversion rates.',

            // 表单操作
            resetForm: 'Reset Form',
            submitAppointment: 'Submit Appointment',

            // 表单标签页
            tabs: {
                overview: 'Appointment Overview',
                proposal: 'Proposal Selection',
                policyholder: 'Policyholder Info',
                insured: 'Insured Info',
                beneficiary: 'Beneficiary Info'
            },

            // 提示信息
            formReferenceFailed: 'Form reference retrieval failed',
            formValidationFailed: 'Form validation failed, please check your input',
            appointmentSubmitSuccess: 'Appointment information submitted successfully!',
            submitFailed: 'Submission failed, please try again later',

            // 邀请链接相关
            inviteLinkGenerated: 'Invitation Link Generated',
            scanQrOrShare: 'Scan the QR code or share the link to invite customers to fill in information',
            downloadQrCode: 'Download QR Code',
            copy: 'Copy',
            linkValidUntil: 'Link valid until',
            linkCopied: 'Link copied to clipboard',
            copyFailed: 'Copy failed, please copy manually',

            // 二维码相关
            qrCodeElementNotExist: 'QR code element does not exist',
            qrCodeDownloaded: 'QR code downloaded',
            downloadQrCodeFailed: 'Failed to download QR code, please try again later',
            generatingQrCode: 'Generating QR code image, please wait...',
            inviteQrCode: 'Invitation QR Code',
            generateQrCodeImageFailed: 'Failed to generate QR code image, please try again later',

            // 确认框
            tip: 'Tip',
            generateInviteConfirmation: 'Policyholder, insured, and beneficiary information can be filled in by customers through an invitation link. Do you want to generate it? After generation, information can only be modified after the customer fills it in.',
            generateInviteFailed: 'Failed to generate invitation link, please try again later'
        }
    },
    'zh-HK': {
        policyappoint: {
            // 页面标题和描述
            appointmentTitle: '客戶預約',
            browseAndManage: '創建客戶預約，安排客戶簽約，提高業務轉化率。',

            // 表单操作
            resetForm: '重置表單',
            submitAppointment: '提交預約',

            // 表单标签页
            tabs: {
                overview: '預約概況',
                proposal: '計劃書選擇',
                policyholder: '投保人信息',
                insured: '被保人信息',
                beneficiary: '受益人信息'
            },

            // 提示信息
            formReferenceFailed: '表單引用獲取失敗',
            formValidationFailed: '表單驗證失敗，請檢查輸入',
            appointmentSubmitSuccess: '預約信息提交成功！',
            submitFailed: '提交失敗，請稍後重試',

            // 邀请链接相关
            inviteLinkGenerated: '邀請鏈接已生成',
            scanQrOrShare: '掃描二維碼或分享鏈接，邀請客戶填寫信息',
            downloadQrCode: '下載二維碼',
            copy: '複製',
            linkValidUntil: '鏈接有效期至',
            linkCopied: '鏈接已複製到剪貼板',
            copyFailed: '複製失敗，請手動複製',

            // 二维码相关
            qrCodeElementNotExist: '二維碼元素不存在',
            qrCodeDownloaded: '二維碼已下載',
            downloadQrCodeFailed: '下載二維碼失敗，請稍後重試',
            generatingQrCode: '正在生成二維碼圖片，請稍候...',
            inviteQrCode: '邀請二維碼',
            generateQrCodeImageFailed: '生成二維碼圖片失敗，請稍後重試',

            // 确认框
            tip: '提示',
            generateInviteConfirmation: '投保人信息、被保人信息、受益人信息可生成邀請鏈接由客戶填寫，是否生成？生成後，修改信息只能待客戶填寫後修改。',
            generateInviteFailed: '生成邀請鏈接失敗，請稍後重試'
        }
    }
};

