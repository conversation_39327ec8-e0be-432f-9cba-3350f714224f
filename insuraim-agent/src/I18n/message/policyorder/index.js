export default {
    'zh-CN': {
        policyorder: {
            policyStatus: '保单状态',
            pleaseSelectPolicyStatus: '请选择保单状态',
            policyOrder: '预约信息',
            orderStatus: '订单状态',
            policyOrderList: '预约信息列表',
            orderManagement: '订单管理',
            policyOrderDetail: '预约信息详情',
            policyOrderCreate: '创建预约信息',
            policyOrderUpdate: '更新预约信息',
            policyOrderDelete: '删除预约信息',
            policyOrderSearch: '搜索预约信息',
            policyManagement: '保单管理',
            browseAndManage: '浏览和管理您的保单订单，提供完整的保单服务。',
            refreshData: '刷新数据',
            nextRenewalAmount: '下次续保金额',
            nextRenewalDate: '下次续保日期',
            deleteSuccess: '删除成功',
            deletePolicy: '删除保单',
            deleteFailed: '删除保单失败',
            confirmDelete: '确认删除此订单吗？此操作不可恢复',
            confirm: '确认',
            cancel: '取消',
            loadPolicyDataFailed: '加载保单数据失败',
            policyDetail: '保单详情',
            loading: '加载中...',
            pleaseWait: '请稍等',
            noPolicyDetail: '暂无保单详情',
            loadDetailFailed: '加载保单详情失败，请稍后再试',
            fileUploadSuccess: '文件上传成功',
            fileUploadFailed: '文件上传失败',
            paymentRecordSaveSuccess: '缴费记录保存成功',
            paymentRecordSaveFailed: '保存缴费记录失败',
            editPayment: '编辑缴费记录',
            editPaymentTodo: '编辑缴费记录功能待实现',
            deletePayment: '删除缴费记录',
            deletePaymentSuccess: '删除缴费记录成功',
            status: '状态',
            region: '地区',
            insuranceCompany: '保险公司',
            searchPlaceholder: '搜索订单号/投保人/保单号',
            all: '全部',
            hongKong: '香港',
            macau: '澳门',
            singapore: '新加坡',
            mainlandChina: '中国大陆',
            bermuda: '百慕大',
            chinaLifeSG: '国寿（新加坡）',
            chinaLifeHK: '国寿（香港）',
            aiaHK: '友邦（香港）',
            prudentialSG: '保诚（新加坡）',
            hsbcLife: '汇丰人寿',
            manulife: '宏利',
            sunlife: '永明',
            bocLife: '中银人寿',
            chinaTaiping: '中国太平',
            orderNo: '订单号',
            policyholder: '投保人',
            insured: '受保人',
            company: '保险公司',
            product: '签约产品',
            policyNo: '保单号',
            paymentTerm: '缴费期限',
            annualPremium: '年缴保费',
            effectiveDate: '生效日期',
            nextRenewal: '下次续保',
            action: '操作',
            viewDetail: '查看详情',
            confirmDelete: '确定要删除这个保单吗?',
            beneficiaryList: '受益人列表',
            confirm: '确定',
            cancel: '取消',
            notEffective: '未生效',
            appointment: '已预约',
            waitPayment: '待缴费',
            quietPeriod: '冷静期',
            inEffect: '生效中',
            expired: '已失效',
            pending: '代办',
            appointmentInfo: '预约信息',
            policyInfo: '保单信息',
            policyType: '保单类型',
            currency: '币种',
            applicationDate: '投保日期',
            issueDate: '签发日期',
            expiryDate: '到期日期',
            policyholderInfo: '投保人信息',
            name: '姓名',
            nameEn: '英文姓名',
            gender: '性别',
            male: '男',
            female: '女',
            birthdate: '出生日期',
            idType: '证件类型',
            idNumber: '证件号码',
            nationality: '国籍',
            phoneNumber: '联系电话',
            email: '电子邮箱',
            address: '联系地址',
            insuredInfo: '受保人信息',
            relationshipWithPolicyholder: '与投保人关系',
            self: '本人',
            spouse: '配偶',
            child: '子女',
            parent: '父母',
            other: '其他',
            beneficiaryInfo: '受益人信息',
            beneficiaryType: '受益人类型',
            beneficiaryShare: '分配比例',
            primary: '第一受益人',
            contingent: '第二受益人',
            paymentRecords: '缴费记录',
            addPayment: '添加缴费记录',
            paymentDate: '缴费日期',
            paymentAmount: '保费金额',
            paymentMethod: '缴费方式',
            paymentStatus: '缴费状态',
            bankTransfer: '银行转账',
            creditCard: '信用卡',
            cheque: '支票',
            cash: '现金',
            paid: '已支付',
            unpaid: '未支付',
            policyHistory: '保单历史状态',
            currentStatus: '当前状态',
            statusChangeTime: '状态变更时间',
            operator: '操作人',
            remarks: '备注',
            addPaymentRecord: '添加缴费记录',
            save: '保存',
            close: '关闭',
            uploadFile: '上传文件',
            fileType: '文件类型',
            selectFile: '选择文件',
            upload: '上传',
            fileTypeRequired: '请选择文件类型',
            fileRequired: '请上传文件',
            editInfo: '编辑信息',
            saveChanges: '保存更改',
            policyFiles: '保单文件',
            fileName: '文件名',
            fileSize: '文件大小',
            uploadTime: '上传时间',
            download: '下载',
            preview: '预览',
            noFiles: '暂无文件',
            cny: '人民币 (CNY)',
            hkd: '港币 (HKD)',
            usd: '美元 (USD)',
            sgd: '新加坡元 (SGD)',
            policyholderNameCn: '投保人姓名(中文)',
            policyholderNameEn: '投保人姓名(英文)',
            insuredNameCn: '受保人姓名(中文)',
            insuredNameEn: '受保人姓名(英文)',
            team: '团队',
            referrer: '推荐人',
            signDate: '签约日期',
            pleaseEnterPolicyholderName: '请输入投保人姓名',
            pleaseEnterPolicyholderNameEn: '请输入投保人英文姓名',
            pleaseEnterInsuredName: '请输入受保人姓名',
            pleaseEnterInsuredNameEn: '请输入受保人英文姓名',
            pleaseEnterRegion: '请输入地区',
            pleaseEnterCompany: '请输入保险公司',
            pleaseEnterProduct: '请输入签约产品',
            pleaseEnterPhoneNumber: '请输入联系电话',
            pleaseEnterValidPhoneNumber: '请输入有效的手机号码',
            pleaseEnterEmail: '请输入电子邮箱',
            pleaseEnterValidEmail: '请输入有效的电子邮箱',
            pleaseEnterTeam: '请输入团队',
            pleaseEnterReferrer: '请输入推荐人',
            pleaseSelectSignDate: '请选择签约日期',
            pleaseEnterPaymentTerm: '请输入缴费期限',
            pleaseSelectCurrency: '请选择币种',
            pleaseSelectNextRenewalDate: '请选择下次续保日',
            pleaseSelectStatus: '请选择订单状态',
            pleaseEnterRemarks: '请输入备注',
            submitBasicInfoFormData: '提交基本信息表单数据',
            basicInfoUpdateSuccess: '基本信息更新成功',
            updateBasicInfoFailed: '更新基本信息失败',
            paymentGuide: '缴费指引',
            paymentRecord: '缴费记录',
            effective: '已生效',
            contractSent: '合同已寄出',
            signature: '已签名',
            quietPeriodExpired: '冷静期已过',
            signAppointment: '预约签约',
            sign: '已签约',
            paymentProof: '缴费凭证',
            identification: '身份证明',
            medicalReport: '体检报告',
            otherFile: '其他文件',
            selectUploadStatus: '选择上传状态',
            completed: '已完成',
            pendingCompletion: '待完成',
            uploadRelatedFileTooltip: '上传相关文件',
            no: '否',
            noPaymentRecords: '暂无缴费记录',
            dragFileText: '点击或拖拽文件到此区域上传',
            uploadHint: '支持单个文件上传，文件大小不超过10MB',
            fileSizeLimit: '文件大小不能超过10MB',
            remarkPlaceholder: '请输入备注信息',
            nextStatusTooltip: '确认进入下一状态',
            previousStatusTooltip: '回退到上一状态',
            confirmOperation: '确认操作',
            confirmNextStatus: '确认将状态从 {status} 更新为下一状态？',
            confirmRollback: '确认回退',
            confirmRollbackStatus: '确认将状态从 {status} 回退到上一状态？',
            statusUpdateSuccess: '状态更新成功',
            statusUpdateFailed: '状态更新失败',
            statusRollbackSuccess: '状态回退成功',
            statusRollbackFailed: '状态回退失败',
            invalidFileLink: '无效的文件链接',
            filePreviewTip: '文件预览提示',
            filePreviewNotSupported: '不支持预览此类型的文件：{type}',
            downloadFile: '下载文件',
            browserBlockedPopup: '浏览器阻止了弹出窗口',
            downloadTip: '下载提示',
            downloadAlternative: '您可以手动复制链接到新标签页：{url}',
            iKnow: '我知道了',
            isSmoker: '是否吸烟',
            idCardAddress: '证件地址',
            fileDownloadError: '文件下载错误',
            fileDownloadFailed: '文件下载失败',
            fileWillOpenInNewTab: '文件 {name} 将在新标签页中打开',
            attachments: '附件',
            expandAll: '展开全部',
            collapse: '收起',
            height: '身高',
            weight: '体重',
            clickToPreview: '点击预览',
            showAllAttachments: '显示全部 {count} 个附件',
            noHistoryRecords: '暂无历史记录',
            rollback: '回退',
            formValidationFailed: '表单验证失败',
            editInsuredInfo: '编辑受保人信息',
            edit: '编辑',
            editPolicyholderInfo: '编辑投保人信息',
            addressInfo: '地址信息',
            occupationInfo: '职业信息',
            birthPlace: '出生地',
            residentialAddress: '居住地址',
            mailingAddress: '邮寄地址',
            companyName: '公司名称',
            companyNameCn: '公司名称(中文)',
            companyNameEn: '公司名称(英文)',
            companyAddress: '公司地址',
            companyIndustry: '公司行业',
            position: '职位',
            annualIncome: '年收入',
            nameCn: '姓名(中文)',
            travelPermitNo: '通行证号码',
            maritalStatus: '婚姻状况',
            single: '未婚',
            married: '已婚',
            divorced: '离异',
            widowed: '丧偶',
            educationLevel: '教育程度',
            primarySchool: '小学',
            middleSchool: '初中',
            highSchool: '高中',
            college: '大专',
            bachelor: '本科',
            master: '硕士',
            doctor: '博士',
            yes: '是',
            pleaseEnterNameEn: '请输入英文姓名',
            pleaseSelectGender: '请选择性别',
            pleaseSelectBirthdate: '请选择出生日期',
            pleaseEnterValidIdNumber: '请输入有效的证件号码',
            pleaseEnterTravelPermitNo: '请输入通行证号码',
            pleaseEnterNationality: '请输入国籍',
            pleaseSelectMaritalStatus: '请选择婚姻状况',
            pleaseSelectEducationLevel: '请选择教育程度',
            pleaseEnterHeight: '请输入身高',
            pleaseEnterWeight: '请输入体重',
            pleaseSelectIsSmoker: '请选择是否吸烟',
            pleaseEnterIdCardAddress: '请输入证件地址',
            pleaseEnterBirthPlace: '请输入出生地',
            pleaseEnterResidentialAddress: '请输入居住地址',
            pleaseEnterMailingAddress: '请输入邮寄地址',
            pleaseEnterCompanyName: '请输入公司名称',
            pleaseEnterCompanyNameEn: '请输入公司英文名称',
            pleaseEnterCompanyAddress: '请输入公司地址',
            pleaseEnterCompanyIndustry: '请输入公司行业',
            pleaseEnterPosition: '请输入职位',
            pleaseEnterAnnualIncome: '请输入年收入',
            submitPolicyholderInfoFormData: '提交投保人信息表单数据',
            policyholderInfoUpdateSuccess: '投保人信息更新成功',
            updatePolicyholderInfoFailed: '更新投保人信息失败',
            submitInsuredInfoFormData: '提交受保人信息表单数据',
            insuredInfoUpdateSuccess: '受保人信息更新成功',
            updateInsuredInfoFailed: '更新受保人信息失败',
            relationship: '关系',
            sibling: '兄弟姐妹',
            friend: '朋友',
            addBeneficiary: '添加受益人',
            noBeneficiaryInfo: '暂无受益人信息',
            editBeneficiary: '编辑受益人',
            pleaseEnterName: '请输入姓名',
            pleaseSelectRelationship: '请选择关系',
            pleaseEnterBenefitPercentage: '请输入分配比例',
            pleaseSelectIsTrustee: '请选择是否为托管人',
            confirmDeleteBeneficiary: '确认删除受益人 {name}？',
            beneficiaryDeleteSuccess: '受益人删除成功',
            deleteBeneficiaryFailed: '删除受益人失败',
            addNewBeneficiaryData: '添加新受益人数据',
            benefitPercentageExceed: '受益比例总和不能超过100%，当前总和为 {current}%',
            beneficiaryAddSuccess: '受益人添加成功',
            updateBeneficiaryData: '更新受益人数据',
            beneficiaryUpdateSuccess: '受益人更新成功',
            benefitPercentageMustBe100: '受益比例总和必须为100%',
            submitBeneficiaryListData: '提交受益人列表数据',
            updateBeneficiaryFailed: '更新受益人失败',
            isTrustee: '是否托管人',
            totalBenefitPercentage: '总受益比例',
            pleaseAddBeneficiary: '请添加受益人',
            add: '添加',
            item: '项目',
            beneficiary: '受益人',
            pleaseEnter: '请输入',
            pleaseSelect: '请选择',
            pleaseEnterIdNumber: '请输入证件号码',
            pleaseSelectPaymentDate: '请选择缴费日期',
            pleaseEnterPaymentAmount: '请输入保费金额',
            pleaseSelectPaymentMethod: '请选择缴费方式',
            editInfo: '编辑信息',
            formValidationFailed: '表单验证失败',
            pleaseEnter: '请输入',
            pleaseSelect: '请选择',
            item: '项目',
            add: '添加',
            delete: '删除',
            validationFailed: '表单验证失败',
            currentStatus: '当前状态',
            appointmentTime: '预约时间',
            customerName: '客户姓名',
            firstPremiumPaymentMethod: '首期保费支付方式',
            renewalPaymentMethod: '续期保费支付方式',
            coverageAmount: '保障金额',
            pleaseEnterCustomerName: '请输入客户姓名',
            pleaseSelectAppointmentTime: '请选择预约时间',
            pleaseSelectFirstPremiumPaymentMethod: '请选择首期保费支付方式',
            pleaseSelectRenewalPaymentMethod: '请选择续期保费支付方式',
            pleaseEnterCoverageAmount: '请输入保障金额',
            statistics: '保单统计',
            totalPolicies: '保单总数',
            totalPoliciesDesc: '系统中的保单总数量',
            effectivePolicies: '生效保单',
            effectivePoliciesDesc: '当前生效中的保单数量',
            pendingPolicies: '待处理保单',
            pendingPoliciesDesc: '待处理的保单数量',
            expiringPolicies: '即将到期保单',
            expiringPoliciesDesc: '30天内即将到期的保单',
            reset: '重置'
        }
    },
    'en-US': {
        policyorder: {
            policyStatus: 'Policy Status',
            orderStatus: 'Order Status',
            pleaseSelectPolicyStatus: 'Please select policy status',
            policyOrder: 'Policy Order',
            policyOrderList: 'Policy Order List',
            policyOrderDetail: 'Policy Order Detail',
            orderManagement: 'Order Management',
            nextRenewalAmount: 'Next Renewal Amount',
            nextRenewalDate: 'Next Renewal Date',
            policyOrderCreate: 'Create Policy Order',
            policyOrderUpdate: 'Update Policy Order',
            policyOrderDelete: 'Delete Policy Order',
            policyOrderSearch: 'Search Policy Order',
            policyManagement: 'Policy Management',
            browseAndManage: 'Browse and manage your policy orders, providing complete policy services.',
            refreshData: 'Refresh Data',
            deleteSuccess: 'Delete Successful',
            deletePolicy: 'Delete Policy',
            deleteFailed: 'Failed to delete policy',
            confirmDelete: 'Are you sure you want to delete this order? This action cannot be undone',
            confirm: 'Confirm',
            cancel: 'Cancel',
            isSmoker: 'Is Smoker',
            idCardAddress: 'ID Card Address',
            loadPolicyDataFailed: 'Failed to load policy data',
            policyDetail: 'Policy Detail',
            loading: 'Loading...',
            pleaseWait: 'Please wait',
            noPolicyDetail: 'No policy details available',
            loadDetailFailed: 'Failed to load policy details, please try again later',
            fileUploadSuccess: 'File uploaded successfully',
            fileUploadFailed: 'File upload failed',
            paymentRecordSaveSuccess: 'Payment record saved successfully',
            paymentRecordSaveFailed: 'Failed to save payment record',
            editPayment: 'Edit Payment',
            editPaymentTodo: 'Edit payment function to be implemented',
            deletePayment: 'Delete Payment',
            deletePaymentSuccess: 'Payment record deleted successfully',
            status: 'Status',
            region: 'Region',
            insuranceCompany: 'Insurance Company',
            searchPlaceholder: 'Search order number/policyholder/policy number',
            all: 'All',
            hongKong: 'Hong Kong',
            macau: 'Macau',
            singapore: 'Singapore',
            mainlandChina: 'Mainland China',
            bermuda: 'Bermuda',
            chinaLifeSG: 'China Life (Singapore)',
            chinaLifeHK: 'China Life (Hong Kong)',
            aiaHK: 'AIA (Hong Kong)',
            prudentialSG: 'Prudential (Singapore)',
            hsbcLife: 'HSBC Life',
            manulife: 'Manulife',
            sunlife: 'Sun Life',
            bocLife: 'BOC Life',
            chinaTaiping: 'China Taiping',
            orderNo: 'Order No.',
            policyholder: 'Policyholder',
            insured: 'Insured',
            company: 'Company',
            product: 'Product',
            policyNo: 'Policy No.',
            paymentTerm: 'Payment Term',
            annualPremium: 'Annual Premium',
            effectiveDate: 'Effective Date',
            nextRenewal: 'Next Renewal',
            action: 'Action',
            viewDetail: 'View Detail',
            confirmDelete: 'Are you sure you want to delete this policy?',
            confirm: 'Confirm',
            cancel: 'Cancel',
            notEffective: 'Not Effective',
            appointment: 'Appointment',
            waitPayment: 'Waiting for Payment',
            quietPeriod: 'Cooling-off Period',
            inEffect: 'In Effect',
            expired: 'Expired',
            pending: 'Pending',
            appointmentInfo: 'Appointment Information',
            policyInfo: 'Policy Information',
            policyType: 'Policy Type',
            currency: 'Currency',
            applicationDate: 'Application Date',
            issueDate: 'Issue Date',
            expiryDate: 'Expiry Date',
            policyholderInfo: 'Policyholder Information',
            name: 'Name',
            nameEn: 'English Name',
            gender: 'Gender',
            male: 'Male',
            female: 'Female',
            birthdate: 'Date of Birth',
            idType: 'ID Type',
            idNumber: 'ID Number',
            nationality: 'Nationality',
            phoneNumber: 'Phone Number',
            email: 'Email',
            address: 'Address',
            insuredInfo: 'Insured Information',
            relationshipWithPolicyholder: 'Relationship with Policyholder',
            self: 'Self',
            spouse: 'Spouse',
            child: 'Child',
            parent: 'Parent',
            other: 'Other',
            beneficiaryInfo: 'Beneficiary Information',
            beneficiaryType: 'Beneficiary Type',
            beneficiaryShare: 'Share Percentage',
            primary: 'Primary',
            contingent: 'Contingent',
            paymentRecords: 'Payment Records',
            addPayment: 'Add Payment',
            paymentDate: 'Payment Date',
            paymentAmount: 'Premium Amount',
            paymentMethod: 'Payment Method',
            paymentStatus: 'Payment Status',
            bankTransfer: 'Bank Transfer',
            creditCard: 'Credit Card',
            cheque: 'Cheque',
            cash: 'Cash',
            paid: 'Paid',
            height: 'Height',
            weight: 'Weight',
            unpaid: 'Unpaid',
            policyHistory: 'Policy History',
            currentStatus: 'Current Status',
            statusChangeTime: 'Status Change Time',
            operator: 'Operator',
            remarks: 'Remarks',
            addPaymentRecord: 'Add Payment Record',
            save: 'Save',
            close: 'Close',
            uploadFile: 'Upload File',
            fileType: 'File Type',
            selectFile: 'Select File',
            upload: 'Upload',
            fileTypeRequired: 'Please select file type',
            fileRequired: 'Please upload file',
            editInfo: 'Edit Information',
            saveChanges: 'Save Changes',
            policyFiles: 'Policy Files',
            fileName: 'File Name',
            fileSize: 'File Size',
            uploadTime: 'Upload Time',
            download: 'Download',
            preview: 'Preview',
            noFiles: 'No files',
            cny: 'CNY (Chinese Yuan)',
            hkd: 'HKD (Hong Kong Dollar)',
            usd: 'USD (US Dollar)',
            sgd: 'SGD (Singapore Dollar)',
            policyholderNameCn: 'Policyholder Name (Chinese)',
            policyholderNameEn: 'Policyholder Name (English)',
            insuredNameCn: 'Insured Name (Chinese)',
            insuredNameEn: 'Insured Name (English)',
            team: 'Team',
            referrer: 'Referrer',
            signDate: 'Sign Date',
            pleaseEnterPolicyholderName: 'Please enter policyholder name',
            pleaseEnterPolicyholderNameEn: 'Please enter policyholder English name',
            pleaseEnterInsuredName: 'Please enter insured name',
            pleaseEnterInsuredNameEn: 'Please enter insured English name',
            pleaseEnterRegion: 'Please enter region',
            pleaseEnterCompany: 'Please enter company',
            pleaseEnterProduct: 'Please enter product',
            pleaseEnterPhoneNumber: 'Please enter phone number',
            pleaseEnterValidPhoneNumber: 'Please enter valid phone number',
            pleaseEnterEmail: 'Please enter email',
            pleaseEnterValidEmail: 'Please enter valid email',
            pleaseEnterTeam: 'Please enter team',
            pleaseEnterReferrer: 'Please enter referrer',
            pleaseSelectSignDate: 'Please select sign date',
            pleaseEnterPaymentTerm: 'Please enter payment term',
            pleaseSelectCurrency: 'Please select currency',
            beneficiaryList: 'Beneficiary List',
            pleaseEnterAnnualPremium: 'Please enter annual premium',
            pleaseSelectNextRenewalDate: 'Please select next renewal date',
            pleaseSelectStatus: 'Please select status',
            pleaseEnterRemarks: 'Please enter remarks',
            submitBasicInfoFormData: 'Submit basic info form data',
            basicInfoUpdateSuccess: 'Basic information updated successfully',
            updateBasicInfoFailed: 'Failed to update basic information',
            paymentGuide: 'Payment Guide',
            paymentRecord: 'Payment Record',
            effective: 'Effective',
            contractSent: 'Contract Sent',
            signature: 'Signature',
            quietPeriodExpired: 'Cooling-off Period Expired',
            signAppointment: 'Sign Appointment',
            sign: 'Signed',
            paymentProof: 'Payment Proof',
            identification: 'Identification',
            medicalReport: 'Medical Report',
            otherFile: 'Other File',
            selectUploadStatus: 'Select Upload Status',
            completed: 'Completed',
            pendingCompletion: 'Pending Completion',
            uploadRelatedFileTooltip: 'Upload Related File',
            no: 'No',
            noPaymentRecords: 'No Payment Records',
            dragFileText: 'Click or drag file to this area to upload',
            uploadHint: 'Support single file upload, file size should not exceed 10MB',
            fileSizeLimit: 'File size cannot exceed 10MB',
            remarkPlaceholder: 'Please enter remarks',
            nextStatusTooltip: 'Confirm next status',
            previousStatusTooltip: 'Rollback to previous status',
            confirmOperation: 'Confirm Operation',
            confirmNextStatus: 'Confirm updating status from {status} to next status?',
            confirmRollback: 'Confirm Rollback',
            confirmRollbackStatus: 'Confirm rolling back status from {status} to previous status?',
            statusUpdateSuccess: 'Status updated successfully',
            statusUpdateFailed: 'Status update failed',
            statusRollbackSuccess: 'Status rolled back successfully',
            statusRollbackFailed: 'Status rollback failed',
            invalidFileLink: 'Invalid file link',
            filePreviewTip: 'File Preview Tip',
            filePreviewNotSupported: 'Preview not supported for file type: {type}',
            downloadFile: 'Download File',
            browserBlockedPopup: 'Browser blocked popup',
            downloadTip: 'Download Tip',
            downloadAlternative: 'You can manually copy the link to a new tab: {url}',
            iKnow: 'I know',
            fileDownloadError: 'File download error',
            fileDownloadFailed: 'File download failed',
            fileWillOpenInNewTab: 'File {name} will open in a new tab',
            attachments: 'Attachments',
            expandAll: 'Expand All',
            collapse: 'Collapse',
            clickToPreview: 'Click to preview',
            showAllAttachments: 'Show all {count} attachments',
            noHistoryRecords: 'No history records',
            rollback: 'Rollback',
            formValidationFailed: 'Form validation failed',
            editInsuredInfo: 'Edit Insured Information',
            edit: 'Edit',
            editPolicyholderInfo: 'Edit Policyholder Information',
            addressInfo: 'Address Information',
            occupationInfo: 'Occupation Information',
            birthPlace: 'Birth Place',
            residentialAddress: 'Residential Address',
            mailingAddress: 'Mailing Address',
            companyName: 'Company Name',
            companyNameCn: 'Company Name (Chinese)',
            companyNameEn: 'Company Name (English)',
            companyAddress: 'Company Address',
            companyIndustry: 'Company Industry',
            position: 'Position',
            annualIncome: 'Annual Income',
            nameCn: 'Name (Chinese)',
            travelPermitNo: 'Travel Permit No.',
            maritalStatus: 'Marital Status',
            single: 'Single',
            married: 'Married',
            divorced: 'Divorced',
            widowed: 'Widowed',
            educationLevel: 'Education Level',
            primarySchool: 'Primary School',
            middleSchool: 'Middle School',
            highSchool: 'High School',
            college: 'College',
            bachelor: 'Bachelor',
            master: 'Master',
            doctor: 'Doctor',
            yes: 'Yes',
            pleaseEnterNameEn: 'Please enter English name',
            pleaseSelectGender: 'Please select gender',
            pleaseSelectBirthdate: 'Please select birthdate',
            pleaseEnterValidIdNumber: 'Please enter a valid ID number',
            pleaseEnterTravelPermitNo: 'Please enter travel permit number',
            pleaseEnterNationality: 'Please enter nationality',
            pleaseSelectMaritalStatus: 'Please select marital status',
            pleaseSelectEducationLevel: 'Please select education level',
            pleaseEnterHeight: 'Please enter height',
            pleaseEnterWeight: 'Please enter weight',
            pleaseSelectIsSmoker: 'Please select if smoker',
            pleaseEnterIdCardAddress: 'Please enter ID card address',
            pleaseEnterBirthPlace: 'Please enter birth place',
            pleaseEnterResidentialAddress: 'Please enter residential address',
            pleaseEnterMailingAddress: 'Please enter mailing address',
            pleaseEnterCompanyName: 'Please enter company name',
            pleaseEnterCompanyNameEn: 'Please enter company English name',
            pleaseEnterCompanyAddress: 'Please enter company address',
            pleaseEnterCompanyIndustry: 'Please enter company industry',
            pleaseEnterPosition: 'Please enter position',
            pleaseEnterAnnualIncome: 'Please enter annual income',
            submitPolicyholderInfoFormData: 'Submit policyholder info form data',
            policyholderInfoUpdateSuccess: 'Policyholder information updated successfully',
            updatePolicyholderInfoFailed: 'Failed to update policyholder information',
            submitInsuredInfoFormData: 'Submit insured info form data',
            insuredInfoUpdateSuccess: 'Insured information updated successfully',
            updateInsuredInfoFailed: 'Failed to update insured information',
            relationship: 'Relationship',
            sibling: 'Sibling',
            friend: 'Friend',
            addBeneficiary: 'Add Beneficiary',
            noBeneficiaryInfo: 'No beneficiary information',
            editBeneficiary: 'Edit Beneficiary',
            pleaseEnterName: 'Please enter name',
            pleaseSelectRelationship: 'Please select relationship',
            pleaseEnterBenefitPercentage: 'Please enter benefit percentage',
            pleaseSelectIsTrustee: 'Please select if trustee',
            confirmDeleteBeneficiary: 'Confirm delete beneficiary {name}?',
            beneficiaryDeleteSuccess: 'Beneficiary deleted successfully',
            deleteBeneficiaryFailed: 'Failed to delete beneficiary',
            addNewBeneficiaryData: 'Add new beneficiary data',
            benefitPercentageExceed: 'Total benefit percentage cannot exceed 100%, current total is {current}%',
            beneficiaryAddSuccess: 'Beneficiary added successfully',
            updateBeneficiaryData: 'Update beneficiary data',
            beneficiaryUpdateSuccess: 'Beneficiary updated successfully',
            benefitPercentageMustBe100: 'Benefit percentage total must be 100%',
            submitBeneficiaryListData: 'Submit beneficiary list data',
            updateBeneficiaryFailed: 'Failed to update beneficiary',
            isTrustee: 'Is Trustee',
            totalBenefitPercentage: 'Total Benefit Percentage',
            pleaseAddBeneficiary: 'Please add beneficiary',
            add: 'Add',
            item: 'Item',
            beneficiary: 'Beneficiary',
            pleaseEnter: 'Please enter',
            pleaseSelect: 'Please select',
            pleaseEnterIdNumber: 'Please enter ID number',
            pleaseSelectPaymentDate: 'Please select payment date',
            pleaseEnterPaymentAmount: 'Please enter premium amount',
            pleaseSelectPaymentMethod: 'Please select payment method',
            editInfo: 'Edit Information',
            formValidationFailed: 'Form validation failed',
            item: 'Item',
            add: 'Add',
            delete: 'Delete',
            validationFailed: 'Form validation failed',
            currentStatus: 'Current status',
            appointmentTime: 'Appointment Time',
            customerName: 'Customer Name',
            firstPremiumPaymentMethod: 'First Premium Payment Method',
            renewalPaymentMethod: 'Renewal Payment Method',
            coverageAmount: 'Coverage Amount',
            pleaseEnterCustomerName: 'Please enter customer name',
            pleaseSelectAppointmentTime: 'Please select appointment time',
            pleaseSelectFirstPremiumPaymentMethod: 'Please select first premium payment method',
            pleaseSelectRenewalPaymentMethod: 'Please select renewal payment method',
            pleaseEnterCoverageAmount: 'Please enter coverage amount',
            statistics: 'Policy Statistics',
            totalPolicies: 'Total Policies',
            totalPoliciesDesc: 'Total number of policies in the system',
            effectivePolicies: 'Effective Policies',
            effectivePoliciesDesc: 'Number of currently effective policies',
            pendingPolicies: 'Pending Policies',
            pendingPoliciesDesc: 'Number of policies pending processing',
            expiringPolicies: 'Expiring Policies',
            expiringPoliciesDesc: 'Policies expiring within 30 days',
            reset: 'Reset'
        }
    },
    'zh-HK': {
        policyorder: {
            policyStatus: '保單狀態',
            orderStatus: '訂單狀態',
            pleaseSelectPolicyStatus: '請選擇保單狀態',
            policyOrder: '預約信息',
            policyOrderList: '預約信息列表',
            policyOrderDetail: '預約信息詳情',
            nextRenewalAmount: '下次續保金額',
            nextRenewalDate: '下次續保日期',
            policyOrderCreate: '創建預約信息',
            isSmoker: '是否吸煙',
            idCardAddress: '證件地址',
            policyOrderUpdate: '更新預約信息',
            policyOrderDelete: '刪除預約信息',
            policyOrderSearch: '搜索預約信息',
            policyManagement: '保單管理',
            browseAndManage: '瀏覽和管理您的保單訂單，提供完整的保單服務。',
            refreshData: '刷新數據',
            height: '身高',
            orderManagement: '訂單管理',
            weight: '體重',
            deleteSuccess: '刪除成功',
            deletePolicy: '刪除保單',
            deleteFailed: '刪除保單失敗',
            confirmDelete: '確認刪除此訂單嗎？此操作不可恢復',
            confirm: '確認',
            cancel: '取消',
            loadPolicyDataFailed: '加載保單數據失敗',
            policyDetail: '保單詳情',
            loading: '加載中...',
            pleaseWait: '請稍等',
            noPolicyDetail: '暫無保單詳情',
            loadDetailFailed: '加載保單詳情失敗，請稍後再試',
            fileUploadSuccess: '文件上傳成功',
            fileUploadFailed: '文件上傳失敗',
            paymentRecordSaveSuccess: '繳費記錄保存成功',
            paymentRecordSaveFailed: '保存繳費記錄失敗',
            editPayment: '編輯繳費記錄',
            editPaymentTodo: '編輯繳費記錄功能待實現',
            deletePayment: '刪除繳費記錄',
            deletePaymentSuccess: '刪除繳費記錄成功',
            status: '狀態',
            region: '地區',
            insuranceCompany: '保險公司',
            searchPlaceholder: '搜索訂單號/投保人/保單號',
            all: '全部',
            hongKong: '香港',
            macau: '澳門',
            singapore: '新加坡',
            mainlandChina: '中國大陸',
            bermuda: '百慕達',
            chinaLifeSG: '國壽（新加坡）',
            chinaLifeHK: '國壽（香港）',
            aiaHK: '友邦（香港）',
            prudentialSG: '保誠（新加坡）',
            hsbcLife: '匯豐人壽',
            manulife: '宏利',
            sunlife: '永明',
            bocLife: '中銀人壽',
            chinaTaiping: '中國太平',
            orderNo: '訂單號',
            policyholder: '投保人',
            insured: '受保人',
            company: '保險公司',
            product: '簽約產品',
            policyNo: '保單號',
            paymentTerm: '繳費期限',
            annualPremium: '年繳保費',
            effectiveDate: '生效日期',
            nextRenewal: '下次續保',
            action: '操作',
            viewDetail: '查看詳情',
            confirmDelete: '確定要刪除這個保單嗎?',
            confirm: '確定',
            cancel: '取消',
            notEffective: '未生效',
            appointment: '已預約',
            waitPayment: '待繳費',
            quietPeriod: '冷靜期',
            inEffect: '生效中',
            expired: '已失效',
            pending: '代辦',
            appointmentInfo: '預約信息',
            policyInfo: '保單信息',
            policyType: '保單類型',
            currency: '幣種',
            applicationDate: '投保日期',
            issueDate: '簽發日期',
            expiryDate: '到期日期',
            policyholderInfo: '投保人信息',
            name: '姓名',
            nameEn: '英文姓名',
            gender: '性別',
            male: '男',
            female: '女',
            birthdate: '出生日期',
            idType: '證件類型',
            idNumber: '證件號碼',
            nationality: '國籍',
            phoneNumber: '聯繫電話',
            email: '電子郵箱',
            address: '聯繫地址',
            insuredInfo: '受保人信息',
            relationshipWithPolicyholder: '與投保人關係',
            self: '本人',
            spouse: '配偶',
            child: '子女',
            parent: '父母',
            other: '其他',
            beneficiaryInfo: '受益人信息',
            beneficiaryType: '受益人類型',
            beneficiaryShare: '分配比例',
            primary: '第一受益人',
            contingent: '第二受益人',
            paymentRecords: '繳費記錄',
            addPayment: '添加繳費記錄',
            paymentDate: '繳費日期',
            paymentAmount: '保費金額',
            paymentMethod: '繳費方式',
            paymentStatus: '繳費狀態',
            bankTransfer: '銀行轉賬',
            creditCard: '信用卡',
            cheque: '支票',
            cash: '現金',
            paid: '已支付',
            unpaid: '未支付',
            policyHistory: '保單歷史狀態',
            currentStatus: '當前狀態',
            statusChangeTime: '狀態變更時間',
            operator: '操作人',
            remarks: '備註',
            addPaymentRecord: '添加繳費記錄',
            save: '保存',
            close: '關閉',
            uploadFile: '上傳文件',
            fileType: '文件類型',
            selectFile: '選擇文件',
            upload: '上傳',
            fileTypeRequired: '請選擇文件類型',
            fileRequired: '請上傳文件',
            editInfo: '編輯信息',
            saveChanges: '保存更改',
            policyFiles: '保單文件',
            fileName: '文件名',
            fileSize: '文件大小',
            uploadTime: '上傳時間',
            download: '下載',
            preview: '預覽',
            noFiles: '暫無文件',
            cny: '人民幣 (CNY)',
            hkd: '港幣 (HKD)',
            usd: '美元 (USD)',
            sgd: '新加坡元 (SGD)',
            policyholderNameCn: '投保人姓名(中文)',
            policyholderNameEn: '投保人姓名(英文)',
            insuredNameCn: '受保人姓名(中文)',
            insuredNameEn: '受保人姓名(英文)',
            team: '團隊',
            referrer: '推薦人',
            signDate: '簽約日期',
            pleaseEnterPolicyholderName: '請輸入投保人姓名',
            pleaseEnterPolicyholderNameEn: '請輸入投保人英文姓名',
            pleaseEnterInsuredName: '請輸入受保人姓名',
            pleaseEnterInsuredNameEn: '請輸入受保人英文姓名',
            pleaseEnterRegion: '請輸入地區',
            pleaseEnterCompany: '請輸入保險公司',
            pleaseEnterProduct: '請輸入簽約產品',
            pleaseEnterPhoneNumber: '請輸入聯繫電話',
            pleaseEnterValidPhoneNumber: '請輸入有效的手機號碼',
            pleaseEnterEmail: '請輸入電子郵箱',
            pleaseEnterValidEmail: '請輸入有效的電子郵箱',
            pleaseEnterTeam: '請輸入團隊',
            pleaseEnterReferrer: '請輸入推薦人',
            pleaseSelectSignDate: '請選擇簽約日期',
            pleaseEnterPaymentTerm: '請輸入繳費期限',
            pleaseSelectCurrency: '請選擇幣種',
            pleaseEnterAnnualPremium: '請輸入年繳保費',
            pleaseSelectNextRenewalDate: '請選擇下次續保日',
            pleaseSelectStatus: '請選擇訂單狀態',
            pleaseEnterRemarks: '請輸入備註',
            submitBasicInfoFormData: '提交基本信息表單數據',
            basicInfoUpdateSuccess: '基本信息更新成功',
            updateBasicInfoFailed: '更新基本信息失敗',
            paymentGuide: '繳費指引',
            paymentRecord: '繳費記錄',
            effective: '已生效',
            contractSent: '合同已寄出',
            signature: '已簽名',
            quietPeriodExpired: '冷靜期已過',
            signAppointment: '預約簽約',
            sign: '已簽約',
            paymentProof: '繳費憑證',
            identification: '身份證明',
            medicalReport: '體檢報告',
            otherFile: '其他文件',
            selectUploadStatus: '選擇上傳狀態',
            completed: '已完成',
            pendingCompletion: '待完成',
            uploadRelatedFileTooltip: '上傳相關文件',
            no: '否',
            noPaymentRecords: '暫無繳費記錄',
            dragFileText: '點擊或拖拽文件到此區域上傳',
            uploadHint: '支持單個文件上傳，文件大小不超過10MB',
            fileSizeLimit: '文件大小不能超過10MB',
            remarkPlaceholder: '請輸入備註信息',
            nextStatusTooltip: '確認進入下一狀態',
            previousStatusTooltip: '回退到上一狀態',
            confirmOperation: '確認操作',
            confirmNextStatus: '確認將狀態從 {status} 更新為下一狀態？',
            confirmRollback: '確認回退',
            confirmRollbackStatus: '確認將狀態從 {status} 回退到上一狀態？',
            statusUpdateSuccess: '狀態更新成功',
            statusUpdateFailed: '狀態更新失敗',
            statusRollbackSuccess: '狀態回退成功',
            statusRollbackFailed: '狀態回退失敗',
            invalidFileLink: '無效的文件鏈接',
            filePreviewTip: '文件預覽提示',
            filePreviewNotSupported: '不支持預覽此類型的文件：{type}',
            downloadFile: '下載文件',
            browserBlockedPopup: '瀏覽器阻止了彈出窗口',
            downloadTip: '下載提示',
            downloadAlternative: '您可以手動複製鏈接到新標籤頁：{url}',
            iKnow: '我知道了',
            fileDownloadError: '文件下載錯誤',
            fileDownloadFailed: '文件下載失敗',
            fileWillOpenInNewTab: '文件 {name} 將在新標籤頁中打開',
            attachments: '附件',
            expandAll: '展開全部',
            collapse: '收起',
            clickToPreview: '點擊預覽',
            showAllAttachments: '顯示全部 {count} 個附件',
            noHistoryRecords: '暫無歷史記錄',
            rollback: '回退',
            formValidationFailed: '表單驗證失敗',
            editInsuredInfo: '編輯受保人信息',
            edit: '編輯',
            editPolicyholderInfo: '編輯投保人信息',
            addressInfo: '地址信息',
            occupationInfo: '職業信息',
            birthPlace: '出生地',
            residentialAddress: '居住地址',
            mailingAddress: '郵寄地址',
            companyName: '公司名稱',
            companyNameCn: '公司名稱(中文)',
            companyNameEn: '公司名稱(英文)',
            companyAddress: '公司地址',
            companyIndustry: '公司行業',
            position: '職位',
            annualIncome: '年收入',
            nameCn: '姓名(中文)',
            travelPermitNo: '通行證號碼',
            maritalStatus: '婚姻狀況',
            single: '未婚',
            married: '已婚',
            divorced: '離異',
            widowed: '喪偶',
            educationLevel: '教育程度',
            primarySchool: '小學',
            middleSchool: '初中',
            highSchool: '高中',
            college: '大專',
            bachelor: '本科',
            master: '碩士',
            doctor: '博士',
            yes: '是',
            pleaseEnterNameEn: '請輸入英文姓名',
            pleaseSelectGender: '請選擇性別',
            pleaseSelectBirthdate: '請選擇出生日期',
            pleaseEnterValidIdNumber: '請輸入有效的證件號碼',
            pleaseEnterTravelPermitNo: '請輸入通行證號碼',
            pleaseEnterNationality: '請輸入國籍',
            pleaseSelectMaritalStatus: '請選擇婚姻狀況',
            pleaseSelectEducationLevel: '請選擇教育程度',
            pleaseEnterHeight: '請輸入身高',
            pleaseEnterWeight: '請輸入體重',
            pleaseSelectIsSmoker: '請選擇是否吸煙',
            pleaseEnterIdCardAddress: '請輸入證件地址',
            pleaseEnterBirthPlace: '請輸入出生地',
            pleaseEnterResidentialAddress: '請輸入居住地址',
            pleaseEnterMailingAddress: '請輸入郵寄地址',
            pleaseEnterCompanyName: '請輸入公司名稱',
            pleaseEnterCompanyNameEn: '請輸入公司英文名稱',
            pleaseEnterCompanyAddress: '請輸入公司地址',
            pleaseEnterCompanyIndustry: '請輸入公司行業',
            pleaseEnterPosition: '請輸入職位',
            pleaseEnterAnnualIncome: '請輸入年收入',
            submitPolicyholderInfoFormData: '提交投保人信息表單數據',
            policyholderInfoUpdateSuccess: '投保人信息更新成功',
            updatePolicyholderInfoFailed: '更新投保人信息失敗',
            submitInsuredInfoFormData: '提交受保人信息表單數據',
            insuredInfoUpdateSuccess: '受保人信息更新成功',
            updateInsuredInfoFailed: '更新受保人信息失敗',
            relationship: '關係',
            sibling: '兄弟姐妹',
            friend: '朋友',
            addBeneficiary: '添加受益人',
            noBeneficiaryInfo: '暫無受益人信息',
            editBeneficiary: '編輯受益人',
            pleaseEnterName: '請輸入姓名',
            pleaseSelectRelationship: '請選擇關係',
            pleaseEnterBenefitPercentage: '請輸入分配比例',
            pleaseSelectIsTrustee: '請選擇是否為托管人',
            confirmDeleteBeneficiary: '確認刪除受益人 {name}？',
            beneficiaryDeleteSuccess: '受益人刪除成功',
            deleteBeneficiaryFailed: '刪除受益人失敗',
            addNewBeneficiaryData: '添加新受益人數據',
            benefitPercentageExceed: '受益比例總和不能超過100%，當前總和為 {current}%',
            beneficiaryAddSuccess: '受益人添加成功',
            updateBeneficiaryData: '更新受益人數據',
            beneficiaryUpdateSuccess: '受益人更新成功',
            benefitPercentageMustBe100: '受益比例總和必須為100%',
            submitBeneficiaryListData: '提交受益人列表數據',
            updateBeneficiaryFailed: '更新受益人失敗',
            isTrustee: '是否托管人',
            totalBenefitPercentage: '總受益比例',
            pleaseAddBeneficiary: '請添加受益人',
            add: '添加',
            item: '項目',
            beneficiary: '受益人',
            pleaseEnter: '請輸入',
            pleaseSelect: '請選擇',
            pleaseEnterIdNumber: '請輸入證件號碼',
            pleaseSelectPaymentDate: '請選擇繳費日期',
            pleaseEnterPaymentAmount: '請輸入保費金額',
            pleaseSelectPaymentMethod: '請選擇繳費方式',
            editInfo: '編輯信息',
            formValidationFailed: '表單驗證失敗',
            item: '項目',
            add: '添加',
            delete: '刪除',
            validationFailed: '表單驗證失敗',
            currentStatus: '當前狀態',
            appointmentTime: '預約時間',
            customerName: '客戶姓名',
            firstPremiumPaymentMethod: '首期保費支付方式',
            renewalPaymentMethod: '續期保費支付方式',
            coverageAmount: '保障金額',
            pleaseEnterCustomerName: '請輸入客戶姓名',
            pleaseSelectAppointmentTime: '請選擇預約時間',
            pleaseSelectFirstPremiumPaymentMethod: '請選擇首期保費支付方式',
            pleaseSelectRenewalPaymentMethod: '請選擇續期保費支付方式',
            pleaseEnterCoverageAmount: '請輸入保障金額',
            statistics: '保單統計',
            totalPolicies: '保單總數',
            totalPoliciesDesc: '系統中的保單總數量',
            effectivePolicies: '生效保單',
            effectivePoliciesDesc: '當前生效中的保單數量',
            pendingPolicies: '待處理保單',
            pendingPoliciesDesc: '待處理的保單數量',
            expiringPolicies: '即將到期保單',
            expiringPoliciesDesc: '30天內即將到期的保單',
            reset: '重置'
        }
    }
}