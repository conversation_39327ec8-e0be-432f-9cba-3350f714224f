export default {
    'zh-CN': {
        products: {
            productList: '产品列表',
            productName: '产品名称',
            productCode: '产品代码',
            productType: '产品类型',
            // 选项卡
            allProducts: '所有产品',
            chinaLife: '中国人寿',
            // 筛选器
            category: '险种',
            region: '地区',
            all: '全部',
            // 表格列
            sort: '排序',
            insuranceCompany: '保险公司',
            productStatus: '产品状态',
            // 状态
            selling: '在售',
            notSelling: '停售',
            // 操作
            action: '操作',
            viewDetails: '查看详情',
            // 搜索
            searchPlaceholder: '搜索产品或保司',
            // 分页
            total: '共 {total} 条',
            // 提示
            refreshData: '刷新数据',
            autoAddedProduct: '已为您自动添加对比产品',
            // 错误信息
            loadProductError: '加载产品数据失败',
            loadChinaLifeProductError: '加载中国人寿产品数据失败',
            // 险种分类
            criticalIllness: '危疾',
            medical: '医疗',
            universalLife: '万用寿险',
            investmentLinked: '投资相连',
            life: '人寿',
            accident: '意外',
            termLife: '定期寿险',
            annuity: '年金',
            others: '其他',
            mpf: '强积金',
            gi: '一般保险',
            indexUniversalLife: '指数型万用寿险',
            variableUniversalLife: '可变万用寿险',
            // 地区
            hongKong: '香港',
            macau: '澳门',
            singapore: '新加坡',
            bermuda: '百慕大',
            mainlandChina: '中国大陆',

            // 新增字段 - 产品页面
            browseAndFind: '浏览和发现优质保险产品，满足您的保障需求。',
            statistics: '产品统计',
            totalProducts: '产品总数',
            totalProductsDesc: '系统中的产品总数量',
            favoriteProducts: '收藏产品',
            favoriteProductsDesc: '您收藏的产品数量',
            categories: '产品分类',
            categoriesDesc: '产品分类种类数量',
            regions: '覆盖地区',
            regionsDesc: '产品覆盖的地区数量',
            reset: '重置',

            // 产品详情页
            backToList: '返回列表',
            productInfoTab: '产品信息',
            productReviewTab: '产品评测',
            productFilesTab: '产品资料',
            filePreview: '文件预览',
            productHighlightsTab: '产品亮点',
            noProductInfo: '未找到产品信息',
            noHighlights: '暂无产品亮点',
            noFiles: '暂无产品资料',
            getProductDetailFailed: '获取产品详情失败',
            productCodeNotExist: '产品代码不存在',

            // 中国人寿产品详情页
            premiumCalculation: '保费试算',
            productComparison: '产品对比',
            generateProposal: '生成计划书',
            productIntroductionTab: '产品介绍',
            productDetailTab: '产品详情',
            productDocumentsTab: '产品文档',
            noProductFound: '未找到中国人寿产品',
            generateProposalFailed: '生成计划书失败',
            incompleteProductInfo: '产品信息不完整，无法生成计划书',

            // 产品亮点组件
            productHighlights: '产品亮点',
            dividendRealizationRate: '分红实现率',
            compoundAndSimpleInterest: '单复利',
            caseReference: '案例参考',
            gender: '性别',
            age: '年龄',
            smokingStatus: '是否吸烟',
            premium: '保费',
            coverageAmount: '保额',
            paymentMode: '缴费方式',
            coverageTerm: '保障年份',
            premiumPaymentTerm: '供款年限',
            policyYear: '保单年度',
            totalPremium: '缴付保费总额',
            netCashFlow: '净现金流',
            cashValue: '现金价值',
            simpleInterest: '单利',
            compoundInterest: 'IRR',

            // 产品介绍组件
            loading: '加载中...',
            productFeatures: '产品特色',
            productDetails: '产品详情',
            productOverview: '产品概览',
            productTerms: '产品条款',
            importantReminder: '重要提醒',
            importantReminderText: '以上资料只供参考。有关本计划之详细条款、细则及除外责任，概以相关保险合约为准。',
            previewBrochure: '预览产品小册子',

            // 产品详情组件
            hideDetails: '隐藏详情',
            showDetails: '查看详情',
            applicable: '适用',
            notApplicable: '不适用',

            // 产品文件组件
            preview: '预览',
            uploader: '上传者',
            uploadTime: '上传时间',
            noDocuments: '暂无产品文档',
            getProductBrochureFailed: '获取产品小册子失败',

            // 文件相关
            invalidFilePath: '文件路径无效',

            // 收藏相关
            favorite: '收藏',
            unfavorite: '取消收藏',
            favoriteSuccess: '收藏成功',
            unfavoriteSuccess: '取消收藏成功',
            pleaseLogin: '请先登录',
            actionFailed: '操作失败'
        }
    },
    'en-US': {
        products: {
            productList: 'Product List',
            productName: 'Product Name',
            productCode: 'Product Code',
            productType: 'Product Type',
            // 选项卡
            allProducts: 'All Products',
            chinaLife: 'China Life',
            // 筛选器
            category: 'Category',
            region: 'Region',
            all: 'All',
            // 表格列
            sort: 'Sort',
            insuranceCompany: 'Insurance Company',
            productStatus: 'Product Status',
            // 状态
            selling: 'Selling',
            notSelling: 'Not Selling',
            productHighlightsTab: 'Product Highlights',
            // 操作
            action: 'Action',
            viewDetails: 'View Details',
            // 搜索
            searchPlaceholder: 'Search products or companies',
            // 分页
            total: 'Total {total} items',
            // 提示
            refreshData: 'Refresh Data',
            autoAddedProduct: 'Comparison product has been automatically added for you',
            // 错误信息
            loadProductError: 'Failed to load product data',
            loadChinaLifeProductError: 'Failed to load China Life product data',
            // 险种分类
            criticalIllness: 'Critical Illness',
            medical: 'Medical',
            universalLife: 'Universal Life',
            investmentLinked: 'Investment-linked',
            life: 'Life',
            accident: 'Accident',
            termLife: 'Term Life',
            annuity: 'Annuity',
            others: 'Others',
            mpf: 'MPF',
            gi: 'General Insurance',
            indexUniversalLife: 'Index Universal Life',
            variableUniversalLife: 'Variable Universal Life',
            // 地区
            hongKong: 'Hong Kong',
            macau: 'Macau',
            singapore: 'Singapore',
            bermuda: 'Bermuda',
            mainlandChina: 'Mainland China',

            // 新增字段 - 产品页面
            browseAndFind: 'Browse and discover quality insurance products to meet your protection needs.',
            statistics: 'Product Statistics',
            totalProducts: 'Total Products',
            totalProductsDesc: 'Total number of products in the system',
            favoriteProducts: 'Favorite Products',
            favoriteProductsDesc: 'Number of products you have favorited',
            categories: 'Categories',
            categoriesDesc: 'Number of product categories',
            regions: 'Regions',
            regionsDesc: 'Number of regions covered by products',
            reset: 'Reset',

            // 产品详情页
            backToList: 'Back to List',
            productInfoTab: 'Product Information',
            productReviewTab: 'Product Review',
            productFilesTab: 'Product Files',
            filePreview: 'File Preview',
            noProductInfo: 'No product information found',
            noHighlights: 'No product highlights available',
            noFiles: 'No product files available',
            getProductDetailFailed: 'Failed to get product details',
            productCodeNotExist: 'Product code does not exist',

            // 中国人寿产品详情页
            premiumCalculation: 'Premium Calculation',
            productComparison: 'Product Comparison',
            generateProposal: 'Generate Proposal',
            productIntroductionTab: 'Product Introduction',
            productDetailTab: 'Product Details',
            productDocumentsTab: 'Product Documents',
            noProductFound: 'No China Life product found',
            generateProposalFailed: 'Failed to generate proposal',
            incompleteProductInfo: 'Product information is incomplete, unable to generate proposal',

            // 产品亮点组件
            productHighlights: 'Product Highlights',
            dividendRealizationRate: 'Dividend Realization Rate',
            compoundAndSimpleInterest: 'Compound & Simple Interest',
            caseReference: 'Case Reference',
            gender: 'Gender',
            age: 'Age',
            smokingStatus: 'Smoking Status',
            premium: 'Premium',
            coverageAmount: 'Coverage Amount',
            paymentMode: 'Payment Mode',
            coverageTerm: 'Coverage Term',
            premiumPaymentTerm: 'Premium Payment Term',
            policyYear: 'Policy Year',
            totalPremium: 'Total Premium',
            netCashFlow: 'Net Cash Flow',
            cashValue: 'Cash Value',
            simpleInterest: 'Simple Interest',
            compoundInterest: 'IRR',

            // 产品介绍组件
            loading: 'Loading...',
            productFeatures: 'Product Features',
            productDetails: 'Product Details',
            productOverview: 'Product Overview',
            productTerms: 'Product Terms',
            importantReminder: 'Important Reminder',
            importantReminderText: 'The above information is for reference only. For details of the terms, conditions and exclusions of the plan, please refer to the relevant insurance contract.',
            previewBrochure: 'Preview Product Brochure',

            // 产品详情组件
            hideDetails: 'Hide Details',
            showDetails: 'Show Details',
            applicable: 'Applicable',
            notApplicable: 'Not Applicable',

            // 产品文件组件
            preview: 'Preview',
            uploader: 'Uploader',
            uploadTime: 'Upload Time',
            noDocuments: 'No product documents available',
            getProductBrochureFailed: 'Failed to get product brochure',

            // 文件相关
            invalidFilePath: 'Invalid file path',

            // Favorite
            favorite: 'Favorite',
            unfavorite: 'Unfavorite',
            favoriteSuccess: 'Favorited successfully',
            unfavoriteSuccess: 'Unfavorited successfully',
            pleaseLogin: 'Please login first',
            actionFailed: 'Action failed'
        }
    },
    'zh-HK': {
        products: {
            productList: '產品列表',
            productName: '產品名稱',
            productCode: '產品代碼',
            productType: '產品類型',
            // 選項卡
            allProducts: '所有產品',
            chinaLife: '中國人壽',
            // 篩選器
            category: '險種',
            region: '地區',
            all: '全部',
            // 表格列
            sort: '排序',
            insuranceCompany: '保險公司',
            productStatus: '產品狀態',
            // 狀態
            selling: '在售',
            notSelling: '停售',
            // 操作
            action: '操作',
            viewDetails: '查看詳情',
            // 搜索
            searchPlaceholder: '搜索產品或保司',
            // 分頁
            total: '共 {total} 條',
            // 提示
            refreshData: '刷新數據',
            autoAddedProduct: '已為您自動添加對比產品',
            // 錯誤信息
            loadProductError: '加載產品數據失敗',
            loadChinaLifeProductError: '加載中國人壽產品數據失敗',
            // 險種分類
            criticalIllness: '危疾',
            medical: '醫療',
            universalLife: '萬用壽險',
            investmentLinked: '投資相連',
            life: '人壽',
            accident: '意外',
            termLife: '定期壽險',
            annuity: '年金',
            others: '其他',
            mpf: '強積金',
            gi: '一般保險',
            indexUniversalLife: '指數型萬用壽險',
            variableUniversalLife: '可變萬用壽險',
            // 地區
            hongKong: '香港',
            macau: '澳門',
            singapore: '新加坡',
            bermuda: '百慕達',
            mainlandChina: '中國大陸',

            // 新增字段 - 产品页面
            browseAndFind: '瀏覽和發現優質保險產品，滿足您的保障需求。',
            statistics: '產品統計',
            totalProducts: '產品總數',
            totalProductsDesc: '系統中的產品總數量',
            favoriteProducts: '收藏產品',
            favoriteProductsDesc: '您收藏的產品數量',
            categories: '產品分類',
            categoriesDesc: '產品分類種類數量',
            regions: '覆蓋地區',
            regionsDesc: '產品覆蓋的地區數量',
            reset: '重置',

            // 產品詳情頁
            backToList: '返回列表',
            productInfoTab: '產品信息',
            productReviewTab: '產品評測',
            productFilesTab: '產品資料',
            filePreview: '文件預覽',
            noProductInfo: '未找到產品信息',
            noHighlights: '暫無產品亮點',
            productHighlightsTab: '產品亮點',
            noFiles: '暫無產品資料',
            getProductDetailFailed: '獲取產品詳情失敗',
            productCodeNotExist: '產品代碼不存在',

            // 中國人壽產品詳情頁
            premiumCalculation: '保費試算',
            productComparison: '產品對比',
            generateProposal: '生成計劃書',
            productIntroductionTab: '產品介紹',
            productDetailTab: '產品詳情',
            productDocumentsTab: '產品文檔',
            noProductFound: '未找到中國人壽產品',
            generateProposalFailed: '生成計劃書失敗',
            incompleteProductInfo: '產品信息不完整，無法生成計劃書',

            // 產品亮點組件
            productHighlights: '產品亮點',
            dividendRealizationRate: '分紅實現率',
            compoundAndSimpleInterest: '單複利',
            caseReference: '案例參考',
            gender: '性別',
            age: '年齡',
            smokingStatus: '是否吸煙',
            premium: '保費',
            coverageAmount: '保額',
            paymentMode: '繳費方式',
            coverageTerm: '保障年份',
            premiumPaymentTerm: '供款年限',
            policyYear: '保單年度',
            totalPremium: '繳付保費總額',
            netCashFlow: '淨現金流',
            cashValue: '現金價值',
            simpleInterest: '單利',
            compoundInterest: 'IRR',

            // 產品介紹組件
            loading: '加載中...',
            productFeatures: '產品特色',
            productDetails: '產品詳情',
            productOverview: '產品概覽',
            productTerms: '產品條款',
            importantReminder: '重要提醒',
            importantReminderText: '以上資料只供參考。有關本計劃之詳細條款、細則及除外責任，概以相關保險合約為準。',
            previewBrochure: '預覽產品小冊子',

            // 產品詳情組件
            hideDetails: '隱藏詳情',
            showDetails: '查看詳情',
            applicable: '適用',
            notApplicable: '不適用',

            // 產品文件組件
            preview: '預覽',
            uploader: '上傳者',
            uploadTime: '上傳時間',
            noDocuments: '暫無產品文檔',
            getProductBrochureFailed: '獲取產品小冊子失敗',

            // 文件相關
            invalidFilePath: '文件路徑無效',

            // 收藏相關
            favorite: '收藏',
            unfavorite: '取消收藏',
            favoriteSuccess: '收藏成功',
            unfavoriteSuccess: '取消收藏成功',
            pleaseLogin: '請先登入',
            actionFailed: '操作失敗'
        }
    }
}