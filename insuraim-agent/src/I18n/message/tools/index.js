// 通用国际化文本
export default {
    'zh-CN': {
        premiumCalculator: {
            title: '保费试算',
            description: '通过选择产品和填写信息，计算您的保险保费',
            productType: '产品类型',
            allTypes: '全部类型',
            searchPlaceholder: '搜索产品名称',
            productList: '产品列表',
            noProducts: '没有找到符合条件的产品',
            calculate: '计算',
            calculationForm: '计算表单',
            age: '年龄',
            noInsuredFee: '该产品保费试算与保额无关',
            insuredFee: '保险金额',
            paymentMode: '缴费方式',
            paymentModes: {
                annual: '年缴',
                semiAnnual: '半年缴',
                quarterly: '季缴',
                monthly: '月缴',
                singlePremium: '趸缴'
            },
            sex: '性别',
            pleaseSelectSex: '请选择性别',
            sexOptions: {
                male: '男',
                female: '女'
            },
            calculationResult: '计算结果',
            premium: '保费',
            ageRange: '年龄范围',
            to: '至',
            years: '岁',
            currency: '¥',
            formValidation: {
                ageRequired: '请输入年龄',
                ageRange: '年龄必须在{min}至{max}岁之间',
                insuredFeeRequired: '请输入保险金额',
                insuredFeePositive: '保险金额必须大于0'
            },
            noResult: '暂无计算结果',
            loading: '计算中...',
            error: '计算出错，请重试',
            tabs: {
                products: '选择产品',
                calculation: '保费计算'
            },
            noProductSelected: '请先选择一个产品进行计算',
            selectProductFirst: '请在"选择产品"标签页中选择一个产品'
        }
    },
    'en-US': {
        premiumCalculator: {
            title: 'Premium Calculator',
            description: 'Calculate your insurance premium by selecting products and filling information',
            productType: 'Product Type',
            noInsuredFee: 'This product premium calculation is not related to the insured amount',
            allTypes: 'All Types',
            searchPlaceholder: 'Search product name',
            productList: 'Product List',
            noProducts: 'No products found',
            calculate: 'Calculate',
            sex: 'Sex',
            pleaseSelectSex: 'Please select sex',
            sexOptions: {
                male: 'Male',
                female: 'Female'
            },
            calculationForm: 'Calculation Form',
            age: 'Age',
            insuredFee: 'Insured Amount',
            paymentMode: 'Payment Mode',
            paymentModes: {
                annual: 'Annual',
                semiAnnual: 'Semi-Annual',
                quarterly: 'Quarterly',
                monthly: 'Monthly',
                singlePremium: 'Single Premium'
            },
            calculationResult: 'Calculation Result',
            premium: 'Premium',
            ageRange: 'Age Range',
            to: 'to',
            years: 'years',
            currency: '$',
            formValidation: {
                ageRequired: 'Please enter age',
                ageRange: 'Age must be between {min} and {max}',
                insuredFeeRequired: 'Please enter insured amount',
                insuredFeePositive: 'Insured amount must be greater than 0'
            },
            noResult: 'No calculation result',
            loading: 'Calculating...',
            error: 'Calculation error, please try again',
            tabs: {
                products: 'Select Product',
                calculation: 'Premium Calculation'
            },
            noProductSelected: 'Please select a product first',
            selectProductFirst: 'Please select a product in the "Select Product" tab'
        }
    },
    'zh-HK': {
        premiumCalculator: {
            title: '保費試算',
            description: '通過選擇產品和填寫信息，計算您的保險保費',
            productType: '產品類型',
            noInsuredFee: '該產品保費試算與保額無關',
            allTypes: '全部類型',
            searchPlaceholder: '搜索產品名稱',
            productList: '產品列表',
            noProducts: '沒有找到符合條件的產品',
            calculate: '計算',
            calculationForm: '計算表單',
            age: '年齡',
            insuredFee: '保險金額',
            paymentMode: '繳費方式',
            paymentModes: {
                annual: '年繳',
                semiAnnual: '半年繳',
                quarterly: '季繳',
                monthly: '月繳',
                singlePremium: '趸繳'
            },
            sex: '性別',
            pleaseSelectSex: '請選擇性別',
            sexOptions: {
                male: '男',
                female: '女'
            },
            calculationResult: '計算結果',
            premium: '保費',
            ageRange: '年齡範圍',
            to: '至',
            years: '歲',
            currency: 'HK$',
            formValidation: {
                ageRequired: '請輸入年齡',
                ageRange: '年齡必須在{min}至{max}歲之間',
                insuredFeeRequired: '請輸入保險金額',
                insuredFeePositive: '保險金額必須大於0'
            },
            noResult: '暫無計算結果',
            loading: '計算中...',
            error: '計算出錯，請重試',
            tabs: {
                products: '選擇產品',
                calculation: '保費計算'
            },
            noProductSelected: '請先選擇一個產品進行計算',
            selectProductFirst: '請在"選擇產品"標籤頁中選擇一個產品'
        }
    }
}; 