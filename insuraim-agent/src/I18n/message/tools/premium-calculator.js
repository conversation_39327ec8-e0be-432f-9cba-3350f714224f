export default {
    'zh-CN': {
        premiumCalculator: {
            title: '保费试算',
            description: '快速计算产品保费，帮助您选择最合适的保险计划。输入您的基本信息即可获得精准保费预估。',
            searchPlaceholder: '搜索产品名称',
            reset: '重置',
            productCount: '产品总数',
            allTypes: '全部类型',
            popularProducts: '热门产品',
            productCountDesc: '当前可计算的产品总数',
            avgPremium: '平均保费',
            avgPremiumDesc: '所有产品的平均保费',
            maxPremium: '最高保费',
            maxPremiumDesc: '最高保费产品',
            minPremium: '最低保费',
            minPremiumDesc: '最低保费产品',
            productList: '产品列表',
            productName: '产品名称',
            productType: '产品类型',
            ageRange: '年龄范围',
            region: '地区',
            selectRegion: '选择地区',
            allRegions: '全部地区',
            action: '操作',
            total: '共计',
            items: '项',
            calculate: '计算保费',
            years: '岁',
            to: '至',
            calculation: '保费计算',
            age: '年龄',
            sex: '性别',
            sexOptions: {
                male: '男',
                female: '女'
            },
            insuredFee: '保额',
            noInsuredFee: '部分产品不需要输入保额',
            paymentMode: '缴费方式',
            paymentModes: {
                annual: '年缴',
                semiAnnual: '半年缴',
                quarterly: '季缴',
                monthly: '月缴'
            },
            premium: '保费金额',
            calculationError: '计算出错',
            tryAgain: '请检查输入并重试',
            noResult: '暂无计算结果',
            fillFormAndCalculate: '请填写表单并点击计算',
            saveQuote: '保存报价',
            ageRequired: '请输入年龄',
            ageMinError: '年龄不能小于最小限制',
            ageMaxError: '年龄不能大于最大限制',
            sexRequired: '请选择性别',
            insuredFeeRequired: '请输入保额',
            insuredFeeMinError: '保额必须大于0',
            paymentModeRequired: '请选择缴费方式',
            error: '操作失败，请重试',
            selectProduct: '选择产品',
            searchOrSelectProduct: '搜索或选择产品',
            selectProductRequired: '请选择产品',
            selectProductType: '选择产品类型',
            productTypeOptions: {
                life: '人寿险'
            },
            company: '保险公司',
            selectCompany: '选择保险公司',
            allCompanies: '全部公司'
        }
    },
    'en-US': {
        premiumCalculator: {
            title: 'Premium Calculator',
            description: 'Calculate product premiums quickly to help you choose the most suitable insurance plan. Enter your basic information for accurate premium estimates.',
            searchPlaceholder: 'Search product name',
            reset: 'Reset',
            productCount: 'Total Products',
            allTypes: 'All Types',
            popularProducts: 'Popular Products',
            productCountDesc: 'Total number of calculable products',
            avgPremium: 'Average Premium',
            avgPremiumDesc: 'Average premium of all products',
            maxPremium: 'Maximum Premium',
            maxPremiumDesc: 'Product with the highest premium',
            minPremium: 'Minimum Premium',
            minPremiumDesc: 'Product with the lowest premium',
            productList: 'Product List',
            productName: 'Product Name',
            productType: 'Product Type',
            ageRange: 'Age Range',
            region: 'Region',
            selectRegion: 'Select Region',
            allRegions: 'All Regions',
            action: 'Action',
            total: 'Total',
            items: 'items',
            calculate: 'Calculate',
            years: 'years',
            to: 'to',
            calculation: 'Premium Calculation',
            age: 'Age',
            sex: 'Gender',
            sexOptions: {
                male: 'Male',
                female: 'Female'
            },
            insuredFee: 'Insured Amount',
            noInsuredFee: 'Some products do not require an insured amount',
            paymentMode: 'Payment Mode',
            paymentModes: {
                annual: 'Annual',
                semiAnnual: 'Semi-Annual',
                quarterly: 'Quarterly',
                monthly: 'Monthly'
            },
            premium: 'Premium Amount',
            calculationError: 'Calculation Error',
            tryAgain: 'Please check your input and try again',
            noResult: 'No calculation result yet',
            fillFormAndCalculate: 'Please fill the form and click calculate',
            saveQuote: 'Save Quote',
            ageRequired: 'Please enter age',
            ageMinError: 'Age cannot be less than minimum limit',
            ageMaxError: 'Age cannot exceed maximum limit',
            sexRequired: 'Please select gender',
            insuredFeeRequired: 'Please enter insured amount',
            insuredFeeMinError: 'Insured amount must be greater than 0',
            paymentModeRequired: 'Please select payment mode',
            error: 'Operation failed, please try again',
            selectProduct: 'Select Product',
            searchOrSelectProduct: 'Search or select product',
            selectProductRequired: 'Please select a product',
            selectProductType: 'Select product type',
            productTypeOptions: {
                life: 'Life Insurance'
            },
            company: 'Insurance Company',
            selectCompany: 'Select Insurance Company',
            allCompanies: 'All Companies'
        }
    },
    'zh-HK': {
        premiumCalculator: {
            title: '保費試算',
            description: '快速計算產品保費，幫助您選擇最合適的保險計劃。輸入您的基本信息即可獲得精準保費預估。',
            searchPlaceholder: '搜索產品名稱',
            reset: '重置',
            productCount: '產品總數',
            allTypes: '全部類型',
            popularProducts: '熱門產品',
            productCountDesc: '當前可計算的產品總數',
            avgPremium: '平均保費',
            avgPremiumDesc: '所有產品的平均保費',
            maxPremium: '最高保費',
            maxPremiumDesc: '最高保費產品',
            minPremium: '最低保費',
            minPremiumDesc: '最低保費產品',
            productList: '產品列表',
            productName: '產品名稱',
            productType: '產品類型',
            ageRange: '年齡範圍',
            region: '地區',
            selectRegion: '選擇地區',
            allRegions: '全部地區',
            action: '操作',
            total: '共計',
            items: '項',
            calculate: '計算保費',
            years: '歲',
            to: '至',
            calculation: '保費計算',
            age: '年齡',
            sex: '性別',
            sexOptions: {
                male: '男',
                female: '女'
            },
            insuredFee: '保額',
            noInsuredFee: '部分產品不需要輸入保額',
            paymentMode: '繳費方式',
            paymentModes: {
                annual: '年繳',
                semiAnnual: '半年繳',
                quarterly: '季繳',
                monthly: '月繳'
            },
            premium: '保費金額',
            calculationError: '計算出錯',
            tryAgain: '請檢查輸入並重試',
            noResult: '暫無計算結果',
            fillFormAndCalculate: '請填寫表單並點擊計算',
            saveQuote: '保存報價',
            ageRequired: '請輸入年齡',
            ageMinError: '年齡不能小於最小限制',
            ageMaxError: '年齡不能大於最大限制',
            sexRequired: '請選擇性別',
            insuredFeeRequired: '請輸入保額',
            insuredFeeMinError: '保額必須大於0',
            paymentModeRequired: '請選擇繳費方式',
            error: '操作失敗，請重試',
            selectProduct: '選擇產品',
            searchOrSelectProduct: '搜索或選擇產品',
            selectProductRequired: '請選擇產品',
            selectProductType: '選擇產品類型',
            productTypeOptions: {
                life: '人壽險'
            },
            company: '保險公司',
            selectCompany: '選擇保險公司',
            allCompanies: '全部公司'
        }
    }
}