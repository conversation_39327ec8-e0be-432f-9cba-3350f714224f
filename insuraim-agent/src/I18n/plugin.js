import i18n, { t, getCurrentLocale, setLocale } from './index';

/**
 * 获取支持的语言列表
 * @returns {Array} 支持的语言代码数组
 */
export const getAvailableLocales = () => {
    return Object.keys(i18n.global.messages.value);
};

/**
 * 获取语言名称
 * @param {string} locale 语言代码
 * @returns {string} 语言名称
 */
export const getLanguageName = (locale) => {
    const langNames = {
        'zh-CN': '简体中文',
        'en-US': 'English',
        'zh-HK': '繁體中文'
        // 可以添加更多语言
    };
    return langNames[locale] || locale;
};

/**
 * 获取所有语言名称列表（用于选择器）
 * @returns {Array} 语言选项数组，包含value和label
 */
export const getLanguageOptions = () => {
    return getAvailableLocales().map(locale => ({
        value: locale,
        label: getLanguageName(locale)
    }));
};

/**
 * 格式化翻译带参数的文本
 * @param {string} key 翻译key
 * @param {Object} params 参数对象
 * @returns {string} 格式化后的文本
 */
export const tWithParams = (key, params) => {
    return t(key, params);
};

/**
 * 根据数量选择不同的翻译文本（复数形式）
 * @param {string} key 翻译key
 * @param {number} count 数量
 * @param {Object} params 其他参数
 * @returns {string} 根据数量选择的翻译文本
 */
export const tPlural = (key, count, params = {}) => {
    return t(key, { count, ...params });
};

/**
 * 在不同语言之间切换
 * @param {string} targetLocale 目标语言代码
 */
export const toggleLanguage = (targetLocale) => {
    const currentLocale = getCurrentLocale();
    const availableLocales = getAvailableLocales();

    // 如果指定了目标语言且可用，则切换到该语言
    if (targetLocale && availableLocales.includes(targetLocale)) {
        setLocale(targetLocale);
        return;
    }

    // 否则轮换语言
    const currentIndex = availableLocales.indexOf(currentLocale);
    const nextIndex = (currentIndex + 1) % availableLocales.length;
    setLocale(availableLocales[nextIndex]);
};

/**
 * Vue国际化插件
 * 使app.use(i18nPlugin)能够全局注册i18n实例
 * 并且为Vue实例注入$t和$i18n全局属性
 */
export const i18nPlugin = {
    install: (app) => {
        // 注册i18n实例
        app.use(i18n);

        // 全局属性，使模板中可以直接使用$t
        app.config.globalProperties.$t = t;

        // 全局属性，格式化带参数的翻译
        app.config.globalProperties.$tp = tWithParams;

        // 全局属性，复数形式的翻译
        app.config.globalProperties.$tPlural = tPlural;

        // 全局属性，使模板中可以直接使用$i18n
        app.config.globalProperties.$i18n = {
            locale: getCurrentLocale(),
            availableLocales: getAvailableLocales(),
            languageOptions: getLanguageOptions(),
            t,
            setLocale,
            getLanguageName,
            toggleLanguage
        };

        // 全局指令 v-t
        app.directive('t', {
            mounted(el, binding) {
                // 简单的文本翻译指令
                el.textContent = t(binding.value);
            },
            updated(el, binding) {
                el.textContent = t(binding.value);
            }
        });

        // 全局指令 v-t-html - 当需要翻译HTML内容时使用（谨慎使用，可能有XSS风险）
        app.directive('t-html', {
            mounted(el, binding) {
                el.innerHTML = t(binding.value);
            },
            updated(el, binding) {
                el.innerHTML = t(binding.value);
            }
        });

        // 全局混入，使所有组件都能方便地访问国际化功能
        app.mixin({
            computed: {
                // 当前语言
                currentLanguage() {
                    return getCurrentLocale();
                },
                // 可用语言列表
                availableLanguages() {
                    return getAvailableLocales();
                },
                // 语言选项（用于下拉菜单等）
                languageOptions() {
                    return getLanguageOptions();
                }
            },
            methods: {
                // 切换语言
                toggleLanguage(locale) {
                    return toggleLanguage(locale);
                },
                // 获取语言名称
                getLanguageName(locale) {
                    return getLanguageName(locale);
                }
            }
        });
    }
};

export default i18nPlugin; 