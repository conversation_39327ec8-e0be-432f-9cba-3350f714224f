import request from '../utils/request';

// 创建会话
export const createSession = (data) => {
    return request.post('/chat/session/create?modal=normal', data);
}

// 处理文件嵌入
export const createSessionWithPdf = (sessionId, proposalId) => {
    return request.post('/chat/assistant/create', {
        sessionId,
        proposalId
    });
}

// 处理用户查询 - 支持SSE流式传输
export const processQuery = (sessionId, query, onToken, onComplete, onError) => {
    return new Promise((resolve, reject) => {
        // 构建SSE URL
        let baseUrl = "https://agent.insuraim.com/prod-api/api";
        if (import.meta.env.MODE === 'development') {
            baseUrl = request.getBaseUrl();
        }
        const url = new URL(`${baseUrl}/chat/completion`);
        url.searchParams.append('sessionId', sessionId);
        url.searchParams.append('query', query);

        // 创建EventSource
        const eventSource = new EventSource(url.toString());

        let fullResponse = '';

        // 监听消息事件
        eventSource.onmessage = (event) => {
            const tokenData = event.data;
            if (tokenData && tokenData !== 'Stream finished') {
                fullResponse += tokenData;
                // 调用流式回调函数
                if (onToken && typeof onToken === 'function') {
                    onToken(tokenData);
                }
            }
        };

        // 监听完成事件
        eventSource.addEventListener('COMPLETE', (event) => {
            eventSource.close();
            if (onComplete && typeof onComplete === 'function') {
                onComplete(fullResponse);
            }
            resolve(fullResponse);
        });

        // 监听错误事件
        eventSource.addEventListener('ERROR', (event) => {
            eventSource.close();
            const errorMessage = event.data || '流式传输出现错误';
            if (onError && typeof onError === 'function') {
                onError(errorMessage);
            }
            reject(new Error(errorMessage));
        });

        // 处理连接错误
        eventSource.onerror = (event) => {
            eventSource.close();
            const errorMessage = 'SSE连接失败';
            if (onError && typeof onError === 'function') {
                onError(errorMessage);
            }
            reject(new Error(errorMessage));
        };

        // 设置超时处理
        setTimeout(() => {
            if (eventSource.readyState !== EventSource.CLOSED) {
                eventSource.close();
                const timeoutMessage = '请求超时';
                if (onError && typeof onError === 'function') {
                    onError(timeoutMessage);
                }
                reject(new Error(timeoutMessage));
            }
        }, 60000); // 60秒超时
    });
};

// 清除会话
export const clearSession = (sessionId) => {
    return request.post('/chat/session/clear', {
        sessionId
    });
}
