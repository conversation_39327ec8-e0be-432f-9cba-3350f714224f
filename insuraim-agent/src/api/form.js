import request from '../utils/request';

export const formAPI = {
    /**
 * @description  创建表单邀请链接
 * @param {int} policyId 保单id
 * @returns {string} 邀请链接
 */

    createFormInvite: (policyId) => {
        return request.post('/form/invite/create?policyId=' + policyId);
    },

    /**
     * @description  提交表单邀请链接
     * @param {string} inviteCode 邀请码
     * @param {object} data 表单数据
     * @returns {string} 提交结果
     */
    submitFormInvite: (inviteCode, data) => {
        return request.post('/form/invite/submit/' + inviteCode, data);
    },

    /**
     * @description  验证表单邀请链接
     * @param {string} inviteCode 邀请码
     * @returns {string} 验证结果
     */
    validateFormInvite: (inviteCode) => {
        return request.get('/form/invite/validate/' + inviteCode);
    },

    /**
     * @description  上传表单附件
     * 
     * @param {string} inviteCode 邀请码
     * @param {string} data.status 表单状态 写死为APPOINTMENT
     * @param {string} data.files 表单附件列表2
     */
    submitFormAttachment: (data, inviteCode) => {
        const service = request.getService();
        return service.post('/form/invite/submit-attachment/' + inviteCode, data, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });
    },

    /**
     * @description  获取表单邀请链接
     * @param {string} policyId 保单id
     * @returns {string} 邀请链接
     */
    getFormInvite: (policyId) => {
        return request.get('/form/invite/get/' + policyId);
    }
}