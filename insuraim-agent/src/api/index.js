import request from "../utils/request";

// 用户相关接口
export const userAPI = {
  // 用户登录
  login: (data) => {
    return request.post("/front/user/login", data);
  },

  // 用户登出
  logout: () => {
    return request.post("/front/user/logout");
  },

  // 获取用户信息
  getUserInfo: () => {
    return request.get("/front/user/info");
  },

  // 修改用户信息
  updateUserInfo: (data) => {
    return request.put("/front/user/info", data);
  },
};

// 个人云盘相关接口
export const personalCloudAPI = {
  // 获取用户云盘基本信息
  getCloudInfo: () => {
    return request.get("/front/personal-cloud/info");
  },

  // 获取文件夹内容（文件夹和文件）
  getFolderContent: (folderId) => {
    let params = {};
    if (folderId !== undefined && folderId !== null) {
      params.folderId = folderId;
    } else {
      params.folderId = 0;
    }
    return request.get("/front/personal-cloud/folder-content", params);
  },

  // 创建文件夹
  createFolder: (folderData) => {
    return request.post("/front/personal-cloud/folder", folderData);
  },

  // 删除文件夹
  deleteFolder: (folderId) => {
    return request.del(`/front/personal-cloud/folder/${folderId}`);
  },

  // 上传文件
  uploadFile: (file, folderId) => {
    const formData = new FormData();
    formData.append("file", file);
    if (folderId !== undefined && folderId !== null) {
      formData.append("folderId", folderId);
    }

    // 直接使用 axios 实例，确保 Content-Type 正确设置
    const service = request.getService();
    const token = localStorage.getItem("token");

    return service.post("/front/personal-cloud/file/upload", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
        Authorization: token ? `Bearer ${token}` : "",
      },
    });
  },

  // 删除文件
  deleteFile: (fileId) => {
    return request.del(`/front/personal-cloud/file/${fileId}`);
  },

  // 获取文件下载URL
  getFileDownloadUrl: (fileId) => {
    return request.get(`/front/personal-cloud/file/download/${fileId}`);
  },
};

// 产品相关接口
export const productAPI = {
  /**
   * @description 获取计划书链接
   * @param data.productName 产品名称
   * @param data.age 年龄
   */
  getPlanBookLink: (data) => {
    return request.get("/front/interest/productPlanLink", data);
  },

  /**
   * @description 获取明星产品
   */
  getProductRealizationStar: () => {
    return request.get(
      "/front/china-life-product-list/getProductRealizationStar"
    );
  },

  /**
   * @description 获取中国人寿产品分类列表
   */
  getProductCategoryList: () => {
    return request.get("/front/product-category/list");
  },

  // 获取产品列表
  getProductList: (params) => {
    return request.get("/common/ins-product/page", params);
  },

  // 获取产品详情
  getProductDetail: (id) => {
    return request.get(`/common/ins-product/${id}`);
  },

  // 获取lifebee产品列表（分页）
  getLifeBeeProductPage: (params) => {
    return request.get("/common/product/page", params);
  },

  // 获取lifebee产品详情
  getLifeBeeProductDetail: (code) => {
    return request.get(`/common/product/${code}`);
  },

  // 获取可生成计划书产品配置列表
  getPlanBookList: () => {
    return request.get("/common/ins-product/plan-book-list");
  },

  // 分页获取可以计算IRR的产品
  getIrrProductList: (data) => {
    return request.post("/front/interest/productCalDisplayV2", data);
  },

  // 动态计算IRR
  calculateProfitDynamic: (data) => {
    return request.post("/front/interest/calculateProfitV2", data);
    // return request.get("/front/interest/calculateProfitDynamic", params);
  },

  /**
   * 获取中国人寿产品列表
   * @param params.pageNum 页码
   * @param params.pageSize 每页条数
   * @param params.mainType 产品类型
   * @param params.region 地区
   * @param params.search 产品搜索关键字
   */
  getChinaLifeProductPage: (params) => {
    return request.get("/front/china-life-product-list/getBasicInfo", params);
  },

  /**
   * 根据ID获取产品小册子
   */
  getChinaLifeProductBrochureById: (id) => {
    return request.get("/front/china-life-product-list/getDownloadBrochure", {
      productId: id,
    });
  },

  /**
   * 根据ID获取产品代码列表
   */
  getChinaLifeProductCodeList: (id) => {
    return request.get("/front/china-life-product-list/getProductCode", {
      productId: id,
    });
  },

  /**
   * 根据ID获取IRR和分红率
   * @param productId 产品ID
   */
  getChinaLifeProductIRRAndDividend: (productId) => {
    return request.get("/front/china-life-product-list/getIRRAndDividend", {
      productId,
    });
  },

  /**
   * 根据ID计算产品IRR和分红率
   * @param data.page 页码
   * @param data.pageSize 每页条数
   * @param data.id 产品ID
   * @param data.age 年龄
   * @param data.insuredFee 保费
   * @param data.paymentMode 缴费方式  1是年缴,2是半年缴,,3季缴,4月缴
   */
  getChinaLifeProductCalculationFactors: (data) => {
    return request.get(
      "/front/china-life-product-list/getCalculationFactors",
      data
    );
  },
  /**
   * 根据ID与持有年限计算单利和复利
   * @param data.productId 产品ID
   * @param data.years 持有年限
   */
  getChinaLifeProductSingleAndDoubleInterest: (data) => {
    return request.get(
      "/front/china-life-product-list/getSingleAndDoubleInterest",
      data
    );
  },

  /**
   * 获取可计算的产品列表
   * @param data.search 产品搜索关键子
   * @param data.type 产品类型
   */
  getChinaLifeProductCalculationProducts: (data) => {
    return request.get(
      "/front/china-life-product-list/displayCalculationProducts",
      data
    );
  },
  /**
   * @description  获取可计算的产品类型列表
   */
  getChinaLifeProductCalculationType: (data) => {
    return request.get("/front/product-category/list", data);
  },
  /**
   * @description 获取货币列表
   */
  getIRRCurrencyList: () => {
    return request.get("/front/interest/IRRCurrency");
  },
  /**
   * 获取产品分红实现率
   * @param data.pageNum 页码
   * @param data.pageSize 每页条数
   * @param data.search 产品名称
   */
  getChinaLifeProductRealizationDetails: (data) => {
    return request.get(
      "/front/china-life-product-list/getProductRealizationDetails",
      data
    );
  },

  /**
   * 获取产品分红实现率名称
   * @param data.pageNum 页码
   * @param data.pageSize 每页条数
   */
  getChinaLifeProductRealizationName: (data) => {
    return request.get(
      "/front/china-life-product-list/getProductRealizationName",
      data
    );
  },

  // ---------------- 产品收藏相关接口 ----------------
  // 用户收藏产品
  favoriteProduct: (productId) => {
    return request.post(`/common/ins-product/favorite/${productId}`);
  },

  // 用户取消收藏产品
  unfavoriteProduct: (productId) => {
    return request.post(`/common/ins-product/unfavorite/${productId}`);
  },

  // 检查用户是否已收藏产品
  checkProductFavorited: (productId) => {
    return request.get(`/common/ins-product/favorite/check/${productId}`);
  },

  // 获取用户已收藏的产品列表
  getFavoriteProducts: () => {
    return request.get("/common/ins-product/favorites");
  },
};

// 保险公司相关接口
export const companyAPI = {
  // 获取保险公司列表
  getCompanyList: (params) => {
    return request.get("/common/company/page", params);
  },

  /**
   * 获取保险公司列表
   * @returns 公司列表
   */
  getCompanyPage: () => {
    return request.get("/common/company/list");
  },

  // 获取保险公司详情
  getCompanyDetail: (id) => {
    return request.get(`/common/company/${id}`);
  },
};

// 客户相关接口
export const customerAPI = {
  // 获取客户列表
  getCustomerList: (params) => {
    return request.get("/front/customer/page", params);
  },

  // 获取客户详情
  getCustomerDetail: (id) => {
    return request.get(`/front/customer/${id}`);
  },
};

// 资讯相关接口
export const newsAPI = {
  // 获取资讯列表
  getNewsList: (params) => {
    return request.get("/common/news/list", params);
  },

  // 获取资讯详情
  getNewsDetail: (id) => {
    return request.get(`/common/news/${id}`);
  },

  // 获取资讯分类
  getNewsCategories: () => {
    return request.get("/common/news/categories");
  },

  // 获取相关资讯
  getRelatedNews: (id) => {
    return request.get(`/common/news/related/${id}`);
  },
};

// 文件夹相关接口
export const folderAPI = {
  // 创建文件夹
  createFolder: (data) => {
    return request.post("/front/folder", data);
  },

  // 获取文件夹信息
  getFolder: (id) => {
    return request.get(`/front/folder/${id}`);
  },

  // 获取文件夹内容（包括子文件夹和文件）
  getFolderContent: (id, fileType) => {
    let url = `/front/folder/content/${id || "0"}`;
    if (fileType) {
      url += `?fileType=${fileType}`;
    }
    return request.get(url);
  },

  // 获取文件夹树形结构
  getFolderTree: (parentId) => {
    let url = "/front/folder/tree";
    if (parentId) {
      url += `?parentId=${parentId}`;
    }
    return request.get(url);
  },

  // 获取文件夹列表
  listFolders: (parentId) => {
    let params = {};
    if (parentId !== undefined) params.parentId = parentId;
    return request.get("/front/folder/list", params);
  },

  // 分页查询文件夹
  pageFolders: (params) => {
    return request.get("/front/folder/page", params);
  },

  // 更新文件夹
  updateFolder: (data) => {
    return request.put("/front/folder", data);
  },

  // 删除文件夹
  deleteFolder: (id) => {
    return request.del(`/front/folder/${id}`);
  },
};

// 文件相关接口
export const fileAPI = {
  // 上传文件
  uploadFile: (file, folderId, uploaderId) => {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("folderId", folderId);
    formData.append("uploaderId", uploaderId);

    console.log("发送上传请求，folderId:", folderId, "uploaderId:", uploaderId);

    // 直接使用 axios 实例，确保 Content-Type 正确设置
    const service = request.getService();
    const token = localStorage.getItem("token");

    return service.post("/front/file/upload", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
        Authorization: token ? `Bearer ${token}` : "",
      },
    });
  },

  // 获取文件信息
  getFile: (id) => {
    return request.get(`/front/file/${id}`);
  },

  // 获取文件列表
  listFiles: (folderId, fileType) => {
    let params = { folderId };
    if (fileType) params.fileType = fileType;
    return request.get("/front/file/list", params);
  },

  // 分页查询文件
  pageFiles: (params) => {
    return request.get("/front/file/page", params);
  },

  // 更新文件
  updateFile: (data) => {
    return request.put("/front/file", data);
  },

  // 删除文件
  deleteFile: (id) => {
    return request.del(`/front/file/${id}`);
  },
};

// 计划书相关接口
export const planAPI = {
  // 生成计划书
  generatePlan: (data) => {
    return request.post("/front/proposal/generate", data);
  },

  // 获取计划书列表
  getPlanList: (params) => {
    return request.get("/front/proposal/page", params);
  },

  // 获取计划书详情
  getPlanDetail: (id) => {
    return request.get(`/front/proposal/${id}`);
  },

  // 轮询计划书生成状态
  getPlanStatus: (id) => {
    return request.get(`/front/proposal/status/${id}`);
  },

  // 预览计划书 PDF
  previewPdf: (id) => {
    return `${request.getBaseUrl()}/proposal-file/pdf/preview/${id}`;
  },

  // 下载计划书 PDF
  downloadPdf: (id) => {
    return `${request.getBaseUrl()}/proposal-file/pdf/download/${id}`;
  },

  // 获取可收益演算的产品列表
  getBenefitCalculateList: () => {
    return request.get("/common/ins-product/benefit-calculate-list");
  },

  // 生成计划书演算
  calculateForExcel: (data) => {
    return request.post("/front/proposal/calculate/excel", data);
  },
};

// 日历相关接口
export const calendarAPI = {
  // 查询日程列表
  getCalendarList: () => {
    return request.get("front/calendar/list");
  },

  // 获取日程详情
  getCalendarById: (id) => {
    return request.get(`front/calendar/${id}`);
  },

  // 创建日程
  createCalendar: (data) => {
    return request.post("front/calendar/create", data);
  },

  // 更新日程
  updateCalendar: (data) => {
    return request.put("front/calendar/update", data);
  },

  // 删除日程
  deleteCalendar: (id) => {
    return request.del(`front/calendar/${id}`);
  },

  // 更新日程状态
  updateCalendarStatus: (id, status) => {
    return request.put(`front/calendar/${id}/status?status=${status}`);
  },
};

export const messageAPI = {
  // 获取未读消息数量
  getUnreadMessageCount: () => {
    return request.get("/common/message/poll");
  },

  // 更新消息为已读状态
  updateMessageStatus: (id) => {
    return request.post(`/common/message/read`, { id });
  },
};

// 考试相关接口
export const examAPI = {
  // 获取考试类目列表
  getCategoryList: () => {
    return request.get("/front/exam/category");
  },

  // 获取考试题目列表
  getQuestionList: (categoryId) => {
    return request.get("/front/exam/question", { categoryId: categoryId });
  },
};

// 问卷相关接口
export const surveyAPI = {
  // 获取问卷列表
  getSurveyList: (params) => {
    return request.get("/front/survey/list", params);
  },

  // 获取问卷详情
  getSurveyDetail: (id) => {
    return request.get(`/front/survey/${id}`);
  },

  // 提交问卷答案
  submitSurvey: (data) => {
    return request.post("/front/survey/submit", data);
  },

  // 保存问卷进度
  saveSurveyProgress: (data) => {
    return request.post("/front/survey/save-progress", data);
  },

  // 获取我的问卷历史
  getMySurveyHistory: (params) => {
    return request.get("/front/survey/history", params);
  },
};

// 知识库相关接口
export const knowledgeBaseAPI = {
  // 获取知识库保险公司列表
  getCompanyList: () => {
    return request.get("/front/knowledge-base/company-list");
  },

  // 获取知识库专题列表（根据保险公司ID）
  getCategoryListByCompanyId: (companyId) => {
    return request.get("/front/knowledge-base/company-categories", {
      companyId,
    });
  },

  // 获取完整的分类树
  getCategoryTree: () => {
    console.log("调用完整分类树API");
    return request.get("/front/knowledge-base/category-tree");
  },

  // 根据公司ID获取分类树
  getCategoryTreeByCompanyId: (companyId) => {
    console.log("调用公司分类树API, 公司ID:", companyId);
    return request.get(
      `/front/knowledge-base/category-tree/company/${companyId}`
    );
  },

  // 根据分类ID获取分类树
  getCategoryTreeById: (categoryId) => {
    console.log("调用分类树子树API, 分类ID:", categoryId);
    return request.get(`/front/knowledge-base/category-tree/${categoryId}`);
  },

  // 根据分类ID获取知识库信息列表
  getLoreList: (categoryId) => {
    console.log("调用文章列表API, 分类ID:", categoryId);
    return request.get("/front/knowledge-base/lore-list", { categoryId });
  },

  // 获取知识库详情
  getLoreDetail: (id) => {
    return request.get("/front/knowledge-base/lore-detail", { id });
  },
};

export const appointmentAPI = {
  // 创建预约
  createAppointment: (data) => {
    return request.post("/appoint/create", data);
  },
};

// 保单订单相关接口
export const policyOrderAPI = {
  // 分页获取订单列表
  page: (params) => {
    return request.get("/policy-order/page", params);
  },

  // 根据订单ID查询所有信息
  allDetailByOrderId: (id) => {
    return request.get(`/policy-order/detail`, { orderId: id });
  },
  /**
   * @param data.policyId 保单id
   */
  prePolicyStatus: (data) => {
    return request.post("policy/withdraw-status-change", data);
  },
  /**
   * @param data.policyId 保单id
   */
  nextPolicyStatus: (data) => {
    return request.post("/policy/update-status", data);
  },
  /**
   * @data type:formData
   * @param data.file 文件
   * @param data.policyId 保单id
   * @param data.status 保单状态
   */
  uploadAttachment: (data) => {
    const service = request.getService();
    return service.post("/policy/upload-attachment", data, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },

  // 修改保单基本信息（预约信息）
  updatePolicyBasicInfo: (data) => {
    return request.post("/policy/update-basic-info", data);
  },

  // 修改受益人信息
  updatePolicyBeneficiaryInfo: (data) => {
    return request.post("/policy/update-beneficiary-info", data);
  },

  // 删除受益人信息
  deletePolicyBeneficiaryInfo: (id) => {
    return request.post(`/policy/delete-beneficiary-info?beneficiaryId=${id}`);
  },

  // 添加受益人信息
  addPolicyBeneficiaryInfo: (data) => {
    return request.post("/policy/add-beneficiary-info", data);
  },

  // 修改投保人信息
  updatePolicyPolicyholderInfo: (data) => {
    return request.post("/policy/update-policyholder-info", data);
  },
  // 修改被保人信息
  updatePolicyInsuredInfo: (data) => {
    return request.post("/policy/update-insured-info", data);
  },

  // 删除订单
  deletePolicyOrder: (policyId) => {
    return request.post(`/policy-order/delete?policyId=${policyId}`);
  },
};
