import request from '../utils/request';

// 轮询未推送消息
export const pollMessage = () => {
  return request.get('/common/message/poll');
};

// 更新消息为已推送
export const pushMessage = (messageId) => {
  return request.post('/common/message/push?messageId=' + messageId);
};

// 获取所有消息
export const getAllMessages = () => {
  return request.get('/common/message/all');
};

// 标记消息为已读
export const readMessage = (messageId) => {
  return request.post('/common/message/read?messageId=' + messageId);
};

// 获取所有未读消息
export const getAllUnreadMessages = () => {
  return request.get('/common/message/unread');
};

// 分页获取消息
export const getPagedMessages = (page = 1, pageSize = 10) => {
  return request.get('/common/message/paged', { page, pageSize });
};

export default {
  pollMessage,
  pushMessage,
  getAllMessages,
  readMessage,
  getPagedMessages
}; 