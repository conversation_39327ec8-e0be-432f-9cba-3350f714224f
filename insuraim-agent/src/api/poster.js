import request from '@/utils/request';


/**
 * 获取可生成的产品列表
 * @param {Object} params.search 搜索关键字
 * @param {Object} params.pageNum 页码
 * @param {Object} params.pageSize 每页条数
 */
export const getInsurancePoster = (params) => {
    return request.get('/front/china-life-product-list/getInsurancePoster', params)
};

/**
 * 获取保险海报数据详情
 * @param {Object} id 产品ID
 * @returns {Promise} 返回Promise对象
 */
export const getInsurancePosterDetails = (id) => {
    return request.get(`/front/china-life-product-list/getInsurancePosterDetails`, { id })
};

/**
 * 生成AI海报图像
 * @param {Object} data 海报生成数据
 * @param {String} data.styleId 海报风格ID
 * @param {String} data.prompt 生成提示词
 * @param {String} data.negative_prompt 负面提示词
 * @param {Object} data.productData 产品数据
 * @returns {Promise} 返回Promise对象
 */
export const generateAIPoster = (data) => {
    const options = {
        method: 'POST',
        headers: {
            Authorization: 'Bearer sk-qjypedbqdcauirctshsyvpucfaxalvolhkcabwqeiusofimn',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            model: "Kwai-Kolors/Kolors",
            prompt: data.prompt,
            negative_prompt: data.negative_prompt || "",
            image_size: "1024x1920",
            batch_size: 1,
            seed: Math.floor(Math.random() * 4999999999),
            num_inference_steps: 20,
            guidance_scale: 7.5
        })
    };

    // 返回promise对象
    return fetch('https://api.siliconflow.cn/v1/images/generations', options)
        .then(response => response.json())
        .catch(err => {
            console.error('生成海报失败:', err);
            throw err;
        });
};