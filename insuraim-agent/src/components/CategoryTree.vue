<template>
  <div class="category-tree">
    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center p-4">
      <div class="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-blue-500 mr-2"></div>
      <span class="text-gray-500 text-sm">加载中...</span>
    </div>
    
    <!-- 错误状态 -->
    <div v-else-if="error" class="p-4 text-red-500 text-sm">
      <div class="flex items-center">
        <span class="mr-2">加载失败</span>
        <button @click="loadCategoryTree" class="text-blue-500 hover:underline text-xs">
          重试
        </button>
      </div>
      <div class="mt-1 text-xs">{{ error }}</div>
    </div>
    
    <!-- 树形结构 -->
    <div v-else class="category-tree-container">
      <!-- 使用说明 -->
      <div class="p-2 mb-2 bg-gray-50 rounded text-xs text-gray-600 border border-gray-100">
        <div class="flex items-start">
          <Icon icon="mdi:folder-open" class="text-amber-500 mr-1 mt-0.5 flex-shrink-0" />
          <span>文件夹：包含子分类，点击展开</span>
        </div>
        <div class="flex items-start mt-1">
          <Icon icon="mdi:folder" class="text-amber-500 mr-1 mt-0.5 flex-shrink-0" />
          <span>末级分类：可查看文章列表</span>
        </div>
      </div>
      
      <div class="category-tree-nodes">
        <template v-for="(category, index) in categoryTree" :key="category.id">
          <CategoryTreeNode 
            :category="category" 
            :level="0"
            :active-id="activeId"
            @select="handleCategorySelect"
          />
        </template>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { knowledgeBaseAPI } from '@/api';
import { Icon } from '@iconify/vue';
import CategoryTreeNode from './CategoryTreeNode.vue';

const props = defineProps({
  companyId: {
    type: [String, Number],
    default: null
  },
  activeId: {
    type: [String, Number],
    default: null
  }
});

const emit = defineEmits(['select']);

const categoryTree = ref([]);
const loading = ref(false);
const error = ref('');

// 加载分类树数据
const loadCategoryTree = async () => {
  loading.value = true;
  error.value = '';
  
  try {
    let response;
    
    if (props.companyId) {
      // 如果提供了公司ID，则获取该公司的分类树
      console.log('正在加载公司分类树:', props.companyId);
      response = await knowledgeBaseAPI.getCategoryTreeByCompanyId(props.companyId);
    } else {
      // 否则获取完整的分类树
      console.log('正在加载完整分类树');
      response = await knowledgeBaseAPI.getCategoryTree();
    }
    
    console.log('分类树加载成功:', response);
    categoryTree.value = response || [];
  } catch (err) {
    console.error('获取分类树失败:', err);
    error.value = '获取分类树失败，请稍后重试';
  } finally {
    loading.value = false;
  }
};

// 处理分类选择
const handleCategorySelect = (category) => {
  console.log('CategoryTree组件接收到选择事件:', category.id, category.name);
  emit('select', category);
};

// 监听公司ID变化，重新加载分类树
watch(() => props.companyId, () => {
  loadCategoryTree();
});

// 组件挂载时加载分类树
onMounted(() => {
  loadCategoryTree();
});
</script>

<style scoped>
.category-tree {
  width: 100%;
  overflow-y: auto;
  max-height: calc(100vh - 200px);
}

.category-tree-container {
  padding: 0.5rem;
}

.category-tree-nodes {
  width: 100%;
}
</style> 