<template>
  <div class="category-tree-node" :class="{ 'has-children': hasChildren }">
    <!-- 分类项 -->
    <div class="category-item" :class="{
      'active': isActive,
      'is-leaf': isLeaf,
      'is-clickable': isLeaf,
      'is-folder': hasChildren
    }" :style="{ paddingLeft: `${level * 16 + 8}px` }" @click="handleClick">
      <!-- 展开/折叠图标 -->
      <div v-if="hasChildren" class="expander" @click.stop="toggleExpand">
        <Icon :icon="expanded ? 'mdi:chevron-down' : 'mdi:chevron-right'" />
      </div>
      <div v-else class="expander-placeholder"></div>

      <!-- 文件夹/文件图标 -->
      <div class="icon">
        <Icon v-if="hasChildren" :icon="expanded ? 'mdi:folder-open' : 'mdi:folder'" class="text-amber-500" />
        <Icon v-else :icon="'mdi:folder'" class="text-amber-500" />
      </div>

      <!-- 分类名称 -->
      <div class="name">{{ category.name }}</div>

      <!-- 叶子节点右侧指示器 -->
      <div v-if="isLeaf" class="leaf-indicator">
        <Icon icon="mdi:arrow-right" class="h-4 w-4 text-blue-500" />
      </div>
    </div>

    <!-- 子分类列表 -->
    <div v-if="hasChildren && expanded" class="children-container">
      <template v-for="(child, index) in category.children" :key="child.id">
        <CategoryTreeNode :category="child" :level="level + 1" :active-id="activeId" @select="handleSelect" />
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, toRefs, watch } from 'vue';
import { Icon } from '@iconify/vue';

const props = defineProps({
  category: {
    type: Object,
    required: true
  },
  level: {
    type: Number,
    default: 0
  },
  activeId: {
    type: [String, Number],
    default: null
  }
});

const emit = defineEmits(['select']);

const { category } = toRefs(props);

// 展开状态
const expanded = ref(false);

// 是否有子分类
const hasChildren = computed(() => {
  return category.value.children && category.value.children.length > 0;
});

// 是否是叶子节点（没有子分类）
const isLeaf = computed(() => {
  return !hasChildren.value;
});

// 是否是当前激活的分类
const isActive = computed(() => {
  return props.activeId === category.value.id;
});

// 切换展开/折叠状态
const toggleExpand = () => {
  expanded.value = !expanded.value;
};

// 处理点击事件
const handleClick = () => {
  // 如果是叶子节点，则触发选择事件，查看该分类下的文章列表
  if (isLeaf.value) {
    console.log('选中叶子节点:', category.value.id, category.value.name);
    handleSelect(category.value);
  } else {
    // 如果有子节点，则切换展开状态
    console.log('切换文件夹展开状态:', category.value.id, category.value.name);
    toggleExpand();
  }
};

// 处理选择事件
const handleSelect = (selectedCategory) => {
  console.log('发送选择事件:', selectedCategory.id, selectedCategory.name);
  emit('select', selectedCategory);
};

// 如果该分类是激活状态，自动展开所有父级
watch(() => props.activeId, (newValue) => {
  if (hasChildren.value && category.value.children.some(child => child.id === newValue)) {
    expanded.value = true;
  }
});
</script>

<style scoped>
.category-tree-node {
  width: 100%;
}

.category-item {
  display: flex;
  align-items: center;
  padding: 8px;
  padding-right: 16px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;
}

.category-item:hover {
  background-color: rgba(59, 130, 246, 0.1);
}

.category-item.active {
  background-color: rgba(59, 130, 246, 0.15);
  font-weight: 500;
}

.category-item.is-folder {
  font-weight: 500;
  color: #4b5563;
}

.category-item.is-leaf {
  color: #374151;
}

.category-item.is-clickable {
  color: #1f2937;
}

.category-item.is-clickable:hover {
  color: #1d4ed8;
  background-color: rgba(59, 130, 246, 0.15);
}

.expander,
.expander-placeholder {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 4px;
}

.icon {
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.name {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 14px;
}

.leaf-indicator {
  opacity: 0;
  transition: opacity 0.2s;
}

.is-clickable:hover .leaf-indicator {
  opacity: 1;
}

.children-container {
  width: 100%;
}
</style>