<template>
    <div class="i-pic-preview" v-if="visible">
        <div class="preview-mask" @click="handleClose">
            <div class="preview-container" @click.stop>
                <!-- 图片预览区域 -->
                <!-- <div class="preview-content"> -->
                <img :src="src" :alt="title || '图片预览'" class="preview-image" ref="imageRef" :style="{
                    transform: `scale(${scale})`,
                    transition: 'transform 0.3s ease-in-out'
                }" />
                <!-- </div> -->

                <!-- 底部工具栏 -->
                <div class="preview-toolbar">
                    <!-- 默认工具栏 -->
                    <template v-if="defaultTools">
                        <div class="toolbar-default">
                            <button class="toolbar-btn" @click="handleDownload">
                                <Icon icon="material-symbols:download" class="btn-icon" />
                                <span>下载</span>
                            </button>
                            <!-- <button class="toolbar-btn" @click="handlePreviewData">
                                <Icon icon="material-symbols:copy" class="btn-icon" />
                                <span>元始数据</span>
                            </button> -->
                            <button class="toolbar-btn" @click="handleZoomIn" :disabled="scale >= maxScale">
                                <Icon icon="material-symbols:zoom-in" class="btn-icon" />
                                <span>放大</span>
                            </button>
                            <button class="toolbar-btn" @click="handleZoomOut" :disabled="scale <= minScale">
                                <Icon icon="material-symbols:zoom-out" class="btn-icon" />
                                <span>缩小</span>
                            </button>
                            <button class="toolbar-btn" @click="handleResetZoom" v-if="scale !== 1">
                                <Icon icon="material-symbols:fit-screen" class="btn-icon" />
                                <span>重置</span>
                            </button>
                        </div>
                    </template>

                    <!-- 自定义工具栏插槽 -->
                    <slot name="toolbar" v-if="toolbarSlot"></slot>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { Icon } from '@iconify/vue';
import { message } from 'ant-design-vue';

// 定义组件属性
const props = defineProps({
    src: {
        type: String,
        required: true,
    },
    title: {
        type: String,
        default: '',
    },
    description: {
        type: String,
        default: '',
    },
    toolbarSlot: {
        type: Boolean,
        default: false,
    },
    defaultTools: {
        type: Boolean,
        default: true,
    },
    visible: {
        type: Boolean,
        default: false,
    }
});

// 定义事件
const emit = defineEmits(['download', 'copy', 'update:visible', 'previewData']);

// 图片引用
const imageRef = ref(null);

// 缩放相关状态
const scale = ref(1);
const minScale = 0.5;
const maxScale = 3;
const scaleStep = 0.2;

// 处理关闭
const handleClose = () => {
    // 重置缩放
    scale.value = 1;
    emit('update:visible', false);
};
// const handlePreviewData = () => {
//     emit('previewData');
// };
// // 处理下载图片
const handleDownload = async () => {
    try {
        emit('download');
    } catch (error) {
        console.error('下载图片失败:', error);
        message.error('下载图片失败');
    }
};

// 处理放大图片
const handleZoomIn = () => {
    if (scale.value >= maxScale) return;
    scale.value = Math.min(maxScale, scale.value + scaleStep);
};

// 处理缩小图片
const handleZoomOut = () => {
    if (scale.value <= minScale) return;
    scale.value = Math.max(minScale, scale.value - scaleStep);
};

// 重置缩放
const handleResetZoom = () => {
    scale.value = 1;
};

// 监听键盘事件
const handleKeydown = (e) => {
    if (!props.visible) return;

    switch (e.key) {
        case '=':
        case '+':
            if (e.ctrlKey || e.metaKey) {
                e.preventDefault();
                handleZoomIn();
            }
            break;
        case '-':
            if (e.ctrlKey || e.metaKey) {
                e.preventDefault();
                handleZoomOut();
            }
            break;
        case '0':
            if (e.ctrlKey || e.metaKey) {
                e.preventDefault();
                handleResetZoom();
            }
            break;
        case 'Escape':
            handleClose();
            break;
    }
};

// 监听滚轮事件
const handleWheel = (e) => {
    if (!props.visible) return;
    if (e.ctrlKey || e.metaKey) {
        e.preventDefault();
        if (e.deltaY < 0) {
            handleZoomIn();
        } else {
            handleZoomOut();
        }
    }
};

// 组件挂载时添加事件监听
onMounted(() => {
    window.addEventListener('keydown', handleKeydown);
    window.addEventListener('wheel', handleWheel, { passive: false });
});

// 组件卸载时移除事件监听
onUnmounted(() => {
    window.removeEventListener('keydown', handleKeydown);
    window.removeEventListener('wheel', handleWheel);
});
</script>

<style scoped>
.i-pic-preview {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
}

.preview-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.75);
    backdrop-filter: blur(4px);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: zoom-out;
}

.preview-container {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    max-width: 90vw;
    max-height: 90vh;
    width: auto;
    background-color: transparent;
    display: flex;
    flex-direction: column;
    cursor: default;
    animation: zoom-in 0.2s ease-out;
    position: relative;
}

@keyframes zoom-in {
    from {
        opacity: 0;
        transform: scale(0.95);
    }

    to {
        opacity: 1;
        transform: scale(1);
    }
}

.preview-content {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
    user-select: none;
}

.preview-image {
    max-width: 100%;
    max-height: calc(90vh - 100px);
    object-fit: contain;
    border-radius: 8px;
    will-change: transform;
}

.preview-toolbar {
    position: fixed;
    bottom: 25px;
    left: 50%;
    transform: translateX(-50%);
    padding: 12px 20px;
    border-radius: 12px;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(8px);
    z-index: 1;
}

.toolbar-default {
    display: flex;
    justify-content: center;
    gap: 16px;
}

.toolbar-btn {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    border: none;
    background: #ffffff;
    color: #303133;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.2s;
    font-size: 14px;
}

.toolbar-btn:not(:disabled):hover {
    background: #f0f0f0;
    color: var(--primary);
    transform: translateY(-1px);
}

.toolbar-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-icon {
    margin-right: 6px;
    font-size: 20px;
}

/* 响应式样式 */
@media (max-width: 640px) {
    .preview-container {
        width: 95vw;
        margin: 0 16px;
    }

    .toolbar-default {
        flex-direction: row;
        flex-wrap: wrap;
        align-items: center;
    }

    .toolbar-btn {
        padding: 6px 12px;
        font-size: 12px;
    }

    .btn-icon {
        font-size: 16px;
    }
}
</style>