<template>
  <div class="language-switch-wrapper" 
       @mouseenter="handleMouseEnter"
       @mouseleave="handleMouseLeave">
    <slot>
      <!-- 默认图标 -->
      <div class="icon-wrapper" @click="toggleDropdown">
        <Icon icon="mdi:translate" class="icon" />
      </div>
    </slot>
    
    <!-- 语言切换下拉菜单 -->
    <div v-if="showDropdown" 
         class="language-dropdown"
         @mouseenter="cancelCloseTimer"
         @mouseleave="startCloseTimer">
      <div v-for="lang in availableLocales" :key="lang"
           class="language-item"
           :class="{ 'language-item-active': currentLocale === lang }"
           @click="switchLanguage(lang)">
        <div class="flex items-center">
          <span class="language-name">{{ getLanguageName(lang) }}</span>
          <Icon v-if="currentLocale === lang" icon="mdi:check" class="language-check" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { useI18n } from 'vue-i18n';
import { getLanguageName as getLocaleDisplayName } from '@/I18n/plugin';

// 获取国际化功能
const { locale, availableLocales, t } = useI18n();

// 当前语言
const currentLocale = computed(() => locale.value);

// 下拉菜单显示状态
const showDropdown = ref(false);

// 关闭定时器ID
let closeTimerId = null;

// 关闭延迟时间(毫秒)
const closeDelay = 250;

// 显示下拉菜单
const handleMouseEnter = () => {
  cancelCloseTimer();
  showDropdown.value = true;
};

// 延迟关闭下拉菜单
const handleMouseLeave = () => {
  startCloseTimer();
};

// 切换下拉菜单显示状态
const toggleDropdown = () => {
  showDropdown.value = !showDropdown.value;
  // 如果打开了菜单，添加点击外部区域关闭的事件监听
  if (showDropdown.value) {
    setTimeout(() => {
      document.addEventListener('click', handleOutsideClick);
    }, 0);
  }
};

// 启动关闭定时器
const startCloseTimer = () => {
  cancelCloseTimer();
  closeTimerId = setTimeout(() => {
    showDropdown.value = false;
  }, closeDelay);
};

// 取消关闭定时器
const cancelCloseTimer = () => {
  if (closeTimerId) {
    clearTimeout(closeTimerId);
    closeTimerId = null;
  }
};

// 处理点击外部区域
const handleOutsideClick = (e) => {
  const wrapper = document.querySelector('.language-switch-wrapper');
  if (wrapper && !wrapper.contains(e.target)) {
    showDropdown.value = false;
    document.removeEventListener('click', handleOutsideClick);
  }
};

// 获取语言名称
const getLanguageName = (lang) => {
  return getLocaleDisplayName(lang);
};

// 切换语言
const switchLanguage = (lang) => {
  if (lang !== locale.value) {
    // 导入setLocale函数
    import('@/I18n/index').then(module => {
      const { setLocale } = module;
      setLocale(lang);
    });
  }
  showDropdown.value = false;
  document.removeEventListener('click', handleOutsideClick);
};

// 组件卸载前清理
onBeforeUnmount(() => {
  cancelCloseTimer();
  document.removeEventListener('click', handleOutsideClick);
});
</script>

<style scoped>
.language-switch-wrapper {
  position: relative;
}

.icon-wrapper {
  box-sizing: border-box;
  position: relative;
  width: 30px;
  height: 30px;
  background: var(--bg-primary);
  border-radius: 4px;
  cursor: pointer;
}

.icon {
  position: absolute;
  width: 16px;
  height: 16px;
  left: 7px;
  top: 7px;
}

.language-dropdown {
  position: absolute;
  top: 36px;
  right: -5px;
  width: 120px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 50;
  padding: 8px 0;
  animation: dropdown-fade 0.2s ease;
}

.language-dropdown::before {
  content: '';
  position: absolute;
  top: -6px;
  right: 12px;
  width: 12px;
  height: 12px;
  background: white;
  transform: rotate(45deg);
  box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.03);
}

.language-dropdown::after {
  content: '';
  position: absolute;
  top: -20px;
  left: 0;
  right: 0;
  height: 20px;
  background: transparent;
}

.language-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.language-item:hover {
  background-color: var(--bg-secondary);
}

.language-item-active {
  background-color: var(--bg-secondary);
}

.language-name {
  font-size: 13px;
  color: #333;
}

.language-check {
  width: 16px;
  height: 16px;
  margin-left: 8px;
  color: #1890ff;
}

@keyframes dropdown-fade {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style> 