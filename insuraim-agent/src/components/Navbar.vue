<template>
  <div class="bg-secondary shadow-sm">
    <div class="mx-auto px-6 flex items-center justify-between h-16 container">
      <!-- 左侧Logo -->
      <div class="flex items-center">
        <img src="http://resouce.insuraim.com/Insuriam/icon/Cfo6zjv.webp" alt="Logo" class="h-10 w-10" />
        <span class="text-xl font-bold ml-2"><a href="/">Insuraim</a></span>
      </div>

      <!-- 中间导航菜单
      <div class="hidden md:flex items-center space-x-6">
        <div v-for="(item, index) in navItems" :key="index" 
          class="relative group">
          <router-link :to="item.path" 
            class="text-sm px-2 py-5 hover:text-blue-600 transition-colors duration-200"
            :class="[currentPath === item.path ? 'text-blue-600 font-medium' : 'text-gray-700']">
            {{ item.name }}
            <div v-if="currentPath === item.path" class="absolute bottom-0 left-0 w-full h-0.5 bg-blue-600"></div>
          </router-link>
        </div>
      </div> -->

      <!-- 右侧功能区 -->
      <div class="flex items-center">
        <button @click="toggleMenu" class="md:hidden p-2 hover:bg-gray-100 rounded-full">
          <Icon icon="mdi:menu" class="h-5 w-5 text-gray-700" />
        </button>

        <div class="hidden md:flex items-center">
          <!-- 图标组容器 -->
          <div class="icons-container">
            <!-- 主题切换图标 -->
            <ThemeToggle />

            <!-- 日历图标 -->
            <div class="icon-wrapper">
              <Icon icon="ic:outline-calendar-today" class="icon" />
            </div>

            <!-- 通知图标 -->
            <div class="icon-wrapper">
              <Icon icon="ic:outline-notifications" class="icon" />
              <!-- 通知红点 -->
              <div v-if="notificationCount > 0" class="notification-dot"></div>
            </div>

            <!-- 消息图标 -->
            <div class="icon-wrapper" @click="goToMessageCenter">
              <Icon icon="ic:outline-email" class="icon" />
              <!-- 消息红点 -->
              <div v-if="messageCount > 0" class="notification-dot"></div>
            </div>

            <!-- 语言切换图标 -->
            <LanguageSwitcher />

            <!-- 分隔线 -->
            <div class="divider"></div>
          </div>

          <!-- 用户信息区域 (Frame 427321250) -->
          <div class="user-info-container relative">
            <!-- 头像和用户名胶囊 (Frame 427321249) -->
            <div class="user-info-wrapper" @click="toggleDropdown">
              <!-- 头像和用户名容器 (Frame 427321248) -->
              <div class="avatar-name-container">
                <!-- 头像 (Ellipse 2733) -->
                <div class="avatar-container">
                  <img v-if="userAvatar" :src="userAvatar" alt="用户头像" class="h-full w-full object-cover" />
                  <img v-else src="https://wp-cdn.yukuii.top/v2/Cfo6zjv.png" alt="默认头像"
                    class="h-full w-full object-cover" />
                </div>

                <!-- 用户名和下拉箭头 (Frame 427321247) -->
                <div class="name-dropdown-container">
                  <!-- 用户名 -->
                  <span class="user-name">{{ userName }}</span>

                  <!-- 下拉箭头 (Polygon 1) -->
                  <div class="dropdown-arrow" :class="{ 'dropdown-arrow-up': menuVisible }"></div>
                </div>
              </div>

              <!-- 角色标签胶囊 (Frame 427321246) -->
              <div class="role-container">
                <span class="role-text">{{ userRole }}</span>
              </div>
            </div>

            <!-- 用户下拉菜单 -->
            <div v-if="menuVisible" class="user-dropdown-menu">
              <div v-for="(item, idx) in bottomMenuItems" :key="idx" class="dropdown-item"
                :class="{ 'dropdown-item-disabled': item.available === false }"
                @click="item.available !== false && navigateToPage(item)">
                <Icon :icon="item.icon" class="dropdown-item-icon"
                  :class="[item.iconColor || 'text-gray-500', item.available === false ? 'text-gray-300' : '']" />
                <span :class="{ 'text-gray-300': item.available === false }">{{ item.label }}</span>
              </div>

              <div class="dropdown-divider"></div>

              <div class="dropdown-item" @click="logout">
                <Icon icon="mdi:logout" class="dropdown-item-icon text-red-500" />
                <span class="text-red-500">退出登录</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { userAPI } from '@/api';
import { useUserStore } from '@/store/modules/user';
import LanguageSwitcher from './LanguageSwitcher.vue';
import ThemeToggle from './ThemeToggle.vue';

const userStore = useUserStore();
const router = useRouter();
const route = useRoute();

// 下拉菜单显示状态
const menuVisible = ref(false);

// 底部菜单项数据（从SideMenu.vue移过来）
const bottomMenuItems = ref([
  { icon: 'mdi:account', label: '个人中心', route: '/profile', iconColor: 'text-gray-600', available: true },
  { icon: 'mdi:help-circle', label: '帮助中心', route: '/help', iconColor: 'text-gray-600', available: false },
  { icon: 'mdi:cog', label: '设置', route: '/settings', iconColor: 'text-gray-600', available: false }
]);

const props = defineProps({
  userName: {
    type: String,
    default: '欧阳'
  },
  userAvatar: {
    type: String,
    default: ''
  },
  userRole: {
    type: String,
    default: '保险代理人'
  },
  messageCount: {
    type: Number,
    default: 0
  },
  notificationCount: {
    type: Number,
    default: 0
  },
  toggleMenu: {
    type: Function,
    required: true
  },
  navItems: {
    type: Array,
    default: () => [
      { name: '首页', path: '/' },
      { name: '公司产品', path: '/products' },
      { name: '计划书中心', path: '/plan-center' },
      { name: '客户服务', path: '/customer-service' },
      { name: '常用工具', path: '/tools' },
      { name: '产品选型', path: '/product-selection' },
      { name: '培训学习', path: '/training' }
    ]
  }
});

const currentPath = computed(() => {
  return route.path;
});

// 切换下拉菜单显示状态
const toggleDropdown = () => {
  menuVisible.value = !menuVisible.value;
};

// 关闭下拉菜单
const closeDropdown = () => {
  menuVisible.value = false;
};

// 点击外部区域关闭下拉菜单
const handleClickOutside = (event) => {
  const userInfoContainer = document.querySelector('.user-info-container');
  if (userInfoContainer && !userInfoContainer.contains(event.target)) {
    closeDropdown();
  }
};

// 页面导航
const navigateToPage = (item) => {
  if (item.route) {
    router.push(item.route);
    closeDropdown();
  }
};

// 退出登录
const logout = () => {
  userAPI.logout().then(() => {
    // 清除本地用户信息
    localStorage.removeItem('userInfo');
    localStorage.removeItem('token');

    // 跳转到登录页
    router.push('/login');
  }).catch(error => {
    console.error('退出登录失败', error);
  });
};

const goToMessageCenter = () => {
  router.push('/message-center');
};

// 组件挂载后添加点击外部区域关闭下拉菜单的事件监听
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

// 组件卸载前移除事件监听
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
.router-link-active {
  position: relative;
}

.router-link-active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #2563eb;
}

/* 图标组样式 */
.icons-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px;
  gap: 12px;
  height: 32px;
}

.icon-wrapper {
  box-sizing: border-box;
  position: relative;
  width: 30px;
  height: 30px;
  background: var(--bg-primary);
  border-radius: 4px;
  cursor: pointer;
}

.icon {
  position: absolute;
  width: 16px;
  height: 16px;
  left: 7px;
  top: 7px;
}

.notification-dot {
  position: absolute;
  width: 6px;
  height: 6px;
  right: 3px;
  top: 3px;
  background: #FF0000;
  border-radius: 50%;
}

.divider {
  width: 1px;
  height: 20px;
  background: #D9D9D9;
  margin: 0 6px;
}

/* 用户信息区域样式 */
.user-info-container {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 3px;
  gap: 10px;
  width: 161px;
  height: 32px;
  background: #FFFFFF;
  border-radius: 16px;
  flex: none;
  order: 4;
  flex-grow: 0;
  margin-left: 12px;
  /* 与分隔线的间距 */
  cursor: pointer;
}

.user-info-wrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px;
  gap: 5px;
  width: 155px;
  height: 26px;
  flex: none;
  order: 0;
  flex-grow: 0;
}

.avatar-name-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px;
  gap: 5px;
  width: 66px;
  height: 26px;
  flex: none;
  order: 0;
  flex-grow: 0;
}

.avatar-container {
  width: 26px;
  height: 26px;
  border-radius: 8px;
  overflow: hidden;
  flex: none;
  order: 0;
  flex-grow: 0;
}

.name-dropdown-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px;
  gap: 1px;
  width: 35px;
  height: 15px;
  flex: none;
  order: 1;
  flex-grow: 0;
}

.user-name {
  width: 24px;
  height: 15px;
  font-family: 'Inter', sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 15px;
  text-transform: capitalize;
  color: #303133;
  flex: none;
  order: 0;
  flex-grow: 0;
}

.dropdown-arrow {
  width: 10px;
  height: 6px;
  background: #303133;
  border-radius: 0.5px;
  transform: matrix(1, 0, 0, -1, 0, 0);
  flex: none;
  order: 1;
  flex-grow: 0;
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
  transition: transform 0.2s ease;
}

.dropdown-arrow-up {
  transform: matrix(1, 0, 0, 1, 0, 0);
}

.role-container {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 5px 12px;
  gap: 10px;
  width: 84px;
  height: 26px;
  background: #401EEB;
  border-radius: 16px;
  flex: none;
  order: 1;
  flex-grow: 0;
}

.role-text {
  width: 60px;
  height: 15px;
  font-family: 'Inter', sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 15px;
  text-transform: capitalize;
  color: #FFFFFF;
  flex: none;
  order: 0;
  flex-grow: 0;
}

/* 下拉菜单样式 */
.user-dropdown-menu {
  position: absolute;
  top: 38px;
  right: 0;
  width: 180px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 50;
  padding: 8px 0;
  animation: dropdown-fade 0.2s ease;
}

@keyframes dropdown-fade {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.dropdown-item:hover {
  background-color: var(--bg-primary);
}

.dropdown-item-disabled {
  cursor: not-allowed;
  opacity: 0.7;
}

.dropdown-item-disabled:hover {
  background-color: transparent;
}

.dropdown-item-icon {
  width: 18px;
  height: 18px;
  margin-right: 12px;
}

.dropdown-divider {
  height: 1px;
  background-color: #E5E7EB;
  margin: 8px 0;
}
</style>