<template>
    <a-modal v-model:visible="visible" :title="`AI分析 - ${proposalName || '计划书'}`" width="800px" :footer="null"
        :maskClosable="false" @cancel="handleClose">
        <!-- 聊天消息区域 -->
        <div class="chat-container h-96 flex flex-col">
            <!-- 消息列表 -->
            <div ref="messagesContainer" class="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50 rounded-lg mb-4">
                <div v-if="messages.length === 0 && !isLoading" class="text-center text-gray-500 py-8">
                    <p>请开始与AI对话，分析您的计划书</p>
                </div>
                <!-- 消息列表 -->
                <div v-for="(message, index) in messages" :key="index" class="flex"
                    :class="message.type === 'user' ? 'justify-end' : 'justify-start'">
                    <!-- 只显示有内容的消息，避免空的AI消息气泡 -->
                    <div v-if="message.content.trim()" class="max-w-xs lg:max-w-md px-4 py-2 rounded-lg" :class="message.type === 'user'
                        ? 'bg-blue-500 text-white'
                        : 'bg-white text-gray-800 border'
                        ">
                        <div class="flex items-start space-x-2">
                            <Icon v-if="message.type === 'ai'" icon="mdi:robot"
                                class="text-blue-500 mt-1 flex-shrink-0" />
                            <div class="flex-1">
                                <div class="text-sm whitespace-pre-wrap">{{ message.content }}</div>
                                <div class="text-xs opacity-70 mt-1">
                                    {{ formatTime(message.timestamp) }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- AI正在输入提示 -->
                <div v-if="isAITyping && !isStreamingResponse" class="flex justify-start">
                    <div class="bg-white text-gray-800 border px-4 py-2 rounded-lg">
                        <div class="flex items-center space-x-2">
                            <Icon icon="mdi:robot" class="text-blue-500" />
                            <div class="flex space-x-1">
                                <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                                <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                                    style="animation-delay: 0.1s"></div>
                                <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                                    style="animation-delay: 0.2s"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="flex space-x-2">
                <a-input v-model:value="inputMessage" placeholder="请输入您想了解的问题..." @press-enter="sendMessage"
                    :disabled="isLoading || isInitializing" class="flex-1" />
                <a-button type="primary" @click="sendMessage" :loading="isLoading"
                    :disabled="!inputMessage.trim() || isInitializing">
                    <template #icon>
                        <Icon icon="mdi:send" />
                    </template>
                    发送
                </a-button>
            </div>

            <!-- 初始化状态提示 -->
            <div v-if="isInitializing" class="text-center text-blue-500 mt-2">
                <Icon icon="mdi:loading" class="animate-spin mr-1" />
                正在初始化AI会话，请稍候...
            </div>
        </div>
    </a-modal>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue';
import { Icon } from '@iconify/vue';
import { message } from 'ant-design-vue';
import { createSession, createSessionWithPdf, processQuery, clearSession } from '@/api/chat';

// Props
const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    proposalId: {
        type: [String, Number],
        default: null
    },
    proposalName: {
        type: String,
        default: ''
    }
});

// Emits
const emit = defineEmits(['update:visible', 'close']);

// 数据
const messages = ref([]);
const inputMessage = ref('');
const isLoading = ref(false);
const isInitializing = ref(false);
const isAITyping = ref(false);
const isStreamingResponse = ref(false);
const sessionId = ref(null);
const messagesContainer = ref(null);

// 计算属性
const visible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
});

// 监听模态框显示状态
watch(() => props.visible, (newVisible) => {
    if (newVisible && props.proposalId) {
        initializeChat();
    } else if (!newVisible) {
        resetChat();
    }
});

// 初始化聊天
const initializeChat = async () => {
    isInitializing.value = true;

    try {
        // 1. 创建会话
        const sessionResponse = await createSession();
        sessionId.value = sessionResponse.sessionId

        // 2. 关联计划书
        await createSessionWithPdf(sessionId.value, props.proposalId);

        // 3. 添加欢迎消息
        messages.value.push({
            type: 'ai',
            content: `您好！我已经加载了「${props.proposalName}」的计划书内容。您可以问我关于这份计划书的任何问题，比如：\n\n• 这份计划书的主要保障内容是什么？\n• 保费和保额是多少？\n• 有什么特色保障？\n• 适合什么样的客户群体？`,
            timestamp: new Date()
        });

        // 滚动到底部
        await nextTick();
        scrollToBottom();

    } catch (error) {
        console.error('初始化聊天失败:', error);
        message.error('初始化AI对话失败，请重试');
    } finally {
        isInitializing.value = false;
    }
};

// 重置聊天
const resetChat = () => {
    messages.value = [];
    inputMessage.value = '';
    sessionId.value = null;
    isLoading.value = false;
    isInitializing.value = false;
    isAITyping.value = false;
    isStreamingResponse.value = false;
};

// 发送消息
const sendMessage = async () => {
    if (!inputMessage.value.trim() || isLoading.value || !sessionId.value) {
        return;
    }

    const userMessage = inputMessage.value.trim();
    inputMessage.value = '';

    // 添加用户消息
    messages.value.push({
        type: 'user',
        content: userMessage,
        timestamp: new Date()
    });

    // 滚动到底部
    await nextTick();
    scrollToBottom();

    // 显示AI正在输入
    isAITyping.value = true;
    isLoading.value = true;

    // 创建AI消息占位符
    const aiMessageIndex = messages.value.length;
    messages.value.push({
        type: 'ai',
        content: '',
        timestamp: new Date()
    });

    // 注意：不要立即设置isStreamingResponse，等收到第一个token时再设置
    // 这样可以确保AI正在输入动画在等待期间正常显示

    try {
        // 调用SSE流式API
        await processQuery(
            sessionId.value,
            userMessage,
            // onToken回调 - 处理流式数据
            (token) => {
                // 如果是第一个token，设置流式响应状态，隐藏AI正在输入动画
                if (!isStreamingResponse.value) {
                    isStreamingResponse.value = true;
                    isAITyping.value = false; // 隐藏AI正在输入动画
                }

                // 实时更新AI消息内容
                if (messages.value[aiMessageIndex]) {
                    messages.value[aiMessageIndex].content += token;
                    // 滚动到底部以跟随新内容
                    nextTick(() => scrollToBottom());
                }
            },
            // onComplete回调 - 流式传输完成
            (fullResponse) => {
                console.log('AI回复完成:', fullResponse);
                isStreamingResponse.value = false;
                // 确保最终消息内容正确
                if (messages.value[aiMessageIndex]) {
                    messages.value[aiMessageIndex].content = fullResponse || '抱歉，我没有收到完整的回复。';
                }
            },
            // onError回调 - 处理错误
            (errorMessage) => {
                console.error('SSE流式传输错误:', errorMessage);
                isStreamingResponse.value = false;
                // 更新消息为错误提示
                if (messages.value[aiMessageIndex]) {
                    messages.value[aiMessageIndex].content = '抱歉，我遇到了一些技术问题，请稍后重试。';
                }
                message.error('AI回复失败: ' + errorMessage);
            }
        );

    } catch (error) {
        console.error('发送消息失败:', error);
        message.error('AI回复失败，请重试');
        isStreamingResponse.value = false;

        // 更新AI消息为错误提示
        if (messages.value[aiMessageIndex]) {
            messages.value[aiMessageIndex].content = '抱歉，我遇到了一些技术问题，请稍后重试。';
        }
    } finally {
        isAITyping.value = false;
        isLoading.value = false;
        isStreamingResponse.value = false;

        // 最终滚动到底部
        await nextTick();
        scrollToBottom();
    }
};

// 格式化时间
const formatTime = (timestamp) => {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
};

// 滚动到底部
const scrollToBottom = () => {
    if (messagesContainer.value) {
        messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
    }
};

// 关闭模态框
const handleClose = async () => {
    emit('close');
    emit('update:visible', false);
    await clearSession(sessionId.value);
};
</script>

<style scoped>
.chat-container {
    min-height: 400px;
}

/* 自定义滚动条 */
.overflow-y-auto::-webkit-scrollbar {
    width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 消息动画 */
.space-y-4>*+* {
    margin-top: 1rem;
}

/* 打字动画 */
@keyframes bounce {

    0%,
    80%,
    100% {
        transform: scale(0);
    }

    40% {
        transform: scale(1);
    }
}

.animate-bounce {
    animation: bounce 1.4s infinite ease-in-out both;
}

/* 修复发送按钮图标文本对齐 */
:deep(.ant-btn) {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

:deep(.ant-btn .anticon),
:deep(.ant-btn .iconify) {
    display: inline-flex;
    align-items: center;
    line-height: 1;
    vertical-align: middle;
}

/* 确保按钮内容垂直居中 */
:deep(.ant-btn-content) {
    display: inline-flex;
    align-items: center;
    gap: 4px;
}
</style>