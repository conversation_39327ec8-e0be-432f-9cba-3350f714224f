<template>
  <!-- 侧边栏菜单 -->
  <div class="sidebar-menu">

    <!-- 菜单内容区域 -->
    <div class="menu-content">
      <!-- 菜单分类 -->
      <div v-for="(category, categoryIndex) in menuCategories" :key="categoryIndex" class="menu-section">
        <div class="menu-section-header" @click="toggleCategory(category)"
          :class="{ 'cursor-pointer': category.expandable }">
          <Icon :icon="category.icon" class="w-5 h-5" :class="category.iconColor" />
          <p>{{ category.i18nKey ? t(category.i18nKey) : category.name }}</p>
          <Icon v-if="category.expandable" :icon="category.expanded ? 'mdi:chevron-down' : 'mdi:chevron-right'"
            class="ml-auto w-5 h-5 text-gray-400" />
        </div>

        <div class="menu-section-content" v-show="!category.expandable || category.expanded">
          <div v-for="(item, itemIndex) in category.items" :key="itemIndex" class="menu-item" :class="{
            'menu-item-disabled': item.available === false,
            'active': item.selected
          }" @click="item.available !== false && navigateToPage(item)">
            <Icon :icon="item.icon" class="menu-item-icon"
              :class="[item.selected ? 'text-blue-500' : 'text-gray-500', item.available === false ? 'text-gray-300' : '']" />
            <span
              :class="[item.selected ? 'text-blue-500' : 'text-gray-700', item.available === false ? 'text-gray-300' : '']">
              {{ item.i18nKey ? t(item.i18nKey) : item.label }}
              <span v-if="item.beta" class="ml-1 px-1 py-0.5 text-xs bg-red-100 text-red-500 rounded">Beta</span>
              <span v-if="item.new" class="ml-1 px-1 py-0.5 text-xs bg-green-100 text-green-500 rounded">New</span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';

// 路由实例
const router = useRouter();

// 国际化实例
const { t } = useI18n();

// 侧边栏菜单数据（重新分类和组织）
const menuCategories = ref([
  {
    name: '公司产品',
    i18nKey: 'menu.category.companyProducts',
    icon: 'mdi:package-variant',
    iconColor: 'text-gray-800',
    expanded: false,
    expandable: true,
    items: [
      { icon: 'mdi:office-building', label: '保险公司', i18nKey: 'menu.insuranceCompany', selected: false, route: '/company', available: true },
      { icon: 'mdi:file-document-outline', label: '产品资料', i18nKey: 'menu.productInfo', selected: false, route: '/products', available: true },
      { icon: 'mdi:file-document-outline', label: 'LifeBee产品资料', i18nKey: 'Lifebee产品', selected: false, route: '/lifebee-products', available: true },
      { icon: 'mdi:compare', label: '产品对比', i18nKey: 'menu.productCompare', selected: false, route: '/product-compare', available: true },
    ]
  },
  {
    name: '计划书中心',
    i18nKey: 'menu.category.planCenter',
    icon: 'mdi:file-document',
    iconColor: 'text-gray-800',
    expanded: false,
    expandable: true,
    items: [
      { icon: 'mdi:file-document-edit', label: '计划书生成', i18nKey: 'menu.planCreator', selected: false, route: '/plan-creator', available: true },
      { icon: 'mdi:file-document-multiple', label: '我的计划书', i18nKey: 'menu.myPlans', selected: false, route: '/my-plans', available: true },
    ]
  },
  {
    name: '客户服务',
    i18nKey: 'menu.category.customerService',
    icon: 'mdi:account-group',
    iconColor: 'text-gray-800',
    expanded: false,
    expandable: true,
    items: [
      { icon: 'mdi:account-multiple', label: '我的客户', i18nKey: 'menu.myClients', selected: false, route: '/my-clients', available: true },
      { icon: 'mdi:file-document-multiple', label: '订单管理', i18nKey: 'menu.orderManagement', selected: false, route: '/my-policy-orders', available: true },
      { icon: 'mdi:calendar-clock', label: '预约管理', i18nKey: 'menu.appointmentManagement', selected: false, route: '/policy-appoint', available: true },
      { icon: 'mdi:calendar-month', label: '我的日程', i18nKey: 'menu.mySchedule', selected: false, route: '/calendar', available: true },
      { icon: 'mdi:chart-bar', label: '需求分析', i18nKey: 'menu.needsAnalysis', selected: false, route: '/needs-analysis', available: true },
      { icon: 'mdi:clipboard-text', label: '问卷调查', i18nKey: 'menu.survey', selected: false, route: '/survey-list', available: false },
      { icon: 'mdi:calendar-check', label: '回访提醒', i18nKey: 'menu.followupReminder', selected: false, route: '/client-followup', available: false },
    ]
  },
  {
    name: '常用工具',
    i18nKey: 'menu.category.commonTools',
    icon: 'mdi:tools',
    iconColor: 'text-gray-800',
    expanded: false,
    expandable: true,
    items: [
      // { icon: 'mdi:robot', label: '海报编辑', i18nKey: 'menu.posterEditor', selected: false, route: '/poster-editor', available: true },
      { icon: 'mdi:robot-excited', label: 'AI助手', i18nKey: 'menu.aiAssistant', selected: false, route: '/ai-chat-new', available: true, new: true },
      { icon: 'mdi:share-variant', label: '分享统计', i18nKey: 'menu.shareStatistics', selected: false, route: '/share-statistics', available: false },
      // { icon: 'mdi:chart-box-outline', label: '收益演算', i18nKey: 'menu.benefitCalculator', selected: false, route: '/tools/calculator/benefit', available: true },
      { icon: 'mdi:calculator', label: '保费试算', i18nKey: 'menu.premiumCalculator', selected: false, route: '/tools/calculator/premium', available: true },
      { icon: 'mdi:calculator-variant', label: '收益演算', i18nKey: 'menu.interestCalculator', selected: false, route: '/tools/calculator-tools/compound-interest', available: true },
      { icon: 'mdi:chart-line', label: '分红实现率', i18nKey: 'menu.dividendRate', selected: false, route: '/tools/calculator-tools/dividend-rate', available: true },
      { icon: 'mdi:image-filter-none', label: '海报生成', i18nKey: 'menu.posterGenerator', selected: false, route: '/tools/poster-generator', available: true },
    ]
  },
  {
    name: '产品资讯',
    i18nKey: 'menu.category.productInfo',
    icon: 'mdi:newspaper',
    iconColor: 'text-gray-800',
    expanded: false,
    expandable: true,
    items: [

      { icon: 'mdi:view-dashboard', label: 'insuraim快讯', i18nKey: 'menu.quickNews', selected: false, route: '/news?category=快讯&label=insuraim快讯', available: true },
      { icon: 'mdi:view-dashboard', label: '平台精选', i18nKey: 'menu.productSelection', selected: false, route: '/news?category=平台精选&label=平台精选', available: true },
      // { icon: 'mdi:trending-up', label: '产品动态', i18nKey: 'menu.productUpdates', selected: false, route: '/news?category=产品动态', available: true },
      { icon: 'mdi:tag-multiple', label: '产品动态', i18nKey: 'menu.productUpdates', selected: false, route: '/news?category=产品优惠&label=产品动态', available: true },
      { icon: 'mdi:newspaper-variant', label: '行业新闻', i18nKey: 'menu.industryNews', selected: false, route: '/news?category=行业新闻&label=行业新闻', available: true },
      // { icon: 'mdi:gavel', label: '政策法规', i18nKey: 'menu.policyRegulations', selected: false, route: '/news?category=政策法规&label=政策法规', available: true },
      // { icon: 'mdi:chart-line', label: '市场分析', i18nKey: 'menu.marketAnalysis', selected: false, route: '/news?category=市场分析', available: true },
    ]
  },
  {
    name: '社区中心',
    i18nKey: 'menu.communityCenter',
    icon: 'mdi:account-group',
    iconColor: 'text-gray-800',
    expanded: false,
    expandable: true,
    items: [
      { icon: 'mdi:forum', label: '社区交流', i18nKey: 'menu.community', selected: false, route: '/community', available: false },
      { icon: 'mdi:folder-account', label: '问答中心', i18nKey: 'menu.questionCenter', selected: false, route: '/question-center', available: false },
    ]
  },
  {
    name: '培训学习',
    i18nKey: 'menu.category.training',
    icon: 'mdi:school',
    iconColor: 'text-gray-800',
    expanded: false,
    expandable: true,
    items: [
      { icon: 'mdi:bookmark-multiple', label: '知识库', i18nKey: 'menu.knowledgeBase', selected: false, route: '/kb/company', available: true },
      { icon: 'mdi:folder-account', label: '个人云盘', i18nKey: 'menu.personalCloud', selected: false, route: '/personal-cloud', available: true },
      { icon: 'mdi:certificate-outline', label: '资格考试', i18nKey: 'menu.qualificationExam', selected: false, route: '/exam-category', available: true },
      // { icon: 'mdi:forum', label: '社区交流', i18nKey: 'menu.community', selected: false, route: '/community', available: false },
    ]
  },
  {
    name: '消息管理',
    i18nKey: 'menu.category.messageManagement',
    icon: 'mdi:bell',
    iconColor: 'text-gray-800',
    expanded: false,
    expandable: true,
    items: [
      { icon: 'mdi:bell', label: '消息中心', i18nKey: 'menu.messageCenter', selected: false, route: '/message-center', available: true },
      { icon: 'mdi:bell-ring', label: '系统消息', i18nKey: 'menu.systemMessages', selected: false, route: '/message-center/system', available: false },
      { icon: 'mdi:book-open', label: '知识更新', i18nKey: 'menu.knowledgeUpdates', selected: false, route: '/message-center/knowledge', available: false },
      { icon: 'mdi:file-document-alert', label: '计划书提醒', i18nKey: 'menu.planReminders', selected: false, route: '/message-center/plans', available: false }
    ]
  },
]);

// 切换分类展开/折叠
const toggleCategory = (category) => {
  if (category.expandable) {
    category.expanded = !category.expanded;
  }
};

// 页面导航
const navigateToPage = (item) => {
  if (item.route) {
    // 先取消所有菜单项的选中状态
    clearSelectedState();

    // 设置当前点击的菜单项为选中状态
    item.selected = true;

    // 导航到目标路由
    router.push(item.route);
  }
};

// 清除所有菜单项的选中状态
const clearSelectedState = () => {
  // 清除主菜单项的选中状态
  menuCategories.value.forEach(category => {
    category.items.forEach(item => {
      item.selected = false;
    });
  });
};

// 组件挂载时设置选中状态
onMounted(() => {
  // 标记当前路由对应的菜单项为选中状态
  const currentPath = router.currentRoute.value.path;

  menuCategories.value.forEach(category => {
    category.items.forEach(item => {
      if (item.route === currentPath) {
        item.selected = true;
      }
    });
  });
});
</script>

<style scoped>
/* 侧边栏菜单样式 */
.sidebar-menu {
  background-color: var(--bg-secondary);
  border-radius: 12px;
  min-width: 220px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  height: auto;
}

/* 菜单头部 */
.menu-header {
  flex-shrink: 0;
}

/* 菜单内容 */
.menu-content {
  flex-grow: 1;
  overflow-y: auto;
  padding: 12px 8px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 菜单部分 */
.menu-section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  width: 100%;
  margin-bottom: 8px;
}

/* 菜单部分标题 */
.menu-section-header {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  width: 100%;
}

.menu-section-header p {
  margin-left: 8px;
  font-family: 'PingFang SC', sans-serif;
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  display: flex;
  margin-bottom: 0;
  align-items: center;
  text-transform: capitalize;
  color: var(--text-primary);
}

/* 菜单部分内容 */
.menu-section-content {
  padding: 0 4px;
  width: 100%;
}

/* 菜单项 */
.menu-item {
  display: flex;
  align-items: center;
  padding: 6px 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  margin: 2px 0;
  position: relative;
  height: 32px;
  width: 100%;
}

.menu-item:hover {
  background-color: var(--bg-primary);
}

.menu-item span {
  font-family: 'PingFang SC', sans-serif;
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  display: flex;
  align-items: center;
  text-transform: capitalize;
  color: var(--text-primary);
}

.menu-item span.text-blue-500,
.menu-item span.text-blue-600 {
  color: #401EEB;
  font-weight: 600;
}

/* 选中状态菜单项 */
.menu-item.active {
  background-color: var(--bg-primary);
  position: relative;
}

.menu-item.active::before {
  content: "";
  position: absolute;
  left: 0;
  top: 8px;
  bottom: 8px;
  width: 3px;
  background-color: #401EEB;
  border-radius: 0 2px 2px 0;
}

.menu-item.active span {
  color: #401EEB;
  font-weight: 600;
}

/* 不可用菜单项样式 */
.menu-item-disabled {
  cursor: not-allowed;
  opacity: 0.7;
}

.menu-item-disabled:hover {
  background-color: transparent;
}

.menu-item-icon {
  width: 18px;
  height: 18px;
  margin-right: 14px;
  margin-left: 9px;
}

.menu-item-icon.text-blue-500 {
  color: #401EEB;
}
</style>
