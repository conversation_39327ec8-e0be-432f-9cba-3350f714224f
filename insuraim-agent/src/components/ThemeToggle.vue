<template>
  <div class="icon-wrapper" @click="toggleTheme">
    <template v-if="isDarkTheme">
      <Icon icon="material-symbols:light-mode" class="icon" />
    </template>
    <template v-else>
      <Icon icon="material-symbols:dark-mode" class="icon" />
    </template>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { Icon } from '@iconify/vue';
import { useThemeStore } from '@/store/modules/theme';

// 使用主题Store
const themeStore = useThemeStore();

// 获取当前主题状态
const isDarkTheme = computed(() => themeStore.isDarkTheme);

// 切换主题的方法
const toggleTheme = () => {
  themeStore.toggleTheme();
};
</script>

<style scoped>
.icon-wrapper {
  box-sizing: border-box;
  position: relative;
  width: 30px;
  height: 30px;
  background: var(--bg-primary);
  border-radius: 4px;
  cursor: pointer;
}

.icon {
  position: absolute;
  width: 16px;
  height: 16px;
  left: 7px;
  top: 7px;
}
</style> 