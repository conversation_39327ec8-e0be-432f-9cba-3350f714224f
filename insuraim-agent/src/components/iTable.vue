<template>
    <div :class="['i-table-container', `theme-${theme}`]">
        <a-table :dataSource="processedData" :columns="processedColumns" :rowKey="rowKey" :pagination="false"
            :bordered="bordered" :loading="loading" :scroll="scroll" :customRow="customRowFunction" :class="tableClass"
            size="middle">
            <!-- 使用插槽允许自定义列内容 -->
            <template v-for="column in visibleColumns" :key="column.dataIndex"
                #[`${column.dataIndex}`]="{ text, record, index }">
                <slot :name="`cell-${column.dataIndex}`" :text="text" :record="record" :index="index">
                    <template v-if="column.customRender">
                        <!-- 这里会使用Ant Design的customRender函数 -->
                    </template>
                    <template v-else>
                        {{ text }}
                    </template>
                </slot>
            </template>

            <!-- 空状态插槽 -->
            <template #emptyText>
                <slot name="empty">
                    {{ emptyText }}
                </slot>
            </template>
        </a-table>
    </div>
</template>

<script setup>
import { computed, ref } from 'vue';
import { Table } from 'ant-design-vue';

const props = defineProps({
    // 表格数据源
    dataSource: {
        type: Array,
        default: () => []
    },
    // 列配置
    columns: {
        type: Array,
        default: () => []
    },
    // 行的唯一标识字段，用于v-for的key
    rowKey: {
        type: [String, Function],
        default: 'id'
    },
    // 表格主题样式
    theme: {
        type: String,
        default: 'default',
        validator: (value) => ['default', 'primary', 'success', 'warning', 'danger', 'custom'].includes(value)
    },
    // 每隔多少行显示一行，1表示显示所有行
    rowStep: {
        type: Number,
        default: 1
    },
    // 开始显示的行索引
    rowStartIndex: {
        type: Number,
        default: 0
    },
    // 高亮行的条件函数：(row, index) => boolean
    highlightRow: {
        type: Function,
        default: null
    },
    // 高亮行的样式
    highlightStyle: {
        type: Object,
        default: () => ({ backgroundColor: 'rgba(59, 130, 246, 0.1)' })
    },
    // 表格为空时显示的文本
    emptyText: {
        type: String,
        default: '暂无数据'
    },
    // 是否显示边框
    bordered: {
        type: Boolean,
        default: true
    },
    // 显示的行过滤器：(row, index) => boolean
    rowFilter: {
        type: Function,
        default: null
    },
    // 表格自定义样式类
    tableClass: {
        type: String,
        default: ''
    },
    // 加载状态
    loading: {
        type: Boolean,
        default: false
    },
    // 滚动设置
    scroll: {
        type: Object,
        default: () => ({})
    }
});

// 计算可见的列
const visibleColumns = computed(() => {
    return props.columns.filter(col => col.visible !== false);
});

// 处理列配置，将自定义渲染函数适配为a-table的格式
const processedColumns = computed(() => {
    return visibleColumns.value.map(column => {
        const { dataIndex, title, width, align = 'center', fixed, sorter, filters } = column;

        // 创建列配置对象
        const columnConfig = {
            dataIndex,
            title,
            key: dataIndex,
            width,
            align,
            fixed,
            sorter,
            filters,
            slots: { customRender: dataIndex },
        };

        // 如果有自定义渲染函数
        if (column.render || column.formatter || column.customRender) {
            columnConfig.customRender = ({ text, record, index }) => {
                if (column.customRender) {
                    // 如果已经提供了Ant Design风格的customRender函数
                    return column.customRender({ text, record, index });
                } else if (column.render) {
                    // 如果是自定义渲染函数
                    return column.render(text, record, index);
                } else if (column.formatter) {
                    // 如果是格式化函数
                    return column.formatter(text, record, index);
                }
                return text;
            };
        }

        // 如果有条件样式
        if (column.conditionalStyle || column.conditionalClass) {
            const originalRender = columnConfig.customRender;

            columnConfig.customRender = ({ text, record, index }) => {
                let content = text;

                // 使用原有的渲染函数获取内容（如果有）
                if (originalRender) {
                    content = originalRender({ text, record, index });
                }

                // 应用条件样式
                let cellStyle = {};
                let cellClass = '';

                if (column.conditionalStyle) {
                    cellStyle = column.conditionalStyle(text, record, index) || {};
                }

                if (column.conditionalClass) {
                    cellClass = column.conditionalClass(text, record, index) || '';
                }

                // 创建带样式的内容
                return {
                    children: content,
                    props: {
                        style: cellStyle,
                        class: cellClass
                    }
                };
            };
        }

        return columnConfig;
    });
});

// 处理数据，应用行过滤和行步长
const processedData = computed(() => {
    let filteredData = props.dataSource;

    // 应用行过滤器
    if (props.rowFilter) {
        filteredData = filteredData.filter((row, index) => props.rowFilter(row, index));
    }

    // 应用行步长
    if (props.rowStep > 1) {
        filteredData = filteredData.filter((_, index) =>
            (index + props.rowStartIndex) % props.rowStep === 0
        );
    }

    return filteredData;
});

// 自定义行属性函数
const customRowFunction = (record, index) => {
    const customProps = {};

    // 应用交替行样式
    if (index % 2 === 1) {
        customProps.class = 'bg-gray-50';
    }

    // 应用高亮样式
    if (props.highlightRow && props.highlightRow(record, index)) {
        customProps.class = (customProps.class || '') + ' highlight-row';

        // 如果highlightStyle是函数，则调用它并传入record
        if (typeof props.highlightStyle === 'function') {
            customProps.style = props.highlightStyle(record);
        } else {
            customProps.style = { ...props.highlightStyle };
        }
    }

    return customProps;
};
</script>

<style scoped>
.i-table-container {
    width: 100%;
    margin-bottom: 1rem;
}

/* 主题样式 - 深色主题 */
.theme-default :deep(.ant-table-thead > tr > th) {
    background-color: #2c333c;
    color: #ffffff;
}

.theme-default :deep(.ant-table-row) {
    background-color: #ffffff !important;
}

.theme-default :deep(.highlight-row) {
    background-color: rgba(55, 65, 81, 0.6) !important;
}

.theme-primary :deep(.ant-table-thead > tr > th) {
    background-color: var(--primary);
    color: #ffffff;
}

.theme-primary :deep(.ant-table-row) {
    background-color: #ffffff !important;
}

.theme-primary :deep(.highlight-row) {
    background-color: rgba(var(--primary-rgb), 0.6) !important;
}

.theme-success :deep(.ant-table-thead > tr > th) {
    background-color: #16a34a;
    color: #ffffff;
}

.theme-success :deep(.ant-table-row) {
    background-color: #ffffff !important;
}

.theme-success :deep(.highlight-row) {
    background-color: rgba(22, 163, 74, 0.6) !important;
}

.theme-warning :deep(.ant-table-thead > tr > th) {
    background-color: #d97706;
    color: #ffffff;
}

.theme-warning :deep(.ant-table-row) {
    background-color: #ffffff !important;
}

.theme-warning :deep(.highlight-row) {
    background-color: rgba(217, 119, 6, 0.6) !important;
}

.theme-danger :deep(.ant-table-thead > tr > th) {
    background-color: #dc2626;
    color: #ffffff;
}

.theme-danger :deep(.ant-table-row) {
    background-color: #ffffff !important;
}

.theme-danger :deep(.highlight-row) {
    background-color: rgba(220, 38, 38, 0.6) !important;
}

/* 自定义主题样式 - 使用CSS变量动态设置颜色 */
.theme-custom :deep(.ant-table-thead > tr > th) {
    background-color: var(--custom-theme-color, #8B5CF6);
    color: var(--custom-theme-text-color, #ffffff);
}

.theme-custom :deep(.ant-table-row) {
    background-color: var(--custom-theme-color-30, rgba(139, 92, 246, 0.3)) !important;
}

.theme-custom :deep(.highlight-row) {
    background-color: var(--custom-theme-color-60, rgba(139, 92, 246, 0.6)) !important;
}
</style>