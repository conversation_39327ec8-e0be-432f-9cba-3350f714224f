<template>
  <div class="min-h-screen bg-primary">
    <Navbar :user-name="userInfo?.name || userInfo?.username || '用户'" :user-avatar="userInfo?.avatar || ''"
      :user-role="userInfo?.roleName || '超级管理员'" :message-count="messageCount" :toggle-menu="toggleMenu" />
    <div class="flex flex-col container mx-auto">
      <div class="flex overflow-hidden py-4 px-6">
        <SideMenu :user-name="userInfo?.name || userInfo?.username || '用户'" :user-role="userInfo?.roleName || '超级管理员'"
          :user-avatar="userInfo?.avatar || ''" />
        <div class="flex-1  ml-4">
          <router-view></router-view>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import Navbar from '@/components/Navbar.vue';
import SideMenu from '@/components/SideMenu.vue';
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { useMessagePolling } from '@/utils/useMessagePolling';
import { useUserMinix } from '@/minix/UserMinix.vue';

// 使用UserMinix获取用户信息
const { userInfo } = useUserMinix();

// 初始化消息轮询
const { unreadCount } = useMessagePolling(10000);

// 消息数量
const messageCount = ref(0);

// 监听unreadCount变化，更新messageCount
watch(unreadCount, (newCount) => {
  messageCount.value = newCount;
});

// 消息数量更新事件处理函数
const handleMessageCountUpdate = (event) => {
  if (event.detail && typeof event.detail.count === 'number') {
    messageCount.value = event.detail.count;
  }
};

// 组件挂载时启动轮询
onMounted(() => {
  // 添加消息数量更新事件监听
  window.addEventListener('update-message-count', handleMessageCountUpdate);
});

// 组件卸载时移除事件监听并停止轮询
onUnmounted(() => {
  window.removeEventListener('update-message-count', handleMessageCountUpdate);
});

// 侧边栏菜单控制
const isMenuOpen = ref(false);

// 切换菜单显示状态
const toggleMenu = () => {
  isMenuOpen.value = !isMenuOpen.value;
};
</script>

<style scoped>
/* 布局样式 */
</style>