import { createApp } from 'vue'
import router from './router'
import './style.css'
// 引入主题变量
import './theme.css'
import App from './App.vue'
import pinia from './store'
import './store/modules/_export'
// 引入Ant Design Vue的样式
// import 'ant-design-vue/dist/reset.css'

// 引入国际化插件
import i18nPlugin from './I18n/plugin'

const app = createApp(App)

// 注册插件
app.use(router)
app.use(pinia)
app.use(i18nPlugin)

app.mount('#app')
