<script>
import { useUserStore } from '@/store/modules/user'
import { storeToRefs } from 'pinia'

/**
 * 组合式API函数 - 获取用户数据
 * 在组合式API中使用: const { userInfo, token, isLoggedIn, getUserInfo, getToken, isAuthenticated, setUserInfo, setToken, logout } = useUserMinix()
 * @returns {Object} 用户数据和方法
 */
export function useUserMinix() {
  const userStore = useUserStore()
  // 使用storeToRefs保持响应式
  const { userInfo, token, isLoggedIn } = storeToRefs(userStore)
  
  return {
    // 状态
    userInfo,
    token,
    isLoggedIn,
    
    // Getters
    getUserInfo: () => userStore.getUserInfo,
    getToken: () => userStore.getToken,
    isAuthenticated: () => userStore.isAuthenticated,
    
    // Actions
    setUserInfo: userStore.setUserInfo,
    setToken: userStore.setToken,
    logout: userStore.logout
  }
}

/**
 * 选项式API混入 - 获取用户数据
 * 在组件中使用: mixins: [UserMinix]
 */
export default {
  computed: {
    // 用户数据
    userInfo() {
      return useUserStore().userInfo
    },
    token() {
      return useUserStore().token
    },
    isLoggedIn() {
      return useUserStore().isLoggedIn
    },
    
    // Getters
    getUserInfo() {
      return useUserStore().getUserInfo
    },
    getToken() {
      return useUserStore().getToken
    },
    isAuthenticated() {
      return useUserStore().isAuthenticated
    }
  },
  
  methods: {
    // Actions
    setUserInfo(userInfo) {
      useUserStore().setUserInfo(userInfo)
    },
    setToken(token) {
      useUserStore().setToken(token)
    },
    logout() {
      useUserStore().logout()
    }
  }
}
</script>

