import { createRouter, createWebHistory } from "vue-router";
import MainLayout from "../layouts/MainLayout.vue";

const routes = [
  {
    path: "/",
    component: MainLayout,
    children: [
      {
        path: "",
        name: "Home",
        component: () => import("../views/home.vue"),
      },
      {
        path: "about",
        name: "About",
        component: () => import("../views/about.vue"),
      },
      {
        path: "products",
        name: "Products",
        component: () => import("../views/product/products.vue"),
      },
      {
        path: "lifebee-products",
        name: "LifeBeeProducts",
        component: () => import("../views/product/lifebee-products.vue"),
      },
      {
        path: "lifebee-product/:code",
        name: "LifeBeeProductDetail",
        component: () =>
          import("../views/product/views/LifebeeProductDetail.vue"),
      },
      {
        path: "product/:id",
        name: "ProductDetail",
        component: () => import("../views/product/views/ProductDetail.vue"),
      },
      {
        path: "product-compare",
        name: "ProductCompare",
        component: () => import("../views/product-compare/product-compare.vue"),
      },
      {
        path: "company",
        name: "Company",
        component: () => import("../views/company/company.vue"),
      },
      {
        path: "company/:code",
        name: "CompanyDetail",
        component: () => import("../views/company/company-detail.vue"),
      },
      {
        path: "news",
        name: "News",
        component: () => import("../views/news/news.vue"),
      },
      {
        path: "news/:id",
        name: "NewsDetail",
        component: () => import("../views/news/news-details.vue"),
      },
      {
        path: "exam-category",
        name: "ExamCategory",
        component: () => import("../views/exam-category.vue"),
      },
      {
        path: "mock-exam",
        name: "MockExam",
        component: () => import("../views/mock-exam.vue"),
      },
      {
        path: "profile",
        name: "Profile",
        component: () => import("../views/profile.vue"),
      },
      {
        path: "plan-creator",
        name: "PlanCreator",
        component: () => import("../views/plan-creator.vue"),
      },
      {
        path: "my-plans",
        name: "MyPlans",
        component: () => import("../views/my-plans.vue"),
      },
      {
        path: "calendar",
        name: "Calendar",
        component: () => import("../views/calendar.vue"),
      },
      {
        path: "personal-cloud",
        name: "PersonalCloud",
        component: () => import("../views/personal-cloud.vue"),
      },
      {
        path: "message-center",
        name: "MessageCenter",
        component: () => import("../views/message-center.vue"),
      },
      {
        path: "my-policy-orders",
        name: "PolicyOrderList",
        component: () => import("../views/policyorder/index.vue"),
      },
      {
        path: "policyorder/detail/:policyOrderId",
        name: "PolicyDetail",
        component: () => import("../views/policyorder/PolicyDetail.vue"),
        meta: { title: "保单详情" },
      },
      {
        path: "my-clients",
        name: "MyClients",
        component: () => import("../views/customer.vue"),
      },
      {
        path: "policy-appoint",
        name: "PolicyAppoint",
        component: () => import("../views/policyappoint/index.vue"),
      },
      {
        path: "survey-list",
        name: "SurveyList",
        component: () => import("../views/survey-list.vue"),
      },
      {
        path: "survey-detail",
        name: "SurveyDetail",
        component: () => import("../views/survey-detail.vue"),
      },
      {
        path: "kb/company",
        name: "KnowledgeBaseCompany",
        component: () => import("../views/knowledge-base-company.vue"),
      },
      {
        path: "kb/category/:companyId",
        name: "KnowledgeBaseCategory",
        component: () => import("../views/knowledge-base-category.vue"),
      },
      {
        path: "kb/detail/:id",
        name: "KnowledgeBaseDetail",
        component: () => import("../views/knowledge-base-category-detail.vue"),
      },
      {
        path: "kb/item",
        name: "KnowledgeBaseItem",
        component: () => import("../views/knowledge-base-article-detail.vue"),
      },
      {
        path: "tools/calculator-tools/compound-interest",
        name: "InterestCalculator",
        component: () => import("../views/tools/interest-calculator.vue"),
      },
      {
        path: "tools/calculator/premium",
        name: "PremiumCalculator",
        component: () => import("../views/tools/premium-calculator.vue"),
      },

      {
        path: "tools/calculator-tools/dividend-rate",
        name: "DividendRate",
        component: () => import("../views/tools/dividend-rate.vue"),
      },
      {
        path: "tools/calculator/benefit",
        name: "BenefitCalculator",
        component: () => import("../views/tools/benefit-calculator.vue"),
      },
      {
        path: "tools/poster-generator",
        name: "PosterGenerator",
        component: () => import("../views/tools/poster-generator.vue"),
      },
      {
        path: "ai-chat-new",
        name: "AiChatNew",
        component: () => import("../views/tools/ai-chat-new.vue"),
      },
      {
        path: "needs-analysis",
        name: "NeedsAnalysis",
        component: () => import("../views/needs-analysis/NeedsAnalysis.vue"),
      },
    ],
  },
  {
    path: "/login",
    name: "Login",
    component: () => import("../views/login.vue"),
  },
  {
    path: "/register",
    name: "Register",
    component: () => import("../views/register.vue"),
  },
  {
    path: "/form/invite/:id",
    name: "UserForm",
    component: () => import("../views/form/user_form.vue"),
  },
  {
    path: "/form/success",
    name: "FormSuccess",
    component: () => import("../views/form/success.vue"),
  },
  // 404页面
  {
    path: "/:pathMatch(.*)*",
    name: "NotFound",
    component: () => import("../views/404.vue"),
  },
  {
    path: "/poster-editor",
    name: "PosterEditor",
    component: () => import("../views/tools/poster-editor.vue"),
  },
];

import { message } from "ant-design-vue";

const router = createRouter({
  history: createWebHistory(),
  routes,
});

// 全局路由守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem("token");

  // 判断是否是登录页或404页面或表单相关页面
  const isLoginPage = to.path === "/login";
  const isRegisterPage = to.path === "/register";
  const is404Page = to.name === "NotFound";
  // 修改判断逻辑，使用path而不是name，并且正确检查表单路径
  const isFormPage =
    to.path.includes("/form/invite") || to.path.includes("/form/success");

  // 如果不是登录页或注册页或404页面或表单页面，且没有token
  if (!isLoginPage && !isRegisterPage && !is404Page && !isFormPage && !token) {
    message.warning("请先登录");
    next("/login");
  } else if ((isLoginPage || isRegisterPage) && token) {
    // 如果已登录但访问登录页或注册页，则重定向到首页
    next("/");
  } else {
    next();
  }
});

export default router;
