import { reactive, ref } from 'vue';

// 创建一个简单的状态管理
export const useAIChat = () => {
    // 当前活动的对话ID
    const activeConversationId = ref('conversation1');

    // 所有对话列表
    const conversations = reactive([
        {
            id: 'conversation1',
            title: '新对话',
            timestamp: Date.now(),
            messages: []
        }
    ]);

    // 当前对话的消息列表
    const currentMessages = reactive([]);

    // 加载状态
    const loading = ref(false);

    // 提示词集合
    const prompts = reactive([
        { id: 'p1', title: '自我介绍', content: '请介绍一下你自己' },
        { id: 'p2', title: '写一篇文章', content: '请写一篇关于人工智能的文章' },
        { id: 'p3', title: '代码解释', content: '请解释以下代码的功能：' },
        { id: 'p4', title: '数据分析', content: '请分析以下数据并给出结论：' }
    ]);

    // 切换对话
    const switchConversation = (conversationId) => {
        activeConversationId.value = conversationId;
        const conversation = conversations.find(c => c.id === conversationId);
        if (conversation) {
            currentMessages.length = 0;
            conversation.messages.forEach(msg => currentMessages.push(msg));
        }
    };

    // 创建新对话
    const createNewConversation = () => {
        const newId = `conversation${conversations.length + 1}`;
        const newConversation = {
            id: newId,
            title: '新对话',
            timestamp: Date.now(),
            messages: []
        };
        conversations.push(newConversation);
        switchConversation(newId);
        return newId;
    };

    // 发送消息
    const sendMessage = async (content) => {
        if (!content.trim()) return;

        // 添加用户消息
        const userMessage = {
            id: `msg-${Date.now()}`,
            role: 'user',
            content,
            timestamp: Date.now()
        };

        currentMessages.push(userMessage);

        // 更新当前对话的消息
        const currentConversation = conversations.find(c => c.id === activeConversationId.value);
        if (currentConversation) {
            currentConversation.messages.push(userMessage);

            // 如果是第一条消息，更新对话标题
            if (currentConversation.messages.length === 1) {
                currentConversation.title = content.length > 20 ? content.substring(0, 20) + '...' : content;
            }
        }

        // 模拟AI响应
        loading.value = true;
        try {
            // 这里将来会替换为实际的API调用
            const aiResponse = await simulateAIResponse(content);

            const assistantMessage = {
                id: `msg-${Date.now()}`,
                role: 'assistant',
                content: aiResponse,
                timestamp: Date.now()
            };

            currentMessages.push(assistantMessage);

            // 更新当前对话的消息
            if (currentConversation) {
                currentConversation.messages.push(assistantMessage);
            }
        } finally {
            loading.value = false;
        }
    };

    // 模拟AI响应（临时函数，将来会替换为实际API调用）
    const simulateAIResponse = async (userMessage) => {
        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, 1000));
        return `这是对"${userMessage}"的模拟回复。实际项目中，这里将调用API获取真实的AI回复。`;
    };

    // 使用API调用AI服务
    const callAIAPI = async (messages) => {
        const apiKey = 'sk-qjypedbqdcauirctshsyvpucfaxalvolhkcabwqeiusofimn';
        const options = {
            method: 'POST',
            headers: {
                Authorization: `Bearer ${apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: 'Qwen/Qwen3-8B',
                messages: messages.map(msg => ({
                    role: msg.role,
                    content: msg.content
                })),
                stream: false,
                max_tokens: 512,
                enable_thinking: false,
                thinking_budget: 4096,
                min_p: 0.05,
                stop: null,
                temperature: 0.7,
                top_p: 0.7,
                top_k: 50,
                frequency_penalty: 0.5,
                n: 1,
                response_format: { type: 'text' }
            })
        };

        try {
            const response = await fetch('https://api.siliconflow.cn/v1/chat/completions', options);
            const data = await response.json();
            return data.choices[0].message.content;
        } catch (error) {
            console.error('AI API调用失败:', error);
            return '抱歉，AI服务暂时不可用。请稍后再试。';
        }
    };

    return {
        activeConversationId,
        conversations,
        currentMessages,
        loading,
        prompts,
        switchConversation,
        createNewConversation,
        sendMessage,
        callAIAPI
    };
}; 