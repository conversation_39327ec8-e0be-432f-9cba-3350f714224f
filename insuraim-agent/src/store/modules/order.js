import { defineStore } from 'pinia'

/**
 * @description 险种类型
 */
export const ProductTypeEnum = Object.freeze({
  // 危疾
  CRITICAL_ILLNESS: 'CRITICAL_ILLNESS',
  // 医疗
  MEDICAL: 'MEDICAL',
  // 万用寿险
  UNIVERSAL_LIFE: 'UNIVERSAL_LIFE',
  // 投资相连
  INVESTMENT_LINKED: 'INVESTMENT_LINKED',
  // 人寿
  LIFE: 'LIFE',
  // 意外
  ACCIDENT: 'ACCIDENT',
  // 定期寿险
  TERM_LIFE: 'TERM_LIFE',
  // 年金
  ANNUITY: 'ANNUITY',
  // 其他
  OTHERS: 'OTHERS',
  // 强积金
  MPF: 'MPF',
  // 一般保险
  GI: 'GI',
  // 指数型万用寿险
  INDEX_UNIVERSAL_LIFE: 'INDEX_UNIVERSAL_LIFE',
  // 可变万用寿险
  VARIABLE_UNIVERSAL_LIFE: 'VARIABLE_UNIVERSAL_LIFE',
})

/**
 * @description 地区枚举
 */
export const AreaEnum = Object.freeze({
  //中国大陆
  MAINLAND: '中国大陆',
  // 香港
  HONGKONG: '香港',
  // 澳门
  MACAO: '澳门',
  // 新加坡
  SINGAPORE: '新加坡',
  // 百慕大
  BERMUDA: '百慕大',
  // 美国
  USA: '美国',
})

/**
 * @description 订单状态枚举
 */
export const OrderStatusEnum = Object.freeze({
  // 已预约
  APPOINTMENT: 'APPOINTMENT',

  // 待缴费
  WAIT_PAYMENT: 'WAIT_PAYMENT',

  // 冷静期
  QUIET_PERIOD: 'QUIET_PERIOD',

  // 生效中
  IN_EFFECT: 'IN_EFFECT',

  // 已失效
  EXPIRED: 'EXPIRED'
})

/**
 * @description 保单状态枚举
 */
export const PolicyStatusEnum = Object.freeze({
  // 预约状态
  APPOINTMENT: 'APPOINTMENT',

  // 签单预约确认
  SIGN_APPOINTMENT: 'SIGN_APPOINTMENT',

  // 签单
  SIGN: 'SIGN',

  // 保费缴纳指引
  PAYMENT_GUIDE: 'PAYMENT_GUIDE',

  // 保单交费记录
  PAYMENT_RECORD: 'PAYMENT_RECORD',

  // 保单生效
  EFFECTIVE: 'EFFECTIVE',

  // 保单合同寄送
  CONTRACT_SENT: 'CONTRACT_SENT',

  // 保单签收
  SIGNATURE: 'SIGNATURE',

  // 保单冷静期到期
  QUIET_PERIOD_EXPIRED: 'QUIET_PERIOD_EXPIRED'
})

/**
 * @description 保单记录状态枚举
 */
export const PolicyRecordStatusEnum = Object.freeze({
  // 已完成
  COMPLETED: 'COMPLETED',

  // 待确认
  PENDING_COMPLETION: 'PENDING_COMPLETION',

  // 待处理
  PENDING: 'PENDING'
})

/**
 * @description 订单状态与保单状态的映射关系
 */
export const OrderToPolicyStatusMap = Object.freeze({
  [OrderStatusEnum.APPOINTMENT]: [
    PolicyStatusEnum.APPOINTMENT,
    PolicyStatusEnum.SIGN_APPOINTMENT
  ],
  [OrderStatusEnum.WAIT_PAYMENT]: [
    PolicyStatusEnum.SIGN,
    PolicyStatusEnum.PAYMENT_GUIDE,
    PolicyStatusEnum.PAYMENT_RECORD
  ],
  [OrderStatusEnum.QUIET_PERIOD]: [
    PolicyStatusEnum.EFFECTIVE,
    PolicyStatusEnum.CONTRACT_SENT,
    PolicyStatusEnum.SIGNATURE,
    PolicyStatusEnum.QUIET_PERIOD_EXPIRED
  ],
  [OrderStatusEnum.IN_EFFECT]: [],
  [OrderStatusEnum.EXPIRED]: []
})

// 定义订单状态仓库
export const useOrderStore = defineStore('order', {
  // 状态
  state: () => {
    return {
      currentOrder: null,
      orderList: [],
      orderStatus: null,
      policyStatus: null,
      policyRecordStatus: null
    }
  },

  // 计算属性
  getters: {
    getCurrentOrder: (state) => state.currentOrder,
    getOrderList: (state) => state.orderList,
    getOrderStatus: (state) => state.orderStatus,
    getPolicyStatus: (state) => state.policyStatus,
    getPolicyRecordStatus: (state) => state.policyRecordStatus
  },

  // 方法
  actions: {
    setCurrentOrder(order) {
      this.currentOrder = order
    },

    setOrderList(list) {
      this.orderList = list
    },

    setOrderStatus(status) {
      if (!Object.values(OrderStatusEnum).includes(status)) {
        console.error('无效的订单状态:', status)
        return
      }
      this.orderStatus = status
    },

    setPolicyStatus(status) {
      if (!Object.values(PolicyStatusEnum).includes(status)) {
        console.error('无效的保单状态:', status)
        return
      }
      this.policyStatus = status
    },

    setPolicyRecordStatus(status) {
      if (!Object.values(PolicyRecordStatusEnum).includes(status)) {
        console.error('无效的保单记录状态:', status)
        return
      }
      this.policyRecordStatus = status
    },

    /**
     * 根据保单状态自动设置订单状态
     * @param {string} policyStatus 保单状态
     */
    updateOrderStatusByPolicyStatus(policyStatus) {
      for (const [orderStatus, policyStatuses] of Object.entries(OrderToPolicyStatusMap)) {
        if (policyStatuses.includes(policyStatus)) {
          this.orderStatus = orderStatus
          break
        }
      }
    }
  }
})
