// 产品相关的store
import { defineStore } from "pinia";
import { ref, computed } from "vue";
import { companyAPI, productAPI } from "@/api";

export const useProductStore = defineStore("product", () => {
  // 状态
  const companyList = ref([]);
  const formattedCompanyList = ref(
    JSON.parse(localStorage.getItem("formattedCompanyList")) || []
  );
  const productCategoryList = ref([]);

  // 获取产品分类列表
  async function getProductCategoryList() {
    const res = await productAPI.getProductCategoryList();
    productCategoryList.value = res;
  }

  // 获取原始公司列表
  async function getCompanyListOrigin() {
    const res = await companyAPI.getCompanyPage();
    companyList.value = res;
    return companyList.value;
  }

  // 获取公司列表
  async function getCompanyList() {
    const res = await companyAPI.getCompanyPage();
    companyList.value = res;

    // 格式化数据为a-select组件所需的格式
    formattedCompanyList.value = companyList.value.map((company) => ({
      value: company.name,
      label: company.name,
    }));
    // // 存入localStorage
    // localStorage.setItem(
    //   "formattedCompanyList",
    //   JSON.stringify(formattedCompanyList.value)
    // );

    return companyList.value;
  }

  // 计算属性：获取格式化后的公司列表
  const getFormattedCompanyList = computed(() => formattedCompanyList.value);

  return {
    companyList,
    formattedCompanyList,
    getCompanyListOrigin,
    getCompanyList,
    productCategoryList,
    getProductCategoryList,
    getFormattedCompanyList,
  };
});
