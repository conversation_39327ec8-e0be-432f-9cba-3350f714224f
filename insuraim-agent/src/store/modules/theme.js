import { defineStore } from 'pinia'
import { ref, watch } from 'vue'

// 定义主题类型
export const THEME_MODE = {
    LIGHT: 'light',
    DARK: 'dark',
}

// 获取本地存储的主题，如果没有则使用默认的亮色主题
const getStoredTheme = () => {
    const storedTheme = localStorage.getItem('theme')
    return storedTheme && Object.values(THEME_MODE).includes(storedTheme)
        ? storedTheme
        : THEME_MODE.LIGHT
}

// 定义主题store
export const useThemeStore = defineStore('theme', () => {
    // 当前主题模式
    const currentTheme = ref(getStoredTheme())

    // 判断是否为暗色主题
    const isDarkTheme = ref(currentTheme.value === THEME_MODE.DARK)

    // 监听主题变化，更新DOM类名
    watch(currentTheme, (newTheme) => {
        // 保存到本地存储
        localStorage.setItem('theme', newTheme)

        // 更新isDarkTheme值
        isDarkTheme.value = newTheme === THEME_MODE.DARK

        // 更新文档根元素的类名
        const htmlEl = document.documentElement
        if (newTheme === THEME_MODE.DARK) {
            htmlEl.classList.add('dark-theme')
            htmlEl.classList.remove('light-theme')
        } else {
            htmlEl.classList.add('light-theme')
            htmlEl.classList.remove('dark-theme')
        }
    }, { immediate: true })

    // 切换主题
    const toggleTheme = () => {
        currentTheme.value = currentTheme.value === THEME_MODE.LIGHT
            ? THEME_MODE.DARK
            : THEME_MODE.LIGHT
    }

    // 设置指定主题
    const setTheme = (theme) => {
        if (Object.values(THEME_MODE).includes(theme)) {
            currentTheme.value = theme
        }
    }

    return {
        currentTheme,
        isDarkTheme,
        toggleTheme,
        setTheme,
    }
}) 