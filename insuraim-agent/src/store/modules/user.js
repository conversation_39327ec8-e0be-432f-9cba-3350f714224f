import { defineStore } from 'pinia'

// 定义用户状态仓库
export const useUserStore = defineStore('user', {
  // 状态
  state: () => {
    // 尝试从localStorage中获取用户信息
    let userInfo = null;
    try {
      const userInfoStr = localStorage.getItem('userInfo');
      if (userInfoStr) {
        userInfo = JSON.parse(userInfoStr);
      }
    } catch (error) {
      console.error('获取用户信息失败', error);
    }

    return {
      userInfo,
      token: localStorage.getItem('token') || '',
      isLoggedIn: !!localStorage.getItem('token')
    }
  },

  // 计算属性
  getters: {
    getUserInfo: (state) => state.userInfo,
    getToken: (state) => state.token,
    isAuthenticated: (state) => state.isLoggedIn
  },

  // 方法
  actions: {
    setUserInfo(userInfo) {
      this.userInfo = userInfo
      // 同时更新localStorage
      localStorage.setItem('userInfo', JSON.stringify(userInfo))
    },

    setToken(token) {
      this.token = token
      localStorage.setItem('token', token)
      this.isLoggedIn = true
    },

    logout() {
      this.userInfo = null
      this.token = ''
      this.isLoggedIn = false
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
    }
  }
}) 