@import "tailwindcss";

/* 导入PingFang SC字体 */
@font-face {
  font-family: 'PingFang SC';
  src: url('./assets/fonts/PingFangSC-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'PingFang SC';
  src: url('./assets/fonts/PingFangSC-Medium.otf') format('opentype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'PingFang SC';
  src: url('./assets/fonts/PingFangSC-Semibold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'PingFang SC';
  src: url('./assets/fonts/PingFangSC-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

/* 导入AlimamaShuHeiTi-Bold字体 */
@font-face {
  font-family: 'AlimamaShuHeiTi-Bold';
  src: url('./assets/fonts/AlimamaShuHeiTi-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

body,
* {
  font-family: 'PingFang SC', sans-serif !important;
}

/* 修复a-button组件中图标和文字不能对齐的问题 */
.ant-btn {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.ant-btn .anticon,
.ant-btn .iconify {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  vertical-align: middle !important;
  line-height: 1 !important;
}

.ant-btn-icon-only {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 自定义滚动条样式 - 适配主题 */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background-color: var(--bg-secondary);
  border-radius: var(--radius-md);
}

::-webkit-scrollbar-thumb {
  background-color: var(--neutral-300);
  border-radius: var(--radius-md);
  border: 2px solid var(--bg-secondary);
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--neutral-400);
}

/* 暗色主题滚动条适配 */
.dark-theme ::-webkit-scrollbar-track {
  background-color: var(--bg-secondary);
}

.dark-theme ::-webkit-scrollbar-thumb {
  background-color: var(--neutral-600);
  border: 2px solid var(--bg-secondary);
}

.dark-theme ::-webkit-scrollbar-thumb:hover {
  background-color: var(--neutral-500);
}

/* Firefox滚动条支持 */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--neutral-300) var(--bg-secondary);
}

.dark-theme * {
  scrollbar-color: var(--neutral-600) var(--bg-secondary);
}

.dark-theme .ant-modal-content {
  background-color: #111827 !important;
}

.dark-theme .ant-modal-header {
  background-color: #111827 !important;
}

.dark-theme .ant-modal-body {
  background-color: #111827 !important;
}

.ant-modal-content {
  background-color: #fff !important;
}

.ant-modal-header {
  background-color: #fff !important;
}