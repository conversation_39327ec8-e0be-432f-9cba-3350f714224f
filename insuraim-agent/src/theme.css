:root {
    /* ===== 默认亮色主题 ===== */

    /* 主色调 */
    --primary: #2563eb;
    --primary-rgb: 37, 99, 235;
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;
    --primary-950: #172554;

    /* 次要色调 - 紫色系 */
    --secondary-50: #f5f3ff;
    --secondary-100: #ede9fe;
    --secondary-200: #ddd6fe;
    --secondary-300: #c4b5fd;
    --secondary-400: #a78bfa;
    --secondary-500: #8b5cf6;
    --secondary-600: #7c3aed;
    --secondary-700: #6d28d9;
    --secondary-800: #5b21b6;
    --secondary-900: #4c1d95;
    --secondary-950: #2e1065;

    /* 成功色调 */
    --success-50: #f0fdf4;
    --success-100: #dcfce7;
    --success-200: #bbf7d0;
    --success-300: #86efac;
    --success-400: #4ade80;
    --success-500: #22c55e;
    --success-600: #16a34a;
    --success-700: #15803d;
    --success-800: #166534;
    --success-900: #14532d;
    --success-950: #052e16;

    /* 警告色调 */
    --warning-50: #fffbeb;
    --warning-100: #fef3c7;
    --warning-200: #fde68a;
    --warning-300: #fcd34d;
    --warning-400: #fbbf24;
    --warning-500: #f59e0b;
    --warning-600: #d97706;
    --warning-700: #b45309;
    --warning-800: #92400e;
    --warning-900: #78350f;
    --warning-950: #451a03;

    /* 危险色调 */
    --danger-50: #fef2f2;
    --danger-100: #fee2e2;
    --danger-200: #fecaca;
    --danger-300: #fca5a5;
    --danger-400: #f87171;
    --danger-500: #ef4444;
    --danger-600: #dc2626;
    --danger-700: #b91c1c;
    --danger-800: #991b1b;
    --danger-900: #7f1d1d;
    --danger-950: #450a0a;

    /* 中性色调 */
    --neutral-50: #f9fafb;
    --neutral-100: #f3f4f6;
    --neutral-200: #e5e7eb;
    --neutral-300: #d1d5db;
    --neutral-400: #9ca3af;
    --neutral-500: #6b7280;
    --neutral-600: #4b5563;
    --neutral-700: #374151;
    --neutral-800: #1f2937;
    --neutral-900: #111827;
    --neutral-950: #030712;

    /* 背景色 */
    --bg-primary: #f3f6fe;
    --bg-secondary: #ffffff;
    --bg-tertiary: #f8fafc;
    --bg-accent: #eff6ff;

    /* 文本色 */
    --text-primary: #111827;
    --text-secondary: #4b5563;
    --text-tertiary: #6b7280;
    --text-accent: #2563eb;
    --text-light: #ffffff;

    /* 边框色 */
    --border-light: #e5e7eb;
    --border-medium: #d1d5db;
    --border-dark: #9ca3af;

    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

    /* 圆角 */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;

    /* 其他 */
    --header-height: 4rem;
    --sidebar-width: 16rem;
    --sidebar-collapsed-width: 4.5rem;
    --z-index-navbar: 40;
    --z-index-sidebar: 30;
    --z-index-dropdown: 50;
    --z-index-modal: 100;
}

/* 暗色主题 */
.dark-theme {
    /* 背景色 */
    --bg-primary: #111827;
    --bg-secondary: #1f2937;
    --bg-tertiary: #1a1f2d;
    --bg-accent: #172554;

    /* 文本色 */
    --text-primary: #f9fafb;
    --text-secondary: #e5e7eb;
    --text-tertiary: #9ca3af;
    --text-accent: #3b82f6;
    --text-light: #f9fafb;

    /* 边框色 */
    --border-light: #374151;
    --border-medium: #4b5563;
    --border-dark: #6b7280;

    /* 阴影 - 暗色模式下阴影需要更明显 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

/* 亮色主题（可以显式设置，与:root相同） */
.light-theme {
    /* 背景色 */
    --bg-primary: #f3f6fe;
    --bg-secondary: #ffffff;
    --bg-tertiary: #f8fafc;
    --bg-accent: #eff6ff;

    /* 文本色 */
    --text-primary: #111827;
    --text-secondary: #4b5563;
    --text-tertiary: #6b7280;
    --text-accent: #2563eb;
    --text-light: #ffffff;

    /* 边框色 */
    --border-light: #e5e7eb;
    --border-medium: #d1d5db;
    --border-dark: #9ca3af;

    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 应用辅助类 */
.bg-primary {
    background-color: var(--bg-primary);
}

.bg-secondary {
    background-color: var(--bg-secondary);
}

.bg-tertiary {
    background-color: var(--bg-tertiary);
}

.bg-accent {
    background-color: var(--bg-accent);
}

.text-primary {
    color: var(--text-primary);
}

.text-secondary {
    color: var(--text-secondary);
}

.text-tertiary {
    color: var(--text-tertiary);
}

.text-accent {
    color: var(--text-accent);
}

.text-light {
    color: var(--text-light);
}

.border-light {
    border-color: var(--border-light);
}

.border-medium {
    border-color: var(--border-medium);
}

.border-dark {
    border-color: var(--border-dark);
}

.shadow-sm {
    box-shadow: var(--shadow-sm);
}

.shadow-md {
    box-shadow: var(--shadow-md);
}

.shadow-lg {
    box-shadow: var(--shadow-lg);
}

/* 动画过渡 */
.theme-transition {
    transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease;
}

.dark-theme .bg-white {
    background-color: var(--bg-primary);
}

h1,
h2,
h3,
h4,
h5,
h6 {
    color: var(--text-primary);
}