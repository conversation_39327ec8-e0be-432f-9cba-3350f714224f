/**
 * 简体中文与繁体中文转换工具
 * 使用opencc-js库实现
 */
import { Converter } from 'opencc-js';

// 创建简体到繁体的转换器（中国大陆 => 香港繁体）
const s2hkConverter = Converter({ from: 'cn', to: 'hk' });

// 创建繁体到简体的转换器（香港繁体 => 中国大陆）
const hk2sConverter = Converter({ from: 'hk', to: 'cn' });


/**
 * 将简体中文转换为繁体中文（香港）
 * @param {string} text 要转换的文本
 * @returns {string} 转换后的繁体文本
 */
export const toTraditional = (text) => {
    if (!text) return '';
    return s2hkConverter(text);
};

/**
 * 将繁体中文（香港）转换为简体中文
 * @param {string} text 要转换的文本
 * @returns {string} 转换后的简体文本
 */
export const toSimplified = (text) => {
    if (!text) return '';
    return hk2sConverter(text);
};

// 判断字符串是否包含繁体
export const isTraditional = (text) => {
    if (!text) return false;
    // 通过比较原文和转换为简体后的文本是否一致来判断
    // 如果不一致，说明文本中包含了繁体字符
    return text !== toSimplified(text);
};
export default {
    toTraditional,
    toSimplified
}; 