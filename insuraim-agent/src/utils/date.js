/**
 * 日期格式化工具函数
 */
import dayjs from 'dayjs';

/**
 * 格式化日期
 * @param {Date|string|number} date - 需要格式化的日期，可以是Date对象、时间戳或日期字符串
 * @param {string} format - 格式化模式，例如：'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的日期字符串
 */
export const formatDate = (date, format = 'YYYY-MM-DD') => {
  if (!date) return '';

  try {
    return dayjs(date).format(format);
  } catch (error) {
    console.error('日期格式化错误:', error);
    return '';
  }
};

/**
 * 获取当前日期
 * @param {string} format - 格式化模式
 * @returns {string} 格式化后的当前日期
 */
export const getCurrentDate = (format = 'YYYY-MM-DD') => {
  return formatDate(new Date(), format);
};

/**
 * 日期相加减
 * @param {Date|string|number} date - 基准日期
 * @param {number} num - 要加减的数值
 * @param {string} unit - 单位（day, month, year, hour, minute, second）
 * @param {string} format - 返回格式
 * @returns {string} 计算后的日期字符串
 */
export const addDate = (date, num, unit = 'day', format = 'YYYY-MM-DD') => {
  if (!date) return '';

  try {
    return dayjs(date).add(num, unit).format(format);
  } catch (error) {
    console.error('日期计算错误:', error);
    return '';
  }
};

/**
 * 计算两个日期之间的差值
 * @param {Date|string|number} date1 - 日期1
 * @param {Date|string|number} date2 - 日期2
 * @param {string} unit - 单位（day, month, year等）
 * @returns {number} 差值
 */
export const diffDate = (date1, date2, unit = 'day') => {
  if (!date1 || !date2) return 0;

  try {
    return dayjs(date1).diff(dayjs(date2), unit);
  } catch (error) {
    console.error('日期差值计算错误:', error);
    return 0;
  }
};

/**
 * 检查日期是否有效  
 * @param {Date|string|number} date - 需要检查的日期
 * @returns {boolean} 是否有效
 */
export const isValidDate = (date) => {
  return dayjs(date).isValid();
};


/**
 * 格式化时间为 HH:mm 格式
 * @param {number} timestamp - Unix时间戳（秒）
 * @returns {string} 格式化后的时间字符串
 */
export const formatTimeHM = (timestamp) => {
  const date = new Date(timestamp * 1000);
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  return `${hours}:${minutes}`;
};

/**
 * 格式化快讯日期为中文格式
 * @param {number} timestamp - Unix时间戳（秒）
 * @returns {string} 格式化后的日期字符串（年月日 星期几）
 */
export const formatFlashNewsDate = (timestamp) => {
  const date = new Date(timestamp * 1000);
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const weekday = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'][date.getDay()];
  return `${year}年${month}月${day}日 ${weekday}`;
}; 