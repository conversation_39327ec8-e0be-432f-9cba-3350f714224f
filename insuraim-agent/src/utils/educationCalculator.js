// 计算教育金规划
export function calculateEducationFund(educationData) {
    const {
        childAge,
        targetAge,
        educationType,
        currentSavings,
        expectedReturnRate,
        inflationRate,
        annualEducationCost,
        yearsOfEducation
    } = educationData;

    // 验证输入
    if (childAge == null || childAge < 0 || // 或者 childAge <=0 如果不允许0岁
        targetAge == null || targetAge <= childAge || // targetAge 必须大于 childAge
        !educationType || // 假设 educationType 是必需的非空字符串
        currentSavings == null || currentSavings < 0 || // 允许现有储蓄为0
        expectedReturnRate == null || expectedReturnRate < 0 || // 允许回报率为0
        inflationRate == null || inflationRate < 0 || // 允许通胀率为0
        annualEducationCost == null || annualEducationCost <= 0 || // 年费用必须大于0
        yearsOfEducation == null || yearsOfEducation <= 0) { // 教育年限必须大于0
        throw new Error('请填写所有教育金规划信息，并确保数值有效（年龄/费用/年限 > 0，储蓄/利率 >= 0）。');
    }
    // 原有的 childAge >= targetAge 检查依然有效且重要
    if (childAge >= targetAge) {
        throw new Error('当前年龄必须小于目标年龄');
    }

    // 计算教育金需求
    const yearsToTarget = targetAge - childAge;
    const inflationRateDecimal = inflationRate / 100;
    const expectedReturnRateDecimal = expectedReturnRate / 100;

    // 计算未来教育总成本（考虑通货膨胀）
    const futureAnnualCost = annualEducationCost * Math.pow(1 + inflationRateDecimal, yearsToTarget);

    let actualTotalEducationCost = 0;
    let costForYear = futureAnnualCost;
    for (let i = 0; i < yearsOfEducation; i++) {
    actualTotalEducationCost += costForYear;
    if (i < yearsOfEducation - 1) { // 为下一年成本充气，最后一年之后不需要
        costForYear *= (1 + inflationRateDecimal);
    }
    }
// const totalEducationCost = actualTotalEducationCost; // 使用这个更精确的值


    // 计算现有储蓄的未来价值
    const futureValueOfCurrentSavings = currentSavings * Math.pow(1 + expectedReturnRateDecimal, yearsToTarget);

    // 计算教育金缺口
    const educationGap = actualTotalEducationCost  - futureValueOfCurrentSavings;

    // 计算每月需要储蓄金额
    const monthlyExpectedRateDecimal = expectedReturnRateDecimal / 12;
    const numberOfSavingPeriods = yearsToTarget * 12;

    let monthlySavings;
    if (educationGap <= 0) {
        monthlySavings = 0;
    } else if (numberOfSavingPeriods <= 0) {
        monthlySavings = educationGap;
    } else if (Math.abs(monthlyExpectedRateDecimal) < 0.0000001) {
        monthlySavings = educationGap / numberOfSavingPeriods;
    } else {
        const denominator = Math.pow(1 + monthlyExpectedRateDecimal, numberOfSavingPeriods) - 1;
        if (denominator === Infinity) {
            monthlySavings = 0;
        } else if (Math.abs(denominator) < 0.0000001) {
            monthlySavings = educationGap / numberOfSavingPeriods;
        } else {
            monthlySavings = (educationGap * monthlyExpectedRateDecimal) / denominator;
        }
    }

    // 确保每月储蓄不为负
    if (monthlySavings < 0) {
        monthlySavings = 0;
    }

    // 根据教育类型和时间确定投资策略
    let investmentStrategy = '';
    if (yearsToTarget > 10) {
        investmentStrategy = '建议采用积极型投资组合：60%股票，30%债券，10%现金';
    } else if (yearsToTarget > 5) {
        investmentStrategy = '建议采用平衡型投资组合：40%股票，50%债券，10%现金';
    } else {
        investmentStrategy = '建议采用保守型投资组合：20%股票，70%债券，10%现金';
    }
    const firstYearEducationCost = futureAnnualCost * Math.pow(1 + inflationRateDecimal, yearsToTarget);

    // 返回前确保数值有效
    const formatNumber = (num) => (isNaN(num) || !isFinite(num)) ? '计算错误' : Math.round(num).toLocaleString();

    return {
        totalEducationCost: formatNumber(actualTotalEducationCost ),
        monthlySavings: formatNumber(monthlySavings),
        educationGap: formatNumber(educationGap),
        investmentStrategy,
        firstYearEducationCost: formatNumber(firstYearEducationCost),
        futureValueOfCurrentSavings: formatNumber(futureValueOfCurrentSavings)
    };
} 