import ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';
import { log } from 'sockjs-client/dist/sockjs';

/**
 * @description 导出excel
 * @param {Array} data 数据
 * @param {Array} columns 列配置
 * @param {String} filename 文件名
 * @param {Object} options 配置项
 * @param {String} options.sheetName 工作表名称
 * @param {Boolean} options.autoWidth 是否自动调整列宽
 * @param {Boolean} options.showHeader 是否显示表头
 * @param {Object} options.headerStyle 表头样式
 * @param {Object} options.cellStyle 单元格样式
 * @param {Boolean} options.multipleSheets 是否包含多个工作表
 * @param {Array} options.sheetsData 多个工作表的数据 [{sheetName, data}]
 * @param {Object} options.columnWidths 自定义列宽设置，格式为 {dataIndex: width}
 * @param {String} options.productName 产品名称，用作表格标题
 * @param {Object} options.productInfo 产品信息，用于添加案例参考
 * @param {String} options.theme 当前主题，用于案例参考的样式
 */
export const exportExcel = async (
    data,
    columns,
    filename = 'export.xlsx',
    options = {}
) => {
    /**
     * 根据主题获取对应的颜色
     * @param {String} theme 主题名称
     * @returns {Object} 主题颜色对象
     */
    const getThemeColors = (theme) => {
        switch (theme) {
            case 'default':
                return {
                    headerBg: '374151',
                    headerText: 'FFFFFF',
                    normalRowBg: 'FFFFFF',
                    highlightRowBg: '91969E',
                    textColor: '303540',
                    titleBg: '374151',
                    infoBg: 'E9E9E9'
                };
            case 'primary':
                return {
                    headerBg: '2563EB',
                    headerText: 'FFFFFF',
                    normalRowBg: 'C7D3F9',
                    highlightRowBg: '89A5F3',
                    textColor: '303540',
                    titleBg: '2563EB',
                    infoBg: 'E5EDFF'
                };
            case 'success':
                return {
                    headerBg: '16A34A',
                    headerText: 'FFFFFF',
                    normalRowBg: 'C5E4CE',
                    highlightRowBg: '87C99B',
                    textColor: '303540',
                    titleBg: '16A34A',
                    infoBg: 'E7F7ED'
                };
            case 'warning':
                return {
                    headerBg: 'D97707',
                    headerText: 'FFFFFF',
                    normalRowBg: 'F4D9C2',
                    highlightRowBg: 'E9B07F',
                    textColor: '303540',
                    titleBg: 'D97707',
                    infoBg: 'FFF4E6'
                };
            case 'danger':
                return {
                    headerBg: 'DC2625',
                    headerText: 'FFFFFF',
                    normalRowBg: 'F5C4C7',
                    highlightRowBg: 'E78389',
                    textColor: '303540',
                    titleBg: 'DC2625',
                    infoBg: 'FFEBEB'
                };
            default:
                return {
                    headerBg: '374151',
                    headerText: 'FFFFFF',
                    normalRowBg: 'FFFFFF',
                    highlightRowBg: '91969E',
                    textColor: '303540',
                    titleBg: '374151',
                    infoBg: 'E9E9E9'
                };
        }
    };

    // 默认配置
    const defaultOptions = {
        sheetName: 'Sheet1',
        autoWidth: true,
        showHeader: true,
        multipleSheets: false,
        headerStyle: {
            font: { bold: true, color: { argb: '000000' } },
            fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'E6F7FF' } },
            alignment: { vertical: 'middle', horizontal: 'center' },
            border: {
                top: { style: 'thin', color: { argb: 'D9D9D9' } },
                left: { style: 'thin', color: { argb: 'D9D9D9' } },
                bottom: { style: 'thin', color: { argb: 'D9D9D9' } },
                right: { style: 'thin', color: { argb: 'D9D9D9' } }
            }
        },
        cellStyle: {
            alignment: { vertical: 'middle' },
            border: {
                top: { style: 'thin', color: { argb: 'D9D9D9' } },
                left: { style: 'thin', color: { argb: 'D9D9D9' } },
                bottom: { style: 'thin', color: { argb: 'D9D9D9' } },
                right: { style: 'thin', color: { argb: 'D9D9D9' } }
            }
        },
        columnWidths: {} // 新增自定义列宽设置
    };

    // 合并配置
    const mergedOptions = { ...defaultOptions, ...options };

    // 创建工作簿
    const workbook = new ExcelJS.Workbook();

    // 处理列定义
    const excelColumns = columns.map(col => {
        // 如果col.title为收益演算，则不用在excel中显示
        if (col.title === '收益演算') {
            return null;
        }
        // 检查是否有自定义列宽设置
        let columnWidth = 15; // 默认列宽

        // 优先使用自定义列宽设置
        if (mergedOptions.columnWidths && mergedOptions.columnWidths[col.dataIndex]) {
            columnWidth = mergedOptions.columnWidths[col.dataIndex];
        }
        // 其次使用列配置中的宽度
        else if (col.width) {
            columnWidth = col.width / 8; // 将像素值转换为Excel列宽
        }

        // 考虑表头文本长度，确保列宽至少能容纳表头文本
        // 中文字符通常需要更多空间，所以乘以1.5作为估计
        const headerLength = col.title ? col.title.length * 1.5 : 0;
        columnWidth = Math.max(columnWidth, headerLength);

        return {
            header: col.title,
            key: col.dataIndex,
            width: columnWidth
        };
    });

    // 处理多工作表导出
    if (mergedOptions.multipleSheets && Array.isArray(mergedOptions.sheetsData)) {
        // 获取主题颜色
        const themeColors = getThemeColors(mergedOptions.theme || 'default');

        // 遍历每个工作表数据
        for (const sheetData of mergedOptions.sheetsData) {
            if (!sheetData.sheetName || !Array.isArray(sheetData.data)) continue;

            // 创建工作表 - 先不设置列，这样不会自动添加列标题
            const worksheet = workbook.addWorksheet(sheetData.sheetName);

            // 添加产品信息和案例参考（如果有）
            let startRow = 1;
            console.log('mergedOptions.productName', mergedOptions.productName);
            console.log('mergedOptions.productInfo', mergedOptions.productInfo);
            if (mergedOptions.productName || mergedOptions.productInfo) {
                console.log('mergedOptions.productName', mergedOptions.productName);
                console.log('mergedOptions.productInfo', mergedOptions.productInfo);

                // 添加产品名称作为标题（大标题，横跨整个表格）
                if (mergedOptions.productName) {
                    // 创建一个新的行用于产品名称标题
                    const titleRow = worksheet.addRow([mergedOptions.productName]);
                    titleRow.height = 40; // 增加高度，使标题更显眼
                    titleRow.font = { size: 18, bold: true, name: 'PingFang SC', color: { argb: 'FFFFFF' } }; // 加大字号，加粗
                    titleRow.alignment = { vertical: 'middle', horizontal: 'center' };
                    titleRow.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: themeColors.titleBg } };

                    // 合并标题单元格，横跨整个表格
                    const columnCount = excelColumns.length || 1;
                    if (columnCount > 1) {
                        worksheet.mergeCells(startRow, 1, startRow, columnCount);
                    }

                    startRow++;

                    // 添加空行，使标题与内容分隔开
                    worksheet.addRow([]);
                    startRow++;
                }

                // 添加案例参考标题（二级标题）
                worksheet.addRow(['案例参考']);
                const referenceHeaderRow = worksheet.getRow(startRow);
                referenceHeaderRow.height = 35; // 增加高度
                referenceHeaderRow.font = { size: 16, bold: true, name: 'PingFang SC', color: { argb: '303540' } }; // 增加字号
                referenceHeaderRow.alignment = { vertical: 'middle', horizontal: 'left' };
                referenceHeaderRow.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: themeColors.infoBg } };

                // 合并案例参考标题单元格
                const columnCount = excelColumns.length || 1;
                if (columnCount > 1) {
                    worksheet.mergeCells(startRow, 1, startRow, columnCount);
                }

                startRow++;

                // 添加产品信息
                if (mergedOptions.productInfo) {
                    const infoData = [];
                    // 第1行信息 - 产品信息
                    const row1 = [];

                    // 添加年龄信息（如果有）
                    if (mergedOptions.productInfo.assumedAge) {
                        row1.push('年龄：' + mergedOptions.productInfo.assumedAge);
                    }
                    if (mergedOptions.productInfo.gender) {
                        row1.push('性别：' + mergedOptions.productInfo.gender);
                    }
                    if (mergedOptions.productInfo.productName) {
                        row1.push('产品：' + mergedOptions.productInfo.productName);
                    }
                    infoData.push(row1);

                    // 第2行信息
                    const row2 = [];
                    if (mergedOptions.productInfo.smoking_status) {
                        row2.push('吸烟状态：' + mergedOptions.productInfo.smoking_status);
                    }
                    if (mergedOptions.productInfo.annualPremium) {
                        row2.push('保费：' + mergedOptions.productInfo.annualPremium);
                    }
                    if (mergedOptions.productInfo.paymentMode) {
                        row2.push('缴费方式：' + mergedOptions.productInfo.paymentMode);
                    }
                    if (row2.length > 0) {
                        infoData.push(row2);
                    }

                    // 第3行信息
                    const row3 = [];
                    if (mergedOptions.productInfo.coverageTerm) {
                        row3.push('保障期限：' + mergedOptions.productInfo.coverageTerm);
                    }
                    if (mergedOptions.productInfo.premiumPaymentTerm) {
                        row3.push('缴费期限：' + mergedOptions.productInfo.premiumPaymentTerm);
                    }
                    if (mergedOptions.productInfo.policyCurrency) {
                        row3.push('保单币种：' + mergedOptions.productInfo.policyCurrency);
                    }
                    if (row3.length > 0) {
                        infoData.push(row3);
                    }

                    // 添加产品信息行
                    infoData.forEach(rowData => {
                        const row = worksheet.addRow(rowData);
                        const infoRow = worksheet.getRow(startRow);
                        infoRow.height = 30; // 增加信息行高度
                        infoRow.font = { size: 12, name: 'PingFang SC', color: { argb: '303540' } }; // 增加字体大小
                        infoRow.alignment = { vertical: 'middle', horizontal: 'left' };

                        // 合并单元格，每个信息项占用更多的列空间
                        if (columnCount > rowData.length && rowData.length > 0) {
                            const cellsPerInfo = Math.floor(columnCount / rowData.length);
                            for (let i = 0; i < rowData.length; i++) {
                                const startCol = i * cellsPerInfo + 1;
                                const endCol = (i === rowData.length - 1) ? columnCount : (startCol + cellsPerInfo - 1);
                                if (endCol > startCol) {
                                    worksheet.mergeCells(startRow, startCol, startRow, endCol);
                                }
                            }
                        }

                        startRow++;
                    });
                }

                // 添加空行
                worksheet.addRow([]);
                worksheet.getRow(startRow).height = 20; // 增加空行高度
                startRow++;
            }

            // 过滤掉null值
            const filteredExcelColumns = excelColumns.filter(col => col !== null);

            // 现在设置列定义，这样列标题会出现在产品名称和案例参考之后
            worksheet.columns = filteredExcelColumns;

            // 确保表头行存在
            if (mergedOptions.showHeader !== false) {
                // 检查是否需要手动添加表头行
                // 获取表头行
                const headerRow = worksheet.getRow(startRow);
                let needAddHeader = true;

                // 检查表头行是否已经有内容
                headerRow.eachCell((cell) => {
                    if (cell.value) {
                        needAddHeader = false;
                    }
                });

                // 如果需要添加表头行
                if (needAddHeader) {
                    // 创建表头行数据
                    const headerData = [];
                    filteredExcelColumns.forEach(col => {
                        if (col.header) {
                            headerData.push(col.header);
                        }
                    });

                    // 插入表头行
                    worksheet.addRow(headerData);

                    // 更新startRow
                    startRow++;

                    // 重新获取表头行
                    const headerRow2 = worksheet.getRow(startRow - 1);
                    headerRow2.eachCell((cell) => {
                        cell.font = {
                            ...mergedOptions.headerStyle.font,
                            color: { argb: themeColors.headerText }
                        };
                        cell.fill = {
                            type: 'pattern',
                            pattern: 'solid',
                            fgColor: { argb: themeColors.headerBg }
                        };
                        cell.alignment = mergedOptions.headerStyle.alignment;
                        cell.border = mergedOptions.headerStyle.border;
                    });
                    headerRow2.height = 25; // 表头高度
                }
            }

            // 添加数据行
            if (sheetData.data && sheetData.data.length > 0) {
                // 添加数据
                worksheet.addRows(sheetData.data);

                // 处理数据行样式
                for (let i = startRow + 1; i <= startRow + sheetData.data.length; i++) {
                    const row = worksheet.getRow(i);
                    const rowData = sheetData.data[i - startRow - 1];

                    // 检查是否需要应用高亮样式
                    const isHighlightRow = rowData._highlight && mergedOptions.enableHighlight && mergedOptions.highlightCellStyle;
                    const rowStyle = isHighlightRow ? mergedOptions.highlightCellStyle : mergedOptions.cellStyle;

                    row.eachCell((cell) => {
                        // 应用样式
                        cell.alignment = rowStyle.alignment;
                        cell.border = rowStyle.border;

                        // 应用字体样式（如果有）
                        if (rowStyle.font) {
                            cell.font = rowStyle.font;
                        }

                        // 应用背景颜色（对所有行）
                        if (rowStyle.fill) {
                            cell.fill = rowStyle.fill;
                        }

                        // 为百分比数据添加格式
                        const cellValue = cell.value;
                        if (typeof cellValue === 'string' && cellValue.endsWith('%')) {
                            cell.value = parseFloat(cellValue.replace('%', ''));
                            cell.numFmt = '0.0"%"';
                        }
                    });
                    row.height = 22; // 数据行高度
                }

                // 自动调整列宽（如果启用）
                if (mergedOptions.autoWidth) {
                    worksheet.columns.forEach((column, index) => {
                        // 如果已经设置了自定义列宽，则不进行自动调整
                        if (mergedOptions.columnWidths && mergedOptions.columnWidths[columns[index].dataIndex]) {
                            return;
                        }

                        let maxLength = 0;
                        column.eachCell({ includeEmpty: true }, (cell) => {
                            let columnLength = 0;
                            if (cell.value !== null && cell.value !== undefined) {
                                columnLength = String(cell.value).length;
                                // 对中文字符进行特殊处理
                                const chineseCharCount = String(cell.value).match(/[\u4e00-\u9fa5]/g)?.length || 0;
                                columnLength += chineseCharCount * 0.5; // 中文字符需要更多空间
                            } else {
                                columnLength = 10;
                            }

                            if (columnLength > maxLength) {
                                maxLength = columnLength;
                            }
                        });

                        // 确保列宽足够显示内容
                        column.width = Math.max(maxLength + 2, column.width || 10);
                    });
                }
            }

            // 冻结表头
            worksheet.views = [
                { state: 'frozen', xSplit: 0, ySplit: startRow, topLeftCell: `A${startRow + 1}`, activeCell: `A${startRow + 1}` }
            ];
        }
    } else {
        // 单工作表导出
        const worksheet = workbook.addWorksheet(mergedOptions.sheetName);

        // 获取主题颜色
        const themeColors = getThemeColors(mergedOptions.theme || 'default');

        // 添加产品信息和案例参考（如果有）
        let startRow = 1;
        console.log('mergedOptions.productName', mergedOptions.productName);
        console.log('mergedOptions.productInfo', mergedOptions.productInfo);
        if (mergedOptions.productName || mergedOptions.productInfo) {
            // 添加产品名称作为标题（大标题，横跨整个表格）
            if (mergedOptions.productName) {
                // 创建一个新的行用于产品名称标题
                const titleRow = worksheet.addRow([mergedOptions.productName]);
                titleRow.height = 40; // 增加高度，使标题更显眼
                titleRow.font = { size: 18, bold: true, name: 'PingFang SC', color: { argb: 'FFFFFF' } }; // 加大字号，加粗
                titleRow.alignment = { vertical: 'middle', horizontal: 'center' };
                titleRow.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: themeColors.titleBg } };

                // 合并标题单元格，横跨整个表格
                const columnCount = excelColumns.length || 1;
                if (columnCount > 1) {
                    worksheet.mergeCells(startRow, 1, startRow, columnCount);
                }

                startRow++;

                // 添加空行，使标题与内容分隔开
                worksheet.addRow([]);
                startRow++;
            }

            // 添加案例参考标题（二级标题）
            worksheet.addRow(['案例参考']);
            const referenceHeaderRow = worksheet.getRow(startRow);
            referenceHeaderRow.height = 35; // 增加高度
            referenceHeaderRow.font = { size: 16, bold: true, name: 'PingFang SC', color: { argb: '303540' } }; // 增加字号
            referenceHeaderRow.alignment = { vertical: 'middle', horizontal: 'left' };
            referenceHeaderRow.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: themeColors.infoBg } };

            // 合并案例参考标题单元格
            const columnCount = excelColumns.length || 1;
            if (columnCount > 1) {
                worksheet.mergeCells(startRow, 1, startRow, columnCount);
            }

            startRow++;

            // 添加产品信息
            if (mergedOptions.productInfo) {
                const infoData = [];
                // 第1行信息 - 产品信息
                const row1 = [];
                if (mergedOptions.productInfo.productName) {
                    row1.push(mergedOptions.productInfo.productName);
                    // 宽度设置为20
                }
                // 不再重复显示产品名称，因为已经在大标题中显示
                if (mergedOptions.productInfo.gender) {
                    row1.push('性别：' + mergedOptions.productInfo.gender);
                }
                if (mergedOptions.productInfo.assumedAge) {
                    row1.push('年龄：' + mergedOptions.productInfo.assumedAge);
                }

                infoData.push(row1);

                // 第2行信息
                const row2 = [];
                if (mergedOptions.productInfo.smoking_status) {
                    row2.push('吸烟状态：' + mergedOptions.productInfo.smoking_status);
                }
                if (mergedOptions.productInfo.annualPremium) {
                    row2.push('保费：' + mergedOptions.productInfo.annualPremium);
                }
                if (mergedOptions.productInfo.paymentMode) {
                    row2.push('缴费方式：' + mergedOptions.productInfo.paymentMode);
                }
                if (row2.length > 0) {
                    infoData.push(row2);
                }

                // 第3行信息
                const row3 = [];
                if (mergedOptions.productInfo.coverageTerm) {
                    row3.push('保障期限：' + mergedOptions.productInfo.coverageTerm);
                }
                if (mergedOptions.productInfo.premiumPaymentTerm) {
                    row3.push('缴费期限：' + mergedOptions.productInfo.premiumPaymentTerm);
                }
                if (mergedOptions.productInfo.policyCurrency) {
                    row3.push('保单币种：' + mergedOptions.productInfo.policyCurrency);
                }
                if (row3.length > 0) {
                    infoData.push(row3);
                }

                // 添加产品信息行
                infoData.forEach(rowData => {
                    worksheet.addRow(rowData);
                    const infoRow = worksheet.getRow(startRow);
                    infoRow.height = 30; // 增加信息行高度
                    infoRow.font = { size: 12, name: 'PingFang SC', color: { argb: '303540' } }; // 增加字体大小
                    infoRow.alignment = { vertical: 'middle', horizontal: 'left' };

                    // 合并单元格，每个信息项占用更多的列空间
                    if (columnCount > rowData.length && rowData.length > 0) {
                        const cellsPerInfo = Math.floor(columnCount / rowData.length);
                        for (let i = 0; i < rowData.length; i++) {
                            const startCol = i * cellsPerInfo + 1;
                            const endCol = (i === rowData.length - 1) ? columnCount : (startCol + cellsPerInfo - 1);
                            if (endCol > startCol) {
                                worksheet.mergeCells(startRow, startCol, startRow, endCol);
                            }
                        }
                    }

                    startRow++;
                });
            }

            // 添加空行
            worksheet.addRow([]);
            worksheet.getRow(startRow).height = 20; // 增加空行高度
            startRow++;
        }

        // 过滤掉null值
        const filteredExcelColumns = excelColumns.filter(col => col !== null);

        // 现在设置列定义，这样列标题会出现在产品名称和案例参考之后
        worksheet.columns = filteredExcelColumns;

        // 确保表头行存在
        if (mergedOptions.showHeader !== false) {
            // 检查是否需要手动添加表头行
            // 获取表头行
            const headerRow = worksheet.getRow(startRow);
            let needAddHeader = true;

            // 检查表头行是否已经有内容
            headerRow.eachCell((cell) => {
                if (cell.value) {
                    console.log('cell.value', cell.value);
                    needAddHeader = false;
                }
            });

            // 如果需要添加表头行
            if (needAddHeader) {
                // 创建表头行数据
                const headerData = [];
                filteredExcelColumns.forEach(col => {
                    if (col.header) {
                        headerData.push(col.header);
                    }
                });

                // 插入表头行
                worksheet.addRow(headerData);

                // 更新startRow
                startRow++;

                // 重新获取表头行
                const headerRow2 = worksheet.getRow(startRow);
                headerRow2.eachCell((cell) => {
                    cell.font = {
                        ...mergedOptions.headerStyle.font,
                        color: { argb: themeColors.headerText }
                    };
                    cell.fill = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: { argb: themeColors.headerBg }
                    };
                    cell.alignment = mergedOptions.headerStyle.alignment;
                    cell.border = mergedOptions.headerStyle.border;
                    cell.width = getColumnWidth(cell.value);
                });
                headerRow2.height = 25; // 表头高度
            }
        }

        // 添加数据行
        if (data && data.length > 0) {
            // 添加数据
            worksheet.addRows(data);

            // 处理数据行样式
            for (let i = startRow + 1; i <= startRow + data.length; i++) {
                const row = worksheet.getRow(i);
                const rowData = data[i - startRow - 1];

                // 检查是否需要应用高亮样式
                const isHighlightRow = rowData._highlight && mergedOptions.enableHighlight && mergedOptions.highlightCellStyle;
                const rowStyle = isHighlightRow ? mergedOptions.highlightCellStyle : mergedOptions.cellStyle;

                row.eachCell((cell) => {
                    // 应用样式
                    cell.alignment = rowStyle.alignment;
                    cell.border = rowStyle.border;

                    // 应用字体样式（如果有）
                    if (rowStyle.font) {
                        cell.font = rowStyle.font;
                    }

                    // 应用背景颜色（对所有行）
                    if (rowStyle.fill) {
                        cell.fill = rowStyle.fill;
                    }

                    // 为百分比数据添加格式
                    const cellValue = cell.value;
                    if (typeof cellValue === 'string' && cellValue.endsWith('%')) {
                        cell.value = parseFloat(cellValue.replace('%', ''));
                        cell.numFmt = '0.0"%"';
                    }
                });
                row.height = 22; // 数据行高度
            }
            // 获取第9行
            const row9 = worksheet.getRow(9);
            row9.font = {
                size: 14,
                bold: false,
                name: 'PingFang SC',
                autoWidth: true,
                color: { argb: 'FFFFFF' }
            };
            row9.eachCell((cell) => {
                cell.fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    autoWidth: true,
                    fgColor: { argb: themeColors.headerBg },
                    font: {
                        size: 14,
                        bold: false,
                        name: 'PingFang SC',
                        color: { argb: 'FFFFFF' }
                    }
                };
            });
            // 自动调整列宽（如果启用）
            if (mergedOptions.autoWidth) {
                worksheet.columns.forEach((column, index) => {
                    // 如果已经设置了自定义列宽，则不进行自动调整
                    if (mergedOptions.columnWidths && mergedOptions.columnWidths[columns[index].dataIndex]) {
                        return;
                    }

                    let maxLength = 0;
                    column.eachCell({ includeEmpty: true }, (cell) => {
                        let columnLength = 0;
                        if (cell.value !== null && cell.value !== undefined) {
                            columnLength = String(cell.value).length;
                            // 对中文字符进行特殊处理
                            const chineseCharCount = String(cell.value).match(/[\u4e00-\u9fa5]/g)?.length || 0;
                            columnLength += chineseCharCount * 0.1; // 中文字符需要更多空间
                        } else {
                            columnLength = 10;
                        }

                        if (columnLength > maxLength) {
                            maxLength = columnLength;
                        }
                    });

                    // 确保列宽足够显示内容
                    column.width = Math.max(maxLength + 2, column.width || 10);
                });
            }
        }

        // 在此处修改第一行标题的文字为produName
        const titleRow = worksheet.getRow(1);
        titleRow.eachCell((cell) => {
            cell.value = mergedOptions.productName;
        });

        // 冻结表头
        worksheet.views = [
            { state: 'frozen', xSplit: 0, ySplit: startRow, topLeftCell: `A${startRow + 1}`, activeCell: `A${startRow + 1}` }
        ];
    }

    // 导出文件
    try {
        // 确保文件名有正确的扩展名
        const fileName = filename.endsWith('.xlsx') ? filename : `${filename}.xlsx`;

        // 生成Excel文件的Buffer
        const buffer = await workbook.xlsx.writeBuffer();

        // 使用file-saver保存文件
        saveAs(new Blob([buffer], { type: 'application/octet-stream' }), fileName);

        return true;
    } catch (error) {
        console.error('导出Excel失败:', error);
        return false;
    }
};

// 根据value计算列宽
const getColumnWidth = (value) => {
    let columnLength = 0;
    if (value !== null && value !== undefined) {
        columnLength = String(value).length;
        // 对中文字符进行特殊处理
        const chineseCharCount = String(value).match(/[\u4e00-\u9fa5]/g)?.length || 0;
        columnLength += chineseCharCount * 0.1; // 中文字符需要更多空间
    } else {
        columnLength = 10;
    }

    return columnLength;
}
