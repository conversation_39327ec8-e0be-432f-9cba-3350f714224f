import { ref, reactive, watch } from 'vue';
import dayjs from 'dayjs';

/**
 * @description 表单组件钩子函数
 * @param {Object} props 组件属性
 * @param {Function} emit 事件发射函数
 * @param {Object} initialState 初始表单状态
 * @param {Array} dateFields 需要进行日期转换的字段数组
 * @param {Object} options 配置选项
 * @returns {Object} 表单相关状态和方法
 */
export const useFormBinding = (props, emit, initialState = {}, dateFields = [], options = {}) => {
    // 表单引用
    const formRef = ref(null);

    // 避免递归更新的标志位
    let isUpdatingParent = false;

    // 配置选项，默认启用自动数据发送
    const { autoEmit = true } = options;

    // 表单状态
    const formState = reactive({ ...initialState });

    // 向父组件发送表单数据的函数
    const emitFormData = (data = null) => {
        if (isUpdatingParent) return;

        isUpdatingParent = true;
        try {
            const formData = data || { ...formState };

            // 处理日期字段，如果是Dayjs对象，转换为时间戳
            dateFields.forEach(field => {
                if (formData[field] && typeof formData[field].valueOf === 'function') {
                    formData[field] = formData[field].valueOf();
                }
            });

            emit('update:form-data', formData);
        } finally {
            // 确保标志位被重置
            setTimeout(() => {
                isUpdatingParent = false;
            }, 0);
        }
    };

    // 监听父组件传入的表单数据
    watch(() => props.formData, (newVal) => {
        if (newVal && Object.keys(newVal).length > 0 && !isUpdatingParent) {
            // 处理可能的时间戳转换为日期对象
            const formData = { ...newVal };

            // 处理日期字段，如果是时间戳，转换为Dayjs对象
            dateFields.forEach(field => {
                if (formData[field] && typeof formData[field] === 'number') {
                    formData[field] = dayjs(formData[field]);
                }
            });

            // 更新表单状态
            Object.assign(formState, formData);
        }
    }, { deep: true, immediate: true });

    // 监听表单状态变化，向父组件发送更新（仅当autoEmit为true时）
    if (autoEmit) {
        watch(formState, () => {
            emitFormData();
        }, { deep: true });
    }

    // 表单提交处理
    const onFinish = (values) => {
        console.log('表单提交成功:', values);
    };

    // 表单重置方法
    const resetFields = () => {
        formRef.value?.resetFields();
    };

    return {
        formRef,
        formState,
        emitFormData,
        onFinish,
        resetFields
    };
};

/**
 * @description 表单验证钩子函数
 * @param {Object} formRef 表单引用
 * @param {Object} formState 表单状态
 * @param {Array} requiredFields 必填字段数组
 * @param {String} errorMessage 错误提示信息
 * @returns {Function} 表单验证方法
 */
export const useFormValidation = (formRef, formState, requiredFields = [], errorMessage = '请完善表单信息') => {
    // 返回一个与原始validate方法接口一致的函数
    const validateFn = () => {
        return new Promise((resolve, reject) => {
            if (!formRef.value) {
                resolve(true); // 如果没有表单引用，直接返回成功
                return;
            }

            formRef.value
                .validate()
                .then(() => {
                    // 检查必填字段
                    const missingFields = requiredFields.filter(field => {
                        const value = formState[field];
                        return value === undefined || value === null || value === '';
                    });

                    if (missingFields.length > 0) {
                        reject(errorMessage);
                    } else {
                        resolve(true); // 验证成功返回true
                    }
                })
                .catch(error => {
                    reject(error);
                });
        });
    };

    return validateFn;
}; 