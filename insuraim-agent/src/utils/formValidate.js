// ant-design-vue 表单验证规则


/**
 * @description 验证邮箱
 * @param {string} email 邮箱
 * @returns {boolean} 是否验证通过
 */
export const validateEmail = (email) => {
    return /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(email);
}

/**
 * @description 验证手机号
 */
export const validatePhone = (phone) => {
    return /^1[3-9]\d{9}$/.test(phone);
}

/**
 * @description 验证身份证号
 */
export const validateIdCard = (idCard) => {
    return /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[0-9Xx]$/.test(idCard);
}

/**
 * @description 验证通行证合法性
 */
export const validatePassport = (passport) => {
    return /^[A-Z0-9]{9}$/.test(passport);
}


/**
 * @description 验证输入是否全大写并且是英文
 */
export const validateAllUppercase = (input) => {
    return /^[A-Z]+$/.test(input);
}

/**
 * @description 验证输入是否是中文
 */
export const validateChinese = (input) => {
    return /^[\u4e00-\u9fa5]+$/.test(input);
}

/**
 * @description 验证输入是否是英文
 */
export const validateEnglish = (input) => {
    return /^[a-zA-Z]+$/.test(input);
}

/**
 * @description 验证输入是否是英文名（允许空格）
 */
export const validateEnglishName = (input) => {
    return /^[a-zA-Z\s]+$/.test(input);
}

/**
 * @description 验证输入是否为数字
 */
export const validateNumber = (input) => {
    return /^[0-9]+$/.test(input);
}

/**
 * @description 通用表单验证规则
 */
export const formRules = {
    /**
     * 中文姓名验证规则
     */
    chineseName: [
        { required: true, message: '请输入中文姓名', trigger: 'blur' },
        { min: 2, max: 20, message: '姓名长度应在2-20个字符之间', trigger: 'blur' },
        {
            validator: (rule, value) => {
                if (!value || validateChinese(value)) {
                    return Promise.resolve();
                }
                return Promise.reject('请输入中文姓名');
            },
            trigger: 'blur'
        }
    ],

    /**
     * 拼音/英文名验证规则
     */
    englishName: [
        { required: true, message: '请输入英文名', trigger: 'blur' },
        { min: 2, max: 20, message: '姓名长度应在2-20个字符之间', trigger: 'blur' },
        {
            validator: (rule, value) => {
                if (!value || validateEnglishName(value)) {
                    return Promise.resolve();
                }
                return Promise.reject('请输入有效的英文名（可包含空格）');
            },
            trigger: 'blur'
        }
    ],

    /**
     * 身份证号验证规则
     * @param {boolean} required 是否必填
     * @returns {Array} 验证规则数组
     */
    idCard: (required = true) => [
        { required: required, message: '请输入身份证号码', trigger: 'blur' },
        {
            validator: (rule, value) => {
                if (!value || validateIdCard(value)) {
                    return Promise.resolve();
                }
                return Promise.reject('请输入有效的身份证号码');
            },
            trigger: 'blur'
        }
    ],

    /**
     * 通行证号验证规则
     */
    passport: [
        { required: true, message: '请输入通行证号码', trigger: 'blur' },
        {
            validator: (rule, value) => {
                if (!value || validatePassport(value)) {
                    return Promise.resolve();
                }
                return Promise.reject('请输入有效的通行证号码');
            },
            trigger: 'blur'
        }
    ],

    /**
     * 出生日期验证规则
     */
    birthDate: [
        { required: true, message: '请选择出生日期', trigger: 'change' }
    ],

    /**
     * 性别验证规则
     */
    gender: [
        { required: true, message: '请选择性别', trigger: 'change' }
    ],

    /**
     * 手机号验证规则
     * @param {boolean} required 是否必填
     * @returns {Array} 验证规则数组
     */
    phone: (required = true) => [
        { required: required, message: '请输入手机号码', trigger: 'blur' },
        {
            validator: (rule, value) => {
                if (!value || validatePhone(value)) {
                    return Promise.resolve();
                }
                return Promise.reject('请输入有效的手机号码');
            },
            trigger: 'blur'
        }
    ],

    /**
     * 邮箱验证规则
     * @param {boolean} required 是否必填
     * @returns {Array} 验证规则数组
     */
    email: (required = true) => [
        { required: required, message: '请输入邮箱', trigger: 'blur' },
        {
            validator: (rule, value) => {
                if (!value || validateEmail(value)) {
                    return Promise.resolve();
                }
                return Promise.reject('请输入有效的邮箱地址');
            },
            trigger: 'blur'
        }
    ],

    /**
     * 身高验证规则
     */
    height: [
        { required: false, message: '请输入身高', trigger: 'blur' },
        { type: 'number', min: 50, max: 250, message: '身高应在50-250厘米之间', trigger: 'blur' }
    ],

    /**
     * 体重验证规则
     */
    weight: [
        { required: false, message: '请输入体重', trigger: 'blur' },
        { type: 'number', min: 2, max: 200, message: '体重应在2-200公斤之间', trigger: 'blur' }
    ],

    /**
     * 地址验证规则
     * @param {boolean} required 是否必填
     * @param {number} maxLength 最大长度
     * @returns {Array} 验证规则数组
     */
    address: (required = false, maxLength = 100) => [
        { required: required, message: '请输入地址', trigger: 'blur' },
        { max: maxLength, message: `地址长度不能超过${maxLength}个字符`, trigger: 'blur' }
    ]
};

