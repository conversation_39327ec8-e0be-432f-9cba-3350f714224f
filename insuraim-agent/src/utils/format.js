/**
 * 提取纯文本摘要
 * @param {string} html - HTML内容
 * @param {number} [len=100] - 截取长度
 * @returns {string} 纯文本摘要
 */
export const getPlainText = (html, len = 100) => {
    if (!html) return '';
    // 去除所有HTML标签
    const text = html.replace(/<[^>]+>/g, '').replace(/\s+/g, ' ').trim();

    // 对于快讯内容，保留段落结构
    if (len >= 500) {
        const paragraphs = text.split(/\n+/);
        return paragraphs.join('\n\n');
    }

    return text.length > len ? text.slice(0, len) + '...' : text;
};

/**
 * 复制文本到剪贴板
 * @param {string} text - 要复制的文本
 * @returns {Promise<void>}
 */
export const copyToClipboard = async (text) => {
    try {
        await navigator.clipboard.writeText(text);
        return true;
    } catch (error) {
        console.error('复制失败:', error);
        return false;
    }
}; 