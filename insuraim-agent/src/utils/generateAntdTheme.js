/**
 * 根据CSS变量生成Ant Design Vue主题配置
 * 将CSS变量映射到Ant Design Vue的主题变量
 */

// 从根元素或给定元素获取CSS变量的值
const getCssVariable = (variableName, element = document.documentElement) => {
    return getComputedStyle(element).getPropertyValue(variableName).trim()
}

// 将CSS颜色转换为RGB数组 [r, g, b]
const hexToRgb = (hex) => {
    // 移除可能的#前缀
    hex = hex.replace(/^#/, '')

    // 处理缩写形式 (#fff -> #ffffff)
    if (hex.length === 3) {
        hex = hex.split('').map(char => char + char).join('')
    }

    const bigint = parseInt(hex, 16)
    const r = (bigint >> 16) & 255
    const g = (bigint >> 8) & 255
    const b = bigint & 255

    return [r, g, b]
}

// 生成色板函数（用于生成不同深浅的颜色）
const generateColorPalette = (primaryColor) => {
    // 这里我们简化实现，仅返回主色
    // 实际应用中可以基于主色生成不同明度/饱和度的变体
    return {
        1: primaryColor,
        2: primaryColor,
        3: primaryColor,
        4: primaryColor,
        5: primaryColor,
        6: primaryColor,
        7: primaryColor,
        8: primaryColor,
        9: primaryColor,
        10: primaryColor,
    }
}

/**
 * 生成Ant Design Vue主题配置
 * @param {boolean} isDark - 是否为暗色主题
 * @returns {Object} Ant Design Vue主题配置对象
 */
export const generateAntdTheme = (isDark = false) => {
    // 确保DOM已加载
    if (typeof window === 'undefined' || !document.documentElement) {
        return {}
    }

    // 从CSS变量获取颜色
    const primaryColor = getCssVariable('--primary-600')
    const successColor = getCssVariable('--success-600')
    const warningColor = getCssVariable('--warning-600')
    const errorColor = getCssVariable('--danger-600')

    // 背景和文本颜色
    const backgroundColor = getCssVariable('--bg-secondary')
    const textColor = getCssVariable('--text-primary')
    const borderColor = getCssVariable('--border-medium')



    // 创建Ant Design Vue的主题配置
    return {
        token: {
            colorPrimary: primaryColor,
            colorSuccess: successColor,
            colorWarning: warningColor,
            colorError: errorColor,
            colorInfo: primaryColor,
            colorTextBase: textColor,
            colorBgBase: backgroundColor,
            colorBorder: borderColor,


            // 亮色/暗色模式通用的设置
            fontSize: 14,
            lineHeight: 1.5,
            fontFamily: 'PingFang SC',
            // 根据主题模式设置特定值
            ...(isDark ? {
                // 暗色模式特定设置
                colorBgContainer: getCssVariable('--bg-secondary'),
                colorBgElevated: getCssVariable('--bg-tertiary'),
                colorBgLayout: getCssVariable('--bg-primary'),
                colorTextBase: getCssVariable('--text-primary'),
                colorTextSecondary: getCssVariable('--text-secondary'),
                colorTextTertiary: getCssVariable('--text-tertiary'),
                boxShadow: getCssVariable('--shadow-md'),
                boxShadowSecondary: getCssVariable('--shadow-lg'),
            } : {
                // 亮色模式特定设置
                colorBgContainer: getCssVariable('--bg-secondary'),
                colorBgElevated: getCssVariable('--bg-tertiary'),
                colorBgLayout: getCssVariable('--bg-primary'),
                colorTextBase: getCssVariable('--text-primary'),
                colorTextSecondary: getCssVariable('--text-secondary'),
                colorTextTertiary: getCssVariable('--text-tertiary'),
                boxShadow: getCssVariable('--shadow-md'),
                boxShadowSecondary: getCssVariable('--shadow-lg'),
            }),
        },

        // 组件特定的样式覆盖
        components: {
            Button: {
                colorPrimary: primaryColor,
                algorithm: true, // 使用算法生成派生颜色
            },
            Input: {
                colorBorder: borderColor,
                colorPrimaryHover: primaryColor,
            },
            Select: {
                colorBorder: borderColor,
                colorPrimaryHover: primaryColor,
            },
            // 可根据需要添加更多组件配置
        }
    }
}

/**
 * 为Ant Design Vue生成暗色主题配置
 * @returns {Object} 暗色主题配置
 */
export const generateDarkAntdTheme = () => {
    return generateAntdTheme(true)
}

/**
 * 为Ant Design Vue生成亮色主题配置
 * @returns {Object} 亮色主题配置
 */
export const generateLightAntdTheme = () => {
    return generateAntdTheme(false)
} 