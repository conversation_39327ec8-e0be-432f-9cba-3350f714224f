/**
 * 保险相关图片备选列表
 */
export const insuranceImages = [
    // 握手（信任、合作）
    'https://images.unsplash.com/photo-1521791136064-7986c2920216?auto=format&fit=crop&w=800&q=80',
    // 家庭（保障、温馨）
    'https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?auto=format&fit=crop&w=800&q=80',
    // 保单/文件（保险合同）
    'https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?auto=format&fit=crop&w=800&q=80',
    // 金融办公（保险公司/理财）
    'https://images.unsplash.com/photo-1450101499163-c8848c66ca85?auto=format&fit=crop&w=800&q=80',
    // 团队（服务、专业）
    'https://images.unsplash.com/photo-1521737852567-6949f3f9f2b5?auto=format&fit=crop&w=800&q=80',
    // 安全锁（安全、保障）
    'https://images.unsplash.com/photo-1503676382389-4809596d5290?auto=format&fit=crop&w=800&q=80',
    // 医疗专业人员（医疗保险）
    'https://images.unsplash.com/photo-1579684385127-1ef15d508118?auto=format&fit=crop&w=800&q=80',
    // 房屋（房屋保险）
    'https://images.unsplash.com/photo-1570129477492-45c003edd2be?auto=format&fit=crop&w=800&q=80',
    // 汽车（车险）
    'https://images.unsplash.com/photo-1533473359331-0135ef1b58bf?auto=format&fit=crop&w=800&q=80',
    // 金融图表（投资理财保险）
    'https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?auto=format&fit=crop&w=800&q=80',
    // 老年夫妇（养老保险）
    'https://images.unsplash.com/photo-1476703993599-0035a21b17a9?auto=format&fit=crop&w=800&q=80',
    // 父母与孩子（儿童保险）
    'https://images.unsplash.com/photo-1540479859555-17af45c78602?auto=format&fit=crop&w=800&q=80',
    // 金融科技（保险科技）
    'https://images.unsplash.com/photo-1551288049-bebda4e38f71?auto=format&fit=crop&w=800&q=80'
];

// 图片缓存，保存每个新闻ID对应的图片URL
const newsImageMap = new Map();

/**
 * 获取随机保险图片
 * @param {string|number} id - 新闻ID
 * @returns {string} 图片URL
 */
export const getRandomInsuranceImage = (id) => {
    // 如果已有缓存，直接返回
    if (newsImageMap.has(id)) {
        return newsImageMap.get(id);
    }

    // 生成新图片并缓存
    const idx = Math.floor(Math.random() * insuranceImages.length);
    const imageUrl = insuranceImages[idx];
    newsImageMap.set(id, imageUrl);
    return imageUrl;
};

/**
 * 获取新闻图片
 * @param {Object} news - 新闻对象
 * @param {Set} failedImages - 加载失败的图片URL集合
 * @returns {string|null} 图片URL或null
 */
export const getNewsImage = (news, failedImages) => {
    // 有图片且未记录失败
    if (news.img && (!failedImages || !failedImages.has(news.img))) {
        return news.img;
    }
    // 无图片，使用随机图
    if (!news.img) {
        return getRandomInsuranceImage(news.id);
    }
    // 图片失败情况
    return null;
};

/**
 * 处理图片加载失败
 * @param {string} imgUrl - 失败的图片URL
 * @param {Set} failedImages - 加载失败的图片URL集合
 */
export const handleImgError = (imgUrl, failedImages) => {
    console.log('图片加载失败:', imgUrl);
    if (failedImages) {
        failedImages.add(imgUrl);
    }
}; 