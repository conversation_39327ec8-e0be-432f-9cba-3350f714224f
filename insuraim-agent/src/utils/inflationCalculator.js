/**
 * 通货膨胀计算器工具函数
 */

/**
 * 计算通货膨胀相关信息
 * @param {Object} data - 通货膨胀数据
 * @param {number} data.currentAmount - 当前金额
 * @param {number} data.inflationRate - 年通货膨胀率
 * @param {number} data.years - 年数
 * @returns {Object} 计算结果
 */
export function calculateInflation(data) {
    const futureValue = data.currentAmount * 
                       Math.pow(1 + data.inflationRate / 100, data.years);
    const purchasingPowerLoss = futureValue - data.currentAmount;
    const currentPurchasingPower = data.currentAmount / 
                                  Math.pow(1 + data.inflationRate / 100, data.years);
    const purchasingPowerLossPercentage = (purchasingPowerLoss / data.currentAmount * 100).toFixed(2);

    return {
        futureValue: futureValue.toFixed(2),
        purchasingPowerLoss: purchasingPowerLoss.toFixed(2),
        currentPurchasingPower: currentPurchasingPower.toFixed(2),
        purchasingPowerLossPercentage: purchasingPowerLossPercentage
    };
}

/**
 * 验证通货膨胀数据
 * @param {Object} data - 通货膨胀数据
 * @returns {boolean} 是否有效
 */
export function validateInflationData(data) {
    return data.currentAmount > 0 &&
           data.inflationRate > 0 &&
           data.years > 0;
} 