import { IRR as financeIRR } from 'financejs';
// Helper function to calculate Net Present Value (NPV)
export function NPV(rate, cashFlows) {
    let npv = 0;
    for (let i = 0; i < cashFlows.length; i++) {
        npv += cashFlows[i] / Math.pow(1 + rate, i + 1);
    }
    return npv;
}

// Calculate IRR using an iterative method
export function calculateIRR(cashFlows, guess = 0.05, maxIterations = 1000, tolerance = 1e-7) {
    if (!cashFlows || cashFlows.length === 0) return "N/A";

    const hasPositive = cashFlows.some(cf => cf > 0);
    const hasNegative = cashFlows.some(cf => cf < 0);

    if (!hasPositive || !hasNegative) {
        if (cashFlows.every(cf => cf === 0)) return "0.00";
        if (!hasNegative && hasPositive) return "Infinity";
        if (hasNegative && !hasPositive) return "N/A";
    }

    let rate = guess;
    let lowRate = -0.99;
    let highRate = 5.0;

    for (let i = 0; i < maxIterations; i++) {
        const npvValue = NPV(rate, cashFlows);

        if (Math.abs(npvValue) < tolerance) {
            return (rate * 100).toFixed(2);
        }

        let derivative = 0;
        for (let j = 0; j < cashFlows.length; j++) {
            derivative -= (j + 1) * cashFlows[j] / Math.pow(1 + rate, j + 2);
        }

        if (Math.abs(derivative) < 1e-9) {
            if (npvValue > 0) lowRate = rate; else highRate = rate;
            rate = (lowRate + highRate) / 2;
            if (highRate - lowRate < tolerance * 10) {
                return (rate * 100).toFixed(2);
            }
            continue;
        }

        const newRate = rate - npvValue / derivative;

        if (isNaN(newRate) || !isFinite(newRate) || newRate <= lowRate || newRate >= highRate) {
            if (npvValue > 0) lowRate = rate; else highRate = rate;
            rate = (lowRate + highRate) / 2;
            if (highRate - lowRate < tolerance * 10) {
                return (rate * 100).toFixed(2);
            }
            continue;
        }
        rate = newRate;
    }

    const lastAttemptNpv = NPV(rate, cashFlows);
    if (Math.abs(lastAttemptNpv) < tolerance * 100) return (rate * 100).toFixed(2);
    return "N/A";
}

// Helper function to get actual annual premiums after discounts
export function getActualPremiumsPerYear(annualPremium, paymentYears, discounts) {
    const actualPremiums = [];

    if (annualPremium <= 0 || paymentYears <= 0) {
        return Array(paymentYears).fill(0);
    }

    for (let i = 1; i <= paymentYears; i++) {
        let currentYearPremium = annualPremium;
        const discountForYear = discounts.find(d => d.year === i && d.rate !== null && d.rate !== undefined);
        if (discountForYear) {
            currentYearPremium -= annualPremium * (parseFloat(discountForYear.rate) / 100);
        }
        actualPremiums.push(Math.max(0, currentYearPremium));
    }
    return actualPremiums;
}

// Calculate simple interest
export function calculateSimpleInterest(holdingYear, paymentYearsList, netOutflowsList, totalReturnAtEnd) {
    const totalNetCashFlow = netOutflowsList.reduce((sum, val) => sum + val, 0) + totalReturnAtEnd;

    let timeWeightedSumOfPremiums = 0;
    for (let j = 0; j < paymentYearsList.length; j++) {
        if (j < netOutflowsList.length) {
            const timeFactor = holdingYear + 1 - paymentYearsList[j];
            timeWeightedSumOfPremiums += timeFactor * netOutflowsList[j];
        }
    }

    if (timeWeightedSumOfPremiums !== 0) {
        try {
            const singleInterestRate = totalNetCashFlow / Math.abs(timeWeightedSumOfPremiums);
            return (singleInterestRate * 100).toFixed(2);
        } catch (error) {
            console.error("计算单利错误:", error);
            return "N/A";
        }
    } else {
        if (totalNetCashFlow > 0 && netOutflowsList.every(p => p === 0)) return "Infinity";
        if (totalNetCashFlow === 0) return "0.00";
        return "N/A";
    }
} 