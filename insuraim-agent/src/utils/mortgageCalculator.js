/**
 * 房贷计算器工具函数
 */

/**
 * 计算房贷相关信息
 * @param {Object} data - 房贷数据
 * @param {number} data.loanAmount - 贷款总额
 * @param {number} data.loanYears - 贷款年限
 * @param {number} data.annualRate - 年利率
 * @param {string} data.paymentMethod - 还款方式 ('equal' 等额本息, 'principal' 等额本金)
 * @param {number} data.downPaymentRatio - 首付比例
 * @returns {Object} 计算结果
 */
export function calculateMortgage(data) {
    const monthlyRate = data.annualRate / 100 / 12;
    const totalMonths = data.loanYears * 12;
    const loanPrincipal = data.loanAmount * (1 - data.downPaymentRatio / 100);
    const downPayment = data.loanAmount * (data.downPaymentRatio / 100);

    let monthlyPayment;
    if (data.paymentMethod === 'equal') {
        // 等额本息
        monthlyPayment = loanPrincipal * monthlyRate * Math.pow(1 + monthlyRate, totalMonths) / 
                        (Math.pow(1 + monthlyRate, totalMonths) - 1);
    } else {
        // 等额本金
        const monthlyPrincipal = loanPrincipal / totalMonths;
        monthlyPayment = monthlyPrincipal + loanPrincipal * monthlyRate;
    }

    const totalPayment = monthlyPayment * totalMonths;
    const totalInterest = totalPayment - loanPrincipal;

    return {
        monthlyPayment: monthlyPayment.toFixed(2),
        totalPayment: totalPayment.toFixed(2),
        totalInterest: totalInterest.toFixed(2),
        downPayment: downPayment.toFixed(2),
        loanPrincipal: loanPrincipal.toFixed(2),
        monthlyRate: (monthlyRate * 100).toFixed(4),
        totalMonths: totalMonths
    };
}

/**
 * 验证房贷数据
 * @param {Object} data - 房贷数据
 * @returns {boolean} 是否有效
 */
export function validateMortgageData(data) {
    return data.loanAmount > 0 &&
           data.loanYears > 0 &&
           data.annualRate > 0 &&
           data.downPaymentRatio >= 0 &&
           data.downPaymentRatio <= 100;
} 