/**
 * 以千位符分割数字，支持指定小数位数、区域设置和货币格式
 * @param {number|string} num - 需要格式化的数字或数字字符串
 * @param {Object} [options] - 格式化选项
 * @param {number} [options.decimals] - 保留的小数位数，默认为自动识别
 * @param {boolean} [options.round=true] - 是否四舍五入，true为四舍五入，false为截断
 * @param {string} [options.locale='zh-CN'] - 区域设置，影响千位分隔符和小数点的格式
 * @param {string} [options.currency] - 货币代码，如 'CNY', 'USD' 等，设置后将使用货币格式
 * @param {boolean} [options.useGrouping=true] - 是否使用千位分隔符
 * @param {string} [options.defaultValue='0'] - 当输入无效时的默认返回值
 * @returns {string} 格式化后的数字字符串
 * 
 * @example
 * // 基本用法
 * formatNumber(1234.56); // "1,234.56"
 * 
 * // 指定小数位
 * formatNumber(1234.56, { decimals: 1 }); // "1,234.6"
 * 
 * // 货币格式
 * formatNumber(1234.56, { currency: 'CNY' }); // "¥1,234.56"
 * 
 * // 自定义区域
 * formatNumber(1234.56, { locale: 'en-US' }); // "1,234.56"
 * 
 * // 无效输入
 * formatNumber(null, { defaultValue: '无数据' }); // "无数据"
 */
export const formatNumber = (num, options = {}) => {
    // 默认选项
    const {
        decimals,              // 小数位数，默认自动识别
        round = true,          // 是否四舍五入
        locale = 'zh-CN',      // 区域设置
        currency,              // 货币代码
        useGrouping = true,    // 是否使用千位分隔符
        defaultValue = '0'     // 默认值
    } = options;

    // 处理无效输入
    if (num === null || num === undefined || num === '' || isNaN(Number(num))) {
        return defaultValue;
    }

    // 将输入转换为数字
    let numValue = Number(num);

    try {
        // 创建格式化选项
        const formatOptions = {
            useGrouping,                  // 是否使用千位分隔符
            minimumFractionDigits: decimals !== undefined ? decimals : 0,  // 最小小数位数
            maximumFractionDigits: decimals !== undefined ? decimals : 20  // 最大小数位数 (如果未指定decimals)
        };

        // 如果指定了货币，使用货币格式
        if (currency) {
            if (currency === 'USD') {
                return "$" + numValue.toFixed(decimals);
            }
            return numValue.toLocaleString(locale, {
                ...formatOptions,
                style: 'currency',
                currency: currency
            });
        }

        // 否则使用普通数字格式
        return numValue.toLocaleString(locale, formatOptions);
    } catch (error) {
        console.error('格式化数字时出错:', error);
        // 发生错误时返回原始数字的字符串形式
        return String(numValue);
    }
};

/**
 * 格式化大数字为缩写形式（K, M, B等）
 * @param {number|string} num - 需要格式化的数字或数字字符串
 * @param {Object} [options] - 格式化选项
 * @param {number} [options.decimals=1] - 保留的小数位数
 * @param {string} [options.locale='zh-CN'] - 区域设置
 * @param {Array<string>} [options.units=['', 'K', 'M', 'B', 'T']] - 单位数组
 * @param {number} [options.threshold=1000] - 缩写的阈值
 * @param {string} [options.defaultValue='0'] - 当输入无效时的默认返回值
 * @returns {string} 缩写后的数字字符串
 * 
 * @example
 * // 基本用法
 * formatNumberToShort(1500); // "1.5K"
 * formatNumberToShort(1500000); // "1.5M"
 * 
 * // 自定义小数位
 * formatNumberToShort(1500, { decimals: 2 }); // "1.50K"
 * 
 * // 无效输入
 * formatNumberToShort(null, { defaultValue: '无数据' }); // "无数据"
 */
export const formatNumberToShort = (num, options = {}) => {
    // 默认选项
    const {
        decimals = 1,                          // 小数位数
        locale = 'zh-CN',                      // 区域设置
        units = ['', 'K', 'M', 'B', 'T'],      // 单位数组
        threshold = 1000,                      // 缩写的阈值
        defaultValue = '0'                     // 默认值
    } = options;

    // 处理无效输入
    if (num === null || num === undefined || num === '' || isNaN(Number(num))) {
        return defaultValue;
    }

    // 将输入转换为数字
    let numValue = Math.abs(Number(num));
    const sign = Math.sign(Number(num)) === -1 ? '-' : '';

    // 如果数字小于阈值，直接返回格式化后的数字
    if (numValue < threshold) {
        return sign + formatNumber(numValue, { decimals, locale });
    }

    // 计算指数
    const exp = Math.min(Math.floor(Math.log(numValue) / Math.log(threshold)), units.length - 1);

    // 计算缩写后的数值
    const shortValue = numValue / Math.pow(threshold, exp);

    // 格式化并添加单位
    return sign + formatNumber(shortValue, { decimals, locale }) + units[exp];
};