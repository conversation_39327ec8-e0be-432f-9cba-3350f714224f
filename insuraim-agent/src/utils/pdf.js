import html2canvas from 'html2canvas-pro';
import { jsPDF } from 'jspdf';

/**
 * 将 HTML 元素导出为 PDF
 * @param {HTMLElement} element - 要导出的 HTML 元素
 * @param {string} filename - 导出的文件名
 * @param {Object} options - 配置项
 * @param {string} options.format - PDF 格式，默认为 'a4'
 * @param {string} options.orientation - PDF 方向，默认为 'portrait'
 * @param {number} options.quality - 图像质量，默认为 1
 * @param {number} options.scale - 缩放比例，默认为 2
 * @param {Object} options.margin - 页边距，默认为 { top: 10, right: 10, bottom: 10, left: 10 }
 * @param {boolean} options.fitToPage - 是否适应页面宽度，默认为 true
 * @returns {Promise<boolean>} - 是否导出成功
 */
export const exportElementToPDF = async (
    element,
    filename = 'document.pdf',
    options = {}
) => {
    if (!element) {
        console.error('导出PDF失败: 未提供有效的HTML元素');
        return false;
    }

    try {
        // 默认配置
        const defaultOptions = {
            format: 'a4',
            orientation: 'portrait',
            quality: 0.95,
            scale: 2,
            margin: { top: 10, right: 10, bottom: 10, left: 10 },
            fitToPage: true
        };

        // 合并配置
        const mergedOptions = { ...defaultOptions, ...options };

        // 使用 html2canvas 将元素转换为 canvas
        const canvas = await html2canvas(element, {
            scale: mergedOptions.scale,
            useCORS: true,
            allowTaint: true,
            scrollY: -window.scrollY,
            scrollX: 0,
            logging: false,
            backgroundColor: '#ffffff' // 确保背景为白色
        });

        // 创建 PDF 文档
        const pdf = new jsPDF({
            orientation: mergedOptions.orientation,
            unit: 'mm',
            format: mergedOptions.format
        });

        // 获取 PDF 页面的尺寸
        const pdfWidth = pdf.internal.pageSize.getWidth() - (mergedOptions.margin.left + mergedOptions.margin.right);
        const pdfHeight = pdf.internal.pageSize.getHeight() - (mergedOptions.margin.top + mergedOptions.margin.bottom);

        // 获取 canvas 的尺寸（像素）
        const canvasWidth = canvas.width;
        const canvasHeight = canvas.height;

        // 根据配置确定缩放比例
        let scaleFactor;
        if (mergedOptions.fitToPage) {
            // 适应页面宽度
            scaleFactor = pdfWidth / canvasWidth;
        } else {
            // 保持比例，确保内容完全适应页面
            scaleFactor = Math.min(pdfWidth / canvasWidth, pdfHeight / canvasHeight);
        }

        // 计算缩放后的尺寸（毫米）
        const imgWidth = canvasWidth * scaleFactor;
        const imgHeight = canvasHeight * scaleFactor;

        // 计算总页数
        const pageCount = Math.ceil(imgHeight / pdfHeight);

        // 计算每页在canvas上的像素高度
        const pageHeightInPixels = pdfHeight / scaleFactor;

        // 创建临时canvas用于分页处理
        const tempCanvas = document.createElement('canvas');
        const tempCtx = tempCanvas.getContext('2d');

        // 遍历每一页进行处理
        for (let i = 0; i < pageCount; i++) {
            // 如果不是第一页，添加新页
            if (i > 0) {
                pdf.addPage();
            }

            // 计算当前页在原始canvas中的位置和高度
            const sourceY = i * pageHeightInPixels;
            const sourceHeight = Math.min(pageHeightInPixels, canvasHeight - sourceY);

            // 设置临时canvas尺寸为当前页内容尺寸
            tempCanvas.width = canvasWidth;
            tempCanvas.height = sourceHeight;

            // 清除临时canvas
            tempCtx.fillStyle = '#ffffff';
            tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

            // 将原始canvas的对应部分绘制到临时canvas
            tempCtx.drawImage(
                canvas,
                0, sourceY, canvasWidth, sourceHeight,
                0, 0, canvasWidth, sourceHeight
            );

            // 将临时canvas转换为图像数据
            const imgData = tempCanvas.toDataURL('image/jpeg', mergedOptions.quality);

            // 计算在PDF中的位置（居中）
            const xPos = mergedOptions.margin.left + (pdfWidth - imgWidth) / 2;
            const yPos = mergedOptions.margin.top;

            // 将图像添加到PDF
            pdf.addImage(
                imgData,
                'JPEG',
                xPos,
                yPos,
                imgWidth,
                sourceHeight * scaleFactor
            );
        }

        // 保存PDF
        pdf.save(filename.endsWith('.pdf') ? filename : `${filename}.pdf`);
        return true;
    } catch (error) {
        console.error('导出PDF失败:', error);
        return false;
    }
};

/**
 * 将多个 HTML 元素导出为 PDF
 * @param {Array<HTMLElement>} elements - 要导出的 HTML 元素数组
 * @param {string} filename - 导出的文件名
 * @param {Object} options - 配置项
 * @returns {Promise<boolean>} - 是否导出成功
 */
export const exportMultipleElementsToPDF = async (
    elements,
    filename = 'document.pdf',
    options = {}
) => {
    if (!elements || !Array.isArray(elements) || elements.length === 0) {
        console.error('导出PDF失败: 未提供有效的HTML元素数组');
        return false;
    }

    try {
        // 默认配置
        const defaultOptions = {
            format: 'a4',
            orientation: 'portrait',
            quality: 1,
            scale: 2,
            margin: { top: 10, right: 10, bottom: 10, left: 10 }
        };

        // 合并配置
        const mergedOptions = { ...defaultOptions, ...options };

        // 创建 PDF 文档
        const pdf = new jsPDF({
            orientation: mergedOptions.orientation,
            unit: 'mm',
            format: mergedOptions.format
        });

        // 计算 PDF 页面的宽高（减去边距）
        const pdfWidth = pdf.internal.pageSize.getWidth() - (mergedOptions.margin.left + mergedOptions.margin.right);
        const pdfHeight = pdf.internal.pageSize.getHeight() - (mergedOptions.margin.top + mergedOptions.margin.bottom);

        // 遍历每个元素
        for (let i = 0; i < elements.length; i++) {
            const element = elements[i];

            // 如果不是第一个元素，添加新页
            if (i > 0) {
                pdf.addPage();
            }

            // 使用 html2canvas 将元素转换为 canvas
            const canvas = await html2canvas(element, {
                scale: mergedOptions.scale,
                useCORS: true,
                allowTaint: true,
                scrollY: -window.scrollY,
                scrollX: 0,
                logging: false
            });

            // 获取 canvas 的宽高
            const canvasWidth = canvas.width;
            const canvasHeight = canvas.height;

            // 计算缩放比例
            const ratio = Math.min(pdfWidth / canvasWidth, pdfHeight / canvasHeight);
            const scaledWidth = canvasWidth * ratio;
            const scaledHeight = canvasHeight * ratio;

            // 计算居中位置
            const x = (pdf.internal.pageSize.getWidth() - scaledWidth) / 2;
            const y = mergedOptions.margin.top;

            // 将 canvas 添加到 PDF
            const imgData = canvas.toDataURL('image/jpeg', mergedOptions.quality);
            pdf.addImage(imgData, 'JPEG', x, y, scaledWidth, scaledHeight);
        }

        // 保存 PDF
        pdf.save(filename.endsWith('.pdf') ? filename : `${filename}.pdf`);
        return true;
    } catch (error) {
        console.error('导出PDF失败:', error);
        return false;
    }
};
