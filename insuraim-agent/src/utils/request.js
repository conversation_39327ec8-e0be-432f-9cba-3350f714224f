import axios from 'axios';
import { message } from 'ant-design-vue';

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL + '/api',
  timeout: 60000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json;charset=utf-8',
  }
});

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 从localStorage获取token并设置到请求头
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data;
    if (res.code !== 200) {
      if (res.code === 401) {
        // 清除Token并重定向到登录页
        localStorage.removeItem('token');
        setTimeout(() => {
          window.location.href = '/login';
        }, 1000);
      }

      return Promise.reject(new Error(res.message || '服务器错误'));
    }
    return res.data;
  },
  error => {
    console.error('网络错误:', error);
    message.error('网络错误');

    // 处理401等特定HTTP状态码
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/login';
    }

    return Promise.reject(error);
  }
);

// 封装GET请求
export function get(url, params) {
  return service.get(url, { params });
}

// 封装POST请求
export function post(url, data) {
  return service.post(url, data);
}

// 封装PUT请求
export function put(url, data) {
  return service.put(url, data);
}

// 封装DELETE请求
export function del(url, params) {
  return service.delete(url, { params });
}


// 获取axios实例
export function getService() {
  return service;
}

// 获取API基础URL
export function getBaseUrl() {
  const baseUrl = import.meta.env.VITE_API_BASE_URL + '/api';
  return baseUrl;
}

// 导出封装后的请求方法
export default {
  get,
  post,
  put,
  del,
  getService,
  getBaseUrl
};