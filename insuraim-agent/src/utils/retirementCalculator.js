// 计算退休规划
export function calculateRetirement(retirementData) {
    const {
        currentAge,
        retirementAge,
        lifeExpectancy,
        currentMonthlyIncome,
        retirementIncomeRatio,
        inflationRate, // e.g., 5 for 5%
        currentSavings,
        expectedReturnRate, // e.g., 20 for 20%
        retirementReturnRate // e.g., 5 for 5%
    } = retirementData;

    // 验证输入 (您的验证逻辑保持不变)
    if (!currentAge || !retirementAge || !lifeExpectancy || !currentMonthlyIncome ||
        !retirementIncomeRatio || inflationRate === null || inflationRate < 0 || !currentSavings || // Allow 0 for rates
        expectedReturnRate === null || expectedReturnRate < 0 ||
        retirementReturnRate === null || retirementReturnRate < 0) { // Ensure rates can be 0
        throw new Error('请填写所有退休规划信息，利率不能为负');
    }

    if (currentAge >= retirementAge) {
        throw new Error('当前年龄必须小于退休年龄');
    }

    if (retirementAge >= lifeExpectancy) {
        throw new Error('退休年龄必须小于预期寿命');
    }
    
    

    // 计算退休时所需总金额
    const yearsToRetirement = retirementAge - currentAge;
    const retirementYears = lifeExpectancy - retirementAge;
    const monthlyRetirementIncome = currentMonthlyIncome * (retirementIncomeRatio / 100);
    const annualRetirementIncome = monthlyRetirementIncome * 12;

    // 考虑通货膨胀
    const inflationRateDecimal = inflationRate / 100;
    const inflatedAnnualIncome = annualRetirementIncome * Math.pow(1 + inflationRateDecimal, yearsToRetirement);

    // 计算退休时所需总金额（考虑退休后的投资回报和通货膨胀）
    const retirementReturnRateDecimal = retirementReturnRate / 100;
    // 近似实际回报率 (如果追求更高精度，可以使用 ((1+r)/(1+i))-1 )
    const realReturnRateDecimal = retirementReturnRateDecimal - inflationRateDecimal;

    let totalRetirementFundValue;
    if (retirementYears <= 0) { // 虽然已有验证，但再次确认
        totalRetirementFundValue = 0;
    } else if (Math.abs(realReturnRateDecimal) < 0.00001) { // 如果实际回报率接近0
        totalRetirementFundValue = inflatedAnnualIncome * retirementYears;
    } else {
        totalRetirementFundValue = inflatedAnnualIncome * ((1 - Math.pow(1 + realReturnRateDecimal, -retirementYears)) / realReturnRateDecimal);
    }

    // 计算每月需要储蓄金额
    const expectedReturnRateDecimal = expectedReturnRate / 100;
    const futureValueOfCurrentSavings = currentSavings * Math.pow(1 + expectedReturnRateDecimal, yearsToRetirement);
    const retirementGapValue = totalRetirementFundValue - futureValueOfCurrentSavings;

    let monthlySavingsValue;
    const monthlyExpectedRateDecimal = expectedReturnRateDecimal / 12;
    const numberOfSavingPeriods = yearsToRetirement * 12;

    if (retirementGapValue <= 0) {
        monthlySavingsValue = 0;
    } else if (numberOfSavingPeriods <= 0) { // 如果没有储蓄期，但仍有缺口 (理论上被 currentAge < retirementAge 阻止)
        monthlySavingsValue = retirementGapValue; // 需要一次性补足，或视为无法规划
    } else if (Math.abs(monthlyExpectedRateDecimal) < 0.0000001) { // 如果预期月回报率接近0
        monthlySavingsValue = retirementGapValue / numberOfSavingPeriods;
    } else {
        const denominator = Math.pow(1 + monthlyExpectedRateDecimal, numberOfSavingPeriods) - 1;
        if (denominator === Infinity) { // 极高回报率或极长期限导致复利因子为无穷大
            monthlySavingsValue = 0; // 理论上不需要每月存钱，因为现有储蓄增长极快
        } else if (Math.abs(denominator) < 0.0000001) { // 分母异常接近0（非正常情况，除非月回报率为0）
            monthlySavingsValue = retirementGapValue / numberOfSavingPeriods; // 降级为无回报率计算
        } else {
            monthlySavingsValue = (retirementGapValue * monthlyExpectedRateDecimal) / denominator;
        }
    }
    
    // 确保每月储蓄不为负
    if (monthlySavingsValue < 0) {
        monthlySavingsValue = 0;
    }

    // 根据年龄和风险承受能力确定投资策略
    let investmentStrategy = '';
    if (yearsToRetirement > 20) {
        investmentStrategy = '建议采用积极型投资组合：70%股票，20%债券，10%现金';
    } else if (yearsToRetirement > 10) {
        investmentStrategy = '建议采用平衡型投资组合：50%股票，40%债券，10%现金';
    } else {
        investmentStrategy = '建议采用保守型投资组合：30%股票，60%债券，10%现金';
    }

    const firstYearRetirementIncome = monthlyRetirementIncome * Math.pow(1 + inflationRate / 100, yearsToRetirement);

    // 返回前确保数值有效，避免NaN传递给toLocaleString
    const formatNumber = (num) => (isNaN(num) || !isFinite(num)) ? '计算错误' : Math.round(num).toLocaleString();

    return {
        totalRetirementFund: formatNumber(totalRetirementFundValue),
        monthlySavings: formatNumber(monthlySavingsValue),
        retirementGap: formatNumber(retirementGapValue),
        investmentStrategy,
        firstYearRetirementIncome: formatNumber(firstYearRetirementIncome),
        futureValueOfCurrentSavings: formatNumber(futureValueOfCurrentSavings),
    };
}