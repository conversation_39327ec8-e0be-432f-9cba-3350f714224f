import { ref, onUnmounted } from 'vue';
import { pollMessage, pushMessage } from '../api/message';
import { notification } from 'ant-design-vue';

export function useMessagePolling(interval = 10000) {
    const unreadCount = ref(0);
    const isPolling = ref(false);
    let timer = null;

    // 更新消息为已推送
    const updateMessagePushed = async (messages) => {
        try {
            // 依次更新每个消息的推送状态
            await Promise.all(messages.map(message => pushMessage(message.id)));
        } catch (error) {
            console.error('更新消息推送状态失败:', error);
        }
    };

    // 获取未读消息数量
    const fetchMessageCount = async () => {
        try {
            const messages = await pollMessage();
            if (Array.isArray(messages)) {
                unreadCount.value = messages.length;

                // 如果有未推送的消息
                if (messages.length > 0) {
                    // 显示通知
                    notification.info({
                        message: `${messages[0].type === '1' ? '系统消息' : '业务消息'}`,
                        description: `${messages[0].title}`,
                        placement: 'topRight',
                        onClick: () => {
                            window.location.href = '/message-center';
                        }
                    });

                    // 更新消息为已推送
                    await updateMessagePushed(messages);
                    // 判断当前url
                    const currentUrl = window.location.href;
                    if (currentUrl.includes('/my-plans')) {
                        window.location.reload();
                    }
                }
            }
        } catch (error) {
            console.error('轮询消息失败:', error);
        }
    };

    // 开始轮询
    const startPolling = () => {
        if (isPolling.value) return;

        isPolling.value = true;
        fetchMessageCount(); // 立即执行一次
        timer = setInterval(fetchMessageCount, interval);
    };

    // 停止轮询
    const stopPolling = () => {
        if (timer) {
            clearInterval(timer);
            timer = null;
        }
        isPolling.value = false;
    };

    // 组件卸载时清理
    onUnmounted(() => {
        stopPolling();
    });

    return {
        unreadCount,
        isPolling,
        startPolling,
        stopPolling,
    };
} 