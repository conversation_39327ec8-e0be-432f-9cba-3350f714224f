<template>
  <div class="container mx-auto py-6 px-4">
    <!-- 页面标题 -->
    <div
      class="title-section mb-6 p-6 rounded-lg shadow-md text-white bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700">
      <div class="flex items-center">
        <Icon icon="mdi:calendar" class="text-4xl mr-3" />
        <h1 class="text-2xl font-bold page-title text-white">{{ $t('calendar.calendarManagement') }}</h1>
      </div>
      <p class="mt-2 page-description">
        {{ $t('calendar.browseAndManage') }}
      </p>
    </div>

    <!-- 统计卡片 -->
    <a-card class="mb-6 rounded-lg" :bordered="false">
      <h1 class="text-2xl font-bold mb-4">{{ $t('calendar.statistics') }}</h1>
      <!-- 日程统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- 总日程数卡片 -->
        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-blue-500">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">{{ $t('calendar.totalEvents') }}</p>
              <p class="text-2xl font-bold">{{ calendarData.length || 0 }}</p>
            </div>
            <div class="bg-blue-100 p-2 rounded-full">
              <Icon icon="mdi:calendar-month" class="text-2xl text-blue-500" />
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-500">
            <span class="flex items-center">
              <Icon icon="mdi:information-outline" class="mr-1" />
              {{ $t('calendar.totalEventsDesc') }}
            </span>
          </div>
        </div>

        <!-- 待办日程卡片 -->
        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-orange-500">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <p class="text-gray-500 text-sm">{{ $t('calendar.pendingEvents') }}</p>
              <p class="text-2xl font-bold">{{ pendingEventsCount }}</p>
            </div>
            <div class="bg-orange-100 p-2 rounded-full">
              <Icon icon="mdi:clock-outline" class="text-2xl text-orange-500" />
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-500">
            <span class="flex items-center">
              <Icon icon="mdi:information-outline" class="mr-1" />
              {{ $t('calendar.pendingEventsDesc') }}
            </span>
          </div>
        </div>

        <!-- 已完成日程卡片 -->
        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-green-500">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">{{ $t('calendar.completedEvents') }}</p>
              <p class="text-2xl font-bold">{{ completedEventsCount }}</p>
            </div>
            <div class="bg-green-100 p-2 rounded-full">
              <Icon icon="mdi:check-circle" class="text-2xl text-green-500" />
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-500">
            <span class="flex items-center">
              <Icon icon="mdi:information-outline" class="mr-1" />
              {{ $t('calendar.completedEventsDesc') }}
            </span>
          </div>
        </div>

        <!-- 今日日程卡片 -->
        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-purple-500">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">{{ $t('calendar.todayEvents') }}</p>
              <p class="text-2xl font-bold">{{ todayEventsCount }}</p>
            </div>
            <div class="bg-purple-100 p-2 rounded-full">
              <Icon icon="mdi:calendar-today" class="text-2xl text-purple-500" />
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-500">
            <span class="flex items-center">
              <Icon icon="mdi:information-outline" class="mr-1" />
              {{ $t('calendar.todayEventsDesc') }}
            </span>
          </div>
        </div>
      </div>
    </a-card>

    <div class="mx-auto bg-white rounded-lg shadow-sm p-6 mt-4">
      <!-- 添加日程按钮 -->
      <div class="flex justify-between items-center mb-4">
        <a-button type="primary" @click="showAddEventModal" class="flex items-center">
          <Icon icon="mdi:plus" class="mr-1 text-lg" />
          <span>{{ $t('calendar.addEvent') }}</span>
        </a-button>
      </div>

      <a-calendar v-model:value="value" @change="onCalendarChange">
        <template #dateCellRender="{ current }">
          <div class="events-wrapper">
            <div class="events">
              <div v-for="item in getListData(current)" :key="item.id || item.content" class="event-item"
                :class="[{ 'event-completed': item.status === 1 }]" @click="openEventDetails(item)">
                {{ item.content || item.title }}
              </div>
            </div>
          </div>
        </template>
        <template #monthCellRender="{ current }">
          <div v-if="getMonthData(current)" class="notes-month">
            <div class="notes-month-card">
              <div class="notes-month-icon">
                <Icon icon="mdi:clipboard-check-multiple" />
              </div>
              <div class="notes-month-content">
                <div class="notes-month-count">{{ getMonthData(current) }}</div>
                <div class="notes-month-text">{{ $t('calendar.todoItems') }}</div>
              </div>
            </div>
          </div>
        </template>
      </a-calendar>
    </div>
  </div>

  <!-- 添加日程的模态框 -->
  <a-modal v-model:visible="eventModalVisible" :title="isEdit ? $t('calendar.editEvent') : $t('calendar.addEvent')"
    @ok="handleAddEvent" @cancel="cancelEventModal" :confirmLoading="confirmLoading">
    <a-form :model="eventForm" layout="vertical">
      <a-form-item :label="$t('calendar.eventTime')" required>
        <a-date-picker v-model:value="eventForm.date" style="width: 100%" :disabledDate="disabledDate" />
      </a-form-item>

      <a-form-item :label="$t('calendar.eventTitle')" required>
        <a-input v-model:value="eventForm.title" :placeholder="$t('calendar.enterEventTitle')" />
      </a-form-item>

      <a-form-item :label="$t('calendar.eventContent')">
        <a-textarea v-model:value="eventForm.content" :placeholder="$t('calendar.eventContent')" :rows="3" />
      </a-form-item>

      <a-form-item :label="$t('calendar.remindTime')">
        <a-date-picker v-model:value="eventForm.remindDate" style="width: 100%" :showTime="true"
          format="YYYY-MM-DD HH:mm" :placeholder="$t('calendar.remindTime')" />
      </a-form-item>

      <a-form-item :label="$t('calendar.repeatRule')">
        <a-select v-model:value="eventForm.repeatRule" style="width: 100%">
          <a-select-option value="none">{{ $t('calendar.noRepeat') }}</a-select-option>
          <a-select-option value="daily">{{ $t('calendar.daily') }}</a-select-option>
          <a-select-option value="weekly">{{ $t('calendar.weekly') }}</a-select-option>
          <a-select-option value="monthly">{{ $t('calendar.monthly') }}</a-select-option>
          <a-select-option value="yearly">{{ $t('calendar.yearly') }}</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item :label="$t('calendar.remark')">
        <a-textarea v-model:value="eventForm.remark" :placeholder="$t('calendar.remark')" :rows="2" />
      </a-form-item>

      <a-form-item v-if="isEdit" :label="$t('calendar.completionStatus')">
        <a-switch v-model:checked="statusChecked" :checked-children="$t('calendar.completed')"
          :un-checked-children="$t('calendar.inProgress')" />
      </a-form-item>
    </a-form>
  </a-modal>

  <!-- 日程详情模态框 -->
  <a-modal v-model:visible="detailsModalVisible" :title="$t('calendar.eventDetails')"
    @cancel="detailsModalVisible = false" :footer="null">
    <template v-if="currentEvent">
      <div class="event-details">
        <p class="mb-2"><span class="font-medium">{{ $t('calendar.title') }}：</span>{{ currentEvent.title }}</p>
        <p class="mb-2" v-if="currentEvent.content"><span class="font-medium">{{ $t('calendar.content') }}：</span>{{
          currentEvent.content }}</p>
        <p class="mb-2"><span class="font-medium">{{ $t('calendar.eventTime') }}：</span>{{
          formatDate(currentEvent.calendarTime) }}</p>
        <p class="mb-2"><span class="font-medium">{{ $t('calendar.status') }}：</span>{{
          getStatusText(currentEvent.status) }}</p>
        <p class="mb-2" v-if="currentEvent.remindTime"><span class="font-medium">{{ $t('calendar.remindTime')
            }}：</span>{{
              formatDate(currentEvent.remindTime) }}</p>
        <p class="mb-2" v-if="currentEvent.repeatRule && currentEvent.repeatRule !== 'none'">
          <span class="font-medium">{{ $t('calendar.repeatRule') }}：</span>{{ getRepeatRuleText(currentEvent.repeatRule)
          }}
        </p>
        <p class="mb-2" v-if="currentEvent.remark"><span class="font-medium">{{ $t('calendar.remark') }}：</span>{{
          currentEvent.remark }}</p>
      </div>

      <div class="flex justify-end mt-4 gap-2">
        <a-button @click="toggleEventStatus(currentEvent)" :type="currentEvent.status === 1 ? 'default' : 'primary'">
          {{ currentEvent.status === 1 ? $t('calendar.markAsInProgress') : $t('calendar.markAsCompleted') }}
        </a-button>
        <a-button type="primary" @click="editEvent(currentEvent)">
          {{ $t('calendar.edit') }}
        </a-button>
        <a-popconfirm :title="$t('calendar.confirmDelete')" @confirm="deleteEvent(currentEvent)"
          :ok-text="$t('calendar.yes')" :cancel-text="$t('calendar.no')">
          <a-button danger>{{ $t('calendar.delete') }}</a-button>
        </a-popconfirm>
      </div>
    </template>
  </a-modal>
</template>

<script setup>
import { Calendar, Badge, Modal, Button, Form, DatePicker, Input, Select, message, Switch, Popconfirm } from 'ant-design-vue';
import MainLayout from '@/layouts/MainLayout.vue';
import { ref, reactive, onMounted, computed } from 'vue';
import dayjs from 'dayjs';
import { Icon } from '@iconify/vue';
import { calendarAPI } from '@/api';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const value = ref(dayjs());
const ACalendar = Calendar;

// 日历数据
const calendarData = ref([]);
const isLoading = ref(false);

// 统计数据计算属性
const pendingEventsCount = computed(() => {
  return calendarData.value.filter(item => item.status === 0).length;
});

const completedEventsCount = computed(() => {
  return calendarData.value.filter(item => item.status === 1).length;
});

const todayEventsCount = computed(() => {
  const today = dayjs().format('YYYY-MM-DD');
  return calendarData.value.filter(item => {
    const itemDate = dayjs(item.calendarTime).format('YYYY-MM-DD');
    return itemDate === today;
  }).length;
});

// 模态框相关状态
const eventModalVisible = ref(false);
const detailsModalVisible = ref(false);
const confirmLoading = ref(false);
const isEdit = ref(false);
const currentEvent = ref(null);

// 事件表单数据
const eventForm = reactive({
  id: null,
  title: '',
  content: '',
  date: dayjs(),
  remindDate: null, // 用于UI显示的提醒时间
  remindTime: null, // 提醒时间戳
  repeatRule: 'none', // 重复规则
  status: 0,
  remark: '' // 备注
});

// 完成状态切换计算属性
const statusChecked = computed({
  get: () => eventForm.status === 1,
  set: (value) => { eventForm.status = value ? 1 : 0 }
});

// 加载日历数据
const loadCalendarData = async () => {
  isLoading.value = true;

  try {
    const data = await calendarAPI.getCalendarList();
    calendarData.value = data || [];
  } catch (error) {
    console.error(t('calendar.loadFailed'), error);
    message.error(t('calendar.loadFailed'));
  } finally {
    isLoading.value = false;
  }
};

// 日历变化事件
const onCalendarChange = (date) => {
  value.value = date;
  loadCalendarData();
};

// 显示添加事件模态框
const showAddEventModal = () => {
  isEdit.value = false;
  eventForm.id = null;
  eventForm.title = '';
  eventForm.content = '';
  eventForm.date = dayjs();
  eventForm.remindDate = null;
  eventForm.remindTime = null;
  eventForm.repeatRule = 'none';
  eventForm.status = 0;
  eventForm.remark = '';
  eventModalVisible.value = true;
};

// 取消事件模态框
const cancelEventModal = () => {
  eventModalVisible.value = false;
  isEdit.value = false;
};

// 禁用过去的日期
const disabledDate = (current) => {
  return current && current < dayjs().startOf('day');
};

// 打开事件详情
const openEventDetails = (event) => {
  currentEvent.value = event;
  detailsModalVisible.value = true;
};

// 编辑事件
const editEvent = (event) => {
  isEdit.value = true;
  eventForm.id = event.id;
  eventForm.title = event.title || '';
  eventForm.content = event.content || '';
  eventForm.date = event.calendarTime ? dayjs(event.calendarTime) : dayjs(); // 使用日程时间
  eventForm.remindTime = event.remindTime;
  eventForm.remindDate = event.remindTime ? dayjs(event.remindTime) : null;
  eventForm.repeatRule = event.repeatRule || 'none';
  eventForm.status = event.status || 0;
  eventForm.remark = event.remark || '';

  detailsModalVisible.value = false;
  eventModalVisible.value = true;
};

// 切换事件状态
const toggleEventStatus = async (event) => {
  const newStatus = event.status === 1 ? 0 : 1; // 0-进行中，1-已完成

  try {
    await calendarAPI.updateCalendarStatus(event.id, newStatus);
    message.success(t('calendar.updateSuccess'));

    // 更新本地数据
    if (currentEvent.value) {
      currentEvent.value.status = newStatus;
    }

    // 重新加载数据
    loadCalendarData();
  } catch (error) {
    console.error(t('calendar.updateFailed'), error);
    message.error(t('calendar.updateFailed'));
  }
};

// 删除事件
const deleteEvent = async (event) => {
  try {
    await calendarAPI.deleteCalendar(event.id);
    message.success(t('calendar.deleteSuccess'));
    detailsModalVisible.value = false;

    // 重新加载数据
    loadCalendarData();
  } catch (error) {
    console.error(t('calendar.deleteFailed'), error);
    message.error(t('calendar.deleteFailed'));
  }
};

// 提交添加/编辑事件
const handleAddEvent = async () => {
  // 表单验证
  if (!eventForm.date) {
    message.error(t('calendar.selectEventTime'));
    return;
  }

  if (!eventForm.title.trim()) {
    message.error(t('calendar.enterEventTitle'));
    return;
  }

  confirmLoading.value = true;

  try {
    const formData = {
      id: eventForm.id,
      title: eventForm.title.trim(),
      content: eventForm.content.trim(),
      calendarTime: eventForm.date.startOf('day').valueOf(),
      remindTime: eventForm.remindDate ? eventForm.remindDate.valueOf() : null,
      repeatRule: eventForm.repeatRule,
      status: eventForm.status,
      remark: eventForm.remark.trim()
    };

    if (isEdit.value && eventForm.id) {
      // 更新日程
      await calendarAPI.updateCalendar(formData);
      message.success(t('calendar.eventUpdateSuccess'));
    } else {
      // 创建日程
      await calendarAPI.createCalendar(formData);
      message.success(t('calendar.eventAddSuccess'));
    }

    // 关闭模态框
    eventModalVisible.value = false;

    // 重新加载数据
    loadCalendarData();
  } catch (error) {
    console.error(t('calendar.saveFailed'), error);
    message.error(t('calendar.saveFailed'));
  } finally {
    confirmLoading.value = false;
  }
};

// 获取每日事件数据
const getListData = (value) => {
  if (calendarData.value.length === 0) {
    return [];
  }

  const date = value.format('YYYY-MM-DD');
  return calendarData.value.filter(item => {
    const itemDate = dayjs(item.calendarTime).format('YYYY-MM-DD');
    return itemDate === date;
  });
};

// 获取每月数据
const getMonthData = (value) => {
  if (calendarData.value.length === 0) {
    return 0;
  }

  if (value.month() === value.value.month()) {
    return calendarData.value.filter(item => item.status === 0).length;
  }
  return 0;
};

// 格式化日期
const formatDate = (timestamp) => {
  return dayjs(timestamp).format('YYYY-MM-DD');
};

// 获取重复规则文本
const getRepeatRuleText = (rule) => {
  const ruleMap = {
    'none': t('calendar.noRepeat'),
    'daily': t('calendar.daily'),
    'weekly': t('calendar.weekly'),
    'monthly': t('calendar.monthly'),
    'yearly': t('calendar.yearly')
  };
  return ruleMap[rule] || t('calendar.noRepeat');
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    0: t('calendar.inProgress'),
    1: t('calendar.completed'),
    2: t('calendar.cancelled')
  };
  return statusMap[status] || t('calendar.inProgress');
};

// 组件挂载时加载数据
onMounted(() => {
  loadCalendarData();
});
</script>

<style scoped>
/* 标题区域样式 */
.title-section {
  transition: all 0.3s ease;
}

.title-section:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.page-title {
  position: relative;
  display: inline-block;
  color: white;
}

.page-title::after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: white;
  border-radius: 3px;
}

.page-description {
  max-width: 600px;
  opacity: 0.9;
}

/* 统计卡片样式 */
.stat-card {
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.events-wrapper {
  width: 100%;
  overflow: hidden;
}

.events {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 4px;
}

.event-item {
  margin: 0;
  padding: 4px 4px;
  font-size: 14px;
  line-height: 16px;
  border-radius: 2px;
  max-width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  color: #595959;
  cursor: pointer;
  transition: all 0.3s;
}

.event-item:hover {
  background-color: #e6f7ff;
  border-color: #1890ff;
}

.event-completed {
  text-decoration: line-through;
  opacity: 0.6;
}

/* 月视图待办事项样式 */
.notes-month {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notes-month-card {
  display: flex;
  align-items: center;
  padding: 8px 12px;
}

.notes-month-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background-color: #1890ff;
  color: white;
  border-radius: 8px;
  margin-right: 12px;
  font-size: 20px;
}

.notes-month-content {
  display: flex;
  flex-direction: column;
}

.notes-month-count {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
}

.notes-month-text {
  font-size: 12px;
  color: #666;
}

/* 确保Icon组件垂直居中 */
:deep(.ant-btn) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* 日历单元格内容样式优化 */
:deep(.ant-picker-calendar-date-content) {
  height: auto;
  min-height: 24px;
  line-height: 1.2;
}

/* 详情页样式 */
.event-details p {
  padding: 8px;
  border-radius: 4px;
  background-color: #f5f5f5;
}
</style>