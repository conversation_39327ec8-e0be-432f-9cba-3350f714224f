<template>
    <div>
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-800">资料云盘</h1>
            <p class="text-gray-600">管理和获取保险产品相关资料</p>
        </div>

        <!-- 面包屑导航 -->
        <div class="mb-4">
            <div class="flex items-center text-sm">
                <template v-for="(crumb, index) in breadcrumbs" :key="index">
                    <span class="cursor-pointer text-blue-500 hover:text-blue-700"
                        @click="navigateToFolder({ id: crumb.id })">
                        {{ crumb.name }}
                    </span>
                    <span v-if="index < breadcrumbs.length - 1" class="mx-2 text-gray-500">/</span>
                </template>
            </div>
        </div>

        <!-- 搜索和筛选区域 -->
        <div class="bg-white rounded-xl shadow-sm p-6 mb-6">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <!-- 搜索框 -->
                <div class="relative flex-grow md:max-w-md">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Icon icon="mdi:magnify" class="h-5 w-5 text-gray-400" />
                    </div>
                    <input type="text" placeholder="搜索文件..." v-model="searchQuery"
                        class="block w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg text-sm focus:ring-blue-500 focus:border-blue-500" />
                </div>

                <!-- 筛选选项 -->
                <div class="flex flex-wrap gap-2">
                    <a-select v-model:value="fileTypeFilter" style="width: 120px" placeholder="文件类型"
                        @change="handleFilterChange">
                        <a-select-option value="">全部</a-select-option>
                        <a-select-option value="pdf">PDF</a-select-option>
                        <a-select-option value="doc">Word</a-select-option>
                        <a-select-option value="xls">Excel</a-select-option>
                        <a-select-option value="ppt">PPT</a-select-option>
                    </a-select>
                    <a-button type="primary" class="btn-with-icon" @click="openUploadModal">
                        <Icon icon="mdi:upload" class="btn-icon" />
                        <span class="btn-text">上传文件</span>
                    </a-button>
                </div>
            </div>
        </div>

        <!-- 标题区域 -->
        <div class="mb-6">
            <h2 class="text-xl font-semibold text-gray-800">
                {{ currentFolder ? currentFolder.name : '全部文件' }}
            </h2>
        </div>

        <!-- 加载中状态 -->
        <div v-if="loading" class="text-center py-20">
            <a-spin size="large" />
        </div>

        <div v-else class="grid grid-cols-1 gap-6">
            <!-- 文件和文件夹列表 -->
            <div class="bg-white rounded-xl shadow-sm p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold">文件和文件夹</h2>
                    <div class="flex space-x-2">
                        <a-button type="text" class="btn-with-icon" @click="openFolderModal">
                            <Icon icon="mdi:folder-plus" class="btn-icon" />
                            <span class="btn-text">新建文件夹</span>
                        </a-button>
                        <a-button-group class="ml-2">
                            <a-button type="text" :class="viewMode === 'grid' ? 'text-blue-500' : ''"
                                @click="viewMode = 'grid'">
                                <Icon icon="mdi:view-grid" />
                            </a-button>
                            <a-button type="text" :class="viewMode === 'list' ? 'text-blue-500' : ''"
                                @click="viewMode = 'list'">
                                <Icon icon="mdi:format-list-bulleted" />
                            </a-button>
                        </a-button-group>
                        <a-dropdown>
                            <template #overlay>
                                <a-menu>
                                    <a-menu-item key="1">按名称排序</a-menu-item>
                                    <a-menu-item key="2">按更新时间排序</a-menu-item>
                                </a-menu>
                            </template>
                            <a-button type="text" class="btn-with-icon">
                                <Icon icon="mdi:sort" class="btn-icon" />
                                <span class="btn-text">排序</span>
                            </a-button>
                        </a-dropdown>
                    </div>
                </div>

                <!-- 网格视图 -->
                <div v-if="viewMode === 'grid'" class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                    <!-- 文件夹项 -->
                    <div v-for="folder in filteredFolders" :key="`folder-${folder.id}`"
                        class="flex flex-col items-center p-4 hover:bg-blue-50 rounded-lg cursor-pointer transition-all"
                        @click="navigateToFolder(folder)">
                        <div class="w-12 h-12 flex items-center justify-center mb-2 relative group">
                            <Icon icon="mdi:folder" class="w-12 h-12 text-blue-400" />
                            <div
                                class="absolute top-0 right-0 opacity-0 group-hover:opacity-100 transition-opacity">
                                <a-dropdown :trigger="['click']" @click.stop>
                                    <template #overlay>
                                        <a-menu>
                                            <a-menu-item key="1">重命名</a-menu-item>
                                            <a-menu-item key="2"
                                                @click="confirmDeleteFolder(folder)">删除</a-menu-item>
                                        </a-menu>
                                    </template>
                                    <button class="bg-white rounded-full p-1 shadow hover:bg-gray-100">
                                        <Icon icon="mdi:dots-vertical" class="text-gray-600" />
                                    </button>
                                </a-dropdown>
                            </div>
                        </div>
                        <span class="text-sm text-center font-medium">{{ folder.name }}</span>
                    </div>

                    <!-- 文件项 -->
                    <div v-for="file in filteredFiles" :key="`file-${file.id}`"
                        class="flex flex-col items-center p-4 hover:bg-blue-50 rounded-lg cursor-pointer transition-all"
                        @dblclick="previewFile(file)">
                        <div class="w-12 h-12 flex items-center justify-center mb-2 relative group">
                            <Icon :icon="getFileIcon(file.fileType)" class="w-12 h-12"
                                :class="getFileIconColor(file.fileType)" />
                            <div
                                class="absolute top-0 right-0 opacity-0 group-hover:opacity-100 transition-opacity">
                                <a-dropdown :trigger="['click']" @click.stop>
                                    <template #overlay>
                                        <a-menu>
                                            <a-menu-item key="1" @click="downloadFile(file)">下载</a-menu-item>
                                            <a-menu-item key="2" @click="previewFile(file)">预览</a-menu-item>
                                            <a-menu-item key="3">分享</a-menu-item>
                                            <a-menu-item key="4">重命名</a-menu-item>
                                            <a-menu-item key="5" @click="confirmDeleteFile(file)">删除</a-menu-item>
                                        </a-menu>
                                    </template>
                                    <button class="bg-white rounded-full p-1 shadow hover:bg-gray-100">
                                        <Icon icon="mdi:dots-vertical" class="text-gray-600" />
                                    </button>
                                </a-dropdown>
                            </div>
                       
                        </div>
                        <span class="text-sm text-center font-medium truncate w-full">{{ file.name }}</span>
                        <span class="text-xs text-gray-500">{{ formatFileSize(file.size) }}</span>
                    </div>
                </div>

                <!-- 列表视图 -->
                <div v-else-if="viewMode === 'list'" class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col"
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    名称
                                </th>
                                <th scope="col"
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    类型
                                </th>

                                <th scope="col"
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    大小/修改日期
                                </th>
                                <th scope="col"
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    操作
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <!-- 文件夹行 -->
                            <tr v-for="folder in filteredFolders" :key="`folder-${folder.id}`"
                                class="hover:bg-gray-50 cursor-pointer" @click="navigateToFolder(folder)">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center">
                                            <Icon icon="mdi:folder" class="h-6 w-6 text-blue-400" />
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">{{ folder.name }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">文件夹</div>
                                </td>

                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">{{ formatDate(folder.updateAt) }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium" @click.stop>
                                    <div class="flex space-x-2">
                                        <a-dropdown>
                                            <template #overlay>
                                                <a-menu>
                                                    <a-menu-item key="1">重命名</a-menu-item>
                                                    <a-menu-item key="2"
                                                        @click="confirmDeleteFolder(folder)">删除</a-menu-item>
                                                </a-menu>
                                            </template>
                                            <a class="text-gray-600 hover:text-gray-900">
                                                <Icon icon="mdi:dots-horizontal" />
                                            </a>
                                        </a-dropdown>
                                    </div>
                                </td>
                            </tr>

                            <!-- 文件行 -->
                            <tr v-for="file in filteredFiles" :key="`file-${file.id}`" class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center">
                                            <Icon :icon="getFileIcon(file.fileType)" class="h-6 w-6"
                                                :class="getFileIconColor(file.fileType)" />
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">{{ file.name }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ getFileTypeName(file.fileType) }}</div>
                                </td>

                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">{{ formatFileSize(file.size) }}</div>
                                    <div class="text-xs text-gray-400">{{ formatDate(file.updateAt) }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <a @click.prevent="downloadFile(file)"
                                            class="link-with-icon text-blue-600 hover:text-blue-900">
                                            <Icon icon="mdi:download" class="link-icon" />
                                            <span class="link-text">下载</span>
                                        </a>
                                        <a @click.prevent="previewFile(file)"
                                            class="link-with-icon text-green-600 hover:text-green-900">
                                            <Icon icon="mdi:eye" class="link-icon" />
                                            <span class="link-text">预览</span>
                                        </a>
                                        <a-dropdown>
                                            <template #overlay>
                                                <a-menu>
                                                    <a-menu-item key="1">重命名</a-menu-item>
                                                    <a-menu-item key="2">移动到</a-menu-item>
                                                    <a-menu-item key="3" @click="previewFile(file)">预览</a-menu-item>
                                                    <a-menu-item key="4" danger
                                                        @click="confirmDeleteFile(file)">删除</a-menu-item>
                                                </a-menu>
                                            </template>
                                            <a class="text-gray-600 hover:text-gray-900">
                                                <Icon icon="mdi:dots-horizontal" />
                                            </a>
                                        </a-dropdown>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div v-if="filteredFolders.length === 0 && filteredFiles.length === 0"
                    class="text-center py-6 text-gray-500">
                    没有找到文件或文件夹
                </div>
            </div>
        </div>
    </div>

    <!-- 上传文件对话框 -->
    <a-modal v-model:visible="uploadModalVisible" title="上传文件" :maskClosable="false" :confirmLoading="uploadLoading"
        @ok="uploadFile">
        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">选择文件</label>
                <a-upload-dragger
                    :multiple="false"
                    :showUploadList="false"
                    :beforeUpload="handleFileChange"
                    @drop="handleFileDrop"
                >
                    <p class="ant-upload-drag-icon">
                        <Icon icon="mdi:upload" class="h-8 w-8 text-blue-500" />
                    </p>
                    <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
                    <p class="ant-upload-hint">支持单个或批量上传，严禁上传公司数据或其他违禁文件</p>
                </a-upload-dragger>
            </div>

            <div v-if="fileToUpload">
                <p class="text-sm text-gray-500">选中的文件: {{ fileToUpload.name }} ({{
                    formatFileSize(fileToUpload.size) }})
                </p>
            </div>
        </div>
    </a-modal>

    <!-- 新建文件夹对话框 -->
    <a-modal v-model:visible="folderModalVisible" title="新建文件夹" :maskClosable="false"
        :confirmLoading="folderLoading" @ok="createFolder">
        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">文件夹名称</label>
                <a-input v-model:value="newFolderName" placeholder="请输入文件夹名称" />
            </div>

        </div>
    </a-modal>
</template>

<script setup>
import { Icon } from '@iconify/vue';
import { ref, computed, onMounted } from 'vue';
import { folderAPI, fileAPI } from '@/api';
import { message, Modal } from 'ant-design-vue';

// 视图模式
const viewMode = ref('grid');

// 搜索和筛选
const searchQuery = ref('');
const fileTypeFilter = ref('');

// 当前用户ID (实际项目中应从用户状态或本地存储获取)
const currentUserId = ref(1);

// 当前导航的文件夹
const currentFolder = ref(0);
const breadcrumbs = ref([]);

// 文件夹数据
const folders = ref([]);
const loading = ref(false);

// 文件数据
const files = ref([]);

// 上传文件对话框
const uploadModalVisible = ref(false);
const fileToUpload = ref(null);
const uploadLoading = ref(false);

// 新建文件夹对话框
const folderModalVisible = ref(false);
const newFolderName = ref('');
const folderLoading = ref(false);

// 初始加载数据
onMounted(() => {
    loadFolderContent();
});

// 加载文件夹内容
const loadFolderContent = async (folderId = null) => {
    loading.value = true;
    try {
        const response = await folderAPI.getFolderContent(folderId, fileTypeFilter.value);
        const data = response.data || response;
        folders.value = data.folders || [];
        files.value = data.files || [];
        currentFolder.value = data.currentFolder;
        await updateBreadcrumbs();

    } catch (error) {
        console.error('加载文件夹内容出错:', error);
        message.error('加载文件夹内容失败');
    } finally {
        loading.value = false;
    }
};

// 更新面包屑导航
const updateBreadcrumbs = async () => {
    if (!currentFolder.value) {
        breadcrumbs.value = [{ id: null, name: '根目录' }];
        return;
    }

    try {
        // 递归获取完整路径
        const path = await getBreadcrumbPath(currentFolder.value);
        // 添加根目录作为第一个元素
        breadcrumbs.value = [{ id: null, name: '根目录' }, ...path];
    } catch (error) {
        console.error('获取面包屑路径出错:', error);
        // 发生错误时至少显示当前文件夹
        breadcrumbs.value = [
            { id: null, name: '根目录' },
            { id: currentFolder.value.id, name: currentFolder.value.name }
        ];
    }
};

// 递归获取文件夹路径
const getBreadcrumbPath = async (folder) => {
    if (!folder || !folder.id) {
        return [];
    }

    const path = [{ id: folder.id, name: folder.name }];

    // 如果存在父文件夹，则递归获取
    if (folder.parentId) {
        try {
            // 获取父文件夹信息
            const response = await folderAPI.getFolder(folder.parentId);
            const parentFolder = response.data || response;

            if (parentFolder && parentFolder.id) {
                // 递归获取父文件夹路径
                const parentPath = await getBreadcrumbPath(parentFolder);
                // 合并路径
                return [...parentPath, ...path];
            }
        } catch (error) {
            console.error('获取父文件夹出错:', error);
        }
    }

    return path;
};

// 导航到文件夹
const navigateToFolder = (folder) => {
    loadFolderContent(folder ? folder.id : null);
};

// 打开上传文件对话框
const openUploadModal = () => {
    fileToUpload.value = null;
    uploadModalVisible.value = true;
};

// 处理文件选择
const handleFileChange = (file) => {
    fileToUpload.value = file;
    return false; // 阻止自动上传
};

// 处理文件拖拽
const handleFileDrop = (e) => {
    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
        fileToUpload.value = files[0];
    }
};

// 上传文件
const uploadFile = async () => {
    if (!fileToUpload.value) {
        message.error('请选择要上传的文件');
        return;
    }

    uploadLoading.value = true;
    try {
        const folderId = currentFolder.value ? currentFolder.value.id : 0;

        const response = await fileAPI.uploadFile(
            fileToUpload.value,
            folderId,
            currentUserId.value
        );

        // 检查响应格式

        if (response) {
            message.success('文件上传成功');
            uploadModalVisible.value = false;
            // 重新加载文件列表
            loadFolderContent(folderId);
        } else {
            message.error((response.data?.msg || response.msg || '文件上传失败'));
        }
    } catch (error) {
        console.error('上传文件出错:', error);
        message.error('文件上传失败: ' + (error.message || '未知错误'));
    } finally {
        uploadLoading.value = false;
    }
};

// 打开新建文件夹对话框
const openFolderModal = () => {
    newFolderName.value = '';
    folderModalVisible.value = true;
};

// 创建文件夹
const createFolder = async () => {
    if (!newFolderName.value) {
        message.error('请输入文件夹名称');
        return;
    }

    folderLoading.value = true;
    try {
        const folderData = {
            name: newFolderName.value,
            parentId: currentFolder.value ? currentFolder.value.id : 0,
            creatorId: currentUserId.value
        };

        await folderAPI.createFolder(folderData);
        message.success('文件夹创建成功');
        folderModalVisible.value = false;
        // 重新加载文件夹列表
        loadFolderContent(folderData.parentId);

    } catch (error) {
        console.error('创建文件夹出错:', error);
        message.error('文件夹创建失败');
    } finally {
        folderLoading.value = false;
    }
};

// 下载文件
const downloadFile = (file) => {
    try {
        // 直接使用ossUrl进行下载
        if (!file.ossUrl) {
            message.error('文件链接不存在');
            return;
        }

        // 使用fetch获取文件并强制下载
        fetch(file.ossUrl)
            .then(response => response.blob())
            .then(blob => {
                // 创建一个新的URL对象
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', file.name);
                document.body.appendChild(link);
                link.click();

                // 清理
                window.URL.revokeObjectURL(url);
                document.body.removeChild(link);

                message.success('文件下载成功');
            })
            .catch(error => {
                console.error('下载文件出错:', error);
                message.error('文件下载失败');
            });
    } catch (error) {
        console.error('下载文件出错:', error);
        message.error('文件下载失败');
    }
};

// 删除文件
const confirmDeleteFile = (file) => {
    Modal.confirm({
        title: '确认删除',
        content: `确定要删除文件 "${file.name}" 吗？`,
        okText: '确认',
        okType: 'danger',
        cancelText: '取消',
        async onOk() {
            try {
                await fileAPI.deleteFile(file.id);
                message.success('文件删除成功');
                loadFolderContent(currentFolder.value ? currentFolder.value.id : 0);
            } catch (error) {
                console.error('删除文件出错:', error);
                message.error('文件删除失败');
            }
        }
    });
};

// 删除文件夹
const confirmDeleteFolder = (folder) => {
    Modal.confirm({
        title: '确认删除',
        content: `确定要删除文件夹 "${folder.name}" 吗？`,
        okText: '确认',
        okType: 'danger',
        cancelText: '取消',
        async onOk() {
            try {
                await folderAPI.deleteFolder(folder.id);
                message.success('文件夹删除成功');
                loadFolderContent(currentFolder.value ? currentFolder.value.id : 0);

            } catch (error) {
                console.error('删除文件夹出错:', error);
                message.error('文件夹删除失败');
            }
        }

    });
};

// 根据筛选条件过滤文件夹
const filteredFolders = computed(() => {
    if (!searchQuery.value) {
        return folders.value;
    }

    return folders.value.filter(folder =>
        folder.name.toLowerCase().includes(searchQuery.value.toLowerCase())
    );
});

// 根据筛选条件过滤文件
const filteredFiles = computed(() => {
    let result = files.value;

    // 根据搜索关键词过滤
    if (searchQuery.value) {
        result = result.filter(file =>
            file.name.toLowerCase().includes(searchQuery.value.toLowerCase())
        );
    }

    // 根据文件类型过滤
    if (fileTypeFilter.value) {
        result = result.filter(file => file.fileType === fileTypeFilter.value);
    }



    return result;
});

// 监听筛选条件变化
const handleFilterChange = () => {
    loadFolderContent(currentFolder.value ? currentFolder.value.id : null);
};

// 获取文件图标
const getFileIcon = (fileType) => {
    switch (fileType) {
        case 'pdf':
            return 'mdi:file-pdf-box';
        case 'doc':
        case 'docx':
            return 'mdi:file-word-box';
        case 'xls':
        case 'xlsx':
            return 'mdi:file-excel-box';
        case 'ppt':
        case 'pptx':
            return 'mdi:file-powerpoint-box';
        default:
            return 'mdi:file-document-outline';
    }
};

// 获取文件图标颜色
const getFileIconColor = (fileType) => {
    switch (fileType) {
        case 'pdf':
            return 'text-red-500';
        case 'doc':
        case 'docx':
            return 'text-blue-500';
        case 'xls':
        case 'xlsx':
            return 'text-green-500';
        case 'ppt':
        case 'pptx':
            return 'text-orange-500';
        default:
            return 'text-gray-500';
    }
};



// 获取文件类型显示名称
const getFileTypeName = (fileType) => {
    switch (fileType) {
        case 'pdf':
            return 'PDF文档';
        case 'doc':
        case 'docx':
            return 'Word文档';
        case 'xls':
        case 'xlsx':
            return 'Excel表格';
        case 'ppt':
        case 'pptx':
            return 'PPT演示文稿';
        case 'txt':
            return '文本文件';
        case 'jpg':
        case 'jpeg':
        case 'png':
            return '图片文件';
        default:
            return fileType ? fileType.toUpperCase() : '未知类型';
    }
};

// 格式化文件大小
const formatFileSize = (sizeInBytes) => {
    if (sizeInBytes < 1024) {
        return sizeInBytes + ' B';
    } else if (sizeInBytes < 1024 * 1024) {
        return (sizeInBytes / 1024).toFixed(2) + ' KB';
    } else if (sizeInBytes < 1024 * 1024 * 1024) {
        return (sizeInBytes / (1024 * 1024)).toFixed(2) + ' MB';
    } else {
        return (sizeInBytes / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
    }
};

// 格式化日期
const formatDate = (dateString) => {
    if (!dateString) return '-';

    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
};

// 预览文件
const previewFile = (file) => {
    try {
        // 检查文件URL是否存在
        if (!file.ossUrl) {
            message.error('文件链接不存在');
            return;
        }

        // 检查文件类型
        const supportedPreviewTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'jpg', 'jpeg', 'png'];
        if (!supportedPreviewTypes.includes(file.fileType)) {
            message.warning('该文件类型不支持在线预览');
            return;
        }

        // 在新标签页中打开文件
        window.open(file.ossUrl, '_blank');
        
        // 提示用户
        message.success('文件预览已在新标签页打开');
    } catch (error) {
        console.error('预览文件出错:', error);
        message.error('文件预览失败');
    }
};
</script>

<style scoped>
.ant-tabs-nav {
    margin-bottom: 0 !important;
}

/* 新增按钮内图标与文字对齐样式 */
.btn-with-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    vertical-align: middle;
}

.btn-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 4px;
    line-height: 1;
    vertical-align: middle;
}

.btn-text {
    display: inline-block;
    line-height: 1;
    vertical-align: middle;
}

/* 链接中图标与文字对齐样式 */
.link-with-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    vertical-align: middle;
}

.link-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 4px;
    line-height: 1;
    vertical-align: middle;
}

.link-text {
    display: inline-block;
    line-height: 1;
    vertical-align: middle;
}

/* 确保所有图标大小一致 */
.btn-icon svg,
.link-icon svg {
    width: 1em;
    height: 1em;
}

.ant-upload-drag-icon {
    display: flex;
    justify-content: center;
    margin-bottom: 16px;
}

.ant-upload-text {
    font-size: 16px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
    margin-bottom: 8px;
}

.ant-upload-hint {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.45);
}
</style>