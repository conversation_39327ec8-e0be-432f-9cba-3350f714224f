<template>
  <div class="mx-auto">
    <!-- 加载状态 -->
    <div v-if="loading" class="flex justify-center items-center py-20">
      <a-spin size="large" />
    </div>

    <!-- 错误提示 -->
    <div v-else-if="error" class="flex justify-center items-center py-20">
      <a-result status="error" :title="error" :sub-title="'无法加载保险公司详情'">
        <template #extra>
          <a-button type="primary" @click="goBack">返回列表</a-button>
        </template>
      </a-result>
    </div>

    <!-- 公司详情内容 -->
    <template v-else-if="company">
      <!-- 公司基本信息头部 -->
      <div class="bg-white rounded-lg shadow-md p-6 mb-6 ">
        <div class="flex items-start justify-between mb-4">
          <div class="flex items-center space-x-4">
            <img 
              v-if="company.logoUrl" 
              :src="company.logoUrl" 
              :alt="company.name"
              class="w-16 h-16 object-contain"
            />
            <div>
              <h1 class="text-2xl font-medium">{{ company.name }}</h1>
              <div class="flex items-center mt-2">
                <span class="text-gray-500">{{ company.region }}</span>
                <span class="mx-2 text-gray-300">|</span>
                <span class="text-gray-500">排名: 第 {{ company.companyRank }} 名</span>
                <span class="mx-2 text-gray-300">|</span>
                <span class="text-gray-500">代码: {{ company.code }}</span>
              </div>
            </div>
          </div>
          <div class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 cursor-pointer" @click="goBack">
            <Icon icon="material-symbols:arrow-back" class="mr-1.5" />
            <span>返回列表</span>
          </div>
        </div>
      </div>

      <!-- 详情内容区域 -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <!-- 选项卡 -->
        <a-tabs v-model:activeKey="activeTab" class="company-detail-tabs">
          <!-- 公司介绍 -->
          <a-tab-pane key="intro" tab="公司介绍">
            <div class="space-y-4">
              <p class="text-gray-600 leading-relaxed">{{ company.description }}</p>
              <div class="space-y-4 mt-6">
                <div class="flex items-center justify-between py-2 border-b border-gray-200">
                  <span class="text-gray-500">排名</span>
                  <span class="font-medium">第 {{ company.companyRank }} 名</span>
                </div>
                <div class="flex items-center justify-between py-2 border-b border-gray-200">
                  <span class="text-gray-500">公司代码</span>
                  <span class="font-medium">{{ company.code }}</span>
                </div>
                <div v-if="company.website" class="flex items-center justify-between py-2 border-b">
                  <span class="text-gray-500">官方网站</span>
                  <a :href="company.website" target="_blank" class="text-blue-500 hover:underline">{{ company.website }}</a>
                </div>
              </div>
            </div>
          </a-tab-pane>

          <!-- 相关链接 -->
          <a-tab-pane key="links" tab="相关链接">
            <div class="space-y-4">
              <a-empty v-if="!company.links?.length" description="暂无相关链接" />
              <template v-else>
                <div 
                  v-for="link in company.links" 
                  :key="link.id" 
                  class="p-4 border rounded-md border-gray-200 flex justify-between items-center hover:shadow-sm transition-shadow"
                >
                  <div>
                    <p class="text-base font-medium text-gray-800 mb-1">{{ link.description }}</p>
                    <p class="text-sm text-gray-500 break-all">{{ link.linkUrl }}</p>
                  </div>
                  <a :href="link.linkUrl" target="_blank" class="ml-4">
                    <div class="w-8 h-8 rounded-full bg-primary flex items-center justify-center cursor-pointer hover:bg-primary-dark transition-colors">
                      <Icon icon="material-symbols:link" class="text-white text-base" />
                    </div>
                  </a>
                </div>
              </template>
            </div>
          </a-tab-pane>

          <!-- 信用评级 -->
          <a-tab-pane key="credit" tab="信用评级">
            <div class="space-y-4">
              <a-empty v-if="!company.creditRatings?.length" description="暂无信用评级信息" />
              <template v-else>
                <div v-for="rating in company.creditRatings" :key="rating.id" class="p-4 border rounded-lg border-gray-200 hover:shadow-sm transition-shadow">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <div class="flex items-center gap-3 mb-1">
                        <span class="text-2xl font-semibold text-red-500">{{ rating.rating }}</span>
                        <span class="text-gray-900">{{ rating.ratingAgency }}</span>
                      </div>
                      <div class="text-gray-600">{{ rating.project }}</div>
                      <div class="text-sm text-gray-400 mt-2">
                        {{ new Date(rating.time).toLocaleDateString() }}
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </a-tab-pane>

          <!-- 官方文件 -->
          <a-tab-pane key="docs" tab="官方文件">
            <div class="space-y-4">
              <a-empty v-if="!company.files?.length" description="暂无官方文件" />
              <template v-else>
                <div v-for="file in company.files" :key="file.id" class="p-4 border rounded-lg border-gray-200 hover:shadow-sm transition-shadow">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <div class="flex items-center gap-2 mb-2">
                        <Icon icon="material-symbols:description" class="text-blue-500 text-xl" />
                        <span class="text-gray-900 font-medium">{{ file.fileName }}</span>
                      </div>
                      <div class="flex items-center text-sm text-gray-500 gap-4">
                        <span>上传者：{{ file.author }}</span>
                        <span>上传时间：{{ new Date(file.uploadTime).toLocaleDateString() }}</span>
                      </div>
                    </div>
                    <a :href="file.filePath" target="_blank" class="ml-4">
                      <div class="w-8 h-8 rounded-full bg-primary flex items-center justify-center cursor-pointer hover:bg-primary-dark transition-colors">
                        <Icon icon="material-symbols:download" class="text-white text-base" />
                      </div>
                    </a>
                  </div>
                </div>
              </template>
            </div>
          </a-tab-pane>

          <!-- 内部文件 -->
          <a-tab-pane key="internal" tab="内部文件">
            <div class="space-y-4">
              <a-empty v-if="!company.internalDocs?.length" description="暂无内部文件" />
              <template v-else>
                <div v-for="doc in company.internalDocs" :key="doc.id" class="py-2 border-b">
                  <div class="flex justify-between items-center">
                    <span class="text-gray-600">{{ doc.title }}</span>
                    <a-button type="link" size="small">
                      <template #icon>
                        <Icon icon="material-symbols:download" />
                      </template>
                      下载
                    </a-button>
                  </div>
                  <p class="text-sm text-gray-500 mt-1">更新时间：{{ doc.updateTime }}</p>
                </div>
              </template>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </template>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Icon } from '@iconify/vue';
import { companyAPI } from '../../api';
import { message } from 'ant-design-vue';

const route = useRoute();
const router = useRouter();

// 状态变量
const company = ref(null);
const loading = ref(true);
const error = ref('');
const activeTab = ref('intro');

// 获取公司详情
const loadCompanyDetail = async () => {
  const code = route.params.code;
  if (!code) {
    error.value = '未找到公司代码';
    loading.value = false;
    return;
  }

  try {
    loading.value = true;
    const result = await companyAPI.getCompanyDetail(code);
    company.value = result;
  } catch (err) {
    console.error('获取公司详情失败:', err);
    error.value = '获取公司详情失败';
    message.error('获取公司详情失败');
  } finally {
    loading.value = false;
  }
};

// 返回列表页面
const goBack = () => {
  router.push('/company');
};

// 组件挂载时加载数据
onMounted(() => {
  loadCompanyDetail();
});
</script>

<style scoped>
/* 主题色和悬停效果 */
.bg-primary {
  background-color: #1890ff;
}

.bg-primary-dark:hover {
  background-color: #096dd9;
}

/* 选项卡样式 */
:deep(.company-detail-tabs .ant-tabs-nav) {
  margin-bottom: 24px;
}

:deep(.company-detail-tabs .ant-tabs-tab) {
  padding: 12px 16px;
  font-size: 14px;
}

:deep(.company-detail-tabs .ant-tabs-tab + .ant-tabs-tab) {
  margin-left: 24px;
}

:deep(.company-detail-tabs .ant-tabs-ink-bar) {
  height: 2px;
}

/* 按钮样式 */
:deep(.ant-btn .anticon) {
  display: inline-flex;
  align-items: center;
}
</style> 