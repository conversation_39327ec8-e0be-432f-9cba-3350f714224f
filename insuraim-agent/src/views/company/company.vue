<template>
  <!-- 主要内容区域 -->
  <div class="container mx-auto py-6 px-4">
    <!-- 页面标题 -->
    <div
      class="title-section mb-6 p-6 rounded-lg shadow-md text-white bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700">
      <div class="flex items-center">
        <Icon icon="mdi:office-building" class="text-4xl mr-3" />
        <h1 class="text-2xl font-bold page-title">{{ $t('company.companyList') }}</h1>
      </div>
      <p class="mt-2 page-description">
        {{ $t('company.browseAndFind') }}
      </p>
    </div>

    <!-- 统计卡片 -->
    <a-card class="mb-6 rounded-lg" :bordered="false">
      <h1 class="text-2xl font-bold mb-4">{{ $t('company.statistics') }}</h1>
      <!-- 公司统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- 公司总数卡片 -->
        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-blue-500">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">{{ $t('company.totalCompanies') }}</p>
              <p class="text-2xl font-bold">{{ companyTotal || 0 }}</p>
            </div>
            <div class="bg-blue-100 p-2 rounded-full">
              <Icon icon="mdi:office-building" class="text-2xl text-blue-500" />
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-500">
            <span class="flex items-center">
              <Icon icon="material-symbols:info-outline" class="mr-1" />
              {{ $t('company.totalCompaniesDesc') }}
            </span>
          </div>
        </div>

        <!-- 地区分布卡片 -->
        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-green-500">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <p class="text-gray-500 text-sm">{{ $t('company.regionDistribution') }}</p>
              <p class="text-2xl font-bold">{{ regionOptions.length || 0 }}</p>
            </div>
            <div class="bg-green-100 p-2 rounded-full">
              <Icon icon="mdi:earth" class="text-2xl text-green-500" />
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-500">
            <span class="flex items-center">
              <Icon icon="mdi:information-outline" class="mr-1" />
              {{ $t('company.regionDistributionDesc') }}
            </span>
          </div>
        </div>

        <!-- 公司类型卡片 -->
        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-orange-500">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">{{ $t('company.companyTypes') }}</p>
              <p class="text-2xl font-bold">{{ Object.keys($t('company.typeOptions')).length || 0 }}</p>
            </div>
            <div class="bg-orange-100 p-2 rounded-full">
              <Icon icon="mdi:shape" class="text-2xl text-orange-500" />
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-500">
            <span class="flex items-center">
              <Icon icon="mdi:information-outline" class="mr-1" />
              {{ $t('company.companyTypesDesc') }}
            </span>
          </div>
        </div>

        <!-- 状态分布卡片 -->
        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-purple-500">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">{{ $t('company.statusDistribution') }}</p>
              <p class="text-2xl font-bold">{{ Object.keys($t('company.statusOptions')).length || 0 }}</p>
            </div>
            <div class="bg-purple-100 p-2 rounded-full">
              <Icon icon="mdi:state-machine" class="text-2xl text-purple-500" />
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-500">
            <span class="flex items-center">
              <Icon icon="mdi:information-outline" class="mr-1" />
              {{ $t('company.statusDistributionDesc') }}
            </span>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 保险公司列表 -->
    <div class="bg-white rounded-lg shadow-md p-6 mt-4">
      <!-- 筛选器 -->
      <div class="filter-section mb-6 p-6 rounded-lg bg-gradient-to-r from-gray-50 to-gray-100 border border-gray-200">
        <div class="flex flex-wrap items-center justify-between gap-4">
          <div class="filter-group flex flex-wrap items-center gap-3">
            <span class="text-gray-600 text-sm">{{ $t('company.region') }}:</span>
            <a-select v-model:value="companyParams.region" :placeholder="$t('company.region')" style="width: 120px;"
              class="region-select" size="middle" @change="handleRegionChange">
              <a-select-option value="">{{ $t('common.all') }}</a-select-option>
              <a-select-option v-for="item in regionOptions" :key="item.code" :value="item.name">
                {{ item.name }}
              </a-select-option>
            </a-select>

            <span class="text-gray-600 text-sm">{{ $t('company.name') }}:</span>
            <a-select v-model:value="companyParams.companyCode" style="width: 180px" @change="handleCompanyFilterChange"
              allowClear show-search :filter-option="false" @search="handleCompanySearch" placeholder="选择保司"
              :options="filteredCompanyList">
            </a-select>
          </div>

          <div class="search-group flex items-center gap-3">
            <a-button type="primary" size="middle" @click="refreshCompanyData" class="refresh-btn">
              <template #icon>
                <Icon icon="mdi:refresh" />
              </template>
              <span>{{ $t('company.reset') }}</span>
            </a-button>
          </div>
        </div>
      </div>

      <!-- 保险公司表格 -->
      <a-table class="company-table" :dataSource="companyList" :columns="localizedCompanyColumns" :pagination="{
        total: companyTotal,
        current: companyParams.pageNum,
        pageSize: companyParams.pageSize,
        showSizeChanger: true,
        showQuickJumper: true,
        pageSizeOptions: ['10', '20', '50'],
        showTotal: (total) => tp('common.totalItems', { total }),
      }" @change="handleCompanyTableChange" size="middle"
        :rowClassName="(record, index) => (index % 2 === 1 ? 'table-striped' : '')" bordered :loading="companyLoading"
        :customRow="customRowHandler" @resizeColumn="handleResizeColumn">
        <template #bodyCell="{ column, record }">
          <!-- 公司名称列 -->
          <template v-if="column.dataIndex === 'name'">
            <div class="flex items-center">
              <div class="w-6 h-6 mr-2 flex-shrink-0">
                <img v-if="record.logoUrl" :src="record.logoUrl" :alt="record.name"
                  class="w-full h-full object-contain rounded company-logo" />
                <div v-else class="w-full h-full rounded logo-placeholder flex items-center justify-center">
                  <span class="text-xs text-gray-500">无</span>
                </div>
              </div>
              <a-tooltip :title="record.name">
                <span class="truncate">{{ record.name }}</span>
              </a-tooltip>
            </div>
          </template>

          <!-- 公司介绍列 -->
          <template v-if="column.dataIndex === 'description'">
            <a-tooltip placement="top" :title="record.description"
              :overlay-style="{ maxWidth: '500px', minWidth: '300px' }"
              :overlay-inner-style="{ whiteSpace: 'pre-wrap', textAlign: 'left', padding: '8px 12px', lineHeight: '1.5' }">
              <span class="cursor-help truncate block">{{ record.description }}</span>
            </a-tooltip>
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup>
import { Icon } from '@iconify/vue'
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { companyAPI } from '../../api';
import { message } from 'ant-design-vue';
import { ref, reactive, onMounted, computed } from 'vue';
import { useProductStore } from '@/store/modules/product';

// 获取产品store
const productStore = useProductStore();
// 获取格式化后的公司列表
const formattedCompanyList = computed(() => productStore.getFormattedCompanyList);
// 获取国际化功能
const { t, locale } = useI18n();

// 自定义带参数翻译函数
const tp = (key, params) => t(key, params);

// 地区选项列表
const regionOptions = ref([
  { code: 'HK', name: '香港' },
  { code: 'MO', name: '澳门' },
  { code: 'SG', name: '新加坡' },
  { code: 'BM', name: '百慕大' },
]);

// 保险公司数据列和配置 - 使用计算属性实现国际化
const companyColumns = ref([
  {
    title: 'company.name',
    dataIndex: 'name',
    key: 'name',
    ellipsis: true,
    width: 180,
    minWidth: 120,
    resizable: true,
  },
  {
    title: 'company.region',
    dataIndex: 'region',
    key: 'region',
    width: 100,
    minWidth: 80,
    resizable: true,
  },
  {
    title: 'company.description',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true,
    width: 350,
    minWidth: 200,
    resizable: true,
  },
]);

// 国际化表格列 - 使用全局混入提供的功能
const localizedCompanyColumns = computed(() => {
  return companyColumns.value.map(column => ({
    ...column,
    title: column.title ? t(column.title) : column.title
  }));
});

// 保险公司列表数据
const companyList = ref([]);
const companyLoading = ref(false);
const companyTotal = ref(0);
const companyList1 = ref([]);
const companyParams = reactive({
  pageNum: 1,
  pageSize: 10,
  search: '',
  region: '',
  companyCode: undefined
});

// 组件挂载时加载数据
onMounted(() => {
  loadCompanyData();
  productStore.getCompanyList();
});

// 筛选参数
const filterParams = reactive({
  companyCode: undefined,
  status: undefined,
  dateRange: [],
});

// 处理公司筛选变化
const handleCompanyFilterChange = (value) => {
  companyParams.companyCode = value;
  companyParams.pageNum = 1;
  companyParams.search = value;
  loadCompanyData();
};

// 处理状态筛选变化
const handleStatusFilterChange = (value) => {
  filterParams.status = value;
};

// 处理日期范围筛选变化
const handleDateRangeChange = (dates) => {
  filterParams.dateRange = dates;
};

// 应用筛选
const applyFilters = () => {
  // 合并筛选参数到查询参数
  companyParams.pageNum = 1;
  if (filterParams.companyCode) {
    companyParams.companyCode = filterParams.companyCode;
  }
  if (filterParams.status) {
    companyParams.status = filterParams.status;
  }
  if (filterParams.dateRange && filterParams.dateRange.length === 2) {
    companyParams.startDate = filterParams.dateRange[0].format('YYYY-MM-DD');
    companyParams.endDate = filterParams.dateRange[1].format('YYYY-MM-DD');
  }

  // 重新加载数据
  loadCompanyData();
};

// 重置筛选
const resetFilters = () => {
  // 重置筛选参数
  filterParams.companyCode = undefined;
  filterParams.status = undefined;
  filterParams.dateRange = [];

  // 重置查询参数中的筛选相关字段
  delete companyParams.companyCode;
  delete companyParams.status;
  delete companyParams.startDate;
  delete companyParams.endDate;

  // 重新加载数据
  loadCompanyData();
};

// 加载保险公司数据
const loadCompanyData = async () => {
  try {
    companyLoading.value = true;
    const result = await companyAPI.getCompanyList(companyParams);
    companyList.value = result.records || [];
    companyTotal.value = result.total || 0;
  } catch (error) {
    console.error('加载保险公司数据失败:', error);
    message.error(t('company.loadError'));
  } finally {
    companyLoading.value = false;
  }
};
// 加载公司列表
const loadCompanyList1 = async () => {
  const companyList1 = await companyAPI.getCompanyPage();
};
// 搜索关键词
const searchCompanyKeyword = ref('');

// 处理公司搜索
const handleCompanySearch = (value) => {
  searchCompanyKeyword.value = value;
};

// 获取过滤后的公司列表
const filteredCompanyList = computed(() => {
  if (!searchCompanyKeyword.value) {
    return formattedCompanyList.value;
  }

  const keyword = searchCompanyKeyword.value.toLowerCase();
  return formattedCompanyList.value.filter(item => {
    return item.label.toLowerCase().includes(keyword);
  });
});

// 搜索保险公司
const searchCompany = (value) => {
  companyParams.search = value;
  companyParams.pageNum = 1;
  loadCompanyData();
};

// 刷新保险公司数据
const refreshCompanyData = () => {
  companyParams.region = '';
  companyParams.search = '';
  companyParams.pageNum = 1;
  loadCompanyData();
};

// 处理保险公司表格分页变化
const handleCompanyTableChange = (pagination, filters, sorter) => {
  companyParams.pageNum = pagination.current;
  companyParams.pageSize = pagination.pageSize;
  loadCompanyData();
};

const router = useRouter();

// 导航到保险公司详情页面
const navigateToCompanyDetail = (record) => {
  router.push(`/company/${record.code}`);
};

// 处理地区变化
const handleRegionChange = (value) => {
  companyParams.region = value;
  companyParams.pageNum = 1;
  loadCompanyData();
};

// 自定义行点击处理函数
const customRowHandler = (record) => {
  return {
    onClick: () => {
      navigateToCompanyDetail(record);
    },
    style: 'cursor: pointer;'
  };
};

// 处理列宽度调整
const handleResizeColumn = (w, col) => {
  // 查找并更新对应列的宽度
  const targetColumn = companyColumns.value.find(column => column.key === col.key);
  if (targetColumn) {
    targetColumn.width = w;
  }
};

// 自定义过滤函数
const filterCompany = (input, option) => {
  // 防止空输入
  if (!input) return true;

  try {
    // 尝试获取选项文本的不同方式
    let optionText = '';

    // 方法1：直接从DOM元素获取文本
    if (option.el) {
      optionText = option.el.textContent || '';
    }
    // 方法2：从选项值映射到公司名称
    else {
      const company = formattedCompanyList.value.find(item => item.code === option.value);
      if (company) {
        optionText = company.name;
      }
    }

    return optionText.toLowerCase().includes(input.toLowerCase());
  } catch (error) {
    console.error('过滤选项出错:', error);
    return false;
  }
};
</script>

<style scoped>
/* 主题色定义 */
:root {
  --color-primary: #3B82F6;
  --color-primary-hover: #2563EB;
  --color-secondary: #6366F1;
  --color-success: #10B981;
  --color-warning: #F59E0B;
  --color-error: #EF4444;
  --color-info: #6B7280;
}

/* 标题区域动画 */
.title-section {
  background-size: 200% 200%;
  animation: gradientAnimation 5s ease infinite;
}

@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

.page-title {
  color: #fff;
}

.page-description {
  color: #e0e0e0;
}

/* 统计卡片样式 */
.stat-card {
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 筛选区域样式 */
.filter-section {
  transition: all 0.3s ease;
}

/* 表格样式 */
:deep(.ant-table-pagination) {
  margin: 24px 0;
}

/* 表格条纹样式 */
:deep(.table-striped) {
  background-color: rgba(241, 245, 249, 0.5);
}

/* 行高 */
:deep(.ant-table-tbody > tr > td) {
  padding: 12px;
  font-size: 14px;
}

/* 悬停效果 */
:deep(.ant-table-tbody > tr:hover > td) {
  background-color: rgba(236, 240, 255, 0.7) !important;
}

/* 表头样式 */
:deep(.ant-table-thead > tr > th) {
  padding: 12px;
  font-size: 14px;
  font-weight: 600;
  background-color: #f8fafc;
}

/* 按钮样式 */
.refresh-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  transform: scale(1.05);
}

/* 公司logo样式 */
.company-logo {
  transition: transform 0.2s ease;
}

.company-logo:hover {
  transform: scale(1.1);
}

/* logo占位符样式 */
.logo-placeholder {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border: 1px solid #e1e5e9;
}

/* 响应式布局调整 */
@media (max-width: 768px) {
  .filter-section {
    flex-direction: column;
  }

  .filter-group,
  .search-group {
    width: 100%;
    margin-bottom: 12px;
  }
}

/* 确保所有按钮的图标和文本垂直居中对齐 */
:deep(.ant-btn) {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

:deep(.ant-btn .anticon),
:deep(.ant-btn .iconify) {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  line-height: 1 !important;
  vertical-align: middle !important;
}

:deep(.ant-btn .anticon + span),
:deep(.ant-btn .iconify + span),
:deep(.ant-btn span + .anticon),
:deep(.ant-btn span + .iconify) {
  margin-left: 8px !important;
}
</style>