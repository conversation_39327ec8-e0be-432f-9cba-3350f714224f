<template>
    <!-- 主要内容区域 -->
    <div class="container mx-auto py-6 px-4">
        <!-- 页面标题 -->
        <div
            class="title-section mb-6 p-6 rounded-lg shadow-md text-white bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700">
            <div class="flex items-center">
                <Icon icon="mdi:account-group" class="text-4xl mr-3" />
                <h1 class="text-2xl font-bold page-title text-white">{{ $t('customer.customerList') }}</h1>
            </div>
            <p class="mt-2 page-description">
                {{ $t('customer.browseAndManage') }}
            </p>
        </div>

        <!-- 统计卡片 -->
        <a-card class="mb-6 rounded-lg" :bordered="false">
            <h1 class="text-2xl font-bold mb-4">{{ $t('customer.statistics') }}</h1>
            <!-- 客户统计卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- 客户总数卡片 -->
                <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-blue-500">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-500 text-sm">{{ $t('customer.totalCustomers') }}</p>
                            <p class="text-2xl font-bold">{{ customerTotal || 0 }}</p>
                        </div>
                        <div class="bg-blue-100 p-2 rounded-full">
                            <Icon icon="mdi:account-multiple" class="text-2xl text-blue-500" />
                        </div>
                    </div>
                    <div class="mt-2 text-xs text-gray-500">
                        <span class="flex items-center">
                            <Icon icon="mdi:information-outline" class="mr-1" />
                            {{ $t('customer.totalCustomersDesc') }}
                        </span>
                    </div>
                </div>

                <!-- 男性客户卡片 -->
                <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-green-500">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <p class="text-gray-500 text-sm">{{ $t('customer.maleCustomers') }}</p>
                            <p class="text-2xl font-bold">{{ maleCustomerCount || 0 }}</p>
                        </div>
                        <div class="bg-green-100 p-2 rounded-full">
                            <Icon icon="mdi:gender-male" class="text-2xl text-green-500" />
                        </div>
                    </div>
                    <div class="mt-2 text-xs text-gray-500">
                        <span class="flex items-center">
                            <Icon icon="mdi:information-outline" class="mr-1" />
                            {{ $t('customer.maleCustomersDesc') }}
                        </span>
                    </div>
                </div>

                <!-- 女性客户卡片 -->
                <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-pink-500">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-500 text-sm">{{ $t('customer.femaleCustomers') }}</p>
                            <p class="text-2xl font-bold">{{ femaleCustomerCount || 0 }}</p>
                        </div>
                        <div class="bg-pink-100 p-2 rounded-full">
                            <Icon icon="mdi:gender-female" class="text-2xl text-pink-500" />
                        </div>
                    </div>
                    <div class="mt-2 text-xs text-gray-500">
                        <span class="flex items-center">
                            <Icon icon="mdi:information-outline" class="mr-1" />
                            {{ $t('customer.femaleCustomersDesc') }}
                        </span>
                    </div>
                </div>

                <!-- 活跃客户卡片 -->
                <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-purple-500">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-500 text-sm">{{ $t('customer.activeCustomers') }}</p>
                            <p class="text-2xl font-bold">{{ activeCustomerCount || 0 }}</p>
                        </div>
                        <div class="bg-purple-100 p-2 rounded-full">
                            <Icon icon="mdi:account-check" class="text-2xl text-purple-500" />
                        </div>
                    </div>
                    <div class="mt-2 text-xs text-gray-500">
                        <span class="flex items-center">
                            <Icon icon="mdi:information-outline" class="mr-1" />
                            {{ $t('customer.activeCustomersDesc') }}
                        </span>
                    </div>
                </div>
            </div>
        </a-card>

        <!-- 客户列表 -->
        <div class="bg-white rounded-lg shadow-md p-6 mt-4">
            <!-- 筛选器 -->
            <div
                class="filter-section mb-6 p-6 rounded-lg bg-gradient-to-r from-gray-50 to-gray-100 border border-gray-200">
                <div class="flex flex-wrap items-center justify-between gap-4">
                    <div class="filter-group flex flex-wrap items-center gap-3">
                        <span class="text-gray-600 text-sm">{{ $t('customer.gender') }}:</span>
                        <a-select v-model:value="customerParams.gender" :placeholder="$t('customer.gender')"
                            style="width: 100px;" class="gender-select" size="middle" @change="handleGenderChange">
                            <a-select-option value="">{{ $t('customer.all') }}</a-select-option>
                            <a-select-option value="1">{{ $t('customer.male') }}</a-select-option>
                            <a-select-option value="2">{{ $t('customer.female') }}</a-select-option>
                        </a-select>

                        <span class="text-gray-600 text-sm">{{ $t('customer.status') }}:</span>
                        <a-select v-model:value="customerParams.status" :placeholder="$t('customer.status')"
                            style="width: 100px;" class="status-select" size="middle" @change="handleStatusChange">
                            <a-select-option value="">{{ $t('customer.all') }}</a-select-option>
                            <a-select-option value="1">{{ $t('customer.active') }}</a-select-option>
                            <a-select-option value="0">{{ $t('customer.inactive') }}</a-select-option>
                        </a-select>
                    </div>

                    <div class="search-group flex items-center gap-3">
                        <a-input-search :placeholder="$t('customer.search')" style="width: 280px" class="mr-3"
                            size="middle" @search="searchCustomer" />
                        <a-button type="primary" size="middle" @click="refreshCustomerData">
                            <template #icon>
                                <Icon icon="mdi:refresh" />
                            </template>
                            <span>{{ $t('customer.reset') }}</span>
                        </a-button>
                        <a-button type="primary" size="middle">
                            <template #icon>
                                <Icon icon="mdi:plus" class="text-white" />
                            </template>
                            <span class="ml-1">{{ $t('customer.addCustomer') }}</span>
                        </a-button>
                    </div>
                </div>
            </div>

            <!-- 客户表格 -->
            <a-table :dataSource="customerList" :columns="localizedCustomerColumns" :pagination="{
                total: customerTotal,
                current: customerParams.pageNum,
                pageSize: customerParams.pageSize,
                showSizeChanger: true,
                showQuickJumper: true,
                pageSizeOptions: ['10', '20', '50'],
                showTotal: (total) => `${$t('common.totalItems', { total })}`,
                size: 'middle'
            }" @change="handleCustomerTableChange" size="middle"
                :rowClassName="(record, index) => (index % 2 === 1 ? 'table-striped' : '')" bordered
                :loading="customerLoading" :customRow="customRowHandler" @resizeColumn="handleResizeColumn">
                <template #bodyCell="{ column, record }">
                    <!-- 性别列 -->
                    <template v-if="column.dataIndex === 'gender'">
                        <a-tag :color="record.gender === 1 ? 'blue' : 'pink'">
                            {{ record.gender === 1 ? $t('customer.male') : $t('customer.female') }}
                        </a-tag>
                    </template>

                    <!-- 生日列 -->
                    <template v-if="column.dataIndex === 'birthday'">
                        {{ formatDate(record.birthday) }}
                    </template>

                    <!-- 状态列 -->
                    <template v-if="column.dataIndex === 'status'">
                        <a-badge :status="record.status === 1 ? 'success' : 'error'"
                            :text="record.status === 1 ? $t('customer.active') : $t('customer.inactive')" />
                    </template>

                    <!-- 操作列 -->
                    <!-- <template v-if="column.dataIndex === 'action'">
                        <div class="flex items-center justify-center space-x-2">
                            <a-tooltip :title="$t('customer.editInfo')">
                                <a-button type="link" size="small">
                                    <template #icon>
                                        <Icon icon="mdi:pencil-outline" class="text-gray-500 text-xl" />
                                    </template>
                                </a-button>
                            </a-tooltip>
                            <a-tooltip :title="$t('customer.viewDetails')">
                                <a-button type="link" size="small" @click="showCustomerDetail(record)">
                                    <template #icon>
                                        <Icon icon="mdi:information-outline" class="text-gray-500 text-xl" />
                                    </template>
                                </a-button>
                            </a-tooltip>
                        </div>
                    </template> -->
                </template>
            </a-table>
        </div>
    </div>

    <!-- 客户详情抽屉 -->
    <a-drawer :visible="drawerVisible" placement="right" :width="600" @close="closeDrawer"
        :title="selectedCustomer?.name" :bodyStyle="{ paddingBottom: '80px' }">
        <template v-if="selectedCustomer">
            <!-- 显示加载状态 -->
            <div v-if="customerDetailLoading" class="flex justify-center items-center h-64">
                <a-spin size="large" />
            </div>

            <template v-else>
                <!-- 客户基本信息 -->
                <div class="mb-8">
                    <h3 class="text-lg font-medium mb-4">{{ $t('customer.basicInfo') }}</h3>
                    <div class="space-y-4 bg-gray-50 p-4 rounded-lg">
                        <div class="grid grid-cols-2 gap-4">
                            <div class="flex items-center">
                                <span class="text-gray-500 w-24">{{ $t('customer.name') }}：</span>
                                <span class="font-medium">{{ selectedCustomer.name }}</span>
                            </div>
                            <div class="flex items-center">
                                <span class="text-gray-500 w-24">{{ $t('customer.gender') }}：</span>
                                <a-tag :color="selectedCustomer.gender === 1 ? 'blue' : 'pink'">
                                    {{ selectedCustomer.gender === 1 ? $t('customer.male') : $t('customer.female') }}
                                </a-tag>
                            </div>
                            <div class="flex items-center">
                                <span class="text-gray-500 w-24">{{ $t('customer.phone') }}：</span>
                                <span>{{ selectedCustomer.phone }}</span>
                            </div>
                            <div class="flex items-center">
                                <span class="text-gray-500 w-24">{{ $t('customer.birthday') }}：</span>
                                <span>{{ formatDate(selectedCustomer.birthday) }}</span>
                            </div>
                            <div class="flex items-center">
                                <span class="text-gray-500 w-24">{{ $t('customer.idCard') }}：</span>
                                <span>{{ selectedCustomer.idCard }}</span>
                            </div>
                            <div class="flex items-center">
                                <span class="text-gray-500 w-24">{{ $t('customer.status') }}：</span>
                                <a-badge :status="selectedCustomer.status === 1 ? 'success' : 'error'"
                                    :text="selectedCustomer.status === 1 ? $t('customer.active') : $t('customer.inactive')" />
                            </div>
                        </div>
                        <div class="flex items-start">
                            <span class="text-gray-500 w-24">{{ $t('customer.address') }}：</span>
                            <span>{{ selectedCustomer.address }}</span>
                        </div>
                        <div class="flex items-start">
                            <span class="text-gray-500 w-24">{{ $t('customer.remark') }}：</span>
                            <span>{{ selectedCustomer.remark || $t('customer.none') }}</span>
                        </div>
                        <div class="flex items-start">
                            <span class="text-gray-500 w-24">{{ $t('customer.createTime') }}：</span>
                            <span>{{ formatDate(selectedCustomer.createTime) }}</span>
                        </div>
                        <div class="flex items-start">
                            <span class="text-gray-500 w-24">{{ $t('common.updateTime') }}：</span>
                            <span>{{ formatDate(selectedCustomer.updateTime) }}</span>
                        </div>
                    </div>
                </div>

                <!-- 相关保单信息 -->
                <div class="mb-8">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium">{{ $t('customer.relatedPolicies') }}</h3>
                        <a-button type="link">{{ $t('customer.viewAll') }}</a-button>
                    </div>
                    <a-empty v-if="!selectedCustomer.policies?.length" :description="$t('customer.noPolicies')" />
                    <template v-else>
                        <a-list size="small" bordered>
                            <a-list-item v-for="policy in selectedCustomer.policies" :key="policy.id">
                                <a-list-item-meta>
                                    <template #title>
                                        <a href="javascript:;">{{ policy.policyName }}</a>
                                    </template>
                                    <template #description>
                                        <span>{{ $t('customer.policyNo') }}: {{ policy.policyNo }}</span>
                                    </template>
                                </a-list-item-meta>
                                <template #extra>
                                    <span>{{ formatDate(policy.startDate) }} ~ {{ formatDate(policy.endDate) }}</span>
                                </template>
                            </a-list-item>
                        </a-list>
                    </template>
                </div>
            </template>
        </template>
    </a-drawer>
</template>

<script setup>
import { Icon } from '@iconify/vue';
import { ref, onMounted, reactive, computed } from 'vue';
import { message } from 'ant-design-vue';
import { useI18n } from 'vue-i18n';
// 引入客户API
import { customerAPI } from '../api';

// 获取国际化功能
const { t } = useI18n();

// 客户列表参数
const customerParams = reactive({
    page: 1,
    pageSize: 10,
    gender: '',
    status: '',
    keyword: ''
});

// 表格列定义
const customerColumns = ref([
    {
        title: 'customer.serialNumber',
        dataIndex: 'index',
        width: 60,
        customRender: ({ index }) => index + 1,
        resizable: true,
        minWidth: 60,
    },
    {
        title: 'customer.name',
        dataIndex: 'name',
        width: 100,
        ellipsis: true,
        resizable: true,
        minWidth: 80,
    },
    {
        title: 'customer.phone',
        dataIndex: 'phone',
        width: 120,
        ellipsis: true,
        resizable: true,
        minWidth: 120,
    },
    {
        title: 'customer.gender',
        dataIndex: 'gender',
        width: 60,
        resizable: true,
        minWidth: 60,
    },
    {
        title: 'customer.birthday',
        dataIndex: 'birthday',
        width: 100,
        resizable: true,
        minWidth: 100,
    },
    {
        title: 'customer.idCard',
        dataIndex: 'idCard',
        width: 160,
        ellipsis: true,
        resizable: true,
        minWidth: 160,
    },
    {
        title: 'customer.status',
        dataIndex: 'status',
        width: 80,
        resizable: true,
        minWidth: 80,
    },
    {
        title: 'customer.createTime',
        dataIndex: 'createTime',
        width: 120,
        customRender: ({ text }) => formatDate(text),
        resizable: true,
        minWidth: 120,
    },

]);

// 国际化表格列
const localizedCustomerColumns = computed(() => {
    return customerColumns.value.map(column => ({
        ...column,
        title: column.title ? t(column.title) : column.title
    }));
});

// 格式化日期
const formatDate = (timestamp) => {
    if (!timestamp) return '—';
    const date = new Date(timestamp);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

// 客户列表数据
const customerList = ref([]);
const customerTotal = ref(0);
const customerLoading = ref(false);

// 客户统计数据
const maleCustomerCount = ref(0);
const femaleCustomerCount = ref(0);
const activeCustomerCount = ref(0);

const loadCustomerList = async () => {
    customerLoading.value = true;
    try {
        // 替换为实际API调用
        const params = {
            page: customerParams.page,
            pageSize: customerParams.pageSize
        };

        // 如果有过滤条件，添加到请求参数中
        if (customerParams.gender !== '') {
            params.gender = customerParams.gender;
        }

        if (customerParams.status !== '') {
            params.status = customerParams.status;
        }

        if (customerParams.keyword) {
            params.keyword = customerParams.keyword;
        }

        const response = await customerAPI.getCustomerList(params);

        if (response) {
            customerList.value = response.records || [];
            customerTotal.value = response.total || 0;

            // 计算统计数据
            maleCustomerCount.value = (response.records || []).filter(item => item.gender === 1).length;
            femaleCustomerCount.value = (response.records || []).filter(item => item.gender === 2).length;
            activeCustomerCount.value = (response.records || []).filter(item => item.status === 1).length;
        }
    } catch (error) {
        console.error(t('customer.loadFailed'), error);
        message.error(t('customer.loadFailed'));

    } finally {
        customerLoading.value = false;
    }
};

// 刷新客户数据
const refreshCustomerData = () => {
    customerParams.gender = '';
    customerParams.status = '';
    customerParams.keyword = '';
    customerParams.page = 1;
    loadCustomerList();
};

// 处理表格变化
const handleCustomerTableChange = (pagination) => {
    customerParams.page = pagination.current;
    customerParams.pageSize = pagination.pageSize;
    loadCustomerList();
};

// 处理性别选择变化
const handleGenderChange = () => {
    customerParams.page = 1;
    loadCustomerList();
};

// 处理状态选择变化
const handleStatusChange = () => {
    customerParams.page = 1;
    loadCustomerList();
};

// 搜索客户
const searchCustomer = (value) => {
    customerParams.keyword = value;
    customerParams.page = 1;
    loadCustomerList();
};

// 客户详情相关
const drawerVisible = ref(false);
const selectedCustomer = ref(null);
const customerDetailLoading = ref(false);

// 显示客户详情
const showCustomerDetail = async (customer) => {
    drawerVisible.value = true;
    customerDetailLoading.value = true;
    try {
        // 使用实际API调用
        const response = await customerAPI.getCustomerDetail(customer.id);
        selectedCustomer.value = response.data || response;
    } catch (error) {
        console.error(t('customer.detailFailed'), error);
        message.error(t('customer.detailFailed'));

        // 出错时使用模拟数据（可在正式环境中移除）
        selectedCustomer.value = {
            ...customer,
            policies: [] // 假设没有关联保单
        };
    } finally {
        customerDetailLoading.value = false;
    }
};

// 关闭详情抽屉
const closeDrawer = () => {
    drawerVisible.value = false;
    selectedCustomer.value = null;
};

// 自定义行点击处理函数
const customRowHandler = (record) => {
    return {
        onClick: () => {
            showCustomerDetail(record);
        },
        style: 'cursor: pointer;'
    };
};

// 处理列宽度调整
const handleResizeColumn = (w, col) => {
    // 查找并更新对应列的宽度
    const targetColumn = customerColumns.value.find(column => column.dataIndex === col.dataIndex);
    if (targetColumn) {
        targetColumn.width = w;
    }
};

// 组件挂载时加载数据
onMounted(() => {
    loadCustomerList();
});
</script>

<style scoped>
/* 标题区域样式 */
.title-section {
    transition: all 0.3s ease;
}

.title-section:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.page-title {
    position: relative;
    display: inline-block;
    color: white;
}

.page-title::after {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 0;
    width: 40px;
    height: 3px;
    background-color: white;
    border-radius: 3px;
}

.page-description {
    max-width: 600px;
    opacity: 0.9;
}

/* 统计卡片样式 */
.stat-card {
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

/* 表格条纹样式 */
:deep(.table-striped) {
    background-color: var(--bg-tertiary);
}

/* 表格hover样式 */
:deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
    background-color: var(--bg-secondary);
}

/* 表格列宽调整样式 */
:deep(.react-resizable) {
    position: relative;
    background-clip: padding-box;
}

:deep(.react-resizable-handle) {
    position: absolute;
    right: -5px;
    bottom: 0;
    z-index: 1;
    width: 10px;
    height: 100%;
    cursor: col-resize;
}

/* 表头样式 */
:deep(.ant-table-thead > tr > th) {
    padding: 8px 12px;
    font-size: 13px;
    font-weight: 600;
}

/* 表格内容截断样式 */
:deep(.ant-table-cell) {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 选择器样式覆盖 */
:deep(.ant-select-selector) {
    border-radius: 6px !important;
}

/* 输入框样式覆盖 */
:deep(.ant-input-affix-wrapper) {
    border-radius: 6px !important;
}

/* 按钮样式覆盖 */
:deep(.ant-btn) {
    border-radius: 6px !important;
}

/* 修复按钮中图标和文字的对齐问题 */
:deep(.ant-btn .anticon) {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    vertical-align: middle;
}

:deep(.ant-btn) {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

:deep(.ant-btn > span) {
    display: inline-flex;
    align-items: center;
}

/* 筛选器动画 */
.filter-section {
    transition: all 0.3s ease;
}

.filter-section:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* 刷新按钮动画 */
.refresh-btn {
    transition: transform 0.3s ease;
}

.refresh-btn:hover {
    transform: rotate(180deg);
}
</style>