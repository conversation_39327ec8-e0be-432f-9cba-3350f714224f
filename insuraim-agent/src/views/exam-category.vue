<template>
  <div class="container mx-auto py-6 px-4">
    <!-- 页面标题 -->
    <div
      class="title-section mb-6 p-6 rounded-lg shadow-md text-white bg-gradient-to-r from-purple-500 via-purple-600 to-purple-700">
      <div class="flex items-center">
        <Icon icon="mdi:book-education" class="text-4xl mr-3" />
        <h1 class="text-2xl font-bold page-title text-white">模拟考试</h1>
      </div>
      <p class="mt-2 page-description">
        请选择以下考试类型开始模拟测试，检验您的知识掌握程度
      </p>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="flex justify-center items-center py-20">
      <a-spin size="large" />
      <span class="ml-3 text-gray-600">加载考试类目中...</span>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="bg-white rounded-lg shadow-md p-6 mb-6">
      <div class="flex flex-col items-center">
        <Icon icon="mdi:alert-circle" class="text-5xl text-red-500 mb-3" />
        <span class="text-red-600 text-lg mb-4">加载考试类目失败，请稍后重试</span>
        <a-button type="primary" @click="fetchCategories">
          <template #icon>
            <Icon icon="mdi:refresh" />
          </template>
          重新加载
        </a-button>
      </div>
    </div>

    <!-- 主要内容区 -->
    <div v-if="!loading && !error">
      <!-- 统计卡片 -->
      <a-card class="mb-6 rounded-lg" :bordered="false" v-if="categories.length > 0">
        <h1 class="text-2xl font-bold mb-4">统计信息</h1>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <!-- 考试类型总数 -->
          <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-purple-500">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-gray-500 text-sm">考试类型总数</p>
                <p class="text-2xl font-bold">{{ categories.length }}</p>
              </div>
              <div class="bg-purple-100 p-2 rounded-full">
                <Icon icon="mdi:book-open-page-variant" class="text-2xl text-purple-500" />
              </div>
            </div>
            <div class="mt-2 text-xs text-gray-500">
              <span class="flex items-center">
                <Icon icon="mdi:information-outline" class="mr-1" />
                系统内所有可用考试类型数量
              </span>
            </div>
          </div>

          <!-- 题目总数 -->
          <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-blue-500">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-gray-500 text-sm">题目总数</p>
                <p class="text-2xl font-bold">{{ getTotalQuestions() }}</p>
              </div>
              <div class="bg-blue-100 p-2 rounded-full">
                <Icon icon="mdi:help-circle-outline" class="text-2xl text-blue-500" />
              </div>
            </div>
            <div class="mt-2 text-xs text-gray-500">
              <span class="flex items-center">
                <Icon icon="mdi:information-outline" class="mr-1" />
                所有考试包含的题目总数
              </span>
            </div>
          </div>

          <!-- 平均时长 -->
          <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-green-500">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-gray-500 text-sm">平均时长</p>
                <p class="text-2xl font-bold">{{ getAverageDuration() }}分钟</p>
              </div>
              <div class="bg-green-100 p-2 rounded-full">
                <Icon icon="mdi:clock-time-four-outline" class="text-2xl text-green-500" />
              </div>
            </div>
            <div class="mt-2 text-xs text-gray-500">
              <span class="flex items-center">
                <Icon icon="mdi:information-outline" class="mr-1" />
                每场考试的平均时长
              </span>
            </div>
          </div>

          <!-- 完成情况 -->
          <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-amber-500">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-gray-500 text-sm">已完成考试</p>
                <p class="text-2xl font-bold">{{ getCompletedExams() }}</p>
              </div>
              <div class="bg-amber-100 p-2 rounded-full">
                <Icon icon="mdi:check-circle-outline" class="text-2xl text-amber-500" />
              </div>
            </div>
            <div class="mt-2 text-xs text-gray-500">
              <span class="flex items-center">
                <Icon icon="mdi:information-outline" class="mr-1" />
                您已完成的模拟考试次数
              </span>
            </div>
          </div>
        </div>
      </a-card>

      <!-- 筛选区域 -->
      <div class="bg-white rounded-lg shadow-md p-6 mb-6 mt-4" v-if="categories.length > 0">
        <div class="flex flex-wrap items-center justify-between gap-4">
          <h2 class="text-xl font-semibold text-gray-800">考试类目 ({{ categories.length }})</h2>

          <div class="search-group flex items-center gap-3">
            <a-input-search placeholder="搜索考试名称" style="width: 280px" class="mr-3" size="middle" @search="searchExam" />
            <a-button type="primary" size="middle" @click="fetchCategories">
              <template #icon>
                <Icon icon="mdi:refresh" />
              </template>
              <span>刷新</span>
            </a-button>
          </div>
        </div>
      </div>

      <!-- 类目列表 -->
      <div class="bg-white rounded-lg shadow-md p-6" v-if="filteredCategories.length > 0">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div v-for="category in filteredCategories" :key="category.id"
            class="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-lg transition-all card-hover"
            @click="startExam(category.id)">
            <div class="p-4 border-b border-gray-100">
              <div class="flex justify-between items-start mb-4">
                <h3 class="text-xl font-semibold text-gray-800">{{ category.examName }}</h3>
                <div class="bg-purple-100 text-purple-700 px-3 py-1 rounded-lg text-sm font-medium">
                  {{ category.questionCount > 0 ? category.questionCount : '未知' }}题
                </div>
              </div>

              <div class="flex space-x-2 mb-3">
                <div class="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm flex items-center">
                  <Icon icon="mdi:clock-outline" class="mr-2 text-base" />
                  {{ category.duration }}分钟
                </div>
                <div class="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm flex items-center">
                  <Icon icon="mdi:book-outline" class="mr-2 text-base" />
                  {{ category.examType }}
                </div>
              </div>

              <p class="text-gray-600 text-sm mb-4 line-clamp-2">
                {{ category.description || '本模拟考试内容涵盖相关领域的重要知识点，旨在帮助您巩固所学知识。' }}
              </p>
            </div>

            <div class="p-4 bg-gray-50">
              <a-button type="primary" class="w-full flex items-center justify-center">
                <template #icon>
                  <Icon icon="mdi:file-document-edit-outline" />
                </template>
                开始考试
              </a-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && !error && filteredCategories.length === 0"
        class="flex flex-col items-center justify-center py-20 bg-white rounded-lg shadow-md">
        <Icon icon="mdi:book-open-variant" class="text-6xl text-gray-400 mb-4" />
        <p class="text-gray-600 mb-4">暂无可用的考试类目</p>
        <a-button type="primary" @click="fetchCategories">刷新数据</a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { Icon } from '@iconify/vue';
import { examAPI } from '@/api/index';

// 路由实例
const router = useRouter();

// 状态变量
const categories = ref([]);
const loading = ref(false);
const error = ref(false);
const searchKeyword = ref('');

// 筛选后的类目列表
const filteredCategories = computed(() => {
  if (!searchKeyword.value) return categories.value;
  return categories.value.filter(category =>
    category.examName.toLowerCase().includes(searchKeyword.value.toLowerCase())
  );
});

// 统计函数
const getTotalQuestions = () => {
  return categories.value.reduce((total, category) => total + (category.questionCount || 0), 0);
};

const getAverageDuration = () => {
  if (categories.value.length === 0) return 0;
  const totalDuration = categories.value.reduce((total, category) => total + (category.duration || 0), 0);
  return Math.round(totalDuration / categories.value.length);
};

const getCompletedExams = () => {
  // 这里是模拟数据，实际情况应从API获取
  return Math.floor(Math.random() * 10);
};

// 搜索考试
const searchExam = (value) => {
  searchKeyword.value = value;
};

// 获取考试类目列表
const fetchCategories = async () => {
  loading.value = true;
  error.value = false;
  searchKeyword.value = '';

  try {
    const data = await examAPI.getCategoryList();
    categories.value = data;
    message.success('考试类目加载成功');
  } catch (err) {
    console.error('获取考试类目失败', err);
    error.value = true;
    message.error('获取考试类目失败');
  } finally {
    loading.value = false;
  }
};

// 开始考试
const startExam = (categoryId) => {
  // 从categories中找到对应的类目信息
  const category = categories.value.find(c => c.id === categoryId);

  router.push({
    path: '/mock-exam',
    query: {
      categoryId,
      examName: category ? category.examName : ''
    }
  });

  message.success(`开始${category ? category.examName : ''}考试`);
};

// 组件挂载时获取类目列表
onMounted(() => {
  fetchCategories();
});
</script>

<style scoped>
/* 标题区域样式 */
.title-section {
  transition: all 0.3s ease;
}

.title-section:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.page-title {
  position: relative;
  display: inline-block;
  color: white;
}

.page-title::after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: white;
  border-radius: 3px;
}

.page-description {
  max-width: 600px;
  opacity: 0.9;
}

/* 统计卡片样式 */
.stat-card {
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

/* 卡片悬停效果 */
.card-hover {
  transition: all 0.3s ease;
  cursor: pointer;
}

.card-hover:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border-color: #e5e7eb;
}

/* 限制文本行数 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 修复Ant Design组件中图标和文字的对齐问题 */
:deep(.ant-btn) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

:deep(.ant-btn .anticon),
:deep(.ant-btn .iconify) {
  display: inline-flex;
  align-self: center;
  line-height: 0;
}
</style>