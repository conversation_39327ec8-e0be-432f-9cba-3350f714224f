<template>
    <div class="mobile-beneficiary-form">
        <div class="form-header">
            <h3 class="form-section-title">受益人信息</h3>
            <a-button type="primary" @click="addBeneficiary" size="small" class="add-btn">添加受益人</a-button>
        </div>

        <div v-if="beneficiaries.length === 0" class="empty-state">
            <p>暂无受益人信息，请点击"添加受益人"按钮添加</p>
        </div>

        <div v-for="(beneficiary, index) in beneficiaries" :key="index" class="beneficiary-card">
            <div class="beneficiary-header">
                <h4>受益人 #{{ index + 1 }}</h4>
                <a-button type="text" danger @click="removeBeneficiary(index)" class="remove-btn">
                    <template #icon>
                        <Icon icon="material-symbols:delete-outline" />
                    </template>
                </a-button>
            </div>

            <a-form :ref="(el) => { if (el) formRefs[index] = el }" :model="beneficiary" layout="vertical"
                :rules="rules" :validate-trigger="['blur', 'change']">
                <a-form-item name="name" label="受益人姓名" required>
                    <a-input :value="beneficiary.name" @update:value="val => updateBeneficiaryData(index, 'name', val)"
                        placeholder="请输入受益人姓名" />
                </a-form-item>

                <a-form-item name="gender" label="性别" required>
                    <a-radio-group :value="beneficiary.gender"
                        @update:value="val => updateBeneficiaryData(index, 'gender', val)">
                        <a-radio :value="1">男</a-radio>
                        <a-radio :value="0">女</a-radio>
                    </a-radio-group>
                </a-form-item>

                <a-form-item name="relationship" label="与受保人关系" required>
                    <a-select :value="beneficiary.relationship"
                        @update:value="val => updateBeneficiaryData(index, 'relationship', val)"
                        placeholder="请选择与受保人关系">
                        <a-select-option value="本人">本人</a-select-option>
                        <a-select-option value="配偶">配偶</a-select-option>
                        <a-select-option value="子女">子女</a-select-option>
                        <a-select-option value="父母">父母</a-select-option>
                        <a-select-option value="兄弟姐妹">兄弟姐妹</a-select-option>
                        <a-select-option value="其他亲属">其他亲属</a-select-option>
                        <a-select-option value="朋友">朋友</a-select-option>
                        <a-select-option value="其他">其他</a-select-option>
                    </a-select>
                </a-form-item>

                <a-form-item name="idCardNo" label="身份证号码">
                    <a-input :value="beneficiary.idCardNo" placeholder="请输入身份证号码"
                        @update:value="val => updateBeneficiaryData(index, 'idCardNo', val)" />
                </a-form-item>

                <a-form-item name="benefitPercentage" label="受益比例(%)" required>
                    <a-input-number :value="beneficiary.benefitPercentage"
                        @update:value="val => updateBeneficiaryData(index, 'benefitPercentage', val)"
                        style="width: 100%" placeholder="请输入受益比例(%)" :precision="2" :min="0" :max="100" />
                </a-form-item>

                <a-form-item name="isTrustee" label="是否为信托人" required>
                    <a-radio-group :value="beneficiary.isTrustee"
                        @update:value="val => updateBeneficiaryData(index, 'isTrustee', val)">
                        <a-radio :value="1">是</a-radio>
                        <a-radio :value="0">否</a-radio>
                    </a-radio-group>
                </a-form-item>

            </a-form>
        </div>

        <div v-if="beneficiaries.length > 0" class="percentage-summary" :class="{ 'error': totalPercentage !== 100 }">
            <div class="percentage-bar">
                <div class="percentage-progress"
                    :style="{ width: `${totalPercentage > 100 ? 100 : totalPercentage}%`, backgroundColor: totalPercentage === 100 ? '#52c41a' : '#faad14' }">
                </div>
            </div>
            <p :class="{ 'text-success': totalPercentage === 100, 'text-warning': totalPercentage !== 100 }">
                受益比例总和: {{ totalPercentage }}%
                <span v-if="totalPercentage !== 100" class="percentage-hint">(应为100%)</span>
            </p>
        </div>
        <!-- 附件上传区域 -->
        <div class="attachment-section" :class="{ 'error': attachmentError }">
            <h4 class="attachment-title">附件上传 <span class="required-mark">*</span></h4>
            <p class="attachment-hint">请上传相关证明文件，支持PDF、Word、JPG、PNG格式，单个文件不超过10MB，至少上传1个附件，最多9个附件</p>

            <div class="attachment-upload-area">
                <a-upload v-model:file-list="attachmentList" :before-upload="beforeUpload" :remove="removeAttachment"
                    list-type="text" :multiple="true" :max-count="9" :show-upload-list="true">
                    <a-button v-if="attachmentList.length < 9" type="dashed" class="upload-btn">
                        <template #icon>
                            <Icon icon="material-symbols:upload" />
                        </template>
                        选择文件
                    </a-button>
                </a-upload>
            </div>

            <div v-if="attachmentList.length > 0" class="attachment-list">
                <div v-for="(file, index) in attachmentList" :key="index" class="attachment-item">
                    <div class="attachment-info">
                        <Icon :icon="getFileIcon(file.name)" class="file-icon" />
                        <div class="file-details">
                            <div class="file-name">{{ file.name }}</div>
                            <div class="file-size">{{ formatFileSize(file.size) }}</div>
                        </div>
                    </div>
                    <a-button type="text" danger @click="removeAttachmentByIndex(index)" class="remove-btn">
                        <template #icon>
                            <Icon icon="material-symbols:delete-outline" />
                        </template>
                    </a-button>
                </div>
            </div>
            <p v-if="attachmentError" class="attachment-error">{{ attachmentError }}</p>
        </div>
        <div v-if="formError" class="form-error">{{ formError }}</div>
    </div>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits, defineExpose, nextTick, watch } from 'vue';
import { Icon } from '@iconify/vue';
import { formRules } from '@/utils/formValidate';
import { message } from 'ant-design-vue';
import { useFormBinding } from '@/utils/formHooks';

const props = defineProps({
    formData: {
        type: Array,
        default: () => []
    },
    policyholderData: {
        type: Object,
        default: () => ({})
    },
    insuredData: {
        type: Object,
        default: () => ({})
    }
});

const emit = defineEmits(['update:form-data']);

// 表单引用
const formRefs = ref([]);

// 受益人列表 - 不使用响应式引用，避免双向绑定问题
let beneficiariesData = [];
// 附件上传相关状态
const attachmentList = ref([]);
const rules = {
    name: [
        { required: true, message: '请输入受益人姓名', trigger: 'blur' },
        { min: 2, max: 20, message: '姓名长度必须在2-20个字符之间', trigger: 'blur' }
    ],
    gender: [
        { required: true, message: '请选择受益人性别', trigger: 'change' }
    ],

    relationship: [
        { required: true, message: '请选择与受保人关系', trigger: 'change' }
    ],
    benefitPercentage: [
        { required: true, message: '请输入受益比例', trigger: 'blur' },
        { type: 'number', min: 0, max: 100, message: '受益比例必须在0-100之间', trigger: 'blur' },
        {
            validator: (rule, value) => {
                if (value !== undefined && value !== null) {
                    const numValue = parseFloat(value);
                    if (isNaN(numValue)) {
                        return Promise.reject('受益比例必须是数字');
                    }
                    if (numValue < 0 || numValue > 100) {
                        return Promise.reject('受益比例必须在0-100之间');
                    }
                }
                return Promise.resolve();
            },
            trigger: 'blur'
        }
    ],
    isTrustee: [
        { required: true, message: '请选择是否为信托人', trigger: 'change' }
    ]
};
useFormBinding(props, emit, {}, [], { autoEmit: false });
// 支持的文件类型
const allowedFileTypes = ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'];
const maxFileSize = 10 * 1024 * 1024; // 10MB
// 创建一个计算属性来获取受益人数据
const beneficiaries = computed({
    get: () => {
        // 如果props.formData有值且与当前数据不同，更新本地数据
        if (props.formData && props.formData.length > 0 &&
            JSON.stringify(props.formData) !== JSON.stringify(beneficiariesData)) {
            beneficiariesData = JSON.parse(JSON.stringify(props.formData));
        }
        return beneficiariesData;
    },
    set: (newValue) => {
        beneficiariesData = newValue;
        emit('update:form-data', JSON.parse(JSON.stringify(beneficiariesData)));
    }
});

// 表单错误信息
const formError = ref('');
const attachmentError = ref('');

// 注意：表单验证规则已在模板中直接处理，无需单独定义

// 计算受益比例总和
const totalPercentage = computed(() => {
    return parseFloat(
        beneficiaries.value.reduce((sum, item) => {
            return sum + (parseFloat(item.benefitPercentage) || 0);
        }, 0).toFixed(2)
    );
});

// 记录上一次的关系状态，用于检测关系变化
let lastRelationship = null;

// 自动填写受益人信息的函数
const autoFillBeneficiaryInfo = () => {
    const currentRelationship = props.insuredData?.relationshipWithPolicyholder;

    console.log('autoFillBeneficiaryInfo 被调用', {
        currentRelationship,
        lastRelationship,
        policyholderData: props.policyholderData,
        insuredData: props.insuredData,
        beneficiariesLength: beneficiaries.value.length
    });

    // 检测关系是否发生变化
    const relationshipChanged = lastRelationship !== null && lastRelationship !== currentRelationship;

    // 如果关系发生变化，清空现有受益人数据
    if (relationshipChanged) {
        console.log('关系发生变化，清空受益人数据');
        beneficiaries.value = [];
    }

    // 更新记录的关系状态
    lastRelationship = currentRelationship;

    // 如果已经有受益人数据且关系没有变化，不自动填写
    if (beneficiaries.value.length > 0 && !relationshipChanged) {
        console.log('已有受益人数据且关系未变化，跳过自动填写');
        return;
    }

    let sourceData = null;
    let relationship = '';

    // 根据被保人与投保人的关系决定数据源和受益人关系
    if (props.insuredData && props.insuredData.relationshipWithPolicyholder === '本人') {
        // 关系是"本人"，使用投保人数据，受益人就是投保人本人
        sourceData = props.policyholderData;
        relationship = '本人'; // 受益人与被保人的关系（被保人就是投保人本人）
    } else if (props.insuredData && props.insuredData.relationshipWithPolicyholder !== '本人') {
        // 关系不是"本人"，使用被保人数据，受益人就是被保人本人
        sourceData = props.insuredData;
        relationship = '本人'; // 受益人与被保人的关系（受益人就是被保人本人）
    }

    // 如果有数据源且有姓名，自动添加受益人
    if (sourceData && sourceData.nameCn) {
        const newBeneficiary = {
            name: sourceData.nameCn || '',
            gender: sourceData.gender !== undefined ? sourceData.gender : 1,
            relationship: relationship,
            idCardNo: sourceData.idCardNo || '',
            benefitPercentage: 100,
            isTrustee: 0
        };

        console.log('自动填写受益人信息', {
            sourceData,
            newBeneficiary,
            relationship: currentRelationship
        });

        // 使用nextTick确保DOM更新后再更新表单引用
        nextTick(() => {
            beneficiaries.value = [newBeneficiary];
        });
    } else {
        console.log('无法自动填写受益人信息', {
            hasSourceData: !!sourceData,
            hasNameCn: !!(sourceData && sourceData.nameCn),
            sourceData
        });
    }
};
const validateIdCard = (idCard) => {
    // 检查是否是18位身份证
    if (!/^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[0-9Xx]$/.test(idCard)) {
        return false;
    }

    // 检查日期是否有效
    const year = parseInt(idCard.substr(6, 4));
    const month = parseInt(idCard.substr(10, 2));
    const day = parseInt(idCard.substr(12, 2));
    const date = new Date(year, month - 1, day);
    if (date.getFullYear() !== year || date.getMonth() + 1 !== month || date.getDate() !== day) {
        return false;
    }

    // 检查校验码
    if (idCard.length === 18) {
        const factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
        const parity = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
        let sum = 0;
        let checkCode;

        for (let i = 0; i < 17; i++) {
            sum += parseInt(idCard.charAt(i)) * factor[i];
        }

        checkCode = parity[sum % 11];
        return checkCode === idCard.charAt(17).toUpperCase();
    }

    return true;
}
// 监听投保人和被保人数据变化，自动填写受益人信息
watch([() => props.policyholderData, () => props.insuredData], () => {
    autoFillBeneficiaryInfo();
}, { deep: true, immediate: true });

// 添加受益人
const addBeneficiary = () => {
    clearFormError();
    const newData = [...beneficiaries.value];
    newData.push({
        name: '',
        gender: 1,
        relationship: '',
        idCardNo: '',
        benefitPercentage: newData.length === 0 ? 100 : 0,
        isTrustee: 0
    });

    // 使用nextTick确保DOM更新后再更新表单引用
    nextTick(() => {
        beneficiaries.value = newData;
    });
};

// 移除受益人
const removeBeneficiary = (index) => {
    const newData = [...beneficiaries.value];
    newData.splice(index, 1);

    // 如果删除后只剩一个受益人，将其比例设为100%
    if (newData.length === 1) {
        newData[0].benefitPercentage = 100;
    }

    // 更新表单引用
    formRefs.value = formRefs.value.filter((_, i) => i !== index);

    // 使用nextTick确保DOM更新后再更新表单引用
    nextTick(() => {
        beneficiaries.value = newData;
    });
};

// 更新受益人数据
const updateBeneficiaryData = (index, field, value) => {
    const newData = [...beneficiaries.value];
    newData[index][field] = value;
    beneficiaries.value = newData;

    // 实时验证单个字段
    validateField(index, field);

    // 对身份证号码进行特殊处理，确保在值变化时立即触发验证
    if (field === 'idCardNo' && value && value.length >= 18) {
        validateField(index, field);
    }
};

// 实时验证单个字段
const validateField = (index, field) => {
    if (formRefs.value[index]) {
        formRefs.value[index].validateFields([field]).catch(() => {
            // 捕获错误但不处理，防止控制台报错
        });
    }
};

// 不显示错误消息的验证方法（用于静默检查表单状态）
const validateSilent = async () => {
    if (beneficiaries.value.length === 0) {
        return Promise.reject('请至少添加一位受益人');
    }

    // 验证受益比例总和是否为100%
    if (Math.abs(totalPercentage.value - 100) > 0.01) {
        return Promise.reject('受益人比例总和必须为100%');
    }

    // 验证附件上传
    if (attachmentList.value.length === 0) {
        return Promise.reject('请至少上传一个附件');
    }

    try {
        // 确保所有表单引用都存在
        if (formRefs.value.length !== beneficiaries.value.length) {
            return Promise.reject('表单引用不完整');
        }

        // 验证每个受益人表单
        const validatePromises = formRefs.value.map((form) => {
            if (!form) {
                return Promise.reject('表单引用不存在');
            }
            return form.validateFields();
        });

        await Promise.all(validatePromises);

        return Promise.resolve({
            beneficiaries: JSON.parse(JSON.stringify(beneficiaries.value)),
            attachments: attachmentList.value
        });
    } catch (error) {
        return Promise.reject(error || '表单验证失败');
    }
};

// 清除表单错误信息
const clearFormError = () => {
    formError.value = '';
    attachmentError.value = '';
};

// 附件上传前的验证
const beforeUpload = (file) => {
    clearFormError();
    attachmentError.value = '';

    // 检查文件类型
    const fileExtension = file.name.split('.').pop().toLowerCase();
    if (!allowedFileTypes.includes(fileExtension)) {
        attachmentError.value = `不支持的文件类型，请上传 ${allowedFileTypes.join(', ')} 格式的文件`;
        message.error(`不支持的文件类型，请上传 ${allowedFileTypes.join(', ')} 格式的文件`);
        return false;
    }

    // 检查文件大小
    if (file.size > maxFileSize) {
        attachmentError.value = '文件大小不能超过10MB';
        message.error('文件大小不能超过10MB');
        return false;
    }

    // 检查文件数量
    if (attachmentList.value.length >= 9) {
        attachmentError.value = '最多只能上传9个附件';
        message.error('最多只能上传9个附件');
        return false;
    }

    // 清除附件错误
    attachmentError.value = '';

    // 阻止自动上传，只添加到文件列表
    return false;
};

// 移除附件
const removeAttachment = (file) => {
    const index = attachmentList.value.indexOf(file);
    if (index > -1) {
        attachmentList.value.splice(index, 1);
    }
};

// 通过索引移除附件
const removeAttachmentByIndex = (index) => {
    attachmentList.value.splice(index, 1);
};

// 获取文件图标
const getFileIcon = (fileName) => {
    const extension = fileName.split('.').pop().toLowerCase();
    switch (extension) {
        case 'pdf':
            return 'material-symbols:picture-as-pdf';
        case 'doc':
        case 'docx':
            return 'material-symbols:description';
        case 'jpg':
        case 'jpeg':
        case 'png':
            return 'material-symbols:image';
        default:
            return 'material-symbols:insert-drive-file';
    }
};

// 格式化文件大小
const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 表单验证方法（供父组件调用）
const validate = async () => {
    clearFormError();

    if (beneficiaries.value.length === 0) {
        formError.value = '请至少添加一位受益人';
        return Promise.reject('请至少添加一位受益人');
    }

    // 验证受益比例总和是否为100%
    if (Math.abs(totalPercentage.value - 100) > 0.01) {
        formError.value = '受益人比例总和必须为100%';
        return Promise.reject('受益人比例总和必须为100%');
    }

    // 验证附件上传
    if (attachmentList.value.length === 0) {
        attachmentError.value = '请至少上传一个附件';
        formError.value = '请至少上传一个附件';
        return Promise.reject('请至少上传一个附件');
    }

    // 验证每个受益人表单
    try {
        // 确保所有表单引用都存在
        if (formRefs.value.length !== beneficiaries.value.length) {
            formError.value = '表单引用不完整，请检查';
            return Promise.reject('表单引用不完整，请检查');
        }

        // 验证每个受益人表单
        const validatePromises = formRefs.value.map((form, index) => {
            if (!form) {
                return Promise.reject(`第${index + 1}个受益人表单引用不存在`);
            }

            // 先检查必填字段
            const beneficiary = beneficiaries.value[index];
            if (!beneficiary.name) {
                formError.value = `第${index + 1}个受益人姓名不能为空`;
                return Promise.reject(`第${index + 1}个受益人姓名不能为空`);
            }

            if (beneficiary.gender === undefined || beneficiary.gender === null) {
                formError.value = `第${index + 1}个受益人性别不能为空`;
                return Promise.reject(`第${index + 1}个受益人性别不能为空`);
            }

            if (!beneficiary.relationship) {
                formError.value = `第${index + 1}个受益人与受保人关系不能为空`;
                return Promise.reject(`第${index + 1}个受益人与受保人关系不能为空`);
            }

            if (!beneficiary.idCardNo) {
                formError.value = `第${index + 1}个受益人身份证号码不能为空`;
                return Promise.reject(`第${index + 1}个受益人身份证号码不能为空`);
            }

            if (beneficiary.benefitPercentage === undefined || beneficiary.benefitPercentage === null) {
                formError.value = `第${index + 1}个受益人受益比例不能为空`;
                return Promise.reject(`第${index + 1}个受益人受益比例不能为空`);
            }

            if (beneficiary.isTrustee === undefined || beneficiary.isTrustee === null) {
                formError.value = `第${index + 1}个受益人是否为信托人不能为空`;
                return Promise.reject(`第${index + 1}个受益人是否为信托人不能为空`);
            }

            // 使用表单组件的验证
            return form.validate();
        });

        await Promise.all(validatePromises);

        // 所有验证通过，返回成功
        return Promise.resolve({
            beneficiaries: JSON.parse(JSON.stringify(beneficiaries.value)),
            attachments: attachmentList.value
        });
    } catch (error) {
        // 如果formError已经设置了具体错误，就不要覆盖它
        if (!formError.value) {
            formError.value = '受益人信息填写有误，请检查';
        }
        return Promise.reject(error || '受益人信息填写有误，请检查');
    }
};

// 表单重置方法（供父组件调用）
const resetFields = () => {
    beneficiaries.value = [];
    formRefs.value = [];
    attachmentList.value = [];
    clearFormError();
};

// 向父组件暴露方法
defineExpose({
    validate,
    validateSilent,
    resetFields,
    beneficiaries,
    autoFillBeneficiaryInfo,
    attachmentList
});
</script>

<style scoped>
.mobile-beneficiary-form {
    padding: 16px 0;
}

.form-section-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 0px;
    padding-bottom: 0px;
    border-bottom: 1px solid #eee;
}

.form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.add-btn {
    padding: 0 16px;
    height: 32px;
    border-radius: 16px;
}

.empty-state {
    background-color: #fff;
    border-radius: 12px;
    padding: 32px 16px;
    text-align: center;
    color: #999;
    margin-bottom: 24px;
}

.beneficiary-card {
    background-color: #fff;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.beneficiary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
}

.beneficiary-header h4 {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin: 0;
}

.remove-btn {
    padding: 4px;
    color: #ff4d4f;
}

.percentage-summary {
    background-color: #fff;
    border-radius: 12px;
    padding: 16px;
    margin-top: 8px;
    text-align: center;
}

.percentage-bar {
    height: 8px;
    background-color: #f0f0f0;
    border-radius: 4px;
    margin-bottom: 8px;
    overflow: hidden;
}

.percentage-progress {
    height: 100%;
    transition: all 0.3s;
}

.text-success {
    color: #52c41a;
}

.text-warning {
    color: #faad14;
}

.percentage-hint {
    font-size: 12px;
    opacity: 0.8;
}

.form-error {
    color: #ff4d4f;
    text-align: center;
    margin-top: 16px;
    padding: 8px;
    background-color: #fff1f0;
    border-radius: 8px;
    border: 1px solid #ffccc7;
}

/* 附件上传样式 */
.attachment-section {
    background-color: #fff;
    border-radius: 12px;
    padding: 16px;
    margin-top: 16px;
}

.attachment-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
}

.required-mark {
    color: #ff4d4f;
}

.attachment-hint {
    font-size: 12px;
    color: #666;
    margin-bottom: 16px;
    line-height: 1.4;
}

.attachment-upload-area {
    margin-bottom: 16px;
}

.upload-btn {
    width: 100%;
    height: 80px;
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #666;
    transition: all 0.3s;
}

.upload-btn:hover {
    border-color: #1890ff;
    color: #1890ff;
}

.attachment-list {
    margin-top: 16px;
}

.attachment-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 8px;
}

.attachment-info {
    display: flex;
    align-items: center;
    flex: 1;
}

.file-icon {
    font-size: 24px;
    color: #1890ff;
    margin-right: 12px;
}

.file-details {
    flex: 1;
}

.file-name {
    font-size: 14px;
    color: #333;
    font-weight: 500;
    margin-bottom: 2px;
    word-break: break-all;
}

.file-size {
    font-size: 12px;
    color: #666;
}

.remove-btn {
    padding: 4px;
    color: #ff4d4f;
}

:deep(.ant-form-item) {
    margin-bottom: 16px;
}

:deep(.ant-radio-group) {
    display: flex;
    width: 100%;
}

:deep(.ant-radio-wrapper) {
    flex: 1;
    display: flex;
    justify-content: center;
}

@media (max-width: 767px) {
    .beneficiary-card {
        padding: 12px;
    }

    .form-section-title {
        font-size: 17px;
    }

    .beneficiary-header h4 {
        font-size: 15px;
    }
}

.percentage-summary.error .percentage-bar {
    border: 1px solid #ff4d4f;
}

.attachment-section.error {
    border: 1px solid #ff4d4f;
    border-radius: 12px;
}

.attachment-error {
    color: #ff4d4f;
    font-size: 12px;
    margin-top: 8px;
}
</style>