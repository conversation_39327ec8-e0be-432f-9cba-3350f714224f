<template>
    <div class="mobile-insured-form">
        <h3 class="form-section-title">被保人信息</h3>

        <div class="form-section">
            <a-form-item name="relationshipWithPolicyholder" label="与投保人关系">
                <a-select v-model:value="relationshipWithPolicyholder" placeholder="请选择与投保人关系"
                    @change="handleRelationshipChange">
                    <a-select-option value="本人">本人</a-select-option>
                    <a-select-option value="配偶">配偶</a-select-option>
                    <a-select-option value="父亲">父亲</a-select-option>
                    <a-select-option value="母亲">母亲</a-select-option>
                    <a-select-option value="儿子">儿子</a-select-option>
                    <a-select-option value="女儿">女儿</a-select-option>
                    <a-select-option value="兄弟">兄弟</a-select-option>
                    <a-select-option value="姐妹">姐妹</a-select-option>
                    <a-select-option value="祖父">祖父</a-select-option>
                    <a-select-option value="祖母">祖母</a-select-option>
                    <a-select-option value="外祖父">外祖父</a-select-option>
                    <a-select-option value="外祖母">外祖母</a-select-option>idCardNo
                    <a-select-option value="其他亲属">其他亲属</a-select-option>
                    <a-select-option value="雇主">雇主</a-select-option>
                    <a-select-option value="雇员">雇员</a-select-option>
                    <a-select-option value="其他">其他</a-select-option>
                </a-select>
            </a-form-item>
        </div>

        <!-- 仅当关系不是"本人"时显示表单 -->
        <a-form v-if="relationshipWithPolicyholder !== '本人'" ref="formRef" :model="formState" :rules="rules"
            layout="vertical">
            <div class="form-section">
                <a-form-item name="nameCn" label="中文姓名" required>
                    <a-input v-model:value="formState.nameCn" placeholder="请输入被保人中文姓名" />
                </a-form-item>

                <a-form-item name="nameEn" label="中文（护照）拼音" required>
                    <a-input v-model:value="formState.nameEn" placeholder="请输入被保人拼音名" />
                </a-form-item>

                <a-form-item name="idCardNo" label="身份证号码" required>
                    <a-input v-model:value="formState.idCardNo" placeholder="请输入身份证号码" />
                </a-form-item>

                <a-form-item name="travelPermitNo" label="通行证号码" required>
                    <a-input v-model:value="formState.travelPermitNo" placeholder="请输入通行证号码" />
                </a-form-item>

                <a-form-item name="birthDate" label="出生日期" required>
                    <a-date-picker v-model:value="formState.birthDate" style="width: 100%" format="YYYY-MM-DD"
                        placeholder="请选择出生日期" />
                </a-form-item>

                <a-form-item name="nationality" label="国籍" required>
                    <a-input v-model:value="formState.nationality" placeholder="请输入国籍" />
                </a-form-item>

                <a-form-item name="gender" label="性别" required>
                    <a-radio-group v-model:value="formState.gender">
                        <a-radio :value="1">男</a-radio>
                        <a-radio :value="0">女</a-radio>
                    </a-radio-group>
                </a-form-item>

                <a-form-item name="mobile" label="手机号码" required>
                    <a-input v-model:value="formState.mobile" placeholder="请输入手机号码" />
                </a-form-item>
            </div>

            <div class="form-section">
                <h4 class="form-subsection-title">其他信息</h4>

                <a-form-item name="maritalStatus" label="婚姻状况" required>
                    <a-select v-model:value="formState.maritalStatus" placeholder="请选择婚姻状况">
                        <a-select-option value="未婚">未婚</a-select-option>
                        <a-select-option value="已婚">已婚</a-select-option>
                        <a-select-option value="离异">离异</a-select-option>
                        <a-select-option value="丧偶">丧偶</a-select-option>
                    </a-select>
                </a-form-item>

                <a-form-item name="educationLevel" label="教育程度" required>
                    <a-select v-model:value="formState.educationLevel" placeholder="请选择教育程度">
                        <a-select-option value="小学">小学</a-select-option>
                        <a-select-option value="初中">初中</a-select-option>
                        <a-select-option value="高中">高中</a-select-option>
                        <a-select-option value="大专">大专</a-select-option>
                        <a-select-option value="本科">本科</a-select-option>
                        <a-select-option value="硕士">硕士</a-select-option>
                        <a-select-option value="博士">博士</a-select-option>
                    </a-select>
                </a-form-item>

                <a-form-item name="height" label="身高" required>
                    <a-input-number v-model:value="formState.height" style="width: 100%" placeholder="请输入身高"
                        :precision="0" :min="0" :max="300" addonAfter="厘米" />
                </a-form-item>

                <a-form-item name="weight" label="体重" required>
                    <a-input-number v-model:value="formState.weight" style="width: 100%" placeholder="请输入体重"
                        :precision="2" :min="0" addonAfter="公斤" />
                </a-form-item>

                <a-form-item name="homePhone" label="住宅电话" required>
                    <a-input v-model:value="formState.homePhone" placeholder="请输入住宅电话" />
                </a-form-item>

                <a-form-item name="email" label="邮箱" required>
                    <a-input v-model:value="formState.email" placeholder="请输入邮箱" />
                </a-form-item>

                <a-form-item name="isSmoker" label="是否吸烟" required>
                    <a-radio-group v-model:value="formState.isSmoker">
                        <a-radio :value="1">是</a-radio>
                        <a-radio :value="0">否</a-radio>
                    </a-radio-group>
                </a-form-item>

                <a-form-item name="idCardAddress" label="身份证地址" required>
                    <a-textarea v-model:value="formState.idCardAddress" placeholder="请输入身份证地址" :rows="3" />
                </a-form-item>
            </div>
        </a-form>

        <!-- 如果关系是"本人"，显示提示信息 -->
        <div v-else class="form-section relationship-info">
            <p>被保人与投保人为同一人，信息已自动填写</p>
        </div>
    </div>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits, defineExpose, onMounted, nextTick } from 'vue';
import { formRules } from '@/utils/formValidate';
import { useFormBinding, useFormValidation } from '@/utils/formHooks';
import dayjs from 'dayjs';

const props = defineProps({
    formData: {
        type: Object,
        default: () => ({})
    },
    policyholderData: {
        type: Object,
        default: () => ({})
    }
});

const emit = defineEmits(['update:form-data']);

// 表单引用
const formRef = ref(null);

// 与投保人关系
const relationshipWithPolicyholder = ref('本人');

// 标志位：防止监听器干扰用户手动选择
const isUserSelecting = ref(false);

// 初始表单状态
const initialState = {
    nameCn: '',
    nameEn: '',
    birthDate: null,
    gender: 1,
    idCardNo: '',
    travelPermitNo: '',
    mobile: '',
    homePhone: '',
    email: '',
    nationality: '',
    maritalStatus: '',
    educationLevel: '',
    height: null,
    weight: null,
    idCardAddress: '',
    isSmoker: 0
};

// 使用formHooks中的useFormBinding处理表单数据绑定，但禁用自动数据发送
const { formState } = useFormBinding(
    props,
    emit,
    initialState,
    ['birthDate'],
    { autoEmit: false } // 禁用自动数据发送
);

// 手动处理表单数据发送，确保包含关系信息
watch(formState, () => {
    // 如果用户正在选择关系，不要发送数据更新
    if (isUserSelecting.value) return;

    const updatedData = { ...formState, relationshipWithPolicyholder: relationshipWithPolicyholder.value };
    emit('update:form-data', updatedData);
}, { deep: true });

// 表单验证规则
const rules = {
    nameCn: formRules.chineseName,
    nameEn: [
        { required: true, message: '请输入英文名或中文名拼音', trigger: 'blur' },
    ],
    idCardNo: formRules.idCard(true),
    travelPermitNo: formRules.passport,
    birthDate: formRules.birthDate,
    nationality: [
        { required: true, message: '请输入国籍', trigger: 'blur' }
    ],
    gender: formRules.gender,
    mobile: formRules.phone(true),
    homePhone: formRules.phone(true),
    email: formRules.email(true),
    maritalStatus: [
        { required: true, message: '请选择婚姻状况', trigger: 'change' }

    ],
    educationLevel: [
        { required: true, message: '请选择教育程度', trigger: 'change' }
    ],
    height: formRules.height,
    weight: formRules.weight,
    isSmoker: [
        { required: true, message: '请选择是否吸烟', trigger: 'change' }
    ],
    idCardAddress: formRules.address(true),
    idCardNo: formRules.idCard(true),
    mobile: formRules.phone(true),
    homePhone: formRules.phone(true),
    email: formRules.email(true),
    maritalStatus: [
        { required: true, message: '请选择婚姻状况', trigger: 'change' }
    ],
    educationLevel: [
        { required: true, message: '请选择教育程度', trigger: 'change' }
    ],
    height: { required: true, message: '请输入身高', trigger: 'blur' },
    weight: { required: true, message: '请输入体重', trigger: 'blur' },
    homePhone: formRules.phone(true),
    email: formRules.email(true),
    isSmoker: [
        { required: true, message: '请选择是否吸烟', trigger: 'change' }
    ],
};

// 处理"与投保人关系"变化
const handleRelationshipChange = (value) => {
    console.log('handleRelationshipChange', value);

    // 设置用户选择标志位
    isUserSelecting.value = true;

    if (value === '本人') {
        // 如果关系是"本人"，先重置表单状态到初始值，然后复制投保人信息
        Object.assign(formState, { ...initialState });
        copyPolicyholderData();
    } else {
        // 如果关系不是"本人"，重置表单状态到初始值
        Object.assign(formState, { ...initialState });
    }

    // 使用 nextTick 确保 DOM 更新完成后再发送数据
    nextTick(() => {
        const updatedData = { ...formState, relationshipWithPolicyholder: value };
        emit('update:form-data', updatedData);

        // 重置标志位
        isUserSelecting.value = false;
    });
};

// 复制投保人信息到被保人
const copyPolicyholderData = () => {
    if (Object.keys(props.policyholderData).length > 0) {
        // 处理可能的时间戳转换为日期对象
        const policyholderData = { ...props.policyholderData };

        // 如果birthDate是时间戳，转换为Dayjs对象
        if (policyholderData.birthDate && typeof policyholderData.birthDate === 'number') {
            policyholderData.birthDate = dayjs(policyholderData.birthDate);
        }

        // 更新表单状态，只复制存在的字段
        Object.keys(policyholderData).forEach(key => {
            if (key in formState) {
                formState[key] = policyholderData[key];
            }
        });
    }
};

// 监听父组件传入的表单数据
watch(() => props.formData, (newVal) => {
    // 如果用户正在选择，不要干扰
    if (isUserSelecting.value) return;

    if (newVal && Object.keys(newVal).length > 0) {
        // 只有当明确传入了relationshipWithPolicyholder字段且值不同时，才更新关系选择
        if (newVal.hasOwnProperty('relationshipWithPolicyholder') &&
            newVal.relationshipWithPolicyholder !== undefined &&
            newVal.relationshipWithPolicyholder !== relationshipWithPolicyholder.value) {
            console.log('从父组件更新关系选择', {
                oldValue: relationshipWithPolicyholder.value,
                newValue: newVal.relationshipWithPolicyholder
            });
            relationshipWithPolicyholder.value = newVal.relationshipWithPolicyholder;
        }
    }
}, { deep: true, immediate: false, flush: 'post' });

// 监听投保人数据变化
watch(() => props.policyholderData, (newVal) => {
    if (relationshipWithPolicyholder.value === '本人' && newVal && Object.keys(newVal).length > 0) {
        copyPolicyholderData();
    }
}, { deep: true });

// 注意：关系变化的数据更新已在 handleRelationshipChange 函数中处理，无需重复监听

// 组件挂载时，确保初始数据传递给父组件
onMounted(() => {
    nextTick(() => {
        // 如果关系是"本人"，并且有投保人数据，复制投保人信息
        if (relationshipWithPolicyholder.value === '本人' && Object.keys(props.policyholderData).length > 0) {
            copyPolicyholderData();
        }
        // 确保关系字段传递给父组件
        const updatedData = { ...formState, relationshipWithPolicyholder: relationshipWithPolicyholder.value };
        emit('update:form-data', updatedData);
    });
});

// 必填字段列表（仅当关系不是"本人"时需要验证）
const requiredFields = ['relationshipWithPolicyholder', 'nameCn', 'birthDate', 'gender', 'idCardNo', 'mobile', 'isSmoker', 'nationality', 'homePhone', 'email', 'maritalStatus', 'educationLevel', 'height', 'weight', 'isSmoker', 'idCardAddress'];

// 使用formHooks中的useFormValidation处理表单验证
const validateFn = useFormValidation(formRef, formState, requiredFields, '请完善被保人信息');

// 表单验证方法（供父组件调用）
const validate = () => {
    // 如果关系是"本人"，则无需额外验证
    if (relationshipWithPolicyholder.value === '本人') {
        return Promise.resolve();
    }

    return validateFn();
};

// 表单重置方法（供父组件调用）
const resetFields = () => {
    // 重置表单状态到初始值
    Object.assign(formState, { ...initialState });

    // 重置Ant Design表单
    if (formRef.value) {
        formRef.value.resetFields();
    }

    // 重置关系选择
    relationshipWithPolicyholder.value = '本人';

    // 重置后自动填充投保人信息
    handleRelationshipChange('本人');
};

// 向父组件暴露方法
defineExpose({
    validate,
    resetFields,
    formState,
    relationshipWithPolicyholder
});
</script>

<style scoped>
.mobile-insured-form {
    padding: 16px 0;
}

.form-section-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.form-subsection-title {
    font-size: 16px;
    font-weight: 500;
    color: #555;
    margin: 16px 0;
}

.form-section {
    margin-bottom: 24px;
    background-color: #fff;
    border-radius: 12px;
    padding: 16px;
}

.relationship-info {
    padding: 24px 16px;
    text-align: center;
    color: #666;
}

:deep(.ant-form-item) {
    margin-bottom: 16px;
}

:deep(.ant-radio-group) {
    display: flex;
    width: 100%;
}

:deep(.ant-radio-wrapper) {
    flex: 1;
    display: flex;
    justify-content: center;
}

:deep(.ant-textarea) {
    border-radius: 8px;
    font-size: 16px;
}

@media (max-width: 767px) {
    .mobile-insured-form {
        padding: 12px 0;
    }

    .form-section {
        padding: 12px;
        margin-bottom: 16px;
    }

    .form-section-title {
        font-size: 17px;
        margin-bottom: 16px;
    }
}
</style>