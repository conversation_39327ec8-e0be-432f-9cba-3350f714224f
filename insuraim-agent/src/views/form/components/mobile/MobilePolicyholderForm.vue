<template>
    <div class="mobile-policyholder-form">
        <h3 class="form-section-title">投保人信息</h3>

        <a-form ref="formRef" :model="formState" :rules="rules" layout="vertical">
            <div class="form-section">
                <a-form-item name="nameCn" label="中文姓名" required>
                    <a-input v-model:value="formState.nameCn" placeholder="请输入投保人中文姓名" />
                </a-form-item>

                <a-form-item name="idCardNo" label="身份证号码" required>
                    <a-input v-model:value="formState.idCardNo" placeholder="请输入身份证号码" />
                </a-form-item>

                <a-form-item name="birthDate" label="出生日期" required>
                    <a-date-picker v-model:value="formState.birthDate" style="width: 100%" format="YYYY-MM-DD"
                        placeholder="请选择出生日期" />
                </a-form-item>

                <a-form-item name="gender" label="性别" required>
                    <a-radio-group v-model:value="formState.gender">
                        <a-radio :value="1">男</a-radio>
                        <a-radio :value="0">女</a-radio>
                    </a-radio-group>
                </a-form-item>

                <a-form-item name="mobile" label="手机号码" required>
                    <a-input v-model:value="formState.mobile" placeholder="请输入手机号码" />
                </a-form-item>

                <a-form-item name="email" label="邮箱" required>
                    <a-input v-model:value="formState.email" placeholder="请输入邮箱" />
                </a-form-item>
            </div>

            <div class="form-section">
                <h4 class="form-subsection-title">其他信息</h4>

                <a-form-item name="nameEn" label="中文（护照）拼音" required>
                    <a-input v-model:value="formState.nameEn" placeholder="请输入投保人拼音名" />
                </a-form-item>

                <a-form-item name="travelPermitNo" label="通行证号码" required>
                    <a-input v-model:value="formState.travelPermitNo" placeholder="请输入通行证号码" />
                </a-form-item>

                <a-form-item name="nationality" label="国籍" required>
                    <a-input v-model:value="formState.nationality" placeholder="请输入国籍" />
                </a-form-item>

                <a-form-item name="birthPlace" label="出生地" required>
                    <a-input v-model:value="formState.birthPlace" placeholder="请输入出生地" />
                </a-form-item>

                <a-form-item name="maritalStatus" label="婚姻状况" required>
                    <a-select v-model:value="formState.maritalStatus" placeholder="请选择婚姻状况">
                        <a-select-option value="未婚">未婚</a-select-option>
                        <a-select-option value="已婚">已婚</a-select-option>
                        <a-select-option value="离异">离异</a-select-option>
                        <a-select-option value="丧偶">丧偶</a-select-option>
                    </a-select>
                </a-form-item>

                <a-form-item name="educationLevel" label="教育程度" required>
                    <a-select v-model:value="formState.educationLevel" placeholder="请选择教育程度">
                        <a-select-option value="小学">小学</a-select-option>
                        <a-select-option value="初中">初中</a-select-option>
                        <a-select-option value="高中">高中</a-select-option>
                        <a-select-option value="大专">大专</a-select-option>
                        <a-select-option value="本科">本科</a-select-option>
                        <a-select-option value="硕士">硕士</a-select-option>
                        <a-select-option value="博士">博士</a-select-option>
                    </a-select>
                </a-form-item>

                <a-form-item name="height" label="身高" required>
                    <a-input-number v-model:value="formState.height" style="width: 100%" placeholder="请输入身高"
                        :precision="0" :min="0" :max="300" addonAfter="厘米" />
                </a-form-item>

                <a-form-item name="weight" label="体重" required>
                    <a-input-number v-model:value="formState.weight" style="width: 100%" placeholder="请输入体重"
                        :precision="2" :min="0" addonAfter="公斤" />
                </a-form-item>

                <a-form-item name="homePhone" label="住宅电话" required>
                    <a-input v-model:value="formState.homePhone" placeholder="请输入住宅电话" />
                </a-form-item>

                <a-form-item name="isSmoker" label="是否吸烟" required>
                    <a-radio-group v-model:value="formState.isSmoker">
                        <a-radio :value="1">是</a-radio>
                        <a-radio :value="0">否</a-radio>
                    </a-radio-group>
                </a-form-item>

                <a-form-item name="idCardAddress" label="身份证地址" required>
                    <a-textarea v-model:value="formState.idCardAddress" placeholder="请输入身份证地址" :rows="3" />
                </a-form-item>

                <a-form-item name="residentialAddress" label="居住地址（如与身份证地址不同）" required>
                    <a-textarea v-model:value="formState.residentialAddress" placeholder="请输入居住地址（如与身份证地址不同）"
                        :rows="3" />
                </a-form-item>

                <a-form-item name="mailingAddress" label="通讯地址（如与居住地址不同）" required>
                    <a-textarea v-model:value="formState.mailingAddress" placeholder="请输入通讯地址（如与居住地址不同）" :rows="3" />
                </a-form-item>
            </div>

            <div class="form-section">
                <h4 class="form-subsection-title">工作信息</h4>

                <a-form-item name="companyNameCn" label="公司中文名称" required>
                    <a-input v-model:value="formState.companyNameCn" placeholder="请输入公司中文名称" />
                </a-form-item>

                <a-form-item name="companyNameEn" label="公司英文名称" required>
                    <a-input v-model:value="formState.companyNameEn" placeholder="请输入公司英文名称" />
                </a-form-item>

                <a-form-item name="companyAddress" label="公司地址" required>
                    <a-textarea v-model:value="formState.companyAddress" placeholder="请输入公司地址" :rows="3" />
                </a-form-item>

                <a-form-item name="companyIndustry" label="公司行业" required>
                    <a-input v-model:value="formState.companyIndustry" placeholder="请输入公司行业" />
                </a-form-item>

                <a-form-item name="position" label="职位" required>
                    <a-input v-model:value="formState.position" placeholder="请输入职位" />
                </a-form-item>

                <a-form-item name="annualIncome" label="年薪" required>
                    <a-input-number v-model:value="formState.annualIncome" style="width: 100%" placeholder="请输入年薪"
                        :precision="2" :min="0" />
                </a-form-item>
            </div>
        </a-form>
    </div>
</template>

<script setup>
import { ref, reactive, watch, defineProps, defineEmits, defineExpose } from 'vue';
import { formRules } from '@/utils/formValidate';
import { useFormBinding, useFormValidation } from '@/utils/formHooks';
import dayjs from 'dayjs';

const props = defineProps({
    formData: {
        type: Object,
        default: () => ({})
    }
});

const emit = defineEmits(['update:form-data']);

// 表单引用
const formRef = ref(null);

// // 初始表单状态
// const initialState = {
//     nameCn: '',
//     nameEn: '',
//     birthDate: null,
//     gender: 1,
//     idCardNo: '',
//     travelPermitNo: '',
//     nationality: '中国',
//     birthPlace: '',
//     maritalStatus: '',
//     educationLevel: '',
//     height: null,
//     weight: null,
//     mobile: '',
//     homePhone: '',
//     email: '',
//     idCardAddress: '',
//     residentialAddress: '',
//     mailingAddress: '',
//     isSmoker: 0,
//     companyNameCn: '',
//     companyNameEn: '',
//     companyAddress: '',
//     companyIndustry: '',
//     position: '',
//     annualIncome: null
// };


// 预填数据 方便测试
const initialState = {
    nameCn: '张三',
    nameEn: 'Zhang San',
    birthDate: null,
    gender: 1,
    idCardNo: '11010519491231001X',
    travelPermitNo: '*********',
    nationality: '中国',
    birthPlace: '',
    maritalStatus: '已婚',
    educationLevel: '本科',
    height: 180,
    weight: 70,
    mobile: '13800138000',
    homePhone: '13800138000',
    email: '<EMAIL>',
    idCardAddress: '北京市海淀区',
    residentialAddress: '北京市海淀区',
    mailingAddress: '北京市海淀区',
    isSmoker: 0,
    companyNameCn: '北京科技有限公司',
    companyNameEn: 'Beijing Technology Co., Ltd.',
    companyAddress: '北京市海淀区',
    companyIndustry: '科技',
    position: '工程师',
    annualIncome: 100000
};
// // 使用formHooks中的useFormBinding处理表单数据绑定
const { formState, emitFormData, resetFields: hookResetFields } = useFormBinding(
    props,
    emit,
    initialState,
    ['birthDate']
);

// 表单验证规则
const rules = {
    nameCn: formRules.chineseName,
    nameEn: [
        { required: true, message: '请输入英文名或中文名拼音', trigger: 'blur' },
    ],
    idCardNo: formRules.idCard(true),
    travelPermitNo: formRules.passport,
    birthDate: formRules.birthDate,
    nationality: [
        { required: true, message: '请输入国籍', trigger: 'blur' }
    ],
    birthPlace: [
        { required: true, message: '请输入出生地', trigger: 'blur' }
    ],
    gender: formRules.gender,
    mobile: formRules.phone(true),
    homePhone: formRules.phone(true),
    email: formRules.email(true),
    maritalStatus: [
        { required: true, message: '请选择婚姻状况', trigger: 'change' }
    ],
    educationLevel: [
        { required: true, message: '请选择教育程度', trigger: 'change' }
    ],
    height: formRules.height,
    weight: formRules.weight,
    isSmoker: [
        { required: true, message: '请选择是否吸烟', trigger: 'change' }
    ],
    idCardAddress: formRules.address(true),
    residentialAddress: formRules.address(true),
    mailingAddress: formRules.address(true),
    companyNameCn: formRules.chineseName.filter(rule => !rule.required),
    companyNameEn: formRules.englishName,
    companyAddress: formRules.address(true),
    companyIndustry: [
        { required: true, message: '请输入公司行业', trigger: 'blur' }
    ],
    position: [
        { required: true, message: '请输入职位', trigger: 'blur' }
    ],
    annualIncome: [
        { required: true, message: '请输入年薪', trigger: 'blur' },
        { type: 'number', min: 0, message: '年薪不能为负数', trigger: 'blur' }
    ],
    companyNameEn: { required: true, message: '请输入公司英文名称', trigger: 'blur' },
    companyAddress: { required: true, message: '请输入公司地址', trigger: 'blur' },
    companyIndustry: { required: true, message: '请输入公司行业', trigger: 'blur' },
    position: { required: true, message: '请输入职位', trigger: 'blur' },
    annualIncome: { required: true, message: '请输入年薪', trigger: 'blur' },
    homePhone: { required: true, message: '请输入住宅电话', trigger: 'blur' },
    email: { required: true, message: '请输入邮箱', trigger: 'blur' },
    isSmoker: { required: true, message: '请选择是否吸烟', trigger: 'blur' },
    companyNameCn: { required: true, message: '请输入公司中文名称', trigger: 'blur' },
    height: { required: true, message: '请输入身高', trigger: 'blur' },
    weight: { required: true, message: '请输入体重', trigger: 'blur' },


};

// 必填字段列表
const requiredFields = [
    'nameCn', 'birthDate', 'gender', 'idCardNo',
    'nationality', 'mobile', 'email', 'maritalStatus',
    'educationLevel', 'isSmoker', 'idCardAddress',
    'birthPlace', 'residentialAddress', 'mailingAddress',
    'companyAddress', 'companyIndustry', 'position', 'annualIncome',
    'companyNameCn', 'companyNameEn', 'travelPermitNo'
];

// 使用formHooks中的useFormValidation处理表单验证
const validateFn = useFormValidation(formRef, formState, requiredFields, '请完善投保人信息');

// 表单验证方法（供父组件调用）
const validate = () => {
    return validateFn();
};

// 表单重置方法（供父组件调用）
const resetFields = () => {
    hookResetFields();
    formRef.value?.resetFields();
};

// 向父组件暴露方法
defineExpose({
    validate,
    resetFields,
    formState
});
</script>

<style scoped>
.mobile-policyholder-form {
    padding: 16px 0;
}

.form-section-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.form-subsection-title {
    font-size: 16px;
    font-weight: 500;
    color: #555;
    margin: 16px 0;
}

.form-section {
    margin-bottom: 24px;
    background-color: #fff;
    border-radius: 12px;
    padding: 16px;
}

:deep(.ant-form-item) {
    margin-bottom: 16px;
}

:deep(.ant-radio-group) {
    display: flex;
    width: 100%;
}

:deep(.ant-radio-wrapper) {
    flex: 1;
    display: flex;
    justify-content: center;
}

:deep(.ant-textarea) {
    border-radius: 8px;
    font-size: 16px;
}

@media (max-width: 767px) {
    .mobile-policyholder-form {
        padding: 12px 0;
    }

    .form-section {
        padding: 12px;
        margin-bottom: 16px;
    }

    .form-section-title {
        font-size: 17px;
        margin-bottom: 16px;
    }
}
</style>