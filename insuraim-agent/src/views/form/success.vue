<template>
    <div class="success-container">
        <div class="success-card">
            <div class="success-icon">
                <Icon icon="material-symbols:check-circle" class="check-icon" />
            </div>
            <h2 class="success-title">提交成功</h2>
            <p class="success-message">您的保单信息已成功提交，我们的保险顾问会尽快与您联系。</p>

        </div>
    </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';
import { useRouter } from 'vue-router';

const router = useRouter();

// 返回首页
const goHome = () => {
    router.push('/');
};
</script>

<style scoped>
.success-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background-color: #f5f7fa;
}

.success-card {
    background-color: #fff;
    border-radius: 16px;
    padding: 40px 20px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    max-width: 400px;
    width: 100%;
}

.success-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 24px;
}

.check-icon {
    font-size: 80px;
    color: #52c41a;
}

.success-title {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 16px;
}

.success-message {
    font-size: 16px;
    color: #666;
    line-height: 1.6;
    margin-bottom: 32px;
}

.success-actions {
    margin-top: 16px;
}

.home-btn {
    height: 40px;
    font-size: 16px;
    padding: 0 32px;
    border-radius: 20px;
}

@media (max-width: 480px) {
    .success-card {
        padding: 32px 16px;
    }

    .check-icon {
        font-size: 70px;
    }

    .success-title {
        font-size: 22px;
    }

    .success-message {
        font-size: 15px;
    }
}
</style>