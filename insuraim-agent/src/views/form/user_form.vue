<template>
    <a-spin :spinning="loading">
        <div class="mobile-form-container">
            <div v-if="isInviteCodeValid">
                <div class="form-header">
                    <h2 class="form-title">保单信息填写</h2>
                    <p class="form-subtitle">请填写以下信息完成保单申请</p>
                </div>

                <div class="form-content">
                    <div class="form-step" :class="{ 'active': currentStep === 1 }">
                        <mobile-policyholder-form ref="policyholderFormRef"
                            v-model:form-data="formData.policyholderData" />
                    </div>

                    <div class="form-step" :class="{ 'active': currentStep === 2 }">
                        <mobile-insured-form ref="insuredFormRef" v-model:form-data="formData.insuredData"
                            :policyholder-data="formData.policyholderData" />
                    </div>

                    <div class="form-step" :class="{ 'active': currentStep === 3 }">
                        <mobile-beneficiary-form ref="beneficiaryFormRef" v-model:form-data="formData.beneficiaryData"
                            :policyholder-data="formData.policyholderData" :insured-data="formData.insuredData" />
                    </div>
                </div>

                <div class="form-actions">
                    <a-button v-if="currentStep > 1" @click="prevStep" class="action-btn prev-btn">
                        上一步
                    </a-button>

                    <a-button v-if="currentStep < 3" type="primary" @click="nextStep" :loading="validating"
                        class="action-btn next-btn">
                        下一步
                    </a-button>

                    <a-button v-if="currentStep === 3" type="primary" @click="submitForm" :loading="submitting"
                        class="action-btn submit-btn">
                        提交
                    </a-button>
                </div>

                <!-- <div class="form-progress">
                <div class="progress-dots">
                    <div v-for="step in 3" :key="step" class="progress-dot"
                        :class="{ 'active': step <= currentStep, 'completed': step < currentStep }"
                        @click="goToStep(step)">
                        <span class="dot-number">{{ step }}</span>
                    </div>
                </div>
                <div class="progress-labels">
                    <span class="progress-label" :class="{ 'active': currentStep === 1 }">投保人</span>
                    <span class="progress-label" :class="{ 'active': currentStep === 2 }">被保人</span>
                    <span class="progress-label" :class="{ 'active': currentStep === 3 }">受益人</span>
                </div>
            </div> -->
            </div>
            <div v-else class="invalid-code">
                <div class="invalid-code-content">
                    <div class="flex justify-center items-center">
                        <Icon icon="material-symbols:error-outline" class="error-icon" />
                    </div>

                    <h2>邀请码无效</h2>
                    <p>您使用的邀请链接已失效或不存在，请联系您的保险代理人获取新的邀请链接。</p>
                </div>
            </div>
        </div>
    </a-spin>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { Icon } from '@iconify/vue';
import { formAPI } from '../../api/form';
import MobilePolicyholderForm from './components/mobile/MobilePolicyholderForm.vue';
import MobileInsuredForm from './components/mobile/MobileInsuredForm.vue';
import MobileBeneficiaryForm from './components/mobile/MobileBeneficiaryForm.vue';

const router = useRouter();
const isInviteCodeValid = ref(false);
const inviteCode = ref('');
const currentStep = ref(1);
const validating = ref(false);
const submitting = ref(false);
const loading = ref(true);
// 表单引用
const policyholderFormRef = ref(null);
const insuredFormRef = ref(null);
const beneficiaryFormRef = ref(null);

// 表单数据
const formData = reactive({
    policyholderData: {},
    insuredData: {},
    beneficiaryData: []
});

onMounted(async () => {
    // 从路由中获取邀请码
    const uuid = router.currentRoute.value.params.id;
    inviteCode.value = uuid;

    try {
        loading.value = true;
        // 验证邀请码
        const isValid = await formAPI.validateFormInvite(uuid);
        isInviteCodeValid.value = isValid;

        if (!isValid) {
            message.error('邀请码无效，请联系您的保险代理人');
        }
    } catch (error) {
        console.error('验证邀请码失败:', error);
        message.error('验证邀请码失败，请稍后再试');
        isInviteCodeValid.value = false;
        loading.value = false;
    } finally {
        loading.value = false;
    }
});

// 下一步
const nextStep = async () => {
    validating.value = true;

    try {
        if (currentStep.value === 1) {
            // 验证投保人表单
            await policyholderFormRef.value.validate();
        } else if (currentStep.value === 2) {
            // 验证被保人表单
            await insuredFormRef.value.validate();
        }

        // 验证通过，进入下一步
        currentStep.value += 1;
    } catch (error) {
        console.error('表单验证失败:', error);
        message.error(typeof error === 'string' ? error : '请完善表单信息');
    } finally {
        validating.value = false;
    }
};

// 上一步
const prevStep = () => {
    if (currentStep.value > 1) {
        currentStep.value -= 1;
    }
};

// 跳转到指定步骤
const goToStep = (step) => {
    // 只允许跳转到已完成的步骤或当前步骤的下一步
    if (step <= currentStep.value || step === currentStep.value + 1) {
        currentStep.value = step;
    }
};

// 提交表单信息
const submitForm = async () => {
    submitting.value = true;
    try {
        // 验证受益人表单
        await beneficiaryFormRef.value.validate();        // 获取附件列表
        const attachments = beneficiaryFormRef.value.attachmentList;
        console.log('attachments', beneficiaryFormRef.value.attachmentList);

        // 先上传附件
        if (attachments && attachments.length > 0) {
            message.loading('正在上传附件...', 0);
            const formData = new FormData();

            // 循环添加每个文件
            for (let i = 0; i < attachments.length; i++) {
                formData.append('files', attachments[i].originFileObj);
            }

            formData.append('status', 'APPOINTMENT');

            await formAPI.submitFormAttachment(formData, inviteCode.value);

            message.destroy();
            message.success('附件上传成功');
        }
        // 将insuredInfo的birthdate炸转换为时间戳
        if (formData.insuredData.birthDate) {
            formData.insuredData.birthDate = formData.insuredData.birthDate.valueOf();
        }
        // 构建提交数据
        const submitData = {
            policyholderInfo: formData.policyholderData,
            insuredInfo: formData.insuredData,
            beneficiaryInfo: formData.beneficiaryData
        };

        // 提交表单
        await formAPI.submitFormInvite(inviteCode.value, submitData);
        message.success('表单提交成功');
        showSuccessPage();
    } catch (error) {
        console.error('表单提交失败:', error);
        message.error(typeof error === 'string' ? error : '表单提交失败，请检查填写信息');
    } finally {
        submitting.value = false;
    }
};

// 显示成功页面
const showSuccessPage = () => {
    // 这里可以跳转到成功页面或显示成功信息
    router.push('/form/success');
};
</script>

<style scoped>
.mobile-form-container {
    padding: 16px;
    max-width: 600px;
    margin: 0 auto;
    min-height: 100vh;
    background-color: #f5f7fa;
}

.form-header {
    text-align: center;
    margin-bottom: 24px;
    padding: 16px 0;
}

.form-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.form-subtitle {
    font-size: 14px;
    color: #666;
}

.form-content {
    position: relative;
    min-height: 400px;
}

.form-step {
    display: none;
}

.form-step.active {
    display: block;
}

.form-actions {
    display: flex;
    justify-content: space-between;
    padding: 16px 0;
    padding-top: 0;
}

.action-btn {
    padding: 0 20px;
    height: 40px;
    font-size: 16px;
    border-radius: 20px;
}

.prev-btn {
    min-width: 100px;
}

.next-btn,
.submit-btn {
    min-width: 100px;
}

.form-progress {
    margin-top: 32px;
    padding-top: 16px;
}

.progress-dots {
    display: flex;
    justify-content: space-between;
    position: relative;
    margin: 0 40px;
}

.progress-dots::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #e8e8e8;
    transform: translateY(-50%);
    z-index: 0;
}

.progress-dot {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #fff;
    border: 2px solid #e8e8e8;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 1;
    cursor: pointer;
    transition: all 0.3s;
}

.progress-dot.active {
    border-color: #1890ff;
    background-color: #1890ff;
    color: #fff;
}

.progress-dot.completed {
    border-color: #52c41a;
    background-color: #52c41a;
    color: #fff;
}

.dot-number {
    font-size: 14px;
    font-weight: 500;
}

.progress-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    padding: 0 20px;
}

.progress-label {
    font-size: 14px;
    color: #999;
    transition: color 0.3s;
    flex: 1;
    text-align: center;
}

.progress-label.active {
    color: #1890ff;
    font-weight: 500;
}

.invalid-code {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    text-align: center;
    padding: 20px;
}

.invalid-code-content {
    background-color: #fff;
    border-radius: 12px;
    padding: 32px 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
}

.error-icon {
    font-size: 48px;
    color: #ff4d4f;
    margin-bottom: 16px;
}

.invalid-code h2 {
    font-size: 18px;
    margin-bottom: 12px;
    color: #333;
}

.invalid-code p {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
}

@media (max-width: 480px) {
    .mobile-form-container {
        padding: 12px;
    }

    .form-title {
        font-size: 18px;
    }

    .form-subtitle {
        font-size: 13px;
    }

    .action-btn {
        padding: 0 16px;
        height: 38px;
        font-size: 15px;
    }

    .progress-dot {
        width: 26px;
        height: 26px;
    }

    .dot-number {
        font-size: 13px;
    }

    .progress-label {
        font-size: 13px;
    }
}
</style>