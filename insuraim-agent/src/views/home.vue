<template>
  <div class="mx-auto space-y-8">
    <!-- 欢迎词和统计卡片容器 -->
    <div class="welcome-stats-card">
      <!-- 欢迎词部分 -->
      <div class="welcome-section">
        <h1 class="welcome-title">欢迎回来，{{ userName }}!</h1>
        <p class="welcome-date">{{ currentDate }} · {{ weekday }}</p>
      </div>
      
      <!-- 统计卡片部分 -->
      <div class="stats-cards">
        <div v-for="(card, index) in dataCards" :key="index" class="stat-card">
          <div class="stat-icon-bg">
            <Icon :icon="card.icon" class="stat-icon" />
          </div>
          <p class="stat-value">{{ card.value }}</p>
          <p class="stat-title">{{ card.title }}</p>
          <div :class="card.change.includes('下降') ? 'stat-change-down' : 'stat-change-up'">
            <Icon :icon="card.change.includes('下降') ? 'mdi:arrow-bottom-right-thick' : 'mdi:arrow-top-right-thick'" class="trend-icon" />
            <span>{{ card.change }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 左侧：业绩图表 -->
      <div class="lg:col-span-2 bg-white rounded-lg p-6">
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-lg font-semibold">业绩趋势</h2>
          <div class="flex space-x-2">
            <button v-for="(period, idx) in periods" :key="idx"
                    class="px-3 py-1 text-sm rounded-md transition-colors"
                    :class="selectedPeriod === period.value ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'"
                    @click="selectedPeriod = period.value">
              {{ period.label }}
            </button>
          </div>
        </div>
        
        <!-- ECharts图表区域 -->
        <div class="h-72 w-full">
          <v-chart class="chart" :option="chartOption" autoresize />
        </div>
      </div>

      <!-- 右侧：未处理事项 -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-lg font-semibold">待处理事项</h2>
          <a href="#" class="text-blue-500 text-sm hover:underline">全部</a>
        </div>
        
        <div class="space-y-4">
          <div v-for="(task, index) in pendingTasks" :key="index" 
               class="p-3 border border-gray-100 rounded-lg hover:bg-blue-50 cursor-pointer transition-all">
            <div class="flex justify-between items-start">
              <div class="flex items-start space-x-3">
                <div :class="`p-2 rounded-lg ${task.bgColor} ${task.textColor}`">
                  <Icon :icon="task.icon" class="w-5 h-5" />
                </div>
                <div>
                  <p class="font-medium">{{ task.title }}</p>
                  <p class="text-gray-500 text-sm mt-1">{{ task.description }}</p>
                </div>
              </div>
              <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">{{ task.deadline }}</span>
            </div>
          </div>
        </div>
        
        <div class="mt-4 text-center">
          <button class="text-blue-500 hover:text-blue-700 text-sm font-medium">
            查看更多
          </button>
        </div>
      </div>
    </div>

    <!-- 第三行：常用工具与最新资讯 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
      <!-- 常用工具 -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <h2 class="text-lg font-semibold mb-6">常用工具</h2>
        
        <div class="grid grid-cols-3 gap-4">
          <div v-for="(tool, index) in quickTools" :key="index" 
               class="flex flex-col items-center p-3 hover:bg-blue-50 rounded-lg cursor-pointer transition-all">
            <div :class="`w-12 h-12 rounded-lg ${tool.bgColor} flex items-center justify-center mb-2`">
              <Icon :icon="tool.icon" class="w-6 h-6 text-white" />
            </div>
            <span class="text-sm text-center">{{ tool.label }}</span>
          </div>
        </div>
      </div>
      
      <!-- 最新资讯 -->
      <div class="lg:col-span-2 bg-white rounded-xl shadow-sm p-6">
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-lg font-semibold">最新资讯</h2>
          <a href="#" class="text-blue-500 text-sm hover:underline">更多</a>
        </div>
        
        <div class="space-y-4">
          <div v-for="(news, index) in newsList" :key="index" 
               class="p-3 border-b border-gray-100 last:border-b-0 hover:bg-gray-50 cursor-pointer transition-all">
            <div class="flex justify-between">
              <div class="flex items-start space-x-3">
                <span class="px-2 py-1 text-xs rounded-md" :class="news.tagBg">{{ news.type }}</span>
                <div>
                  <p class="font-medium">{{ news.title }}</p>
                  <p class="text-gray-500 text-sm mt-1">{{ news.summary }}</p>
                </div>
              </div>
              <span class="text-gray-400 text-sm whitespace-nowrap">{{ news.date }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';

// 路由实例
const router = useRouter();

// 用户信息
const userName = ref('用户');
const userId = ref(null);

// 从localStorage获取用户信息
const getUserInfo = () => {
  try {
    const userInfoStr = localStorage.getItem('userInfo');
    if (userInfoStr) {
      const userInfo = JSON.parse(userInfoStr);
      userName.value = userInfo.name || userInfo.username || '用户';
      userId.value = userInfo.id;
    }
  } catch (error) {
    console.error('获取用户信息失败', error);
  }
};

// 组件挂载时获取用户信息
onMounted(() => {
  getUserInfo();
});

// 当前日期
const now = new Date();
const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
const currentDate = computed(() => {
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
});
const weekday = computed(() => weekdays[now.getDay()]);

// 数据概览卡片
const dataCards = ref([
  {
    title: '保单数量',
    value: '34',
    change: '+12.5%',
    changeColor: 'text-green-500',
    changeIcon: 'mdi:arrow-up',
    icon: 'solar:document-text-bold-duotone',
    iconBg: 'bg-blue-500',
    borderColor: 'border-blue-500'
  },
  {
    title: '被保人数',
    value: '108',
    change: '增长 8.3%',
    changeColor: 'text-green-500',
    changeIcon: 'mdi:arrow-up',
    icon: 'mdi:account-group',
    iconBg: 'bg-green-500',
    borderColor: 'border-green-500'
  },
  {
    title: '计划书数量',
    value: '61',
    change: '增长 5.2%',
    changeColor: 'text-green-500',
    changeIcon: 'mdi:arrow-up',
    icon: 'mdi:notebook',
    iconBg: 'bg-purple-500',
    borderColor: 'border-purple-500'
  },
  {
    title: '保费金额 (万)',
    value: '276.5',
    change: '下降 2.1%',
    changeColor: 'text-red-500',
    changeIcon: 'mdi:arrow-down',
    icon: 'mdi:currency-usd',
    iconBg: 'bg-orange-500',
    borderColor: 'border-orange-500'
  }
]);

// 业绩趋势图表相关
const periods = ref([
  { label: '本周', value: 'week' },
  { label: '本月', value: 'month' },
  { label: '本季度', value: 'quarter' },
  { label: '全年', value: 'year' }
]);
const selectedPeriod = ref('month');

// ECharts图表配置
const chartOption = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
    axisLine: {
      lineStyle: {
        color: '#e5e7eb'
      }
    },
    axisLabel: {
      color: '#6b7280'
    }
  },
  yAxis: {
    type: 'value',
    axisLine: {
      show: false
    },
    axisLabel: {
      color: '#6b7280'
    },
    splitLine: {
      lineStyle: {
        color: '#f3f4f6'
      }
    }
  },
  series: [{
    data: [56, 42, 76, 63, 82, 72, 90, 86, 92, 78, 34, 0],
    type: 'bar',
    showBackground: true,
    backgroundStyle: {
      color: 'rgba(243, 244, 246, 0.5)'
    },
    itemStyle: {
      color: '#3b82f6',
      borderRadius: [4, 4, 0, 0]
    },
    emphasis: {
      itemStyle: {
        color: '#2563eb'
      }
    },
    barWidth: '60%'
  }],
  grid: {
    left: '3%',
    right: '3%',
    bottom: '3%',
    containLabel: true
  }
});

// 待处理事项数据
const pendingTasks = ref([
  {
    icon: 'mdi:file-document',
    title: '计划书待审核',
    description: '保单号: #BX2023110512 等待您的审核',
    deadline: '今天',
    bgColor: 'bg-blue-100',
    textColor: 'text-blue-600'
  },
  {
    icon: 'mdi:account-alert',
    title: '客户资料待补充',
    description: '李先生的个人信息需要更新',
    deadline: '明天',
    bgColor: 'bg-orange-100',
    textColor: 'text-orange-600'
  },
  {
    icon: 'mdi:cash-check',
    title: '保费催收提醒',
    description: '3笔保费即将逾期，需要提醒客户',
    deadline: '3天后',
    bgColor: 'bg-red-100',
    textColor: 'text-red-600'
  },
  {
    icon: 'mdi:phone-in-talk',
    title: '客户回访安排',
    description: '5位新客户需要安排首次回访',
    deadline: '本周内',
    bgColor: 'bg-green-100',
    textColor: 'text-green-600'
  }
]);

// 快捷工具
const quickTools = ref([
  { icon: 'mdi:calculator', label: '保费计算', bgColor: 'bg-blue-500' },
  { icon: 'mdi:file-document-plus', label: '创建计划书', bgColor: 'bg-purple-500' },
  { icon: 'mdi:compare', label: '产品对比', bgColor: 'bg-green-500' },
  { icon: 'mdi:account-plus', label: '新增客户', bgColor: 'bg-orange-500' },
  { icon: 'mdi:chart-line', label: '需求分析', bgColor: 'bg-teal-500' },
  { icon: 'mdi:calendar-check', label: '日程安排', bgColor: 'bg-indigo-500' }
]);

// 最新资讯
const newsList = ref([
  {
    type: '产品更新',
    title: 'AXA安盛脑内膜计数乙保障健康计划推荐',
    summary: '提供全面保障，增加特定疾病的赔付比例，满足客户多样化保障需求',
    date: '2025-03-28',
    tagBg: 'bg-blue-100 text-blue-600'
  },
  {
    type: '政策动态',
    title: '关于调整大病医疗保险报销比例的政策解读',
    summary: '新政策将从2025年4月1日起实施，对保险销售有较大影响',
    date: '2025-03-26',
    tagBg: 'bg-green-100 text-green-600'
  },
  {
    type: '平台通知',
    title: 'LifeBee UserWeb 用户端是否登录接口更新',
    summary: '系统将于下周进行更新维护，更新期间部分功能将暂停使用',
    date: '2025-03-24',
    tagBg: 'bg-orange-100 text-orange-600'
  }
]);
</script>

<style scoped>
/* 欢迎词和统计卡片样式 */
.welcome-stats-card {
  position: relative;
  width: 100%;
  background: #FFFFFF;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
}

.welcome-section {
  margin-bottom: 30px;
}

.welcome-title {
  font-family: 'PingFang SC', sans-serif;
  font-style: normal;
  font-weight: 600;
  font-size: 18px;
  line-height: 20px;
  display: flex;
  align-items: center;
  text-transform: capitalize;
  color: #303133;
}

.welcome-date {
  font-family: 'PingFang SC', sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 17px;
  display: flex;
  align-items: center;
  text-transform: capitalize;
  color: #919399;
  margin-top: 15px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

/* 统计卡片样式 */
.stat-card {
  position: relative;
  box-sizing: border-box;
  height: 120px;
  background: #FAFCFF;
  border: 1px solid #E2E4EB;
  border-radius: 6px;
  padding: 12px;
}

.stat-icon-bg {
  position: absolute;
  width: 44px;
  height: 44px;
  left: 12px;
  top: 38px;
  background: #F1EEFF;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-icon {
  width: 24px;
  height: 24px;
  color: #401EEB;
}

.stat-value {
  position: absolute;
  font-family: 'PingFang SC', sans-serif;
  font-style: normal;
  font-weight: 600;
  font-size: 35px;
  line-height: 49px;
  display: flex;
  align-items: center;
  text-transform: capitalize;
  color: #303133;
  left: 72px;
  top: 23px;
}

.stat-title {
  position: absolute;
  font-family: 'PingFang SC', sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  display: flex;
  align-items: center;
  text-transform: capitalize;
  color: #919399;
  left: 72px;
  top: 77px;
}

.stat-change-up, .stat-change-down {
  position: absolute;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px;
  gap: 5px;
  right: 15px;
  top: 80px;
}

.stat-change-up span {
  font-family: 'PingFang SC', sans-serif;
  font-style: normal;
  font-weight: 500;
  font-size: 12px;
  line-height: 17px;
  display: flex;
  align-items: center;
  text-transform: capitalize;
  color: #22A979;
}

.stat-change-down span {
  font-family: 'PingFang SC', sans-serif;
  font-style: normal;
  font-weight: 500;
  font-size: 12px;
  line-height: 17px;
  display: flex;
  align-items: center;
  text-transform: capitalize;
  color: #F54A45;
}

.trend-icon {
  width: 16px;
  height: 16px;
}

.stat-change-up .trend-icon {
  color: #22A979;
}

.stat-change-down .trend-icon {
  color: #F54A45;
}

/* 清理旧图表样式 */
.performance-chart {
  display: none;
}

/* ECharts容器样式 */
.chart {
  width: 100%;
  height: 100%;
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 卡片悬停动画 */
.card-hover {
  transition: transform 0.2s ease;
}
.card-hover:hover {
  transform: translateY(-2px);
}

/* 清理所有旧图表相关样式 */
.performance-chart {
  display: none !important;
}

/* 响应式调整 */
@media (max-width: 1280px) {
  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .stats-cards {
    grid-template-columns: 1fr;
  }
}
</style> 