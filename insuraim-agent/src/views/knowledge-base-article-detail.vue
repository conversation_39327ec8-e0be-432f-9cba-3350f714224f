<template>
  <div class="mx-auto p-8 bg-white rounded-xl min-h-screen">
    <!-- 加载中状态 - 整体页面加载 -->
    <div v-if="pageLoading" class="flex flex-col justify-center items-center py-32">
      <div class="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-blue-500 mb-4"></div>
      <p class="text-gray-600">加载中，请稍候...</p>
    </div>

    <!-- 页面内容 - 所有API响应完毕后显示 -->
    <div v-else>

      <!-- 错误状态 -->
      <div v-if="error" class="bg-gray-50 rounded-lg p-8 text-center">
        <Icon icon="mdi:alert-circle-outline" class="h-16 w-16 text-red-500 mx-auto mb-4" />
        <h3 class="text-xl font-semibold text-gray-700 mb-2">加载失败</h3>
        <p class="text-red-500 mb-4">{{ error }}</p>
        <div class="flex justify-center gap-4">
          <button 
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            @click="reloadData"
          >
            重新加载
          </button>
          <button 
            class="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
            @click="goBackToList"
          >
            返回列表
          </button>
        </div>
      </div>

      <!-- 未找到内容状态 -->
      <div v-else-if="!knowledgeItem" class="bg-gray-50 rounded-lg p-8 text-center">
        <Icon icon="mdi:file-search-outline" class="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h3 class="text-xl font-semibold text-gray-700 mb-2">未找到知识点</h3>
        <p class="text-gray-500">该知识点可能已被删除或移动</p>
        <button 
          class="px-6 py-2 mt-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm"
          @click="goBackToList"
        >
          返回列表
        </button>
      </div>

      <!-- 文章内容区域 -->
      <div v-else class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-100 p-6">
          <!-- 详情头部 -->
          <div class="mb-6">
            <div class="flex items-center mb-3">
              <Icon v-if="knowledgeItem.type === 'file'" icon="mdi:file-pdf-box" class="h-7 w-7 text-red-500 mr-2" />
              <h1 class="text-2xl font-bold text-gray-800">{{ knowledgeItem.title }}</h1>
            </div>
            <div class="flex items-center text-sm text-gray-500 justify-between flex-wrap gap-y-2">
              <div class="flex items-center">
                <span class="flex items-center mr-4">
                  <Icon icon="mdi:calendar" class="h-5 w-5 mr-1" />
                  <span>更新于 {{ formatDate(knowledgeItem.updateDate) }}</span>
                </span>
                <span class="flex items-center">
                  <Icon icon="mdi:eye" class="h-5 w-5 mr-1" />
                  <span>{{ knowledgeItem.views }} 次查看</span>
                </span>
              </div>
              <div class="flex items-center">
                <span class="mr-2">作者:</span>
                <span class="font-medium">{{ knowledgeItem.author }}</span>
              </div>
            </div>
          </div>
          
          <!-- 内容 -->
          <div v-if="knowledgeItem.content" class="content prose max-w-none mb-8" v-html="knowledgeItem.content"></div>
          
          <!-- PDF预览 -->
          <div v-else-if="knowledgeItem.attachments && knowledgeItem.attachments.length > 0" class="pdf-container mb-8">
            <div v-if="pdfLoading" class="pdf-loading">
              <div class="animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-blue-500"></div>
              <p class="mt-2 text-gray-600">PDF加载中，请稍候...</p>
            </div>
            <PDF 
              v-if="pdfUrl" 
              :src="pdfUrl" 
              :pdfWidth="1000"
              @onComplete="handlePdfLoaded" 
              @onError="handlePdfError"
              class="pdf-viewer"
            />
            <div v-if="!pdfUrl" class="text-center py-8">
              <Icon icon="mdi:file-alert-outline" class="h-16 w-16 text-red-500 mx-auto mb-4" />
              <p class="text-gray-700">PDF文件路径无效或文件不存在</p>
            </div>
          </div>
          
          <!-- 相关文档 -->
          <div class="mt-6 pt-4 border-t border-gray-100">
            <h4 class="text-sm font-medium text-gray-700 mb-3">相关文档</h4>
            <div class="space-y-2">
              <div 
                v-for="attachment in knowledgeItem.attachments" 
                :key="attachment.id"
                class="flex items-center p-2 border border-gray-200 rounded-md hover:bg-gray-50 transition-colors"
              >
                <Icon :icon="getFileIcon(attachment.fileType)" class="h-5 w-5 text-red-500 mr-2" />
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium text-gray-700 truncate">{{ attachment.fileName }}</p>
                  <p class="text-xs text-gray-500">{{ formatFileSize(attachment.fileSize) }} · {{ attachment.fileType.toUpperCase() }}</p>
                </div>
                <button 
                  class="ml-2 p-1 text-blue-600 hover:text-blue-800 rounded-full hover:bg-blue-50 transition-colors"
                  @click="downloadFile(attachment)"
                >
                  <Icon icon="mdi:download" class="h-5 w-5" />
                </button>
              </div>
              <div v-if="!knowledgeItem.attachments || knowledgeItem.attachments.length === 0" class="text-gray-500 text-sm italic py-2">
                暂无相关文档
              </div>
            </div>
          </div>
          
          <!-- 底部按钮 -->
          <div class="mt-8 flex justify-center">
            <button 
              class="px-6 py-2 bg-gray-100 text-gray-800 rounded-lg hover:bg-gray-200 transition-colors shadow-sm flex items-center"
              @click="goBackToList"
            >
              <Icon icon="mdi:arrow-left" class="mr-2" />
              返回列表
            </button>
          </div>
        </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Icon } from '@iconify/vue';
import dayjs from 'dayjs';
import { knowledgeBaseAPI } from '@/api';
import PDF from 'pdf-vue3';
import { message } from 'ant-design-vue';

// 路由信息
const route = useRoute();
const router = useRouter();
const categoryId = computed(() => route.query.categoryId || '1');
const itemId = computed(() => route.query.itemId);

// 状态
const loading = ref(false);
const error = ref('');
const knowledgeItem = ref(null);
const pageLoading = ref(true);  // 整体页面加载状态
const pdfLoading = ref(false);  // PDF加载状态
const pdfUrl = ref('');         // PDF预览URL

// 返回列表页
const goBackToList = () => {
  router.push({
    name: 'KnowledgeBaseDetail',
    params: { id: categoryId.value }
  });
};


// 处理PDF加载完成
const handlePdfLoaded = () => {
  console.log('PDF加载完成 - onComplete事件触发');
  pdfLoading.value = false;
};

// 处理PDF加载错误
const handlePdfError = (error) => {
  console.error('PDF加载错误 - onError事件触发:', error);
  pdfLoading.value = false;
  message.error('PDF文件加载失败');
};

// 格式化日期 - 处理时间戳
const formatDate = (timestamp) => {
  // 处理后端返回的时间戳（毫秒）
  if (!timestamp) return '';
  return dayjs(Number(timestamp)).format('YYYY-MM-DD');
};

// 格式化标签 - 将逗号分隔的字符串转换为数组
const formatTags = (tagsString) => {
  if (!tagsString) return [];
  return tagsString.split(',').map(tag => tag.trim()).filter(tag => tag);
};



// 获取知识库详情
const fetchKnowledgeDetail = async () => {
  if (!itemId.value) {
    error.value = '未提供知识点ID';
    pageLoading.value = false;
    return;
  }

  loading.value = true;
  error.value = '';
  
  try {
    const response = await knowledgeBaseAPI.getLoreDetail(itemId.value);
    
    if (response) {
      // 格式化后端返回的数据
      knowledgeItem.value = {
        id: response.kbId || response.id,
        title: response.title,
        content: response.content,
        summary: response.summary || '',
        updateDate: response.updatedAt, // 时间戳
        author: response.authorId ? `作者${response.authorId}` : '未知作者',
        views: response.views || 0,
        tags: response.tags || '',
        type: response.type || 'article', // 默认为文章类型
        attachments: response.attachments || []
      };
      
      // 处理PDF预览逻辑
      if (!knowledgeItem.value.content && knowledgeItem.value.attachments && knowledgeItem.value.attachments.length > 0) {
        // 找到第一个PDF类型的附件
        const pdfAttachment = knowledgeItem.value.attachments.find(attach => 
          attach.fileType === 'pdf' || attach.fileType === 'application/pdf');
        
        if (pdfAttachment) {
          pdfUrl.value = pdfAttachment.filePath;
          pdfLoading.value = true;
          console.log('正在加载PDF文件:', pdfUrl.value);
        }
      }
    } else {
      error.value = '未找到相关知识库详情';
    }
  } catch (err) {
    console.error('获取知识库详情失败:', err);
    error.value = '获取知识库详情失败，请稍后重试';
  } finally {
    loading.value = false;
  }
};

// 重新加载数据
const reloadData = async () => {
  pageLoading.value = true;
  error.value = '';
  
  try {
    // 重新获取详情数据
    await fetchKnowledgeDetail();
  } catch (err) {
    console.error('重新加载数据失败:', err);
    error.value = '加载数据失败，请稍后重试';
  } finally {
    pageLoading.value = false;
  }
};

// 获取知识库分类和详情信息
onMounted(async () => {
  pageLoading.value = true;
  error.value = '';
  
  try {
    // 获取详情数据
    await fetchKnowledgeDetail();
  } catch (err) {
    console.error('获取数据失败:', err);
    error.value = '加载数据失败，请稍后重试';
  } finally {
    // 所有API响应完毕，关闭页面加载状态
    pageLoading.value = false;
  }
});

// 根据分类ID获取图标
const getIconForCategory = (categoryId) => {
  const icons = {
    1: 'mdi:book-open-variant',
    2: 'mdi:clipboard-text-outline',
    3: 'mdi:certificate-outline',
    4: 'mdi:file-document-outline',
    5: 'mdi:notebook-outline'
  };
  
  return icons[categoryId] || 'mdi:folder-outline';
};

// 根据分类ID获取背景色
const getColorForCategory = (categoryId) => {
  const colors = {
    1: 'bg-indigo-900',
    2: 'bg-amber-800',
    3: 'bg-emerald-800',
    4: 'bg-blue-800',
    5: 'bg-purple-800'
  };
  
  return colors[categoryId] || 'bg-gray-800';
};

// 获取文件图标
const getFileIcon = (fileType) => {
  if (!fileType) return 'mdi:file-outline';
  
  // 转换为小写并去除空格
  const type = fileType.toLowerCase().trim();
  
  const icons = {
    'pdf': 'mdi:file-pdf-box',
    'doc': 'mdi:file-word',
    'docx': 'mdi:file-word',
    'xls': 'mdi:file-excel',
    'xlsx': 'mdi:file-excel',
    'ppt': 'mdi:file-powerpoint',
    'pptx': 'mdi:file-powerpoint',
    'txt': 'mdi:file-text',
    'jpg': 'mdi:file-image',
    'jpeg': 'mdi:file-image',
    'png': 'mdi:file-image',
    'gif': 'mdi:file-image',
    'mp4': 'mdi:file-video',
    'mov': 'mdi:file-video',
    'mp3': 'mdi:file-audio',
    'wav': 'mdi:file-audio',
    'zip': 'mdi:file-zip',
    'rar': 'mdi:file-zip',
    '7z': 'mdi:file-zip',
    // MIME类型
    'application/pdf': 'mdi:file-pdf-box',
    'application/msword': 'mdi:file-word',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'mdi:file-word',
    'application/vnd.ms-excel': 'mdi:file-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'mdi:file-excel',
    'application/vnd.ms-powerpoint': 'mdi:file-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'mdi:file-powerpoint',
    'text/plain': 'mdi:file-text',
    'image/jpeg': 'mdi:file-image',
    'image/png': 'mdi:file-image',
    'image/gif': 'mdi:file-image',
    'video/mp4': 'mdi:file-video',
    'video/mpeg': 'mdi:file-video',
    'video/webm': 'mdi:file-video',
    'audio/mpeg': 'mdi:file-audio',
    'audio/wav': 'mdi:file-audio',
    'audio/ogg': 'mdi:file-audio',
    'application/zip': 'mdi:file-zip',
    'application/gzip': 'mdi:file-zip',
    'application/x-rar-compressed': 'mdi:file-zip',
    'application/x-7z-compressed': 'mdi:file-zip'
  };
  
  return icons[type] || 'mdi:file-outline';
};

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return '';
  if (size < 1024) return size + ' B';
  if (size < 1024 * 1024) return (size / 1024).toFixed(2) + ' KB';
  if (size < 1024 * 1024 * 1024) return (size / (1024 * 1024)).toFixed(2) + ' MB';
  return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
};

// 下载文件
const downloadFile = (file) => {
  if (!file || !file.filePath) {
    message.error('下载失败：文件路径无效');
    return;
  }

  message.loading({ content: `正在下载 ${file.fileName}...`, key: 'download' });

  fetch(file.filePath)
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      return response.blob();
    })
    .then(blob => {
      // 创建blob链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      
      // 确保文件名包含正确的扩展名
      let fileName = file.fileName || '未命名文件';
      
      // 获取文件类型对应的扩展名
      const fileExtension = getFileExtension(file.fileType);
      
      // 检查文件名是否已包含扩展名
      if (fileExtension && !fileName.toLowerCase().endsWith('.' + fileExtension.toLowerCase())) {
        fileName = `${fileName}.${fileExtension}`;
      }
      
      link.download = fileName;
      document.body.appendChild(link);
      
      // 触发下载
      link.click();
      
      // 清理
      window.URL.revokeObjectURL(url);
      document.body.removeChild(link);
      
      message.success({ content: `${fileName} 下载成功`, key: 'download' });
      console.log(`文件下载成功: ${fileName}`);
    })
    .catch(err => {
      message.error({ content: `下载失败: ${err.message}`, key: 'download' });
      console.error(`文件下载失败: ${file.fileName}`, err);
    });
};

// 根据文件类型获取对应的扩展名
const getFileExtension = (fileType) => {
  if (!fileType) return '';
  
  const fileTypeMap = {
    'pdf': 'pdf',
    'application/pdf': 'pdf',
    'doc': 'doc',
    'docx': 'docx',
    'application/msword': 'doc',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
    'xls': 'xls',
    'xlsx': 'xlsx',
    'application/vnd.ms-excel': 'xls',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
    'ppt': 'ppt',
    'pptx': 'pptx',
    'application/vnd.ms-powerpoint': 'ppt',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'pptx',
    'txt': 'txt',
    'text/plain': 'txt',
    'jpg': 'jpg',
    'jpeg': 'jpeg',
    'image/jpeg': 'jpg',
    'png': 'png',
    'image/png': 'png',
    'gif': 'gif',
    'image/gif': 'gif',
    'mp4': 'mp4',
    'video/mp4': 'mp4',
    'mp3': 'mp3',
    'audio/mpeg': 'mp3',
    'zip': 'zip',
    'application/zip': 'zip',
    'rar': 'rar',
    'application/x-rar-compressed': 'rar'
  };
  
  // 转换为小写并去除空格
  const type = fileType.toLowerCase().trim();
  
  return fileTypeMap[type] || '';
};
</script>

<style scoped>
:deep(.content img) {
  max-width: 100%;
  height: auto;
}

:deep(.content h2) {
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  color: #1f2937;
}

:deep(.content h3) {
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 1.25rem;
  margin-bottom: 0.75rem;
  color: #374151;
}

:deep(.content p) {
  margin-bottom: 1rem;
  line-height: 1.6;
}

:deep(.content ul), :deep(.content ol) {
  padding-left: 1.5rem;
  margin-bottom: 1rem;
}

:deep(.content li) {
  margin-bottom: 0.5rem;
}

.prose {
  max-width: none;
}

/* PDF预览相关样式 */
.pdf-container {
  width: 100%;
  min-height: 500px;
  position: relative;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  overflow: hidden;
}

.pdf-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 10;
}

:deep(.pdf-viewer) {
  width: 100%;
  height: 100%;
}

:deep(.pdf-viewer canvas) {
  margin: 0 auto;
}
</style> 