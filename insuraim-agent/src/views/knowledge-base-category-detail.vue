<template>
  <div class="mx-auto p-8 bg-white rounded-xl min-h-screen">
    <!-- 加载中状态 - 整体页面加载 -->
    <div v-if="pageLoading" class="flex flex-col justify-center items-center py-32">
      <div class="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-blue-500 mb-4"></div>
      <p class="text-gray-600">加载中，请稍候...</p>
    </div>

    <!-- 页面内容 - 所有API响应完毕后显示 -->
    <div v-else>
      <!-- 页面标题 -->
      <div class="mb-8">
        <div class="flex items-center gap-4">
          <div :class="`w-16 h-16 rounded-lg flex items-center justify-center ${getColorForCategory(categoryInfo.id)}`">
            <Icon :icon="getIconForCategory(categoryInfo.id)" class="h-10 w-10 text-white" />
          </div>
          <div>
            <h1 class="text-3xl font-bold text-gray-800">{{ categoryInfo.categoryName || categoryInfo.name }}</h1>
            <p class="text-gray-600 mt-1">{{ categoryInfo.description }}</p>
          </div>
        </div>
      </div>

    
      <!-- 搜索栏与返回按钮容器 -->
      <div v-if="isLeafCategory" class="mb-8 flex items-center gap-4">
        <!-- 返回按钮 -->
        <button 
          class="px-4 py-2 bg-gray-100 text-gray-800 rounded-lg hover:bg-gray-200 transition-colors shadow-sm flex items-center flex-shrink-0"
          @click="goBackToCategoryList"
        >
          <Icon icon="mdi:arrow-left" class="mr-1" />
          返回
        </button>
        
        <!-- 搜索栏 -->
        <div class="flex-grow flex gap-4">
          <input 
            type="text" 
            placeholder="搜索知识点..." 
            class="flex-1 px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            v-model="searchQuery"
          />
          <button 
            class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm"
            @click="searchKnowledge"
          >
            搜索
          </button>
        </div>
      </div>
      
      <!-- 错误状态 -->
      <div v-if="error" class="py-8 text-center">
        <Icon icon="mdi:alert-circle-outline" class="h-16 w-16 text-red-500 mx-auto mb-4" />
        <h3 class="text-xl font-semibold text-gray-700 mb-2">加载失败</h3>
        <p class="text-red-500 mb-4">{{ error }}</p>
        <button 
          @click="reloadData" 
          class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
        >
          重新加载
        </button>
      </div>

      <!-- 非叶子节点提示 -->
      <div v-else-if="!isLeafCategory" class="bg-gray-50 rounded-lg p-8 text-center">
        <Icon icon="mdi:folder-outline" class="h-16 w-16 text-amber-500 mx-auto mb-4" />
        <h3 class="text-xl font-semibold text-gray-700 mb-2">请选择子分类</h3>
        <p class="text-gray-500">当前分类包含子分类，请从左侧导航选择一个没有子分类的分类来查看文章列表</p>
        <div class="mt-6 flex justify-center">
          <div class="inline-flex items-center text-sm text-blue-600 bg-blue-50 px-3 py-2 rounded-lg">
            <Icon icon="mdi:information-outline" class="mr-2" />
            <span>只有末级分类（没有子分类的分类）可以查看文章列表</span>
          </div>
        </div>
      </div>

      <!-- 文章列表 - 只有叶子节点才显示 -->
      <div v-else-if="isLeafCategory">
        <!-- 主要内容 - 卡片式布局 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div 
            v-for="(item, index) in filteredKnowledgeItems" 
            :key="index"
            class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-all border border-gray-100 flex flex-col"
          >
            <!-- 卡片头部 -->
            <div class="p-4 border-b border-gray-100">
              <div class="flex items-start mb-1">
                <Icon v-if="item.type === 'file'" icon="mdi:file-pdf-box" class="h-5 w-5 text-red-500 mr-2 flex-shrink-0 mt-1" />
                <h3 class="text-lg font-semibold text-gray-800 line-clamp-2 h-14">{{ item.title }}</h3>
              </div>
              <div class="flex items-center text-sm text-gray-500 mt-1">
                <span class="flex items-center mr-4">
                  <Icon icon="mdi:calendar" class="mr-1" />
                  {{ formatDate(item.updateDate) }}
                </span>
                <span class="flex items-center">
                  <Icon icon="mdi:eye" class="mr-1" />
                  {{ item.views }} 次查看
                </span>
              </div>
            </div>
            
            <!-- 卡片内容 -->
            <div class="p-4 flex-grow flex flex-col">
              
              <!-- 标签 -->
              <div class="flex flex-wrap gap-2 mb-4">
                <span 
                  v-for="(tag, tagIndex) in item.tags" 
                  :key="tagIndex"
                  class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full"
                >
                  {{ tag }}
                </span>
              </div>
              
              <!-- 阅读按钮 -->
              <button 
                class="px-4 py-2 w-full mt-auto bg-gray-50 text-blue-600 rounded-lg hover:bg-gray-100 transition-colors border border-gray-200 flex items-center justify-center"
                @click="openKnowledgeDetail(item)"
              >
                <span>查看详情</span>
                <Icon icon="mdi:arrow-right" class="ml-1" />
              </button>
            </div>
          </div>
        </div>

        <!-- 无数据显示 -->
        <div v-if="isLeafCategory && filteredKnowledgeItems.length === 0" class="bg-gray-50 rounded-lg p-8 text-center">
          <Icon icon="mdi:file-search-outline" class="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 class="text-xl font-semibold text-gray-700 mb-2">{{ searchQuery ? '未找到相关知识点' : '暂无知识点' }}</h3>
          <p class="text-gray-500">{{ searchQuery ? '尝试使用其他关键词搜索' : '该分类下暂无内容' }}</p>
          <button 
            v-if="searchQuery"
            class="px-6 py-2 mt-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm"
            @click="clearSearch"
          >
            查看全部
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Icon } from '@iconify/vue';
import dayjs from 'dayjs';
import { knowledgeBaseAPI } from '@/api';



// 路由信息
const route = useRoute();
const router = useRouter();
const categoryId = computed(() => route.params.id);
const companyId = ref('');
const companyName = ref('');
const categoryInfo = ref({});

const error = ref('');
const knowledgeItems = ref([]);
const pageLoading = ref(true);

// 搜索功能
const searchQuery = ref('');
const searchKnowledge = () => {
  // 本地搜索已加载的数据
  console.log(`Searching for: ${searchQuery.value}`);
};
const clearSearch = () => {
  searchQuery.value = '';
};

// 判断当前分类是否为叶子节点（没有子分类）
const isLeafCategory = computed(() => {
  const hasChildren = categoryInfo.value.children && categoryInfo.value.children.length > 0;
  console.log('分类信息:', categoryInfo.value);
  console.log('是否有子节点:', hasChildren, '子节点数量:', categoryInfo.value.children?.length);
  return !hasChildren;
});

// 筛选知识库条目
const filteredKnowledgeItems = computed(() => {
  if (!searchQuery.value) return knowledgeItems.value;
  
  const query = searchQuery.value.toLowerCase();
  return knowledgeItems.value.filter(item => 
    item.title.toLowerCase().includes(query) || 
    (item.summary && item.summary.toLowerCase().includes(query)) ||
    (item.tags && item.tags.some(tag => tag.toLowerCase().includes(query)))
  );
});

// 面包屑初始化函数已移除

// 获取分类详情信息
const fetchCategoryInfo = async () => {
    try {
        console.log('开始获取分类详情, ID:', categoryId.value);
        
        // 请求分类树信息
        const response = await knowledgeBaseAPI.getCategoryTreeById(categoryId.value);
        console.log('获取到分类详情:', response);
        
        if (response) {
            categoryInfo.value = response;
            
            // 更新公司ID和名称
            if (response.companyId) {
                companyId.value = response.companyId;
            }
        } else {
            console.error('未获取到分类详情数据');
            throw new Error('未获取到分类详情数据');
        }
    } catch (err) {
        console.error('获取分类详情失败:', err);
        error.value = `获取分类详情失败: ${err.message}`;
        throw err;
    }
};

// 获取知识库列表数据 - 只有叶子节点才需要获取
const fetchKnowledgeList = async () => {
    // 如果不是叶子节点，则不需要获取列表
    if (!isLeafCategory.value) {
        console.log('非叶子节点，跳过获取文章列表');
        return;
    }
    
    try {
        console.log('开始获取分类文章列表, 分类ID:', categoryId.value);
        const response = await knowledgeBaseAPI.getLoreList(categoryId.value);
        console.log('获取到文章列表:', response);
        
        const formattedData = (response || []).map(item => ({
            id: item.id,
            title: item.title,
            summary: item.summary || '',
            updateDate: item.updatedAt, // 时间戳
            author: item.authorId ? `作者${item.authorId}` : '未知作者',
            views: item.views || 0,
            tags: formatTags(item.tags),
            type: item.type || 'article' // 默认为文章类型
        }));
        
        knowledgeItems.value = formattedData;
        console.log('文章列表处理完成, 总数:', formattedData.length);
    } catch (err) {
        console.error('获取知识库列表失败:', err);
        error.value = `获取文章列表失败: ${err.message}`;
        throw err; 
    }
};

// 处理分类树节点选择
const handleCategorySelect = (category) => {
    console.log('分类详情页接收到选择事件:', category.id, category.name);
    
    // 判断是否为叶子节点（没有子分类）
    const isLeaf = !category.children || category.children.length === 0;
    
    if (isLeaf) {
        console.log('选择叶子节点，导航到该分类:', category.id);
        
        // 如果当前已经在该分类页面，则直接重新加载数据
        if (category.id.toString() === categoryId.value.toString()) {
            console.log('已在当前分类，重新加载数据');
            reloadData();
        } else {
            // 保存选中的分类信息到localStorage
            saveKnowledgeInfo(category, companyId.value, companyName.value);
            
            // 导航到该分类详情页，查看其文章列表
            router.push({
                name: 'KnowledgeBaseDetail',
                params: { id: category.id }
            });
        }
    } else {
        console.log('选择文件夹节点，不执行跳转');
        // 非叶子节点，不执行跳转，自动通过树组件展开子节点
    }
};

// 跳转到详情页
const openKnowledgeDetail = (item) => {
    
    // 路由导航到详情页
    router.push({
        name: 'KnowledgeBaseItem',
        query: { 
            itemId: item.id,
            categoryId: categoryId.value
        }
    });
};

// 格式化日期 - 处理时间戳
const formatDate = (timestamp) => {
  // 处理后端返回的时间戳（毫秒）
  if (!timestamp) return '';
  return dayjs(Number(timestamp)).format('YYYY-MM-DD');
};

// 格式化标签 - 将逗号分隔的字符串转换为数组
const formatTags = (tagsString) => {
  if (!tagsString) return [];
  return tagsString.split(',').map(tag => tag.trim()).filter(tag => tag);
};

// 监听分类ID变化，重新加载数据
watch(() => route.params.id, (newId) => {
  if (newId) {
    console.log('分类ID变更为:', newId);
    pageLoading.value = true;
    reloadData();
  }
});

// 重新加载数据
const reloadData = async () => {
  pageLoading.value = true;
  error.value = '';
  
  try {
    // 获取分类详情
    await fetchCategoryInfo();
    
    // 只有叶子节点才获取文章列表
    if (isLeafCategory.value) {
        await fetchKnowledgeList();
    }
  } catch (err) {
    console.error('重新加载数据失败:', err);
    error.value = '加载数据失败，请稍后重试';
  } finally {
    pageLoading.value = false;
  }
};

// 获取知识库分类信息和列表数据
onMounted(async () => {
  pageLoading.value = true;
  error.value = '';
  
  try {
    await reloadData();
  } catch (err) {
    console.error('获取数据失败:', err);
    error.value = '加载数据失败，请稍后重试';
  } finally {
    // 所有API响应完毕，关闭页面加载状态
    pageLoading.value = false;
  }
});

// 根据分类ID获取图标
const getIconForCategory = (categoryId) => {
  const icons = {
    1: 'mdi:book-open-variant',
    2: 'mdi:clipboard-text-outline',
    3: 'mdi:certificate-outline',
    4: 'mdi:file-document-outline',
    5: 'mdi:notebook-outline'
  };
  
  return icons[categoryId] || 'mdi:folder-outline';
};

// 根据分类ID获取背景色
const getColorForCategory = (categoryId) => {
  const colors = {
    1: 'bg-indigo-900',
    2: 'bg-amber-800',
    3: 'bg-emerald-800',
    4: 'bg-blue-800',
    5: 'bg-purple-800'
  };
  
  return colors[categoryId] || 'bg-gray-800';
};

// 返回到公司分类列表页
const goBackToCategoryList = () => {
  router.push({
    name: 'KnowledgeBaseCategory',
    params: { companyId: companyId.value } 
  });
};
</script>

<style scoped>
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

:deep(.content img) {
  max-width: 100%;
  height: auto;
}

:deep(.content h2) {
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  color: #1f2937;
}

:deep(.content h3) {
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 1.25rem;
  margin-bottom: 0.75rem;
  color: #374151;
}

:deep(.content p) {
  margin-bottom: 1rem;
  line-height: 1.6;
}

:deep(.content ul), :deep(.content ol) {
  padding-left: 1.5rem;
  margin-bottom: 1rem;
}

:deep(.content li) {
  margin-bottom: 0.5rem;
}
</style> 