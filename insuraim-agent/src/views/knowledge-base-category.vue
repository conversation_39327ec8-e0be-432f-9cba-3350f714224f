<template>
    <div class="mx-auto p-8 bg-white rounded-xl min-h-screen">
        <!-- 加载中状态 - 整体页面加载 -->
        <div v-if="pageLoading" class="flex flex-col justify-center items-center py-32">
            <div class="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-blue-500 mb-4"></div>
            <p class="text-gray-600">加载中，请稍候...</p>
        </div>

        <!-- 页面内容 - 所有API响应完毕后显示 -->
        <div v-else>
            <!-- 页面标题 -->
            <div class="mb-6">
                <h1 class="text-2xl font-bold text-gray-800">知识库</h1>
                <p class="text-gray-600">保险业务知识学习和查询平台</p>
            </div>
            
            <!-- 错误状态 -->
            <div v-if="error" class="py-8 text-center">
                <Icon icon="mdi:alert-circle-outline" class="h-16 w-16 text-red-500 mx-auto mb-4" />
                <h3 class="text-xl font-semibold text-gray-700 mb-2">加载失败</h3>
                <p class="text-red-500 mb-4">{{ error }}</p>
                <button 
                    @click="reloadData" 
                    class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                >
                    重新加载
                </button>
            </div>
            
            <!-- 布局容器：左侧分类树 + 右侧内容 -->
            <div v-else class="flex flex-row gap-6">
                <!-- 左侧分类树导航 -->
                <div class="w-64 border border-gray-200 rounded-lg overflow-hidden bg-white shadow-sm">
                    <div class="p-3 bg-gray-50 border-b border-gray-200">
                        <h3 class="font-medium text-gray-700">分类目录</h3>
                    </div>
                    <CategoryTree 
                        :company-id="companyId" 
                        :active-id="activeCategory"
                        @select="handleCategorySelect"
                    />
                </div>
                
                <!-- 右侧内容区域 -->
                <div class="flex-1">
                    <!-- 全部专题 -->
                    <div>
                        <div class="mb-4">
                            <h2 class="text-xl font-semibold text-gray-800">全部专题 ({{ categories.length }})</h2>
                        </div>

                        <!-- 知识库模块 - 网格布局 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <!-- 知识库模块循环 -->
                            <div 
                                v-for="category in categories" 
                                :key="category.id"
                                class="bg-white rounded-lg overflow-hidden shadow hover:shadow-md transition-all cursor-pointer"
                                @click="navigateToDetail(category)"
                            >
                                <div :class="`p-6 ${category.bgColor || getRandomBgColor(category.id)} flex justify-center items-center`">
                                    <Icon :icon="category.icon || getDefaultIcon(category.id)" class="h-16 w-16 text-white" />
                                </div>
                                <div class="p-4 text-center">
                                    <h3 class="font-medium text-gray-900">{{ category.categoryName }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { Icon } from '@iconify/vue';
import { useRouter, useRoute } from 'vue-router';
import { knowledgeBaseAPI } from '@/api';
import CategoryTree from '@/components/CategoryTree.vue';

const router = useRouter();
const categories = ref([]);
const error = ref('');
const pageLoading = ref(true);

const route = useRoute();
const companyId = computed(() => route.params.companyId);
const companyName = ref('');
const activeCategory = ref(null); // 当前选中的分类ID

// 背景颜色列表
const bgColors = [
    'bg-indigo-900',
    'bg-amber-800',
    'bg-emerald-800',
    'bg-teal-800',
    'bg-gray-800',
    'bg-blue-800',
    'bg-orange-800',
    'bg-purple-800'
];

// 图标列表
const icons = [
    'mdi:book-open-variant',
    'mdi:clipboard-text-outline',
    'mdi:account-group',
    'mdi:umbrella',
    'mdi:file-document-outline',
    'mdi:handshake',
    'mdi:cash-multiple',
    'mdi:lightbulb'
];

// 获取随机背景颜色
const getRandomBgColor = (id) => {
    return bgColors[id % bgColors.length];
};

// 获取默认图标
const getDefaultIcon = (id) => {
    return icons[id % icons.length];
};

// 面包屑相关函数已移除

// 获取知识库分类数据
const fetchCategories = async () => {
    error.value = '';
    
    try {
        const response = await knowledgeBaseAPI.getCategoryListByCompanyId(companyId.value);
        categories.value = response.categoryList;
        
        // 如果API返回了公司名称，则更新本地变量
        if (response.companyName) {
            companyName.value = response.companyName;
        }
    } catch (err) {
        console.error('获取知识库分类失败:', err);
        error.value = '获取知识库分类信息失败，请稍后重试';
    } finally {
        pageLoading.value = false; // 所有API响应完毕，关闭页面加载状态
    }
};

// 处理分类树节点选择
const handleCategorySelect = (category) => {
    // 判断是否为叶子节点（没有子分类）
    const isLeaf = !category.children || category.children.length === 0;
    
    if (isLeaf) {
        // 导航到分类详情页查看文章列表
        console.log('导航到分类详情页:', category.id);
        router.push({
            name: 'KnowledgeBaseDetail',
            params: { id: category.id },
        });
    } else {
        console.log('非叶子节点，只展开不跳转');
        // 如果不是叶子节点，只展开不跳转
    }
};

// 重新加载数据
const reloadData = async () => {
    pageLoading.value = true;
    error.value = '';
    
    try {
        await fetchCategories();
    } catch (err) {
        console.error('重新加载数据失败:', err);
        error.value = '加载数据失败，请稍后重试';
    } finally {
        pageLoading.value = false;
    }
};

// 导航到知识库详情页
const navigateToDetail = (category) => {  
    router.push({
        name: 'KnowledgeBaseDetail',
        params: { id: category.id },
    });
};

// 组件挂载时获取数据
onMounted(() => {
    pageLoading.value = true;
    fetchCategories();
});
</script>

<style scoped>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style> 