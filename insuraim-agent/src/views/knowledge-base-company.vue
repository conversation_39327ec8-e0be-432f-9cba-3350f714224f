<template>
    <div class="container mx-auto py-6 px-4">
        <!-- 页面标题 -->
        <div
            class="title-section mb-6 p-6 rounded-lg shadow-md text-white bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700">
            <div class="flex items-center">
                <Icon icon="mdi:office-building" class="text-4xl mr-3" />
                <h1 class="text-2xl font-bold page-title text-white">知识库保险公司分类</h1>
            </div>
            <p class="mt-2 page-description">
                按保险公司查看保险业务知识，快速获取相关政策和产品资料
            </p>
        </div>

        <!-- 加载中状态 -->
        <div v-if="loading" class="flex justify-center items-center py-12">
            <a-spin size="large" />
            <span class="ml-3 text-gray-600">正在加载保险公司数据...</span>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error" class="py-8 text-center bg-white rounded-lg shadow-md p-6">
            <Icon icon="mdi:alert-circle-outline" class="text-5xl text-red-500 mb-3" />
            <p class="text-red-500 mb-4 text-lg">{{ error }}</p>
            <a-button type="primary" @click="fetchFirstLevelCategories" class="flex items-center mx-auto">
                <template #icon>
                    <Icon icon="mdi:refresh" />
                </template>
                重新加载
            </a-button>
        </div>

        <div v-else class="mb-6">
            <!-- 统计卡片 -->
            <a-card class="mb-6 rounded-lg" :bordered="false">
                <h1 class="text-2xl font-bold mb-4">统计信息</h1>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <!-- 公司总数卡片 -->
                    <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-blue-500">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-500 text-sm">公司总数</p>
                                <p class="text-2xl font-bold">{{ companies.length }}</p>
                            </div>
                            <div class="bg-blue-100 p-2 rounded-full">
                                <Icon icon="mdi:office-building" class="text-2xl text-blue-500" />
                            </div>
                        </div>
                        <div class="mt-2 text-xs text-gray-500">
                            <span class="flex items-center">
                                <Icon icon="mdi:information-outline" class="mr-1" />
                                系统内所有保险公司总数
                            </span>
                        </div>
                    </div>

                    <!-- 区域分布卡片 -->
                    <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-green-500">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-500 text-sm">区域分布</p>
                                <p class="text-2xl font-bold">{{ getRegionCount() }}</p>
                            </div>
                            <div class="bg-green-100 p-2 rounded-full">
                                <Icon icon="mdi:map-marker" class="text-2xl text-green-500" />
                            </div>
                        </div>
                        <div class="mt-2 text-xs text-gray-500">
                            <span class="flex items-center">
                                <Icon icon="mdi:information-outline" class="mr-1" />
                                保险公司覆盖的区域数量
                            </span>
                        </div>
                    </div>

                    <!-- 公司类型卡片 -->
                    <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-purple-500">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-500 text-sm">公司类型</p>
                                <p class="text-2xl font-bold">{{ getCategoryCount() }}</p>
                            </div>
                            <div class="bg-purple-100 p-2 rounded-full">
                                <Icon icon="mdi:book-multiple" class="text-2xl text-purple-500" />
                            </div>
                        </div>
                        <div class="mt-2 text-xs text-gray-500">
                            <span class="flex items-center">
                                <Icon icon="mdi:information-outline" class="mr-1" />
                                保险公司类型数量
                            </span>
                        </div>
                    </div>

                    <!-- 热门公司卡片 -->
                    <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-amber-500">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-500 text-sm">热门公司</p>
                                <p class="text-2xl font-bold">{{ getHotCompanyCount() }}</p>
                            </div>
                            <div class="bg-amber-100 p-2 rounded-full">
                                <Icon icon="mdi:fire" class="text-2xl text-amber-500" />
                            </div>
                        </div>
                        <div class="mt-2 text-xs text-gray-500">
                            <span class="flex items-center">
                                <Icon icon="mdi:information-outline" class="mr-1" />
                                访问量较高的热门公司
                            </span>
                        </div>
                    </div>
                </div>
            </a-card>

            <!-- 筛选区域 -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6 mt-6">
                <div class="flex flex-wrap items-center justify-between gap-4">
                    <h2 class="text-xl font-semibold text-gray-800">全部保险公司 ({{ companies.length }})</h2>

                    <div class="search-group flex items-center gap-3">
                        <a-input-search placeholder="搜索公司名称" style="width: 280px" class="mr-3" size="middle"
                            @search="searchCompany" />
                        <a-button type="primary" size="middle" @click="refreshCompanyData">
                            <template #icon>
                                <Icon icon="mdi:refresh" />
                            </template>
                            <span>重置</span>
                        </a-button>
                    </div>
                </div>
            </div>

            <!-- 保险公司列表 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <!-- 保险公司模块 - 网格布局 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <!-- 保险公司模块循环 -->
                    <div v-for="company in filteredCompanies" :key="company.id"
                        class="bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-lg transition-all border border-gray-200 cursor-pointer transform hover:-translate-y-1 duration-200"
                        @click="navigateToDetail(company)">
                        <div
                            :class="`p-6 ${company.bgColor || getRandomBgColor(company.id)} flex justify-center items-center`">
                            <img v-if="company.logoUrl" :src="company.logoUrl" class="h-16 w-16" alt="公司Logo" />
                            <Icon v-else :icon="company.icon || getDefaultIcon(company.id)"
                                class="h-16 w-16 text-white" />
                        </div>
                        <div class="p-4">
                            <h3 class="font-medium text-gray-900 text-center">{{ company.companyName }}（{{
                                company.companyCode
                            }}）</h3>
                            <div class="mt-3 flex justify-center gap-2">
                                <a-tag color="blue">{{ company.region || '全国' }}</a-tag>
                                <a-tag color="green">{{ company.type || '保险公司' }}</a-tag>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 空状态 -->
                <div v-if="filteredCompanies.length === 0" class="flex flex-col items-center justify-center py-20">
                    <Icon icon="mdi:office-building-outline" class="text-6xl text-gray-400 mb-4" />
                    <p class="text-gray-500">暂无符合条件的保险公司</p>
                </div>
            </div>
        </div>

        <!-- 资料云盘区域 -->
        <div class="mt-8 bg-white rounded-lg shadow-md p-6">
            <cloud-docs />
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { Icon } from '@iconify/vue';
import { useRouter } from 'vue-router';
import { knowledgeBaseAPI } from '@/api';
import CloudDocs from '@/views/cloud-docs.vue';
import { message } from 'ant-design-vue';

const router = useRouter();
const companies = ref([]);
const loading = ref(false);
const error = ref('');
const searchKeyword = ref('');

// 背景颜色列表
const bgColors = [
    'bg-indigo-900',
    'bg-amber-800',
    'bg-emerald-800',
    'bg-teal-800',
    'bg-gray-800',
    'bg-blue-800',
    'bg-orange-800',
    'bg-purple-800'
];

// 图标列表
const icons = [
    'mdi:office-building',
    'mdi:shield-home',
    'mdi:hospital-building',
    'mdi:bank',
    'mdi:umbrella',
    'mdi:handshake',
    'mdi:cash-multiple',
    'mdi:security'
];

// 筛选后的公司列表
const filteredCompanies = computed(() => {
    if (!searchKeyword.value) return companies.value;
    return companies.value.filter(company =>
        company.companyName.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
        company.companyCode.toLowerCase().includes(searchKeyword.value.toLowerCase())
    );
});

// 获取随机背景颜色
const getRandomBgColor = (id) => {
    return bgColors[id % bgColors.length];
};

// 获取默认图标
const getDefaultIcon = (id) => {
    return icons[id % icons.length];
};

// 获取区域统计数量（模拟数据）
const getRegionCount = () => {
    return 4;
};

// 获取公司类型统计数量（模拟数据）
const getCategoryCount = () => {
    return 5;
};

// 获取热门公司统计数量（模拟数据）
const getHotCompanyCount = () => {
    return 8;
};

// 搜索公司
const searchCompany = (value) => {
    searchKeyword.value = value;
};

// 重置搜索
const refreshCompanyData = () => {
    searchKeyword.value = '';
};

// 获取知识库一级分类数据
const fetchFirstLevelCategories = async () => {
    loading.value = true;
    error.value = '';

    try {
        const response = await knowledgeBaseAPI.getCompanyList();
        companies.value = response;
        message.success('数据加载成功');
    } catch (err) {
        console.error('获取知识库一级分类失败:', err);
        error.value = '获取知识库一级分类信息失败，请稍后重试';
        message.error('获取知识库一级分类信息失败');
    } finally {
        loading.value = false;
    }
};

// 导航到知识库专题列表页
const navigateToDetail = (company) => {
    router.push({
        name: 'KnowledgeBaseCategory',
        params: { companyId: company.id }
    });
};

// 组件挂载时获取数据
onMounted(() => {
    fetchFirstLevelCategories();
});
</script>

<style scoped>
/* 标题区域样式 */
.title-section {
    transition: all 0.3s ease;
}

.title-section:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.page-title {
    position: relative;
    display: inline-block;
    color: white;
}

.page-title::after {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 0;
    width: 40px;
    height: 3px;
    background-color: white;
    border-radius: 3px;
}

.page-description {
    max-width: 600px;
    opacity: 0.9;
}

/* 统计卡片样式 */
.stat-card {
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

/* 公司卡片样式 */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 修复按钮中图标和文字的对齐问题 */
:deep(.ant-btn) {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

:deep(.ant-btn .anticon),
:deep(.ant-btn .iconify) {
    display: inline-flex;
    align-self: center;
}
</style>