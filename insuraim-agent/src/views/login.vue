<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-100 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-4 bg-white p-10 rounded-xl shadow-lg">
      <div class="flex items-center justify-center space-x-4">
        <img class="h-14 w-auto" src="https://wp-cdn.yukuii.top/v2/Cfo6zjv.png" alt="BIS系统" />
        <span class="text-2xl font-bold">Insuraim</span>
      </div>
      <p class="text-center text-sm text-gray-600">
        欢迎使用Insuraim保险信息管理系统
      </p>

      <a-form :model="formState" class="mt-8 space-y-6" @keyup.enter="handleSubmit">
        <div class="rounded-md -space-y-px">
          <div class="mb-6">
            <a-form-item name="username">
              <a-input v-model:value="formState.username" size="large" placeholder="用户名或邮箱">
                <template #prefix>
                  <Icon icon="material-symbols:person" class="text-gray-400" />
                </template>
              </a-input>
            </a-form-item>
          </div>

          <div class="mb-6">
            <a-form-item name="password">
              <a-input-password v-model:value="formState.password" size="large" placeholder="密码">
                <template #prefix>
                  <Icon icon="material-symbols:lock" class="text-gray-400" />
                </template>
              </a-input-password>
            </a-form-item>
          </div>

          <div class="mb-6">
            <a-form-item name="orgCode">
              <a-input v-model:value="formState.orgCode" size="large" placeholder="组织代码">
                <template #prefix>
                  <Icon icon="material-symbols:domain" class="text-gray-400" />
                </template>
              </a-input>
            </a-form-item>
          </div>
        </div>

        <div class="flex items-center justify-between mb-2">
          <a-checkbox v-model:checked="formState.remember">记住我</a-checkbox>
          <a href="#" class="text-sm text-blue-500 hover:text-blue-700">
            忘记密码?
          </a>
        </div>

        <div>
          <a-button type="primary" size="large" block :loading="loading" @click="handleSubmit" class="h-12">
            登录
          </a-button>
        </div>
      </a-form>

      <div class="mt-4 text-center">
        <p class="text-sm text-gray-600">
          还没有账号？ <a href="/register" class="text-blue-500 hover:text-blue-700">注册新账号</a>
        </p>
      </div>

      <div class="mt-8 pt-6 border-t border-gray-200">
        <div class="text-center text-sm text-gray-600">
          Copyright © {{ new Date().getFullYear() }} Insuraim 保险信息系统
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { userAPI } from '@/api';
import CryptoJS from 'crypto-js';
import { useUserMinix } from '@/minix/UserMinix.vue';
import { getUserMenu } from '@/api/permission';
// 加密密钥，实际应用中应该使用更安全的方式存储
const ENCRYPTION_KEY = 'insuraim-secret-key-2025';
const router = useRouter();
const loading = ref(false);
const { setUserInfo, setToken } = useUserMinix();
// 表单数据
const formState = reactive({
  username: '',
  password: '',
  orgCode: '',
  remember: true,
});

// 页面加载时检查是否有记住的登录信息
onMounted(() => {

  const rememberedInfo = localStorage.getItem('remembered');
  if (rememberedInfo) {
    try {
      const remembered = JSON.parse(rememberedInfo);
      formState.username = remembered.username || '';
      formState.orgCode = remembered.orgCode || '';

      // 解密密码并填充
      if (remembered.password) {
        try {
          const decryptedBytes = CryptoJS.AES.decrypt(remembered.password, ENCRYPTION_KEY);
          const decryptedPassword = decryptedBytes.toString(CryptoJS.enc.Utf8);
          if (decryptedPassword) {
            formState.password = decryptedPassword;
          }
        } catch (decryptError) {
          console.error('密码解密失败:', decryptError);
        }
      }

      formState.remember = true;
    } catch (error) {
      console.error('解析记住的登录信息失败:', error);
      localStorage.removeItem('remembered');
    }
  }
});

// 登录处理
const handleSubmit = async () => {
  if (!formState.username || !formState.password) {
    return;
  }

  loading.value = true;

  try {
    // 判断用户名中是否有@
    let result;
    if (formState.username.includes('@')) {
      result = await userAPI.login({
        email: formState.username,
        password: formState.password,
        username: '',
        orgCode: formState.orgCode
      });
    } else {
      result = await userAPI.login({
        username: formState.username,
        password: formState.password,
        email: '',
        orgCode: formState.orgCode
      });
    }

    // 使用UserMinix中的方法存储用户信息和token
    setToken(result.token);
    setUserInfo(result.user);

    // 存储token到localStorage（可以保留，作为双重保障）
    localStorage.setItem('token', result.token);

    // 存储用户信息
    if (result.user) {
      // 如果选择了"记住我"，则存储用户名
      if (formState.remember) {
        const remembered = {
          username: formState.username,
          password: CryptoJS.AES.encrypt(formState.password, ENCRYPTION_KEY).toString(),
          orgCode: formState.orgCode
        }
        localStorage.setItem('remembered', JSON.stringify(remembered));
      } else {
        localStorage.removeItem('remembered');
      }

      // 提示登录成功
      message.success('登录成功');
      getUserMenu().then(res => {
        console.log(res);
      });
      // 跳转到首页
      router.push('/');
    }
  } catch (error) {
    console.log(error)
    message.error('登录失败: ' + (error.message || '未知错误'));

  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
:deep(.ant-input-affix-wrapper) {
  padding: 12px 16px;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-checkbox-wrapper) {
  font-size: 14px;
}

:deep(.ant-btn-primary) {
  background-color: #1890ff;
}

:deep(.ant-btn-primary:hover) {
  background-color: #40a9ff;
}
</style>