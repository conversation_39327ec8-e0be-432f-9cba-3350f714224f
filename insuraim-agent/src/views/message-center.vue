<template>
  <div class="container mx-auto py-6 px-4">
    <!-- 页面标题 -->
    <div
      class="title-section mb-6 p-6 rounded-lg shadow-md text-white bg-gradient-to-r from-indigo-500 via-indigo-600 to-indigo-700">
      <div class="flex items-center">
        <Icon icon="mdi:bell" class="text-4xl mr-3" />
        <h1 class="text-2xl font-bold page-title text-white">消息中心</h1>
      </div>
      <p class="mt-2 page-description">
        管理您的所有通知和消息，及时了解系统更新和计划书状态
      </p>
    </div>

    <!-- 统计卡片区域 -->
    <a-card class="mb-6 rounded-lg" :bordered="false">
      <h1 class="text-2xl font-bold mb-4">消息概览</h1>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- 消息总数卡片 -->
        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-indigo-500">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">消息总数</p>
              <p class="text-2xl font-bold">{{ messages.length }}</p>
            </div>
            <div class="bg-indigo-100 p-2 rounded-full">
              <Icon icon="mdi:message-text" class="text-2xl text-indigo-500" />
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-500">
            <span class="flex items-center">
              <Icon icon="mdi:information-outline" class="mr-1" />
              系统内所有消息总数
            </span>
          </div>
        </div>

        <!-- 未读消息卡片 -->
        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-red-500">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">未读消息</p>
              <p class="text-2xl font-bold">{{ tabs[0].unread }}</p>
            </div>
            <div class="bg-red-100 p-2 rounded-full">
              <Icon icon="mdi:bell-ring" class="text-2xl text-red-500" />
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-500">
            <span class="flex items-center">
              <Icon icon="mdi:information-outline" class="mr-1" />
              待处理的未读消息数量
            </span>
          </div>
        </div>

        <!-- 系统消息卡片 -->
        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-blue-500">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">系统消息</p>
              <p class="text-2xl font-bold">{{ getMessageCountByType('system') }}</p>
            </div>
            <div class="bg-blue-100 p-2 rounded-full">
              <Icon icon="mdi:cog" class="text-2xl text-blue-500" />
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-500">
            <span class="flex items-center">
              <Icon icon="mdi:information-outline" class="mr-1" />
              <span>未读: <span class="font-semibold text-blue-600">{{ tabs[1].unread }}</span></span>
            </span>
          </div>
        </div>

        <!-- 计划书消息卡片 -->
        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-green-500">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">计划书消息</p>
              <p class="text-2xl font-bold">{{ getMessageCountByType('proposal') }}</p>
            </div>
            <div class="bg-green-100 p-2 rounded-full">
              <Icon icon="mdi:file-document" class="text-2xl text-green-500" />
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-500">
            <span class="flex items-center">
              <Icon icon="mdi:information-outline" class="mr-1" />
              <span>未读: <span class="font-semibold text-green-600">{{ tabs[2].unread }}</span></span>
            </span>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 搜索和操作区域 -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6 mt-4">
      <div class="flex flex-wrap items-center justify-between gap-4">
        <!-- 消息分类标签页 -->
        <div class="tab-section">
          <nav class="flex space-x-6">
            <button v-for="tab in tabs" :key="tab.value" @click="switchTab(tab.value)"
              class="px-3 py-2 text-sm font-medium rounded-full transition-colors relative" :class="[
                currentTab === tab.value
                  ? 'bg-indigo-100 text-indigo-700'
                  : 'text-gray-700 hover:bg-gray-100'
              ]">
              {{ tab.label }}
              <span v-if="tab.unread"
                class="absolute -top-1 -right-1 px-2 py-0.5 text-xs bg-red-500 text-white rounded-full min-w-[20px] h-5 flex items-center justify-center">
                {{ tab.unread }}
              </span>
            </button>
          </nav>
        </div>

        <div class="flex items-center space-x-4">
          <div class="relative">
            <input type="text" v-model="searchQuery" @input="handleSearch" placeholder="搜索消息..."
              class="pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:border-indigo-500 w-full md:w-auto" />
            <Icon icon="mdi:magnify" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          </div>
          <a-button type="primary" @click="markAllAsRead" class="flex items-center">
            <template #icon>
              <Icon icon="mdi:check-all" />
            </template>
            <span>全部标记为已读</span>
          </a-button>
        </div>
      </div>
    </div>

    <!-- 消息列表和详情区域 -->
    <div class="grid grid-cols-12 gap-6">
      <!-- 消息列表 -->
      <div :class="[
        'bg-white rounded-lg shadow-md transition-all duration-300 ease-out',
        selectedMessage ? 'col-span-12 md:col-span-5' : 'col-span-12'
      ]">
        <List :dataSource="filteredMessages" :loading="loading" :pagination="false" class="message-list">
          <template #footer>
            <div class="text-center p-4">
              <a-spin v-if="loadingMore" />
              <a-button v-else-if="hasMore" @click="loadMore" type="primary" size="middle">加载更多</a-button>
              <div v-else-if="filteredMessages.length > 0" class="text-gray-500">
                没有更多消息了
              </div>
            </div>
          </template>

          <template #emptyText>
            <div class="flex flex-col items-center justify-center py-12 text-gray-500">
              <Icon icon="mdi:email-off" class="text-6xl text-gray-300 mb-4" />
              <p>暂无消息</p>
            </div>
          </template>

          <template #renderItem="{ item: message }">
            <List.Item @click="selectMessage(message)"
              class="hover:bg-gray-50 cursor-pointer transition-colors duration-200 px-4 py-4 border-b border-gray-100 last:border-none"
              :class="{ 'bg-indigo-50': selectedMessage?.id === message.id }">
              <div class="flex items-start w-full space-x-4">
                <div class="flex-shrink-0">
                  <div :class="[
                    'w-12 h-12 rounded-lg flex items-center justify-center',
                    message.read ? 'bg-gray-100' : message.type === 'system' ? 'bg-blue-100' : 'bg-green-100'
                  ]">
                    <Icon :icon="getMessageIcon(message.type)" class="w-6 h-6"
                      :class="message.read ? 'text-gray-500' : message.type === 'system' ? 'text-blue-500' : 'text-green-500'" />
                  </div>
                </div>
                <div class="flex-1 min-w-0">
                  <div class="flex justify-between">
                    <p class="text-sm font-medium text-gray-900 flex items-center"
                      :class="{ 'font-bold': !message.read }">
                      {{ message.title }}
                      <span v-if="!message.read"
                        class="inline-block w-2 h-2 rounded-full bg-red-500 ml-2 animate-pulse"></span>
                    </p>
                    <span class="text-xs text-gray-500">{{ formatTime(message.createdAt) }}</span>
                  </div>
                  <p class="mt-1 text-sm text-gray-500 line-clamp-2">
                    {{ message.content }}
                  </p>
                  <div class="mt-2 flex items-center space-x-2">
                    <span :class="[
                      'px-2 py-1 text-xs rounded-full',
                      getTagClass(message.type)
                    ]">
                      {{ getMessageType(message.type) }}
                    </span>
                  </div>
                </div>
              </div>
            </List.Item>
          </template>
        </List>
      </div>

      <!-- 消息详情侧边栏 -->
      <div v-if="selectedMessage" class="col-span-12 md:col-span-7 relative">
        <transition enter-active-class="transition-all duration-300 ease-out" enter-from-class="opacity-0 translate-x-4"
          enter-to-class="opacity-100 translate-x-0" leave-active-class="transition-all duration-200 ease-in"
          leave-from-class="opacity-100 translate-x-0" leave-to-class="opacity-0 translate-x-4" appear>
          <div class="bg-white rounded-lg shadow-md p-6 w-full">
            <div class="flex justify-between items-start mb-6">
              <h2 class="text-xl font-bold text-gray-900">
                {{ selectedMessage.title }}
              </h2>
              <button @click="selectedMessage = null" class="text-gray-400 hover:text-gray-600">
                <Icon icon="mdi:close" class="w-6 h-6" />
              </button>
            </div>
            <transition enter-active-class="transition-all duration-300 ease-out delay-150"
              enter-from-class="opacity-0 -translate-y-2" enter-to-class="opacity-100 translate-y-0" appear>
              <div class="space-y-4">
                <div class="flex items-center space-x-4 text-sm text-gray-500">
                  <div class="flex items-center">
                    <Icon icon="mdi:clock-outline" class="w-4 h-4 mr-1" />
                    <span>{{ formatTime(selectedMessage.createdAt) }}</span>
                  </div>
                  <span :class="[
                    'px-3 py-1 text-xs rounded-full',
                    getTagClass(selectedMessage.type)
                  ]">
                    {{ getMessageType(selectedMessage.type) }}
                  </span>
                  <span v-if="selectedMessage.read" class="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full">
                    已读
                  </span>
                  <span v-else class="px-2 py-1 text-xs bg-yellow-100 text-yellow-700 rounded-full">
                    未读
                  </span>
                </div>
                <div class="prose prose-sm max-w-none bg-gray-50 p-4 rounded-lg mt-4">
                  {{ selectedMessage.content }}
                </div>
                <div class="flex space-x-4 pt-4 border-t">
                  <a-button v-if="!selectedMessage.read" type="primary" @click="markAsRead(selectedMessage)">
                    <template #icon>
                      <Icon icon="mdi:check" />
                    </template>
                    标记为已读
                  </a-button>
                </div>
              </div>
            </transition>
          </div>
        </transition>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { Icon } from '@iconify/vue';
import MainLayout from '@/layouts/MainLayout.vue';
import { getAllMessages, readMessage, getAllUnreadMessages, getPagedMessages } from '@/api/message';
import { message as antMessage, List, Spin, Button } from 'ant-design-vue';

// 消息分类标签
const tabs = ref([
  { label: '全部消息', value: 'all', unread: 0 },
  { label: '系统消息', value: 'system', unread: 0 },
  { label: '计划书消息', value: 'proposal', unread: 0 },
]);

const currentTab = ref('all');
const searchQuery = ref('');
const selectedMessage = ref(null);
const messages = ref([]);
const loading = ref(false);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(5);
const hasMore = ref(true);
const loadingMore = ref(false);

// 格式化时间戳
const formatTime = (timestamp) => {
  if (!timestamp) return '';

  // 确保timestamp是数字
  const timeMs = typeof timestamp === 'string' ? parseInt(timestamp) : timestamp;

  const date = new Date(timeMs);
  const now = new Date();
  const diffMs = now - date;
  const diffMins = Math.floor(diffMs / (1000 * 60));

  // 显示相对时间
  if (diffMins < 1) return '刚刚';
  if (diffMins < 60) return `${diffMins}分钟前`;

  const diffHours = Math.floor(diffMins / 60);
  if (diffHours < 24) return `${diffHours}小时前`;

  // 今年内的日期显示月日时分
  if (date.getFullYear() === now.getFullYear()) {
    return `${date.getMonth() + 1}月${date.getDate()}日 ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
  }

  // 往年日期显示年月日
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

// 获取消息数据
const fetchMessages = async (isRefresh = false) => {
  if (isRefresh) {
    currentPage.value = 1;
    hasMore.value = true;
    messages.value = [];
    loading.value = true;
  } else {
    loadingMore.value = true;
  }

  if (!hasMore.value) return;

  try {
    const data = await getPagedMessages(currentPage.value, pageSize.value);
    if (data && data.list) {
      // 将新消息添加到列表中
      messages.value = [...messages.value, ...data.list.map(msg => ({
        id: msg.id,
        title: msg.title,
        content: msg.content,
        type: msg.messageType === 1 ? 'system' : 'proposal',
        read: msg.status === 1,
        createdAt: msg.createdAt,
        extraData: msg.extraData
      }))];

      // 更新分页状态
      hasMore.value = data.hasNext;
      currentPage.value++;

      // 更新未读消息数量
      updateUnreadCounts();
    }
  } catch (err) {
    console.error('获取消息失败:', err);
    antMessage.error('获取消息失败，请稍后重试');
  } finally {
    loading.value = false;
    loadingMore.value = false;
  }
};

// 加载更多消息
const loadMore = () => {
  if (!hasMore.value || loadingMore.value) return;
  fetchMessages();
};

// 更新未读消息数量
const updateUnreadCounts = () => {
  const systemUnread = messages.value.filter(m => m.type === 'system' && !m.read).length;
  const proposalUnread = messages.value.filter(m => m.type === 'proposal' && !m.read).length;
  const totalUnread = systemUnread + proposalUnread;

  tabs.value[0].unread = totalUnread;
  tabs.value[1].unread = systemUnread;
  tabs.value[2].unread = proposalUnread;
};

// 标签切换函数
const switchTab = (tabValue) => {
  currentTab.value = tabValue;
};

// 搜索处理函数
const handleSearch = () => {
  // 无需额外处理
};

// 根据分类和搜索过滤消息
const filteredMessages = computed(() => {
  return messages.value
    .filter(message => {
      if (currentTab.value === 'all') return true;
      return message.type === currentTab.value;
    })
    .filter(message => {
      if (!searchQuery.value) return true;
      return (
        message.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        message.content.toLowerCase().includes(searchQuery.value.toLowerCase())
      );
    });
});

// 获取特定类型的消息总数
const getMessageCountByType = (type) => {
  return messages.value.filter(message => message.type === type).length;
};

// 获取消息图标
const getMessageIcon = (type) => {
  const icons = {
    system: 'mdi:cog',
    proposal: 'mdi:file-document',
  };
  return icons[type] || 'mdi:message';
};

// 获取消息类型标签样式
const getTagClass = (type) => {
  const classes = {
    system: 'bg-blue-100 text-blue-800',
    proposal: 'bg-green-100 text-green-800',
  };
  return classes[type] || 'bg-gray-100 text-gray-800';
};

// 获取消息类型文本
const getMessageType = (type) => {
  const types = {
    system: '系统消息',
    proposal: '计划书消息',
  };
  return types[type] || '其他';
};

// 选择消息
const selectMessage = (message) => {
  selectedMessage.value = message;
  if (!message.read) {
    markAsRead(message);
  }
};

// 更新导航栏的消息未读数量
const updateNavbarMessageCount = async () => {
  try {
    // 获取所有未读消息
    const unreadMessages = await getAllUnreadMessages();
    // 如果在MainLayout组件的ref属性可访问，直接更新
    if (typeof window !== 'undefined') {
      // 使用事件通知其他组件更新消息数量
      const event = new CustomEvent('update-message-count', {
        detail: { count: unreadMessages.length }
      });
      window.dispatchEvent(event);
    }
  } catch (err) {
    console.error('更新导航栏消息数量失败:', err);
  }
};

// 标记消息为已读
const markAsRead = async (message) => {
  try {
    // 调用后端API标记为已读
    await readMessage(message.id);

    // 更新前端状态
    message.read = true;
    updateUnreadCounts();

    // 更新导航栏消息数量
    await updateNavbarMessageCount();
  } catch (err) {
    console.error('标记消息已读失败:', err);
    antMessage.error('标记消息已读失败');
  }
};

// 标记所有消息为已读
const markAllAsRead = async () => {
  try {
    // 获取所有未读消息ID
    const unreadIds = messages.value.filter(msg => !msg.read).map(msg => msg.id);

    // 如果没有未读消息，直接返回
    if (unreadIds.length === 0) {
      antMessage.info('暂无未读消息');
      return;
    }

    // 逐个标记为已读
    await Promise.all(unreadIds.map(id => readMessage(id)));

    // 更新前端状态
    messages.value.forEach(message => {
      message.read = true;
    });

    // 更新未读计数
    tabs.value.forEach(tab => {
      tab.unread = 0;
    });

    // 更新导航栏消息数量
    await updateNavbarMessageCount();

    antMessage.success('已将所有消息标记为已读');
  } catch (err) {
    console.error('标记所有消息已读失败:', err);
    antMessage.error('标记所有消息已读失败');
  }
};

// 在组件挂载时获取消息
onMounted(() => {
  fetchMessages(true);
});
</script>

<style scoped>
/* 标题区域样式 */
.title-section {
  transition: all 0.3s ease;
}

.title-section:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.page-title {
  position: relative;
  display: inline-block;
  color: white;
}

.page-title::after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: white;
  border-radius: 3px;
}

.page-description {
  max-width: 600px;
  opacity: 0.9;
}

/* 统计卡片样式 */
.stat-card {
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

/* 消息列表相关样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

:deep(.message-list .ant-list-items) {
  padding: 0;
}

:deep(.ant-list-item) {
  padding: 0;
  margin: 0;
}

/* 修复按钮中图标和文字的对齐问题 */
:deep(.ant-btn) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

:deep(.ant-btn .anticon),
:deep(.ant-btn .iconify) {
  display: inline-flex;
  align-self: center;
}
</style>