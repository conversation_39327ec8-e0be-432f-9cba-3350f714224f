<template>
    <div class="mx-auto px-8 py-8 space-y-8">
      <!-- 加载状态 -->
      <div v-if="loading" class="flex flex-col items-center justify-center py-20">
        <a-spin size="large" />
        <span class="mt-4 text-gray-600">加载试题中...</span>
      </div>
      
      <template v-else>
        <!-- 考试标题和信息区域 -->
        <div v-if="!examFinished" class="flex justify-between items-center">
          <div>
            <div class="flex items-center mb-2">
              <a-button @click="goBackToCategory" type="primary" class="flex items-center" style="margin-right: 1rem;">
                <template #icon><Icon icon="mdi:arrow-left" style="vertical-align: -2px;" /></template>
                返回类目选择
              </a-button>
              <h1 class="text-2xl font-bold text-gray-800 flex items-center my-0 leading-none">{{ examTitle }}</h1>
            </div>
            <p class="text-gray-600">总题目: {{ questions.length }} | 考试时长: {{ formatTime(examDuration) }}</p>
          </div>
          <div class="flex items-center space-x-4">
            <div class="text-lg font-semibold bg-red-50 px-4 py-2 rounded-lg text-red-600 icon-text-wrapper">
              <Icon icon="mdi:clock-outline" class="mr-2" />
              剩余时间: {{ formatTime(remainingTime) }}
            </div>
            <a-button type="primary" size="large" :disabled="!canSubmit" @click="submitExam">
              提交考卷
            </a-button>
          </div>
        </div>

        <!-- 考试进行中界面 -->
        <div v-if="!examFinished" class="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <!-- 左侧：题目内容区域 -->
          <div class="lg:col-span-3 bg-white rounded-lg p-6 shadow-sm">
            <div class="flex justify-between items-center mb-4">
              <h2 class="text-lg font-semibold">
                <span class="bg-blue-500 text-white px-2 py-1 rounded-full mr-2">{{ currentIndex + 1 }}</span>
                {{ currentQuestion.type === 'single' ? '单选题' : '多选题' }}
              </h2>
              <div class="flex space-x-2">
                <a-button 
                  :type="isMarked(currentIndex) ? 'primary' : 'default'" 
                  @click="toggleMark(currentIndex)"
                  class="flex items-center"
                >
                  <template #icon>
                    <Icon :icon="isMarked(currentIndex) ? 'mdi:star' : 'mdi:star-outline'" style="vertical-align: -2px;" />
                  </template>
                  {{ isMarked(currentIndex) ? '取消标记' : '标记题目' }}
                </a-button>
              </div>
            </div>

            <!-- 题目内容 -->
            <div class="mb-6">
              <p class="text-lg mb-4">{{ currentQuestion.question }}</p>
              
              <!-- 选项 - 单选题 -->
              <a-radio-group v-if="currentQuestion.type === 'single'" v-model:value="userAnswers[currentIndex]" class="space-y-4 block">
                <a-radio v-for="(option, idx) in currentQuestion.options" :key="idx" :value="idx" class="block py-3 text-base option-item">
                  {{ option }}
                </a-radio>
              </a-radio-group>
              
              <!-- 选项 - 多选题 -->
              <a-checkbox-group v-else-if="currentQuestion.type === 'multiple'" v-model:value="userAnswers[currentIndex]" class="space-y-4 block">
                <a-checkbox v-for="(option, idx) in currentQuestion.options" :key="idx" :value="idx" class="block py-3 text-base option-item">
                  {{ option }}
                </a-checkbox>
              </a-checkbox-group>
            </div>

            <!-- 导航按钮 -->
            <div class="flex justify-between">
              <a-button :disabled="currentIndex === 0" @click="prevQuestion" class="flex items-center">
                <template #icon><Icon icon="mdi:chevron-left" style="vertical-align: -2px;" /></template>
                上一题
              </a-button>
              <a-button :disabled="currentIndex === questions.length - 1" @click="nextQuestion" type="primary" class="flex items-center">
                下一题
                <template #icon><Icon icon="mdi:chevron-right" class="ml-1" style="vertical-align: -2px;" /></template>
              </a-button>
            </div>
          </div>

          <!-- 右侧：题目导航区域 -->
          <div class="bg-white rounded-lg p-6 shadow-sm">
            <h2 class="text-lg font-semibold mb-4">答题进度</h2>
            
            <!-- 进度统计 -->
            <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg mb-4">
              <div class="flex items-center">
                <div class="w-3 h-3 rounded-full bg-green-500 mr-2 indicator-circle"></div>
                <span class="text-sm">已答: {{ answeredCount }}</span>
              </div>
              <div class="flex items-center">
                <div class="w-3 h-3 rounded-full bg-yellow-500 mr-2 indicator-circle"></div>
                <span class="text-sm">标记: {{ markedQuestions.length }}</span>
              </div>
              <div class="flex items-center">
                <div class="w-3 h-3 rounded-full bg-gray-300 mr-2 indicator-circle"></div>
                <span class="text-sm">未答: {{ questions.length - answeredCount }}</span>
              </div>
            </div>
            
            <!-- 题目导航网格 -->
            <div class="grid grid-cols-5 gap-2">
              <div 
                v-for="(_, idx) in questions" 
                :key="idx" 
                @click="goToQuestion(idx)"
                class="h-10 w-10 flex items-center justify-center rounded-lg cursor-pointer border"
                :class="{
                  'border-blue-500 bg-blue-500 text-white': idx === currentIndex,
                  'bg-green-100 border-green-500': isAnswered(idx) && idx !== currentIndex,
                  'bg-gray-100 border-gray-300': !isAnswered(idx) && idx !== currentIndex,
                  'ring-2 ring-yellow-400': isMarked(idx)
                }"
              >
                {{ idx + 1 }}
              </div>
            </div>
            
            <!-- 图例说明 -->
            <div class="mt-4 text-xs text-gray-500">
              <div class="flex items-center mt-1">
                <div class="w-3 h-3 rounded-full bg-blue-500 mr-2 indicator-circle"></div>
                <span>当前题目</span>
              </div>
              <div class="flex items-center mt-1">
                <div class="w-3 h-3 rounded-full bg-green-100 border border-green-500 mr-2 indicator-circle"></div>
                <span>已答题目</span>
              </div>
              <div class="flex items-center mt-1">
                <div class="w-3 h-3 rounded-full bg-gray-100 border border-gray-300 mr-2 indicator-circle"></div>
                <span>未答题目</span>
              </div>
              <div class="flex items-center mt-1">
                <div class="w-3 h-3 rounded-full ring-2 ring-yellow-400 mr-2 indicator-circle"></div>
                <span>已标记题目</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 考试结果展示 -->
        <div v-if="examFinished" class="bg-white rounded-lg p-8 shadow-sm">
          <div class="text-center mb-8">
            <div class="inline-block p-4 rounded-full bg-blue-100 mb-4 flex items-center justify-center">
              <Icon icon="mdi:trophy" class="text-6xl text-blue-500" />
            </div>
            <h2 class="text-2xl font-bold">考试完成！</h2>
            <p class="text-gray-600 mt-2">您的得分: {{ examScore }} / {{ totalScore }}</p>
            <div class="mt-4 text-lg">
              <span class="inline-block px-4 py-2 rounded-lg" :class="passClass">
                {{ examScore >= passingScore ? '恭喜您通过考试！' : '很遗憾，未通过考试。' }}
              </span>
            </div>
          </div>
          
          <div class="grid grid-cols-2 gap-6 mb-8">
            <div class="p-4 bg-gray-50 rounded-lg">
              <h3 class="font-semibold mb-2 text-lg text-blue-500 flex items-center gap-2">
                <Icon icon="mdi:clipboard-text" class="text-xl" />
                考试详情
              </h3>
              <div class="space-y-2">
                <div class="flex justify-between">
                  <span>总题目:</span>
                  <span>{{ questions.length }}</span>
                </div>
                <div class="flex justify-between">
                  <span>答对题目:</span>
                  <span>{{ correctAnswers }}</span>
                </div>
                <div class="flex justify-between">
                  <span>答错题目:</span>
                  <span>{{ questions.length - correctAnswers }}</span>
                </div>
                <div class="flex justify-between">
                  <span>正确率:</span>
                  <span>{{ ((correctAnswers / questions.length) * 100).toFixed(1) }}%</span>
                </div>
              </div>
            </div>
            
            <div class="p-4 bg-gray-50 rounded-lg">
              <h3 class="font-semibold mb-2 text-lg text-blue-500 flex items-center gap-2">
                <Icon icon="mdi:chart-box" class="text-xl" />
                分类统计
              </h3>
              <div class="space-y-2">
                <div class="flex justify-between">
                  <span>单选题:</span>
                  <span>{{ typeStats.single.correct }} / {{ typeStats.single.total }}</span>
                </div>
                <div class="flex justify-between">
                  <span>多选题:</span>
                  <span>{{ typeStats.multiple.correct }} / {{ typeStats.multiple.total }}</span>
                </div>
                <div class="flex justify-between">
                  <span>用时:</span>
                  <span>{{ formatTime(examDuration - remainingTime) }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="flex justify-center gap-4">
            <a-button type="primary" @click="viewAnswers" class="flex items-center">
              查看解析
            </a-button>
            <a-button @click="restartExam" class="flex items-center">
              重新开始
            </a-button>
            <a-button @click="goBackToCategory" class="flex items-center">
              返回类目选择
            </a-button>
            <a-button @click="goToHome" class="flex items-center">
              返回首页
            </a-button>
          </div>
        </div>
      </template>
    </div>

    <!-- 答案解析弹窗 -->
    <a-modal
      v-model:visible="showAnswerAnalysis"
      title="考试答案解析"
      width="800px"
      @cancel="closeAnswerAnalysis"
      :footer="null"
    >
      <div class="space-y-8 max-h-96 overflow-y-auto p-2">
        <div v-for="(result, index) in answerAnalysisResults" :key="index" class="border-b pb-6 mb-6 last:border-0">
          <div class="flex items-start gap-3">
            <div class="bg-gray-100 px-3 py-1 rounded-full text-gray-700 font-medium">
              第 {{ index + 1 }} 题
            </div>
            <div :class="result.isCorrect ? 'text-green-500' : 'text-red-500'" class="font-medium">
              {{ result.isCorrect ? '答对' : '答错' }}
            </div>
          </div>
          
          <div class="mt-3 font-medium">{{ result.questionText }}</div>
          
          <div class="mt-4 grid grid-cols-1 gap-y-4">
            <div>
              <div class="text-sm text-gray-500 mb-1">您的答案:</div>
              <div class="pl-4">
                <div v-if="result.userAnswer.length === 0" class="text-gray-400 italic">未作答</div>
                <div v-for="(option, idx) in result.userAnswer" :key="idx">{{ option }}</div>
              </div>
            </div>
            
            <div>
              <div class="text-sm text-gray-500 mb-1">正确答案:</div>
              <div class="pl-4 text-green-600">
                <div v-for="(option, idx) in result.correctAnswer" :key="idx">{{ option }}</div>
              </div>
            </div>
            
            <div v-if="result.feedback" class="mt-2">
              <div class="text-sm text-gray-500 mb-1">解析:</div>
              <div class="pl-4 text-gray-700 bg-gray-50 p-2 rounded">{{ result.feedback }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="flex justify-end mt-4">
        <a-button type="primary" @click="closeAnswerAnalysis">关闭</a-button>
      </div>
    </a-modal>
</template>

<script setup>
import { Icon } from '@iconify/vue';
import { ref, computed, onMounted, watch, onBeforeUnmount, reactive } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import MainLayout from '@/layouts/MainLayout.vue';
import { examAPI } from '@/api/index';

// 路由实例
const router = useRouter();
const route = useRoute();

// 考试数据
const examTitle = ref('');
const examDuration = ref(60 * 60); // 60分钟，以秒为单位
const remainingTime = ref(examDuration.value);
const examFinished = ref(false);
const passingScore = ref(60); // 及格分数
const markedQuestions = ref([]);
const currentIndex = ref(0);
const categoryId = ref(null);
const questions = ref([]);
const loading = ref(false);
const showAnswerAnalysis = ref(false);
const answerAnalysisResults = ref([]);

// 计时器
let timer = null;

// 加载题目数据
const loadQuestions = async () => {
  if (!categoryId.value) {
    message.error('未选择考试类目');
    router.push('/exam-category');
    return;
  }
  
  loading.value = true;
  
  try {
    const data = await examAPI.getQuestionList(categoryId.value);
    
    if (data && Array.isArray(data) && data.length > 0) {
      // 处理题目数据，添加前端所需属性
      questions.value = data.map(question => {
        // 根据isCorrect字段确定正确答案
        const correctAnswerIndices = [];
        question.answer.forEach((option, index) => {
          if (option.isCorrect === 1) {
            correctAnswerIndices.push(index);
          }
        });
        
        // 确定题目类型：单选或多选
        const questionType = correctAnswerIndices.length > 1 ? 'multiple' : 'single';
        
        // 为题目添加前端所需属性
        return {
          ...question,
          type: questionType,
          // 对于单选题，保存正确选项的索引；对于多选题，保存正确选项索引数组
          correctAnswer: questionType === 'single' ? correctAnswerIndices[0] : correctAnswerIndices,
          // 添加选项文本数组，方便前端遍历渲染
          options: question.answer.map(ans => ans.optionText)
        };
      });
      
      // 初始化用户答案数组
      userAnswers.value = Array(questions.value.length).fill(null);
      
      // 启动计时器
      startTimer();
    } else {
      message.warning('该类目下暂无题目');
      router.push('/exam-category');
    }
  } catch (err) {
    console.error('获取考试题目失败', err);
    message.error('获取考试题目失败，请重试');
    router.push('/exam-category');
  } finally {
    loading.value = false;
  }
};

// 初始化用户答案
const userAnswers = ref([]);

// 计算属性
const currentQuestion = computed(() => questions.value[currentIndex.value] || {});

// 已答题目数量
const answeredCount = computed(() => {
  return userAnswers.value.filter(answer => 
    answer !== null && 
    (Array.isArray(answer) ? answer.length > 0 : true)
  ).length;
});

// 检查是否已回答
const isAnswered = (index) => {
  const answer = userAnswers.value[index];
  return answer !== null && (Array.isArray(answer) ? answer.length > 0 : true);
};

// 检查题目是否被标记
const isMarked = (index) => {
  return markedQuestions.value.includes(index);
};

// 标记/取消标记题目
const toggleMark = (index) => {
  if (isMarked(index)) {
    markedQuestions.value = markedQuestions.value.filter(i => i !== index);
  } else {
    markedQuestions.value.push(index);
  }
};

// 导航到指定题目
const goToQuestion = (index) => {
  currentIndex.value = index;
};

// 上一题
const prevQuestion = () => {
  if (currentIndex.value > 0) {
    currentIndex.value--;
  }
};

// 下一题
const nextQuestion = () => {
  if (currentIndex.value < questions.value.length - 1) {
    currentIndex.value++;
  }
};

// 判断是否可以提交
const canSubmit = computed(() => answeredCount.value > 0);

// 计算考试分数
const examScore = ref(0);
const totalScore = ref(100);
const correctAnswers = ref(0);
const typeStats = reactive({
  single: { total: 0, correct: 0 },
  multiple: { total: 0, correct: 0 }
});

// 评分函数
const calculateScore = () => {
  let correct = 0;
  let total = questions.value.length;
  
  // 重置统计数据
  typeStats.single.total = typeStats.single.correct = 0;
  typeStats.multiple.total = typeStats.multiple.correct = 0;
  
  // 统计各类题目的数量
  questions.value.forEach(q => {
    if (q.type === 'single') typeStats.single.total++;
    else if (q.type === 'multiple') typeStats.multiple.total++;
  });
  
  // 检查答案并计算得分
  questions.value.forEach((question, idx) => {
    const userAnswer = userAnswers.value[idx];
    const correctAnswer = question.correctAnswer;
    
    let isCorrect = false;
    
    if (question.type === 'multiple') {
      // 多选题：数组长度相同且内容完全一致
      isCorrect = userAnswer && 
                 userAnswer.length === correctAnswer.length && 
                 userAnswer.every(a => correctAnswer.includes(a));
    } else {
      // 单选题：直接比较值
      isCorrect = userAnswer === correctAnswer;
    }
    
    if (isCorrect) {
      correct++;
      
      // 统计各类题目的正确数
      if (question.type === 'single') typeStats.single.correct++;
      else if (question.type === 'multiple') typeStats.multiple.correct++;
    }
  });
  
  correctAnswers.value = correct;
  examScore.value = Math.round((correct / total) * 100);
};

// 格式化时间
const formatTime = (seconds) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

// 结果颜色
const passClass = computed(() => {
  return examScore.value >= passingScore.value ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700';
});

// 提交考卷
const submitExam = () => {
  if (answeredCount.value === 0) {
    message.warning('请至少回答一道题目');
    return;
  }
  
  // 二次确认
  if (answeredCount.value < questions.value.length) {
    if (!confirm(`您还有 ${questions.value.length - answeredCount.value} 题未作答，确定要提交吗？`)) {
      return;
    }
  }
  
  // 停止计时
  clearInterval(timer);
  
  // 计算得分
  calculateScore();
  
  // 标记考试完成
  examFinished.value = true;
};

// 查看解析
const viewAnswers = () => {
  // 生成答案分析
  answerAnalysisResults.value = questions.value.map((question, idx) => {
    const userAnswer = userAnswers.value[idx];
    const correctAnswer = question.correctAnswer;
    
    let isCorrect = false;
    
    if (question.type === 'multiple') {
      // 多选题：数组长度相同且内容完全一致
      isCorrect = userAnswer && 
                 userAnswer.length === correctAnswer.length && 
                 userAnswer.every(a => correctAnswer.includes(a));
    } else {
      // 单选题：直接比较值
      isCorrect = userAnswer === correctAnswer;
    }
    
    // 用户选择的选项文本
    let userSelectedOptions = [];
    if (userAnswer !== null) {
      if (question.type === 'multiple' && Array.isArray(userAnswer)) {
        userSelectedOptions = userAnswer.map(idx => question.options[idx]);
      } else if (question.type === 'single' && typeof userAnswer === 'number') {
        userSelectedOptions = [question.options[userAnswer]];
      }
    }
    
    // 正确答案的选项文本
    let correctOptions = [];
    if (question.type === 'multiple' && Array.isArray(correctAnswer)) {
      correctOptions = correctAnswer.map(idx => question.options[idx]);
    } else if (question.type === 'single' && typeof correctAnswer === 'number') {
      correctOptions = [question.options[correctAnswer]];
    }
    
    return {
      questionText: question.question,
      questionType: question.type,
      userAnswer: userSelectedOptions,
      correctAnswer: correctOptions,
      isCorrect: isCorrect,
      feedback: isCorrect ? question.feedbackCorrect : question.feedbackIncorrect
    };
  });
  
  // 显示解析面板
  showAnswerAnalysis.value = true;
};

// 关闭解析
const closeAnswerAnalysis = () => {
  showAnswerAnalysis.value = false;
};

// 重新开始考试
const restartExam = () => {
  // 重置考试数据
  userAnswers.value = Array(questions.value.length).fill(null);
  markedQuestions.value = [];
  currentIndex.value = 0;
  remainingTime.value = examDuration.value;
  examFinished.value = false;
  
  // 重新开始计时
  startTimer();
  
  message.success('已重新开始考试');
};

// 返回首页
const goToHome = () => {
  router.push('/');
};

// 返回类目选择
const goBackToCategory = () => {
  router.push('/exam-category');
};

// 开始计时器
const startTimer = () => {
  if (timer) clearInterval(timer);
  
  timer = setInterval(() => {
    if (remainingTime.value > 0) {
      remainingTime.value--;
    } else {
      // 时间到，自动提交
      clearInterval(timer);
      message.warning('考试时间已结束');
      submitExam();
    }
  }, 1000);
};

// 组件挂载时初始化
onMounted(() => {
  // 从路由参数获取类目ID
  categoryId.value = route.query.categoryId ? Number(route.query.categoryId) : null;
  
  // 从路由参数获取考试名称
  examTitle.value = route.query.examName || '保险基础知识模拟考试';
  
  // 加载题目
  loadQuestions();
  
  // 尝试从本地存储恢复答案（如果有）
  const savedAnswers = localStorage.getItem(`mock_exam_answers_${categoryId.value}`);
  if (savedAnswers && questions.value.length > 0) {
    try {
      const parsed = JSON.parse(savedAnswers);
      // 确保解析后的数据是数组且长度匹配
      if (Array.isArray(parsed) && parsed.length === questions.value.length) {
        // 检查本地存储的答案格式是否与当前题目格式兼容
        const isCompatible = parsed.every((answer, idx) => {
          // 如果答案为null，兼容
          if (answer === null) return true;
          
          const questionType = questions.value[idx].type;
          
          // 多选题答案应该是数组
          if (questionType === 'multiple' && !Array.isArray(answer)) {
            return false;
          }
          
          // 单选题答案应该是数字
          if (questionType === 'single' && (typeof answer !== 'number' && answer !== null)) {
            return false;
          }
          
          return true;
        });
        
        // 如果格式兼容，则恢复答案
        if (isCompatible) {
          userAnswers.value = parsed;
        } else {
          // 格式不兼容，不恢复旧答案
          console.warn('本地存储的答案格式已变更，无法恢复');
          localStorage.removeItem(`mock_exam_answers_${categoryId.value}`);
        }
      }
    } catch (e) {
      console.error('恢复答案失败', e);
      localStorage.removeItem(`mock_exam_answers_${categoryId.value}`);
    }
  }
});

// 自动保存答案到本地存储
watch(userAnswers, (newAnswers) => {
  if (categoryId.value) {
    localStorage.setItem(`mock_exam_answers_${categoryId.value}`, JSON.stringify(newAnswers));
  }
}, { deep: true });

// 组件卸载前清除计时器
onBeforeUnmount(() => {
  if (timer) clearInterval(timer);
});
</script>

<style scoped>
/* 渐入渐出动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 卡片悬停效果 */
.card-hover {
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border-color: #e5e7eb;
}

/* 修复Ant Design组件中图标和文字的对齐问题 */
:deep(.ant-btn) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

:deep(.ant-btn .anticon),
:deep(.ant-btn .iconify) {
  display: inline-flex;
  align-self: center;
  line-height: 0;
}

:deep(.ant-radio-wrapper),
:deep(.ant-checkbox-wrapper) {
  display: flex;
  align-items: flex-start;
}

:deep(.ant-radio),
:deep(.ant-checkbox) {
  top: 0.2em;
}

/* 针对圆点指示器的对齐 */
.indicator-circle {
  display: inline-block;
  vertical-align: middle;
  margin-top: -2px;
  margin-right: 8px;
}

/* 确保所有图标文字组合都对齐 */
.icon-text-wrapper {
  display: inline-flex;
  align-items: center;
}

.icon-text-wrapper .iconify {
  margin-right: 4px;
}

/* 确保标题垂直居中 */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  line-height: 1.2;
}

/* 选项样式美化 */
.option-item {
  font-size: 1.05rem;
  line-height: 1.5;
}

:deep(.ant-radio-wrapper),
:deep(.ant-checkbox-wrapper) {
  font-size: 1.05rem;
  padding: 0.25rem 0;
}

:deep(.ant-radio-wrapper .ant-radio),
:deep(.ant-checkbox-wrapper .ant-checkbox) {
  margin-top: 0.25rem;
}
</style> 