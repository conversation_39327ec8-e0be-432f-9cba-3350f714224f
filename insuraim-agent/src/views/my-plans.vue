<template>
  <div class="mx-auto space-y-8">
    <!-- 页面标题 -->
    <div
      class="title-section mb-6 p-6 rounded-lg shadow-md text-white bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700">
      <div class="flex items-center">
        <Icon icon="mdi:file-document-multiple" class="text-4xl mr-3" />
        <h1 class="text-2xl font-bold page-title">{{ $t('plans.plansList') }}</h1>
      </div>
      <p class="mt-2 page-description">
        {{ $t('plans.browseAndFind') }}
      </p>
    </div>

    <!-- 统计卡片 -->
    <a-card class="mb-6 rounded-lg" :bordered="false">
      <h1 class="text-2xl font-bold mb-4">{{ $t('plans.statistics') }}</h1>
      <!-- 计划书统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- 计划书总数卡片 -->
        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-blue-500">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">{{ $t('plans.totalPlans') }}</p>
              <p class="text-2xl font-bold">{{ pagination.total || 0 }}</p>
            </div>
            <div class="bg-blue-100 p-2 rounded-full">
              <Icon icon="mdi:file-document-multiple" class="text-2xl text-blue-500" />
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-500">
            <span class="flex items-center">
              <Icon icon="mdi:information-outline" class="mr-1" />
              {{ $t('plans.totalPlansDesc') }}
            </span>
          </div>
        </div>

        <!-- 已完成计划书卡片 -->
        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-green-500">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <p class="text-gray-500 text-sm">{{ $t('plans.completedPlans') }}</p>
              <p class="text-2xl font-bold">{{ completedCount || 0 }}</p>
            </div>
            <div class="bg-green-100 p-2 rounded-full">
              <Icon icon="mdi:check-circle" class="text-2xl text-green-500" />
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-500">
            <span class="flex items-center">
              <Icon icon="mdi:information-outline" class="mr-1" />
              {{ $t('plans.completedPlansDesc') }}
            </span>
          </div>
        </div>

        <!-- 处理中计划书卡片 -->
        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-orange-500">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">{{ $t('plans.processingPlans') }}</p>
              <p class="text-2xl font-bold">{{ processingCount || 0 }}</p>
            </div>
            <div class="bg-orange-100 p-2 rounded-full">
              <Icon icon="mdi:clock-outline" class="text-2xl text-orange-500" />
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-500">
            <span class="flex items-center">
              <Icon icon="mdi:information-outline" class="mr-1" />
              {{ $t('plans.processingPlansDesc') }}
            </span>
          </div>
        </div>

        <!-- 失败计划书卡片 -->
        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-red-500">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">{{ $t('plans.failedPlans') }}</p>
              <p class="text-2xl font-bold">{{ failedCount || 0 }}</p>
            </div>
            <div class="bg-red-100 p-2 rounded-full">
              <Icon icon="mdi:alert-circle" class="text-2xl text-red-500" />
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-500">
            <span class="flex items-center">
              <Icon icon="mdi:information-outline" class="mr-1" />
              {{ $t('plans.failedPlansDesc') }}
            </span>
          </div>
        </div>
      </div>
    </a-card>

    <div class="bg-white rounded-lg shadow-md p-8">
      <!-- 搜索和筛选区域 -->
      <div class="filter-section mb-6 p-6 rounded-lg bg-gradient-to-r from-gray-50 to-gray-100 border border-gray-200">
        <div class="flex flex-wrap items-center gap-4">
          <div class="filter-group flex flex-wrap items-center gap-3">
            <a-input-search v-model:value="searchKeyword" :placeholder="$t('plans.searchPlaceholder')"
              style="width: 300px" @search="handleSearch" />

            <span class="text-gray-600 text-sm">{{ $t('plans.status') }}:</span>
            <a-select v-model:value="filterStatus" :placeholder="$t('plans.status')" style="width: 120px"
              @change="handleSearch">
              <a-select-option value="">{{ $t('plans.allStatus') }}</a-select-option>
              <a-select-option value="completed">{{ $t('plans.completed') }}</a-select-option>
              <a-select-option value="processing">{{ $t('plans.processing') }}</a-select-option>
              <a-select-option value="failed">{{ $t('plans.failed') }}</a-select-option>
            </a-select>

            <span class="text-gray-600 text-sm">{{ $t('plans.sortOrder') }}:</span>
            <a-select v-model:value="sortOrder" :placeholder="$t('plans.sortOrder')" style="width: 150px"
              @change="handleSearch">
              <a-select-option value="createdTime_desc">{{ $t('plans.createdTimeDesc') }}</a-select-option>
              <a-select-option value="createdTime_asc">{{ $t('plans.createdTimeAsc') }}</a-select-option>
              <a-select-option value="updateTime_desc">{{ $t('plans.updateTimeDesc') }}</a-select-option>
              <a-select-option value="updateTime_asc">{{ $t('plans.updateTimeAsc') }}</a-select-option>
            </a-select>
          </div>

          <div class="search-group flex items-center gap-3 ml-auto">
            <a-range-picker v-model:value="dateRange" format="YYYY-MM-DD" @change="handleSearch" style="width: 240px" />

            <a-button type="primary" href="/plan-creator" class="create-plan-btn">
              <template #icon>
                <Icon icon="mdi:plus" class="create-plan-icon" />
              </template>
              <span>{{ $t('plans.createPlan') }}</span>
            </a-button>
          </div>
        </div>
      </div>

      <!-- 计划书列表 -->
      <a-table :dataSource="planList" :columns="localizedColumns" :loading="loading" :pagination="pagination"
        @change="handleTableChange" rowKey="id"
        :rowClassName="(record, index) => (index % 2 === 1 ? 'table-striped' : '')" bordered>
        <!-- 产品名称列 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'productName'">
            <div class="flex items-center">
              <img v-if="record.productLogo" :src="record.productLogo" class="w-8 h-8 mr-2 object-contain rounded-md"
                :alt="record.productName" onerror="this.style.display='none'" />
              <div>
                <p class="font-medium">{{ record.productName }}</p>
                <p class="text-xs text-gray-500">{{ record.companyName }}</p>
              </div>
            </div>
          </template>

          <!-- 创建时间列 -->
          <template v-else-if="column.dataIndex === 'createAt'">
            <div class="flex items-center">
              <div>
                <div class="text-sm">{{ formatDate(record.createAt) }} <span class="text-xs text-gray-500">{{
                  formatTime(record.createAt) }}</span></div>
              </div>
            </div>
          </template>

          <!-- 状态列 -->
          <template v-else-if="column.dataIndex === 'status'">
            <div class="flex items-center">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
              <a-tooltip v-if="record.status === 'failed' && record.statusNote" placement="top">
                <template #title>{{ record.statusNote }}</template>
                <Icon icon="mdi:information-outline" class="text-red-500 ml-1 cursor-help" />
              </a-tooltip>
            </div>
          </template>

          <!-- 操作列 -->
          <template v-else-if="column.dataIndex === 'action'">
            <div class="flex items-center space-x-1">
              <a-button type="text" size="small" @click="viewPlanDetails(record)" class="action-btn">
                <template #icon>
                  <Icon icon="mdi:eye-outline" class="action-icon" />
                </template>
                {{ $t('plans.details') }}
              </a-button>

              <template v-if="record.status === 'completed'">
                <a-button type="text" size="small" @click="previewPdf(record)" class="action-btn">
                  <template #icon>
                    <Icon icon="mdi:file-pdf-box" class="action-icon" />
                  </template>
                  {{ $t('plans.preview') }}
                </a-button>

                <a-button type="text" size="small" @click="analyzeWithAI(record)" class="action-btn">
                  <template #icon>
                    <Icon icon="mdi:robot" class="action-icon" />
                  </template>
                  {{ $t('plans.analyze') }}
                </a-button>

                <a-button type="text" size="small" @click="downloadPdf(record)" class="action-btn">
                  <template #icon>
                    <Icon icon="mdi:download" class="action-icon" />
                  </template>
                  {{ $t('plans.download') }}
                </a-button>
              </template>
            </div>
          </template>
        </template>
      </a-table>

      <!-- 计划书详情抽屉 -->
      <a-drawer :visible="drawerVisible" :title="selectedPlan?.productName || $t('plans.proposalDetails')"
        placement="right" width="500" @close="closeDrawer" class="proposal-drawer">
        <template v-if="selectedPlan">
          <div class="mb-6 border-b pb-4">
            <div class="flex items-center mb-4">
              <div>
                <h3 class="text-lg font-medium">{{ selectedPlan.productName }}</h3>
                <p class="text-sm text-gray-500">{{ $t('plans.proposalNo') }}: {{ selectedPlan.proposalNo }}</p>
              </div>
            </div>

            <a-descriptions :column="1" bordered size="small">
              <a-descriptions-item :label="$t('plans.status')">
                <div class="flex items-center">
                  <a-tag :color="getStatusColor(selectedPlan.status)">
                    {{ getStatusText(selectedPlan.status) }}
                  </a-tag>
                  <span v-if="selectedPlan.status === 'failed' && selectedPlan.statusNote" class="ml-2 text-red-500">
                    {{ selectedPlan.statusNote }}
                  </span>
                </div>
              </a-descriptions-item>
              <a-descriptions-item :label="$t('plans.createAt')">
                {{ selectedPlan.createAt }}
              </a-descriptions-item>
              <a-descriptions-item :label="$t('plans.updateAt')">
                {{ selectedPlan.updateAt }}
              </a-descriptions-item>
            </a-descriptions>
          </div>

          <div class="flex justify-center space-x-4 mt-6">
            <template v-if="selectedPlan?.status === 'completed'">
              <a-button type="primary" @click="previewPdf(selectedPlan)" class="action-btn">
                <template #icon>
                  <Icon icon="mdi:file-pdf-box" class="action-icon" />
                </template>
                {{ $t('plans.previewProposal') }}
              </a-button>

              <a-button type="primary" @click="downloadPdf(selectedPlan)" class="action-btn">
                <template #icon>
                  <Icon icon="mdi:download" class="action-icon" />
                </template>
                {{ $t('plans.downloadProposal') }}
              </a-button>
            </template>
          </div>
        </template>
      </a-drawer>
    </div>

    <!-- AI聊天模态框 -->
    <ProposalChat v-model:visible="chatModalVisible" :proposal-id="chatProposalId" :proposal-name="chatProposalName"
      @close="chatModalVisible = false" />
  </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';
import { ref, reactive, onMounted, computed } from 'vue';
import { message } from 'ant-design-vue';
import { planAPI } from '@/api';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import ProposalChat from '@/components/ProposalChat.vue';
import dayjs from 'dayjs';

// 路由实例
const router = useRouter();
// 国际化实例
const { t } = useI18n();

// 数据定义
const loading = ref(false);
const planList = ref([]);
const searchKeyword = ref('');
const filterStatus = ref('');
const sortOrder = ref('createdTime_desc');
const dateRange = ref(null);
const selectedPlan = ref(null);
const drawerVisible = ref(false);

// AI聊天模态框相关状态
const chatModalVisible = ref(false);
const chatProposalId = ref(null);
const chatProposalName = ref('');

// 统计数据计算属性
const completedCount = computed(() => {
  return planList.value.filter(plan => plan.status === 'completed').length;
});

const processingCount = computed(() => {
  return planList.value.filter(plan => plan.status === 'processing').length;
});

const failedCount = computed(() => {
  return planList.value.filter(plan => plan.status === 'failed').length;
});

// 表格列定义
const columns = [
  {
    title: 'plans.productName',
    dataIndex: 'productName',
    key: 'productName',
    width: '20%',
    ellipsis: true,
  },
  {
    title: 'plans.customerName',
    dataIndex: 'customerName',
    key: 'customerName',
    width: '12%',
    ellipsis: true,
  },
  {
    title: 'plans.proposalNo',
    dataIndex: 'proposalNo',
    key: 'proposalNo',
    width: '15%',
    ellipsis: true,
  },
  {
    title: 'plans.createAt',
    dataIndex: 'createAt',
    key: 'createAt',
    width: '18%',
    sorter: true,
  },
  {
    title: 'plans.status',
    dataIndex: 'status',
    key: 'status',
    width: '12%',
    filters: [
      { text: t('plans.completed'), value: 'completed' },
      { text: t('plans.processing'), value: 'processing' },
      { text: t('plans.failed'), value: 'failed' },
    ],
  },
  {
    title: 'plans.action',
    dataIndex: 'action',
    key: 'action',
    width: '23%',
    fixed: 'right',
  },
];

// 国际化处理表格列
const localizedColumns = computed(() => {
  return columns.map(column => ({
    ...column,
    title: column.title ? t(column.title) : column.title
  }));
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  pageSizeOptions: ['10', '20', '50'],
  showTotal: (total) => t('plans.total', { total }),
});

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    completed: t('plans.completed'),
    processing: t('plans.processing'),
    failed: t('plans.failed'),
  };
  return statusMap[status] || t('plans.status');
};

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    completed: 'success',
    processing: 'processing',
    failed: 'error',
  };
  return colorMap[status] || 'default';
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  return dayjs(dateString).format('YYYY-MM-DD');
};

// 格式化时间
const formatTime = (dateString) => {
  if (!dateString) return '';
  return dayjs(dateString).format('HH:mm:ss');
};

// 获取货币名称
const getCurrencyName = (currencyCode) => {
  const currencyMap = {
    'CNY': '人民币',
    'USD': '美元',
    'EUR': '欧元',
    'GBP': '英镑',
    'HKD': '港币'
  };
  return currencyMap[currencyCode] || currencyCode;
};

// 获取语言名称
const getLanguageName = (langCode) => {
  const languageMap = {
    'zh-CN': '中文简体',
    'zh-TW': '中文繁体',
    'en-US': '英文',
    'HK': '繁体中文(香港)',
    'CN': '简体中文',
    'EN': '英文'
  };
  return languageMap[langCode] || langCode;
};

// 格式化货币
const formatCurrency = (amount, currency) => {
  if (!amount) return '0';

  const currencySymbolMap = {
    'CNY': '¥',
    'USD': '$',
    'EUR': '€',
    'GBP': '£',
    'HKD': 'HK$'
  };

  const symbol = currencySymbolMap[currency] || '';
  return `${symbol}${amount.toLocaleString()}`;
};


// 获取计划书列表
const fetchPlanList = async () => {
  loading.value = true;

  try {
    // 构建查询参数
    const params = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      // 其他筛选参数暂不使用，待后端接口支持时添加
    };

    const response = await planAPI.getPlanList(params);

    if (response && response.data) {
      // 后端返回的是包装在data中的分页对象
      planList.value = response.data.records || [];
      pagination.total = response.data.total || 0;
    } else if (response) {
      // 直接返回分页对象的情况
      planList.value = response.records || [];
      pagination.total = response.total || 0;
    } else {
      planList.value = [];
      pagination.total = 0;
    }

    // 打印一下获取到的数据，便于调试
    console.log('获取到计划书列表:', planList.value);
  } catch (error) {
    console.error('获取计划书列表失败:', error);
    message.error('获取计划书列表失败');
    planList.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

// 表格变化处理（排序、筛选、分页）
const handleTableChange = (pag, filters, sorter) => {
  // 更新分页信息
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;

  // 暂时不处理筛选和排序逻辑，因为后端接口尚未支持
  // 重新获取数据
  fetchPlanList();
};

// 搜索处理
const handleSearch = () => {
  pagination.current = 1; // 重置到第一页
  fetchPlanList();
};

// 查看计划书详情
const viewPlanDetails = (record) => {
  selectedPlan.value = record;
  drawerVisible.value = true;
};

// 关闭抽屉
const closeDrawer = () => {
  drawerVisible.value = false;
  selectedPlan.value = null;
};

// 预览PDF
const previewPdf = async (record) => {
  if (record.status !== 'completed') {
    message.warning('计划书尚未生成完成');
    return;
  }

  try {
    // 显示加载提示
    const loadingMessage = message.loading('正在加载PDF...', 0);

    // 获取当前token
    const token = localStorage.getItem('token');

    // 使用新的API接口获取PDF
    const previewUrl = planAPI.previewPdf(record.id);

    // 使用fetch API发送请求，确保包含认证头
    const response = await fetch(previewUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      },
      credentials: 'include'
    });

    // 关闭加载提示
    loadingMessage();

    if (!response.ok) {
      if (response.status === 401) {
        message.error('认证失败，请重新登录');
        return;
      }
      throw new Error(`预览失败: ${response.status} ${response.statusText}`);
    }

    // 检查Content-Type
    const contentType = response.headers.get('Content-Type');
    if (contentType && contentType.includes('application/json')) {
      // 如果返回的是JSON（可能是错误信息）
      const errorData = await response.json();
      throw new Error(errorData.message || '无法获取PDF内容');
    }

    // 获取blob数据
    const blob = await response.blob();

    // 确保是PDF
    if (blob.type !== 'application/pdf' && blob.type !== '') {
      message.error('返回的不是有效的PDF文件');
      return;
    }

    // 创建临时URL
    const url = window.URL.createObjectURL(blob);

    // 打开新窗口进行预览
    window.open(url, '_blank');
    // 清理临时url
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('预览PDF失败:', error);
    message.error(`预览PDF失败: ${error.message || '未知错误'}`);
  }
};

// 下载PDF
const downloadPdf = async (record) => {
  if (record.status !== 'completed') {
    message.warning('计划书尚未生成完成');
    return;
  }

  try {
    // 显示加载提示
    const loadingMessage = message.loading('正在准备下载...', 0);

    // 获取当前token
    const token = localStorage.getItem('token');

    // 使用新的API接口下载PDF
    const downloadUrl = planAPI.downloadPdf(record.id);

    // 使用fetch API进行下载，确保包含认证头
    const response = await fetch(downloadUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      },
      credentials: 'include' // 包含cookie等认证信息
    });

    // 关闭加载提示
    loadingMessage();

    if (!response.ok) {
      if (response.status === 401) {
        message.error('认证失败，请重新登录');
        return;
      }
      throw new Error(`下载失败: ${response.status} ${response.statusText}`);
    }

    // 检查Content-Type
    const contentType = response.headers.get('Content-Type');
    if (contentType && contentType.includes('application/json')) {
      // 如果返回的是JSON（可能是错误信息）
      const errorData = await response.json();
      throw new Error(errorData.message || '无法获取PDF内容');
    }

    // 获取文件名
    let filename = '';
    const disposition = response.headers.get('Content-Disposition');
    if (disposition && disposition.includes('filename=')) {
      // 从Content-Disposition中获取文件名
      const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
      const matches = filenameRegex.exec(disposition);
      if (matches != null && matches[1]) {
        filename = matches[1].replace(/['"]/g, '');
      }
    }

    // 如果没有从响应头获取到文件名，则使用默认文件名
    if (!filename) {
      filename = `${record.productName || '计划书'}_${record.proposalNo}.pdf`;
    }

    // 获取blob数据
    const blob = await response.blob();

    // 确保是PDF
    if (blob.type !== 'application/pdf' && blob.type !== '') {
      message.error('返回的不是有效的PDF文件');
      return;
    }

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();

    // 清理
    window.URL.revokeObjectURL(url);
    document.body.removeChild(link);

    message.success('下载成功');
  } catch (error) {
    console.error('下载文件失败:', error);
    message.error(`下载文件失败: ${error.message || '未知错误'}`);
  }
};

// AI分析
const analyzeWithAI = async (record) => {
  if (record.status !== 'completed') {
    message.warning('计划书尚未生成完成');
    return;
  }

  try {
    // 打开AI聊天模态框，传递计划书ID作为查询参数
    chatProposalId.value = record.id;
    chatProposalName.value = record.productName || '计划书';
    chatModalVisible.value = true;
  } catch (error) {
    console.error('打开AI聊天模态框失败:', error);
    message.error('打开AI聊天模态框失败');
  }
};

onMounted(() => {
  fetchPlanList();
});


</script>

<style scoped>
/* 主题色定义 */
:root {
  --color-primary: #3B82F6;
  --color-primary-hover: #2563EB;
  --color-secondary: #6366F1;
  --color-success: #10B981;
  --color-warning: #F59E0B;
  --color-error: #EF4444;
  --color-info: #6B7280;
}

/* 标题区域动画 */
.title-section {
  background-size: 200% 200%;
  animation: gradientAnimation 5s ease infinite;
}

@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

.page-title {
  color: #fff;
}

.page-description {
  color: #e0e0e0;
}

/* 统计卡片样式 */
.stat-card {
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 筛选区域样式 */
.filter-section {
  transition: all 0.3s ease;
}

/* 卡片阴影效果 */
.shadow-md {
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1), 0 3px 10px 0 rgba(0, 0, 0, 0.05);
}

/* 表格样式优化 */
:deep(.ant-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.ant-table-thead > tr > th) {
  background-color: #f9fafb;
  font-weight: 500;
}

/* 表格条纹样式 */
:deep(.table-striped) {
  background-color: rgba(241, 245, 249, 0.5);
}

/* 行高 */
:deep(.ant-table-tbody > tr > td) {
  padding: 12px;
  font-size: 14px;
}

/* 悬停效果 */
:deep(.ant-table-tbody > tr:hover > td) {
  background-color: rgba(236, 240, 255, 0.7) !important;
}

/* 按钮图标对齐样式 */
.align-middle {
  vertical-align: middle;
}

.inline-flex {
  display: inline-flex;
}

/* 创建计划书按钮样式 */
.create-plan-btn {
  display: inline-flex;
  align-items: center;
  line-height: 1;
}

.create-plan-icon {
  display: inline-flex;
  align-self: center;
  line-height: 0;
  transform: translateY(0);
}

/* 确保所有操作按钮中的图标垂直居中 */
:deep(.ant-btn) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

:deep(.ant-btn .anticon),
:deep(.ant-btn .iconify) {
  display: inline-flex;
  align-self: center;
  line-height: 0;
}

/* 搜索区域响应式调整 */
@media (max-width: 768px) {
  .flex.flex-wrap.items-center>* {
    margin-bottom: 8px;
  }

  .filter-group,
  .search-group {
    width: 100%;
    margin-bottom: 12px;
  }

  .a-input-search,
  .a-select,
  .a-range-picker {
    width: 100% !important;
  }
}

/* 所有操作按钮样式 */
.action-btn {
  display: inline-flex;
  align-items: center;
  line-height: 1;
}

.action-icon {
  display: inline-flex;
  align-self: center;
  line-height: 0;
}

/* 抽屉样式 */
.proposal-drawer :deep(.ant-descriptions-item-label) {
  width: 120px;
  font-weight: 500;
}
</style>