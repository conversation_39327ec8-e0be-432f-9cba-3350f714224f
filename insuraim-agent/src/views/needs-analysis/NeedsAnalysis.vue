<template>
    <div class="needs-analysis-container">
        <div
            class="title-section mb-6 p-6 rounded-lg shadow-md text-white bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700">
            <div class="flex items-center">
                <Icon icon="mdi:file-document-multiple" class="text-4xl mr-3" />
                <h1 class="text-2xl font-bold page-title">保险需求分析</h1>
            </div>
            <p class="mt-2 page-description">
                请选择客户，并填写相关信息，帮助我们更好地了解您的需求
            </p>
        </div>

        <!-- 客户选择区域 -->
        <div v-if="!selectedCustomer && !currentStep" class="customer-selection">
            <!-- 客户列表 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <!-- 筛选器 -->
                <div
                    class="filter-section mb-6 p-6 rounded-lg bg-gradient-to-r from-gray-50 to-gray-100 border border-gray-200">
                    <div class="flex flex-wrap items-center justify-between gap-4">
                        <div class="filter-group flex flex-wrap items-center gap-3">
                            <span class="text-gray-600 text-sm">性别:</span>
                            <a-select v-model:value="customerParams.gender" placeholder="性别" style="width: 100px;"
                                class="gender-select" size="middle" @change="handleGenderChange">
                                <a-select-option value="">全部</a-select-option>
                                <a-select-option value="1">男</a-select-option>
                                <a-select-option value="2">女</a-select-option>
                            </a-select>

                            <span class="text-gray-600 text-sm">状态:</span>
                            <a-select v-model:value="customerParams.status" placeholder="状态" style="width: 100px;"
                                class="status-select" size="middle" @change="handleStatusChange">
                                <a-select-option value="">全部</a-select-option>
                                <a-select-option value="1">活跃</a-select-option>
                                <a-select-option value="0">非活跃</a-select-option>
                            </a-select>
                        </div>

                        <div class="search-group flex items-center gap-3">
                            <a-input-search placeholder="搜索客户" style="width: 280px" class="mr-3" size="middle"
                                @search="searchCustomer" />
                            <a-button type="primary" size="middle" @click="refreshCustomerData">
                                <template #icon>
                                    <Icon icon="mdi:refresh" />
                                </template>
                                <span>重置</span>
                            </a-button>
                        </div>
                    </div>
                </div>

                <!-- 客户表格 -->
                <a-table :dataSource="customerList" :columns="customerColumns" :pagination="{
                    total: customerTotal,
                    current: customerParams.page,
                    pageSize: customerParams.pageSize,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    pageSizeOptions: ['10', '20', '50'],
                    showTotal: (total) => `共 ${total} 条`,
                    size: 'middle'
                }" @change="handleCustomerTableChange" size="middle"
                    :rowClassName="(record, index) => (index % 2 === 1 ? 'table-striped' : '')" bordered
                    :loading="loading" :rowKey="record => record.id">
                    <template #bodyCell="{ column, record }">
                        <!-- 性别列 -->
                        <template v-if="column.dataIndex === 'gender'">
                            <a-tag :color="record.gender === 1 ? 'blue' : 'pink'">
                                {{ record.gender === 1 ? '男' : '女' }}
                            </a-tag>
                        </template>

                        <!-- 生日列 -->
                        <template v-if="column.dataIndex === 'birthday'">
                            {{ formatDate(record.birthday) }}
                        </template>

                        <!-- 状态列 -->
                        <template v-if="column.dataIndex === 'status'">
                            <a-badge :status="record.status === 1 ? 'success' : 'error'"
                                :text="record.status === 1 ? '活跃' : '非活跃'" />
                        </template>

                        <!-- 操作列 -->
                        <template v-if="column.dataIndex === 'action'">
                            <a-button type="primary" @click.stop="handleSelectCustomer(record)">
                                选择
                            </a-button>
                        </template>
                    </template>
                </a-table>
            </div>
        </div>

        <!-- 多步骤流程 -->
        <div v-if="selectedCustomer || currentStep" class="analysis-steps">
            <a-steps :current="currentStep" class="steps-nav">
                <a-step title="基础信息" />
                <a-step title="需求选择" />
                <a-step title="场景问卷" />
                <a-step title="AI分析" />
                <a-step title="推荐方案" />
            </a-steps>

            <!-- 步骤内容区域 -->
            <div class="step-content">
                <!-- 步骤1: 基础信息确认 -->
                <div v-if="currentStep === 0" class="step-basic-info">
                    <h2>基础信息确认</h2>
                    <div class="info-card">
                        <div class="info-item">
                            <span class="label">姓名：</span>
                            <span class="value">{{ formData.name }}</span>
                            <a-button type="link" @click="editField('name')">修改</a-button>
                        </div>
                        <div class="info-item">
                            <span class="label">手机：</span>
                            <span class="value">{{ maskPhone(formData.phone) }}</span>
                            <a-button type="link" @click="editField('phone')">修改</a-button>
                        </div>
                        <div class="info-item">
                            <span class="label">生日：</span>
                            <span class="value">{{ formData.birthday }}</span>
                            <a-button type="link" @click="editField('birthday')">修改</a-button>
                        </div>
                        <div class="info-item">
                            <span class="label">性别：</span>
                            <span class="value">{{ formData.gender === 1 ? '男' : '女' }}</span>
                            <a-button type="link" @click="editField('gender')">修改</a-button>
                        </div>
                    </div>

                    <!-- 编辑弹窗 -->
                    <a-modal v-model:visible="editModalVisible" title="修改信息" @ok="handleEditConfirm"
                        @cancel="editModalVisible = false">
                        <template v-if="editingField === 'name'">
                            <a-input v-model:value="editingValue" placeholder="请输入姓名" />
                        </template>
                        <template v-else-if="editingField === 'phone'">
                            <a-input v-model:value="editingValue" placeholder="请输入手机号" />
                        </template>
                        <template v-else-if="editingField === 'birthday'">
                            <a-date-picker v-model:value="editingDate" style="width: 100%" :format="dateFormat" />
                        </template>
                        <template v-else-if="editingField === 'gender'">
                            <a-radio-group v-model:value="editingValue">
                                <a-radio :value="1">男</a-radio>
                                <a-radio :value="2">女</a-radio>
                            </a-radio-group>
                        </template>
                    </a-modal>

                    <div class="step-actions">
                        <a-button @click="resetAnalysis">返回</a-button>
                        <a-button type="primary" @click="nextStep">确认无误，下一步</a-button>
                    </div>
                </div>

                <!-- 步骤2: 需求优先级选择 -->
                <div v-if="currentStep === 1" class="step-needs-priority">
                    <h2>您最关心的保障是什么？</h2>
                    <p class="step-description">请选择最多3项您最关心的保障需求</p>

                    <div class="needs-cards">
                        <div v-for="need in needsOptions" :key="need.id" class="need-card"
                            :class="{ 'selected': formData.needsPriority.includes(need.id) }"
                            @click="toggleNeedSelection(need.id)">
                            <div class="need-icon">{{ need.icon }}</div>
                            <div class="need-content">
                                <div class="need-title">{{ need.title }}</div>
                                <div class="need-desc">{{ need.description }}</div>
                            </div>
                            <div class="need-check">
                                <a-checkbox :checked="formData.needsPriority.includes(need.id)" />
                            </div>
                        </div>
                    </div>

                    <div class="selection-counter">
                        已选：{{ formData.needsPriority.length }}/3
                    </div>

                    <div class="step-actions">
                        <a-button @click="prevStep">上一步</a-button>
                        <a-button type="primary" @click="nextStep" :disabled="formData.needsPriority.length === 0">
                            下一步
                        </a-button>
                    </div>
                </div>

                <!-- 步骤3: 场景化问卷 -->
                <div v-if="currentStep === 2" class="step-scenario-survey">
                    <h2>个性化场景问卷</h2>
                    <p class="step-description">请回答以下问题，帮助我们更好地了解您的需求</p>

                    <div class="survey-container">
                        <!-- 职业相关问题 -->
                        <div class="survey-section" v-if="shouldShowSection('career')">
                            <div class="survey-question">
                                <h3>🏢 您的工作性质属于：</h3>
                                <a-radio-group v-model:value="formData.scenarioAnswers.career">
                                    <a-radio :value="1">办公室文职（低风险）</a-radio>
                                    <a-radio :value="2">户外作业（中风险）</a-radio>
                                    <a-radio :value="3">高空/危险作业（高风险）</a-radio>
                                    <a-radio :value="4">其他</a-radio>
                                </a-radio-group>
                            </div>

                            <div class="survey-question">
                                <h3>🚗 日常通勤方式：</h3>
                                <a-radio-group v-model:value="formData.scenarioAnswers.commute">
                                    <a-radio :value="1">步行/自行车</a-radio>
                                    <a-radio :value="2">公共交通</a-radio>
                                    <a-radio :value="3">自驾车</a-radio>
                                    <a-radio :value="4">公司班车</a-radio>
                                </a-radio-group>
                            </div>
                        </div>

                        <!-- 医疗相关问题 -->
                        <div class="survey-section" v-if="shouldShowSection('medical')">
                            <div class="survey-question">
                                <h3>💊 是否有慢性疾病？</h3>
                                <a-radio-group v-model:value="formData.scenarioAnswers.chronic">
                                    <a-radio :value="1">没有</a-radio>
                                    <a-radio :value="2">有，控制良好</a-radio>
                                    <a-radio :value="3">有，需要定期治疗</a-radio>
                                </a-radio-group>
                            </div>

                            <!-- 健康数据授权模块 -->
                            <div class="survey-question sensitive-data-section">
                                <div class="sensitive-data-card">
                                    <h3>🔒 健康数据授权</h3>
                                    <p>为提供精准医疗险推荐，需要了解：</p>
                                    <ul>
                                        <li>过去5年住院史</li>
                                        <li>慢性病记录</li>
                                        <li>家族遗传病史</li>
                                    </ul>
                                    <p class="note">数据加密存储，可随时删除</p>

                                    <div class="sensitive-actions">
                                        <a-button type="primary" @click="showHealthDataForm = true"
                                            v-if="!formData.sensitiveData.healthProvided">
                                            同意并填写
                                        </a-button>
                                        <a-button v-if="!formData.sensitiveData.healthProvided" @click="skipHealthData">
                                            暂不提供
                                        </a-button>
                                        <div v-if="formData.sensitiveData.healthProvided" class="data-provided">
                                            <a-tag color="success">已提供</a-tag>
                                            <a-button type="link" @click="showHealthDataForm = true">修改</a-button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 家庭相关问题 -->
                        <div class="survey-section" v-if="shouldShowSection('family')">
                            <div class="survey-question">
                                <h3>👨‍👩‍👧 家庭结构：</h3>
                                <a-radio-group v-model:value="formData.scenarioAnswers.family">
                                    <a-radio :value="1">单身</a-radio>
                                    <a-radio :value="2">已婚无子女</a-radio>
                                    <a-radio :value="3">已婚有子女</a-radio>
                                    <a-radio :value="4">单亲家庭</a-radio>
                                </a-radio-group>
                            </div>

                            <div class="survey-question"
                                v-if="formData.scenarioAnswers.family === 3 || formData.scenarioAnswers.family === 4">
                                <h3>👶 子女年龄：</h3>
                                <a-radio-group v-model:value="formData.scenarioAnswers.childAge">
                                    <a-radio :value="1">0-6岁</a-radio>
                                    <a-radio :value="2">7-15岁</a-radio>
                                    <a-radio :value="3">16-22岁</a-radio>
                                    <a-radio :value="4">23岁以上</a-radio>
                                </a-radio-group>
                            </div>
                        </div>
                    </div>

                    <!-- 健康数据表单弹窗 -->
                    <a-modal v-model:visible="showHealthDataForm" title="健康数据填写" @ok="submitHealthData"
                        @cancel="showHealthDataForm = false" width="600px">
                        <a-form :model="healthDataForm" layout="vertical">
                            <a-form-item label="过去5年是否有住院记录">
                                <a-radio-group v-model:value="healthDataForm.hospitalized">
                                    <a-radio :value="1">无</a-radio>
                                    <a-radio :value="2">1-2次（非大病）</a-radio>
                                    <a-radio :value="3">多次或重大疾病</a-radio>
                                </a-radio-group>
                            </a-form-item>

                            <a-form-item label="是否有慢性病">
                                <a-select v-model:value="healthDataForm.chronicDiseases" mode="multiple"
                                    placeholder="请选择（可多选）" style="width: 100%">
                                    <a-select-option value="none">无</a-select-option>
                                    <a-select-option value="hypertension">高血压</a-select-option>
                                    <a-select-option value="diabetes">糖尿病</a-select-option>
                                    <a-select-option value="hyperlipidemia">高血脂</a-select-option>
                                    <a-select-option value="heart">心脏病</a-select-option>
                                    <a-select-option value="other">其他</a-select-option>
                                </a-select>
                            </a-form-item>

                            <a-form-item label="家族病史">
                                <a-select v-model:value="healthDataForm.familyHistory" mode="multiple"
                                    placeholder="请选择（可多选）" style="width: 100%">
                                    <a-select-option value="none">无</a-select-option>
                                    <a-select-option value="cancer">癌症</a-select-option>
                                    <a-select-option value="cardiovascular">心血管疾病</a-select-option>
                                    <a-select-option value="diabetes">糖尿病</a-select-option>
                                    <a-select-option value="mental">精神类疾病</a-select-option>
                                    <a-select-option value="other">其他</a-select-option>
                                </a-select>
                            </a-form-item>
                        </a-form>

                        <div class="privacy-statement">
                            <p>隐私声明：您提供的健康数据将被加密存储，仅用于保险产品推荐，您可以随时删除这些数据。</p>
                        </div>
                    </a-modal>

                    <div class="survey-progress">
                        <a-progress :percent="surveyProgress" status="active" />
                        <span>完成度：{{ surveyProgress }}%</span>
                    </div>

                    <div class="step-actions">
                        <a-button @click="prevStep">上一步</a-button>
                        <a-button type="primary" @click="nextStep" :disabled="surveyProgress < 50">
                            下一步
                        </a-button>
                    </div>
                </div>

                <!-- 步骤4: AI推荐生成 -->
                <div v-if="currentStep === 3" class="step-ai-analysis">
                    <h2>AI智能分析</h2>
                    <p class="step-description">正在生成个性化方案...</p>

                    <!-- 使用AiAnalysisReport组件 - 将组件实例保存到ref中，以便在所有步骤中使用同一个实例 -->
                    <div v-if="!showReport">
                        <AiAnalysisReport ref="aiAnalysisReport" :formData="formData" />
                    </div>

                    <div class="step-actions" v-if="!aiAnalysisReport?.completed">
                        <a-button @click="prevStep" :disabled="aiGenerating">上一步</a-button>
                        <a-button type="primary" @click="generateAIRecommendation" :loading="aiGenerating">
                            {{ aiGenerating ? '生成中...' : '立即生成分析报告' }}
                        </a-button>
                    </div>

                    <div class="analysis-completed" v-else>
                        <p class="text-green-600 font-medium">分析完成！</p>
                        <div class="step-actions">
                            <a-button @click="prevStep">上一步</a-button>
                            <a-button type="primary" @click="nextStep">查看分析报告</a-button>
                        </div>
                    </div>
                </div>

                <!-- 步骤5: AI分析结果展示 -->
                <div v-if="currentStep === 4" class="step-recommendation">

                    <!-- 直接显示报告内容 -->
                    <div v-if="showReport && reportContent" class="report-content bg-white rounded-lg p-6 my-6"
                        id="report-content">

                        <!-- 直接显示已保存的报告内容 -->
                        <div v-html="reportContent" class="markdown-body prose max-w-none"></div>

                        <!-- PDF导出专用页脚，在正常视图中不显示 -->
                        <div class="pdf-only page-footer-content"
                            style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eaeaea;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="font-size: 12px; color: #999;">
                                    本报告由AI生成，仅供参考 | {{ new Date().toLocaleDateString() }}
                                </div>
                                <div style="font-size: 12px; color: #999; text-align: right;">
                                    <!-- 此页码会在导出PDF时被正确替换 -->
                                    页码
                                </div>
                            </div>
                        </div>

                    </div>

                    <!-- 如果报告未生成，显示提示 -->
                    <div v-else
                        class="flex flex-col items-center justify-center py-10 bg-gray-50 rounded-lg border border-gray-200">
                        <Icon icon="mdi:alert-circle-outline" class="text-5xl text-amber-500 mb-4" />
                        <p class="text-gray-600">未找到分析报告，请返回上一步重新生成</p>
                        <a-button class="mt-4" @click="prevStep">返回生成报告</a-button>
                    </div>

                    <div class="recommendation-actions mt-6 flex flex-wrap justify-center gap-4">
                        <a-button type="primary" class="flex items-center" @click="downloadReport"
                            :disabled="!showReport || !reportContent">
                            <template #icon>
                                <Icon icon="mdi:download" />
                            </template>
                            导出PDF
                        </a-button>
                        <a-button type="primary" class="flex items-center" @click="exportImage"
                            :disabled="!showReport || !reportContent">
                            <template #icon>
                                <Icon icon="mdi:download" />
                            </template>
                            导出图片
                        </a-button>
                    </div>

                    <div class="step-actions final-actions">
                        <a-button @click="prevStep" :disabled="true">上一步</a-button>
                        <a-button type="primary" @click="completeAnalysis">完成分析</a-button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue';
import { customerAPI } from '@/api';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import markdownit from 'markdown-it';
import { Icon } from '@iconify/vue';
import AiAnalysisReport from '@/views/needs-analysis/components/AiAnalysisReport.vue';
import { exportElementToPDF } from '@/utils/pdf';
import { exportMultipleElementsToPDF } from '@/utils/pdf';
import html2canvas from 'html2canvas-pro';
// 初始化markdown渲染器，简化配置与ai-chat-new.vue保持一致
const md = markdownit({ html: true, breaks: true });

// 创建markdown渲染函数，与ai-chat-new.vue保持一致
const renderMarkdown = (content) => {
    if (!content) return ''; // 处理undefined或null的情况
    try {
        // 添加对标题格式的预处理
        // 处理特殊情况：以#开头但没有空格的标题，如"#退休收入补充保险需求分析报告"
        let processedContent = content;

        // 匹配以#开头但后面没有空格的标题（仅处理标题格式，不处理其他格式）
        // 例如: #退休收入补充保险需求分析报告 -> # 退休收入补充保险需求分析报告
        processedContent = processedContent.replace(/^(#{1,6})([^#\s])/gm, '$1 $2');

        // 处理行内的标题格式（如果有新行中的标题）
        processedContent = processedContent.replace(/\n(#{1,6})([^#\s])/gm, '\n$1 $2');

        // 使用markdown-it渲染内容并返回HTML
        return md.render(processedContent);
    } catch (e) {
        console.error('Markdown渲染错误:', e);
        return content || '';
    }
};

// 强制刷新渲染内容
const forceRefreshRendering = (htmlContent) => {
    if (!htmlContent) return htmlContent;

    // 通过添加一个不可见的时间戳注释来强制Vue更新DOM
    return htmlContent + `<!-- refresh-timestamp: ${Date.now()} -->`;
};

// 客户列表参数
const customerParams = reactive({
    page: 1,
    pageSize: 10,
    gender: '',
    status: '',
    keyword: ''
});

// 表格列定义
const customerColumns = [
    {
        title: '序号',
        dataIndex: 'index',
        width: 60,
        customRender: ({ index }) => index + 1
    },
    {
        title: '姓名',
        dataIndex: 'name',
        width: 100
    },
    {
        title: '手机号',
        dataIndex: 'phone',
        width: 120
    },
    {
        title: '性别',
        dataIndex: 'gender',
        width: 60
    },
    {
        title: '生日',
        dataIndex: 'birthday',
        width: 100
    },
    {
        title: '身份证号',
        dataIndex: 'idCard',
        width: 160
    },
    {
        title: '状态',
        dataIndex: 'status',
        width: 80
    },
    {
        title: '操作',
        dataIndex: 'action',
        width: 80
    }
];

// 格式化日期
const formatDate = (timestamp) => {
    if (!timestamp) return '—';
    const date = new Date(timestamp);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

// 客户列表数据
const customerList = ref([]);
const customerTotal = ref(0);
const loading = ref(false);

// 表格列定义
const columns = [
    {
        title: '姓名',
        dataIndex: 'name',
        key: 'name',
    },
    {
        title: '手机号',
        dataIndex: 'phone',
        key: 'phone',
    },
    {
        title: '性别',
        dataIndex: 'gender',
        key: 'gender',
    },
    {
        title: '生日',
        dataIndex: 'birthday',
        key: 'birthday',
    },
    {
        title: '操作',
        key: 'action',
    }
];

// 状态管理
const selectedCustomer = ref(null);
const currentStep = ref(null);
const editModalVisible = ref(false);
const editingField = ref('');
const editingValue = ref('');
const editingDate = ref(null);
const dateFormat = 'YYYY-MM-DD';
const showHealthDataForm = ref(false);
const aiGenerating = ref(false);
const showReport = ref(false);
const reportContent = ref('');

// AI推荐结果数据
const aiResult = reactive({
    completed: false,
    matchScore: 0,
    categoryMatches: [
        { category: '医疗险', score: 0 },
        { category: '意外险', score: 0 },
        { category: '重疾险', score: 0 },
        { category: '养老险', score: 0 }
    ],
    topProduct: {
        name: '',
        matchPoint: '',
        premium: 0,
        paymentTerm: 0,
        feature: ''
    },
    report: ''
});

// 表单数据
const formData = reactive({
    name: '',
    phone: '',
    birthday: '',
    gender: 1,
    // 后续步骤数据
    needsPriority: [], // 需求优先级
    scenarioAnswers: {}, // 场景问卷答案
    sensitiveData: {
        healthProvided: false, // 是否提供健康数据
        healthData: {} // 健康数据内容
    }, // 敏感数据（如健康记录）
});

// 健康数据表单
const healthDataForm = reactive({
    hospitalized: 1, // 住院记录
    chronicDiseases: [], // 慢性病
    familyHistory: [] // 家族病史
});

// 需求选项
const needsOptions = [
    {
        id: 'medical',
        title: '大病医疗费用报销',
        description: '涵盖重大疾病的医疗费用报销',
        icon: '🏥',
    },
    {
        id: 'accident',
        title: '意外受伤赔偿',
        description: '意外伤害导致的医疗费用和伤残赔偿',
        icon: '🚑',
    },
    {
        id: 'education',
        title: '子女教育金储备',
        description: '为子女未来教育提供资金保障',
        icon: '🎓',
    },
    {
        id: 'retirement',
        title: '退休后收入补充',
        description: '退休后的收入来源和生活保障',
        icon: '👴',
    },
    {
        id: 'property',
        title: '家庭财产保障',
        description: '保障家庭财产免受意外损失',
        icon: '🏠',
    },
    {
        id: 'parent',
        title: '赡养父母责任',
        description: '为父母年老后的医疗和生活提供保障',
        icon: '👨‍👩‍👧',
    },
];

// 获取客户列表
const getCustomerList = async () => {
    loading.value = true;
    try {
        const params = {
            page: customerParams.page,
            pageSize: customerParams.pageSize
        };

        // 如果有过滤条件，添加到请求参数中
        if (customerParams.gender !== '') {
            params.gender = customerParams.gender;
        }

        if (customerParams.status !== '') {
            params.status = customerParams.status;
        }

        if (customerParams.keyword) {
            params.keyword = customerParams.keyword;
        }

        const response = await customerAPI.getCustomerList(params);

        if (response) {
            customerList.value = response.records || [];
            customerTotal.value = response.total || 0;
        } else {
            // 模拟数据，实际应该使用API返回的数据
            customerList.value = [];
            customerTotal.value = 0;
            message.error('获取客户列表失败');
        }
        console.log('获取客户列表成功');
    } catch (error) {
        message.error('获取客户列表失败');
        console.error(error);

        // 出错时使用模拟数据
        customerList.value = [
            {
                "id": 1,
                "name": "王小明",
                "phone": "13800138001",
                "gender": 1,
                "birthday": "1988-12-01",
                "idCard": "110101198812011234",
                "address": "北京市海淀区中关村大街1号",
                "createTime": 1672502400000,
                "updateTime": 1672502400000,
                "status": 1,
                "remark": "优质客户"
            },
            {
                "id": 2,
                "name": "张丽华",
                "phone": "13912345678",
                "gender": 2,
                "birthday": "1990-05-05",
                "idCard": "310105199005051122",
                "address": "上海市浦东新区陆家嘴环路1000号",
                "createTime": 1675180800000,
                "updateTime": 1675180800000,
                "status": 1,
                "remark": "企业高管"
            }
        ];
        customerTotal.value = customerList.value.length;
    } finally {
        loading.value = false;
    }
};

// 刷新客户数据
const refreshCustomerData = () => {
    customerParams.gender = '';
    customerParams.status = '';
    customerParams.keyword = '';
    customerParams.page = 1;
    getCustomerList();
};

// 处理表格变化
const handleCustomerTableChange = (pagination) => {
    customerParams.page = pagination.current;
    customerParams.pageSize = pagination.pageSize;
    getCustomerList();
};

// 处理性别选择变化
const handleGenderChange = () => {
    customerParams.page = 1;
    getCustomerList();
};

// 处理状态选择变化
const handleStatusChange = () => {
    customerParams.page = 1;
    getCustomerList();
};

// 搜索客户
const searchCustomer = (value) => {
    customerParams.keyword = value;
    customerParams.page = 1;
    getCustomerList();
};

// 选择客户
const handleSelectCustomer = (record) => {
    selectedCustomer.value = record;
    // 初始化表单数据
    formData.name = record.name;
    formData.phone = record.phone;
    formData.birthday = record.birthday;
    formData.gender = record.gender;
    // 设置当前步骤为0（基础信息确认）
    currentStep.value = 0;
};

// 重置分析流程
const resetAnalysis = () => {
    selectedCustomer.value = null;
    currentStep.value = null;
    // 重置报告相关状态
    showReport.value = false;
    reportContent.value = '';
    // 重置组件（如果存在）
    if (aiAnalysisReport.value) {
        try {
            aiAnalysisReport.value.reset();
        } catch (e) {
            console.error('重置AI分析组件失败:', e);
        }
    }
    // 重置表单数据
    Object.keys(formData).forEach(key => {
        if (Array.isArray(formData[key])) {
            formData[key] = [];
        } else if (typeof formData[key] === 'object') {
            formData[key] = {};
        } else {
            formData[key] = '';
        }
    });
    formData.gender = 1;
};

// 下一步
const nextStep = () => {
    if (currentStep.value < 4) {
        currentStep.value++;
    }
};

// 上一步
const prevStep = () => {
    if (currentStep.value > 0) {
        currentStep.value--;
    }
};

// 手机号码脱敏显示
const maskPhone = (phone) => {
    if (!phone) return '';
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
};

// 编辑字段
const editField = (field) => {
    editingField.value = field;
    if (field === 'birthday') {
        editingDate.value = formData.birthday ? dayjs(formData.birthday) : null;
    } else {
        editingValue.value = formData[field];
    }
    editModalVisible.value = true;
};

// 确认编辑
const handleEditConfirm = () => {
    if (editingField.value === 'birthday' && editingDate.value) {
        formData[editingField.value] = editingDate.value.format(dateFormat);
    } else {
        formData[editingField.value] = editingValue.value;
    }
    editModalVisible.value = false;
};

// 切换需求选择
const toggleNeedSelection = (needId) => {
    const index = formData.needsPriority.indexOf(needId);
    if (index > -1) {
        // 如果已选中，则取消选择
        formData.needsPriority.splice(index, 1);
    } else {
        // 如果未选中，且选择数量未超过3个，则添加选择
        if (formData.needsPriority.length < 3) {
            formData.needsPriority.push(needId);
        } else {
            message.warning('最多只能选择3项保障需求');
        }
    }
};

// 根据需求优先级判断是否显示特定场景问题部分
const shouldShowSection = (sectionType) => {
    // 始终显示职业部分
    if (sectionType === 'career') {
        return true;
    }

    // 如果选择了医疗相关，显示医疗部分
    if (sectionType === 'medical') {
        return formData.needsPriority.includes('medical');
    }

    // 如果选择了子女教育或赡养父母，显示家庭部分
    if (sectionType === 'family') {
        return formData.needsPriority.includes('education') ||
            formData.needsPriority.includes('parent');
    }

    return false;
};

// 计算问卷完成度
const surveyProgress = computed(() => {
    let answeredQuestions = 0;
    let totalQuestions = 0;

    // 职业问题（总是显示）
    totalQuestions += 2;
    if (formData.scenarioAnswers.career) answeredQuestions++;
    if (formData.scenarioAnswers.commute) answeredQuestions++;

    // 医疗问题（条件显示）
    if (shouldShowSection('medical')) {
        totalQuestions += 1;
        if (formData.scenarioAnswers.chronic) answeredQuestions++;

        // 敏感数据授权也计入问题
        totalQuestions += 1;
        if (formData.sensitiveData.healthProvided || formData.sensitiveData.healthSkipped) {
            answeredQuestions++;
        }
    }

    // 家庭问题（条件显示）
    if (shouldShowSection('family')) {
        totalQuestions += 1;
        if (formData.scenarioAnswers.family) answeredQuestions++;

        // 如果有子女，增加子女年龄问题
        if (formData.scenarioAnswers.family === 3 || formData.scenarioAnswers.family === 4) {
            totalQuestions += 1;
            if (formData.scenarioAnswers.childAge) answeredQuestions++;
        }
    }

    return totalQuestions > 0 ? Math.floor((answeredQuestions / totalQuestions) * 100) : 0;
});

// 跳过健康数据收集
const skipHealthData = () => {
    formData.sensitiveData.healthSkipped = true;
    message.info('已跳过健康数据收集');
};

// 提交健康数据
const submitHealthData = () => {
    // 将表单数据复制到敏感数据对象中
    formData.sensitiveData.healthData = { ...healthDataForm };
    formData.sensitiveData.healthProvided = true;
    formData.sensitiveData.healthSkipped = false;
    showHealthDataForm.value = false;
    message.success('健康数据已保存');
};

// 生成AI推荐
const aiAnalysisReport = ref(null);

const generateAIRecommendation = async () => {
    if (!aiAnalysisReport.value) {
        console.error('AI分析组件未初始化');
        message.error('AI分析组件未初始化，请刷新页面重试');
        return;
    }

    aiGenerating.value = true;

    try {
        // 调用AI分析组件的生成报告方法
        await aiAnalysisReport.value.generateReport();

        // 检查报告是否成功生成
        if (!aiAnalysisReport.value.completed) {
            throw new Error('报告生成未完成');
        }

        // 保存报告内容，用于在第5步中显示
        // 确保保存的是已经渲染好的HTML内容，而不是原始Markdown
        const renderedHtml = aiAnalysisReport.value.renderedReport;
        console.log('从AI组件获取的渲染报告类型:', typeof renderedHtml);
        console.log('从AI组件获取的渲染报告长度:', renderedHtml?.length || 0);

        // 增加调试信息
        if (renderedHtml) {
            console.log('渲染报告前100个字符:', renderedHtml.substring(0, 100));
            console.log('渲染报告是否包含HTML标签:', /<[a-z][\s\S]*>/i.test(renderedHtml));
        }

        if (renderedHtml && renderedHtml.length > 0 && /<[a-z][\s\S]*>/i.test(renderedHtml)) {
            // 使用组件提供的已渲染HTML，并强制刷新
            reportContent.value = forceRefreshRendering(renderedHtml);
            console.log('使用组件提供的已渲染HTML, 长度:', reportContent.value.length);
        } else if (aiAnalysisReport.value.report) {
            // 如果渲染HTML为空或不包含HTML标签但有原始报告，尝试在父组件中渲染
            console.log('尝试在父组件中重新渲染报告');
            console.log('原始报告长度:', aiAnalysisReport.value.report.length);
            console.log('原始报告前100个字符:', aiAnalysisReport.value.report.substring(0, 100));

            // 使用renderMarkdown函数渲染
            const rendered = renderMarkdown(aiAnalysisReport.value.report);
            console.log('渲染后HTML长度:', rendered.length);
            console.log('渲染后HTML前100个字符:', rendered.substring(0, 100));
            console.log('渲染后是否包含HTML标签:', /<[a-z][\s\S]*>/i.test(rendered));

            // 确保结果是HTML格式
            if (/<[a-z][\s\S]*>/i.test(rendered)) {
                reportContent.value = forceRefreshRendering(rendered);
                console.log('在父组件中渲染后的HTML长度:', reportContent.value.length);
            } else {
                // 强制转换为HTML格式，防止显示原始Markdown
                const fallbackHtml = `<div>
                    <h1 style="text-align: center; margin-bottom: 20px;">保险需求分析报告</h1>
                    <div>${aiAnalysisReport.value.report.replace(/\n/g, '<br>')}</div>
                </div>`;
                reportContent.value = forceRefreshRendering(fallbackHtml);
                console.log('使用应急HTML格式化:', reportContent.value.substring(0, 100));
            }
        } else {
            console.error('无法获取报告内容');
            throw new Error('无法获取报告内容');
        }

        // 设置状态标记，表示应该显示报告
        showReport.value = true;

        // 生成完成后，设置分析结果数据并自动进入第五步
        aiResult.completed = true;

        // 延迟一下再进入下一步，让用户看到完成状态
        setTimeout(() => {
            // 直接跳转到第五步
            currentStep.value = 4;
        }, 300);
    } catch (error) {
        console.error('生成AI推荐失败:', error);
        message.error('生成AI推荐失败，请重试');
    } finally {
        aiGenerating.value = false;
    }
};

// 下载分析报告
const downloadReport = () => {
    exportToPDF();
};

// 使报告中的"收益演示数据"及以下内容显示在第二页
const handleReportRendering = () => {
    if (!showReport.value || !reportContent.value) return;

    // 这个函数会在报告内容更新后被调用，可用于添加额外的DOM处理逻辑
    // 但对于PDF导出，主要处理逻辑已在exportToPDF函数中
    console.log('报告内容已更新，可进行额外处理');
};

// 监听reportContent的变化
watch(reportContent, (newVal) => {
    if (newVal) {
        // 在下一个tick处理，确保DOM已更新
        nextTick(() => {
            handleReportRendering();
        });
    }
});

// 导出PDF
const exportToPDF = async () => {
    if (!reportContent.value) {
        message.warning('没有可导出的数据');
        return;
    }

    try {
        // 显示加载提示
        const loadingKey = 'pdf-loading';
        message.loading({ content: '正在生成PDF，请稍候...', key: loadingKey, duration: 0 });

        // 导出文件名
        let filename = '';
        filename = `保险需求分析报告_${formData.name || '客户'}_${new Date().getTime()}.pdf`;

        // 获取原始DOM元素
        const originalDom = document.getElementById('report-content');
        if (!originalDom) {
            message.error({ content: '找不到报告内容元素', key: loadingKey });
            return;
        }

        // 分析报告内容，检测是否需要分页
        const contentDivs = splitReportForPDF(originalDom);

        if (!contentDivs || contentDivs.length === 0) {
            message.error({ content: '无法处理报告内容', key: loadingKey });
            return;
        }

        // 处理导出逻辑
        let success = false;
        if (contentDivs.length === 1) {
            // 只有一页内容，使用原来的导出逻辑
            success = await exportSinglePagePDF(contentDivs[0], filename);
        } else {
            // 多页内容，使用多页导出逻辑
            success = await exportMultiPagePDF(contentDivs, filename);
        }

        // 显示结果提示
        if (success) {
            message.success({ content: '导出PDF成功', key: loadingKey, duration: 2 });
        } else {
            message.error({ content: '导出PDF失败，请稍后重试', key: loadingKey, duration: 3 });
        }
    } catch (error) {
        console.error('导出PDF失败:', error);
        message.error(`导出PDF失败: ${error.message || '请稍后重试'}`);
    }
};

// 分割报告内容为多个页面
const splitReportForPDF = (reportDom) => {
    if (!reportDom) return [];

    try {
        // 创建一个临时容器来存放克隆的DOM
        const tempContainer = document.createElement('div');
        tempContainer.style.position = 'absolute';
        tempContainer.style.left = '-9999px';
        tempContainer.style.top = '-9999px';
        document.body.appendChild(tempContainer);

        // 克隆原始DOM
        const clonedDom = reportDom.cloneNode(true);
        tempContainer.appendChild(clonedDom);

        // 查找"收益演示数据"标题
        const markdownBody = clonedDom.querySelector('.markdown-body');
        if (!markdownBody) {
            console.error('找不到markdown-body元素');
            document.body.removeChild(tempContainer);
            return [reportDom];
        }

        // 查找所有h2和h3标题，确定是否存在"收益演示数据"
        const headers = markdownBody.querySelectorAll('h2, h3');
        let targetHeader = null;

        // 更多关键词以匹配多种可能的标题
        const keywordsToMatch = [
            '收益演示数据',
            '收益演示',
            '演示数据',
            '收益表',
            '注意事项',
            '收益率',
            '产品示例',
            '示例数据'
        ];

        for (const header of headers) {
            // 检查标题是否包含任一关键词
            if (keywordsToMatch.some(keyword => header.textContent.includes(keyword))) {
                targetHeader = header;
                console.log('找到目标分页标题:', header.textContent);
                break;
            }
        }

        if (!targetHeader) {
            console.log('未找到收益演示数据标题，使用单页导出');
            document.body.removeChild(tempContainer);
            return [reportDom];
        }

        // 创建两个DOM元素分别表示第一页和第二页
        const page1Dom = document.createElement('div');
        page1Dom.className = 'pdf-page report-content';
        page1Dom.style.backgroundColor = '#ffffff';
        page1Dom.style.padding = '0px';
        page1Dom.style.boxSizing = 'border-box';
        page1Dom.style.position = 'relative'; // 确保相对定位以便页脚正确放置
        page1Dom.style.width = '800px'; // 固定宽度，确保一致的布局

        const page2Dom = document.createElement('div');
        page2Dom.className = 'pdf-page report-content';
        page2Dom.style.backgroundColor = '#ffffff';
        page2Dom.style.padding = '20px';
        page2Dom.style.boxSizing = 'border-box';
        page2Dom.style.position = 'relative'; // 确保相对定位以便页脚正确放置
        page2Dom.style.width = '800px'; // 固定宽度，确保一致的布局

        // 添加页眉到第一页
        const page1Header = document.createElement('div');
        page1Header.className = 'pdf-header';
        page1Header.innerHTML = `
            <div style="display: flex; align-items: center; margin-bottom: 15px;">
                <div style="flex: 1; text-align: left; font-size: 14px; color: #666;">
                    <div>客户: ${formData.name || '未知客户'}</div>
                    <div>性别: ${formData.gender === 1 ? '男' : formData.gender === 2 ? '女' : '未知'}</div>
                </div>
                <div style="flex: 1; text-align: right; font-size: 14px; color: #666;">
                    <div>生成日期: ${new Date().toLocaleDateString()}</div>
                </div>
            </div>
            <hr style="border: 0; height: 1px; background-color: #eaeaea; margin: 15px 0 25px 0;">
        `;
        page1Dom.appendChild(page1Header);

        // 添加页眉到第二页
        const page2Header = document.createElement('div');
        page2Header.className = 'pdf-header';
        page2Header.innerHTML = `
            <div style="display: flex; align-items: center; margin-bottom: 15px;">
                <div style="flex: 1; text-align: left; font-size: 14px; color: #666;">
                    <div>客户: ${formData.name || '未知客户'}</div>
                    <div>${targetHeader.textContent}</div>
                </div>
                <div style="flex: 1; text-align: right; font-size: 14px; color: #666;">
                    <div>生成日期: ${new Date().toLocaleDateString()}</div>
                </div>
            </div>
            <hr style="border: 0; height: 1px; background-color: #eaeaea; margin: 15px 0 25px 0;">
        `;
        page2Dom.appendChild(page2Header);

        // 创建内容容器
        const page1Content = document.createElement('div');
        page1Content.className = 'markdown-body prose max-w-none';
        page1Dom.appendChild(page1Content);

        const page2Content = document.createElement('div');
        page2Content.className = 'markdown-body prose max-w-none';
        page2Dom.appendChild(page2Content);

        // 分割内容
        // 找到目标标题的所有父元素
        let currentNode = targetHeader;
        let parentSection = null;

        // 尝试找到目标标题所在的父级section或div
        while (currentNode && currentNode !== markdownBody) {
            if (currentNode.nodeName === 'SECTION' ||
                (currentNode.nodeName === 'DIV' && !currentNode.classList.contains('markdown-body'))) {
                parentSection = currentNode;
                break;
            }
            currentNode = currentNode.parentNode;
        }

        // 如果找到了父级容器，则将其及之后的所有兄弟元素移动到第二页
        if (parentSection) {
            // 将目标标题之前的所有内容复制到第一页
            const children = Array.from(markdownBody.childNodes);
            let reachedTarget = false;

            for (const child of children) {
                if (child === parentSection || child.contains(parentSection)) {
                    reachedTarget = true;
                }

                if (!reachedTarget) {
                    page1Content.appendChild(child.cloneNode(true));
                } else {
                    page2Content.appendChild(child.cloneNode(true));
                }
            }
        } else {
            // 如果没有找到父容器，则直接从目标标题开始分割
            const children = Array.from(markdownBody.childNodes);
            let reachedTarget = false;

            for (const child of children) {
                if (child === targetHeader || child.contains(targetHeader)) {
                    reachedTarget = true;
                }

                if (!reachedTarget) {
                    page1Content.appendChild(child.cloneNode(true));
                } else {
                    page2Content.appendChild(child.cloneNode(true));
                }
            }
        }

        // 添加页脚到第一页
        const page1Footer = document.createElement('div');
        page1Footer.className = 'pdf-footer';
        page1Footer.style.marginTop = '40px';
        page1Footer.style.paddingTop = '20px';
        page1Footer.style.borderTop = '1px solid #eaeaea';
        page1Footer.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div style="font-size: 12px; color: #999;">
                    本报告由AI生成，仅供参考
                </div>
                <div style="font-size: 12px; color: #999; text-align: right;">
                    第 1 页
                </div>
            </div>
        `;
        page1Dom.appendChild(page1Footer);

        // 添加页脚到第二页
        const page2Footer = document.createElement('div');
        page2Footer.className = 'pdf-footer';
        page2Footer.style.marginTop = '40px';
        page2Footer.style.paddingTop = '20px';
        page2Footer.style.borderTop = '1px solid #eaeaea';
        page2Footer.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div style="font-size: 12px; color: #999;">
                    本报告由AI生成，仅供参考
                </div>
                <div style="font-size: 12px; color: #999; text-align: right;">
                    第 2 页
                </div>
            </div>
        `;
        page2Dom.appendChild(page2Footer);

        // 清理临时容器
        document.body.removeChild(tempContainer);

        // 复制样式
        applyMarkdownStyles(page1Dom);
        applyMarkdownStyles(page2Dom);

        return [page1Dom, page2Dom];
    } catch (error) {
        console.error('分割报告内容失败:', error);
        return [reportDom];
    }
};

// 导出单页PDF
const exportSinglePagePDF = async (domElement, filename) => {
    try {
        // 创建深度克隆
        const clonedDom = domElement.cloneNode(true);

        // 设置克隆DOM的样式为不可见但保持结构
        clonedDom.style.position = 'absolute';
        clonedDom.style.left = '-9999px';
        clonedDom.style.top = '-9999px';
        clonedDom.style.opacity = '1';
        clonedDom.style.width = domElement.offsetWidth + 'px';
        clonedDom.style.backgroundColor = '#ffffff';
        clonedDom.style.padding = '20px';
        clonedDom.style.boxSizing = 'border-box';

        // 应用样式
        applyMarkdownStyles(clonedDom);

        // 将克隆DOM临时附加到body
        document.body.appendChild(clonedDom);

        try {
            console.log('开始导出单页PDF...');

            // 使用处理后的DOM调用exportElementToPDF
            const success = await exportElementToPDF(clonedDom, filename, {
                orientation: 'portrait',
                format: 'a4',
                margin: { top: 15, right: 15, bottom: 15, left: 15 },
                scale: 2, // 提高分辨率
                quality: 0.95 // 提高质量
            });

            return success;
        } finally {
            // 无论成功与否，都从文档中移除临时DOM
            if (clonedDom && clonedDom.parentNode) {
                clonedDom.parentNode.removeChild(clonedDom);
            }
        }
    } catch (error) {
        console.error('导出单页PDF失败:', error);
        throw error;
    }
};

// 导出多页PDF
const exportMultiPagePDF = async (domElements, filename) => {
    try {
        // 所有页面添加到body并应用样式
        const clonedElements = [];

        for (let i = 0; i < domElements.length; i++) {
            const element = domElements[i];
            // 创建深度克隆
            const clonedDom = element.cloneNode(true);

            // 设置克隆DOM的样式为不可见但保持结构
            clonedDom.style.position = 'absolute';
            clonedDom.style.left = '-9999px';
            clonedDom.style.top = '-9999px';
            clonedDom.style.opacity = '1';
            clonedDom.style.width = '800px'; // 固定宽度以确保一致性
            clonedDom.style.backgroundColor = '#ffffff';
            clonedDom.style.padding = '20px';
            clonedDom.style.boxSizing = 'border-box';

            // 更新页脚中的页码（如果有）
            const footerPageNumber = clonedDom.querySelector('.page-footer-content');
            if (footerPageNumber) {
                const pageNumberText = footerPageNumber.querySelector('div[style*="text-align: right"]');
                if (pageNumberText) {
                    pageNumberText.textContent = `第 ${i + 1} 页 / 共 ${domElements.length} 页`;
                }
            }

            // 应用样式
            applyMarkdownStyles(clonedDom);

            // 将克隆DOM临时附加到body
            document.body.appendChild(clonedDom);
            clonedElements.push(clonedDom);
        }

        try {
            console.log('开始导出多页PDF...');

            // 使用PDF工具导出多页PDF
            const success = await exportMultipleElementsToPDF(clonedElements, filename, {
                orientation: 'portrait',
                format: 'a4',
                margin: { top: 15, right: 15, bottom: 15, left: 15 },
                scale: 2, // 提高分辨率
                quality: 0.95, // 提高质量
                fitToPage: true // 确保内容适应页面宽度
            });

            return success;
        } finally {
            // 无论成功与否，都从文档中移除临时DOM
            for (const clonedDom of clonedElements) {
                if (clonedDom && clonedDom.parentNode) {
                    clonedDom.parentNode.removeChild(clonedDom);
                }
            }
        }
    } catch (error) {
        console.error('导出多页PDF失败:', error);
        throw error;
    }
};

// 应用Markdown样式到DOM元素
const applyMarkdownStyles = (dom) => {
    // 处理标题样式
    const headings = dom.querySelectorAll('.markdown-body h1, .markdown-body h2, .markdown-body h3');
    headings.forEach(heading => {
        heading.style.fontFamily = 'AlimamaShuHeiTi-Bold, sans-serif';

        if (heading.tagName === 'H1') {
            heading.style.fontSize = '2em';
            heading.style.marginBottom = '0.5em';
            heading.style.paddingBottom = '0.3em';
            heading.style.borderBottom = '1px solid #2b0606';
            heading.style.color = '#2b0606';
            heading.style.textAlign = 'center';
        } else if (heading.tagName === 'H2') {
            heading.style.fontSize = '1.5em';
            heading.style.marginTop = '1em';
            heading.style.marginBottom = '0.5em';
            heading.style.paddingBottom = '0.3em';
            heading.style.borderBottom = '1px solid #2b0606';
            heading.style.color = '#2b0606';
            heading.style.textAlign = 'left';
        } else if (heading.tagName === 'H3') {
            heading.style.fontSize = '1.25em';
            heading.style.marginTop = '1em';
            heading.style.marginBottom = '0.5em';
            heading.style.color = '#2b0606';
            heading.style.textAlign = 'left';
        }
    });

    // 处理表格样式
    const tables = dom.querySelectorAll('.markdown-body table');
    tables.forEach(table => {
        table.style.borderCollapse = 'collapse';
        table.style.width = '100%';
        table.style.margin = '1em 0';
        table.style.borderRadius = '0';
        table.style.overflow = 'hidden';
        table.style.boxShadow = 'none';
        table.style.border = '1px solid #d5c3a8';
        table.style.fontSize = '14px';

        const cells = table.querySelectorAll('th, td');
        cells.forEach(cell => {
            cell.style.padding = '12px 15px';
            cell.style.border = '1px solid #d5c3a8';
            cell.style.textAlign = 'center';
            cell.style.verticalAlign = 'middle';
            cell.style.backgroundColor = '#f9f1e4';
            cell.style.color = '#2b0606';
        });

        const headerCells = table.querySelectorAll('th');
        headerCells.forEach(th => {
            th.style.fontWeight = '600';
            th.style.backgroundColor = '#f5e6d0';
            th.style.color = '#2b0606';
        });

        const firstRowCells = table.querySelector('tr:first-child')?.querySelectorAll('td');
        if (firstRowCells) {
            firstRowCells.forEach(td => {
                td.style.backgroundColor = '#f5e6d0';
                td.style.fontWeight = '600';
            });
        }

        const firstColCells = table.querySelectorAll('td:first-child');
        firstColCells.forEach(td => {
            td.style.textAlign = 'left';
            td.style.backgroundColor = '#f5e6d0';
            td.style.fontWeight = '500';
        });
    });

    // 处理段落样式
    const paragraphs = dom.querySelectorAll('.markdown-body p');
    paragraphs.forEach(p => {
        p.style.lineHeight = '2';
    });

    // 处理强调样式
    const strongs = dom.querySelectorAll('.markdown-body strong');
    strongs.forEach(strong => {
        strong.style.fontWeight = '600';
    });

    // 处理产品表格特殊样式
    const productTables = dom.querySelectorAll('.markdown-body .product-table');
    productTables.forEach(table => {
        table.style.border = 'none';
        table.style.backgroundColor = 'transparent';

        const cells = table.querySelectorAll('td');
        cells.forEach(td => {
            td.style.backgroundColor = '#f9f1e4';
            td.style.color = '#2b0606';
            td.style.borderColor = '#d5c3a8';
        });

        const firstRowCells = table.querySelector('tr:first-child')?.querySelectorAll('td');
        if (firstRowCells) {
            firstRowCells.forEach(td => {
                td.style.backgroundColor = '#f5e6d0';
            });
        }
    });

    // 处理引用样式
    const blockquotes = dom.querySelectorAll('.markdown-body blockquote');
    blockquotes.forEach(blockquote => {
        const paragraphs = blockquote.querySelectorAll('p');
        paragraphs.forEach(p => {
            p.style.color = '#919399';
            p.style.marginBottom = '16px';
            p.style.lineHeight = '2';
            p.style.fontSize = '14px';

            // 添加星号前缀
            if (!p.getAttribute('data-styled')) {
                p.setAttribute('data-styled', 'true');
                p.style.position = 'relative';
                p.style.paddingLeft = '16px';

                // 使用伪元素技巧，直接在内容前添加星号
                p.innerHTML = '* ' + p.innerHTML;
            }
        });
    });

    // 查找所有id为no-pdf的元素
    const noPdfElements = dom.querySelectorAll('[id="no-pdf"]');

    // 移除找到的所有元素
    noPdfElements.forEach(element => {
        if (element && element.parentNode) {
            element.parentNode.removeChild(element);
        }
    });

    // 显示所有pdf-only类的元素
    const pdfOnlyElements = dom.querySelectorAll('.pdf-only');
    pdfOnlyElements.forEach(element => {
        if (element) {
            element.style.display = 'block';
        }
    });

    return dom;
};
// 完成分析
const completeAnalysis = () => {
    message.success('需求分析已完成！');
    resetAnalysis();
};

// 添加调试方法
const debugRerender = () => {
    if (aiAnalysisReport.value && aiAnalysisReport.value.debugRender) {
        const success = aiAnalysisReport.value.debugRender();
        if (success) {
            // 重新获取渲染后的报告
            setTimeout(() => {
                reportContent.value = aiAnalysisReport.value.renderedReport || '';
                message.success('报告已重新渲染');
            }, 100);
        } else {
            message.error('重新渲染失败');
        }
    } else {
        console.error('调试功能不可用');
        message.error('调试功能不可用');
    }
};

const debugShowRaw = () => {
    if (aiAnalysisReport.value && aiAnalysisReport.value.report) {
        // 显示原始报告内容
        const rawContent = aiAnalysisReport.value.report;
        console.log('====== 原始报告内容 ======');
        console.log(rawContent);

        // 创建一个临时的模态框显示原始内容
        message.info('原始报告内容已在控制台输出');
    } else {
        message.error('原始报告内容不可用');
    }
};

// 初始加载客户列表
getCustomerList();

// 添加调试功能的变量
const showSource = ref(false);
const sourceContent = ref('');

// 强制重新渲染内容
const forceRerender = () => {
    if (!aiAnalysisReport.value || !aiAnalysisReport.value.report) {
        message.warning('没有可渲染的报告内容');
        return;
    }

    try {
        // 重新渲染原始报告
        const rendered = renderMarkdown(aiAnalysisReport.value.report);
        // 强制刷新
        reportContent.value = forceRefreshRendering(rendered);
        message.success('已重新渲染报告内容');
    } catch (error) {
        console.error('重新渲染失败:', error);
        message.error('重新渲染失败: ' + error.message);
    }
};

// 切换源码/渲染视图
const toggleSourceView = () => {
    showSource.value = !showSource.value;

    if (showSource.value) {
        // 显示源码
        if (aiAnalysisReport.value && aiAnalysisReport.value.report) {
            sourceContent.value = aiAnalysisReport.value.report;
        } else if (reportContent.value) {
            sourceContent.value = reportContent.value;
        } else {
            sourceContent.value = '没有可用的内容';
        }
    }
};

// 检查渲染状态
const checkRenderingState = () => {
    const markdownBody = document.querySelector('.markdown-body');
    if (!markdownBody) {
        message.warning('找不到渲染容器');
        return;
    }

    // 获取计算样式
    const styles = window.getComputedStyle(markdownBody);
    const fontFamily = styles.getPropertyValue('font-family');
    const fontSize = styles.getPropertyValue('font-size');

    // 检查内部元素
    const headings = markdownBody.querySelectorAll('h1, h2, h3');
    const tables = markdownBody.querySelectorAll('table');
    const paragraphs = markdownBody.querySelectorAll('p');

    // 输出诊断信息
    console.log('渲染容器诊断:');
    console.log('- 字体:', fontFamily);
    console.log('- 字号:', fontSize);
    console.log('- 标题数量:', headings.length);
    console.log('- 表格数量:', tables.length);
    console.log('- 段落数量:', paragraphs.length);

    message.info(`检测到 ${headings.length} 个标题，${tables.length} 个表格，${paragraphs.length} 个段落。详情见控制台`);
};

// 导出成图片
const exportImage = async () => {
    if (!reportContent.value) {
        message.warning('没有可导出的数据');
        return;
    }

    try {
        // 显示加载提示
        const loadingKey = 'image-loading';
        message.loading({ content: '正在生成图片，请稍候...', key: loadingKey, duration: 0 });

        // 获取原始DOM元素
        const reportDom = document.getElementById('report-content');
        if (!reportDom) {
            message.error({ content: '找不到报告内容元素', key: loadingKey });
            return;
        }

        // 创建深度克隆用于导出
        const clonedDom = reportDom.cloneNode(true);

        // 设置克隆DOM的样式为不可见但保持结构
        clonedDom.style.position = 'absolute';
        clonedDom.style.left = '-9999px';
        clonedDom.style.top = '-9999px';
        clonedDom.style.width = '800px'; // 固定宽度
        clonedDom.style.backgroundColor = '#ffffff';
        clonedDom.style.padding = '20px';
        clonedDom.style.boxSizing = 'border-box';

        // 移除PDF专用页脚
        const pdfFooter = clonedDom.querySelector('.pdf-only');
        if (pdfFooter) {
            pdfFooter.style.display = 'none';
        }

        // 手动应用样式到克隆元素
        applyMarkdownStyles(clonedDom);

        // 将克隆DOM临时附加到body
        document.body.appendChild(clonedDom);

        try {
            // 使用html2canvas将DOM转换为canvas
            const canvas = await html2canvas(clonedDom, {
                scale: 2, // 提高分辨率
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff',
                logging: false,
                onclone: (documentClone) => {
                    // 获取克隆后的内容元素
                    const clonedContent = documentClone.getElementById('report-content');
                    if (clonedContent) {
                        // 确保应用所有样式
                        applyInlineStylesToElement(clonedContent);
                    }
                }
            });

            // 转换为图片并下载
            const imgData = canvas.toDataURL('image/png', 1.0);

            // 创建下载链接
            const downloadLink = document.createElement('a');
            downloadLink.href = imgData;
            downloadLink.download = `保险需求分析报告_${formData.name || '客户'}_${new Date().getTime()}.png`;

            // 触发下载
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);

            message.success({ content: '导出图片成功', key: loadingKey, duration: 2 });
        } finally {
            // 无论成功与否，都从文档中移除临时DOM
            if (clonedDom && clonedDom.parentNode) {
                clonedDom.parentNode.removeChild(clonedDom);
            }
        }
    } catch (error) {
        console.error('导出图片失败:', error);
        message.error(`导出图片失败: ${error.message || '请稍后重试'}`);
    }
};

// 将计算样式内联应用到元素及其子元素
const applyInlineStylesToElement = (element) => {
    if (!element) return;

    // 处理Markdown内容样式
    const markdownBody = element.querySelector('.markdown-body');
    if (markdownBody) {
        // 设置基本样式
        markdownBody.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif';
        markdownBody.style.fontSize = '16px';
        markdownBody.style.lineHeight = '1.6';
        markdownBody.style.wordWrap = 'break-word';
        markdownBody.style.color = '#2b0606';
        markdownBody.style.maxWidth = 'none';

        // 处理标题样式
        const headings = markdownBody.querySelectorAll('h1, h2, h3');
        headings.forEach(heading => {
            heading.style.fontFamily = 'AlimamaShuHeiTi-Bold, sans-serif';

            if (heading.tagName === 'H1') {
                heading.style.fontSize = '2em';
                heading.style.marginBottom = '0.5em';
                heading.style.paddingBottom = '0.3em';
                heading.style.borderBottom = '1px solid #2b0606';
                heading.style.color = '#2b0606';
                heading.style.textAlign = 'center';
            } else if (heading.tagName === 'H2') {
                heading.style.fontSize = '1.5em';
                heading.style.marginTop = '1em';
                heading.style.marginBottom = '0.5em';
                heading.style.paddingBottom = '0.3em';
                heading.style.borderBottom = '1px solid #2b0606';
                heading.style.color = '#2b0606';
                heading.style.textAlign = 'left';
            } else if (heading.tagName === 'H3') {
                heading.style.fontSize = '1.25em';
                heading.style.marginTop = '1em';
                heading.style.marginBottom = '0.5em';
                heading.style.color = '#2b0606';
                heading.style.textAlign = 'left';
            }
        });

        // 处理表格样式
        const tables = markdownBody.querySelectorAll('table');
        tables.forEach(table => {
            table.style.borderCollapse = 'collapse';
            table.style.width = '100%';
            table.style.margin = '1em 0';
            table.style.borderRadius = '0';
            table.style.overflow = 'hidden';
            table.style.boxShadow = 'none';
            table.style.border = '1px solid #d5c3a8';
            table.style.fontSize = '14px';

            const cells = table.querySelectorAll('th, td');
            cells.forEach(cell => {
                cell.style.padding = '12px 15px';
                cell.style.border = '1px solid #d5c3a8';
                cell.style.textAlign = 'center';
                cell.style.verticalAlign = 'middle';
                cell.style.backgroundColor = '#f9f1e4';
                cell.style.color = '#2b0606';
            });

            const headerCells = table.querySelectorAll('th');
            headerCells.forEach(th => {
                th.style.fontWeight = '600';
                th.style.backgroundColor = '#f5e6d0';
                th.style.color = '#2b0606';
            });

            const firstRowCells = table.querySelector('tr:first-child')?.querySelectorAll('td');
            if (firstRowCells) {
                firstRowCells.forEach(td => {
                    td.style.backgroundColor = '#f5e6d0';
                    td.style.fontWeight = '600';
                });
            }

            const firstColCells = table.querySelectorAll('td:first-child');
            firstColCells.forEach(td => {
                td.style.textAlign = 'left';
                td.style.backgroundColor = '#f5e6d0';
                td.style.fontWeight = '500';
            });
        });

        // 处理段落样式
        const paragraphs = markdownBody.querySelectorAll('p');
        paragraphs.forEach(p => {
            p.style.lineHeight = '2';
            p.style.marginBottom = '1em';
            p.style.color = '#2b0606';
        });

        // 处理强调样式
        const strongs = markdownBody.querySelectorAll('strong');
        strongs.forEach(strong => {
            strong.style.fontWeight = '600';
            strong.style.color = '#2b0606';
        });

        // 处理产品表格特殊样式
        const productTables = markdownBody.querySelectorAll('.product-table');
        productTables.forEach(table => {
            table.style.border = 'none';
            table.style.backgroundColor = 'transparent';

            const cells = table.querySelectorAll('td');
            cells.forEach(td => {
                td.style.backgroundColor = '#f9f1e4';
                td.style.color = '#2b0606';
                td.style.borderColor = '#d5c3a8';
            });

            const firstRowCells = table.querySelector('tr:first-child')?.querySelectorAll('td');
            if (firstRowCells) {
                firstRowCells.forEach(td => {
                    td.style.backgroundColor = '#f5e6d0';
                });
            }
        });

        // 处理列表样式
        const lists = markdownBody.querySelectorAll('ul, ol');
        lists.forEach(list => {
            list.style.paddingLeft = '2em';
            list.style.marginBottom = '1em';

            const items = list.querySelectorAll('li');
            items.forEach(item => {
                item.style.marginBottom = '0.5em';
                item.style.color = '#2b0606';
            });
        });

        // 处理链接样式
        const links = markdownBody.querySelectorAll('a');
        links.forEach(link => {
            link.style.color = '#0366d6';
            link.style.textDecoration = 'none';
        });

        // 添加页面标题
        const pageTitle = document.createElement('h1');
        pageTitle.textContent = '保险需求分析报告';
        pageTitle.style.textAlign = 'center';
        pageTitle.style.fontSize = '24px';
        pageTitle.style.marginBottom = '20px';
        pageTitle.style.fontFamily = 'AlimamaShuHeiTi-Bold, sans-serif';
        pageTitle.style.color = '#2b0606';

        // 添加页面客户信息
        const infoDiv = document.createElement('div');
        infoDiv.style.display = 'flex';
        infoDiv.style.alignItems = 'center';
        infoDiv.style.marginBottom = '15px';

        const leftInfo = document.createElement('div');
        leftInfo.style.flex = '1';
        leftInfo.style.textAlign = 'left';
        leftInfo.style.fontSize = '14px';
        leftInfo.style.color = '#666';

        const clientNameDiv = document.createElement('div');
        clientNameDiv.textContent = `客户: ${formData.name || '未知客户'}`;
        const genderDiv = document.createElement('div');
        genderDiv.textContent = `性别: ${formData.gender === 1 ? '男' : formData.gender === 2 ? '女' : '未知'}`;

        leftInfo.appendChild(clientNameDiv);
        leftInfo.appendChild(genderDiv);

        const rightInfo = document.createElement('div');
        rightInfo.style.flex = '1';
        rightInfo.style.textAlign = 'right';
        rightInfo.style.fontSize = '14px';
        rightInfo.style.color = '#666';

        const dateDiv = document.createElement('div');
        dateDiv.textContent = `生成日期: ${new Date().toLocaleDateString()}`;
        rightInfo.appendChild(dateDiv);

        infoDiv.appendChild(leftInfo);
        infoDiv.appendChild(rightInfo);

        const hr = document.createElement('hr');
        hr.style.border = '0';
        hr.style.height = '1px';
        hr.style.backgroundColor = '#eaeaea';
        hr.style.margin = '15px 0 25px 0';

        // 将标题和信息插入到内容前面
        markdownBody.insertBefore(hr, markdownBody.firstChild);
        markdownBody.insertBefore(infoDiv, markdownBody.firstChild);
        markdownBody.insertBefore(pageTitle, markdownBody.firstChild);
    }
};
</script>

<style scoped>
.page-title {
    color: #fff;
}


.needs-analysis-container {
    padding: 24px;
    max-width: 1200px;
    margin: 0 auto;

    h1 {
        margin-bottom: 0px;
        display: flex;
        align-items: center;
        justify-content: center;

    }

    /* PDF导出相关样式 */
    .pdf-only {
        display: none;
        /* 在正常视图中隐藏 */
    }

    @media print {
        .pdf-only {
            display: block;
            /* 在打印视图中显示 */
        }
    }

    /* Markdown渲染样式 - 使用深度选择器确保样式被应用 */
    :deep(.markdown-body) {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif !important;
        font-size: 16px !important;
        line-height: 1.6 !important;
        word-wrap: break-word !important;
        color: #2b0606 !important;
    }

    :deep(.markdown-body h1) {
        font-size: 2em !important;
        margin-bottom: 0.5em !important;
        padding-bottom: 0.3em !important;
        border-bottom: 1px solid #2b0606 !important;
        color: #2b0606 !important;
        text-align: center !important;
        font-family: AlimamaShuHeiTi-Bold, sans-serif !important;
    }

    :deep(.markdown-body h2) {
        font-size: 1.5em !important;
        margin-top: 1em !important;
        margin-bottom: 0.5em !important;
        padding-bottom: 0.3em !important;
        border-bottom: 1px solid #2b0606 !important;
        color: #2b0606 !important;
        text-align: left !important;
        font-family: AlimamaShuHeiTi-Bold, sans-serif !important;
    }

    :deep(.markdown-body h3) {
        font-size: 1.25em !important;
        margin-top: 1em !important;
        margin-bottom: 0.5em !important;
        color: #2b0606 !important;
        text-align: left !important;
        font-family: AlimamaShuHeiTi-Bold, sans-serif !important;
    }

    :deep(.markdown-body table) {
        border-collapse: collapse !important;
        width: 100% !important;
        margin: 1em 0 !important;
        border-radius: 0 !important;
        overflow: hidden !important;
        box-shadow: none !important;
        border: 1px solid #d5c3a8 !important;
        font-size: 14px !important;
    }

    :deep(.markdown-body table th),
    :deep(.markdown-body table td) {
        padding: 12px 15px !important;
        border: 1px solid #d5c3a8 !important;
        text-align: center !important;
        vertical-align: middle !important;
        background-color: #f9f1e4 !important;
        /* 浅棕色/米色背景 */
        color: #2b0606 !important;
    }

    :deep(.markdown-body table th) {
        font-weight: 600 !important;
        background-color: #f5e6d0 !important;
        color: #2b0606 !important;
    }

    :deep(.markdown-body table tr:first-child td) {
        background-color: #f5e6d0 !important;
        font-weight: 600 !important;
    }

    :deep(.markdown-body table td:first-child) {
        text-align: left !important;
        background-color: #f5e6d0 !important;
        font-weight: 500 !important;
    }

    :deep(.markdown-body table tr:nth-child(2n)) {
        background-color: #f9f1e4 !important;
    }

    :deep(.markdown-body table tr:hover td) {
        background-color: #f0e6d6 !important;
    }

    :deep(.markdown-body p) {
        line-height: 2 !important;
        margin-bottom: 1em !important;
        color: #2b0606 !important;
    }

    :deep(.markdown-body strong) {
        font-weight: 600 !important;
        color: #2b0606 !important;
    }

    /* 添加产品表格特殊样式 */
    :deep(.markdown-body .product-table) {
        border: none !important;
        background-color: transparent !important;
    }

    :deep(.markdown-body .product-table td) {
        background-color: #f9f1e4 !important;
        color: #2b0606 !important;
        border-color: #d5c3a8 !important;
    }

    :deep(.markdown-body .product-table tr:first-child td) {
        background-color: #f5e6d0 !important;
    }


    :deep(.markdown-body blockquote p) {
        color: #919399 !important;
        margin-bottom: 16px !important;
        line-height: 2 !important;
        font-size: 14px !important;
    }

    :deep(.markdown-body blockquote p::before) {
        content: '*' !important;
        color: #919399 !important;
        font-style: normal !important;
        font-size: 14px !important;
        font-weight: 600 !important;
    }

    /* 修复可能的特殊元素样式 */
    :deep(.markdown-body ul),
    :deep(.markdown-body ol) {
        padding-left: 2em !important;
        margin-bottom: 1em !important;
    }

    :deep(.markdown-body li) {
        margin-bottom: 0.5em !important;
        color: #2b0606 !important;
    }

    :deep(.markdown-body code) {
        background-color: #f6f8fa !important;
        padding: 0.2em 0.4em !important;
        border-radius: 3px !important;
        font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace !important;
    }

    :deep(.markdown-body pre) {
        background-color: #f6f8fa !important;
        padding: 16px !important;
        border-radius: 3px !important;
        overflow: auto !important;
    }

    :deep(.markdown-body pre code) {
        background-color: transparent !important;
        padding: 0 !important;
    }

    :deep(.markdown-body a) {
        color: #0366d6 !important;
        text-decoration: none !important;
    }

    :deep(.markdown-body a:hover) {
        text-decoration: underline !important;
    }

    /* 确保最大宽度不受限制 */
    :deep(.prose.max-w-none) {
        max-width: none !important;
    }

    .customer-selection {
        margin-bottom: 24px;

        h2 {
            margin-bottom: 16px;
        }
    }

    .analysis-steps {
        .steps-nav {
            margin-bottom: 40px;
        }

        .step-content {
            padding: 24px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

            h2 {
                margin-bottom: 8px;
                color: #1a1a1a;
            }

            .step-description {
                color: #666;
                margin-bottom: 24px;
            }

            .info-card {
                padding: 24px;
                background: #f9f9f9;
                border-radius: 8px;
                margin-bottom: 24px;

                .info-item {
                    display: flex;
                    margin-bottom: 16px;
                    align-items: center;

                    .label {
                        width: 60px;
                        color: #666;
                    }

                    .value {
                        flex: 1;
                        font-weight: 500;
                    }
                }
            }

            .needs-cards {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 16px;
                margin-bottom: 24px;

                .need-card {
                    display: flex;
                    align-items: center;
                    padding: 16px;
                    background: #f9f9f9;
                    border-radius: 8px;
                    border: 2px solid transparent;
                    transition: all 0.3s ease;
                    cursor: pointer;

                    &:hover {
                        background: #f0f0f0;
                    }

                    &.selected {
                        border-color: #1890ff;
                        background: #e6f7ff;
                    }

                    .need-icon {
                        font-size: 24px;
                        margin-right: 16px;
                        width: 40px;
                        height: 40px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }

                    .need-content {
                        flex: 1;

                        .need-title {
                            font-weight: 500;
                            margin-bottom: 4px;
                        }

                        .need-desc {
                            font-size: 12px;
                            color: #666;
                        }
                    }

                    .need-check {
                        margin-left: 8px;
                    }
                }
            }

            .selection-counter {
                text-align: right;
                color: #666;
                margin-bottom: 24px;
                font-size: 14px;
            }

            .survey-container {
                margin-bottom: 24px;

                .survey-section {
                    margin-bottom: 32px;

                    .survey-question {
                        margin-bottom: 24px;

                        h3 {
                            margin-bottom: 12px;
                            font-weight: 500;
                            font-size: 16px;
                        }
                    }

                    .sensitive-data-section {
                        .sensitive-data-card {
                            background: #f6f6f6;
                            border-radius: 8px;
                            padding: 16px;
                            border-left: 4px solid #1890ff;

                            h3 {
                                color: #1890ff;
                            }

                            ul {
                                padding-left: 20px;
                                margin: 12px 0;
                            }

                            .note {
                                font-size: 12px;
                                color: #999;
                                font-style: italic;
                                margin-bottom: 16px;
                            }

                            .sensitive-actions {
                                display: flex;
                                gap: 12px;
                            }

                            .data-provided {
                                display: flex;
                                align-items: center;
                                gap: 8px;
                            }
                        }
                    }
                }
            }

            .survey-progress {
                margin-bottom: 24px;
                display: flex;
                flex-direction: column;
                gap: 8px;

                span {
                    text-align: right;
                    font-size: 12px;
                    color: #666;
                }
            }

            .privacy-statement {
                margin-top: 16px;
                padding-top: 16px;
                border-top: 1px solid #eee;
                font-size: 12px;
                color: #999;
            }

            .analysis-loading {
                margin: 20px 0;

                .analysis-status {
                    margin-bottom: 20px;

                    .status-item {
                        margin-bottom: 12px;
                        display: flex;
                        align-items: center;
                        gap: 8px;
                    }
                }

                .ai-thinking {
                    margin-top: 16px;
                    min-height: 60px;
                    padding: 12px;
                    background: #f9f9f9;
                    border-radius: 8px;
                    font-style: italic;
                    color: #666;
                }
            }

            .analysis-completed {
                text-align: center;
                padding: 40px 0;

                p {
                    margin-bottom: 24px;
                    font-size: 18px;
                    color: #1890ff;
                }
            }

            .recommendation-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 24px;

                .match-score {
                    display: flex;
                    flex-direction: column;
                    align-items: center;

                    .score-label {
                        font-size: 12px;
                        color: #666;
                    }

                    .score-value {
                        font-size: 24px;
                        font-weight: bold;
                        color: #1890ff;
                    }
                }
            }

            .recommendation-chart {
                margin-bottom: 32px;
                background: #f9f9f9;
                padding: 16px;
                border-radius: 8px;

                .chart-title {
                    margin-bottom: 16px;
                    font-weight: 500;
                }

                .insurance-match-chart {
                    .match-bar-item {
                        margin-bottom: 12px;

                        .category-name {
                            margin-bottom: 4px;
                            font-size: 14px;
                        }

                        .match-bar-container {
                            display: flex;
                            align-items: center;
                            height: 24px;

                            .match-bar {
                                height: 100%;
                                border-radius: 4px;

                                &.high {
                                    background: linear-gradient(to right, #52c41a, #1890ff);
                                }

                                &.medium {
                                    background: linear-gradient(to right, #faad14, #52c41a);
                                }

                                &.low {
                                    background: linear-gradient(to right, #f5222d, #faad14);
                                }
                            }

                            .match-score {
                                margin-left: 8px;
                                font-size: 14px;
                                color: #666;
                            }
                        }
                    }
                }
            }

            .top-recommendation {
                margin-bottom: 32px;
                padding: 20px;
                border: 1px solid #e8e8e8;
                border-radius: 8px;

                .recommendation-title {
                    font-size: 18px;
                    font-weight: 500;
                    margin-bottom: 16px;
                    display: flex;
                    align-items: center;

                    .icon {
                        margin-right: 8px;
                    }
                }

                .product-details {
                    margin-bottom: 24px;

                    .detail-item {
                        display: flex;
                        margin-bottom: 12px;

                        .detail-label {
                            width: 80px;
                            color: #666;
                        }

                        .detail-value {
                            flex: 1;
                        }
                    }
                }

                .recommendation-actions {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 12px;
                }
            }

            .ai-report-content {
                max-height: 60vh;
                overflow-y: auto;
                padding: 0 16px;
            }

            .report-actions {
                margin-top: 24px;
                text-align: right;
            }

            .final-actions {
                margin-top: 40px;
                border-top: 1px solid #e8e8e8;
                padding-top: 24px;
            }

            .step-actions {
                margin-top: 24px;
                display: flex;
                justify-content: space-between;
            }

            .step-placeholder {
                min-height: 300px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                color: #999;
            }
        }
    }
}

/* 表格相关样式 */
:deep(.table-striped) {
    background-color: #f9f9f9;
}

:deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
    background-color: #e6f7ff;
}

/* 筛选器动画 */
.filter-section {
    transition: all 0.3s ease;
}

.filter-section:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* 选择器样式覆盖 */
:deep(.ant-select-selector) {
    border-radius: 6px !important;
}

/* 输入框样式覆盖 */
:deep(.ant-input-affix-wrapper) {
    border-radius: 6px !important;
}

/* 按钮样式覆盖 */
:deep(.ant-btn) {
    border-radius: 6px !important;
}
</style>