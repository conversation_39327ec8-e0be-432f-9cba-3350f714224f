<template>
    <div class="ai-analysis-report">
        <!-- 分析状态显示 -->
        <div class="analysis-loading" v-if="!completed">
            <div class="analysis-status bg-white p-6 rounded-lg shadow-sm">
                <div class="status-item">
                    <a-tag color="success" v-if="analyzing">✓</a-tag>
                    <a-tag color="processing" v-else>...</a-tag>
                    <span class="text-gray-700">正在分析用户需求</span>
                </div>
                <div class="status-item">
                    <a-tag color="success" v-if="matchingProducts">✓</a-tag>
                    <a-tag color="processing" v-else>...</a-tag>
                    <span class="text-gray-700">匹配产品信息</span>
                </div>
                <div class="status-item">
                    <a-tag color="success" v-if="generatingReport">✓</a-tag>
                    <a-tag color="processing" v-else>...</a-tag>
                    <span class="text-gray-700">生成分析报告</span>
                </div>
            </div>

            <div class="mt-6">
                <a-progress :percent="progress" status="active" :stroke-color="{ from: '#108ee9', to: '#87d068' }" />
            </div>

            <div class="ai-thinking bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg border border-blue-100 mt-6"
                v-if="thinking">
                <div class="flex items-center">
                    <Icon icon="mdi:robot" class="text-blue-500 mr-2 text-xl" />
                    <p class="text-gray-700 italic">{{ thinking }}</p>

                </div>
            </div>
            <div v-else class="flex space-x-1">
                <div class="w-2 h-2 bg-blue-400 rounded-full animate-bounce"></div>
                <div class="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style="animation-delay: 0.1s">
                </div>
                <div class="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style="animation-delay: 0.2s">
                </div>
            </div>
        </div>

        <!-- 分析完成后的报告内容 -->
        <div v-if="completed" class="report-content bg-white rounded-lg shadow-md p-6 ">
            <div class="mb-4 pb-4 ">
                <h3 class="text-xl font-medium text-gray-800 flex items-center">
                    <Icon icon="mdi:file-document-check" class="mr-2 text-blue-500" />
                    分析报告摘要
                </h3>
                <p class="text-gray-600 mt-2">根据您的需求和个人信息，AI已为您生成专属保险分析报告</p>
            </div>
            <!-- 使用v-html指令渲染已处理的HTML内容 -->
            <div v-if="renderedReport" v-html="renderedReport" class="markdown-body"></div>
            <!-- 如果没有渲染内容但有原始报告，显示原始报告（用于调试） -->
            <div v-else-if="report" class="raw-report border-2 border-yellow-400 p-4 rounded bg-yellow-50">
                <div class="text-yellow-800 font-medium mb-2">原始Markdown报告（未渲染）:</div>
                <pre class="whitespace-pre-wrap text-sm">{{ report }}</pre>
            </div>
            <!-- 无内容情况 -->
            <div v-else class="no-report-message p-4 bg-gray-100 rounded text-center">
                <Icon icon="mdi:alert-circle-outline" class="text-gray-500 text-2xl mb-2" />
                <p class="text-gray-600">报告内容生成中，请稍候...</p>
            </div>
        </div>

    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import markdownit from 'markdown-it';
import { createSession, processQuery, createSessionWithPdf } from '@/api/chat';
import { Icon } from '@iconify/vue';

// 初始化markdown渲染器，简化配置与ai-chat-new.vue保持一致
const md = markdownit({ html: true, breaks: true });

// Props
const props = defineProps({
    formData: {
        type: Object,
        required: true
    }
});

// 状态
const completed = ref(false);
const generating = ref(false);
const analyzing = ref(false);
const matchingProducts = ref(false);
const generatingReport = ref(false);
const progress = ref(0);
const thinking = ref('');
const report = ref('');
const reportModalVisible = ref(false);
const sessionId = ref(null);

// 创建markdown渲染函数，与ai-chat-new.vue保持一致
const renderMarkdown = (content) => {
    if (!content) return ''; // 处理undefined或null的情况

    try {
        // 添加对标题格式的预处理
        // 处理特殊情况：以#开头但没有空格的标题，如"#退休收入补充保险需求分析报告"
        let processedContent = content;

        // 匹配以#开头但后面没有空格的标题（仅处理标题格式，不处理其他格式）
        // 例如: #退休收入补充保险需求分析报告 -> # 退休收入补充保险需求分析报告
        processedContent = processedContent.replace(/^(#{1,6})([^#\s])/gm, '$1 $2');

        // 处理行内的标题格式（如果有新行中的标题）
        processedContent = processedContent.replace(/\n(#{1,6})([^#\s])/gm, '\n$1 $2');

        // 使用markdown-it渲染内容并返回HTML
        return md.render(processedContent);
    } catch (e) {
        console.error('Markdown渲染错误:', e);
        return content || '';
    }
};

// 计算属性，使用简化的渲染方法
const renderedReport = computed(() => {
    if (!report.value) return '';
    try {
        // 直接使用renderMarkdown函数渲染
        console.log('AiAnalysisReport组件: 开始渲染报告, 原始长度:', report.value.length);
        const rendered = renderMarkdown(report.value);
        console.log('AiAnalysisReport组件: 渲染完成, HTML长度:', rendered.length);
        console.log('AiAnalysisReport组件: 渲染后是否包含HTML标签:', /<[a-z][\s\S]*>/i.test(rendered));

        // 添加时间戳来确保每次渲染都是唯一的，强制Vue更新视图
        return rendered + `<!-- refresh-timestamp: ${Date.now()} -->`;
    } catch (e) {
        console.error('AiAnalysisReport组件: Markdown渲染错误:', e);
        // 返回带有错误提示的HTML，而不是原始的markdown文本
        return `<div class="error-message">
            <h3 style="color: #f56c6c;">报告渲染失败</h3>
            <p>很抱歉，报告渲染过程中遇到了问题。</p>
            <p>错误信息: ${e.message || '未知错误'}</p>
        </div>`;
    }
});

// 初始化会话
const initSession = async () => {
    try {
        const sessionResponse = await createSession();

        sessionId.value = sessionResponse.sessionId;
        await createSessionWithPdf(sessionId.value, 327);
        console.log('会话创建成功:', sessionId.value);
        return true;
    } catch (error) {
        console.error('创建会话失败:', error);
        message.error('初始化AI分析失败，请重试');
        return false;
    }
};

// 生成分析报告
const generateReport = async () => {
    if (generating.value) return;

    generating.value = true;

    // 初始化会话
    if (!sessionId.value) {
        const sessionInitialized = await initSession();
        if (!sessionInitialized) {
            generating.value = false;
            return;
        }
    }

    // 开始分析流程
    analyzing.value = true;
    progress.value = 10;
    thinking.value = '正在分析用户需求和保障偏好...';

    // 构建提示词
    const prompt = buildPrompt();

    try {
        // 模拟进度
        const progressInterval = setInterval(() => {
            if (progress.value < 90) {
                progress.value += 5;
            }

            if (progress.value >= 30 && !matchingProducts.value) {
                matchingProducts.value = true;
                thinking.value = '正在匹配最适合的保险产品，考虑您的年龄、职业和健康状况...';
            }

            if (progress.value >= 60 && !generatingReport.value) {
                generatingReport.value = true;
                thinking.value = '正在生成个性化分析报告，包含产品概况、亮点和案例演示...';
            }
            if (progress.value >= 90 && !completed.value) {
                thinking.value = '分析报告生成中...';
            }
        }, 300);

        // 调用AI接口
        await processQuery(
            sessionId.value,
            prompt,
            // onToken回调 - 处理流式数据
            (token) => {
                report.value += token;
            },
            // onComplete回调 - 流式传输完成
            (fullResponse) => {
                console.log('AI回复完成');
                report.value = fullResponse || '';
                clearInterval(progressInterval);
                progress.value = 100;
                completed.value = true;

                // 确保报告内容已被正确渲染
                console.log('检查最终渲染报告:', renderedReport.value?.length || 0);

                message.success('分析报告生成成功！');
            },
            // onError回调 - 处理错误
            (errorMessage) => {
                console.error('AI处理错误:', errorMessage);
                message.error('生成报告失败: ' + errorMessage);
                clearInterval(progressInterval);
            }
        );
    } catch (error) {
        console.error('生成报告失败:', error);
        message.error('生成报告失败，请重试');
    } finally {
        generating.value = false;
    }
};

// 构建提示词
const buildPrompt = () => {
    // 从formData中提取信息
    const { name, gender, birthday, needsPriority, scenarioAnswers, sensitiveData } = props.formData;

    // 计算年龄
    const age = calculateAge(birthday);

    // 提取需求优先级
    const needs = needsPriority.map(id => {
        switch (id) {
            case 'medical': return '大病医疗费用报销';
            case 'accident': return '意外受伤赔偿';
            case 'education': return '子女教育金储备';
            case 'retirement': return '退休后收入补充';
            case 'property': return '家庭财产保障';
            case 'parent': return '赡养父母责任';
            default: return id;
        }
    }).join('、');

    // 提取职业风险等级
    const careerRisk = scenarioAnswers.career ?
        ['低风险（办公室文职）', '中风险（户外作业）', '高风险（高空/危险作业）', '其他'][scenarioAnswers.career - 1] :
        '未知';

    // 健康状况
    const healthStatus = sensitiveData.healthProvided ?
        (sensitiveData.healthData.hospitalized === 1 ? '健康，无住院记录' :
            sensitiveData.healthData.hospitalized === 2 ? '有轻微健康问题' : '有重大健康问题') :
        '未提供健康数据';

    // 构建提示词
    return `
请根据以下用户信息生成一份保险需求分析报告。报告需要使用Markdown格式，并包含以下三个主要部分：产品概况、产品亮点和案例演示。

用户信息（不需要返回）：
- 姓名：${name}
- 性别：${gender === 1 ? '男' : '女'}
- 年龄：${age}岁
- 主要需求：${needs}
- 职业风险：${careerRisk}
- 健康状况：${healthStatus}

请生成一份完整的保险分析报告，包含以下三个模块：

1. 产品概况（必须包含以下信息）：
   - 保险公司
   - 产品名称
   - 保障期限
   - 缴费年期
   - 年缴保费（包括货币类型）

2. 产品亮点（至少4个亮点）

3. 案例演示，分为两个部分：
   - 演示用户信息：
     - 姓名
     - 年龄
     - 吸烟状态
     - 保费（包括货币类型）
     - 缴费方式
     - 缴费期限
   - 演示收益表格数据，至少包括以下列：
     - 保单年度
     - 用户年龄
     - 保证部分金额
     - 单利率
     - 复利率

请确保所有数据都以表格形式展示，并使用Markdown格式。报告应该是专业的，并且针对用户的具体情况提供个性化建议。
`;
};

// 计算年龄
const calculateAge = (birthday) => {
    if (!birthday) return '未知';
    const birthDate = new Date(birthday);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
        age--;
    }
    return age;
};

const reset = () => {
    completed.value = false;
    generating.value = false;
    analyzing.value = false;
    matchingProducts.value = false;
    generatingReport.value = false;
};
// 导出方法和属性
defineExpose({
    generateReport,
    completed,
    renderedReport,
    reset,
    report,  // 导出原始报告文本，以便在必要时手动渲染
    // 添加一个调试方法，用于手动重新渲染报告
    debugRender: () => {
        if (!report.value) {
            console.warn('没有报告内容可供渲染');
            return false;
        }

        try {
            console.log('====== 原始报告内容 ======');
            console.log(report.value.substring(0, 500) + '...');

            const rendered = renderMarkdown(report.value);
            console.log('====== 渲染后HTML内容长度 ======');
            console.log(rendered.length);

            // 强制重新计算renderedReport
            report.value = report.value + ' ';
            setTimeout(() => {
                report.value = report.value.trim();
            }, 10);

            return true;
        } catch (e) {
            console.error('调试渲染失败:', e);
            return false;
        }
    },

    // 添加一个新方法用于直接获取渲染好的HTML
    getRenderedHTML: () => {
        if (!report.value) return '';
        try {
            return renderMarkdown(report.value);
        } catch (e) {
            console.error('获取渲染HTML失败:', e);
            return '';
        }
    }
});
</script>

<style scoped>
.markdown-body>>>h1 {
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: AlimamaShuHeiTi-Bold, sans-serif !important;
    color: #2b0606;
    margin-bottom: 1.5rem;
}

/* 添加产品表格特殊样式 */
.markdown-body>>>.product-table {
    border: none !important;
    background-color: transparent !important;
}

.markdown-body>>>.product-table td {
    background-color: #f9f1e4 !important;
    color: #2b0606 !important;
    border-color: #d5c3a8 !important;
}

.markdown-body>>>.product-table tr:first-child td {
    background-color: #f5e6d0 !important;
}

.ai-analysis-report {
    margin: 20px 0;
}

.analysis-status {
    margin-bottom: 20px;
}

.status-item {
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.ai-thinking {
    margin-top: 16px;
    min-height: 60px;
    font-style: italic;
}

.report-actions {
    margin-top: 24px;
    display: flex;
    justify-content: center;
}

.report-content {
    margin: 20px 0;
    border: none !important;
}

/* 动画效果 */
@keyframes bounce {

    0%,
    80%,
    100% {
        transform: scale(0);
    }

    40% {
        transform: scale(1);
    }
}

.animate-bounce {
    animation: bounce 1.4s infinite ease-in-out both;
}

:deep(.markdown-body) {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
    font-size: 16px;
    line-height: 1.6;
    word-wrap: break-word;
}

:deep(.markdown-body h1) {
    font-size: 2em;
    margin-bottom: 0.5em;
    padding-bottom: 0.3em;
    border-bottom: 1px solid #eaecef;
    color: #2563eb;
}

:deep(.markdown-body h2) {
    font-size: 1.5em;
    margin-top: 1em;
    margin-bottom: 0.5em;
    padding-bottom: 0.3em;
    border-bottom: 1px solid #eaecef;
    color: #1e40af;
}

:deep(.markdown-body h3) {
    font-size: 1.25em;
    margin-top: 1em;
    margin-bottom: 0.5em;
    color: #374151;
}

:deep(.markdown-body table) {
    border-collapse: collapse;
    width: 100%;
    margin: 1em 0;
    border-radius: 0;
    overflow: hidden;
    box-shadow: none;
    border: 1px solid #d5c3a8;
    font-size: 14px;
}

:deep(.markdown-body table th),
:deep(.markdown-body table td) {
    padding: 12px 15px;
    border: 1px solid #d5c3a8;
    text-align: center;
    vertical-align: middle;
    background-color: #f9f1e4;
    /* 浅棕色/米色背景 */
    color: #2b0606;
}

:deep(.markdown-body table th) {
    font-weight: 600;
    background-color: #f5e6d0;
    color: #2b0606;
}

:deep(.markdown-body table tr:first-child td) {
    background-color: #f5e6d0;
    font-weight: 600;
}

:deep(.markdown-body table td:first-child) {
    text-align: left;
    background-color: #f5e6d0;
    font-weight: 500;
}

:deep(.markdown-body table tr:nth-child(2n)) {
    background-color: #f9f1e4;
}

:deep(.markdown-body table tr:hover td) {
    background-color: #f0e6d6;
}

:deep(.ai-report-content) {
    max-height: 60vh;
    overflow-y: auto;
    padding: 0 16px;
}

:deep(.ant-btn) {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

:deep(.ant-modal-content) {
    border-radius: 8px;
    overflow: hidden;
}

:deep(.ant-modal-header) {
    padding: 16px 24px;
    background: linear-gradient(to right, #3b82f6, #2563eb);
    border-bottom: none;
}

:deep(.ant-modal-title) {
    color: white;
    font-weight: 600;
}

:deep(.ant-modal-close) {
    color: rgba(255, 255, 255, 0.8);
}

:deep(.ant-modal-close:hover) {
    color: white;
}

:deep(.ant-modal-body) {
    padding: 24px;
}

:deep(.ant-modal-footer) {
    padding: 16px 24px;
    border-top: 1px solid #e5e7eb;
}
</style>