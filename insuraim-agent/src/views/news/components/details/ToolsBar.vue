<template>
    <div class="tools-bar">
        <div class="tools-wrapper" :class="{ 'expanded': isExpanded }">
            <!-- 工具按钮组 -->
            <transition-group name="fade" class="tools-container">
                <template v-if="isExpanded">
                    <!-- 回到顶部 -->
                    <button key="top" type="primary" class="tool-btn" @click="scrollToTop">
                        <Icon icon="material-symbols:arrow-upward" class="icon-btn" />
                    </button>
                    <!-- 问题反馈 -->
                    <button key="feedback" type="primary" class="tool-btn" @click="showFeedback">
                        <Icon icon="material-symbols:help" class="icon-btn" />
                    </button>
                    <!--  bug修复 -->
                    <!-- <button key="bug" type="primary" class="tool-btn" @click="showFeedback">
                        <Icon icon="material-symbols:bug-report" class="icon-btn" />
                    </button> -->
                </template>
            </transition-group>

            <!-- 展开/折叠按钮 -->
            <button type="primary" class="tool-btn main-btn" @click="toggleExpand">
                <Icon :icon="isExpanded ? 'material-symbols:close' : 'material-symbols:add'" class="icon-btn" />
            </button>
        </div>

        <!-- 反馈弹窗 -->
        <a-modal v-model:visible="feedbackVisible" title="问题反馈" @ok="handleFeedbackSubmit">
            <a-form :model="feedbackForm">
                <a-form-item label="反馈内容">
                    <a-textarea v-model:value="feedbackForm.content" :rows="4" placeholder="请描述您遇到的问题..." />
                </a-form-item>
                <!-- 反馈类型 -->
                <a-form-item label="反馈类型">
                    <a-select v-model:value="feedbackForm.type" placeholder="请选择反馈类型">
                        <a-select-option value="bug">Bug反馈</a-select-option>
                        <a-select-option value="feature">功能建议</a-select-option>
                        <a-select-option value="other">其他</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="联系方式">
                    <a-input v-model:value="feedbackForm.contact" placeholder="请留下您的联系方式（选填）" />
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>

<script setup>
import { ref } from 'vue';
import { message } from 'ant-design-vue';
import { Icon } from '@iconify/vue';

// 展开状态
const isExpanded = ref(false);

// 反馈表单
const feedbackVisible = ref(false);
const feedbackForm = ref({
    content: '',
    contact: ''
});

// 展开/折叠
const toggleExpand = () => {
    isExpanded.value = !isExpanded.value;
};

// 回到顶部
const scrollToTop = () => {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
};

// 显示反馈弹窗
const showFeedback = () => {
    feedbackVisible.value = true;
};

// 提交反馈
const handleFeedbackSubmit = () => {
    if (!feedbackForm.value.content.trim()) {
        message.warning('请填写反馈内容');
        return;
    }
    // TODO: 实际提交反馈的接口调用
    message.success('感谢您的反馈！');
    feedbackVisible.value = false;
    feedbackForm.value = {
        content: '',
        contact: ''
    };
};
</script>

<style scoped>
.tools-bar {
    position: fixed;
    bottom: 40px;
    right: 40px;
    z-index: 1000;
}

.tools-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
}

.tools-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
}

.tool-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    background-color: #1890ff;
    border: none;
    color: white;
    margin-bottom: 10px;
    cursor: pointer;
}

.main-btn {
    z-index: 2;
}

.icon-btn {
    font-size: 20px;
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
    transition: all 0.1s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
    transform: translateY(10px);
}

.expanded .tool-btn:not(.main-btn) {
    animation: slideIn 0.2s ease forwards;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>