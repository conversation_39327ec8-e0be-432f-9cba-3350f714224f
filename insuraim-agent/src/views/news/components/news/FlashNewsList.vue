<template>
    <div class="flash-news-container">
        <!-- 日期时间标记 -->
        <div class="flex justify-between mb-4 items-center">
            <TimeDisplay />
            <div class="flex items-center">
                <div v-if="isMultiSelectMode" class="mr-4 flex items-center">
                    <span class="text-blue-600 font-medium">已选择 {{ selectedNews.length }}/{{ maxSelectCount }}</span>
                    <button v-if="selectedNews.length > 0"
                        class="ml-3 confirm-btn px-4 py-2 bg-green-600 text-white rounded-lg flex items-center"
                        @click="confirmBatchForward">
                        <Icon icon="mdi:check" class="mr-2" />
                        确认转发
                    </button>
                    <button class="ml-3 cancel-btn px-4 py-2 bg-gray-500 text-white rounded-lg flex items-center"
                        @click="toggleMultiSelectMode">
                        <Icon icon="mdi:close" class="mr-2" />
                        取消
                    </button>
                </div>
                <button class="batch-forward-btn px-4 py-2 bg-blue-600 text-white rounded-lg flex items-center"
                    @click="batchForwardNews" v-if="!isMultiSelectMode">
                    <Icon :icon="isMultiSelectMode ? 'mdi:checkbox-marked-outline' : 'mdi:share-variant'"
                        class="mr-2" />
                    批量分享
                </button>



            </div>
        </div>

        <!-- 快讯列表 -->
        <div class="flash-news-list">
            <div v-for="(item, index) in newsList" :key="item.id" :class="['flash-news-item', {
                'flash-news-item-selected': isNewsSelected(item.id),
                'flash-news-item-selectable': isMultiSelectMode
            }]" @click="isMultiSelectMode ? toggleSelectNews(item) : null">
                <!-- 多选复选框 -->
                <div v-if="isMultiSelectMode" class="flash-news-checkbox">
                    <div :class="['checkbox', { 'checked': isNewsSelected(item.id) }]">
                        <Icon v-if="isNewsSelected(item.id)" icon="mdi:check" class="checkbox-icon" />
                    </div>
                </div>

                <!-- 快讯标题和来源 -->
                <div class="flash-news-header">
                    <h3 class="flash-news-title">{{ item.title }}</h3>
                    <div class="right flex items-center">
                        <span
                            :class="['flash-news-source hover:text-blue-600 hover:underline cursor-pointer', { 'copied': copiedSources.has(item.link) }]"
                            title="点击复制新闻源" @click.stop="copyClipboard(item.link + 'l')">
                            <Icon :icon="copiedSources.has(item.link) ? 'mdi:check' : 'mdi:content-copy'"
                                class="mr-1 text-sm copy-icon" />
                            复制链接
                        </span>
                        <!-- 一键转发按钮 -->
                        <div class="flash-news-footer ml-2">
                            <button class="forward-btn" @click.stop="forwardNews(item)">
                                <Icon icon="mdi:share-variant" class="mr-2" />
                                一键分享
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 快讯内容 -->
                <div class="flash-news-content">
                    <p>{{ getPlainText(item.content, 500) }}</p>
                </div>

                <!-- 分隔线，最后一项不显示 -->
                <div v-if="index < newsList.length - 1" class="flash-news-divider"></div>
            </div>
        </div>

        <!-- 无数据提示 -->
        <div v-if="newsList.length === 0" class="py-12 text-center">
            <Icon icon="mdi:newspaper-variant" class="text-gray-300 text-5xl mb-4" />
            <p class="text-gray-500 text-lg">暂无相关快讯</p>
        </div>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { Icon } from '@iconify/vue';
import { message } from 'ant-design-vue';
import TimeDisplay from './TimeDisplay.vue';
import { getPlainText, copyToClipboard } from '@/utils/format';
const props = defineProps({
    newsList: {
        type: Array,
        default: () => []
    }
});

const emit = defineEmits(['forward', 'batch-forward']);

const maxSelectCount = 4;
const isMultiSelectMode = ref(false);
const selectedNews = ref([]);
const copiedSources = ref(new Set());

const toggleMultiSelectMode = () => {
    isMultiSelectMode.value = !isMultiSelectMode.value;
    if (!isMultiSelectMode.value) {
        selectedNews.value = [];
    }
};

const toggleSelectNews = (news) => {
    if (!isMultiSelectMode.value) return;

    const index = selectedNews.value.findIndex(item => item.id === news.id);
    if (index >= 0) {
        selectedNews.value.splice(index, 1);
    } else {
        if (selectedNews.value.length >= maxSelectCount) {
            message.warning(`最多只能选择${maxSelectCount}条快讯`);
            return;
        }
        selectedNews.value.push(news);
    }
};

const isNewsSelected = (newsId) => {
    return selectedNews.value.some(item => item.id === newsId);
};
const copyClipboard = async (text) => {
    const result = await copyToClipboard(text);
    if (result) {
        message.success('已复制到剪贴板，可分享给好友');
    } else {
        message.error('复制失败，请手动复制');
    }
};

const forwardNews = (news) => {
    if (isMultiSelectMode.value) return;
    emit('forward', news);
};

const batchForwardNews = () => {
    if (isMultiSelectMode.value) return;
    toggleMultiSelectMode();
};

const confirmBatchForward = () => {
    if (selectedNews.value.length === 0) {
        message.warning('请至少选择一条快讯');
        return;
    }
    emit('batch-forward', selectedNews.value);
};

</script>

<style scoped>
.flash-news-container {
    display: flex;
    flex-direction: column;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.flash-news-item {
    padding: 10px 24px;
    position: relative;
    background-color: #fff;
    transition: background-color 0.2s ease;
}

.flash-news-item:hover {
    background-color: #f8fafc;
}

.flash-news-item-selectable {
    cursor: pointer;
    padding-left: 50px;
}

.flash-news-item-selected {
    background-color: #f0f9ff;
    border-left: 4px solid #2563eb;
}

.flash-news-checkbox {
    position: absolute;
    left: 16px;
    top: 24px;
}

.checkbox {
    width: 22px;
    height: 22px;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    background-color: white;
}

.checkbox.checked {
    background-color: #2563eb;
    border-color: #2563eb;
}

.checkbox-icon {
    color: white;
    font-size: 16px;
}

.confirm-btn {
    transition: all 0.2s ease;
}

.confirm-btn:hover {
    background-color: #16a34a;
    transform: translateY(-1px);
}

.cancel-btn {
    transition: all 0.2s ease;
}

.cancel-btn:hover {
    background-color: #64748b;
    transform: translateY(-1px);
}

.flash-news-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.flash-news-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    flex: 1;
    line-height: 1.4;
}

.flash-news-source {
    font-size: 14px;
    color: #666;
    padding: 2px 8px;
    margin-left: 12px;
    white-space: nowrap;
    display: flex;
    align-items: center;
    background-color: #f8fafc;
    border-radius: 4px;
    transition: all 0.2s ease;
    position: relative;
    border: 1px solid transparent;
}

.flash-news-source.copied {
    background-color: #d1fae5;
    color: #059669;
    border-color: #a7f3d0;
}

.flash-news-source:hover {
    background-color: #e0f2fe;
    color: #0284c7;
    border-color: #bae6fd;
}

.flash-news-source.copied:hover {
    background-color: #a7f3d0;
    color: #047857;
    border-color: #6ee7b7;
}

.flash-news-source .copy-icon {
    transition: transform 0.2s ease;
}

.flash-news-source:hover .copy-icon {
    transform: scale(1.2);
    color: #0284c7;
}

.flash-news-source::after {
    content: '点击复制';
    position: absolute;
    bottom: -22px;
    left: 50%;
    transform: translateX(-50%) scale(0.8);
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
    opacity: 0;
    transition: all 0.2s ease;
    pointer-events: none;
    z-index: 10;
    white-space: nowrap;
}

.flash-news-source:hover::after {
    opacity: 1;
    transform: translateX(-50%) scale(1);
}

.flash-news-content {
    font-size: 15px;
    line-height: 1.6;
    color: #444;
    margin-bottom: 16px;
    text-align: justify;
    white-space: pre-line;
}

.flash-news-content p {
    margin-bottom: 0.8em;
}

.flash-news-content p:last-child {
    margin-bottom: 0;
}

.flash-news-footer {
    display: flex;
    justify-content: flex-end;
}

.forward-btn {
    background-color: #f0f9ff;
    color: #0284c7;
    border: 1px solid #bae6fd;
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
}

.forward-btn:hover {
    background-color: #e0f2fe;
    border-color: #7dd3fc;
}

.batch-forward-btn {
    background-color: #2563eb;
    transition: all 0.2s;
    font-weight: 500;
    font-size: 14px;
}

.batch-forward-btn:hover {
    background-color: #1d4ed8;
}

.flash-news-divider {
    height: 1px;
    background-color: #f0f0f0;
    margin: 10px -24px 0;
}
</style>