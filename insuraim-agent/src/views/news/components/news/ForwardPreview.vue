<template>
    <div ref="previewRef" class="forward-preview" style="position: fixed; left: -9999px;">
        <!-- 单条快讯预览 -->
        <template v-if="news && !Array.isArray(news)">
            <div class="forward-card">
                <div class="forward-card-header" style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);">
                    <div class="forward-logo">
                        <img src="http://resouce.insuraim.com/Insuriam/icon/Cfo6zjv.webp" alt="InsurAim"
                            class="logo-img" />
                    </div>
                    <div class="mt-2" style="display: flex;width: 100%;justify-content: center;align-items: center">
                        <h2 class="text-lg text-center text-white"
                            style="color: white;font-weight: 600;margin-right: 10px;">{{ currentDate
                            }}</h2>
                        <span class="text-lg text-center text-white" style="margin-left: 10px;font-weight: 600;">
                            {{ weekDay }}
                        </span>
                    </div>
                </div>


                <div class="forward-card-content" style="display: flex;">
                    <div class="mr-2 font-bold">{{ formatTimeHM(news.pubDate || news.createTime ||
                        Math.floor(Date.now() /
                            1000)) }}</div>
                    <div class="content">
                        <h2 class="forward-title">{{ news.title }}</h2>
                        <div class="forward-text">
                            {{ getPlainText(news.content, 300) }}
                        </div>

                    </div>
                </div>
                <div class="forward-card-footer">
                    <div class="left" style="display: flex;flex-direction: column;align-items: center;">
                        <h2 class="text-lg text-center text-black" style="font-weight: 400;margin-bottom: 10px;">
                            {{ userInfo.name }}
                        </h2>

                        <img :src="userInfo.avatar" @error="handleImageError" :alt="userInfo.name"
                            class="w-20 h-20 rounded-full" />

                        <p class=" text-sm text-center" style="font-weight: 400;margin-bottom: 10px;color: #64748b;">
                            添加我的微信，了解最新保险资讯
                        </p>
                    </div>
                    <div class="right">
                        <div class="qrcode-placeholder">
                            <div class="qrcode-text">微信扫码关注</div>
                        </div>
                    </div>
                </div>
            </div>
        </template>

        <!-- 批量快讯预览 -->
        <template v-else-if="news && Array.isArray(news)">
            <div class="forward-card batch-card">
                <div class="forward-card-header" style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);">
                    <div class="forward-logo">
                        <img src="http://resouce.insuraim.com/Insuriam/icon/Cfo6zjv.webp" alt="InsurAim"
                            class="logo-img" />
                    </div>
                    <div class="mt-2" style="display: flex;width: 100%;justify-content: center;align-items: center">
                        <h2 class="text-lg text-center text-white"
                            style="color: white;font-weight: 600;margin-right: 10px;">{{ currentDate
                            }}</h2>
                        <span class="text-lg text-center text-white" style="margin-left: 10px;font-weight: 600;">
                            {{ weekDay }}
                        </span>
                    </div>
                </div>

                <div class="forward-card-content batch-content">
                    <div v-for="(item, index) in news" :key="item.id" class="batch-news-item">
                        <div class="mr-2 font-bold">{{ formatTimeHM(item.pubDate || item.createTime ||
                            Math.floor(Date.now() /
                                1000)) }}</div>
                        <div class="batch-news-content">
                            <h3 class="batch-news-title">{{ item.title }}</h3>
                            <div class="batch-news-text">
                                {{ getPlainText(item.content, 120) }}
                            </div>

                        </div>
                    </div>
                </div>
                <div class="forward-card-footer">
                    <div class="left" style="display: flex;flex-direction: column;align-items: center;">
                        <h2 class="text-lg text-center text-black" style="font-weight: 400;margin-bottom: 10px;">
                            {{ userInfo.name }}
                        </h2>

                        <img :src="userInfo.avatar" @error="handleImageError" :alt="userInfo.name"
                            class="w-20 h-20 rounded-full" />
                        <p class=" text-sm text-center" style="font-weight: 400;margin-bottom: 10px;color: #64748b;">
                            添加我的微信，了解最新保险资讯
                        </p>
                    </div>
                    <div class="right">
                        <div class="qrcode-placeholder">
                            <div class="qrcode-text">微信扫码关注</div>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useUserStore } from '@/store/modules/user';
const userStore = useUserStore();
const props = defineProps({
    news: {
        type: [Object, Array],
        required: true
    }
});
const userInfo = ref({
    name: userStore.userInfo.name,
    avatar: userStore.userInfo.avatar
});

const defaultAvatar = 'http://resouce.insuraim.com/Insuriam/icon/default-avatar.png'; // 默认头像URL

const handleImageError = (e) => {
    e.target.src = defaultAvatar;
};

const previewRef = ref(null);

const currentDate = computed(() => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    return `${year}年${month}月${day}日`;
});

const formatTimeHM = (timestamp) => {
    const date = new Date(timestamp * 1000);
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${hours}:${minutes}`;
};

const getPlainText = (html, len = 100) => {
    if (!html) return '';
    const text = html.replace(/<[^>]+>/g, '').replace(/\s+/g, ' ').trim();
    return text.length > len ? text.slice(0, len) + '...' : text;
};

const weekDay = computed(() => {
    const days = ['日', '一', '二', '三', '四', '五', '六'];
    const now = new Date();
    return `星期${days[now.getDay()]}`;
});

defineExpose({
    previewRef
});
</script>

<style scoped>
.forward-preview {
    width: 500px;
    background-color: transparent;
    border-radius: 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.forward-card {
    background-color: transparent;
    border-radius: 0px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.forward-card-header {
    border-radius: 1px;
    padding: 16px 20px;
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.forward-logo {
    /* 水平居中对齐 */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.logo-img {
    width: 28px;
    height: 28px;
    object-fit: contain;
    margin-right: 8px;
    background-color: white;
    border-radius: 50%;
    padding: 2px;
}

.logo-text {
    font-size: 16px;
    font-weight: 600;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
}

.forward-date {
    font-size: 14px;
    opacity: 0.9;
}

.forward-card-content {
    padding: 20px;
    flex: 1;
}

.forward-title {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 12px;
    line-height: 1.4;
}

.forward-text {
    font-size: 15px;
    line-height: 1.6;
    color: #334155;
    margin-bottom: 16px;
    white-space: pre-line;
}

.forward-source {
    font-size: 14px;
    color: #64748b;
    padding-top: 12px;
    border-top: 1px dashed #e2e8f0;
}

.forward-card-footer {
    padding: 16px 20px;
    background-color: #b9b9b9;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.qrcode-placeholder {
    width: 80px;
    height: 80px;
    background-color: #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0px;
}

.qrcode-text {
    font-size: 12px;
    color: #64748b;
    text-align: center;
}

.forward-brand {
    font-size: 14px;
    color: #64748b;
    flex: 1;
    text-align: right;
    margin-left: 16px;
}

/* 批量转发样式 */
.batch-card {
    max-width: 750px;
}

.batch-content {
    padding: 12px;
}

.batch-news-item {
    display: flex;
    padding: 12px;
    border-bottom: 1px solid #e2e8f0;
    margin-bottom: 8px;
}

.batch-news-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.batch-news-content {
    flex: 1;
}

.batch-news-title {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 8px;
    line-height: 1.4;
}

.batch-news-text {
    font-size: 14px;
    line-height: 1.6;
    color: #334155;
    margin-bottom: 8px;
}

.batch-news-source {
    font-size: 13px;
    color: #64748b;
}
</style>