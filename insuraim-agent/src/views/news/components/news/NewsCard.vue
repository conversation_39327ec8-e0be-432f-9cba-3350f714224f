<template>
    <div class="news-card news-card-row bg-white rounded-2xl overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-200 flex border border-gray-100 cursor-pointer"
        @click="$emit('click', news)">
        <div class="news-card-img-wrap">
            <!-- PDF文件显示PDF图标 -->
            <template v-if="news.pdf">
                <div class="news-card-img news-card-img-placeholder news-card-pdf">
                    <div class="news-img-summary-icon">
                        <Icon icon="mdi:file-pdf-box" class="icon-pdf" />
                    </div>
                    <div class="news-img-summary-text">
                        PDF文件
                    </div>
                </div>
            </template>
            <!-- 有图片且未记录失败 -->
            <template v-else-if="news.img && !isImageFailed(news.img)">
                <img :src="news.img" :alt="news.title" class="news-card-img" @error="handleImgError(news.img)" />
            </template>
            <!-- 没有图片，使用随机图片 -->
            <template v-else-if="!news.img">
                <img :src="getRandomImage(news.id)" :alt="news.title" class="news-card-img" />
            </template>
            <!-- 图片加载失败，显示纯文本 -->
            <template v-else>
                <div class="news-card-img news-card-img-placeholder news-card-img-summary">
                    <div class="news-img-summary-icon">
                        <Icon icon="mdi:umbrella" class="icon-insurance" />
                    </div>
                    <div class="news-img-summary-text">
                        {{ getPlainText(news.content, 60) }}
                    </div>
                </div>
            </template>
        </div>
        <div class="news-card-content flex-1 flex flex-col justify-between">
            <div>
                <div class="news-tags">
                    <span v-for="(tag, idx) in filteredTags" :key="idx"
                        class="news-tag cursor-pointer hover:bg-blue-700" @click.stop="handleTagClick(tag)">
                        {{ tag }}
                    </span>
                    <span v-if="news.pdf" class="news-tag news-tag-pdf">PDF</span>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2 line-clamp-2">{{ news.title }}</h3>
                <p v-if="!news.pdf" class="text-sm text-gray-600 mb-4 line-clamp-3 flex-1">
                    {{ getPlainText(news.content, 100) }}
                </p>
                <p v-else class="text-sm text-gray-600 mb-4 flex-1">点击查看PDF文件</p>
            </div>
            <div class="flex items-center justify-between text-xs text-gray-500 mt-2">
                <div class="flex items-center">
                    <Icon icon="mdi:calendar" class="mr-1" />
                    <span>{{ formatDate(news.createTime || news.pubDate) }}</span>
                    <span v-if="news.link" class="mx-2">·</span>
                    <a v-if="news.link" class="text-blue-600 hover:text-blue-800 transition-colors cursor-pointer"
                        @click.stop="copyLink(news.link)">复制链接</a>
                </div>
                <div class="text-blue-600 hover:text-blue-800 font-medium flex items-center">
                    {{ news.pdf ? '查看PDF' : '阅读详情' }}
                    <Icon :icon="news.pdf ? 'mdi:file-document' : 'mdi:arrow-right'" class="ml-1" />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';
import { ref, computed } from 'vue';
import { message } from 'ant-design-vue';
import { copyToClipboard } from '../../../../utils/format';

const props = defineProps({
    news: {
        type: Object,
        required: true
    },
    selectedCategory: {
        type: String,
        default: 'all'
    }
});

const emit = defineEmits(['click', 'tagClick']);

// 处理标签点击
const handleTagClick = (tag) => {
    emit('tagClick', tag);
};

// 计算属性：根据selectedCategory过滤标签
const filteredTags = computed(() => {
    if (!props.news.tags) return [];

    const tags = props.news.tags.split(',');

    // 如果是行业新闻分类，过滤掉特定的标签
    if (props.selectedCategory === '行业新闻') {
        return tags.filter(tag => {
            const trimmedTag = tag.trim();
            return !['行业新闻', '市场分析', '产品动态'].includes(trimmedTag);
        });
    }

    return tags;
});

// 记录加载失败的图片URL
const failedImages = ref(new Set());

// 图片缓存，保存每个新闻ID对应的图片URL
const newsImageMap = ref(new Map());
const copyLink = (link) => {
    let res = copyToClipboard(link);
    if (res) {
        message.success('复制链接成功');
    } else {
        message.error('复制链接失败');
    }
};
// 保险相关图片备选
const insuranceImages = [
    'https://images.unsplash.com/photo-1521791136064-7986c2920216?auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1450101499163-c8848c66ca85?auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1521737852567-6949f3f9f2b5?auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1503676382389-4809596d5290?auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1579684385127-1ef15d508118?auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1570129477492-45c003edd2be?auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1533473359331-0135ef1b58bf?auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1476703993599-0035a21b17a9?auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1540479859555-17af45c78602?auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1551288049-bebda4e38f71?auto=format&fit=crop&w=800&q=80'
];

const getRandomImage = (id) => {
    if (newsImageMap.value.has(id)) {
        return newsImageMap.value.get(id);
    }

    const idx = Math.floor(Math.random() * insuranceImages.length);
    const imageUrl = insuranceImages[idx];
    newsImageMap.value.set(id, imageUrl);
    return imageUrl;
};

const handleImgError = (imgUrl) => {
    console.log('图片加载失败:', imgUrl);
    failedImages.value.add(imgUrl);
};

const isImageFailed = (imgUrl) => {
    return failedImages.value.has(imgUrl);
};

const formatDate = (timestamp) => {
    const date = new Date(timestamp * 1000);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

const getPlainText = (html, len = 100) => {
    if (!html) return '';
    const text = html.replace(/<[^>]+>/g, '').replace(/\s+/g, ' ').trim();
    return text.length > len ? text.slice(0, len) + '...' : text;
};
</script>

<style scoped>
.news-card {
    padding: 0;
    margin: 0;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.04);
    border-radius: 18px;
    transition: all 0.2s ease;
    border: 1px solid var(--border-light);
}

.news-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.07);
}

.news-card-row {
    flex-direction: row;
    height: 200px;
    align-items: stretch;
}

.news-card-img-wrap {
    width: 260px;
    min-width: 220px;
    max-width: 320px;
    height: 100%;
    display: flex;
    align-items: stretch;
    background: #f9fafb;
    border-right: 1px solid #f3f4f6;
}

.news-card-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 0;
}

.news-card-img-placeholder {
    color: #bdbdbd;
    font-size: 15px;
    text-align: center;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.news-card-content {
    padding: 22px 28px 18px 28px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
}

.news-tags {
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 6px;
    margin-right: 1rem;
}

.news-tag {
    background: #2563eb;
    color: #fff;
    font-size: 12px;
    padding: 2px 10px;
    border-radius: 12px;
    line-height: 1.5;
    white-space: nowrap;
    transition: background 0.2s;
}

.news-tag:not(:last-child) {
    margin-right: 1rem;
}

.news-card-pdf {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 1.5px solid #bae6fd;
}

.icon-pdf {
    font-size: 32px;
    color: #0284c7;
    filter: drop-shadow(0 2px 4px #7dd3fc88);
}

.news-tag-pdf {
    background: #0284c7;
}

.news-card-img-summary {
    color: #1e293b;
    font-size: 18px;
    font-weight: 500;
    text-align: center;
    background: linear-gradient(135deg, #e0e7ff 0%, #f1f5f9 100%);
    padding: 0 16px;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
    overflow: hidden;
    line-height: 1.7;
    word-break: break-all;
    box-shadow: 0 2px 8px 0 rgba(37, 99, 235, 0.06) inset;
    border: 1.5px solid #e0e7ff;
}

.news-img-summary-icon {
    margin-bottom: 10px;
}

.icon-insurance {
    font-size: 32px;
    color: #2563eb;
    filter: drop-shadow(0 2px 4px #a5b4fc88);
}

.news-img-summary-text {
    font-size: 18px;
    font-weight: 600;
    color: #334155;
    text-shadow: 0 1px 2px #fff, 0 2px 8px #a5b4fc22;
    padding: 0 4px;
    max-height: 3.4em;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>