<template>
    <div
        class="title-section mb-6 p-6 rounded-lg shadow-md text-white bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700">
        <div class="flex items-center">
            <Icon :icon="categoryIcon" class="text-4xl mr-3" />
            <h1 class="text-2xl font-bold page-title text-white">{{ title }}</h1>
        </div>
        <p class="mt-2 page-description">
            {{ description }}
        </p>
    </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';
import { computed } from 'vue';

const props = defineProps({
    category: {
        type: String,
        default: 'all'
    }
});

const categoryIcon = computed(() => {
    switch (props.category) {
        case '快讯':
            return 'mdi:flash';
        case '产品精选':
            return 'mdi:star';
        case '产品优惠':
            return 'mdi:tag-multiple';
        case '行业新闻':
            return 'mdi:newspaper';
        default:
            return 'mdi:newspaper-variant';
    }
});

const title = computed(() => {
    switch (props.category) {
        case '快讯':
            return 'Insuraim快讯';
        case '产品精选':
            return '产品精选';
        case '产品优惠':
            return '产品动态';
        case '行业新闻':
            return '行业新闻';
        default:
            return '保险资讯';
    }
});

const description = computed(() => {
    switch (props.category) {
        case '快讯':
            return '最新保险动态和快讯，及时了解行业最新信息';
        case '产品精选':
            return '精选优质保险产品，满足您的多样化保障需求';
        case '产品优惠':
            return '最新保险产品优惠和动态，把握投保良机';
        case '行业新闻':
            return '保险行业最新动态、政策解读和市场分析';
        default:
            return '了解保险行业最新资讯、产品动态和专业观点';
    }
});
</script>

<style scoped>
.title-section {
    transition: all 0.3s ease;
}

.title-section:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.page-title {
    position: relative;
    display: inline-block;
    color: white;
}

.page-title::after {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 0;
    width: 40px;
    height: 3px;
    background-color: white;
    border-radius: 3px;
}

.page-description {
    max-width: 600px;
    opacity: 0.9;
}
</style>