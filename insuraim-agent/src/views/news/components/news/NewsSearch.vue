<template>
    <div class="py-4 px-4 sm:px-6 shadow-sm sticky top-0 z-10">
        <div class="max-w-5xl mx-auto">
            <div
                class="relative rounded-2xl shadow-sm bg-gray-100 hover:shadow-md transition-shadow duration-200 flex items-center">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Icon icon="mdi:magnify" class="h-5 w-5 text-gray-400" />
                </div>
                <input type="text"
                    class="py-3 block w-full pl-10 pr-12 bg-tertiary focus:ring-0 text-base rounded-[12px]"
                    :placeholder="placeholder" v-model="searchValue" @keyup.enter="handleSearch" />
                <div v-if="searchValue" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <button @click="clearSearch" class="text-gray-400 hover:text-gray-600">
                        <Icon icon="mdi:close" class="h-5 w-5" />
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { Icon } from '@iconify/vue';

const props = defineProps({
    placeholder: {
        type: String,
        default: '搜索保险资讯...'
    },
    modelValue: {
        type: String,
        default: ''
    }
});

const emit = defineEmits(['update:modelValue', 'search', 'clear']);

const searchValue = ref(props.modelValue);

watch(() => props.modelValue, (newVal) => {
    searchValue.value = newVal;
});

watch(searchValue, (newVal) => {
    emit('update:modelValue', newVal);
});

const handleSearch = () => {
    emit('search', searchValue.value);
};

const clearSearch = () => {
    searchValue.value = '';
    emit('update:modelValue', '');
    emit('clear');
};
</script>

<style scoped>
.bg-tertiary {
    background-color: var(--bg-tertiary, #f3f4f6);
}
</style>