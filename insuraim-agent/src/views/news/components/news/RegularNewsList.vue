<template>
    <div class="flex flex-col flex-grow max-h-[80vh] overflow-y-auto">
        <!-- 列表内容区域 - 可滚动 -->
        <div class="flex-grow overflow-y-auto news-list-container">
            <!-- 普通资讯列表 -->
            <div class="grid grid-cols-1 gap-6">
                <NewsCard v-for="item in newsList" :key="item.id" :news="item" :selectedCategory="selectedCategory"
                    @click="$emit('click', item)" @tagClick="handleTagClick" />
            </div>

            <!-- 无数据提示 -->
            <div v-if="newsList.length === 0" class="py-12 text-center">
                <Icon icon="mdi:newspaper-variant" class="text-gray-300 text-5xl mb-4" />
                <p class="text-gray-500 text-lg">暂无相关资讯</p>
            </div>
        </div>
    </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';
import NewsCard from './NewsCard.vue';

defineProps({
    newsList: {
        type: Array,
        default: () => []
    },
    selectedCategory: {
        type: String,
        default: 'all'
    }
});

const emit = defineEmits(['click', 'tagClick']);

// 处理标签点击
const handleTagClick = (tag) => {
    emit('tagClick', tag);
};
</script>

<style scoped>
.news-list-container {
    min-height: 300px;
    max-height: calc(100vh - 390px);
}

.hide-scrollbar::-webkit-scrollbar {
    display: none;
}

.hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
}
</style>