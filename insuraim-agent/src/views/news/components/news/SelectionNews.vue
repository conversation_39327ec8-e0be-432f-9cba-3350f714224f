<template>
    <div class="selection-news">
        <!-- 轮播图部分 -->
        <div class="swiper-section">
            <a-carousel ref="carousel" :autoplay="true" :dots="false" class="swiper-carousel">
                <div v-for="(newsGroup, index) in swiperNews.list" :key="index" class="swiper-slide">
                    <div class="swiper-content">
                        <!-- 左侧图片区域 -->
                        <div class="swiper-image-section">
                            <img :src="newsGroup[activeNewsIndex].image" :alt="newsGroup[activeNewsIndex].title"
                                class="swiper-image">
                        </div>
                        <!-- 右侧新闻列表区域 -->
                        <div class="swiper-news-section">
                            <div class="news-list-container">
                                <div v-for="(item, idx) in newsGroup" :key="item.id" class="swiper-news-item"
                                    :class="{ active: idx === activeNewsIndex }" @click="handleNewsClick(idx)"
                                    @mouseenter="handleNewsHover(idx)">
                                    <h3 class="swiper-news-title">{{ item.title }}</h3>
                                    <p class="swiper-news-desc">{{ getPlainText(item.content, 100) }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </a-carousel>
        </div>

        <!-- 往期精选部分 -->
        <div class="history-section">
            <div class="section-header">
                <h2 class="section-title">往期精选</h2>
                <a-button type="link" class="view-all-btn">
                    查看全部
                    <template #icon>
                        <Icon icon="material-symbols:arrow-forward" />
                    </template>
                </a-button>
            </div>

            <div class="news-grid">
                <div v-for="(newsGroup, groupIndex) in news.list" :key="groupIndex" class="news-card"
                    :class="{ 'expanded': expandedCards[groupIndex] }" @click="toggleCard(groupIndex)">
                    <div class="news-card-header">
                        <span class="news-type">{{ news.type }}</span>
                        <span class="news-date">{{ formatDate(newsGroup[0].pubDate) }}</span>
                    </div>
                    <img :src="newsGroup[0].image" alt="news-image" class="news-image">

                    <!-- 第一条新闻（始终显示） -->
                    <div class="news-list">
                        <div class="news-item primary-news">
                            <h3 class="news-title">{{ newsGroup[0].title }}</h3>
                            <p class="news-content">{{ getPlainText(newsGroup[0].content, 100) }}</p>
                        </div>

                        <!-- 展开后显示的新闻 -->
                        <div class="expandable-news" :class="{ 'show': expandedCards[groupIndex] }">
                            <div v-for="item in newsGroup.slice(1, 3)" :key="item.id" class="news-item">
                                <h3 class="news-title">{{ item.title }}</h3>
                                <p class="news-content">{{ getPlainText(item.content, 100) }}</p>
                            </div>
                        </div>

                        <!-- 展开/折叠按钮 -->
                        <div class="expand-button" @click.stop="toggleCard(groupIndex)">
                            <Icon
                                :icon="expandedCards[groupIndex] ? 'material-symbols:expand-less' : 'material-symbols:expand-more'" />
                            <span>{{ expandedCards[groupIndex] ? '收起' : '展开' }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { Icon } from '@iconify/vue';

const swiperNews = ref({
    title: 'Part 01',
    image: 'https://images.unsplash.com/photo-1521791136064-7986c2920216?auto=format&fit=crop&w=800&q=80',
    list: [
        [
            {
                "id": 713,
                "image": "https://images.unsplash.com/photo-1450101499163-c8848c66ca85?auto=format&fit=crop&w=800&q=80",
                "title": "陳茂波：香港金融中心表現亮眼　成國際資金安全港　",
                "content": "財政司司長陳茂波表示，去年以來香港金融中心的表現相當亮眼，亦是穩步前進，香港銀行存款今年再上升，總額達到18萬億元，形容資金是「湧來香港」，香港註冊基金按年錄得逾440億美元資金淨流入，增長近3倍，反映地緣政治既是挑戰亦是機遇。他期望香港未來兩年至三年可超過瑞士，成為全世界第一的跨境資產管理中心。",
                "pubDate": 1750491600,
            },
            {
                "id": 714,
                "image": "https://images.unsplash.com/photo-1521791136064-7986c2920216?auto=format&fit=crop&w=800&q=80",
                "title": "國安法五周年｜【足本重溫】港澳辦主任夏寶龍於國安法論壇致辭",
                "content": "《香港國安法》公布實施五周年論壇在會展舉行，中央港澳辦主任、國務院港澳辦主任夏寶龍在開幕禮主旨致辭， 指出五年來的實踐證明，《香港國安法》是捍衛「一國兩制」，維護香港繁榮穩定的守護神，是一部具有重大歷史意義和現實意義的好法律。",
                "pubDate": 1750491000,
            },
            {
                "id": 714,
                "image": "https://images.unsplash.com/photo-1540479859555-17af45c78602?auto=format&fit=crop&w=800&q=80",
                "title": "國安法五周年｜【足本重溫】港澳辦主任夏寶龍於國安法論壇致辭",
                "content": "《香港國安法》公布實施五周年論壇在會展舉行，中央港澳辦主任、國務院港澳辦主任夏寶龍在開幕禮主旨致辭， 指出五年來的實踐證明，《香港國安法》是捍衛「一國兩制」，維護香港繁榮穩定的守護神，是一部具有重大歷史意義和現實意義的好法律。",
                "pubDate": 1750491000,
            },
        ],
    ]
});

const news = ref({
    logo: 'https://images.unsplash.com/photo-1521791136064-7986c2920216?auto=format&fit=crop&w=800&q=80',
    title: "锐眼看世界",
    type: "犀利点评",
    list: [
        [
            {
                "image": "https://images.unsplash.com/photo-1521791136064-7986c2920216?auto=format&fit=crop&w=800&q=80",
                "id": 713,
                "title": "陳茂波：香港金融中心表現亮眼　成國際資金安全港　",
                "content": "財政司司長陳茂波表示，去年以來香港金融中心的表現相當亮眼，亦是穩步前進，香港銀行存款今年再上升，總額達到18萬億元，形容資金是「湧來香港」，香港註冊基金按年錄得逾440億美元資金淨流入，增長近3倍，反映地緣政治既是挑戰亦是機遇。他期望香港未來兩年至三年可超過瑞士，成為全世界第一的跨境資產管理中心。",
                "pubDate": 1750491600,
            },
            {
                "id": 714,
                "image": "https://images.unsplash.com/photo-1521791136064-7986c2920216?auto=format&fit=crop&w=800&q=80",
                "title": "國安法五周年｜【足本重溫】港澳辦主任夏寶龍於國安法論壇致辭",
                "content": "《香港國安法》公布實施五周年論壇在會展舉行，中央港澳辦主任、國務院港澳辦主任夏寶龍在開幕禮主旨致辭， 指出五年來的實踐證明，《香港國安法》是捍衛「一國兩制」，維護香港繁榮穩定的守護神，是一部具有重大歷史意義和現實意義的好法律。",
                "pubDate": 1750491000,
            },
            {
                "id": 712,
                "image": "https://images.unsplash.com/photo-1521791136064-7986c2920216?auto=format&fit=crop&w=800&q=80",
                "title": "國安法五周年｜張勇：國安法是維護國家安全法治結晶　",
                "content": "《香港國安法》公布實施五周年論壇，基本法委員會副主任、人大法工委副主任張勇致辭時表示，《香港國安法》是有效維護國家安全的法治結晶。",
                "pubDate": 1750479900,
            },
        ],
        [
            {
                "id": 713,
                "image": "https://images.unsplash.com/photo-1521791136064-7986c2920216?auto=format&fit=crop&w=800&q=80",
                "title": "陳茂波：香港金融中心表現亮眼　成國際資金安全港　",
                "content": "財政司司長陳茂波表示，去年以來香港金融中心的表現相當亮眼，亦是穩步前進，香港銀行存款今年再上升，總額達到18萬億元，形容資金是「湧來香港」，香港註冊基金按年錄得逾440億美元資金淨流入，增長近3倍，反映地緣政治既是挑戰亦是機遇。他期望香港未來兩年至三年可超過瑞士，成為全世界第一的跨境資產管理中心。",
                "pubDate": 1750491600,
            },
            {
                "id": 714,
                "image": "https://images.unsplash.com/photo-1521791136064-7986c2920216?auto=format&fit=crop&w=800&q=80",
                "title": "國安法五周年｜【足本重溫】港澳辦主任夏寶龍於國安法論壇致辭",
                "content": "《香港國安法》公布實施五周年論壇在會展舉行，中央港澳辦主任、國務院港澳辦主任夏寶龍在開幕禮主旨致辭， 指出五年來的實踐證明，《香港國安法》是捍衛「一國兩制」，維護香港繁榮穩定的守護神，是一部具有重大歷史意義和現實意義的好法律。",
                "pubDate": 1750491000,
            },
            {
                "id": 712,
                "image": "https://images.unsplash.com/photo-1521791136064-7986c2920216?auto=format&fit=crop&w=800&q=80",
                "title": "國安法五周年｜張勇：國安法是維護國家安全法治結晶　",
                "content": "《香港國安法》公布實施五周年論壇，基本法委員會副主任、人大法工委副主任張勇致辭時表示，《香港國安法》是有效維護國家安全的法治結晶。",
                "pubDate": 1750479900,
            },
        ],
        [
            {
                "id": 713,
                "image": "https://images.unsplash.com/photo-1521791136064-7986c2920216?auto=format&fit=crop&w=800&q=80",

                "title": "陳茂波：香港金融中心表現亮眼　成國際資金安全港　",
                "content": "財政司司長陳茂波表示，去年以來香港金融中心的表現相當亮眼，亦是穩步前進，香港銀行存款今年再上升，總額達到18萬億元，形容資金是「湧來香港」，香港註冊基金按年錄得逾440億美元資金淨流入，增長近3倍，反映地緣政治既是挑戰亦是機遇。他期望香港未來兩年至三年可超過瑞士，成為全世界第一的跨境資產管理中心。",
                "pubDate": 1750491600,
            },
            {
                "id": 714,
                "image": "https://images.unsplash.com/photo-1521791136064-7986c2920216?auto=format&fit=crop&w=800&q=80",

                "title": "國安法五周年｜【足本重溫】港澳辦主任夏寶龍於國安法論壇致辭",
                "content": "《香港國安法》公布實施五周年論壇在會展舉行，中央港澳辦主任、國務院港澳辦主任夏寶龍在開幕禮主旨致辭， 指出五年來的實踐證明，《香港國安法》是捍衛「一國兩制」，維護香港繁榮穩定的守護神，是一部具有重大歷史意義和現實意義的好法律。",
                "pubDate": 1750491000,
            },
            {
                "id": 712,
                "image": "https://images.unsplash.com/photo-1521791136064-7986c2920216?auto=format&fit=crop&w=800&q=80",

                "title": "國安法五周年｜張勇：國安法是維護國家安全法治結晶　",
                "content": "《香港國安法》公布實施五周年論壇，基本法委員會副主任、人大法工委副主任張勇致辭時表示，《香港國安法》是有效維護國家安全的法治結晶。",
                "pubDate": 1750479900,
            },
        ],
    ]
});

const activeNewsIndex = ref(0);
const autoplayInterval = ref(null);
const carousel = ref(null);

// 处理新闻点击
const handleNewsClick = (index) => {
    activeNewsIndex.value = index;
    resetAutoplay();
};

// 处理新闻悬停
const handleNewsHover = (index) => {
    activeNewsIndex.value = index;
    resetAutoplay();
};

// 重置自动播放
const resetAutoplay = () => {
    if (autoplayInterval.value) {
        clearInterval(autoplayInterval.value);
    }
    startAutoplay();
};

// 开始自动播放
const startAutoplay = () => {
    autoplayInterval.value = setInterval(() => {
        activeNewsIndex.value = (activeNewsIndex.value + 1) % 3;
    }, 3000);
};

// 组件挂载时启动自动播放
onMounted(() => {
    startAutoplay();
});

// 组件卸载时清除定时器
onUnmounted(() => {
    if (autoplayInterval.value) {
        clearInterval(autoplayInterval.value);
    }
});

// 工具函数：格式化日期
const formatDate = (timestamp) => {
    const date = new Date(timestamp * 1000);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

// 工具函数：获取纯文本
const getPlainText = (html, len = 100) => {
    if (!html) return '';
    const text = html.replace(/<[^>]+>/g, '').replace(/\s+/g, ' ').trim();
    return text.length > len ? text.slice(0, len) + '...' : text;
};

// 展开状态管理
const expandedCards = ref({});

// 切换卡片展开状态
const toggleCard = (index) => {
    expandedCards.value[index] = !expandedCards.value[index];
};
</script>

<style scoped>
.selection-news {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 轮播图样式 */
.swiper-section {
    margin-bottom: 40px;
}

.swiper-carousel {
    width: 100%;
    height: 400px;
    border-radius: 16px;
    overflow: hidden;
    background: #fff;
}

.swiper-slide {
    height: 400px;
}

.swiper-content {
    display: flex;
    height: 100%;
}

.swiper-image-section {
    flex: 0 0 50%;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.swiper-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.swiper-news-section {
    flex: 0 0 50%;
    padding: 0px 10px;
    background: #fff;
    display: flex;
    flex-direction: column;
}

.news-list-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.swiper-news-item {
    padding: 15px;
    border-radius: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    margin-bottom: 5px;
}

.swiper-news-item::before {
    content: '';
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 0;
    background: #2563eb;
    transition: height 0.3s ease;
}

.swiper-news-item:hover::before,
.swiper-news-item.active::before {
    height: 70%;
}

.swiper-news-item:hover {
    background: rgba(37, 99, 235, 0.05);
}

.swiper-news-item.active {
    background: rgba(37, 99, 235, 0.1);
}

.swiper-news-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.swiper-news-desc {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 往期精选样式 */
.history-section {
    margin-top: 40px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.section-title {
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

.view-all-btn {
    display: flex;
    align-items: center;
    gap: 4px;
}

.news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 24px;
}

.news-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    padding: 20px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.news-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.news-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.news-type {
    color: #2563eb;
    font-weight: 500;
}

.news-date {
    color: #666;
    font-size: 14px;
}

.news-list {
    display: flex;
    flex-direction: column;
}

.primary-news {
    padding-bottom: 16px;
    border-bottom: 1px solid #eee;
}

.expandable-news {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
    opacity: 0;
}

.expandable-news.show {
    max-height: 500px;
    opacity: 1;
    transition: max-height 0.3s ease-in, opacity 0.3s ease-in;
}

.expand-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    padding: 8px;
    color: #2563eb;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.expand-button:hover {
    background: rgba(37, 99, 235, 0.05);
    border-radius: 6px;
}

.news-card.expanded {
    background: #f8fafc;
}

.news-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.news-content {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .swiper-content {
        flex-direction: column;
    }

    .swiper-carousel {
        height: 600px;
    }

    .swiper-slide {
        height: 600px;
    }

    .swiper-image-section,
    .swiper-news-section {
        flex: 0 0 50%;
    }

    .swiper-news-section {
        padding: 20px;
    }

    .news-list-container {
        gap: 15px;
    }
}
</style>