<template>
    <div class="date-time-container">
        <div class="date-display">
            <Icon icon="mdi:calendar" class="mr-2 text-blue-600" />
            <span class="date">{{ currentTimeFormat.date }}</span>
            <span class="weekday ml-2">{{ currentTimeFormat.weekday }}</span>
        </div>
        <div class="time-display">
            <Icon icon="mdi:clock-outline" class="mr-2 text-blue-600" />
            <span class="time-text">
                <span :class="['hours', { 'changed': changedFields.hours }]" :data-value="prevTimeValues.hours">
                    {{ currentTimeFormat.time.split(':')[0] }}
                </span>
                <span class="time-separator">:</span>
                <span :class="['minutes', { 'changed': changedFields.minutes }]" :data-value="prevTimeValues.minutes">
                    {{ currentTimeFormat.time.split(':')[1] }}
                </span>
                <span class="time-separator">:</span>
                <span :class="['seconds pulse', { 'changed': changedFields.seconds }]"
                    :data-value="prevTimeValues.seconds">
                    {{ currentTimeFormat.time.split(':')[2] }}
                </span>
            </span>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { Icon } from '@iconify/vue';

const currentTime = ref(new Date());
let timeInterval = null;

const prevTimeValues = ref({
    hours: '',
    minutes: '',
    seconds: ''
});

const changedFields = ref({
    hours: false,
    minutes: false,
    seconds: false
});

const updateTime = () => {
    const oldTime = currentTime.value;
    currentTime.value = new Date();

    const oldHours = String(oldTime.getHours()).padStart(2, '0');
    const oldMinutes = String(oldTime.getMinutes()).padStart(2, '0');
    const oldSeconds = String(oldTime.getSeconds()).padStart(2, '0');

    const newHours = String(currentTime.value.getHours()).padStart(2, '0');
    const newMinutes = String(currentTime.value.getMinutes()).padStart(2, '0');
    const newSeconds = String(currentTime.value.getSeconds()).padStart(2, '0');

    changedFields.value.hours = oldHours !== newHours;
    changedFields.value.minutes = oldMinutes !== newMinutes;
    changedFields.value.seconds = oldSeconds !== newSeconds;

    prevTimeValues.value = {
        hours: oldHours,
        minutes: oldMinutes,
        seconds: oldSeconds
    };

    if (changedFields.value.hours || changedFields.value.minutes || changedFields.value.seconds) {
        setTimeout(() => {
            changedFields.value.hours = false;
            changedFields.value.minutes = false;
            changedFields.value.seconds = false;
        }, 500);
    }
};

const currentTimeFormat = computed(() => {
    const now = currentTime.value;
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    const weekday = weekdays[now.getDay()];

    return {
        date: `${year}年${month}月${day}日`,
        time: `${hours}:${minutes}:${seconds}`,
        weekday
    };
});

onMounted(() => {
    updateTime();
    timeInterval = setInterval(updateTime, 1000);
});

onUnmounted(() => {
    if (timeInterval) {
        clearInterval(timeInterval);
        timeInterval = null;
    }
});
</script>

<style scoped>
.date-time-container {
    display: flex;
    flex-direction: column;
    padding: 8px 12px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid #bae6fd;
    transition: all 0.3s ease;
}

.date-time-container:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.date-display {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    font-size: 15px;
    color: #334155;
}

.time-display {
    display: flex;
    align-items: center;
    font-size: 20px;
    font-weight: 600;
    color: #0284c7;
}

.date {
    font-weight: 500;
}

.weekday {
    color: #64748b;
    font-size: 14px;
}

.time-text {
    display: flex;
    align-items: center;
}

.hours,
.minutes,
.seconds {
    min-width: 28px;
    display: inline-block;
    text-align: center;
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    position: relative;
}

.hours::after,
.minutes::after,
.seconds::after {
    content: attr(data-value);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    opacity: 0;
    transform: translateY(-100%);
    transition: all 0.3s ease;
}

.hours.changed::after,
.minutes.changed::after,
.seconds.changed::after {
    animation: number-change 0.5s ease forwards;
}

@keyframes number-change {
    0% {
        opacity: 0;
        transform: translateY(-100%) scale(0.8);
    }

    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.time-separator {
    margin: 0 2px;
    opacity: 0.7;
}

.seconds {
    color: #0369a1;
}

.pulse {
    animation: pulse-animation 1s infinite;
}

@keyframes pulse-animation {
    0% {
        opacity: 1;
        transform: scale(1);
    }

    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }

    100% {
        opacity: 1;
        transform: scale(1);
    }
}
</style>