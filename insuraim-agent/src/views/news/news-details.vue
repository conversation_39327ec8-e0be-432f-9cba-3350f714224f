<template>
    <div class="bg-gray-50 min-h-screen">
        <!-- 页面头部导航 -->
        <div class="bg-white py-4 px-4 sm:px-6 shadow-sm sticky top-0 z-10 transition-all duration-300">
            <div class="max-w-5xl mx-auto">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <button @click="goBack"
                            class="mr-4 text-gray-600 hover:text-blue-600 transition-colors focus:outline-none transform hover:scale-110 duration-200">
                            <Icon icon="material-symbols:arrow-back" class="text-xl" />
                        </button>
                        <h1 class="text-xl font-medium text-gray-900 truncate">新闻详情</h1>
                    </div>

                    <div class="right flex items-center">

                        <!-- 喜欢 -->
                        <button
                            class="ml-4 text-gray-600 hover:text-blue-600 transition-colors focus:outline-none transform hover:scale-110 duration-200"
                            @click="toggleLike">
                            <Icon :icon="isLike ? 'material-symbols:favorite' : 'material-symbols:favorite-outline'"
                                :class="isLike ? 'text-red-500 text-3xl' : 'text-gray-600 text-3xl'" />
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="max-w-5xl mx-auto px-4 sm:px-6 py-6">
            <!-- 加载状态 -->
            <div v-if="loading" class="flex justify-center py-12">
                <a-spin size="large" />
            </div>

            <!-- 资讯详情内容 -->
            <div v-else class="animate-fade-in">
                <ToolsBar />
                <div
                    class="news-detail-container bg-white p-6 rounded-2xl shadow-md hover:shadow-lg transition-all duration-300">
                    <!-- PDF文件查看器 -->
                    <div v-if="newsData.pdf" class="article-content-container">
                        <div class="pdf-controls flex items-center justify-end mb-3">
                            <a :href="newsData.pdf" target="_blank"
                                class="inline-flex items-center text-sm text-gray-500 hover:text-blue-600 transition-colors">
                                <Icon icon="material-symbols:picture-as-pdf" class="mr-1" />
                                查看原始PDF
                                <Icon icon="material-symbols:open-in-new" class="ml-1" />
                            </a>
                        </div>
                        <pdf-viewer :src="newsData.pdf" class="article-pdf-viewer" />
                    </div>

                    <!-- 有图片显示图片 -->
                    <div v-else-if="newsData.img"
                        class="news-detail-img-container mb-6 transform hover:scale-[1.01] transition-all duration-300">
                        <img :src="newsData.img" :alt="newsData.title" class="news-detail-img"
                            @error="handleImgError" />
                        <!-- 标签显示，与列表一致 -->
                        <div class="absolute top-4 left-4 news-tags">
                            <span v-for="(tag, idx) in tagList" :key="idx" class="news-tag">
                                {{ tag }}
                            </span>
                        </div>
                        <div class="news-img-overlay"></div>
                    </div>
                    <!-- 无图片或图片失败，显示纯文本 -->
                    <div v-else
                        class="news-detail-img-container news-card-img-summary mb-6 transform hover:scale-[1.01] transition-all duration-300">
                        <div class="news-img-summary-icon mt-4">
                            <Icon icon="material-symbols:umbrella" class="icon-insurance" />
                        </div>
                        <div class="news-img-summary-text mb-4">
                            {{ getPlainText(newsData.content) }}
                        </div>
                        <!-- 标签显示，与列表一致 -->
                        <div class="absolute top-4 left-4 news-tags">
                            <span v-for="(tag, idx) in tagList" :key="idx" class="news-tag">
                                {{ tag }}
                            </span>
                        </div>
                    </div>

                    <h2 class="text-2xl sm:text-3xl font-semibold text-gray-900 mb-4 news-title">{{ newsData.title }}
                    </h2>

                    <div class="flex items-center text-sm text-gray-500 mb-6 news-meta">
                        <Icon icon="material-symbols:calendar-today" class="mr-1" />
                        <span>{{ formatDate(newsData.pubDate) }}</span>
                        <span v-if="newsData.link" class="mx-2 news-meta-divider">|</span>
                        <Icon v-if="newsData.link" icon="material-symbols:link" class="mr-1" />
                        <span v-if="newsData.link"
                            class="text-blue-600 hover:text-blue-800 transition-colors cursor-pointer"
                            @click="copyLink(newsData.link)">复制链接</span>
                    </div>

                    <div v-if="!newsData.pdf" class="news-detail-content">
                        <div v-if="newsData.content" class="text-gray-700 leading-relaxed html-content"
                            v-html="newsData.content"></div>
                        <div v-else class="text-gray-500 text-center py-8">暂无详细内容</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Icon } from '@iconify/vue';
import { message } from 'ant-design-vue';
import pdfViewer from 'pdf-vue3';
import { copyToClipboard, getPlainText } from '../../utils/format';
import ToolsBar from './components/details/ToolsBar.vue';

const route = useRoute();
const router = useRouter();
const loading = ref(false);
const imgError = ref(false);
const isLike = ref(false);
// 新闻数据
const newsData = ref({
    id: route.params.id,
    title: route.query.title || '',
    content: route.query.content || '',
    pubDate: route.query.pubDate || '',
    link: route.query.link || '',
    tags: route.query.tags || '',
    img: route.query.img || '',
    pdf: route.query.pdf || ''
});
const copyLink = (link) => {
    let res = copyToClipboard(link);
    if (res) {
        message.success('链接已复制到剪贴板');
    } else {
        message.error('复制链接失败');
    }
};
// 计算属性：标签列表
const tagList = computed(() => {
    return newsData.value.tags ? newsData.value.tags.split(',') : [];
});

// 返回上一页
const goBack = () => {
    router.back();
};
// 喜欢
const toggleLike = () => {
    isLike.value = !isLike.value;
};

// 图片加载失败处理
const handleImgError = () => {
    imgError.value = true;
    newsData.value.img = '';
};

// 日期格式化
const formatDate = (timestamp) => {
    if (!timestamp) return '';
    const date = new Date(timestamp * 1000);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

onMounted(() => {
    // 直接使用路由传递的数据，不再需要额外请求
    loading.value = false;
});
</script>

<style scoped>
.news-detail-container {
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.news-detail-container:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
}

.news-detail-img-container {
    position: relative;
    width: 100%;
    height: 350px;
    overflow: hidden;
    border-radius: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.news-detail-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.news-detail-img-container:hover .news-detail-img {
    transform: scale(1.03);
}

.news-img-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 70%, rgba(0, 0, 0, 0.2) 100%);
    pointer-events: none;
}

:deep(.visually-hidden) {
    display: none;
}

/* 穿透到富文本 */
:deep(.block-field-blocknodenewsfield-news-tags) {
    display: inline-block;
}

:deep(.block-field-blocknodenewscreated) {
    display: inline-block;
}

/* PDF查看器样式 */
.article-content-container {
    width: 100%;
    background: #fff;
}

.pdf-controls {
    padding: 0.5rem 0;
}

.article-pdf-viewer {
    width: 100%;
    min-height: 800px;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 去除pdf-vue3默认边框 */
:deep(.article-pdf-viewer .pdf-container) {
    border: none;
    box-shadow: none;
}

:deep(.article-pdf-viewer canvas) {
    border-radius: 4px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
}

:deep(.article-pdf-viewer .pdfviewer-container) {
    background: #fff;
}

@media (max-width: 640px) {
    .article-pdf-viewer {
        min-height: 500px;
    }
}

/* HTML内容样式 */
:deep(.html-content) {
    /* 基本样式 */
    font-size: 17px;
    line-height: 1.9;
    color: #374151;

    /* 标题样式 */
    & h1,
    & h2,
    & h3,
    & h4,
    & h5,
    & h6 {
        margin-top: 1.5rem;
        margin-bottom: 1rem;
        font-weight: 600;
        color: #1f2937;
        line-height: 1.3;
    }

    & h1 {
        font-size: 2rem;
    }

    & h2 {
        font-size: 1.6rem;
    }

    & h3 {
        font-size: 1.4rem;
    }

    /* 段落样式 */
    & p {
        margin-bottom: 1rem;
    }

    /* 强调和加粗 */
    & strong,
    & b {
        font-weight: 600;
        color: #1f2937;
    }

    /* 列表样式 */
    & ul,
    & ol {
        margin-bottom: 1rem;
        padding-left: 1.5rem;
    }

    & ul {
        list-style-type: disc;
    }

    & ol {
        list-style-type: decimal;
    }

    & li {
        margin-bottom: 0.5rem;
    }

    /* 链接样式 */
    & a {
        color: #2563eb;
        text-decoration: none;
        transition: color 0.2s;

        &:hover {
            color: #1d4ed8;
            text-decoration: underline;
        }
    }

    /* 分隔线 */
    & hr {
        margin: 1.5rem 0;
        border: 0;
        border-top: 1px solid #e5e7eb;
    }

    /* 引用样式 */
    & blockquote {
        margin: 1.5rem 0;
        padding-left: 1rem;
        border-left: 4px solid #3b82f6;
        color: #4b5563;
        font-style: italic;
    }

    /* 清除可能的框架样式影响 */
    & div[class^="framer-"],
    & span[class^="framer-"],
    & p[class^="framer-"] {
        all: revert;
        margin-bottom: 1rem;
    }

    /* 恢复特定样式以保持一致性 */
    & .framer-text {
        font-family: inherit;
        font-size: inherit;
        line-height: inherit;
    }
}

.news-title {
    position: relative;
    padding-bottom: 0.75rem;
    margin-bottom: 1.5rem;
    line-height: 1.3;
    color: #1f2937;
}

.news-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 80px;
    height: 3px;
    background: linear-gradient(to right, #3b82f6, #93c5fd);
    border-radius: 2px;
}

.news-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 8px;
}

.news-tag {
    background: #2563eb;
    color: #fff;
    font-size: 12px;
    padding: 3px 12px;
    border-radius: 12px;
    line-height: 1.5;
    white-space: nowrap;
    transition: all 0.2s;
    box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);
}

.news-tag:hover {
    background: #1d4ed8;
    transform: translateY(-1px);
}

.news-meta {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    color: #6b7280;
    margin: 1rem 0 1.5rem;
}

.news-meta-divider {
    color: #d1d5db;
    margin: 0 0.75rem;
}

.news-card-img-summary {
    color: #1e293b;
    font-size: 18px;
    font-weight: 500;
    text-align: center;
    background: linear-gradient(135deg, #e0e7ff 0%, #f1f5f9 100%);
    padding: 0 16px;
    border-radius: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
    overflow: hidden;
    line-height: 1.7;
    word-break: break-all;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.08) inset;
    border: 1.5px solid #e0e7ff;
}

.news-img-summary-icon {
    margin-bottom: 1.25rem;
}

.icon-insurance {
    font-size: 3rem;
    color: #2563eb;
    filter: drop-shadow(0 2px 8px rgba(165, 180, 252, 0.5));
}

.news-img-summary-text {
    font-size: 1.25rem;
    font-weight: 600;
    color: #334155;
    text-shadow: 0 1px 2px #fff, 0 2px 8px rgba(165, 180, 252, 0.15);
    padding: 0 1rem;
    max-height: 5em;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

.news-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 2.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #f3f4f6;
}

.animate-fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@media (max-width: 640px) {
    .news-detail-container {
        padding: 1.5rem;
    }

    .news-detail-img-container {
        height: 250px;
    }

    .news-title {
        font-size: 1.5rem;
    }

    .news-detail-content {
        font-size: 16px;
        line-height: 1.7;
    }

    .news-actions {
        flex-direction: column;
        gap: 1rem;
    }

    .news-actions button,
    .news-actions a {
        width: 100%;
        justify-content: center;
    }
}
</style>
<style>
/* 全局样式，专门针对framer元素，不受scoped限制 */
.html-content .framer-cze7v,
.html-content div[class^="framer-"] {
    max-width: 100%;
    width: 100%;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.html-content h1.framer-text,
.html-content h2.framer-text,
.html-content h3.framer-text,
.html-content h4.framer-text,
.html-content h5.framer-text,
.html-content h6.framer-text {
    margin-top: 1.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
    color: #1f2937;
}

.html-content p.framer-text {
    margin-bottom: 1rem;
    line-height: 1.8;
}

.html-content p.framer-text strong {
    font-weight: 600;
}

/* 日期特殊样式 */
.html-content h6.framer-text[data-styles-preset="YaIWdGEnI"] {
    color: #0497a7 !important;
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
}

/* 恢复所有framer的布局设置为标准文档流 */
.html-content div[style*="display:flex"],
.html-content div[style*="display: flex"],
.html-content div[data-framer-component-type] {
    display: block !important;
    margin-bottom: 1rem;
}

/* 移除所有内联样式和属性，保持HTML语义 */
.html-content *[data-framer-name] {
    margin: inherit;
    padding: inherit;
}
</style>