<template>
  <div class="container mx-auto py-6 px-4">
    <!-- 页面标题 -->
    <NewsHeader :category="selectedCategory" />

    <div class="bg-white rounded-[12px]">
      <!-- 搜索栏 -->
      <NewsSearch v-model="searchQuery" @search="handleSearch" @clear="clearSearch" />
      <div class="max-w-5xl mx-auto">
        <div class="tag-list-container relative">
          <div class="tag-scroll-indicator tag-scroll-left" @click="scrollTags('left')" v-show="canScrollLeft">
            <Icon icon="mdi:chevron-left" class="text-gray-600" />
          </div>
          <div class="tag-list-scroll" ref="tagListRef" v-if="selectedCategory != '快讯'">
            <div class="tag-list py-4 px-4 flex items-center">
              <a-tag :class="['tag-all', !selectedTag ? 'selected-tag' : '']" @click="handleTagClick('')">
                <Icon icon="mdi:tag-multiple" class="mr-1" />
                全部
              </a-tag>
              <a-tag v-for="tag in tagsList" :key="tag" :class="['tag-item', selectedTag === tag ? 'selected-tag' : '']"
                @click="handleTagClick(tag, $event)" class="cursor-pointer transition-all">
                <Icon icon="mdi:tag" class="mr-1" />
                <span class="tag-content">{{ tag }}</span>
                <!-- <span class="tag-count" v-if="tagCounts[tag]">{{ tagCounts[tag] }}</span> -->
              </a-tag>
            </div>
          </div>
          <div class="tag-scroll-indicator tag-scroll-right" @click="scrollTags('right')" v-show="canScrollRight">
            <Icon icon="mdi:chevron-right" class="text-gray-600" />
          </div>
        </div>
      </div>
      <!-- 主要内容 -->
      <div class="max-w-5xl mx-auto px-4 sm:px-6 py-6 flex flex-col main-container">
        <!-- 加载状态 -->
        <div v-if="loading" class="flex-grow flex justify-center py-12">
          <a-spin size="large" />
        </div>
        <div v-else-if="selectedCategory === '快讯'" class="flex-grow overflow-y-auto news-list-container">

          <FlashNewsList :newsList="newsList" @forward="forwardNews" @batch-forward="handleBatchForward" />


        </div>
        <div v-else-if="selectedCategory === '平台精选'" class="flex-grow overflow-y-auto news-list-container">
          <SelectionNews />
        </div>
        <!-- 资讯列表 -->
        <RegularNewsList v-else :newsList="newsList" :selectedCategory="selectedCategory" @click="goToNewsDetail"
          @tagClick="handleTagClick" />

        <!-- 分页 -->
        <div class="pagination-container py-4 border-t border-gray-100 mt-4">
          <a-pagination v-if="newsList.length > 0" v-model:current="pagination.pageNum" :total="pagination.total"
            :pageSize="pagination.pageSize" :pageCount="pagination.pages" @change="handlePageChange"
            :hideOnSinglePage="true" showQuickJumper class="flex justify-center" />
        </div>
      </div>

    </div>

    <!-- 转发预览 -->
    <ForwardPreview ref="forwardPreviewRef" :news="currentForwardNews" />

    <!-- 图片预览 -->
    <IPicPreview v-if="forwardImageUrl" :src="forwardImageUrl" :defaultTools="true" v-model:visible="previewVisible"
      @previewData="handlePreviewData" @copy="copyImageToClipboard(forwardImageUrl)"
      @download="saveBas64Img(forwardImageUrl, 'insuraim-news')" @update:visible="(visible) => {
        previewVisible = visible;
        if (!visible) {
          forwardImageUrl = '';
        }
      }" />

    <!-- 预览数据 Modal -->
    <!-- <a-modal v-model:visible="previewDataVisible" title="预览数据" :footer="null" width="800px" :destroyOnClose="true"
      :maskClosable="true" class="preview-data-modal">
      <div class="preview-data-container">
        <ForwardPreview ref="previewDataRef" :news="currentForwardNews" />
      </div>
    </a-modal> -->
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, h, onUnmounted, nextTick } from 'vue';
import { Icon } from '@iconify/vue';
import { message, Modal } from 'ant-design-vue';
import { newsAPI } from '../../api';
import { useRouter, useRoute } from 'vue-router';
import html2canvas from 'html2canvas';

// 导入组件
import NewsHeader from './components/news/NewsHeader.vue';
import NewsSearch from './components/news/NewsSearch.vue';
import FlashNewsList from './components/news/FlashNewsList.vue';
import RegularNewsList from './components/news/RegularNewsList.vue';
import ForwardPreview from './components/news/ForwardPreview.vue';
import IPicPreview from '@/components/IPicPreview.vue';
import SelectionNews from './components/news/SelectionNews.vue';
// 获取路由对象
const router = useRouter();
const route = useRoute();

// 状态管理
const loading = ref(false);
const searchQuery = ref('');
const selectedCategory = ref('all');
const newsList = ref([]);
const featuredNews = ref(null);
const pagination = reactive({
  pageNum: 1,
  pageSize: 20,
  total: 0,
  pages: 0
});

// 标签相关状态
const tagsList = ref([]);
const selectedTag = ref('');
const tagCounts = ref({}); // 存储每个标签的计数
const tagListRef = ref(null);
const canScrollLeft = ref(false);
const canScrollRight = ref(false);

// 转发相关状态
const isMultiSelectMode = ref(false);
const selectedNews = ref([]);
const showForwardModal = ref(false);
const forwardModalTitle = ref('');
const currentForwardNews = ref(null);
const forwardImageUrl = ref('');
const isGeneratingImage = ref(false);
const forwardPreviewRef = ref(null);
const previewVisible = ref(false);
const maxSelectCount = 4;

// 实时时间
const currentTime = ref(new Date());
let timeInterval = null;
const prevTimeValues = ref({
  hours: '',
  minutes: '',
  seconds: ''
});
const changedFields = ref({
  hours: false,
  minutes: false,
  seconds: false
});

// 预览数据相关状态
const previewDataVisible = ref(false);
const previewDataRef = ref(null);

// 更新时间函数
const updateTime = () => {
  const oldTime = currentTime.value;
  currentTime.value = new Date();

  // 获取新旧时间值
  const oldHours = String(oldTime.getHours()).padStart(2, '0');
  const oldMinutes = String(oldTime.getMinutes()).padStart(2, '0');
  const oldSeconds = String(oldTime.getSeconds()).padStart(2, '0');

  const newHours = String(currentTime.value.getHours()).padStart(2, '0');
  const newMinutes = String(currentTime.value.getMinutes()).padStart(2, '0');
  const newSeconds = String(currentTime.value.getSeconds()).padStart(2, '0');

  // 记录哪些字段发生了变化
  changedFields.value.hours = oldHours !== newHours;
  changedFields.value.minutes = oldMinutes !== newMinutes;
  changedFields.value.seconds = oldSeconds !== newSeconds;

  // 存储上一次的值，用于动画
  prevTimeValues.value = {
    hours: oldHours,
    minutes: oldMinutes,
    seconds: oldSeconds
  };

  // 添加动画类，然后在动画结束后移除
  if (changedFields.value.hours || changedFields.value.minutes || changedFields.value.seconds) {
    setTimeout(() => {
      changedFields.value.hours = false;
      changedFields.value.minutes = false;
      changedFields.value.seconds = false;
    }, 500); // 动画持续时间
  }
};

// 格式化时间显示
const formatCurrentTime = () => {
  const now = currentTime.value;
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
  const weekday = weekdays[now.getDay()];

  return {
    date: `${year}年${month}月${day}日`,
    time: `${hours}:${minutes}:${seconds}`,
    weekday
  };
};

// 记录加载失败的图片URL
const failedImages = ref(new Set());

// 记录已复制的新闻源
const copiedSources = ref(new Set());

// 获取类别标题
const getCategoryTitle = () => {
  switch (selectedCategory.value) {
    case '快讯':
      return 'Insuraim快讯';
    case '产品精选':
      return '产品精选';
    case '产品优惠':
      return '产品动态';
    case '行业新闻':
      return '行业新闻';
    default:
      return '保险资讯';
  }
};

// 获取类别描述
const getCategoryDescription = () => {
  switch (selectedCategory.value) {
    case '快讯':
      return '最新保险动态和快讯，及时了解行业最新信息';
    case '产品精选':
      return '精选优质保险产品，满足您的多样化保障需求';
    case '产品优惠':
      return '最新保险产品优惠和动态，把握投保良机';
    case '行业新闻':
      return '保险行业最新动态、政策解读和市场分析';
    default:
      return '了解保险行业最新资讯、产品动态和专业观点';
  }
};

// 获取类别图标
const getCategoryIcon = () => {
  switch (selectedCategory.value) {
    case '快讯':
      return 'mdi:flash';
    case '产品精选':
      return 'mdi:star';
    case '产品优惠':
      return 'mdi:tag-multiple';
    case '行业新闻':
      return 'mdi:newspaper';
    default:
      return 'mdi:newspaper-variant';
  }
};

// 保险相关图片备选
const insuranceImages = [
  // 握手（信任、合作）
  'https://images.unsplash.com/photo-1521791136064-7986c2920216?auto=format&fit=crop&w=800&q=80',
  // 家庭（保障、温馨）
  'https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?auto=format&fit=crop&w=800&q=80',
  // 保单/文件（保险合同）
  'https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?auto=format&fit=crop&w=800&q=80',
  // 金融办公（保险公司/理财）
  'https://images.unsplash.com/photo-1450101499163-c8848c66ca85?auto=format&fit=crop&w=800&q=80',
  // 团队（服务、专业）
  'https://images.unsplash.com/photo-1521737852567-6949f3f9f2b5?auto=format&fit=crop&w=800&q=80',
  // 安全锁（安全、保障）
  'https://images.unsplash.com/photo-1503676382389-4809596d5290?auto=format&fit=crop&w=800&q=80',
  // 医疗专业人员（医疗保险）
  'https://images.unsplash.com/photo-1579684385127-1ef15d508118?auto=format&fit=crop&w=800&q=80',
  // 房屋（房屋保险）
  'https://images.unsplash.com/photo-1570129477492-45c003edd2be?auto=format&fit=crop&w=800&q=80',
  // 汽车（车险）
  'https://images.unsplash.com/photo-1533473359331-0135ef1b58bf?auto=format&fit=crop&w=800&q=80',
  // 金融图表（投资理财保险）
  'https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?auto=format&fit=crop&w=800&q=80',
  // 老年夫妇（养老保险）
  'https://images.unsplash.com/photo-1476703993599-0035a21b17a9?auto=format&fit=crop&w=800&q=80',
  // 父母与孩子（儿童保险）
  'https://images.unsplash.com/photo-1540479859555-17af45c78602?auto=format&fit=crop&w=800&q=80',
  // 金融科技（保险科技）
  'https://images.unsplash.com/photo-1551288049-bebda4e38f71?auto=format&fit=crop&w=800&q=80'
];

// 图片缓存，保存每个新闻ID对应的图片URL
const newsImageMap = ref(new Map());

// 获取随机保险图片
const getRandomInsuranceImage = (id) => {
  // 如果已有缓存，直接返回
  if (newsImageMap.value.has(id)) {
    return newsImageMap.value.get(id);
  }

  // 生成新图片并缓存
  const idx = Math.floor(Math.random() * insuranceImages.length);
  const imageUrl = insuranceImages[idx];
  newsImageMap.value.set(id, imageUrl);
  return imageUrl;
};

// 获取新闻图片（有img用原图，无则用随机图，有缓存用缓存）
// const getNewsImage = (news) => {
//   // 有图片且未记录失败
//   if (news.img && (!failedImages.value || !failedImages.value.has(news.img))) {
//     return news.img;
//   }
//   // 无图片，使用随机图
//   if (!news.img) {
//     return getRandomInsuranceImage(news.id);
//   }
//   // 图片失败情况
//   return null;
// };

// 按回车搜索
const handleSearch = () => {
  // 清空标签选择，避免筛选冲突
  selectedTag.value = '';
  pagination.pageNum = 1;
  loadNewsList();
};

// 清除搜索
const clearSearch = () => {
  searchQuery.value = '';
  // 同时清空标签选择
  selectedTag.value = '';
  pagination.pageNum = 1;
  loadNewsList();
};

// 处理分页变化
const handlePageChange = (page) => {
  pagination.pageNum = page;
  loadNewsList();
};

// 提取所有唯一标签并计算每个标签的数量
const extractTags = () => {
  // 创建一个Set来存储唯一的标签
  const tagsSet = new Set();
  const counts = {};

  // 遍历所有新闻项的tags，计算每个标签的数量
  newsList.value.forEach(news => {
    if (news.tags) {
      const tags = news.tags.split(',');
      tags.forEach(tag => {
        const trimmedTag = tag.trim();
        if (trimmedTag) {
          tagsSet.add(trimmedTag);
          counts[trimmedTag] = (counts[trimmedTag] || 0) + 1;
        }
      });
    }
  });

  // 将Set转为数组并按频率排序
  const sortedTags = Array.from(tagsSet).sort((a, b) => {
    // 首先按照频率降序排序
    const countDiff = (counts[b] || 0) - (counts[a] || 0);
    // 如果频率相同，则按字母顺序排序
    return countDiff !== 0 ? countDiff : a.localeCompare(b);
  });

  tagsList.value = sortedTags;
  if (selectedCategory.value === '行业新闻') {
    tagsList.value = tagsList.value.filter(tag => {
      const trimmedTag = tag.trim();
      return !['行业新闻', '市场分析', '产品动态'].includes(trimmedTag);
    });
  }
  tagCounts.value = counts;

  // 在下一个渲染周期检查是否需要显示滚动指示器
  nextTick(() => {
    checkScroll();
  });
};

// 滚动标签列表
const scrollTags = (direction) => {
  if (!tagListRef.value) return;

  const container = tagListRef.value;
  const scrollAmount = container.clientWidth / 2;

  if (direction === 'left') {
    container.scrollBy({
      left: -scrollAmount,
      behavior: 'smooth'
    });
  } else {
    container.scrollBy({
      left: scrollAmount,
      behavior: 'smooth'
    });
  }

  // 滚动后检查是否需要显示滚动指示器
  setTimeout(() => {
    checkScroll();
  }, 300);
};

// 检查是否可以滚动
const checkScroll = () => {
  if (!tagListRef.value) return;

  const container = tagListRef.value;

  // 判断是否可以向左滚动（当前滚动位置大于0）
  canScrollLeft.value = container.scrollLeft > 0;

  // 判断是否可以向右滚动（总宽度减去可见宽度大于当前滚动位置）
  canScrollRight.value = container.scrollWidth - container.clientWidth > container.scrollLeft + 1;
};

// 监听窗口大小变化，更新滚动状态
const handleResize = () => {
  checkScroll();
};

// 处理标签点击事件
const handleTagClick = (tag, event) => {
  // 如果点击的是已选中的标签或"全部"按钮，则清除筛选
  if (selectedTag.value === tag || tag === '') {
    selectedTag.value = '';
  } else {
    // 否则设置选中标签
    selectedTag.value = tag;
  }

  // 重置页码并重新加载新闻列表
  pagination.pageNum = 1;
  loadNewsList();
};

// 加载资讯列表
const loadNewsList = async () => {
  loading.value = true;
  try {
    const params = {
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
      title: searchQuery.value || undefined
    };

    // 应用类别和标签筛选
    if (selectedTag.value) {
      // 如果选中了特定标签，优先使用标签筛选
      params.tags = selectedTag.value;
    } else if (selectedCategory.value !== 'all') {
      // 否则如果有选中类别且不是"全部"，使用类别筛选
      params.tags = selectedCategory.value;
    }

    const result = await newsAPI.getNewsList(params);

    // 处理返回的数据
    newsList.value = result.records.map(item => {
      // 如果是PDF类型，使用content（PDF URL）作为唯一标识符
      if (item.content && item.content.toLowerCase().endsWith('.pdf')) {
        return {
          id: item.content, // 使用PDF URL作为ID
          title: item.title || 'PDF文件',
          content: item.content,
          tags: item.tags || '',
          pubDate: item.pubDate || item.createTime || Math.floor(Date.now() / 1000),
          link: item.link || '',
          img: item.img || '',
          pdf: item.content, // 设置pdf字段
          categoryName: item.tags?.split(',')[0] || '未分类',
          source: item.source || 'HKT' // 添加来源字段
        };
      }

      // 普通新闻的数据验证和默认值处理
      if (!item.id) {
        console.error('新闻数据缺少ID:', item);
        return null;
      }

      return {
        id: item.id,
        title: item.title || '无标题',
        content: item.content || '',
        tags: item.tags || '',
        pubDate: item.pubDate || item.createTime || Math.floor(Date.now() / 1000),
        link: item.link || '',
        img: item.img || '',
        pdf: item.pdf || '',
        categoryName: item.tags?.split(',')[0] || '未分类',
        source: item.source || 'HKT' // 添加来源字段
      };
    }).filter(Boolean); // 过滤掉无效的数据

    // 更新分页信息
    pagination.total = result.total;
    pagination.pages = result.pages;

    // 提取标签列表
    extractTags();
  } catch (error) {
    console.error('加载资讯失败:', error);
    message.error('加载资讯列表失败');
    newsList.value = []; // 清空列表
  } finally {
    loading.value = false;
  }
};

// 跳转到新闻详情页
const goToNewsDetail = (news) => {
  // 数据验证
  if (!news) {
    message.error('新闻数据无效');
    return;
  }

  // 获取PDF URL
  const pdfUrl = news.pdf || (news.content && news.content.toLowerCase().endsWith('.pdf') ? news.content : null);

  // 传递必要的参数到详情页
  router.push({
    name: 'NewsDetail',
    params: { id: news.id || pdfUrl }, // 如果是PDF，使用URL作为ID
    query: {
      title: news.title || '',
      content: news.content || '',
      tags: news.tags || '',
      pubDate: news.pubDate || news.createTime || '',
      link: news.link || '',
      img: news.img || '',
      pdf: pdfUrl || ''
    }
  });
};

// 组件挂载时加载数据
onMounted(() => {
  // 确保failedImages已正确初始化
  if (!failedImages.value) {
    failedImages.value = new Set();
    console.log('已初始化failedImages');
  }

  // 启动实时时间更新
  updateTime();
  timeInterval = setInterval(updateTime, 1000);

  // 检查路由参数是否包含分类信息
  if (route.query.category) {
    // 设置选中分类
    selectedCategory.value = route.query.category;
  }

  // 加载新闻列表
  loadNewsList();

  // 添加resize事件监听
  window.addEventListener('resize', handleResize);
});

// 组件卸载时清除定时器
onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval);
    timeInterval = null;
  }

  // 移除resize事件监听
  window.removeEventListener('resize', handleResize);
});

// 监听路由变化，当路由查询参数变化时重新加载数据
watch(
  () => route.query.category,
  (newCategory) => {
    if (newCategory) {
      selectedCategory.value = newCategory;
    } else {
      // 如果没有category参数，则显示全部
      selectedCategory.value = 'all';
    }
    // 清空已选标签，避免筛选冲突
    selectedTag.value = '';
    // 重置页码并加载数据
    pagination.pageNum = 1;
    loadNewsList();
  }
);

// 处理预览数据
const handlePreviewData = () => {
  console.log('handlePreviewData called', currentForwardNews.value);
  if (!currentForwardNews.value) {
    message.error('没有可预览的数据');
    return;
  }
  previewDataVisible.value = true;
};

// 转发快讯
const forwardNews = async (news) => {
  currentForwardNews.value = news;
  isGeneratingImage.value = true;

  try {
    await nextTick();
    const imageUrl = await convertElementToImage(forwardPreviewRef.value.previewRef);
    if (imageUrl) {
      forwardImageUrl.value = imageUrl;
      previewVisible.value = true;
    }
  } catch (error) {
    console.error('生成图片失败:', error);
    message.error('生成图片失败，请重试');
  } finally {
    isGeneratingImage.value = false;
  }
};

// 处理批量转发
const handleBatchForward = async (selectedNews) => {
  currentForwardNews.value = selectedNews;
  isGeneratingImage.value = true;

  try {
    await nextTick();
    const imageUrl = await convertElementToImage(forwardPreviewRef.value.previewRef);
    if (imageUrl) {
      forwardImageUrl.value = imageUrl;
      previewVisible.value = true;
    }
  } catch (error) {
    console.error('生成图片失败:', error);
    message.error('生成图片失败，请重试');
  } finally {
    isGeneratingImage.value = false;
  }
};

// 复制图片到剪贴板
const copyImageToClipboard = async (imageUrl) => {
  try {
    const img = await fetch(imageUrl);
    const blob = await img.blob();
    const data = [new ClipboardItem({ 'image/png': blob })];
    await navigator.clipboard.write(data);
    message.success('图片已复制到剪贴板，可直接粘贴分享');
  } catch (error) {
    console.error('复制图片失败:', error);
    message.error('复制图片失败，请尝试保存图片后分享');
  }
};

// 下载图片
const saveBas64Img = (url, name) => {
  let base64 = url.toString()
  let byteCharacters = atob(
    base64.replace(/^data:image\/(png|jpeg|jpg);base64,/, "")
  )
  let byteNumbers = new Array(byteCharacters.length)
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i)
  }
  let byteArray = new Uint8Array(byteNumbers)
  let blob = new Blob([byteArray], {
    type: undefined
  })
  let aLink = document.createElement("a")
  aLink.download = `${name}.png` //这里写保存时的图片名称
  aLink.href = URL.createObjectURL(blob)
  aLink.click()
}


// 将HTML元素转换为图片
const convertElementToImage = async (element) => {
  if (!element) return null;

  try {
    loading.value = true;
    await nextTick();
    const canvas = await html2canvas(element, {
      backgroundColor: '#ffffff',
      scale: 1.5,
      useCORS: true,
      logging: false,
      allowTaint: true
    });

    return canvas.toDataURL('image/png');
  } catch (error) {
    console.error('生成图片失败:', error);
    message.error('生成图片失败，请重试');
    return null;
  } finally {
    loading.value = false;
  }
};

// 切换多选模式
const toggleMultiSelectMode = () => {
  isMultiSelectMode.value = !isMultiSelectMode.value;
  if (!isMultiSelectMode.value) {
    // 退出多选模式时清空选择
    selectedNews.value = [];
  }
};

// 选择或取消选择新闻
const toggleSelectNews = (news) => {
  if (!isMultiSelectMode.value) return;

  const index = selectedNews.value.findIndex(item => item.id === news.id);
  if (index >= 0) {
    // 已选中，取消选择
    selectedNews.value.splice(index, 1);
  } else {
    // 未选中，添加到选中列表
    if (selectedNews.value.length >= maxSelectCount) {
      message.warning(`最多只能选择${maxSelectCount}条快讯`);
      return;
    }
    selectedNews.value.push(news);
  }
};

// 检查新闻是否被选中
const isNewsSelected = (newsId) => {
  return selectedNews.value.some(item => item.id === newsId);
};
</script>

<style scoped>
/* 标题区域样式 */
.title-section {
  transition: all 0.3s ease;
}

.title-section:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.page-title {
  position: relative;
  display: inline-block;
  color: white;
}

.page-title::after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: white;
  border-radius: 3px;
}

.page-description {
  max-width: 600px;
  opacity: 0.9;
}

/* 实时时间样式 */
.date-time-container {
  display: flex;
  flex-direction: column;
  padding: 8px 12px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #bae6fd;
  transition: all 0.3s ease;
}

.date-time-container:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.date-display {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 15px;
  color: #334155;
}

.time-display {
  display: flex;
  align-items: center;
  font-size: 20px;
  font-weight: 600;
  color: #0284c7;
}

.date {
  font-weight: 500;
}

.weekday {
  color: #64748b;
  font-size: 14px;
}

.time-text {
  display: flex;
  align-items: center;
}

.hours,
.minutes,
.seconds {
  min-width: 28px;
  display: inline-block;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
}

.hours::after,
.minutes::after,
.seconds::after {
  content: attr(data-value);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  opacity: 0;
  transform: translateY(-100%);
  transition: all 0.3s ease;
}

.hours.changed::after,
.minutes.changed::after,
.seconds.changed::after {
  animation: number-change 0.5s ease forwards;
}

@keyframes number-change {
  0% {
    opacity: 0;
    transform: translateY(-100%) scale(0.8);
  }

  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.time-separator {
  margin: 0 2px;
  opacity: 0.7;
}

.seconds {
  color: #0369a1;
}

.pulse {
  animation: pulse-animation 1s infinite;
}

@keyframes pulse-animation {
  0% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.main-container {
  min-height: calc(100vh - 390px);
  /* 减去顶部搜索栏的高度 */
  height: calc(100vh - 390px);
}

.news-list-container {
  min-height: 300px;
  /* 确保始终有一定高度 */
  max-height: calc(100vh - 390px);
  /* 减去顶部搜索栏、页码区域等高度 */
}

.pagination-container {
  flex-shrink: 0;
  /* 防止分页区域被压缩 */
  background-color: var(--bg-secondary);
  border: none;
  filter: drop-shadow(0 0 1px var(--primary-500));
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.aspect-w-16 {
  position: relative;
  padding-bottom: 56.25%;
}

.aspect-w-16>* {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.news-tags {
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 6px;
  margin-right: 1rem;
}

.news-tag {
  background: #2563eb;
  color: #fff;
  font-size: 12px;
  padding: 2px 10px;
  border-radius: 12px;
  line-height: 1.5;
  white-space: nowrap;
  transition: background 0.2s;
}

.news-tag:not(:last-child) {
  margin-right: 1rem;
}

.news-card {
  padding: 0;
  margin: 0;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.04);
  border-radius: 18px;
  transition: all 0.2s ease;
  border: 1px solid var(--border-light);
}

.news-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.07);
}

.news-card .aspect-w-16 {
  border-bottom: 1px solid #f3f4f6;
  background: #f9fafb;
}

.news-card .p-4 {
  padding: 22px 28px 18px 28px;
}

.news-card h3 {
  margin-bottom: 10px;
}

.news-card .news-tags {
  margin-bottom: 0;
}

.news-card .text-sm {
  margin-bottom: 10px;
}

.news-card .flex.items-center.justify-between {
  margin-top: 8px;
}

.news-card-row {
  flex-direction: row;
  height: 200px;
  align-items: stretch;
}

.news-card-img-wrap {
  width: 260px;
  min-width: 220px;
  max-width: 320px;
  height: 100%;
  display: flex;
  align-items: stretch;
  background: #f9fafb;
  border-right: 1px solid #f3f4f6;
}

.news-card-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 0;
}

.news-card-img-placeholder {
  color: #bdbdbd;
  font-size: 15px;
  text-align: center;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.news-card-content {
  padding: 22px 28px 18px 28px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}

.news-card-content h3 {
  margin-bottom: 10px;
}

.news-card-content .news-tags {
  margin-bottom: 8px;
}

.news-card-content .text-sm {
  margin-bottom: 10px;
}

.news-card-content .flex.items-center.justify-between {
  margin-top: 8px;
}

.news-card-img-summary {
  color: #1e293b;
  font-size: 18px;
  font-weight: 500;
  text-align: center;
  background: linear-gradient(135deg, #e0e7ff 0%, #f1f5f9 100%);
  padding: 0 16px;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  overflow: hidden;
  line-height: 1.7;
  word-break: break-all;
  box-shadow: 0 2px 8px 0 rgba(37, 99, 235, 0.06) inset;
  border: 1.5px solid #e0e7ff;
}

.news-img-summary-icon {
  margin-bottom: 10px;
}

.icon-insurance {
  font-size: 32px;
  color: #2563eb;
  filter: drop-shadow(0 2px 4px #a5b4fc88);
}

.news-img-summary-text {
  font-size: 18px;
  font-weight: 600;
  color: #334155;
  text-shadow: 0 1px 2px #fff, 0 2px 8px #a5b4fc22;
  padding: 0 4px;
  max-height: 3.4em;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.news-detail-container {
  padding: 0;
}

.news-detail-img-container {
  position: relative;
  width: 100%;
  height: 300px;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.news-detail-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.news-detail-content {
  line-height: 1.8;
  font-size: 16px;
  color: #333;
  padding: 0 2px;
}

.news-card-pdf {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1.5px solid #bae6fd;
}

.icon-pdf {
  font-size: 32px;
  color: #0284c7;
  filter: drop-shadow(0 2px 4px #7dd3fc88);
}

.news-tag-pdf {
  background: #0284c7;
}

/* 快讯模块样式 */
.flash-news-container {
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.flash-news-item {
  padding: 20px 24px;
  position: relative;
  background-color: #fff;
  transition: background-color 0.2s ease;
}

.flash-news-item:hover {
  background-color: #f8fafc;
}

/* 多选模式样式 */
.flash-news-item-selectable {
  cursor: pointer;
  padding-left: 50px;
  /* 为复选框留出空间 */
}

.flash-news-item-selected {
  background-color: #f0f9ff;
  border-left: 4px solid #2563eb;
}

.flash-news-checkbox {
  position: absolute;
  left: 16px;
  top: 24px;
}

.checkbox {
  width: 22px;
  height: 22px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  background-color: white;
}

.checkbox.checked {
  background-color: #2563eb;
  border-color: #2563eb;
}

.checkbox-icon {
  color: white;
  font-size: 16px;
}

.confirm-btn {
  transition: all 0.2s ease;
}

.confirm-btn:hover {
  background-color: #16a34a;
  transform: translateY(-1px);
}

.cancel-btn {
  transition: all 0.2s ease;
}

.cancel-btn:hover {
  background-color: #64748b;
  transform: translateY(-1px);
}

.flash-news-datetime {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
  font-weight: 500;
}

.flash-news-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.flash-news-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  flex: 1;
  line-height: 1.4;
}

.flash-news-source {
  font-size: 14px;
  color: #666;
  padding: 2px 8px;
  margin-left: 12px;
  white-space: nowrap;
  display: flex;
  align-items: center;
  background-color: #f8fafc;
  border-radius: 4px;
  transition: all 0.2s ease;
  position: relative;
  border: 1px solid transparent;
}

.flash-news-source.copied {
  background-color: #d1fae5;
  color: #059669;
  border-color: #a7f3d0;
}

.flash-news-source:hover {
  background-color: #e0f2fe;
  color: #0284c7;
  border-color: #bae6fd;
}

.flash-news-source.copied:hover {
  background-color: #a7f3d0;
  color: #047857;
  border-color: #6ee7b7;
}

.flash-news-source .copy-icon {
  transition: transform 0.2s ease;
}

.flash-news-source:hover .copy-icon {
  transform: scale(1.2);
  color: #0284c7;
}

.flash-news-source::after {
  content: '点击复制';
  position: absolute;
  bottom: -22px;
  left: 50%;
  transform: translateX(-50%) scale(0.8);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  opacity: 0;
  transition: all 0.2s ease;
  pointer-events: none;
  z-index: 10;
  white-space: nowrap;
}

.flash-news-source:hover::after {
  opacity: 1;
  transform: translateX(-50%) scale(1);
}

.flash-news-content {
  font-size: 15px;
  line-height: 1.6;
  color: #444;
  margin-bottom: 16px;
  text-align: justify;
  white-space: pre-line;
}

.flash-news-content p {
  margin-bottom: 0.8em;
}

.flash-news-content p:last-child {
  margin-bottom: 0;
}

.flash-news-footer {
  display: flex;
  justify-content: flex-end;
}

.forward-btn {
  background-color: #f0f9ff;
  color: #0284c7;
  border: 1px solid #bae6fd;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
}

.forward-btn:hover {
  background-color: #e0f2fe;
  border-color: #7dd3fc;
}

.batch-forward-btn {
  background-color: #2563eb;
  transition: all 0.2s;
  font-weight: 500;
  font-size: 14px;
}

.batch-forward-btn:hover {
  background-color: #1d4ed8;
}

.flash-news-divider {
  height: 1px;
  background-color: #f0f0f0;
  margin: 10px -24px 0;
}

/* 转发预览样式 */
.forward-preview {
  width: 500px;
  background-color: white;
  border-radius: 0px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.forward-card {
  background-color: white;
  border-radius: 0px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.forward-card-header {
  padding: 16px 20px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.forward-logo {
  display: flex;
  align-items: center;
}

.logo-img {
  width: 28px;
  height: 28px;
  object-fit: contain;
  margin-right: 8px;
  background-color: white;
  border-radius: 50%;
  padding: 2px;
}

.logo-text {
  font-size: 16px;
  font-weight: 600;
  color: white;
}

.forward-date {
  font-size: 14px;
  opacity: 0.9;
}

.forward-card-content {
  padding: 20px;
  flex: 1;
}

.forward-title {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 12px;
  line-height: 1.4;
}

.forward-text {
  font-size: 15px;
  line-height: 1.6;
  color: #334155;
  margin-bottom: 16px;
  white-space: pre-line;
}

.forward-source {
  font-size: 14px;
  color: #64748b;
  padding-top: 12px;
  border-top: 1px dashed #e2e8f0;
}

.forward-card-footer {
  padding: 16px 20px;
  background-color: #f8fafc;
  border-top: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.qrcode-placeholder {
  width: 80px;
  height: 80px;
  background-color: #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

.qrcode-text {
  font-size: 12px;
  color: #64748b;
  text-align: center;
}

.forward-brand {
  font-size: 14px;
  color: #64748b;
  flex: 1;
  text-align: right;
  margin-left: 16px;
}

/* 批量转发样式 */
.batch-card {
  max-width: 550px;
}

.batch-content {
  padding: 12px;
}

.batch-news-item {
  display: flex;
  padding: 12px;
  border-bottom: 1px solid #e2e8f0;
  margin-bottom: 8px;
}

.batch-news-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.batch-news-index {
  width: 60px;
  height: 24px;
  background-color: #2563eb;
  color: white;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  margin-right: 12px;
  flex-shrink: 0;
  font-size: 13px;
}

.batch-news-content {
  flex: 1;
}

.batch-news-title {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8px;
  line-height: 1.4;
}

.batch-news-text {
  font-size: 14px;
  line-height: 1.6;
  color: #334155;
  margin-bottom: 8px;
}

.batch-news-source {
  font-size: 13px;
  color: #64748b;
}

/* 加载状态样式 */
:deep(.ant-spin-spinning) {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
}

:deep(.ant-image-preview-operations) {
  background: rgba(0, 0, 0, 0.8);
}

:deep(.ant-image-preview-img) {
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
}

/* 预览数据样式 */
.preview-data-modal {
  :deep(.ant-modal-content) {
    border-radius: 12px;
    overflow: hidden;
  }

  :deep(.ant-modal-header) {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 24px;
  }

  :deep(.ant-modal-body) {
    padding: 0;
  }
}

.preview-data-container {
  background-color: #ffffff;
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

.preview-data-container :deep(.forward-preview) {
  position: static !important;
  left: auto !important;
  width: 100% !important;
  box-shadow: none;
}

/* 标签列表样式 */
.tag-list-container {
  position: relative;
  margin: 0.75rem 0;
  padding: 0 1.5rem;
}

.tag-list {
  padding: 0.75rem 0.5rem;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  gap: 10px;
  min-height: 54px;
}

.tag-scroll-indicator {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 32px;
  height: 32px;
  cursor: pointer;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 2;
  transition: all 0.2s ease;
}

.tag-scroll-left {
  left: 3px;
}

.tag-scroll-right {
  right: 3px;
}

.tag-scroll-indicator:hover {
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-50%) scale(1.1);
}

.tag-scroll-indicator:active {
  transform: translateY(-50%) scale(0.95);
}

.tag-list-scroll {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE/Edge */
}

.tag-list-scroll::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari, Opera */
}

/* 基础标签样式 */
:deep(.ant-tag) {
  margin-right: 0;
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  font-size: 14px;
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  cursor: pointer;
  user-select: none;
  white-space: nowrap;
}

.tag-all {
  background-color: #f5f5f5;
  color: #333;
  font-weight: 500;
  border: 1px solid #f0f0f0;
}

.tag-item {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #f0f0f0;
}

:deep(.ant-tag):hover {
  transform: translateY(-2px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}

:deep(.ant-tag):active {
  transform: translateY(0);
}

.tag-content {
  padding: 0 2px;
}

.tag-count {
  background-color: rgba(0, 0, 0, 0.06);
  color: #666;
  font-size: 12px;
  padding: 1px 6px;
  border-radius: 10px;
  margin-left: 6px;
  font-weight: 500;
}

.selected-tag {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  color: white !important;
  border-color: transparent !important;
  font-weight: 500;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.25);
}

.selected-tag:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #69c0ff 100%);
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.3);
}

.selected-tag .tag-count {
  background-color: rgba(255, 255, 255, 0.3);
  color: white;
}

/* 波纹效果 */
:deep(.ant-tag) {
  position: relative;
  overflow: hidden;
}

.tag-ripple {
  position: absolute;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 50%;
  transform: scale(0);
  animation: ripple 0.6s linear;
  pointer-events: none;
}

@keyframes ripple {
  to {
    transform: scale(4);
    opacity: 0;
  }
}
</style>