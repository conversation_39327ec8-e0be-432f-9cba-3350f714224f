<template>
    <div class="container mx-auto py-6 px-4">
        <!-- 页面标题 -->
        <div
            class="title-section mb-6 p-6 rounded-lg shadow-md text-white bg-gradient-to-r from-cyan-500 via-cyan-600 to-cyan-700">
            <div class="flex items-center">
                <Icon icon="mdi:cloud" class="text-4xl mr-3" />
                <h1 class="text-2xl font-bold page-title text-white">个人资料云盘</h1>
            </div>
            <p class="mt-2 page-description">
                管理和存储您的个人文件资料，随时随地安全访问
            </p>
        </div>

        <!-- 加载中状态 -->
        <div v-if="loading" class="flex flex-col items-center justify-center py-20">
            <a-spin size="large" />
            <p class="mt-4 text-gray-500">正在加载云盘数据...</p>
        </div>

        <div v-else>
            <!-- 未开通云盘提示 -->
            <div v-if="!cloudInfo" class="bg-white rounded-lg shadow-md p-10 mb-6 text-center">
                <Icon icon="mdi:cloud-off-outline" class="w-20 h-20 text-gray-400 mx-auto mb-4" />
                <h2 class="text-xl font-semibold text-gray-700 mb-2">云盘未开通</h2>
                <p class="text-gray-500 mb-6">您的个人云盘服务尚未开通，请联系管理员为您开通服务。</p>
                <a-button type="primary" @click="checkCloudInfo">刷新状态</a-button>
            </div>

            <template v-else>
                <!-- 统计卡片 -->
                <a-card class="mb-6 rounded-lg" :bordered="false">
                    <h1 class="text-2xl font-bold mb-4">存储概览</h1>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <!-- 存储容量卡片 -->
                        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-cyan-500">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-gray-500 text-sm">存储容量</p>
                                    <p class="text-2xl font-bold">{{ formatFileSize(storageTotal) }}</p>
                                </div>
                                <div class="bg-cyan-100 p-2 rounded-full">
                                    <Icon icon="mdi:database" class="text-2xl text-cyan-500" />
                                </div>
                            </div>
                            <div class="mt-2 text-xs text-gray-500">
                                <span class="flex items-center">
                                    <Icon icon="mdi:information-outline" class="mr-1" />
                                    您的云盘总存储空间
                                </span>
                            </div>
                        </div>

                        <!-- 已用空间卡片 -->
                        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-blue-500">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-gray-500 text-sm">已用空间</p>
                                    <p class="text-2xl font-bold">{{ formatFileSize(storageUsed) }}</p>
                                </div>
                                <div class="bg-blue-100 p-2 rounded-full">
                                    <Icon icon="mdi:harddisk" class="text-2xl text-blue-500" />
                                </div>
                            </div>
                            <div class="mt-2 text-xs text-gray-500">
                                <span class="flex items-center">
                                    <Icon icon="mdi:information-outline" class="mr-1" />
                                    已使用: {{ storageUsagePercent }}%
                                </span>
                            </div>
                        </div>

                        <!-- 文件总数卡片 -->
                        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-green-500">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-gray-500 text-sm">文件总数</p>
                                    <p class="text-2xl font-bold">{{ files.length }}</p>
                                </div>
                                <div class="bg-green-100 p-2 rounded-full">
                                    <Icon icon="mdi:file-multiple" class="text-2xl text-green-500" />
                                </div>
                            </div>
                            <div class="mt-2 text-xs text-gray-500">
                                <span class="flex items-center">
                                    <Icon icon="mdi:information-outline" class="mr-1" />
                                    当前目录下所有文件数量
                                </span>
                            </div>
                        </div>

                        <!-- 文件夹总数卡片 -->
                        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-amber-500">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-gray-500 text-sm">文件夹总数</p>
                                    <p class="text-2xl font-bold">{{ folders.length }}</p>
                                </div>
                                <div class="bg-amber-100 p-2 rounded-full">
                                    <Icon icon="mdi:folder-multiple" class="text-2xl text-amber-500" />
                                </div>
                            </div>
                            <div class="mt-2 text-xs text-gray-500">
                                <span class="flex items-center">
                                    <Icon icon="mdi:information-outline" class="mr-1" />
                                    当前目录下所有文件夹数量
                                </span>
                            </div>
                        </div>
                    </div>
                </a-card>

                <!-- 面包屑导航 -->
                <div class="mb-4 bg-white rounded-lg shadow-md p-4  mt-4">
                    <div class="flex items-center text-sm overflow-x-auto">
                        <template v-for="(crumb, index) in breadcrumbs" :key="index">
                            <span class="cursor-pointer text-blue-500 hover:text-blue-700 whitespace-nowrap"
                                @click="navigateToFolder({ id: crumb.id })">
                                {{ crumb.name }}
                            </span>
                            <span v-if="index < breadcrumbs.length - 1" class="mx-2 text-gray-500">/</span>
                        </template>
                    </div>
                </div>

                <!-- 搜索和筛选区域 -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                        <!-- 搜索框 -->
                        <div class="relative flex-grow md:max-w-md">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <Icon icon="mdi:magnify" class="h-5 w-5 text-gray-400" />
                            </div>
                            <input type="text" placeholder="搜索文件..." v-model="searchQuery"
                                class="block w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg text-sm focus:ring-blue-500 focus:border-blue-500" />
                        </div>

                        <!-- 筛选选项 -->
                        <div class="flex flex-wrap gap-2">
                            <a-button type="primary" class="btn-with-icon" @click="openUploadModal">
                                <Icon icon="mdi:upload" class="btn-icon" />
                                <span class="btn-text">上传文件</span>
                            </a-button>
                        </div>
                    </div>
                </div>

                <!-- 容量使用统计 -->
                <div class="mb-6 bg-white rounded-lg p-4 shadow-md">
                    <h3 class="text-base font-medium mb-2">存储空间</h3>
                    <div class="w-full bg-gray-200 rounded-full h-2.5">
                        <div class="bg-gradient-to-r from-cyan-500 to-blue-600 h-2.5 rounded-full"
                            :style="`width: ${storageUsagePercent}%`"></div>
                    </div>
                    <div class="flex justify-between text-sm mt-2 text-gray-600">
                        <span>已使用: {{ formatFileSize(storageUsed) }}</span>
                        <span>总容量: {{ formatFileSize(storageTotal) }}</span>
                    </div>
                </div>

                <div class="grid grid-cols-1 gap-6">
                    <!-- 文件和文件夹列表 -->
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-lg font-semibold">文件和文件夹</h2>
                            <div class="flex space-x-2">
                                <a-button type="primary" class="btn-with-icon" @click="openFolderModal">
                                    <Icon icon="mdi:folder-plus" class="btn-icon" />
                                    <span class="btn-text">新建文件夹</span>
                                </a-button>
                                <a-button-group class="ml-2">
                                    <a-button type="text" :class="viewMode === 'grid' ? 'text-blue-500' : ''"
                                        @click="viewMode = 'grid'">
                                        <Icon icon="mdi:view-grid" />
                                    </a-button>
                                    <a-button type="text" :class="viewMode === 'list' ? 'text-blue-500' : ''"
                                        @click="viewMode = 'list'">
                                        <Icon icon="mdi:format-list-bulleted" />
                                    </a-button>
                                </a-button-group>
                                <a-dropdown>
                                    <template #overlay>
                                        <a-menu>
                                            <a-menu-item key="1">按名称排序</a-menu-item>
                                            <a-menu-item key="2">按更新时间排序</a-menu-item>
                                        </a-menu>
                                    </template>
                                    <a-button type="text" class="btn-with-icon">
                                        <Icon icon="mdi:sort" class="btn-icon" />
                                        <span class="btn-text">排序</span>
                                    </a-button>
                                </a-dropdown>
                            </div>
                        </div>

                        <!-- 网格视图 -->
                        <div v-if="viewMode === 'grid'" class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                            <!-- 文件夹项 -->
                            <div v-for="folder in filteredFolders" :key="`folder-${folder.id}`"
                                class="flex flex-col  items-center p-4 hover:bg-blue-50 rounded-lg cursor-pointer transition-all transform hover:-translate-y-1 duration-200"
                                @click="navigateToFolder(folder)">
                                <div class="w-12 h-12 flex items-center justify-center mb-2 relative group">
                                    <Icon icon="mdi:folder" class="w-12 h-12 text-blue-400" />
                                    <div
                                        class="absolute top-0 right-0 opacity-0 group-hover:opacity-100 transition-opacity">
                                        <a-dropdown :trigger="['click']" @click.stop>
                                            <template #overlay>
                                                <a-menu>
                                                    <a-menu-item key="1">重命名</a-menu-item>
                                                    <a-menu-item key="2"
                                                        @click="confirmDeleteFolder(folder)">删除</a-menu-item>
                                                </a-menu>
                                            </template>
                                            <button class="bg-white rounded-full p-1 shadow hover:bg-gray-100">
                                                <Icon icon="mdi:dots-vertical" class="text-gray-600" />
                                            </button>
                                        </a-dropdown>
                                    </div>
                                </div>
                                <span class="text-sm text-center font-medium">{{ folder.name }}</span>
                            </div>

                            <!-- 文件项 -->
                            <div v-for="file in filteredFiles" :key="`file-${file.id}`"
                                class="flex mt-4 flex-col items-center p-4 hover:bg-blue-50 rounded-lg cursor-pointer transition-all transform hover:-translate-y-1 duration-200"
                                @dblclick="previewFile(file)">
                                <div class="w-12 h-12 flex items-center justify-center mb-2 relative group">
                                    <Icon :icon="getFileIcon(file.fileType)" class="w-12 h-12"
                                        :class="getFileIconColor(file.fileType)" />
                                    <div
                                        class="absolute top-0 right-0 opacity-0 group-hover:opacity-100 transition-opacity">
                                        <a-dropdown :trigger="['click']" @click.stop>
                                            <template #overlay>
                                                <a-menu>
                                                    <a-menu-item key="1" @click="downloadFile(file)">下载</a-menu-item>
                                                    <a-menu-item key="2" @click="previewFile(file)">预览</a-menu-item>
                                                    <a-menu-item key="4">重命名</a-menu-item>
                                                    <a-menu-item key="5"
                                                        @click="confirmDeleteFile(file)">删除</a-menu-item>
                                                </a-menu>
                                            </template>
                                            <button class="bg-white rounded-full p-1 shadow hover:bg-gray-100">
                                                <Icon icon="mdi:dots-vertical" class="text-gray-600" />
                                            </button>
                                        </a-dropdown>
                                    </div>

                                </div>
                                <span class="text-sm text-center font-medium truncate w-full">{{ file.name }}</span>
                                <span class="text-xs text-gray-500">{{ formatFileSize(file.size) }}</span>
                            </div>
                        </div>

                        <!-- 列表视图 -->
                        <div v-else-if="viewMode === 'list'" class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col"
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            名称
                                        </th>
                                        <th scope="col"
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            类型
                                        </th>

                                        <th scope="col"
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            大小/修改日期
                                        </th>
                                        <th scope="col"
                                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            操作
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <!-- 文件夹行 -->
                                    <tr v-for="folder in filteredFolders" :key="`folder-${folder.id}`"
                                        class="hover:bg-gray-50 cursor-pointer" @click="navigateToFolder(folder)">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center">
                                                    <Icon icon="mdi:folder" class="h-6 w-6 text-blue-400" />
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">{{ folder.name }}
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">文件夹</div>
                                        </td>

                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-500">{{ formatDate(folder.updateAt) }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium" @click.stop>
                                            <div class="flex space-x-2">
                                                <a-dropdown>
                                                    <template #overlay>
                                                        <a-menu>
                                                            <a-menu-item key="1">重命名</a-menu-item>
                                                            <a-menu-item key="2"
                                                                @click="confirmDeleteFolder(folder)">删除</a-menu-item>
                                                        </a-menu>
                                                    </template>
                                                    <a class="text-gray-600 hover:text-gray-900">
                                                        <Icon icon="mdi:dots-horizontal" />
                                                    </a>
                                                </a-dropdown>
                                            </div>
                                        </td>
                                    </tr>

                                    <!-- 文件行 -->
                                    <tr v-for="file in filteredFiles" :key="`file-${file.id}`" class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center">
                                                    <Icon :icon="getFileIcon(file.fileType)" class="h-6 w-6"
                                                        :class="getFileIconColor(file.fileType)" />
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">{{ file.name }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ getFileTypeName(file.fileType) }}
                                            </div>
                                        </td>

                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-500">{{ formatFileSize(file.size) }}</div>
                                            <div class="text-xs text-gray-400">{{ formatDate(file.updateAt) }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <a @click.prevent="downloadFile(file)"
                                                    class="link-with-icon text-blue-600 hover:text-blue-900">
                                                    <Icon icon="mdi:download" class="link-icon" />
                                                    <span class="link-text">下载</span>
                                                </a>
                                                <a @click.prevent="previewFile(file)"
                                                    class="link-with-icon text-green-600 hover:text-green-900">
                                                    <Icon icon="mdi:eye" class="link-icon" />
                                                    <span class="link-text">预览</span>
                                                </a>
                                                <a-dropdown>
                                                    <template #overlay>
                                                        <a-menu>
                                                            <a-menu-item key="1">重命名</a-menu-item>
                                                            <a-menu-item key="2">移动到</a-menu-item>
                                                            <a-menu-item key="3"
                                                                @click="openShareModal(file)">分享</a-menu-item>
                                                            <a-menu-item key="4"
                                                                @click="previewFile(file)">预览</a-menu-item>
                                                            <a-menu-item key="5" danger
                                                                @click="confirmDeleteFile(file)">删除</a-menu-item>
                                                        </a-menu>
                                                    </template>
                                                    <a class="text-gray-600 hover:text-gray-900">
                                                        <Icon icon="mdi:dots-horizontal" />
                                                    </a>
                                                </a-dropdown>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div v-if="filteredFolders.length === 0 && filteredFiles.length === 0"
                            class="text-center py-6 text-gray-500">
                            没有找到文件或文件夹
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </div>

    <!-- 上传文件对话框 -->
    <a-modal v-model:visible="uploadModalVisible" title="上传文件" :maskClosable="false" :confirmLoading="uploadLoading"
        @ok="uploadFile">
        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">选择文件</label>
                <a-upload-dragger :multiple="false" :showUploadList="false" :beforeUpload="handleFileChange"
                    @drop="handleFileDrop">
                    <div class="flex flex-col items-center justify-center">
                        <Icon icon="mdi:upload" class="h-12 w-12 text-blue-500 mb-4" />
                        <p class="text-base">点击或拖拽文件到此区域上传</p>
                        <p class="text-sm text-gray-500 mt-2">支持单个或批量上传，严禁上传敏感数据或违禁文件</p>
                    </div>
                </a-upload-dragger>
            </div>

            <div v-if="fileToUpload">
                <p class="text-sm text-gray-500">选中的文件: {{ fileToUpload.name }} ({{
                    formatFileSize(fileToUpload.size) }})
                </p>
            </div>
        </div>
    </a-modal>

    <!-- 新建文件夹对话框 -->
    <a-modal v-model:visible="folderModalVisible" title="新建文件夹" :maskClosable="false" :confirmLoading="folderLoading"
        @ok="createFolder">
        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">文件夹名称</label>
                <a-input v-model:value="newFolderName" placeholder="请输入文件夹名称" />
            </div>
        </div>
    </a-modal>

    <!-- 文件分享对话框 -->
    <a-modal v-model:visible="shareModalVisible" title="分享文件" :maskClosable="false">
        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">分享链接</label>
                <div class="flex items-center">
                    <input type="text" readonly v-model="shareLink"
                        class="block w-full pr-4 py-2 border border-gray-200 rounded-lg text-sm" />
                    <a-button class="ml-2" @click="copyShareLink">复制</a-button>
                </div>
                <p class="text-xs text-gray-500 mt-1">此链接有效期为7天</p>
            </div>
            <div>
                <a-checkbox v-model:checked="shareWithPassword">需要密码访问</a-checkbox>
                <div v-if="shareWithPassword" class="mt-2">
                    <label class="block text-sm font-medium text-gray-700 mb-1">访问密码</label>
                    <a-input v-model:value="sharePassword" placeholder="设置访问密码" />
                </div>
            </div>
        </div>
        <template #footer>
            <a-button @click="shareModalVisible = false">关闭</a-button>
            <a-button type="primary" @click="generateShareLink">生成链接</a-button>
        </template>
    </a-modal>
</template>

<script setup>
import { Icon } from '@iconify/vue';
import { ref, computed, onMounted } from 'vue';
import { personalCloudAPI } from '@/api';
import { message, Modal } from 'ant-design-vue';

// 视图模式
const viewMode = ref('grid');

// 搜索和筛选
const searchQuery = ref('');

// 文件夹数据
const folders = ref([]);
const files = ref([]);
const loading = ref(true); // 默认为加载状态
const breadcrumbs = ref([]);

// 当前导航的文件夹
const currentFolder = ref(0);

// 当前用户ID 
const currentUserId = ref(null);

// 存储空间使用统计
const storageUsed = ref(0); // 单位：字节
const storageTotal = ref(1024 * 1024 * 1024 * 5); // 默认5GB
const storageUsagePercent = computed(() => {
    return Math.min(100, Math.round((storageUsed.value / storageTotal.value) * 100));
});

// 上传文件对话框
const uploadModalVisible = ref(false);
const fileToUpload = ref(null);
const uploadLoading = ref(false);
const cloudInfo = ref(null);

// 新建文件夹对话框
const folderModalVisible = ref(false);
const newFolderName = ref('');
const folderLoading = ref(false);

// 文件分享对话框
const shareModalVisible = ref(false);
const shareWithPassword = ref(false);
const sharePassword = ref('');
const shareLink = ref('');
const currentShareFile = ref(null);

// 组件挂载时加载数据
onMounted(async () => {
    try {
        await getUserInfo();
        await loadPersonalCloudInfo();
    } finally {
        loading.value = false;
    }
});

// 获取用户信息
const getUserInfo = () => {
    try {
        const userInfoStr = localStorage.getItem('userInfo');
        if (userInfoStr) {
            const userInfo = JSON.parse(userInfoStr);
            currentUserId.value = userInfo.id;
        }
    } catch (error) {
        console.error('获取用户信息失败', error);
        message.error('获取用户信息失败');
    }
};

// 加载个人云盘基本信息
const loadPersonalCloudInfo = async () => {
    try {
        const res = await personalCloudAPI.getCloudInfo();
        if (!res) {
            message.error('云盘服务尚未开通，请联系管理员');
            return;
        }
        cloudInfo.value = res;
        storageTotal.value = res.total_storage || 5368709120; // 5GB
        storageUsed.value = res.used_storage || 0;

        // 如果云盘已开通，加载文件
        if (cloudInfo.value) {
            await loadPersonalFiles();
        }
    } catch (error) {
        console.error('获取云盘信息失败:', error);
    }
};

// 刷新云盘状态
const checkCloudInfo = async () => {
    loading.value = true;
    try {
        await loadPersonalCloudInfo();
        if (cloudInfo.value) {
            message.success('云盘服务已开通');
        }
    } catch (error) {
        console.error('检查云盘状态失败:', error);
        message.error('检查云盘状态失败');
    } finally {
        loading.value = false;
    }
};

// 加载当前用户的文件和文件夹
const loadPersonalFiles = async (folderId = null) => {
    // 如果云盘未开通，不加载文件
    if (!cloudInfo.value) return;

    try {
        // 当导航到不同文件夹时显示加载状态
        if (folderId !== null && currentFolder.value?.id !== folderId) {
            loading.value = true;
        }

        const response = await personalCloudAPI.getFolderContent(folderId);

        folders.value = response.folders || [];
        files.value = response.files || [];
        currentFolder.value = response.currentFolder;

        await updateBreadcrumbs();
        calculateStorageUsage();
    } catch (error) {
        console.error('加载个人文件内容出错:', error);
        message.error('加载个人文件内容失败');
    } finally {
        loading.value = false;
    }
};

// 计算存储空间使用情况
const calculateStorageUsage = () => {
    // 直接从云盘信息中获取，如果已加载
    if (storageUsed.value > 0) return;

    // 否则从文件列表计算
    let totalSize = 0;
    files.value.forEach(file => {
        totalSize += (file.size || 0);
    });
    storageUsed.value = totalSize;
};

// 更新面包屑导航
const updateBreadcrumbs = async () => {
    if (!currentFolder.value) {
        breadcrumbs.value = [{ id: null, name: '我的云盘' }];
        saveBreadcrumbsToLocalStorage();
        return;
    }

    try {
        // 如果是从localStorage读取面包屑导航
        if (currentFolder.value.id) {
            const savedBreadcrumbs = loadBreadcrumbsFromLocalStorage();
            const foundBreadcrumb = findBreadcrumbsById(currentFolder.value.id, savedBreadcrumbs);

            if (foundBreadcrumb.length > 0) {
                breadcrumbs.value = [{ id: null, name: '我的云盘' }, ...foundBreadcrumb];
                return;
            }
        }

        // 如果localStorage中没有，则重新构建面包屑
        const path = await getBreadcrumbPath(currentFolder.value);
        breadcrumbs.value = [{ id: null, name: '我的云盘' }, ...path];

        // 更新localStorage中的面包屑数据
        saveBreadcrumbsToLocalStorage();
    } catch (error) {
        console.error('获取面包屑路径出错:', error);
        // 发生错误时至少显示当前文件夹
        breadcrumbs.value = [
            { id: null, name: '我的云盘' },
            { id: currentFolder.value.id, name: currentFolder.value.name }
        ];
    }
};

// 将面包屑保存到localStorage
const saveBreadcrumbsToLocalStorage = () => {
    try {
        // 去掉第一个元素（根目录），只保存子文件夹路径
        const pathToSave = breadcrumbs.value.slice(1);
        if (pathToSave.length > 0) {
            localStorage.setItem('personal-cloud-breadcrumb', JSON.stringify(pathToSave));
        }
    } catch (e) {
        console.error('保存面包屑到localStorage出错:', e);
    }
};

// 从localStorage加载面包屑
const loadBreadcrumbsFromLocalStorage = () => {
    try {
        const savedPath = localStorage.getItem('personal-cloud-breadcrumb');
        return savedPath ? JSON.parse(savedPath) : [];
    } catch (e) {
        console.error('从localStorage加载面包屑出错:', e);
        return [];
    }
};

// 在保存的面包屑中查找指定ID的路径
const findBreadcrumbsById = (folderId, savedBreadcrumbs) => {
    if (!savedBreadcrumbs || savedBreadcrumbs.length === 0) return [];

    // 直接查找当前文件夹
    for (let i = 0; i < savedBreadcrumbs.length; i++) {
        if (savedBreadcrumbs[i].id === folderId) {
            return savedBreadcrumbs.slice(0, i + 1);
        }
    }

    return [];
};

// 递归获取文件夹路径
const getBreadcrumbPath = async (folder) => {
    if (!folder || !folder.id) {
        return [];
    }

    const path = [{ id: folder.id, name: folder.name }];

    // 如果存在父文件夹，则递归获取
    if (folder.parentId && folder.parentId > 0) {
        try {
            // 获取父文件夹信息
            const response = await personalCloudAPI.getFolderContent(folder.parentId);
            if (response && response.currentFolder) {
                const parentFolder = response.currentFolder;
                // 递归获取父文件夹路径
                const parentPath = await getBreadcrumbPath(parentFolder);
                // 合并路径
                return [...parentPath, ...path];
            }
        } catch (error) {
            console.error('获取父文件夹出错:', error);
        }
    }

    return path;
};

// 导航到文件夹
const navigateToFolder = (folder) => {
    loadPersonalFiles(folder ? folder.id : null);
};

// 筛选文件夹
const filteredFolders = computed(() => {
    if (!searchQuery.value) {
        return folders.value;
    }

    return folders.value.filter(folder =>
        folder.name.toLowerCase().includes(searchQuery.value.toLowerCase())
    );
});

// 筛选文件
const filteredFiles = computed(() => {
    let result = files.value;

    // 根据搜索关键词过滤
    if (searchQuery.value) {
        result = result.filter(file =>
            file.name.toLowerCase().includes(searchQuery.value.toLowerCase())
        );
    }

    return result;
});

// 打开上传文件对话框
const openUploadModal = () => {
    fileToUpload.value = null;
    uploadModalVisible.value = true;
};

// 处理文件选择
const handleFileChange = (file) => {
    fileToUpload.value = file;
    return false; // 阻止自动上传
};

// 处理文件拖拽
const handleFileDrop = (e) => {
    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
        fileToUpload.value = files[0];
    }
};

// 上传文件
const uploadFile = async () => {
    if (!fileToUpload.value) {
        message.error('请选择要上传的文件');
        return;
    }

    uploadLoading.value = true;
    try {
        const folderId = currentFolder.value ? currentFolder.value.id : 0;

        await personalCloudAPI.uploadFile(
            fileToUpload.value,
            folderId
        );

        message.success('文件上传成功');
        uploadModalVisible.value = false;
        // 重新加载文件列表和云盘信息
        await loadPersonalCloudInfo();
        await loadPersonalFiles(folderId);
    } catch (error) {
        console.error('上传文件出错:', error);
        message.error('文件上传失败: ' + (error.message || '未知错误'));
    } finally {
        uploadLoading.value = false;
    }
};

// 打开新建文件夹对话框
const openFolderModal = () => {
    newFolderName.value = '';
    folderModalVisible.value = true;
};

// 创建文件夹
const createFolder = async () => {
    if (!newFolderName.value) {
        message.error('请输入文件夹名称');
        return;
    }

    folderLoading.value = true;
    try {
        const folderData = {
            name: newFolderName.value,
            parentId: currentFolder.value ? currentFolder.value.id : 0
        };

        await personalCloudAPI.createFolder(folderData);
        message.success('文件夹创建成功');
        folderModalVisible.value = false;
        // 重新加载文件夹列表
        loadPersonalFiles(folderData.parentId);

    } catch (error) {
        console.error('创建文件夹出错:', error);
        message.error('文件夹创建失败');
    } finally {
        folderLoading.value = false;
    }
};

// 下载文件
const downloadFile = async (file) => {
    try {
        // 获取文件下载地址
        const ossUrl = file.ossUrl;

        // 检查返回结果
        if (!ossUrl) {
            message.error('获取下载链接失败');
            return;
        }

        // 使用fetch下载文件
        fetch(ossUrl)
            .then(response => response.blob())
            .then(blob => {
                // 创建一个新的URL对象
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', file.name);
                document.body.appendChild(link);
                link.click();

                // 清理
                window.URL.revokeObjectURL(url);
                document.body.removeChild(link);
            })
            .catch(error => {
                console.error('下载文件出错:', error);
                message.error('文件下载失败');
            });

        message.success('文件下载开始');
    } catch (error) {
        console.error('下载文件出错:', error);
        message.error('文件下载失败');
    }
};

// 删除文件
const confirmDeleteFile = (file) => {
    Modal.confirm({
        title: '确认删除',
        content: `确定要删除文件 "${file.name}" 吗？`,
        okText: '确认',
        okType: 'danger',
        cancelText: '取消',
        async onOk() {
            try {
                await personalCloudAPI.deleteFile(file.id);
                message.success('文件删除成功');
                // 重新加载文件列表和云盘信息
                await loadPersonalCloudInfo();
                await loadPersonalFiles(currentFolder.value ? currentFolder.value.id : 0);
            } catch (error) {
                console.error('删除文件出错:', error);
                message.error('文件删除失败');
            }
        }
    });
};

// 删除文件夹
const confirmDeleteFolder = (folder) => {
    Modal.confirm({
        title: '确认删除',
        content: `确定要删除文件夹 "${folder.name}" 吗？`,
        okText: '确认',
        okType: 'danger',
        cancelText: '取消',
        async onOk() {
            try {
                await personalCloudAPI.deleteFolder(folder.id);
                message.success('文件夹删除成功');
                loadPersonalFiles(currentFolder.value ? currentFolder.value.id : 0);
            } catch (error) {
                console.error('删除文件夹出错:', error);
                message.error('文件夹删除失败');
            }
        }
    });
};

// 监听筛选条件变化
const handleFilterChange = () => {
    loadPersonalFiles(currentFolder.value ? currentFolder.value.id : null);
};

// 获取文件图标
const getFileIcon = (fileType) => {
    switch (fileType) {
        case 'pdf':
            return 'mdi:file-pdf-box';
        case 'doc':
        case 'docx':
            return 'mdi:file-word-box';
        case 'xls':
        case 'xlsx':
            return 'mdi:file-excel-box';
        case 'ppt':
        case 'pptx':
            return 'mdi:file-powerpoint-box';
        default:
            return 'mdi:file-document-outline';
    }
};

// 获取文件图标颜色
const getFileIconColor = (fileType) => {
    switch (fileType) {
        case 'pdf':
            return 'text-red-500';
        case 'doc':
        case 'docx':
            return 'text-blue-500';
        case 'xls':
        case 'xlsx':
            return 'text-green-500';
        case 'ppt':
        case 'pptx':
            return 'text-orange-500';
        default:
            return 'text-gray-500';
    }
};

// 获取文件类型显示名称
const getFileTypeName = (fileType) => {
    switch (fileType) {
        case 'pdf':
            return 'PDF文档';
        case 'doc':
        case 'docx':
            return 'Word文档';
        case 'xls':
        case 'xlsx':
            return 'Excel表格';
        case 'ppt':
        case 'pptx':
            return 'PPT演示文稿';
        case 'txt':
            return '文本文件';
        case 'jpg':
        case 'jpeg':
        case 'png':
            return '图片文件';
        default:
            return fileType ? fileType.toUpperCase() : '未知类型';
    }
};

// 格式化日期
const formatDate = (dateString) => {
    if (!dateString) return '-';

    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
};

// 预览文件
const previewFile = async (file) => {
    try {
        // 获取文件下载URL
        const ossUrl = file.ossUrl;

        // 检查返回结果
        if (!ossUrl) {
            message.error('获取预览链接失败');
            return;
        }

        // 检查文件类型
        const supportedPreviewTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'jpg', 'jpeg', 'png'];
        if (!supportedPreviewTypes.includes(file.fileType)) {
            message.warning('该文件类型不支持在线预览');
            return;
        }

        // 在新标签页中打开文件
        window.open(ossUrl, '_blank');

        // 提示用户
        message.success('文件预览已在新标签页打开');
    } catch (error) {
        console.error('预览文件出错:', error);
        message.error('文件预览失败');
    }
};

// 打开分享对话框
const openShareModal = (file) => {
    currentShareFile.value = file;
    shareLink.value = '';
    shareWithPassword.value = false;
    sharePassword.value = '';
    shareModalVisible.value = true;
};

// 复制分享链接
const copyShareLink = () => {
    if (!shareLink.value) {
        message.error('请先生成分享链接');
        return;
    }

    // 复制到剪贴板
    navigator.clipboard.writeText(shareLink.value)
        .then(() => {
            message.success('链接已复制到剪贴板');
        })
        .catch(() => {
            message.error('复制失败，请手动复制');
        });
};

// 格式化文件大小
const formatFileSize = (bytes) => {
    if (bytes === 0 || !bytes) return '0 B';

    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));

    return parseFloat((bytes / Math.pow(1024, i)).toFixed(2)) + ' ' + sizes[i];
};
</script>

<style scoped>
/* 标题区域样式 */
.title-section {
    transition: all 0.3s ease;
}

.title-section:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.page-title {
    position: relative;
    display: inline-block;
    color: white;
}

.page-title::after {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 0;
    width: 40px;
    height: 3px;
    background-color: white;
    border-radius: 3px;
}

.page-description {
    max-width: 600px;
    opacity: 0.9;
}

/* 统计卡片样式 */
.stat-card {
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

/* 新增按钮内图标与文字对齐样式 */
.btn-with-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    vertical-align: middle;
}

.btn-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 4px;
    line-height: 1;
    vertical-align: middle;
}

.btn-text {
    display: inline-block;
    line-height: 1;
    vertical-align: middle;
}

/* 链接中图标与文字对齐样式 */
.link-with-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    vertical-align: middle;
}

.link-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 4px;
    line-height: 1;
    vertical-align: middle;
}

.link-text {
    display: inline-block;
    line-height: 1;
    vertical-align: middle;
}

/* 临时占位 */
.content-placeholder {
    min-height: 400px;
}
</style>