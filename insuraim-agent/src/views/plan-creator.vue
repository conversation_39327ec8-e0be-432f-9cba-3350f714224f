<template>
  <div class="mx-auto">
    <!-- 页面标题 -->
    <div
      class="title-section mb-6 p-6 rounded-lg shadow-md text-white bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700">
      <div class="flex items-center">
        <Icon icon="mdi:file-document-edit" class="text-4xl mr-3" />
        <h1 class="text-2xl font-bold page-title">{{ $t('plans.planCreator') }}</h1>
      </div>
      <p class="mt-2 page-description">
        {{ $t('plans.createAndGenerate') }}
      </p>
    </div>

    <div class="bg-white rounded-lg shadow-md p-8">
      <!-- 表单内容 -->
      <a-form :model="formState" name="plan-form" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }"
        @finish="onFinish">
        <!-- 产品筛选条件 -->
        <div class="mb-8">
          <div class="form-section-title flex items-center">
            <Icon icon="mdi:filter-variant" class="text-gray-500 text-xl mr-2 flex-shrink-0" />
            <h2 class="text-lg font-medium text-gray-800">{{ $t('plans.filterConditions') }}</h2>
          </div>

          <!-- 产品筛选选项（一行两列） -->
          <a-row :gutter="8" style="margin-bottom: 4px;">
            <a-col :span="12">
              <a-form-item label="地区" name="region" class="form-item-with-label">
                <a-select v-model:value="formState.region" placeholder="选择地区" :options="regionOptions"
                  @change="handleRegionChange">
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="保司" name="company" class="form-item-with-label">
                <a-select v-model:value="formState.company" placeholder="选择保司" :options="companyOptions"
                  @change="handleCompanyChange">
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="8" style="margin-top: 4px;">
            <a-col :span="12">
              <a-form-item label="分类" name="productType" class="form-item-with-label">
                <a-select v-model:value="formState.productType" placeholder="选择产品分类" :options="productTypeOptions"
                  @change="handleProductTypeChange">
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="货币" name="currency" class="form-item-with-label">
                <a-select v-model:value="formState.currency" placeholder="选择货币类型" :options="currencyOptions"
                  @change="handleCurrencyChange">
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 产品选择区域 -->
        <div class="mb-8">
          <!-- 分隔线和强调标题 -->
          <a-divider>
            <div class="text-lg font-bold flex items-center justify-center">
              <Icon icon="material-symbols:inventory-2" class="text-xl mr-2" />
              选择产品
            </div>
          </a-divider>

          <!-- 产品选择框 -->
          <div>
            <a-form-item label="产品选择" name="productId" :rules="[{ required: true, message: '请选择产品!' }]"
              class="form-item-with-label mb-0">
              <a-select v-model:value="formState.productId" placeholder="选择产品" :options="filteredProductOptions"
                show-search :filter-option="filterOption" option-label-prop="label" @change="handleProductChange"
                size="large" class="product-select">
                <template #option="{ companyName, label, logoUrl, productCode }">
                  <div class="flex items-center py-2">
                    <img v-if="logoUrl" :src="logoUrl" class="w-6 h-6 mr-3 object-contain flex-shrink-0"
                      onerror="this.style.display='none'" />
                    <div class="flex items-center">
                      <span class="text-gray-500 mx-2">{{ companyName }}</span>
                      <span class="font-medium">{{ label }}</span>
                    </div>
                  </div>
                </template>
              </a-select>
            </a-form-item>
          </div>
        </div>

        <!-- 产品货币选择 -->
        <div class="mb-8" v-if="showProductCurrencySelector">
          <div class="form-section-title flex items-center">
            <Icon icon="material-symbols:currency-exchange" class="text-gray-500 text-xl mr-2 flex-shrink-0" />
            <h2 class="text-lg font-medium text-gray-800">选择货币</h2>
          </div>
          <a-form-item label="货币类型" name="productCurrency"
            :rules="[{ required: showProductCurrencySelector, message: '请选择货币类型!' }]" class="form-item-with-label">
            <a-select v-model:value="formState.productCurrency" placeholder="选择货币类型" :options="productCurrencyOptions"
              @change="handleProductCurrencyChange" />
          </a-form-item>
        </div>

        <template v-if="formState.productId">
          <!-- 客户信息 -->
          <div class="mb-8">
            <div class="form-section-title flex items-center">
              <Icon icon="material-symbols:person" class="text-gray-500 text-xl mr-2 flex-shrink-0" />
              <h2 class="text-lg font-medium text-gray-800">客户信息</h2>
            </div>

            <a-form-item label="客户姓名" name="customerName" class="form-item-with-label">
              <a-input v-model:value="formState.customerName" placeholder="输入客户姓名" />
            </a-form-item>

            <a-form-item label="年龄" name="age" :rules="[{ required: true, message: '请输入年龄!' }]"
              class="form-item-with-label">
              <a-input-number v-model:value="formState.age" :min="0" :max="100" style="width: 100%"
                placeholder="输入年龄" />
            </a-form-item>

            <a-form-item label="性别" name="gender" class="form-item-with-label">
              <a-select v-model:value="formState.gender" :options="genderOptions" />
            </a-form-item>

            <a-form-item label="是否吸烟" name="isSmoker" class="form-item-with-label">
              <a-select v-model:value="formState.isSmoker" :options="smokerOptions" />
            </a-form-item>
          </div>

          <!-- 保障信息 -->
          <div class="mb-8" v-if="showProtectionInfo">
            <div class="form-section-title flex items-center">
              <Icon icon="material-symbols:settings" class="text-gray-500 text-xl mr-2 flex-shrink-0" />
              <h2 class="text-lg font-medium text-gray-800">保障信息</h2>
            </div>

            <a-form-item label="金额类型" name="amountType" class="form-item-with-label">
              <a-select v-model:value="formState.amountType" :options="amountTypeOptions"
                @change="handleAmountTypeChange" />
            </a-form-item>

            <a-form-item :label="formState.amountType === 1 ? '保费金额' : '保额金额'" name="amount" :rules="[
              { required: showProtectionInfo, message: '请输入金额!' },
              { validator: validateAmountRange, trigger: 'change' }
            ]" class="form-item-with-label">
              <a-input-number v-model:value="formState.amount" :min="currentAmountRange.min"
                :max="currentAmountRange.max" style="width: 100%" :prefix="currentCurrencySymbol"
                :placeholder="formState.amountType === 1 ? '输入保费金额' : '输入保额金额'" />
              <div class="text-xs text-gray-500 mt-1"
                v-if="currentAmountRange.min > 0 || currentAmountRange.max < Infinity">
                可接受范围: {{ currentCurrencySymbol }}{{ currentAmountRange.min.toLocaleString() }} -
                {{ currentCurrencySymbol }}{{ currentAmountRange.max.toLocaleString() }}
              </div>
            </a-form-item>
          </div>

          <!-- 缴费方式 -->
          <div class="mb-8" v-if="showPaymentMethodDropdown">
            <div class="form-section-title flex items-center">
              <Icon icon="material-symbols:payments" class="text-gray-500 text-xl mr-2 flex-shrink-0" />
              <h2 class="text-lg font-medium text-gray-800">缴费方式</h2>
            </div>
            <a-form-item label="缴费方式" name="paymentMethod"
              :rules="[{ required: showPaymentMethodDropdown, message: '请选择缴费方式!' }]" class="form-item-with-label">
              <a-select v-model:value="formState.paymentMethod" :options="paymentMethodOptions" placeholder="选择缴费方式"
                @change="handlePaymentMethodChange" />
            </a-form-item>
          </div>

          <!-- 预缴年份 -->
          <div class="mb-8" v-if="showPrepaymentYearDropdown">
            <div class="form-section-title flex items-center">
              <Icon icon="material-symbols:calendar-today" class="text-gray-500 text-xl mr-2 flex-shrink-0" />
              <h2 class="text-lg font-medium text-gray-800">预缴年份</h2>
            </div>
            <a-form-item label="预缴年份" name="prepaymentYear"
              :rules="[{ required: showPrepaymentYearDropdown, message: '请选择预缴年份!' }]" class="form-item-with-label">
              <a-select v-model:value="formState.prepaymentYear" :options="prepaymentYearOptions"
                placeholder="选择预缴年份" />
            </a-form-item>
          </div>

          <!-- 自定义现金提取 -->
          <div class="mb-8" v-if="showCustomWithdrawalSwitch">
            <a-form-item label="自定义现金提取" name="enableCustomWithdrawal" class="form-item-with-label">
              <a-switch v-model:checked="formState.enableCustomWithdrawal" />
            </a-form-item>

            <div v-if="formState.enableCustomWithdrawal"
              class="mt-4 p-4 border border-gray-200 rounded-md shadow-sm bg-gray-50">
              <div class="form-section-title flex items-center mb-6">
                <Icon icon="material-symbols:edit-document" class="text-gray-500 text-xl mr-2 flex-shrink-0" />
                <h3 class="text-md font-medium text-gray-800">自定义提取金额详情</h3>
              </div>

              <!-- 领款年龄 -->
              <a-form-item label="领款年龄" class="form-item-with-label mb-0 required">
                <a-row :gutter="16">
                  <a-col :xs="24" :sm="12">
                    <a-form-item name="withdrawalStartAge" :rules="[
                      { required: formState.enableCustomWithdrawal, message: '请输入开始年龄!' },
                      { validator: validateWithdrawalStartAgeAgainstClientAge, trigger: 'change' }
                    ]" class="custom-nested-form-item">
                      <a-input-number v-model:value="formState.withdrawalStartAge" placeholder="从 (岁)" :min="0"
                        :max="138" style="width: 100%" />
                    </a-form-item>
                  </a-col>
                  <a-col :xs="24" :sm="12">
                    <a-form-item name="withdrawalEndAge"
                      :rules="[{ required: formState.enableCustomWithdrawal, message: '请输入结束年龄!' }, { validator: validateEndAge, trigger: 'change' }]"
                      class="custom-nested-form-item">
                      <a-input-number v-model:value="formState.withdrawalEndAge" placeholder="至 (岁)"
                        :min="formState.withdrawalStartAge !== undefined ? formState.withdrawalStartAge : 0" :max="138"
                        style="width: 100%" />
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-form-item>


              <!-- 初始提取金额 -->
              <a-form-item label="初始提取金额" name="initialWithdrawalAmount" :rules="[
                { required: formState.enableCustomWithdrawal, message: '请输入初始提取金额!' },
              ]" class="form-item-with-label">
                <a-input-number v-model:value="formState.initialWithdrawalAmount" :min="0" style="width: 100%"
                  :prefix="currentCurrencySymbol" placeholder="输入金额" />
              </a-form-item>

              <!-- 每年提取增长率 -->
              <a-form-item label="每年提取增长率" name="withdrawalGrowthRate"
                :rules="[{ required: formState.enableCustomWithdrawal, message: '请输入增长率!' }]"
                class="form-item-with-label">
                <a-input-number v-model:value="formState.withdrawalGrowthRate" :min="0" :max="100" style="width: 100%"
                  placeholder="例如: 3 (表示3%)" :formatter="value => `${value}%`"
                  :parser="value => value.replace('%', '')" />
              </a-form-item>
            </div>
          </div>

          <!-- 新增：锁定终期红利 -->
          <div class="mb-8" v-if="showLockEndBonusSwitch">
            <a-form-item label="锁定终期红利" name="enableLockEndBonus" class="form-item-with-label">
              <a-switch v-model:checked="formState.enableLockEndBonus" />
            </a-form-item>

            <div v-if="formState.enableLockEndBonus"
              class="mt-4 p-4 border border-gray-200 rounded-md shadow-sm bg-gray-50">
              <div class="form-section-title flex items-center mb-6">
                <Icon icon="material-symbols:lock-clock" class="text-gray-500 text-xl mr-2 flex-shrink-0" />
                <h3 class="text-md font-medium text-gray-800">锁定终期红利详情</h3>
              </div>

              <!-- 行使權益時年齡 -->
              <a-form-item label="行使權益時年齡" class="form-item-with-label mb-0 required">
                <a-row :gutter="16">
                  <a-col :xs="24" :sm="12">
                    <a-form-item name="lockBonusStartAge"
                      :rules="[{ required: formState.enableLockEndBonus, message: '请输入开始年龄!' }]"
                      class="custom-nested-form-item">
                      <a-input-number v-model:value="formState.lockBonusStartAge" placeholder="从 (岁)" :min="55"
                        :max="138" style="width: 100%" />
                    </a-form-item>
                  </a-col>
                  <a-col :xs="24" :sm="12">
                    <a-form-item name="lockBonusEndAge"
                      :rules="[{ required: formState.enableLockEndBonus, message: '请输入结束年龄!' }, { validator: validateLockBonusEndAge, trigger: 'change' }]"
                      class="custom-nested-form-item">
                      <a-input-number v-model:value="formState.lockBonusEndAge" placeholder="至 (岁)"
                        :min="formState.lockBonusStartAge !== undefined ? formState.lockBonusStartAge : 0" :max="138"
                        style="width: 100%" />
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-form-item>

              <!-- 锁定比例 -->
              <a-form-item label="锁定比例" name="lockBonusPercentage" :rules="[
                { required: formState.enableLockEndBonus, message: '请选择锁定比例!' },
                { validator: validateLockBonusPercentageWithAge, trigger: 'change' }
              ]" class="form-item-with-label">
                <a-space>
                  <a-button v-for="percent in [10, 20, 30, 40, 50]" :key="percent"
                    :type="formState.lockBonusPercentage === percent ? 'primary' : 'default'"
                    @click="handleLockBonusPercentageChange(percent)">
                    {{ percent }}%
                  </a-button>
                </a-space>
              </a-form-item>
            </div>
          </div>

          <!-- 计划书语言 -->
          <div class="mb-8">
            <div class="form-section-title flex items-center">
              <Icon icon="material-symbols:language" class="text-gray-500 text-xl mr-2 flex-shrink-0" />
              <h2 class="text-lg font-medium text-gray-800">计划书语言</h2>
            </div>

            <a-form-item label="语言选择" name="language" class="form-item-with-label">
              <a-select v-model:value="formState.language" :options="languageOptions" />
            </a-form-item>
          </div>
        </template>

        <!-- 操作按钮 -->
        <a-form-item :wrapper-col="{ span: 24 }" class="mt-8">
          <div class="flex justify-center">
            <a-space size="middle">
              <a-button type="primary" html-type="submit" class="flex items-center justify-center">
                <Icon icon="material-symbols:description" class="mr-1 align-middle inline-flex" />
                <span class="align-middle">生成计划书</span>
              </a-button>

              <a-button @click="resetForm">
                重置
              </a-button>
            </a-space>
          </div>
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { productAPI, planAPI, companyAPI } from '@/api';
import { useRouter, useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { AreaEnum } from '@/store/modules/order';
import { useProductStore } from '@/store/modules/product';

const router = useRouter();
const route = useRoute();
const productStore = useProductStore();
const { t } = useI18n();

// ==================== 常量定义 ====================

/**
 * 货币符号映射表
 */
const currencySymbolMap = {
  'CNY': '¥',    // 人民币
  'HKD': 'HK$',  // 港币
  'USD': '$',    // 美元
  'AUD': 'A$',   // 澳元
  'EUR': '€',    // 欧元
  'GBP': '£',    // 英镑
  'CAD': 'C$',   // 加拿大元
  'SGD': 'S$',   // 新加坡元
  'CHF': 'CHF',  // 瑞士法郎
  'JPY': '¥',    // 日元
  'NZD': 'NZ$',  // 新西兰元
  'KRW': '₩',    // 韩元
  '': '¥'        // 默认使用人民币符号
};

/**
 * 语言代码映射表
 */
const languageMap = {
  'zh-TW': '中文繁体',
  'en-US': '英文',
  'HK': '中文繁体',
  'EN': '英文'
};

/**
 * 支付方式中文映射
 */
const paymentMethodLabels = {
  "ANNUALLY": "年缴",
  "MONTHLY": "月缴",
  "QUARTERLY": "季缴",
  "SEMI_ANNUALLY": "半年缴",
  "SINGLE_PREMIUM": "趸缴",
};

/**
 * 默认语言选项
 */
const defaultLanguageOptions = [
  { value: 'zh-TW', label: '中文繁体' },
  { value: 'en-US', label: '英文' }
];

// ==================== 选项数据 ====================

/**
 * 货币类型选项
 */
const currencyOptions = [
  { value: '', label: '全部' },
  { value: 'USD', label: '美元' },
  { value: 'HKD', label: '港币' },
  { value: 'CNY', label: '人民币' },
  { value: 'SGD', label: '新加坡元' },
  { value: 'AUD', label: '澳元' },
  { value: 'EUR', label: '欧元' },
  { value: 'GBP', label: '英镑' },
  { value: 'CAD', label: '加拿大元' },
  { value: 'CHF', label: '瑞士法郎' },
  { value: 'JPY', label: '日元' },
  { value: 'NZD', label: '新西兰元' },
  { value: 'KRW', label: '韩元' },
];

/**
 * 性别选项
 */
const genderOptions = [
  { value: 'male', label: '男性' },
  { value: 'female', label: '女性' },
];

/**
 * 吸烟状态选项
 */
const smokerOptions = [
  { value: 'non-smoker', label: '非吸烟' },
  { value: 'smoker', label: '吸烟' },
];

/**
 * 金额类型选项
 */
const amountTypeOptions = [
  { value: 0, label: '保额' },
  { value: 1, label: '保费' },
];

/**
 * 地区选项
 */
const regionOptions = [
  { value: '', label: '全部' },
  { value: 'HONGKONG', label: AreaEnum.HONGKONG },
  { value: 'MACAO', label: AreaEnum.MACAO },
  { value: 'SINGAPORE', label: AreaEnum.SINGAPORE },
  { value: 'BERMUDA', label: AreaEnum.BERMUDA },
];

/**
 * 产品分类选项（动态获取）
 */
const productTypeOptions = computed(() => {
  const categories = productStore.productCategoryList || [];
  const mapped = categories.map(cat => ({
    value: cat.code,
    label: cat.name,
  }));
  return [{ value: '', label: '全部' }, ...mapped];
});

// ==================== 状态定义 ====================

// 从URL参数中获取产品代码和产品ID
const productCodeFromUrl = route.query.productCode || '';
const productIdFromUrl = route.query.productId || '';
const productNameFromUrl = route.query.productName || '';

// 产品数据
const allProductOptions = ref([]); // 存储所有产品
const productOptions = ref([]);
const languageOptions = ref([...defaultLanguageOptions]);

// 保司数据
const companyOptions = ref([]);

/**
 * 表单数据
 */
const formState = reactive({
  // 产品相关
  productId: undefined,
  productType: undefined,
  region: undefined,
  company: undefined,
  selectedActualProductType: undefined,
  productName: '',
  productNameSearch: '',
  productCode: undefined,

  // 货币相关
  currency: undefined,
  productCurrency: undefined,

  // 客户信息
  customerName: '',
  age: undefined,
  gender: 'male',
  isSmoker: 'non-smoker',

  // 保障信息
  amount: undefined,
  amountType: 0, // 0-保额, 1-保费

  // 支付相关
  paymentMethod: undefined,
  prepaymentYear: undefined,

  // 自定义现金提取
  enableCustomWithdrawal: false,
  withdrawalStartAge: undefined,
  withdrawalEndAge: undefined,
  initialWithdrawalAmount: undefined,
  withdrawalGrowthRate: undefined,

  // 锁定终期红利
  enableLockEndBonus: false,
  lockBonusStartAge: undefined,
  lockBonusEndAge: undefined,
  lockBonusPercentage: undefined,

  // 语言
  language: 'zh-TW',
});

// ==================== 工具函数 ====================

/**
 * 重置产品选择相关字段
 */
const resetProductSelection = () => {
  formState.productId = undefined;
  formState.productName = '';
  formState.selectedActualProductType = undefined;
  formState.productCurrency = undefined;
  formState.amount = undefined;
  formState.amountType = 0;
  formState.paymentMethod = undefined;
  formState.language = 'zh-TW';
};

/**
 * 重置语言选项为默认值
 */
const resetLanguageOptions = () => {
  languageOptions.value = [...defaultLanguageOptions];
};

/**
 * 将字符串确保为字符串类型并标准化处理
 * @param {any} value - 要处理的值
 * @returns {string} - 处理后的字符串
 */
const ensureString = (value) => {
  return typeof value === 'string' ? value.toLowerCase().trim() : String(value).toLowerCase().trim();
};

/**
 * 获取语言显示名称
 * @param {string} code - 语言代码
 * @returns {string} - 语言显示名称
 */
const getLanguageLabel = (code) => {
  return languageMap[code] || code;
};

/**
 * 选择过滤函数 - 用于下拉选择框的过滤
 */
const filterOption = (input, option) => {
  return option.label.toLowerCase().includes(input.toLowerCase());
};

// ==================== 计算属性 ====================

/**
 * 根据筛选条件过滤产品选项
 */
const filteredProductOptions = computed(() => {
  let filtered = productOptions.value;

  // 按产品类型筛选
  if (formState.productType) {
    filtered = filtered.filter(item => item.productType === formState.productType);
  }

  // 按货币类型筛选
  if (formState.currency) {
    filtered = filtered.filter(item =>
      item.supportCurrency &&
      item.supportCurrency.includes(formState.currency)
    );
  }

  // 按地区筛选
  if (formState.region) {
    filtered = filtered.filter(item =>
      item.region === formState.region ||
      (item.regions && item.regions.includes(formState.region))
    );
  }

  // 按产品名称或产品代码关键词筛选
  if (formState.productNameSearch) {
    const searchKeyword = ensureString(formState.productNameSearch);

    // 检查是否是逗号分隔的产品代码
    if (searchKeyword.includes(',')) {
      // 将逗号分隔的产品代码拆分为数组
      const productCodes = searchKeyword.split(',').map(code => code.trim());

      // 过滤产品，如果产品代码匹配数组中的任何一个代码，则保留
      filtered = filtered.filter(item => {
        if (!item.productCode) return false;
        const itemProductCode = item.productCode.toLowerCase();
        return productCodes.some(code => itemProductCode === code.toLowerCase());
      });
    } else {
      // 常规搜索逻辑
      filtered = filtered.filter(item =>
        item.label.toLowerCase().includes(searchKeyword) ||
        (item.productCode && item.productCode.toLowerCase().includes(searchKeyword))
      );
    }
  }

  return filtered;
});

/**
 * 获取当前选中的产品详情
 */
const selectedProductDetails = computed(() => {
  if (!formState.productId) return null;
  return allProductOptions.value.find(p => p.value === formState.productId);
});

// ========== 显示控制相关计算属性 ==========

/**
 * 判断是否显示保障信息模块（非医疗类产品才显示）
 */
const showProtectionInfo = computed(() => {
  if (formState.productId && formState.selectedActualProductType) {
    return formState.selectedActualProductType !== 'MEDICAL';
  }
  if (!formState.productId && formState.productType === 'MEDICAL') {
    return false;
  }
  return true;
});

/**
 * 判断是否显示缴费方式下拉框
 */
const showPaymentMethodDropdown = computed(() => {
  return selectedProductDetails.value &&
    selectedProductDetails.value.paymentMethods &&
    selectedProductDetails.value.paymentMethods.length > 0;
});

/**
 * 判断是否显示预缴年份下拉框
 * 只有在选择年缴时才显示
 */
const showPrepaymentYearDropdown = computed(() => {
  return selectedProductDetails.value &&
    selectedProductDetails.value.prepaymentYears &&
    selectedProductDetails.value.prepaymentYears.length > 0 &&
    formState.paymentMethod === 'ANNUALLY'; // 只有年缴才显示预缴年份
});

/**
 * 判断是否显示自定义现金提取开关
 */
const showCustomWithdrawalSwitch = computed(() => {
  return selectedProductDetails.value && selectedProductDetails.value.supportWithdrawal === true;
});

/**
 * 判断是否显示锁定终期红利部分
 */
const showLockEndBonusSwitch = computed(() => {
  return selectedProductDetails.value && selectedProductDetails.value.canLockendPeriodBonus === true;
});

/**
 * 判断是否显示产品货币选择器（当产品支持多种货币时）
 */
const showProductCurrencySelector = computed(() => {
  return selectedProductDetails.value &&
    selectedProductDetails.value.supportCurrency &&
    selectedProductDetails.value.supportCurrency.length > 1;
});

// ========== 选项数据相关计算属性 ==========

/**
 * 获取当前产品支持的货币选项
 */
const productCurrencyOptions = computed(() => {
  if (!selectedProductDetails.value || !selectedProductDetails.value.supportCurrency) {
    return [];
  }

  return selectedProductDetails.value.supportCurrency.map(currency => {
    const option = currencyOptions.find(opt => opt.value === currency);
    return option || { value: currency, label: currency };
  });
});

/**
 * 获取支付方式选项
 */
const paymentMethodOptions = computed(() => {
  if (!showPaymentMethodDropdown.value) return [];
  return selectedProductDetails.value.paymentMethods.map(method => ({
    value: method,
    label: paymentMethodLabels[method] || method,
  }));
});

/**
 * 获取预缴年份选项
 */
const prepaymentYearOptions = computed(() => {
  if (!showPrepaymentYearDropdown.value) return [];
  return selectedProductDetails.value.prepaymentYears.map(year => ({
    value: year,
    label: year === '1' ? '不适用' : year + '年'
  }));
});

// ========== 值计算相关计算属性 ==========

/**
 * 获取当前选择货币的符号
 */
const currentCurrencySymbol = computed(() => {
  // 优先使用产品货币，如果没有则使用筛选货币，都没有则使用默认值
  const currencyToUse = formState.productCurrency || formState.currency || 'HKD';
  return currencySymbolMap[currencyToUse] || 'HK$'; // 默认返回港币符号
});

/**
 * 获取当前金额范围限制
 */
const currentAmountRange = computed(() => {
  if (!selectedProductDetails.value) return { min: 0, max: Infinity };

  // 根据amountType选择不同的范围限制
  const rangeArray = formState.amountType === 0
    ? selectedProductDetails.value.amountRange
    : selectedProductDetails.value.premiumRange;

  // 如果没有范围或范围格式不正确，返回默认值
  if (!Array.isArray(rangeArray) || rangeArray.length !== 2) {
    return { min: 0, max: Infinity };
  }

  return {
    min: Number(rangeArray[0]) || 0,
    max: Number(rangeArray[1]) || Infinity
  };
});

// ==================== 表单验证函数 ====================

/**
 * 表单验证函数集合
 */
const validators = {
  /**
   * 验证结束年龄
   */
  validateEndAge: async (_rule, value) => {
    if (value === undefined) {
      // 当前结束年龄未填，则不立即报错，依赖required
      return Promise.resolve();
    }

    // 如果没有填写用户年龄，提示先填写用户年龄
    if (formState.enableCustomWithdrawal && formState.age === undefined) {
      return Promise.reject('请先填写客户年龄');
    }

    if (formState.withdrawalStartAge === undefined) {
      // 如果开始年龄未填，不立即报错，依赖required
      return Promise.resolve();
    }

    if (value < formState.withdrawalStartAge) {
      return Promise.reject('结束年龄不能小于开始年龄');
    }
    return Promise.resolve();
  },

  /**
   * 验证锁定终期红利结束年龄
   */
  validateLockBonusEndAge: async (_rule, value) => {
    if (formState.lockBonusStartAge === undefined || value === undefined) {
      return Promise.resolve();
    }
    if (value < formState.lockBonusStartAge) {
      return Promise.reject('结束年龄不能小于开始年龄');
    }
    return Promise.resolve();
  },

  /**
   * 验证锁定终期红利百分比与年龄差的关系
   * 年龄差乘以锁定比例不能大于等于50%
   */
  validateLockBonusPercentageWithAge: async (_rule, value) => {
    if (!formState.enableLockEndBonus || value === undefined) {
      return Promise.resolve();
    }

    if (formState.lockBonusStartAge === undefined || formState.lockBonusEndAge === undefined) {
      return Promise.resolve(); // 如果年龄未填写，依赖其他验证规则
    }

    const ageDiff = formState.lockBonusEndAge - formState.lockBonusStartAge;
    const percentage = value / 100; // 将百分比转换为小数

    if (ageDiff * percentage >= 0.5) {
      return Promise.reject('年龄差与锁定比例的乘积不能大于等于50%');
    }

    return Promise.resolve();
  },

  /**
   * 验证自定义现金提取开始年龄（对比客户年龄 + customWithdrawalAgeOffset）
   */
  validateWithdrawalStartAgeAgainstClientAge: async (_rule, value) => {
    if (!formState.enableCustomWithdrawal || value === undefined) {
      return Promise.resolve();
    }

    // 如果没有填写用户年龄，提示先填写用户年龄
    if (formState.age === undefined) {
      return Promise.reject('请先填写客户年龄');
    }

    if (!selectedProductDetails.value) {
      return Promise.resolve();
    }

    const offsetStr = selectedProductDetails.value.customWithdrawalAgeOffset;
    let offsetNum = 0;

    if (offsetStr !== null && offsetStr !== undefined && offsetStr !== '') {
      const parsedNum = parseInt(offsetStr, 10);
      if (!isNaN(parsedNum)) {
        offsetNum = parsedNum;
      } else {
        return Promise.reject('提取年龄偏移量格式错误，无法计算最小领款年龄。');
      }
    } else {
      console.warn("Product's customWithdrawalAgeOffset is null, undefined or empty. Assuming 0 for validation.");
    }

    const clientAge = formState.age;
    const minAllowedStartAge = clientAge + offsetNum;

    if (value < minAllowedStartAge) {
      return Promise.reject(`起始年龄必须为${minAllowedStartAge}岁或以上`);
    }
    return Promise.resolve();
  },


  /**
   * 验证金额是否在有效范围内
   */
  validateAmountRange: async (_rule, value) => {
    if (value === undefined) {
      return Promise.resolve();
    }

    const { min, max } = currentAmountRange.value;

    if (value < min) {
      return Promise.reject(`最小金额为 ${currentCurrencySymbol.value}${min.toLocaleString()}`);
    }

    if (value > max) {
      return Promise.reject(`最大金额为 ${currentCurrencySymbol.value}${max.toLocaleString()}`);
    }

    return Promise.resolve();
  }
};

// 使用验证函数
const {
  validateEndAge,
  validateLockBonusEndAge,
  validateLockBonusPercentageWithAge,
  validateWithdrawalStartAgeAgainstClientAge,
  validateAmountRange
} = validators;

// ==================== 事件处理函数 ====================

/**
 * 获取产品列表
 */
const getProductList = async () => {
  try {
    const res = await productAPI.getPlanBookList();
    const mappedProducts = res.map(item => ({
      value: item.id,
      label: item.productName,
      companyName: "中国人寿", // 默认公司名称
      logoUrl: item.companyLogoUrl, // 映射 companyLogoUrl 到 logoUrl
      productCode: item.productCode, // 保存产品代码
      productType: item.productType, // 添加产品类型字段
      supportCurrency: item.supportCurrency || [], // 保存支持的货币类型
      proposalLanguages: [], // 默认空值
      supportWithdrawal: item.supportWithdrawal, // 是否支持提取金额
      prepaymentYears: item.prepaymentYears, // 预缴年份 (Array of options for dropdown)
      productContributionTerm: item.paymentYears, // 新增: 产品的固定供款年限 (e.g., "5", "10")
      paymentMethods: item.paymentMethods, // 缴费方式
      canLockendPeriodBonus: item.canLockendPeriodBonus, // 是否可以鎖定終期紅利
      customWithdrawalAgeOffset: item.customWithdrawalAgeOffset, // 自定义提取的年龄偏移量
      amountRange: item.amountRange || [], // 保额范围限制
      premiumRange: item.premiumRange || [], // 保费范围限制
      productCode: item.productCode,
    }));

    allProductOptions.value = mappedProducts;
    productOptions.value = mappedProducts;

    return Promise.resolve();
  } catch (error) {
    console.error('获取产品列表失败', error);
    message.error('获取产品列表失败');
    return Promise.reject(error);
  }
};

/**
 * 获取保司列表
 */
const getCompanyList = async (regionKey = '') => {
  try {
    const res = await companyAPI.getCompanyPage();
    let companies = res || [];

    // 如果传入了地区参数，需要将地区key转换为对应的中文名称进行筛选
    if (regionKey) {
      // 将地区key转换为对应的中文名称
      const regionName = AreaEnum[regionKey] || regionKey;
      companies = companies.filter(company => company.region === regionName);
    }

    // 格式化为下拉选项格式
    const formattedCompanies = companies.map(company => ({
      value: company.code || company.id,
      label: company.name,
      region: company.region,
    }));

    // 添加"全部"选项
    companyOptions.value = [
      { value: '', label: '全部' },
      ...formattedCompanies
    ];

    return Promise.resolve();
  } catch (error) {
    console.error('获取保司列表失败', error);
    message.error('获取保司列表失败');
    return Promise.reject(error);
  }
};

/**
 * 产品分类变更处理
 */
const handleProductTypeChange = (value) => {
  resetProductSelection();
  formState.selectedActualProductType = undefined;
  resetLanguageOptions();
};

/**
 * 货币类型变更处理
 */
const handleCurrencyChange = (value) => {
  resetProductSelection();
  formState.selectedActualProductType = undefined;
  resetLanguageOptions();
};

/**
 * 产品货币变更处理
 */
const handleProductCurrencyChange = (value) => {
  // 只更新产品货币，不重置产品选择
  formState.productCurrency = value;
};

/**
 * 产品名称搜索变更处理
 */
const handleProductNameSearchChange = () => {

  resetProductSelection();
  formState.selectedActualProductType = undefined;
  resetLanguageOptions();
};

/**
 * 地区变更处理
 */
const handleRegionChange = (value) => {
  resetProductSelection();
  formState.selectedActualProductType = undefined;
  resetLanguageOptions();

  // 重置保司选择
  formState.company = undefined;

  // 根据选择的地区获取保司列表
  getCompanyList(value);
};

/**
 * 保司选择变更处理
 */
const handleCompanyChange = (value) => {
  resetProductSelection();
  formState.selectedActualProductType = undefined;
  resetLanguageOptions();
};

/**
 * 更新语言选项
 */
const updateLanguageOptions = (product) => {
  if (product?.proposalLanguages?.length > 0) {
    // 过滤语言选项，只保留中文繁体和英文
    const filteredLanguages = product.proposalLanguages.filter(code =>
      code === 'zh-TW' || code === 'en-US' || code === 'HK' || code === 'EN');

    if (filteredLanguages.length > 0) {
      languageOptions.value = filteredLanguages.map(code => ({
        value: code,
        label: getLanguageLabel(code)
      }));

      // 默认选择第一个语言
      formState.language = languageOptions.value[0].value;
    } else {
      resetLanguageOptions();
      formState.language = 'zh-TW';
    }
  } else {
    resetLanguageOptions();
    formState.language = 'zh-TW';
  }
};

/**
 * 产品选择变更处理
 */
const handleProductChange = (value) => {
  // 重置相关字段
  resetProductSelection();
  formState.selectedActualProductType = undefined;

  // 查找选中的产品
  const selectedProduct = allProductOptions.value.find(item => item.value === value);
  if (!selectedProduct) {
    return; // 如果没有找到产品，已经在resetProductSelection中重置了相关状态
  }

  // 更新产品相关信息
  formState.productName = selectedProduct.label;
  formState.productId = selectedProduct.value;
  formState.selectedActualProductType = selectedProduct.productType;
  formState.productCode = selectedProduct.productCode;

  // 设置货币类型
  if (selectedProduct.supportCurrency?.length > 0) {
    // 只有当产品只支持一种货币时，才自动设置
    if (selectedProduct.supportCurrency.length === 1) {
      formState.productCurrency = selectedProduct.supportCurrency[0];
    }
  }

  // 如果选择的是医疗类产品，重置金额相关字段
  if (selectedProduct.productType === 'MEDICAL') {
    formState.amount = undefined;
    formState.amountType = 0;
  }

  // 更新语言选项
  updateLanguageOptions(selectedProduct);
};

/**
 * 金额类型变化处理
 */
const handleAmountTypeChange = (value) => {
  // 当金额类型变化时，重置金额
  formState.amount = undefined;
};

/**
 * 缴费方式变化处理
 */
const handlePaymentMethodChange = (value) => {
  // 如果不是年缴，清空预缴年份的选择
  if (value !== 'ANNUALLY') {
    formState.prepaymentYear = undefined;
  }
};

/**
 * 处理锁定终期红利百分比按钮点击
 */
const handleLockBonusPercentageChange = (percentage) => {
  formState.lockBonusPercentage = percentage;

  // 手动触发验证
  setTimeout(() => {
    const form = document.querySelector('form[name="plan-form"]');
    if (form) {
      const event = new Event('change', { bubbles: true });
      const lockBonusPercentageField = form.querySelector('[name="lockBonusPercentage"]');
      if (lockBonusPercentageField) {
        lockBonusPercentageField.dispatchEvent(event);
      }
    }
  }, 0);
};

// ==================== 表单提交与重置 ====================

/**
 * 表单提交 - 显示确认模态框
 */
const onFinish = async (values) => {
  Modal.confirm({
    title: t('plans.status'),
    content: t('plans.confirmModal'),
    onOk: async () => {
      await submitForm(values);
    }
  });
};

/**
 * 实际的表单提交逻辑
 */
const submitForm = async (values) => {
  try {
    message.loading({ content: t('plans.generatingTask'), key: 'generatePlan' });

    // 构造符合后端格式的请求参数
    const requestData = {
      // 产品信息
      productId: formState.productId,
      productName: formState.productName,
      productType: formState.selectedActualProductType || '',
      productCode: formState.productCode,

      // 客户信息
      customerName: values.customerName || '',
      age: values.age || 0,
      gender: values.gender || 'male',
      isSmoking: values.isSmoker === 'smoker', // 转换为布尔值

      // 货币和语言
      language: formState.language || 'zh-TW',
      currency: formState.productCurrency || formState.currency || 'HKD',

      // 支付相关
      paymentMethod: formState.paymentMethod || 'ANNUALLY',
      prepaymentYear: formState.prepaymentYear || undefined,

      // 自定义现金提取
      enableCustomWithdrawal: formState.enableCustomWithdrawal,
      withdrawalStartAge: formState.enableCustomWithdrawal ? formState.withdrawalStartAge : undefined,
      withdrawalEndAge: formState.enableCustomWithdrawal ? formState.withdrawalEndAge : undefined,
      initialWithdrawalAmount: formState.enableCustomWithdrawal ? formState.initialWithdrawalAmount : undefined,
      withdrawalGrowthRate: formState.enableCustomWithdrawal ? formState.withdrawalGrowthRate : undefined,

      // 锁定终期红利
      enableLockEndBonus: formState.enableLockEndBonus,
      lockBonusStartAge: formState.enableLockEndBonus ? formState.lockBonusStartAge : undefined,
      lockBonusEndAge: formState.enableLockEndBonus ? formState.lockBonusEndAge : undefined,
      lockBonusPercentage: formState.enableLockEndBonus ? formState.lockBonusPercentage : undefined,
    };

    // 如果不是医疗类产品，添加金额相关字段
    if (showProtectionInfo.value) {
      requestData.amountType = formState.amountType;
      requestData.amount = String(values.amount);
    }

    console.log(requestData);
    await planAPI.generatePlan(requestData);

    message.success({ content: t('plans.generationSuccess'), key: 'generatePlan', duration: 3 });

    // // 跳转到计划书列表页面
    // setTimeout(() => {
    //   window.location.href = '/my-plans';
    // }, 1500);

  } catch (error) {
    console.error('生成计划书失败:', error);
    message.error({ content: t('plans.generationFailure'), key: 'generatePlan' });
  }
};

/**
 * 表单重置
 */
const resetForm = () => {
  // 重置表单字段
  Object.assign(formState, {
    // 产品相关
    productId: undefined,
    productType: undefined,
    region: undefined,
    company: undefined,
    selectedActualProductType: undefined,
    productName: '',
    productNameSearch: '',
    productCode: undefined,

    // 货币相关
    currency: undefined,
    productCurrency: undefined,

    // 客户信息
    customerName: '',
    age: undefined,
    gender: 'male',
    isSmoker: 'non-smoker',

    // 保障信息
    amount: undefined,
    amountType: 0,

    // 支付相关
    paymentMethod: undefined,
    prepaymentYear: undefined,

    // 自定义现金提取
    enableCustomWithdrawal: false,
    withdrawalStartAge: undefined,
    withdrawalEndAge: undefined,
    initialWithdrawalAmount: undefined,
    withdrawalGrowthRate: undefined,

    // 锁定终期红利
    enableLockEndBonus: false,
    lockBonusStartAge: undefined,
    lockBonusEndAge: undefined,
    lockBonusPercentage: undefined,

    // 语言
    language: 'zh-TW',
  });

  // 重置下拉选项为默认值
  resetLanguageOptions();

  // 重置产品列表为全部产品
  productOptions.value = allProductOptions.value;
};

// ==================== 监听器 ====================

/**
 * 监听自定义现金提取开关变化
 * 当开启自定义现金提取时，自动关闭锁定终期红利
 */
watch(() => formState.enableCustomWithdrawal, (newValue) => {
  if (newValue === true && formState.enableLockEndBonus === true) {
    formState.enableLockEndBonus = false;
  }
});

/**
 * 监听锁定终期红利开关变化
 * 当开启锁定终期红利时，自动关闭自定义现金提取
 */
watch(() => formState.enableLockEndBonus, (newValue) => {
  if (newValue === true && formState.enableCustomWithdrawal === true) {
    formState.enableCustomWithdrawal = false;
  }
});

/**
 * 监听锁定终期红利开始年龄变化
 * 当开始年龄变化时，如果已经选择了锁定比例，重新触发锁定比例的验证
 */
watch(() => formState.lockBonusStartAge, (newValue) => {
  if (formState.enableLockEndBonus && formState.lockBonusPercentage !== undefined) {
    // 延迟触发验证，确保Vue已经更新了表单状态
    setTimeout(() => {
      const form = document.querySelector('form[name="plan-form"]');
      if (form) {
        const event = new Event('change', { bubbles: true });
        const lockBonusPercentageField = form.querySelector('[name="lockBonusPercentage"]');
        if (lockBonusPercentageField) {
          lockBonusPercentageField.dispatchEvent(event);
        }
      }
    }, 0);
  }
});

/**
 * 监听锁定终期红利结束年龄变化
 * 当结束年龄变化时，如果已经选择了锁定比例，重新触发锁定比例的验证
 */
watch(() => formState.lockBonusEndAge, (newValue) => {
  if (formState.enableLockEndBonus && formState.lockBonusPercentage !== undefined) {
    // 延迟触发验证，确保Vue已经更新了表单状态
    setTimeout(() => {
      const form = document.querySelector('form[name="plan-form"]');
      if (form) {
        const event = new Event('change', { bubbles: true });
        const lockBonusPercentageField = form.querySelector('[name="lockBonusPercentage"]');
        if (lockBonusPercentageField) {
          lockBonusPercentageField.dispatchEvent(event);
        }
      }
    }, 0);
  }
});

// ==================== 生命周期钩子 ====================

/**
 * 组件挂载时加载数据
 */
onMounted(() => {
  // 加载产品分类数据
  productStore.getProductCategoryList();
  // 同时加载产品列表和保司列表
  Promise.all([
    getProductList(),
    getCompanyList() // 初始加载所有保司
  ]).then(() => {
    // 如果没有productId但有productCode参数，执行现有的productCode处理逻辑
    if (productIdFromUrl) {
      // 确保productCodeFromUrl是字符串
      formState.productId = ensureString(productIdFromUrl);
      // 自动触发产品名称搜索变更处理
      handleProductNameSearchChange();
    }
    // 如果只有productName参数，将其填充到搜索框
    else if (productNameFromUrl) {
      console.log('productNameFromUrl', productNameFromUrl);
      formState.productNameSearch = ensureString(productNameFromUrl);
      handleProductNameSearchChange();
    }
  });
});
</script>

<style scoped>
/* 主题色定义 */
:root {
  --color-primary: #3B82F6;
  --color-primary-hover: #2563EB;
  --color-secondary: #6366F1;
  --color-success: #10B981;
  --color-warning: #F59E0B;
  --color-error: #EF4444;
  --color-info: #6B7280;
}

/* 标题区域动画 */
.title-section {
  background-size: 200% 200%;
  animation: gradientAnimation 5s ease infinite;
}

@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

.page-title {
  color: #fff;
}

.page-description {
  color: #e0e0e0;
}

/* 使用 Google 风格的阴影和过渡效果 */
.shadow-md {
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1), 0 3px 10px 0 rgba(0, 0, 0, 0.05);
}

/* 按钮内图标对齐样式 */
.align-middle {
  vertical-align: middle;
}

.inline-flex {
  display: inline-flex;
}

:deep(.product-select .ant-select-selector) {
  height: 48px !important;
  padding: 8px 12px !important;
}

:deep(.product-select .ant-select-selection-search) {
  display: flex;
  align-items: center;
  height: 100%;
}

:deep(.product-select .ant-select-selection-placeholder) {
  display: flex;
  align-items: center;
  font-size: 16px;
}

/* 选中项垂直居中对齐 */
:deep(.product-select .ant-select-selection-item) {
  display: flex;
  align-items: center;
  height: 100%;
  line-height: normal;
  white-space: normal;
}

/* 产品选择下拉框样式 */
:deep(.ant-select-item) {
  padding: 8px 12px;
}

:deep(.ant-select-item + .ant-select-item) {
  border-top: 1px solid #f0f0f0;
}

:deep(.ant-select-item-option-content) {
  white-space: normal;
}

/* 标题和表单项垂直对齐样式 */
.flex.items-center {
  min-height: 32px;
}

.form-section-title {
  margin-bottom: 24px;
  height: 32px;
  display: flex;
  align-items: center;
}

:deep(.ant-form-item-label) {
  height: 32px;
  line-height: 32px;
}

:deep(.form-item-with-label .ant-form-item-label > label) {
  height: 32px;
  display: flex;
  align-items: center;
}

/* 确保表单模块标题和内容的对齐 */
.mb-8 {
  margin-bottom: 32px;
}

/* 统一表单项间距 */
:deep(.ant-form-item) {
  margin-bottom: 24px;
}

:deep(.ant-form-item:last-child) {
  margin-bottom: 0;
}

/* 搜索输入框样式 */
:deep(.ant-input-affix-wrapper) {
  display: flex;
  align-items: center;
}

:deep(.ant-input-affix-wrapper .ant-input-prefix) {
  display: flex;
  align-items: center;
}

:deep(.ant-input-affix-wrapper .ant-input-suffix) {
  display: flex;
  align-items: center;
}

:deep(.ant-input-affix-wrapper .ant-input-clear-icon) {
  display: flex;
  align-items: center;
  margin-top: 0;
}

/* 移除嵌套FormItem在Row中的多余底部边距，如果其父FormItem已有边距 */
:deep(.custom-nested-form-item .ant-form-item) {
  margin-bottom: 0 !important;
}
</style>