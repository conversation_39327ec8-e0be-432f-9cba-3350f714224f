<template>
    <div class="appointment-overview-form">
        <a-form ref="formRef" :model="formState" :rules="rules" layout="vertical" @finish="onFinish">
            <div class="grid grid-cols-2 gap-x-6">
                <a-form-item label="保险公司" name="company" :rules="rules.company">
                    <a-select v-model:value="formState.company" placeholder="请选择保险公司" show-search
                        :filter-option="filterOption" :options="companyOptions" @change="handleCompanyChange">
                    </a-select>
                </a-form-item>

                <a-form-item label="产品名称" name="product" :rules="rules.product">
                    <a-select v-model:value="formState.product" placeholder="请输入产品名称" show-search
                        :loading="productLoading" :filter-option="false" @search="onProductSearch"
                        @change="handleProductChange">
                        <a-select-option v-for="item in productList" :key="item.productId" :value="item.productId">
                            {{ item.name }}
                        </a-select-option>
                    </a-select>
                </a-form-item>

                <a-form-item label="货币类型" name="currencyType" :rules="rules.currencyType">
                    <a-select v-model:value="formState.currencyType" placeholder="请选择货币类型"
                        @change="handleCurrencyChange">
                        <a-select-option value="USD">USD</a-select-option>
                        <a-select-option value="HKD">HKD</a-select-option>
                        <a-select-option value="SGD">SGD</a-select-option>
                        <a-select-option value="CNY">CNY</a-select-option>
                    </a-select>
                </a-form-item>

                <a-form-item label="保额" name="insuredAmount" :rules="rules.insuredAmount">
                    <a-input-number v-model:value="formState.insuredAmount" style="width: 100%" placeholder="请输入保额"
                        :precision="2" :min="0" :formatter="value => formatAmount(value, formState.currencyType)"
                        :parser="value => parseAmount(value)" :step="10000" />
                </a-form-item>

                <a-form-item label="年供保费" name="annualPremium" :rules="rules.annualPremium">
                    <a-input-number v-model:value="formState.annualPremium" style="width: 100%" placeholder="请输入年供保费"
                        :precision="2" :min="0" :formatter="value => formatAmount(value, formState.currencyType)"
                        :parser="value => parseAmount(value)" :step="10000" />
                </a-form-item>

                <a-form-item label="供款年期" name="paymentTerm" :rules="rules.paymentTerm">
                    <a-input-number v-model:value="formState.paymentTerm" style="width: 100%" placeholder="请输入年期"
                        :precision="0" :min="0" :max="100" addonAfter="年" />
                </a-form-item>
                <a-form-item :label="$t('policyorder.paymentMethod')" name="paymentMethod" :rules="rules.paymentMethod">
                    <a-select v-model:value="formState.paymentMethod" placeholder="请选择缴费方式">
                        <a-select-option value="">{{ $t('policyorder.pleaseSelectPaymentMethod') }}</a-select-option>
                        <a-select-option value="ANNUALLY">{{ $t('premiumCalculator.paymentModes.annual')
                        }}</a-select-option>
                        <a-select-option value="MONTHLY">{{ $t('premiumCalculator.paymentModes.monthly')
                        }}</a-select-option>
                        <a-select-option value="QUARTERLY">{{ $t('premiumCalculator.paymentModes.quarterly')
                        }}</a-select-option>
                        <a-select-option value="SEMI_ANNUALLY">{{ $t('premiumCalculator.paymentModes.semiAnnual')
                        }}</a-select-option>
                        <a-select-option value="SINGLE_PREMIUM">{{ $t('premiumCalculator.paymentModes.singlePremium')
                        }}</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="首年保费缴付方式" name="firstYearPaymentMethod" class="col-span-2"
                    :rules="rules.firstYearPaymentMethod">
                    <a-select v-model:value="formState.firstYearPaymentMethod" placeholder="请选择首年保费缴付方式">
                        <a-select-option value="支付宝转账">支付宝转账</a-select-option>
                        <a-select-option value="银行转账">银行转账</a-select-option>
                        <a-select-option value="微信转账">微信转账</a-select-option>
                        <a-select-option value="现金支付">现金支付</a-select-option>
                        <a-select-option value="支票">支票</a-select-option>
                        <a-select-option value="其他">其他</a-select-option>
                    </a-select>
                </a-form-item>

                <a-form-item label="续期保单缴付方式" name="renewalPaymentMethod" class="col-span-2"
                    :rules="rules.renewalPaymentMethod">
                    <a-select v-model:value="formState.renewalPaymentMethod" placeholder="请选择续期保单缴付方式">
                        <a-select-option value="支付宝转账">支付宝转账</a-select-option>
                        <a-select-option value="银行转账">银行转账</a-select-option>
                        <a-select-option value="微信转账">微信转账</a-select-option>
                        <a-select-option value="现金支付">现金支付</a-select-option>
                        <a-select-option value="支票">支票</a-select-option>
                        <a-select-option value="其他">其他</a-select-option>
                    </a-select>
                </a-form-item>

                <a-form-item label="客户姓名" name="customerName" :rules="rules.customerName">
                    <a-input v-model:value="formState.customerName" placeholder="请输入客户姓名" />
                </a-form-item>

                <a-form-item label="顾问联络电话" name="phone" :rules="rules.phone">
                    <div class="grid grid-cols-3 gap-x-2">
                        <a-select v-model:value="formState.phoneAreaCode" placeholder="区号" style="width: 100%"
                            @change="handleAreaCodeChange">
                            <a-select-option v-for="option in areaCodeOptions" :key="option.value"
                                :value="option.value">
                                {{ option.label }}
                            </a-select-option>
                        </a-select>
                        <div class="col-span-2">
                            <a-input v-model:value="formState.phone" placeholder="请输入手机号码" />
                        </div>
                    </div>
                </a-form-item>

                <a-form-item label="顾问邮箱" name="email" :rules="rules.email">
                    <a-input v-model:value="formState.email" placeholder="请输入邮箱地址" />
                </a-form-item>

                <a-form-item label="团队/主管" name="team" :rules="rules.team">
                    <a-input v-model:value="formState.team" placeholder="请输入团队/主管名称" />
                </a-form-item>

                <a-form-item label="介绍人" name="referrer" :rules="rules.referrer">
                    <a-input v-model:value="formState.referrer" placeholder="请输入介绍人姓名" />
                </a-form-item>

                <a-form-item label="地区" name="region" :rules="rules.region">
                    <a-select v-model:value="formState.region" placeholder="请选择地区">
                        <a-select-option value="香港">香港</a-select-option>
                        <a-select-option value="澳门">澳门</a-select-option>
                        <a-select-option value="新加坡">新加坡</a-select-option>
                        <!-- <a-select-option value="中国大陆">中国大陆</a-select-option> -->
                        <a-select-option value="百慕大">百慕大</a-select-option>
                    </a-select>
                </a-form-item>

                <a-form-item label="预约时间" name="appointmentDate" :rules="rules.appointmentDate">
                    <div class="grid grid-cols-2 gap-x-2">
                        <a-date-picker v-model:value="formState.appointmentDate" style="width: 100%" format="YYYY-MM-DD"
                            placeholder="请选择日期" />
                        <a-select v-model:value="formState.appointmentTimeStr" placeholder="请选择时间"
                            :rules="rules.appointmentTimeStr">
                            <a-select-option v-for="time in timeOptions" :key="time.value" :value="time.value">
                                {{ time.label }}
                            </a-select-option>
                        </a-select>
                    </div>
                </a-form-item>
            </div>

            <a-form-item label="备注" name="remark" :rules="rules.remark">
                <a-textarea v-model:value="formState.remark" placeholder="请输入备注" :rows="3" />
            </a-form-item>
        </a-form>
    </div>
</template>

<script setup>
import { defineProps, defineEmits, defineExpose, ref, computed, watch, onMounted } from 'vue';
import { formRules } from '@/utils/formValidate';
import { useFormBinding, useFormValidation } from '@/utils/formHooks';
import { useProductStore } from '@/store/modules/product';
import { useProductSearch } from '@/views/tools/components/interest/composables/useProductSearch';
import dayjs from 'dayjs';

// 区号列表常量
const areaCodeOptions = [
    { label: '中国大陆 +86', value: '+86', pattern: /^1[3-9]\d{9}$/ },
    { label: '香港 +852', value: '+852', pattern: /^[5-9]\d{7}$/ },
    { label: '澳门 +853', value: '+853', pattern: /^[6]\d{7}$/ },
    { label: '台湾 +886', value: '+886', pattern: /^[0-9]\d{8}$/ },
    { label: '新加坡 +65', value: '+65', pattern: /^[89]\d{7}$/ },
    { label: '美国/加拿大 +1', value: '+1', pattern: /^[2-9]\d{9}$/ },
    { label: '日本 +81', value: '+81', pattern: /^[0-9]\d{9}$/ },
    { label: '韩国 +82', value: '+82', pattern: /^[0-9]\d{8,9}$/ },
    { label: '英国 +44', value: '+44', pattern: /^[0-9]\d{9}$/ },
    { label: '澳大利亚 +61', value: '+61', pattern: /^[0-9]\d{8}$/ },
];

const props = defineProps({
    formData: {
        type: Object,
        default: () => ({})
    }
});

const emit = defineEmits(['update:form-data']);

// 初始化产品store
const productStore = useProductStore();
// 初始化产品搜索
const { productList, productLoading, onProductSearch } = useProductSearch(true, { isCashValue: 0 });

// 获取公司列表
const companyOptions = ref([]);
const fetchCompanyList = async () => {
    await productStore.getCompanyList();
    companyOptions.value = productStore.formattedCompanyList;
};

// 过滤选项
const filterOption = (input, option) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

// 处理公司变更
const handleCompanyChange = (value) => {
    // 当公司变更时，可以根据需要更新产品列表
    // 格式化productname为繁体中文
    formState.product = ''

    onProductSearch(value);
};

// 处理产品变更
const handleProductChange = (productId) => {
    // 当选择产品时，可以根据需要获取产品详情
    const selectedProduct = productList.value.find(item => item.productId === productId);
    if (selectedProduct) {
        // 可以根据需要更新其他字段
        formState.productName = selectedProduct.name;
    }
};

// 处理货币类型变更
const handleCurrencyChange = () => {
    // 当货币类型变更时，重新格式化金额
    if (formState.insuredAmount) {
        formState.insuredAmount = formState.insuredAmount;
    }
    if (formState.annualPremium) {
        formState.annualPremium = formState.annualPremium;
    }
};

// 格式化金额
const formatAmount = (value, currencyType) => {
    if (!value) return '';

    // 获取货币符号
    const currencySymbol = getCurrencySymbol(currencyType);

    // 添加千位分隔符
    const parts = value.toString().split('.');
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');

    return `${currencySymbol} ${parts.join('.')}`;
};

// 解析金额
const parseAmount = (value) => {
    if (!value) return '';

    // 处理"w"或"W"表示的万元
    if (value.toString().toLowerCase().includes('w')) {
        // 提取数字部分
        const numPart = value.toString().toLowerCase().replace(/[^0-9.]/g, '');
        if (numPart) {
            // 将数字乘以10000（万）
            return parseFloat(numPart) * 10000;
        }
    }

    // 移除货币符号和千位分隔符
    return value.replace(/[^\d.]/g, '');
};

// 获取货币符号
const getCurrencySymbol = (currencyType) => {
    switch (currencyType) {
        case 'USD':
            return '$';
        case 'HKD':
            return 'HK$';
        case 'SGD':
            return 'S$';
        case 'CNY':
            return '¥';
        default:
            return 'HK$';
    }
};

// 根据区号验证手机号
const validatePhoneWithAreaCode = (phone, areaCode) => {
    if (!phone || !areaCode) return false;

    // 查找对应区号的验证规则
    const areaCodeOption = areaCodeOptions.find(option => option.value === areaCode);
    if (!areaCodeOption) return false;

    // 使用对应的正则表达式验证手机号
    return areaCodeOption.pattern.test(phone);
};

// 处理区号变更
const handleAreaCodeChange = (value) => {
    // 当区号变更时，可以进行一些额外操作，如清空或重新验证手机号
    // 这里暂时不做特殊处理
};

// 小时选项（8点至20点）
const hourOptions = computed(() => {
    const hours = [];
    for (let i = 8; i <= 20; i++) {
        hours.push(i.toString().padStart(2, '0'));
    }
    return hours;
});

// 分钟选项（15分钟间隔：00, 15, 30, 45）
const minuteOptions = computed(() => {
    return ['00', '15', '30', '45'];
});

// 时间选项（8:00至20:00，半小时间隔）
const timeOptions = computed(() => {
    const times = [];
    for (let hour = 8; hour <= 20; hour++) {
        // 添加整点
        times.push({
            label: `${hour.toString().padStart(2, '0')}:00`,
            value: `${hour.toString().padStart(2, '0')}:00`
        });

        // 添加半点（对于20:00不添加半点）
        if (hour < 20) {
            times.push({
                label: `${hour.toString().padStart(2, '0')}:30`,
                value: `${hour.toString().padStart(2, '0')}:30`
            });
        }
    }
    return times;
});

// 初始表单状态
const initialState = {
    customerName: '张三',
    phone: '13288888888',
    phoneAreaCode: '+86', // 默认为中国大陆区号
    email: '<EMAIL>',
    team: '团队1',
    referrer: '李四',
    company: undefined,
    region: '香港',
    appointmentDate: null,
    appointmentHour: '09',
    appointmentMinute: '00',
    appointmentTimeStr: '09:00', // 新增时间字符串字段
    appointmentTime: null, // 保留用于存储组合后的完整时间
    product: undefined,
    productName: '', // 存储产品名称
    premium: null,
    paymentTerm: 10,
    paymentMethod: 'ANNUALLY',
    remark: '备注',
    currencyType: 'HKD', // 默认为HKD
    insuredAmount: 100000,
    annualPremium: 10000,
    paymentYears: 10,
    firstYearPaymentMethod: '支票',
    renewalPaymentMethod: '支票'
};

// 使用表单绑定钩子
const { formRef, formState, onFinish, resetFields } = useFormBinding(
    props,
    emit,
    initialState,
    ['appointmentDate', 'appointmentTime']
);

// 组件挂载时获取公司列表
onMounted(() => {
    fetchCompanyList();
});

// 监听日期、小时和分钟的变化，更新appointmentTime
watch([() => formState.appointmentDate, () => formState.appointmentHour, () => formState.appointmentMinute],
    ([date, hour, minute]) => {
        if (date && hour && minute) {
            // 组合日期和时间
            const dateObj = dayjs(date);
            formState.appointmentTime = dateObj.hour(parseInt(hour)).minute(parseInt(minute));
        } else {
            formState.appointmentTime = null;
        }
    },
    { immediate: true }
);

// 监听日期和时间字符串的变化，更新appointmentTime
watch([() => formState.appointmentDate, () => formState.appointmentTimeStr],
    ([date, timeStr]) => {
        if (date && timeStr) {
            // 从时间字符串中提取小时和分钟
            const [hour, minute] = timeStr.split(':');

            // 组合日期和时间
            const dateObj = dayjs(date);
            formState.appointmentTime = dateObj.hour(parseInt(hour)).minute(parseInt(minute));

            // 同步更新小时和分钟字段（用于向后兼容）
            formState.appointmentHour = hour;
            formState.appointmentMinute = minute;
        } else {
            formState.appointmentTime = null;
        }
    },
    { immediate: true }
);

// 监听父组件传入的formData中的appointmentTime，拆分为日期、小时和分钟
watch(() => props.formData?.appointmentTime,
    (newVal) => {
        if (newVal) {
            const dateTime = dayjs(newVal);
            if (dateTime.isValid()) {
                formState.appointmentDate = dateTime;
                formState.appointmentHour = dateTime.format('HH');
                formState.appointmentMinute = dateTime.format('mm');
            }
        }
    },
    { immediate: true }
);

// 监听父组件传入的formData中的appointmentTime，拆分为日期和时间字符串
watch(() => props.formData?.appointmentTime,
    (newVal) => {
        if (newVal) {
            const dateTime = dayjs(newVal);
            if (dateTime.isValid()) {
                formState.appointmentDate = dateTime;
                const hour = dateTime.format('HH');
                const minute = dateTime.format('mm');
                formState.appointmentTimeStr = `${hour}:${minute}`;

                // 同步更新小时和分钟字段（用于向后兼容）
                formState.appointmentHour = hour;
                formState.appointmentMinute = minute;
            }
        }
    },
    { immediate: true }
);

// 表单验证规则
const rules = {
    company: [
        { required: true, message: '请选择保险公司', trigger: 'change' }
    ],
    product: [
        { required: true, message: '请选择产品', trigger: 'change' }
    ],
    currencyType: [
        { required: true, message: '请选择货币类型', trigger: 'change' }
    ],
    insuredAmount: [
        { required: true, message: '请输入保额', trigger: 'blur' },
        { type: 'number', min: 0, message: '保额不能为负数', trigger: 'blur' }
    ],
    annualPremium: [
        { required: true, message: '请输入年供保费', trigger: 'blur' },
        { type: 'number', min: 0, message: '年供保费不能为负数', trigger: 'blur' }
    ],
    paymentTerm: [
        { required: true, message: '请输入供款年期', trigger: 'blur' },
        { type: 'number', min: 0, max: 100, message: '供款年期应在0-100年之间', trigger: 'blur' }
    ],
    paymentMethod: [
        { required: true, message: '请选择缴费方式', trigger: 'change' }
    ],
    firstYearPaymentMethod: [
        { required: true, message: '请输入首年保费缴付方式', trigger: 'change' }
    ],
    renewalPaymentMethod: [
        { required: true, message: '请选择续期保单缴付方式', trigger: 'change' }
    ],
    customerName: [
        { required: true, message: '请输入客户姓名', trigger: 'blur' },
        { min: 2, max: 20, message: '姓名长度应在2-20个字符之间', trigger: 'blur' }
    ],
    phone: [
        { required: true, message: '请输入手机号码', trigger: 'blur' },
        {
            validator: (rule, value) => {
                if (!value || validatePhoneWithAreaCode(value, formState.phoneAreaCode)) {
                    return Promise.resolve();
                }
                return Promise.reject(`请输入有效的${formState.phoneAreaCode}手机号码`);
            },
            trigger: 'blur'
        }
    ],
    phoneAreaCode: [
        { required: true, message: '请选择区号', trigger: 'change' }
    ],
    email: formRules.email(true),
    team: [
        { required: false, message: '请输入团队/主管名称', trigger: 'blur' }
    ],
    referrer: [
        { required: false, message: '请输入介绍人姓名', trigger: 'blur' }
    ],
    region: [
        { required: true, message: '请选择地区', trigger: 'change' }
    ],
    appointmentDate: [
        { required: true, message: '请选择预约日期', trigger: 'change' }
    ],
    appointmentHour: [
        { required: true, message: '请选择预约时间（小时）', trigger: 'change' }
    ],
    appointmentMinute: [
        { required: true, message: '请选择预约时间（分钟）', trigger: 'change' }
    ],
    appointmentTimeStr: [
        { required: true, message: '请选择预约时间', trigger: 'change' }
    ],
    remark: [
        { required: false, message: '请输入备注', trigger: 'blur' },
        { max: 200, message: '备注不能超过200个字符', trigger: 'blur' }
    ]
};

// 必填字段列表
const requiredFields = [
    'company', 'product', 'currencyType', 'insuredAmount',
    'annualPremium', 'paymentTerm', 'paymentMethod',
    'customerName', 'phone', 'phoneAreaCode', 'email', 'region',
    'appointmentDate', 'appointmentTimeStr'
];

// 使用表单验证钩子
const validate = useFormValidation(formRef, formState, requiredFields, '请完善预约概览信息');

// 包装validate函数，确保在验证前更新appointmentTime
const validateForm = () => {
    // 确保appointmentTime已更新
    if (formState.appointmentDate && formState.appointmentTimeStr) {
        const [hour, minute] = formState.appointmentTimeStr.split(':');
        const dateObj = dayjs(formState.appointmentDate);
        formState.appointmentTime = dateObj.hour(parseInt(hour)).minute(parseInt(minute));

        // 同步更新小时和分钟字段（用于向后兼容）
        formState.appointmentHour = hour;
        formState.appointmentMinute = minute;
    }

    // 调用原始验证函数
    return validate();
};

// 向父组件暴露方法
defineExpose({
    validate: validateForm,
    resetFields,
    formState
});
</script>

<style scoped>
.appointment-overview-form {
    margin-bottom: 20px;
}
</style>