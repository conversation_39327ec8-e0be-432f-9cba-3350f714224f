<template>
    <div class="attachment-upload">
        <div class="mb-6">
            <h3 class="text-lg font-medium mb-4">上传附件</h3>
            <p class="text-gray-500 mb-4">请上传与预约相关的证件和材料</p>
        </div>

        <!-- 文件上传区域 -->
        <div class="grid grid-cols-1 gap-6">
            <!-- 附件上传卡片 -->
            <a-card title="附件材料" class="attachment-card">
                <template #extra>
                    <a-button type="primary" size="small" @click="showUploadModal">上传文件</a-button>
                </template>
                <div v-if="attachmentList.length === 0" class="text-center p-4 rounded-lg">
                    <p class="text-gray-500">暂无上传的附件材料，请点击"上传文件"按钮添加</p>
                </div>
                <a-list v-else size="small" class="attachment-list">
                    <a-list-item v-for="(file, index) in attachmentList" :key="index">
                        <a-list-item-meta>
                            <template #avatar v-if="isImageFile(file.fileType)">
                                <div class="thumbnail-container">
                                    <img :src="file.previewUrl || ''" class="file-thumbnail" />
                                </div>
                            </template>
                            <template #title>
                                <div class="flex items-center">
                                    <Icon v-if="!isImageFile(file.fileType)" :icon="getFileIcon(file.fileType)"
                                        class="text-lg mr-2" :class="getIconColor(file.fileType)" />
                                    <span>{{ file.fileName }}</span>
                                </div>
                            </template>
                            <template #description>
                                <span>{{ file.description || '无描述' }} | {{ formatFileSize(file.fileSize) }}</span>
                            </template>
                        </a-list-item-meta>
                        <template #actions>
                            <a-button type="text" danger @click="removeAttachment(file)">
                                <template #icon>
                                    <Icon icon="material-symbols:delete-outline" class="text-red-500" />
                                </template>
                            </a-button>
                        </template>
                    </a-list-item>
                </a-list>
            </a-card>
        </div>

        <!-- 上传文件对话框 -->
        <a-modal v-model:visible="uploadModalVisible" title="上传文件" @ok="handleUploadOk" @cancel="handleUploadCancel"
            :okButtonProps="{ disabled: !currentFile }">
            <a-form layout="vertical">
                <a-form-item label="选择文件" required>
                    <a-upload list-type="text" :fileList="fileList" :beforeUpload="beforeUpload" :maxCount="1"
                        @remove="onRemove">
                        <a-button v-if="fileList.length < 1">
                            <template #icon>
                                <Icon icon="material-symbols:upload" />
                            </template>
                            选择文件
                        </a-button>
                    </a-upload>
                </a-form-item>

                <a-form-item label="文件描述">
                    <a-textarea v-model:value="fileDescription" placeholder="请输入文件描述" :rows="3" />
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';
import { ref, defineProps, defineEmits, watch } from 'vue';
import { message } from 'ant-design-vue';

const props = defineProps({
    attachments: {
        type: Array,
        default: () => []
    }
});

const emit = defineEmits(['update:attachments']);

// 附件列表
const attachmentList = ref([]);

// 上传对话框相关状态
const uploadModalVisible = ref(false);
const fileList = ref([]);
const fileDescription = ref('');
const currentFile = ref(null);

// 显示上传对话框
const showUploadModal = () => {
    uploadModalVisible.value = true;
    fileList.value = [];
    fileDescription.value = '';
    currentFile.value = null;
};

// 上传前验证
const beforeUpload = (file) => {
    // 限制文件大小为20MB
    const isLt20M = file.size / 1024 / 1024 < 20;
    if (!isLt20M) {
        message.error('文件必须小于20MB!');
        return false;
    }

    // 保存当前文件
    currentFile.value = file;
    fileList.value = [file];

    return false; // 阻止自动上传
};

// 移除已选择文件
const onRemove = () => {
    fileList.value = [];
    currentFile.value = null;
};

// 确认上传
const handleUploadOk = () => {
    if (!currentFile.value) {
        message.error('请选择要上传的文件');
        return;
    }

    const fileType = currentFile.value.name.split('.').pop().toLowerCase();

    // 创建符合后端AttachmentDTO格式的对象
    const newAttachment = {
        id: Date.now(),
        fileName: currentFile.value.name,
        fileType: fileType,
        fileSize: currentFile.value.size,
        description: fileDescription.value,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        // 保存原始File对象，用于后续FormData提交
        file: currentFile.value
    };

    // 如果是图片文件，生成预览URL
    if (isImageFile(fileType)) {
        newAttachment.previewUrl = URL.createObjectURL(currentFile.value);
    }

    // 添加到附件列表
    attachmentList.value.push(newAttachment);

    // 通知父组件
    emit('update:attachments', [...attachmentList.value]);

    // 重置状态
    uploadModalVisible.value = false;
    fileList.value = [];
    fileDescription.value = '';
    currentFile.value = null;

    message.success('文件上传成功');
};

// 取消上传
const handleUploadCancel = () => {
    uploadModalVisible.value = false;
    fileList.value = [];
    fileDescription.value = '';
    currentFile.value = null;
};

// 移除附件
const removeAttachment = (file) => {
    const index = attachmentList.value.findIndex(item => item.id === file.id);
    if (index !== -1) {
        attachmentList.value.splice(index, 1);
        emit('update:attachments', [...attachmentList.value]);
        message.success('文件已移除');
    }
};

// 获取文件图标
const getFileIcon = (fileType) => {
    const iconMap = {
        'pdf': 'mdi:file-pdf-box',
        'doc': 'mdi:file-word-box',
        'docx': 'mdi:file-word-box',
        'xls': 'mdi:file-excel-box',
        'xlsx': 'mdi:file-excel-box',
        'ppt': 'mdi:file-powerpoint-box',
        'pptx': 'mdi:file-powerpoint-box',
        'jpg': 'mdi:file-image-box',
        'jpeg': 'mdi:file-image-box',
        'png': 'mdi:file-image-box',
        'gif': 'mdi:file-image-box'
    };
    return iconMap[fileType] || 'mdi:file-box';
};

// 获取图标颜色
const getIconColor = (fileType) => {
    const colorMap = {
        'pdf': 'text-red-500',
        'doc': 'text-blue-500',
        'docx': 'text-blue-500',
        'xls': 'text-green-500',
        'xlsx': 'text-green-500',
        'ppt': 'text-orange-500',
        'pptx': 'text-orange-500',
        'jpg': 'text-purple-500',
        'jpeg': 'text-purple-500',
        'png': 'text-purple-500',
        'gif': 'text-purple-500'
    };
    return colorMap[fileType] || 'text-gray-500';
};

// 格式化文件大小
const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 监听父组件传入的附件数据
watch(() => props.attachments, (newVal) => {
    if (newVal && newVal.length > 0) {
        attachmentList.value = [...newVal];
    }
}, { deep: true, immediate: true });

// 验证方法（供父组件调用）
const validate = () => {
    return Promise.resolve();
};

// 重置方法（供父组件调用）
const resetFields = () => {
    attachmentList.value = [];
    emit('update:attachments', []);
};

// 向父组件暴露方法
defineExpose({
    validate,
    resetFields,
    attachmentList
});

// 判断文件是否为图片
const isImageFile = (fileType) => {
    const imageTypes = ['jpg', 'jpeg', 'png', 'gif'];
    return imageTypes.includes(fileType);
};
</script>

<style scoped>
.attachment-upload {
    margin-bottom: 20px;
}

.attachment-card {
    margin-bottom: 16px;
}

.attachment-list {
    margin-top: 8px;
}

.thumbnail-container {
    width: 40px;
    height: 40px;
    overflow: hidden;
    border-radius: 4px;
    margin-right: 8px;
}

.file-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
</style>