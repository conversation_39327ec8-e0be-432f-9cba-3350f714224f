<template>
    <div class="beneficiary-form">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium">受益人信息</h3>
            <a-button type="primary" @click="addBeneficiary" size="small">添加受益人</a-button>
        </div>

        <div v-if="beneficiaries.length === 0" class="text-center p-6 rounded-lg">
            <p class="text-gray-500">暂无受益人信息，请点击"添加受益人"按钮添加</p>
        </div>

        <div v-for="(beneficiary, index) in beneficiaries" :key="index"
            class="mb-6 p-4 border border-gray-200 rounded-lg">
            <div class="flex justify-between items-center mb-4">
                <h4 class="text-base font-medium">受益人 #{{ index + 1 }}</h4>
                <a-button type="text" danger @click="removeBeneficiary(index)">
                    <template #icon>
                        <Icon icon="material-symbols:delete-outline" class="text-red-500 text-xl" />
                    </template>
                </a-button>
            </div>

            <a-form :ref="(el) => { if (el) formRefs[index] = el }" :model="beneficiary" layout="vertical"
                :rules="beneficiaryRules">
                <div class="grid grid-cols-2 gap-x-6">
                    <a-form-item label="受益人姓名" name="name">
                        <a-input v-model:value="beneficiary.name" placeholder="请输入受益人姓名" />
                    </a-form-item>

                    <a-form-item label="性别" name="gender">
                        <a-radio-group v-model:value="beneficiary.gender">
                            <a-radio :value="1">男</a-radio>
                            <a-radio :value="0">女</a-radio>
                        </a-radio-group>
                    </a-form-item>

                    <a-form-item label="与受保人关系" name="relationship">
                        <a-select v-model:value="beneficiary.relationship" placeholder="请选择与受保人关系">
                            <a-select-option value="配偶">配偶</a-select-option>
                            <a-select-option value="子女">子女</a-select-option>
                            <a-select-option value="父母">父母</a-select-option>
                            <a-select-option value="兄弟姐妹">兄弟姐妹</a-select-option>
                            <a-select-option value="其他亲属">其他亲属</a-select-option>
                            <a-select-option value="朋友">朋友</a-select-option>
                            <a-select-option value="其他">其他</a-select-option>
                        </a-select>
                    </a-form-item>

                    <a-form-item label="身份证号码" name="idCardNo">
                        <a-input v-model:value="beneficiary.idCardNo" placeholder="请输入身份证号码" />
                    </a-form-item>

                    <a-form-item label="受益比例(%)" name="benefitPercentage">
                        <a-input-number v-model:value="beneficiary.benefitPercentage" style="width: 100%"
                            placeholder="请输入受益比例(%)" :precision="2" :min="0" :max="100" />
                    </a-form-item>

                    <a-form-item label="是否为信托人" name="isTrustee">
                        <a-radio-group v-model:value="beneficiary.isTrustee">
                            <a-radio :value="1">是</a-radio>
                            <a-radio :value="0">否</a-radio>
                        </a-radio-group>
                    </a-form-item>
                </div>
            </a-form>
        </div>

        <div v-if="beneficiaries.length > 0" class="flex items-center mt-4">
            <div class="flex-1 h-px bg-gray-200"></div>
            <p class="mx-4 text-gray-500">受益比例总和: {{ totalPercentage }}%</p>
            <div class="flex-1 h-px bg-gray-200"></div>
        </div>
    </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';
import { ref, computed, defineProps, defineEmits, watch } from 'vue';
import { formRules } from '@/utils/formValidate';

// 防止递归更新的标志位
let isUpdating = false;

const props = defineProps({
    formData: {
        type: Array,
        default: () => []
    }
});

const emit = defineEmits(['update:form-data']);

// 表单引用
const formRefs = ref([]);

// 受益人列表
const beneficiaries = ref([]);

// 表单验证规则
const beneficiaryRules = {
    name: [
        { required: true, message: '请输入受益人姓名', trigger: 'blur' },
        { min: 2, max: 20, message: '姓名长度应在2-20个字符之间', trigger: 'blur' }
    ],
    relationship: [
        { required: true, message: '请选择与受保人关系', trigger: 'change' }
    ],
    idCardNo: formRules.idCard(false),
    benefitPercentage: [
        { required: true, message: '请输入受益比例', trigger: 'blur' },
        { type: 'number', min: 0, max: 100, message: '受益比例应在0-100之间', trigger: 'blur' }
    ]
};

// 计算受益比例总和
const totalPercentage = computed(() => {
    return beneficiaries.value.reduce((sum, item) => {
        return sum + (parseFloat(item.benefitPercentage) || 0);
    }, 0);
});

// 添加受益人
const addBeneficiary = () => {
    beneficiaries.value.push({
        name: '',
        gender: 1,
        relationship: '',
        idCardNo: '',
        benefitPercentage: beneficiaries.value.length === 0 ? 100 : 0,
        isTrustee: 0
    });
    updateFormData();
};

// 移除受益人
const removeBeneficiary = (index) => {
    beneficiaries.value.splice(index, 1);
    updateFormData();
};

// 向父组件发送更新
const updateFormData = () => {
    // 如果正在更新，则跳过以防止递归更新
    if (isUpdating) return;

    isUpdating = true;
    try {
        emit('update:form-data', [...beneficiaries.value]);
    } finally {
        // 确保标志位被重置，使用setTimeout确保在当前执行栈结束后重置
        setTimeout(() => {
            isUpdating = false;
        }, 0);
    }
};

// 监听表单数据变化
watch(beneficiaries, () => {
    // 仅在非更新状态下触发更新，避免循环
    if (!isUpdating) {
        updateFormData();
    }
}, { deep: true });

// 监听父组件传入的表单数据
watch(() => props.formData, (newVal) => {
    // 仅在非更新状态下处理父组件数据更新，避免循环
    if (!isUpdating && newVal && newVal.length > 0) {
        isUpdating = true;
        try {
            beneficiaries.value = [...newVal];
        } finally {
            // 确保标志位被重置
            setTimeout(() => {
                isUpdating = false;
            }, 0);
        }
    }
}, { deep: true, immediate: true });

// 表单验证方法（供父组件调用）
const validate = async () => {
    if (beneficiaries.value.length === 0) {
        return Promise.reject('请至少添加一位受益人');
    }

    // 验证受益比例总和是否为100%
    if (Math.abs(totalPercentage.value - 100) > 0.01) {
        return Promise.reject('受益人比例总和必须为100%');
    }

    // 验证每个受益人表单
    const validatePromises = formRefs.value.map(form => form.validate());

    try {
        await Promise.all(validatePromises);
        return Promise.resolve();
    } catch (error) {
        return Promise.reject('受益人信息填写有误，请检查');
    }
};

// 表单重置方法（供父组件调用）
const resetFields = () => {
    beneficiaries.value = [];
    formRefs.value = [];
    updateFormData();
};

// 向父组件暴露方法
defineExpose({
    validate,
    resetFields,
    beneficiaries
});
</script>

<style scoped>
.beneficiary-form {
    margin-bottom: 20px;
}
</style>