<template>
    <div class="insured-form">
        <a-form ref="formRef" :model="formState" :rules="rules" layout="vertical" @finish="onFinish">
            <div class="mb-4">
                <a-form-item label="与投保人关系" name="relationshipWithPolicyholder" :rules="relationshipRules">
                    <a-select v-model:value="relationshipWithPolicyholder" placeholder="请选择与投保人关系"
                        @change="handleRelationshipChange">
                        <a-select-option value="本人">本人</a-select-option>
                        <a-select-option value="配偶">配偶</a-select-option>
                        <a-select-option value="父亲">父亲</a-select-option>
                        <a-select-option value="母亲">母亲</a-select-option>
                        <a-select-option value="儿子">儿子</a-select-option>
                        <a-select-option value="女儿">女儿</a-select-option>
                        <a-select-option value="兄弟">兄弟</a-select-option>
                        <a-select-option value="姐妹">姐妹</a-select-option>
                        <a-select-option value="祖父">祖父</a-select-option>
                        <a-select-option value="祖母">祖母</a-select-option>
                        <a-select-option value="外祖父">外祖父</a-select-option>
                        <a-select-option value="外祖母">外祖母</a-select-option>
                        <a-select-option value="其他亲属">其他亲属</a-select-option>
                        <a-select-option value="雇主">雇主</a-select-option>
                        <a-select-option value="雇员">雇员</a-select-option>
                        <a-select-option value="其他">其他</a-select-option>
                    </a-select>
                </a-form-item>
            </div>

            <!-- 表格式布局，仅当关系不是"本人"时显示 -->
            <div v-if="relationshipWithPolicyholder != '本人'" class="grid grid-cols-2 gap-x-6">
                <!-- 第一行 -->
                <a-form-item label="中文姓名" name="nameCn" :rules="rules.nameCn">
                    <a-input v-model:value="formState.nameCn" placeholder="请输入被保人中文姓名" />
                </a-form-item>

                <a-form-item label="中文（护照）拼音" name="nameEn" :rules="rules.nameEn">
                    <a-input v-model:value="formState.nameEn" placeholder="请输入被保人拼音名" />
                </a-form-item>

                <!-- 第二行 -->
                <a-form-item label="身份证号码" name="idCardNo" :rules="rules.idCardNo">
                    <a-input v-model:value="formState.idCardNo" placeholder="请输入身份证号码" />
                </a-form-item>

                <a-form-item label="通行证号码" name="travelPermitNo" :rules="rules.travelPermitNo">
                    <a-input v-model:value="formState.travelPermitNo" placeholder="请输入通行证号码" />
                </a-form-item>

                <!-- 第三行 -->
                <a-form-item label="出生日期（年/月/日）" name="birthDate" :rules="rules.birthDate">
                    <a-date-picker v-model:value="formState.birthDate" style="width: 100%" format="YYYY-MM-DD"
                        placeholder="请选择出生日期" />
                </a-form-item>

                <a-form-item label="国籍" name="nationality" :rules="rules.nationality">
                    <a-input v-model:value="formState.nationality" placeholder="请输入国籍" />
                </a-form-item>

                <!-- 第四行 -->
                <a-form-item label="婚姻状况" name="maritalStatus" :rules="rules.maritalStatus">
                    <a-select v-model:value="formState.maritalStatus" placeholder="请选择婚姻状况">
                        <a-select-option value="未婚">未婚</a-select-option>
                        <a-select-option value="已婚">已婚</a-select-option>
                        <a-select-option value="离异">离异</a-select-option>
                        <a-select-option value="丧偶">丧偶</a-select-option>
                    </a-select>
                </a-form-item>

                <a-form-item label="教育程度" name="educationLevel" :rules="rules.educationLevel">
                    <a-select v-model:value="formState.educationLevel" placeholder="请选择教育程度">
                        <a-select-option value="小学">小学</a-select-option>
                        <a-select-option value="初中">初中</a-select-option>
                        <a-select-option value="高中">高中</a-select-option>
                        <a-select-option value="大专">大专</a-select-option>
                        <a-select-option value="本科">本科</a-select-option>
                        <a-select-option value="硕士">硕士</a-select-option>
                        <a-select-option value="博士">博士</a-select-option>
                    </a-select>
                </a-form-item>

                <!-- 第五行 -->
                <a-form-item label="身高" name="height" :rules="rules.height">
                    <a-input-number v-model:value="formState.height" style="width: 100%" placeholder="请输入身高" :min="0"
                        :max="300" addonAfter="厘米" :step="10" :precision="2" />
                </a-form-item>

                <a-form-item label="手机号码" name="mobile" :rules="rules.mobile">
                    <div class="grid grid-cols-3 gap-x-2">
                        <a-select v-model:value="formState.mobileAreaCode" placeholder="区号" style="width: 100%"
                            @change="handleAreaCodeChange">
                            <a-select-option v-for="option in areaCodeOptions" :key="option.value"
                                :value="option.value">
                                {{ option.label }}
                            </a-select-option>
                        </a-select>
                        <div class="col-span-2">
                            <a-input v-model:value="formState.mobile" placeholder="请输入手机号码" />
                        </div>
                    </div>
                </a-form-item>

                <!-- 第六行 -->
                <a-form-item label="体重" name="weight" :rules="rules.weight">
                    <a-input-number v-model:value="formState.weight" style="width: 100%" placeholder="请输入体重" :min="0"
                        addonAfter="千克" :step="5" :precision="2" />
                </a-form-item>

                <a-form-item label="住宅电话" name="homePhone" :rules="rules.homePhone">
                    <div class="grid grid-cols-3 gap-x-2">
                        <a-select v-model:value="formState.homePhoneAreaCode" placeholder="区号" style="width: 100%"
                            @change="handleAreaCodeChange">
                            <a-select-option v-for="option in areaCodeOptions" :key="option.value"
                                :value="option.value">
                                {{ option.label }}
                            </a-select-option>
                        </a-select>
                        <div class="col-span-2">
                            <a-input v-model:value="formState.homePhone" placeholder="请输入住宅电话" />
                        </div>
                    </div>
                </a-form-item>

                <!-- 第七行 -->
                <a-form-item label="性别" name="gender" :rules="rules.gender">
                    <a-radio-group v-model:value="formState.gender">
                        <a-radio :value="1">男</a-radio>
                        <a-radio :value="0">女</a-radio>
                    </a-radio-group>
                </a-form-item>

                <a-form-item label="是否吸烟" name="isSmoker" :rules="rules.isSmoker">
                    <a-radio-group v-model:value="formState.isSmoker">
                        <a-radio :value="1">是</a-radio>
                        <a-radio :value="0">否</a-radio>
                    </a-radio-group>
                </a-form-item>

                <!-- 第八行 -->
                <a-form-item label="邮箱" name="email" :rules="rules.email" class="col-span-2">
                    <a-input v-model:value="formState.email" placeholder="请输入邮箱" />
                </a-form-item>
            </div>

            <!-- 身份证地址，仅当关系不是"本人"时显示 -->
            <a-form-item v-if="relationshipWithPolicyholder !== '本人'" label="身份证地址/邮编" name="idCardAddress"
                :rules="rules.idCardAddress">
                <a-textarea v-model:value="formState.idCardAddress" placeholder="请输入身份证地址和邮编" :rows="2" />
            </a-form-item>
        </a-form>
    </div>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits, defineExpose, onMounted } from 'vue';
import dayjs from 'dayjs';
import { formRules } from '@/utils/formValidate';
import { useFormBinding, useFormValidation } from '@/utils/formHooks';

// 区号列表常量
const areaCodeOptions = [
    { label: '中国大陆 +86', value: '+86', pattern: /^1[3-9]\d{9}$/ },
    { label: '香港 +852', value: '+852', pattern: /^[5-9]\d{7}$/ },
    { label: '澳门 +853', value: '+853', pattern: /^[6]\d{7}$/ },
    { label: '台湾 +886', value: '+886', pattern: /^[0-9]\d{8}$/ },
    { label: '新加坡 +65', value: '+65', pattern: /^[89]\d{7}$/ },
    { label: '美国/加拿大 +1', value: '+1', pattern: /^[2-9]\d{9}$/ },
    { label: '日本 +81', value: '+81', pattern: /^[0-9]\d{9}$/ },
    { label: '韩国 +82', value: '+82', pattern: /^[0-9]\d{8,9}$/ },
    { label: '英国 +44', value: '+44', pattern: /^[0-9]\d{9}$/ },
    { label: '澳大利亚 +61', value: '+61', pattern: /^[0-9]\d{8}$/ },
];

// 防止递归更新的标志位
let isEmitting = false;

const props = defineProps({
    formData: {
        type: Object,
        default: () => ({})
    },
    policyholderData: {
        type: Object,
        default: () => ({})
    },
    overviewData: {
        type: Object,
        default: () => ({})
    }
});

const emit = defineEmits(['update:form-data']);

// 表单引用
const formRef = ref(null);

// 与投保人关系
const relationshipWithPolicyholder = ref('本人');

// 关系选择验证规则
const relationshipRules = [
    { required: true, message: '请选择与投保人关系', trigger: 'change' }
];

// 初始表单状态
const initialState = {
    nameCn: '',
    nameEn: '',
    birthDate: null,
    gender: 1,
    idCardNo: '',
    travelPermitNo: '',
    mobile: '',
    mobileAreaCode: '+86',
    homePhone: '',
    homePhoneAreaCode: '+86',
    email: '',
    nationality: '',
    maritalStatus: '',
    educationLevel: '',
    height: null,
    weight: null,
    idCardAddress: '',
    isSmoker: 0
};

// 使用表单绑定钩子，但不直接使用返回的emitFormData，因为需要自定义版本
const { formRef: baseFormRef, formState, onFinish, resetFields: baseResetFields } = useFormBinding(
    props,
    emit,
    initialState,
    ['birthDate']
);

// 确保formRef指向baseFormRef
watch(baseFormRef, (val) => {
    formRef.value = val;
});

// 向父组件发送表单数据的函数（自定义版本，包含relationshipWithPolicyholder）
const emitFormData = () => {
    // 如果正在发送数据，则跳过以防止递归更新
    if (isEmitting) return;

    isEmitting = true;
    try {
        const formData = { ...formState };

        // 如果birthDate是Dayjs对象，转换为时间戳
        if (formData.birthDate && typeof formData.birthDate.valueOf === 'function') {
            formData.birthDate = formData.birthDate.valueOf();
        }

        emit('update:form-data', {
            ...formData,
            relationshipWithPolicyholder: relationshipWithPolicyholder.value
        });
    } finally {
        // 确保标志位被重置，使用setTimeout确保在当前执行栈结束后重置
        setTimeout(() => {
            isEmitting = false;
        }, 0);
    }
};

// 根据区号验证手机号
const validatePhoneWithAreaCode = (phone, areaCode) => {
    if (!phone || !areaCode) return false;

    // 查找对应区号的验证规则
    const areaCodeOption = areaCodeOptions.find(option => option.value === areaCode);
    if (!areaCodeOption) return false;

    // 使用对应的正则表达式验证手机号
    return areaCodeOption.pattern.test(phone);
};

// 处理区号变更
const handleAreaCodeChange = (value) => {
    // 当区号变更时，可以进行一些额外操作，如清空或重新验证手机号
    // 这里暂时不做特殊处理
};

// 处理"与投保人关系"变化
const handleRelationshipChange = (value) => {
    // 标记开始更新，防止watch触发emitFormData
    isEmitting = true;

    try {
        if (value === '本人') {
            setTimeout(() => {
                relationshipWithPolicyholder.value = value;
                // 如果关系是"本人"，复制投保人信息到被保人
                formState.nameCn = props.policyholderData.nameCn || '';
                formState.nameEn = props.policyholderData.nameEn || '';

                // 如果birthDate是时间戳，转换为Dayjs对象
                if (props.policyholderData.birthDate && typeof props.policyholderData.birthDate === 'number') {
                    formState.birthDate = dayjs(props.policyholderData.birthDate);
                } else {
                    formState.birthDate = props.policyholderData.birthDate || null;
                }

                formState.gender = props.policyholderData.gender || 1;
                formState.idCardNo = props.policyholderData.idCardNo || '';
                formState.travelPermitNo = props.policyholderData.travelPermitNo || '';
                formState.mobile = props.policyholderData.mobile || '';
                formState.mobileAreaCode = props.policyholderData.mobileAreaCode || '+86';
                formState.homePhone = props.policyholderData.homePhone || '';
                formState.homePhoneAreaCode = props.policyholderData.homePhoneAreaCode || '+86';
                formState.email = props.policyholderData.email || '';
                formState.nationality = props.policyholderData.nationality || '';
                formState.maritalStatus = props.policyholderData.maritalStatus || '';
                formState.educationLevel = props.policyholderData.educationLevel || '';
                formState.height = props.policyholderData.height || null;
                formState.weight = props.policyholderData.weight || null;
                formState.idCardAddress = props.policyholderData.idCardAddress || '';
                formState.isSmoker = props.policyholderData.isSmoker || 0;
            }, 0);

        }
    } finally {
        // 完成所有状态更新后，重置标志位并手动触发一次数据更新
        setTimeout(() => {
            isEmitting = false;
            // 手动触发一次数据更新
            emitFormData();
        }, 0);
    }
};

// 表单验证规则
const rules = {
    nameCn: formRules.chineseName,
    nameEn: formRules.englishName,
    idCardNo: formRules.idCard(true),
    travelPermitNo: formRules.passport,
    birthDate: formRules.birthDate,
    nationality: [
        { required: true, message: '请输入国籍', trigger: 'blur' }
    ],
    maritalStatus: [
        { required: true, message: '请选择婚姻状况', trigger: 'change' }
    ],
    educationLevel: [
        { required: true, message: '请选择教育程度', trigger: 'change' }
    ],
    height: [
        { required: true, message: '请输入身高', trigger: 'blur' }
    ],
    mobile: [
        { required: true, message: '请输入手机号码', trigger: 'blur' },
        {
            validator: (rule, value) => {
                if (!value || validatePhoneWithAreaCode(value, formState.mobileAreaCode)) {
                    return Promise.resolve();
                }
                return Promise.reject(`请输入有效的${formState.mobileAreaCode}手机号码`);
            },
            trigger: 'blur'
        }
    ],
    mobileAreaCode: [
        { required: true, message: '请选择区号', trigger: 'change' }
    ],
    weight: [
        { required: true, message: '请输入体重', trigger: 'blur' }
    ],
    homePhone: [
        { required: true, message: '请输入住宅电话', trigger: 'blur' },
        {
            validator: (rule, value) => {
                if (!value || validatePhoneWithAreaCode(value, formState.homePhoneAreaCode)) {
                    return Promise.resolve();
                }
                return Promise.reject(`请输入有效的${formState.homePhoneAreaCode}电话号码`);
            },
            trigger: 'blur'
        }
    ],
    homePhoneAreaCode: [
        { required: true, message: '请选择区号', trigger: 'change' }
    ],
    gender: [
        { required: true, message: '请选择性别', trigger: 'change' }
    ],
    isSmoker: [
        { required: true, message: '请选择是否吸烟', trigger: 'change' }
    ],
    email: formRules.email(true),
    idCardAddress: formRules.address(true)
};

// 监听表单数据和关系变化
watch([formState, relationshipWithPolicyholder], () => {
    // 仅在非发送状态下触发更新，避免循环
    if (!isEmitting) {
        emitFormData();
    }
}, { deep: true });

// 监听父组件传入的表单数据
watch(() => props.formData, (newVal) => {
    if (newVal && Object.keys(newVal).length > 0) {
        // 处理可能的时间戳转换为日期对象
        const formData = { ...newVal };

        // 如果birthDate是时间戳，转换为Dayjs对象供日期选择器使用
        if (formData.birthDate && typeof formData.birthDate === 'number') {
            formData.birthDate = dayjs(formData.birthDate);
        }

        // 更新表单状态
        Object.assign(formState, formData);

        // 如果表单数据中有relationshipWithPolicyholder字段，更新关系选择
        if (newVal.relationshipWithPolicyholder) {
            relationshipWithPolicyholder.value = newVal.relationshipWithPolicyholder;
        }
    }
}, { deep: true, immediate: true });

// 组件挂载时，确保初始数据传递给父组件
onMounted(() => {
    // 如果关系是"本人"，并且有投保人数据，复制投保人信息
    if (relationshipWithPolicyholder.value === '本人' && Object.keys(props.policyholderData).length > 0) {
        handleRelationshipChange('本人');
    } else {
        // 即使没有投保人数据，也要确保关系字段传递给父组件
        emitFormData();
    }
});

// 必填字段列表（仅当关系不是"本人"时需要验证）
const requiredFields = ['nameCn', 'birthDate', 'gender', 'mobileAreaCode', 'homePhoneAreaCode'];

// 表单验证方法（供父组件调用）
const validate = () => {
    return new Promise((resolve, reject) => {
        formRef.value
            .validate()
            .then(() => {
                // 如果关系是"本人"，则无需额外验证
                if (relationshipWithPolicyholder.value === '本人') {
                    resolve();
                    return;
                }

                // 检查必填字段
                const missingFields = requiredFields.filter(field => {
                    const value = formState[field];
                    return value === undefined || value === null || value === '';
                });

                if (missingFields.length > 0) {
                    reject('请完善被保人信息');
                } else {
                    resolve();
                }
            })
            .catch(error => {
                reject(error);
            });
    });
};

// 表单重置方法（供父组件调用）
const resetFields = () => {
    baseResetFields();
    relationshipWithPolicyholder.value = '本人';
    // 重置后自动填充投保人信息
    handleRelationshipChange('本人');
};

// 向父组件暴露方法
defineExpose({
    validate,
    resetFields,
    formState,
    relationshipWithPolicyholder
});
</script>

<style scoped>
.insured-form {
    margin-bottom: 20px;
}
</style>