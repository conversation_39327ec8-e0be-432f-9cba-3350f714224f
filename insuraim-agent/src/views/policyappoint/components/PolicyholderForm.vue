<template>
    <div class="policyholder-form">
        <a-form ref="formRef" :model="formState" :rules="rules" layout="vertical" @finish="onFinish">
            <div class="grid grid-cols-2 gap-x-6">
                <a-form-item label="中文姓名" name="nameCn" :rules="rules.nameCn">
                    <a-input v-model:value="formState.nameCn" placeholder="请输入投保人中文姓名" />
                </a-form-item>

                <a-form-item label="中文（护照）拼音" name="nameEn" :rules="rules.nameEn">
                    <a-input v-model:value="formState.nameEn" placeholder="请输入投保人拼音名" />
                </a-form-item>

                <a-form-item label="身份证号码" name="idCardNo" :rules="rules.idCardNo">
                    <a-input v-model:value="formState.idCardNo" placeholder="请输入身份证号码" />
                </a-form-item>

                <a-form-item label="通行证号码" name="travelPermitNo" :rules="rules.travelPermitNo">
                    <a-input v-model:value="formState.travelPermitNo" placeholder="请输入通行证号码" />
                </a-form-item>
            </div>

            <div class="grid grid-cols-3 gap-x-6">
                <a-form-item label="出生日期（年/月/日）" name="birthDate" :rules="rules.birthDate">
                    <a-date-picker v-model:value="formState.birthDate" style="width: 100%" format="YYYY-MM-DD"
                        placeholder="请选择出生日期" />
                </a-form-item>

                <a-form-item label="国籍" name="nationality" :rules="rules.nationality">
                    <a-input v-model:value="formState.nationality" placeholder="请输入国籍" />
                </a-form-item>

                <a-form-item label="性别" name="gender" :rules="rules.gender">
                    <a-radio-group v-model:value="formState.gender">
                        <a-radio :value="1">男</a-radio>
                        <a-radio :value="0">女</a-radio>
                    </a-radio-group>
                </a-form-item>
            </div>

            <div class="grid grid-cols-3 gap-x-6">
                <a-form-item label="婚姻状况" name="maritalStatus" :rules="rules.maritalStatus">
                    <a-select v-model:value="formState.maritalStatus" placeholder="请选择婚姻状况">
                        <a-select-option value="未婚">未婚</a-select-option>
                        <a-select-option value="已婚">已婚</a-select-option>
                        <a-select-option value="离异">离异</a-select-option>
                        <a-select-option value="丧偶">丧偶</a-select-option>
                    </a-select>
                </a-form-item>

                <a-form-item label="教育程度" name="educationLevel" :rules="rules.educationLevel">
                    <a-select v-model:value="formState.educationLevel" placeholder="请选择教育程度">
                        <a-select-option value="小学">小学</a-select-option>
                        <a-select-option value="初中">初中</a-select-option>
                        <a-select-option value="高中">高中</a-select-option>
                        <a-select-option value="大专">大专</a-select-option>
                        <a-select-option value="本科">本科</a-select-option>
                        <a-select-option value="硕士">硕士</a-select-option>
                        <a-select-option value="博士">博士</a-select-option>
                    </a-select>
                </a-form-item>

                <a-form-item label="是否吸烟" name="isSmoker" :rules="rules.isSmoker">
                    <a-radio-group v-model:value="formState.isSmoker">
                        <a-radio :value="1">是</a-radio>
                        <a-radio :value="0">否</a-radio>
                    </a-radio-group>
                </a-form-item>
            </div>

            <div class="grid grid-cols-3 gap-x-6">
                <a-form-item label="身高" name="height" :rules="rules.height">
                    <a-input-number v-model:value="formState.height" style="width: 100%" placeholder="请输入身高"
                        :precision="2" :min="0" :max="300" addonAfter="厘米" :step="10" />
                </a-form-item>

                <a-form-item label="手机号码" name="mobile" :rules="rules.mobile">
                    <div class="grid grid-cols-4 gap-x-3">
                        <a-select v-model:value="formState.mobileAreaCode" placeholder="区号" style="width: 100%"
                            @change="handleAreaCodeChange" class="col-span-2">
                            <a-select-option v-for="option in areaCodeOptions" :key="option.value"
                                :value="option.value">
                                {{ option.label }}
                            </a-select-option>
                        </a-select>
                        <div class="col-span-2">
                            <a-input v-model:value="formState.mobile" placeholder="请输入手机号码" />
                        </div>
                    </div>
                </a-form-item>

                <a-form-item label="出生地" name="birthPlace" :rules="rules.birthPlace">
                    <a-input v-model:value="formState.birthPlace" placeholder="请输入出生地" />
                </a-form-item>
            </div>

            <div class="grid grid-cols-3 gap-x-6">
                <a-form-item label="体重" name="weight" :rules="rules.weight">
                    <a-input-number v-model:value="formState.weight" style="width: 100%" placeholder="请输入体重"
                        :precision="2" :min="0" addonAfter="千克" :step="5" />
                </a-form-item>

                <a-form-item label="住宅电话" name="homePhone" :rules="rules.homePhone">
                    <div class="grid grid-cols-4 gap-x-3">
                        <a-select v-model:value="formState.homePhoneAreaCode" placeholder="区号" style="width: 100%"
                            @change="handleAreaCodeChange" class="col-span-2">
                            <a-select-option v-for="option in areaCodeOptions" :key="option.value"
                                :value="option.value">
                                {{ option.label }}
                            </a-select-option>
                        </a-select>
                        <div class="col-span-2">
                            <a-input v-model:value="formState.homePhone" placeholder="请输入住宅电话" />
                        </div>
                    </div>
                </a-form-item>

                <a-form-item label="邮箱" name="email" :rules="rules.email">
                    <a-input v-model:value="formState.email" placeholder="请输入邮箱" />
                </a-form-item>
            </div>

            <a-form-item label="身份证地址/邮编" name="idCardAddress" :rules="rules.idCardAddress">
                <a-textarea v-model:value="formState.idCardAddress" placeholder="请输入身份证地址及邮编" :rows="2" />
            </a-form-item>

            <a-form-item label="居住地址（如与身份证地址不同）" name="residentialAddress" :rules="rules.residentialAddress">
                <a-textarea v-model:value="formState.residentialAddress" placeholder="请输入居住地址（如与身份证地址不同）" :rows="2" />
            </a-form-item>

            <a-form-item label="通讯地址（如与居住地址不同）" name="mailingAddress" :rules="rules.mailingAddress">
                <a-textarea v-model:value="formState.mailingAddress" placeholder="请输入通讯地址（如与居住地址不同）" :rows="2" />
            </a-form-item>

            <a-divider orientation="left">工作信息</a-divider>

            <div class="grid grid-cols-1 gap-x-6">
                <a-form-item label="公司中文名称" name="companyNameCn" :rules="rules.companyNameCn">
                    <a-input v-model:value="formState.companyNameCn" placeholder="请输入公司中文名称" />
                </a-form-item>

                <a-form-item label="公司英文名称" name="companyNameEn" :rules="rules.companyNameEn">
                    <a-input v-model:value="formState.companyNameEn" placeholder="请输入公司英文名称" />
                </a-form-item>

                <a-form-item label="公司地址" name="companyAddress" :rules="rules.companyAddress">
                    <a-textarea v-model:value="formState.companyAddress" placeholder="请输入公司地址" :rows="2" />
                </a-form-item>
            </div>

            <div class="grid grid-cols-3 gap-x-6">
                <a-form-item label="公司行业（细化行业，如服务业等）" name="companyIndustry" :rules="rules.companyIndustry">
                    <a-input v-model:value="formState.companyIndustry" placeholder="请输入公司行业" />
                </a-form-item>

                <a-form-item label="职位" name="position" :rules="rules.position">
                    <a-input v-model:value="formState.position" placeholder="请输入职位" />
                </a-form-item>


                <a-form-item label="年薪" name="annualIncome" :rules="rules.annualIncome">
                    <a-input-number v-model:value="formState.annualIncome" style="width: 100%" placeholder="请输入年薪"
                        :precision="2" :min="0" :parser="value => parseAmount(value)"
                        :formatter="value => formatAmount(value)" :step="10000" />
                </a-form-item>

            </div>
        </a-form>
    </div>
</template>

<script setup>
import { defineProps, defineEmits, defineExpose } from 'vue';
import { formRules } from '@/utils/formValidate';
import { useFormBinding, useFormValidation } from '@/utils/formHooks';

// 区号列表常量
const areaCodeOptions = [
    { label: '中国大陆 +86', value: '+86', pattern: /^1[3-9]\d{9}$/ },
    { label: '香港 +852', value: '+852', pattern: /^[5-9]\d{7}$/ },
    { label: '澳门 +853', value: '+853', pattern: /^[6]\d{7}$/ },
    { label: '台湾 +886', value: '+886', pattern: /^[0-9]\d{8}$/ },
    { label: '新加坡 +65', value: '+65', pattern: /^[89]\d{7}$/ },
    { label: '美国/加拿大 +1', value: '+1', pattern: /^[2-9]\d{9}$/ },
    { label: '日本 +81', value: '+81', pattern: /^[0-9]\d{9}$/ },
    { label: '韩国 +82', value: '+82', pattern: /^[0-9]\d{8,9}$/ },
    { label: '英国 +44', value: '+44', pattern: /^[0-9]\d{9}$/ },
    { label: '澳大利亚 +61', value: '+61', pattern: /^[0-9]\d{8}$/ },
];

const props = defineProps({
    formData: {
        type: Object,
        default: () => ({})
    }
});

const emit = defineEmits(['update:form-data']);

// 初始表单状态
const initialState = {
    nameCn: '',
    nameEn: '',
    birthDate: null,
    gender: 1,
    idCardNo: '',
    travelPermitNo: '',
    nationality: '',
    birthPlace: '',
    maritalStatus: '',
    educationLevel: '',
    height: null,
    weight: null,
    mobile: '',
    mobileAreaCode: '+86',
    homePhone: '',
    homePhoneAreaCode: '+86',
    email: '',
    idCardAddress: '',
    residentialAddress: '',
    mailingAddress: '',
    isSmoker: 0,
    companyNameCn: '',
    companyNameEn: '',
    companyAddress: '',
    companyIndustry: '',
    position: '',
    annualIncome: null
};

// 使用表单绑定钩子
const { formRef, formState, onFinish, resetFields } = useFormBinding(
    props,
    emit,
    initialState,
    ['birthDate']
);

// 格式化金额
const formatAmount = (value) => {
    if (!value) return '';

    // 添加千位分隔符
    const parts = value.toString().split('.');
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');

    return `${parts.join('.')}`;
};

// 解析金额
const parseAmount = (value) => {
    if (!value) return '';

    // 处理"w"或"W"表示的万元
    if (value.toString().toLowerCase().includes('w')) {
        // 提取数字部分
        const numPart = value.toString().toLowerCase().replace(/[^0-9.]/g, '');
        if (numPart) {
            // 将数字乘以10000（万）
            return parseFloat(numPart) * 10000;
        }
    }

    // 移除货币符号和千位分隔符
    return value.replace(/[^\d.]/g, '');
};
// 根据区号验证手机号
const validatePhoneWithAreaCode = (phone, areaCode) => {
    if (!phone || !areaCode) return false;

    // 查找对应区号的验证规则
    const areaCodeOption = areaCodeOptions.find(option => option.value === areaCode);
    if (!areaCodeOption) return false;

    // 使用对应的正则表达式验证手机号
    return areaCodeOption.pattern.test(phone);
};

// 处理区号变更
const handleAreaCodeChange = (value) => {
    // 当区号变更时，可以进行一些额外操作，如清空或重新验证手机号
    // 这里暂时不做特殊处理
};

// 表单验证规则
const rules = {
    nameCn: formRules.chineseName,
    nameEn: formRules.englishName,
    birthDate: formRules.birthDate,
    gender: formRules.gender,
    idCardNo: formRules.idCard(true),
    travelPermitNo: formRules.passport,
    nationality: [
        { required: true, message: '请输入国籍', trigger: 'blur' }
    ],
    birthPlace: [
        { required: false, message: '请输入出生地', trigger: 'blur' }
    ],
    maritalStatus: [
        { required: true, message: '请选择婚姻状况', trigger: 'change' }
    ],
    educationLevel: [
        { required: true, message: '请选择教育程度', trigger: 'change' }
    ],
    height: [
        { required: true, message: '请输入身高', trigger: 'blur' }
    ],
    weight: [
        { required: true, message: '请输入体重', trigger: 'blur' }
    ],
    mobile: [
        { required: true, message: '请输入手机号码', trigger: 'blur' },
        {
            validator: (rule, value) => {
                if (!value || validatePhoneWithAreaCode(value, formState.mobileAreaCode)) {
                    return Promise.resolve();
                }
                return Promise.reject(`请输入有效的${formState.mobileAreaCode}手机号码`);
            },
            trigger: 'blur'
        }
    ],
    mobileAreaCode: [
        { required: true, message: '请选择区号', trigger: 'change' }
    ],
    homePhone: [
        { required: true, message: '请输入住宅电话', trigger: 'blur' },
        {
            validator: (rule, value) => {
                if (!value || validatePhoneWithAreaCode(value, formState.homePhoneAreaCode)) {
                    return Promise.resolve();
                }
                return Promise.reject(`请输入有效的${formState.homePhoneAreaCode}电话号码`);
            },
            trigger: 'blur'
        }
    ],
    homePhoneAreaCode: [
        { required: true, message: '请选择区号', trigger: 'change' }
    ],
    email: formRules.email(true),
    idCardAddress: formRules.address(true),
    residentialAddress: formRules.address(true),
    mailingAddress: formRules.address(true),
    isSmoker: [
        { required: true, message: '请选择是否吸烟', trigger: 'change' }
    ],
    companyNameCn: formRules.chineseName,
    companyNameEn: [
        { required: true, message: '请输入公司英文名称', trigger: 'blur' }
    ],
    companyAddress: formRules.address(true),
    companyIndustry: formRules.address(true),
    position: [
        { required: true, message: '请输入职位', trigger: 'blur' }
    ],
    annualIncome: [
        { required: true, message: '请输入年薪', trigger: 'blur' },
        { type: 'number', min: 0, message: '年薪不能为负数', trigger: 'blur' }
    ]
};

// 必填字段列表
const requiredFields = [
    'nameCn', 'birthDate', 'gender', 'idCardNo', 'nationality', 'maritalStatus', 'educationLevel', 'mobile', 'mobileAreaCode',
    'email', 'idCardAddress', 'residentialAddress', 'mailingAddress', 'isSmoker', 'companyNameCn', 'companyNameEn',
    'companyAddress', 'companyIndustry', 'position', 'annualIncome', 'height', 'weight', 'homePhone', 'homePhoneAreaCode',
    'birthPlace', 'nameEn'
];

// 使用表单验证钩子
const validate = useFormValidation(formRef, formState, requiredFields, '请完善投保人信息');

// 向父组件暴露方法
defineExpose({
    validate,
    resetFields,
    formState
});
</script>

<style scoped>
.policyholder-form {
    margin-bottom: 20px;
}
</style>