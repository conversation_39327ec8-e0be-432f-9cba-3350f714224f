<template>
    <div class="proposal-form">
        <div class="mb-6">
            <h3 class="text-lg font-medium mb-4">计划书选择</h3>
            <p class="text-gray-500 mb-4">请选择产品计划书</p>
        </div>

        <!-- 计划书卡片区域 -->
        <div class="grid grid-cols-1 gap-6">
            <!-- 已有计划书选择 -->
            <a-card title="选择计划书" class="proposal-card">
                <template #extra>
                    <a-button type="primary" size="small" @click="refreshProposalList">
                        <template #icon>
                            <Icon icon="material-symbols:refresh" />
                        </template>
                        刷新
                    </a-button>
                </template>
                <div v-if="loading" class="text-center p-8">
                    <a-spin />
                </div>
                <div v-else-if="availableProposals.length === 0" class="text-center p-4 rounded-lg">
                    <p class="text-gray-500">暂无可选择的计划书</p>
                </div>
                <a-list v-else size="small" class="proposal-list">
                    <a-list-item v-for="(proposal, index) in availableProposals" :key="proposal.id"
                        :class="{ 'selected-proposal': selectedProposalId === proposal.id }">
                        <a-list-item-meta>
                            <template #avatar>
                                <div class="icon-container">
                                    <Icon icon="mdi:file-pdf-box" class="text-red-500 text-2xl" />
                                </div>
                            </template>
                            <template #title>
                                <div class="flex items-center">
                                    <span>{{ proposal.productName || '未命名计划书' }}</span>
                                </div>
                            </template>
                            <template #description>
                                <span>{{ proposal.proposalNo || '无编号' }} | {{ formatDate(proposal.createAt) }} | {{ proposal.customerName || '无客户' }}</span>
                            </template>
                        </a-list-item-meta>
                        <template #actions>
                            <a-button type="primary" size="small" @click="selectProposal(proposal)">
                                {{ selectedProposalId === proposal.id ? '已选择' : '选择' }}
                            </a-button>
                        </template>
                    </a-list-item>

                    <!-- 分页器 -->
                    <div class="mt-4 flex justify-center">
                        <a-pagination v-model:current="pagination.current" :total="pagination.total"
                            :pageSize="pagination.pageSize" :showSizeChanger="false" @change="handlePageChange" />
                    </div>
                </a-list>
            </a-card>
        </div>
    </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';
import { ref, defineProps, defineEmits, watch, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { planAPI } from '@/api';
import dayjs from 'dayjs';

const props = defineProps({
    formData: {
        type: Object,
        default: () => ({
            selectedProposalId: null
        })
    }
});

const emit = defineEmits(['update:form-data']);

// 表单数据
const selectedProposalId = ref(null);
const formDataObj = reactive({
    selectedProposalId: null
});

// 加载状态
const loading = ref(false);

// 可用计划书列表
const availableProposals = ref([]);

// 分页配置
const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0
});

// 页码变化处理
const handlePageChange = (page) => {
    pagination.current = page;
    fetchProposalList();
};

// 获取计划书列表
const fetchProposalList = async () => {
    loading.value = true;
    try {
        // 构建查询参数
        const params = {
            pageNum: pagination.current,
            pageSize: pagination.pageSize,
            status: 'completed' // 只获取已完成的计划书
        };

        const response = await planAPI.getPlanList(params);

        if (response && response.data) {
            // 后端返回的是包装在data中的分页对象
            availableProposals.value = response.data.records || [];
            pagination.total = response.data.total || 0;
        } else if (response) {
            // 直接返回分页对象的情况
            availableProposals.value = response.records || [];
            pagination.total = response.total || 0;
        } else {
            availableProposals.value = [];
            pagination.total = 0;
        }
    } catch (error) {
        console.error('获取计划书列表失败:', error);
        message.error('获取计划书列表失败');
        availableProposals.value = [];
        pagination.total = 0;
    } finally {
        loading.value = false;
    }
};

// 刷新计划书列表
const refreshProposalList = () => {
    pagination.current = 1; // 重置为第一页
    fetchProposalList();
};

// 格式化日期
const formatDate = (dateString) => {
    if (!dateString) return '';
    return dayjs(dateString).format('YYYY-MM-DD');
};

// 选择已有计划书
const selectProposal = (proposal) => {
    if (selectedProposalId.value === proposal.id) {
        selectedProposalId.value = null;

    } else {
        selectedProposalId.value = proposal.id;
    }
    updateFormData();
};

// 向父组件发送更新
const updateFormData = () => {
    formDataObj.selectedProposalId = selectedProposalId.value;
    emit('update:form-data', { ...formDataObj });
};

// 监听父组件传入的表单数据
watch(() => props.formData, (newVal) => {
    if (newVal) {
        if (newVal.selectedProposalId !== undefined) {
            selectedProposalId.value = newVal.selectedProposalId;
        }
    }
}, { deep: true, immediate: true });

// 表单验证方法（供父组件调用）
const validate = async () => {
    return Promise.resolve();
};

// 表单重置方法（供父组件调用）
const resetFields = () => {
    selectedProposalId.value = null;
    updateFormData();
};

// 初始化
onMounted(() => {
    fetchProposalList();
});

// 向父组件暴露方法
defineExpose({
    validate,
    resetFields,
    refreshProposalList
});
</script>

<style scoped>
.proposal-form {
    margin-bottom: 20px;
}

.proposal-card {
    margin-bottom: 16px;
}

.proposal-list {
    margin-top: 8px;
}

.icon-container {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
}

.selected-proposal {
    background-color: rgba(0, 114, 255, 0.05);
    border-radius: 4px;
}
</style>