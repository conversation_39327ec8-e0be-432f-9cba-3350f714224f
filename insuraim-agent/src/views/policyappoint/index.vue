<template>
    <div class="container mx-auto py-6 px-4">
        <!-- 页面标题 -->
        <div
            class="title-section mb-6 p-6 rounded-lg shadow-md text-white bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700">
            <div class="flex items-center">
                <Icon icon="mdi:calendar-clock" class="text-4xl mr-3" />
                <h1 class="text-2xl font-bold page-title">{{ $t('policyappoint.appointmentTitle') }}</h1>
            </div>
            <p class="mt-2 page-description">
                {{ $t('policyappoint.browseAndManage') }}
            </p>
        </div>

        <!-- 客户预约表单 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="px-6">


                <!-- 表单部分 -->
                <div class="appointment-form">
                    <a-tabs :activeKey="activeTabKey" @change="handleTabChange">
                        <a-tab-pane key="overview" :tab="$t('policyappoint.tabs.overview')">
                            <appointment-overview-form ref="overviewFormRef" :form-data="formData.overview"
                                @update:form-data="updateFormData('overview', $event)" />
                        </a-tab-pane>

                        <a-tab-pane key="proposal" :tab="$t('policyappoint.tabs.proposal')">
                            <proposal-form ref="proposalFormRef" :form-data="formData.proposal"
                                @update:form-data="updateFormData('proposal', $event)" />
                        </a-tab-pane>
                        <a-tab-pane key="policyholder" :tab="$t('policyappoint.tabs.policyholder')">
                            <policyholder-form ref="policyholderFormRef" :form-data="formData.policyholder"
                                @update:form-data="updateFormData('policyholder', $event)" />
                        </a-tab-pane>

                        <a-tab-pane key="insured" :tab="$t('policyappoint.tabs.insured')">
                            <insured-form ref="insuredFormRef" :form-data="formData.insured"
                                :policyholder-data="formData.policyholder" :overview-data="formData.overview"
                                @update:form-data="updateFormData('insured', $event)" />
                        </a-tab-pane>

                        <a-tab-pane key="beneficiary" :tab="$t('policyappoint.tabs.beneficiary')">
                            <beneficiary-form ref="beneficiaryFormRef" :form-data="formData.beneficiary"
                                @update:form-data="updateFormData('beneficiary', $event)" />
                        </a-tab-pane>
                    </a-tabs>

                    <!-- 表单操作按钮 -->
                    <div class="mt-6 flex justify-end">
                        <div class="flex space-x-4">
                            <a-button @click="resetForm">{{ $t('common.reset') }}</a-button>
                            <div>
                                <a-button @click="prevStep" :disabled="activeTabKey === 'overview'">
                                    {{ $t('common.prev') }}</a-button>
                            </div>
                            <a-button v-if="activeTabKey !== 'beneficiary'" type="primary" @click="nextStep">{{
                                $t('common.next')
                                }}</a-button>
                            <a-button v-else type="primary" @click="submitForm">{{ $t('policyappoint.submitAppointment')
                                }}</a-button>
                        </div>
                    </div>

                    <a-modal v-model:open="open" :title="$t('policyappoint.inviteLinkGenerated')" @ok="handleOk"
                        :footer="null" width="500px">
                        <div class="invite-modal-content">
                            <!-- 二维码 -->
                            <div class="flex flex-col items-center mb-4">
                                <p class="text-center mb-3">{{ $t('policyappoint.scanQrOrShare') }}</p>
                                <div id="qrcode-container" class="mb-3">
                                    <a-qrcode :value="inviteCodeUrl" :size="200" id="qrcode" />
                                </div>
                                <a-button type="primary" size="small" @click="downloadQRCode">
                                    <template #icon>
                                        <Icon icon="mdi:download" />
                                    </template>
                                    {{ $t('policyappoint.downloadQrCode') }}
                                </a-button>
                            </div>

                            <!-- 链接 -->
                            <div class="mb-4">
                                <div class="flex items-center bg-gray-50 p-3 rounded-md">
                                    <div class="flex-1 truncate mr-2">{{ inviteCodeUrl }}</div>
                                    <a-button type="primary" size="small" @click="copyLink">
                                        {{ $t('policyappoint.copy') }}
                                    </a-button>
                                </div>
                            </div>

                            <!-- 过期时间 -->
                            <div class="text-gray-500 text-sm text-center flex items-center justify-center"
                                v-if="expireTime">
                                <Icon icon="mdi:timer-outline" class="mr-1" />
                                <span> {{ $t('policyappoint.linkValidUntil') }}: {{ formatExpireTime(expireTime)
                                    }}</span>
                            </div>

                            <!-- 底部按钮 -->
                            <div class="flex justify-center mt-6">
                                <a-button @click="closeModal">{{ $t('common.close') }}</a-button>
                            </div>
                        </div>
                    </a-modal>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';
import { ref, reactive, onMounted, computed } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { appointmentAPI } from '../../api';
import { formAPI } from '../../api/form';
// 组件导入
import AppointmentOverviewForm from './components/AppointmentOverviewForm.vue';
import PolicyholderForm from './components/PolicyholderForm.vue';
import InsuredForm from './components/InsuredForm.vue';
import BeneficiaryForm from './components/BeneficiaryForm.vue';
import ProposalForm from './components/ProposalForm.vue';

// 获取国际化功能
const { t } = useI18n();

// 路由实例
const router = useRouter();

// Tab切换
const activeTabKey = ref('overview');
const tabKeys = ['overview', 'proposal', 'policyholder', 'insured', 'beneficiary'];
// 表单引用
const overviewFormRef = ref(null);
const proposalFormRef = ref(null);
const policyholderFormRef = ref(null);
const insuredFormRef = ref(null);
const beneficiaryFormRef = ref(null);

// 用于生成邀请链接的表单数据
const proposalFormData = reactive({
    policyId: '',
    orderId: '',
});
const inviteLink = ref('');
const open = ref(false);
const expireTime = ref(null);

const baseUrl = import.meta.env.DEV
    ? (import.meta.env.VITE_BASE_URL || '')
    : (typeof window !== 'undefined' ? window.location.origin : '');
// 邀请链接
const inviteCodeUrl = computed(() => {
    return baseUrl + inviteLink.value;
});

// 将表单引用组织为数组，与tabKeys对应
const formRefs = computed(() => [
    overviewFormRef.value,
    proposalFormRef.value,
    policyholderFormRef.value,
    insuredFormRef.value,
    beneficiaryFormRef.value
]);

// 表单数据
const formData = reactive({
    overview: {},
    policyholder: {},
    insured: {},
    beneficiary: [],
    proposal: {}
});

// 处理Tab切换
const handleTabChange = (key) => {
    console.log('Tab切换到:', key);
    // 不更新activeTabKey，只通过底部按钮控制切换
    activeTabKey.value = key;
};

// 更新表单数据
const updateFormData = (formType, data) => {
    formData[formType] = data;
};

// 下一步
const nextStep = async () => {
    // 校验当前表单
    const currentIndex = tabKeys.indexOf(activeTabKey.value);
    const currentFormRef = formRefs.value[currentIndex];

    if (!currentFormRef) {
        message.error(t('policyappoint.formReferenceFailed'));
        return;
    }

    try {
        await currentFormRef.validate();
    } catch (error) {
        console.error(t('policyappoint.formValidationFailed'), error);
        message.error(typeof error === 'string' ? error : t('policyappoint.formValidationFailed'));
        return;
    }

    if (activeTabKey.value === 'proposal') {
        Modal.confirm({
            title: t('policyappoint.tip'),
            content: t('policyappoint.generateInviteConfirmation'),
            onOk: async () => {
                //  生成邀请链接
                // 先提交表单
                await submitForm();
                try {
                    const res = await formAPI.createFormInvite(proposalFormData.policyId);
                    inviteLink.value = res.inviteLink;
                    expireTime.value = res.expireTime;
                    open.value = true; // 打开邀请链接弹窗
                    resetForm();
                } catch (error) {
                    message.error(t('policyappoint.generateInviteFailed'));
                }
            },
            onCancel: () => {
                if (currentIndex < tabKeys.length - 1) {
                    activeTabKey.value = tabKeys[currentIndex + 1];
                }
            }
        });
        return;
    }

    if (currentIndex < tabKeys.length - 1) {
        activeTabKey.value = tabKeys[currentIndex + 1];
    }
};

// 格式化过期时间
const formatExpireTime = (timestamp) => {
    const date = new Date(timestamp);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
};

// 复制链接
const copyLink = () => {
    navigator.clipboard.writeText(inviteCodeUrl.value)
        .then(() => {
            message.success(t('policyappoint.linkCopied'));
        })
        .catch(() => {
            message.error(t('policyappoint.copyFailed'));
        });
};

// 下载二维码
const downloadQRCode = () => {
    const qrcodeContainer = document.getElementById('qrcode');
    if (!qrcodeContainer) {
        message.error(t('policyappoint.qrCodeElementNotExist'));
        return;
    }

    // 查找二维码容器内的SVG或Canvas元素
    const svgElement = qrcodeContainer.querySelector('svg');
    const canvasElement = qrcodeContainer.querySelector('canvas');

    console.log('二维码容器:', qrcodeContainer);
    console.log('SVG元素:', svgElement);
    console.log('Canvas元素:', canvasElement);

    if (svgElement) {
        // 如果是SVG元素，使用SVG转换为图片
        const svgData = new XMLSerializer().serializeToString(svgElement);
        const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
        const svgUrl = URL.createObjectURL(svgBlob);

        const img = new Image();
        img.onload = () => {
            const canvas = document.createElement('canvas');
            canvas.width = svgElement.width.baseVal.value;
            canvas.height = svgElement.height.baseVal.value;

            const ctx = canvas.getContext('2d');
            ctx.drawImage(img, 0, 0);

            try {
                const url = canvas.toDataURL('image/png');
                const link = document.createElement('a');
                link.download = t('policyappoint.inviteQrCode') + '.png';
                link.href = url;
                link.click();

                message.success(t('policyappoint.qrCodeDownloaded'));
            } catch (error) {
                message.error(t('policyappoint.downloadQrCodeFailed'));
                console.error(t('policyappoint.downloadQrCodeFailed'), error);
            }

            URL.revokeObjectURL(svgUrl);
        };

        img.src = svgUrl;
    } else if (canvasElement) {
        // 如果是Canvas元素，直接使用
        try {
            const url = canvasElement.toDataURL('image/png');
            const link = document.createElement('a');
            link.download = t('policyappoint.inviteQrCode') + '.png';
            link.href = url;
            link.click();

            message.success(t('policyappoint.qrCodeDownloaded'));
        } catch (error) {
            message.error(t('policyappoint.downloadQrCodeFailed'));
            console.error(t('policyappoint.downloadQrCodeFailed'), error);
        }
    } else {
        // 如果都找不到，使用更简单的方法：创建一个新的二维码图像
        message.info(t('policyappoint.generatingQrCode'));

        // 创建一个临时的img元素来保存二维码
        const img = new Image();
        img.crossOrigin = 'anonymous';
        img.onload = function () {
            // 创建canvas
            const canvas = document.createElement('canvas');
            canvas.width = img.width;
            canvas.height = img.height;

            // 绘制图像
            const ctx = canvas.getContext('2d');
            ctx.drawImage(img, 0, 0);

            // 导出为图片并下载
            try {
                const url = canvas.toDataURL('image/png');
                const link = document.createElement('a');
                link.download = t('policyappoint.inviteQrCode') + '.png';
                link.href = url;
                link.click();

                message.success(t('policyappoint.qrCodeDownloaded'));
            } catch (error) {
                message.error(t('policyappoint.downloadQrCodeFailed'));
                console.error(t('policyappoint.downloadQrCodeFailed'), error);
            }
        };

        // 设置图像源为当前页面的二维码
        // 这里我们直接使用一个新的QR code API生成图像URL
        img.src = `https://api.qrserver.com/v1/create-qr-code/?data=${encodeURIComponent(inviteCodeUrl.value)}&size=200x200`;

        img.onerror = function () {
            message.error(t('policyappoint.generateQrCodeImageFailed'));
        };
    }
};

// 关闭弹窗
const closeModal = () => {
    open.value = false;
    resetForm();
    router.push('/my-policy-orders');
};

// 继续填写表单
const continueToNextStep = () => {
    open.value = false;
    const currentIndex = tabKeys.indexOf(activeTabKey.value);
    if (currentIndex < tabKeys.length - 1) {
        activeTabKey.value = tabKeys[currentIndex + 1];
    }
};

// 处理确认
const handleOk = () => {
    open.value = false;
};

// 上一步
const prevStep = () => {
    const currentIndex = tabKeys.indexOf(activeTabKey.value);
    if (currentIndex > 0) {
        activeTabKey.value = tabKeys[currentIndex - 1];
    }
};

// 重置表单
const resetForm = () => {
    Object.keys(formData).forEach(key => {
        if (Array.isArray(formData[key])) {
            formData[key] = [];
        } else {
            formData[key] = {};
        }
    });
};

// 提交表单
const submitForm = async () => {
    try {
        // 准备表单数据
        const appointmentData = {
            overview: formData.overview,
            policyholder: formData.policyholder,
            insured: formData.insured,
            beneficiary: formData.beneficiary,
            proposal: { ...formData.proposal, customerName: formData.overview.customerName }
        };

        const response = await appointmentAPI.createAppointment(appointmentData);
        proposalFormData.policyId = response.policyId;
        proposalFormData.orderId = response.orderId;
        console.log('proposalFormData', proposalFormData, response);
        message.success(t('policyappoint.appointmentSubmitSuccess'));
        resetForm();
        activeTabKey.value = 'overview'; // 重置回第一个Tab
    } catch (error) {
        message.error(t('policyappoint.submitFailed'));
        console.error(t('policyappoint.submitFailed'), error);
    }
};

// 生命周期钩子
onMounted(() => {
    // 初始化数据或其他操作
    console.log('baseUrl', baseUrl);
});
</script>

<style scoped>
/* 标题区域样式 */
.title-section {
    transition: all 0.3s ease;
}

.title-section:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.page-title {
    color: white;
    position: relative;
    display: inline-block;
}

.page-title::after {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 0;
    width: 40px;
    height: 3px;
    background-color: white;
    border-radius: 3px;
}

.page-description {
    max-width: 600px;
    opacity: 0.9;
}

/* 刷新按钮动画 */
.refresh-btn {
    transition: transform 0.3s ease;
}

.refresh-btn:hover {
    transform: rotate(180deg);
}

.appointment-form {
    max-width: 900px;
    margin: 0 auto;
}

:deep(.ant-tabs-nav) {
    margin-bottom: 24px;
}

:deep(.ant-form-item) {
    margin-bottom: 16px;
}

.invite-modal-content {
    padding: 10px;
}
</style>