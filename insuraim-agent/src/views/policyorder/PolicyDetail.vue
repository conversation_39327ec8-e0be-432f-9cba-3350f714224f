<template>
    <div class="mx-auto">
        <a-spin :spinning="policyDetailLoading">
            <a-alert v-if="policyDetailLoading" :message="$t('policyorder.loading')"
                :description="$t('policyorder.pleaseWait')"></a-alert>

            <div v-if="!policyDetailLoading && !selectedPolicy" class="text-center text-gray-500">
                <a-empty :description="$t('policyorder.noPolicyDetail')" />
            </div>

            <div v-if="!policyDetailLoading && selectedPolicy"
                class="bg-white rounded-lg shadow-md p-6 overflow-y-auto max-h-[calc(100vh-100px)]">
                <h2 class="text-xl font-semibold mb-6">
                    {{ $t('policyorder.policyDetail') }} - {{ selectedPolicy.policyOrder?.orderNo || 'N/A' }}
                </h2>

                <!-- 基本信息组件 -->
                <policy-basic-info :policy-data="selectedPolicy" @update="handleInfoUpdate" />

                <!-- 投保人信息组件 -->
                <policy-policyholder :policyholder="selectedPolicy.policyholderInfo" @update="handleInfoUpdate" />

                <!-- 受保人信息组件 -->
                <policy-insured :insured="selectedPolicy.insuredInfo" @update="handleInfoUpdate" />

                <!-- 受益人信息组件 -->
                <policy-beneficiaries :beneficiaries="selectedPolicy.beneficiaryInfos" @update="handleInfoUpdate"
                    :policyId="selectedPolicy.policyInfo.id" />

                <!-- 缴费记录组件 -->
                <policy-payments :payments="selectedPolicy.payments || []" @add="addPaymentModalVisible = true"
                    @edit="editPayment" @delete="deletePayment" />

                <!-- 保单历史状态组件 -->
                <policy-history :histories="selectedPolicy.policyStatusHistories"
                    :currentStatus="selectedPolicy.policyInfo?.policyStatus || ''"
                    :policyOrderId="selectedPolicy.policyInfo.id" @refresh="loadPolicyDetail" />
            </div>
        </a-spin>



        <!-- 添加缴费记录模态框组件 -->
        <payment-add-modal v-model:visible="addPaymentModalVisible" :policy-order="selectedPolicy?.policyOrder || {}"
            @save="handlePaymentSave" />
    </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { policyOrderAPI } from '@/api'; // 假设API路径
import { message } from 'ant-design-vue';

// 导入组件
import PolicyBasicInfo from './components/PolicyBasicInfo.vue';
import PolicyPolicyholder from './components/PolicyPolicyholder.vue';
import PolicyInsured from './components/PolicyInsured.vue';
import PolicyBeneficiaries from './components/PolicyBeneficiaries.vue';
import PolicyFiles from './components/PolicyFiles.vue';
import PolicyPayments from './components/PolicyPayments.vue';
import PolicyHistory from './components/PolicyHistory.vue';
import PaymentAddModal from './components/PaymentAddModal.vue';

const route = useRoute();
const router = useRouter();

const selectedPolicy = ref({});
const policyDetailLoading = ref(true);
const uploadFileModalVisible = ref(false); // 控制上传文件模态框
const addPaymentModalVisible = ref(false); // 控制添加缴费记录模态框
const fileUploading = ref(false); // 文件上传加载状态

const policyOrderId = computed(() => route.params.policyOrderId);

const loadPolicyDetail = async () => {
    console.log('policyOrderId', policyOrderId.value);
    if (!policyOrderId.value) {
        message.error($t('policyorder.loadDetailFailed'));
        policyDetailLoading.value = false;
        return;
    }
    policyDetailLoading.value = true;
    try {
        const response = await policyOrderAPI.allDetailByOrderId(policyOrderId.value);
        selectedPolicy.value = response;
        console.log('selectedPolicy', selectedPolicy.value.policyOrder);
    } catch (error) {
        console.error($t('policyorder.loadDetailFailed'), error);
        message.error($t('policyorder.loadDetailFailed'));
        selectedPolicy.value = null; // 清空数据以避免渲染错误
    } finally {
        policyDetailLoading.value = false;
    }
};

onMounted(() => {
    loadPolicyDetail();
});

// 处理文件上传
const handleFileUpload = async (formData) => {
    fileUploading.value = true;
    try {
        await policyOrderAPI.uploadAttachment(formData);
        message.success($t('policyorder.fileUploadSuccess'));
        // 重新加载保单详情
        loadPolicyDetail();
    } catch (error) {
        console.error($t('policyorder.fileUploadFailed'), error);
        message.error($t('policyorder.fileUploadFailed') + ': ' + (error.message || $t('common.errors.serverError')));
    } finally {
        fileUploading.value = false;
    }
};

// 处理缴费记录保存
const handlePaymentSave = async (paymentData) => {
    try {
        // 这里应该调用保存缴费记录的API
        console.log($t('policyorder.paymentRecordSaveSuccess'), paymentData);
        message.success($t('policyorder.paymentRecordSaveSuccess'));
        // 重新加载保单详情
        loadPolicyDetail();
    } catch (error) {
        console.error($t('policyorder.paymentRecordSaveFailed'), error);
        message.error($t('policyorder.paymentRecordSaveFailed'));
    }
};

// 编辑缴费记录
const editPayment = (record) => {
    console.log($t('policyorder.editPayment'), record);
    message.info($t('policyorder.editPaymentTodo'));
};

// 删除缴费记录
const deletePayment = (record) => {
    console.log($t('policyorder.deletePayment'), record);
    message.success($t('policyorder.deletePaymentSuccess'));
    // 重新加载保单详情
    loadPolicyDetail();
};

// 处理信息更新
const handleInfoUpdate = () => {
    // 重新加载保单详情
    loadPolicyDetail();
};
</script>

<style scoped>
.mac-style-info-panel {
    border-radius: 8px;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-medium);
}

:deep(.mac-style-list) {
    border-radius: 8px;
    overflow: hidden;
}

:deep(.mac-style-list .ant-list-item:hover) {
    background-color: #f0f7ff;
}

:deep(.mac-style-table-sm .ant-table) {
    border-radius: 6px;
    overflow: hidden;
}

:deep(.ant-timeline-item-content) {
    top: -0.5em;
    /* 根据实际效果微调 */
}

:deep(.ant-timeline-item-head) {
    font-size: 16px;
    /* 调整时间线头部圆点大小 */
}

.whitespace-pre-wrap {
    white-space: pre-wrap;
    /* 保留换行符 */
}
</style>