<template>
    <a-modal :visible="visible" @update:visible="(val) => $emit('update:visible', val)" :title="modalTitle"
        @ok="handleSubmit" @cancel="handleCancel" :okText="$t('policyorder.save')" :cancelText="$t('policyorder.cancel')" :maskClosable="false"
        :destroyOnClose="true" :okButtonProps="{ loading: loading }" :width="modalWidth" :centered="false"
        :bodyStyle="{ maxHeight: '80vh', overflowY: 'auto' }">
        <a-form ref="formRef" :model="formModel" layout="vertical" :labelCol="{ span: 8 }" :wrapperCol="{ span: 24 }">
            <template v-for="(field, index) in formFields" :key="index">
                <!-- 分组标题 -->
                <div v-if="field.type === 'group'" class="border-t border-gray-200 mt-4 pt-2 mb-2">
                    <h4 class="text-md font-medium">{{ field.label }}</h4>
                </div>

                <!-- 表单项 -->
                <a-form-item v-else :label="field.label" :name="field.name" :rules="field.rules"
                    style="width: 90%;margin-right: auto;">
                    <!-- 输入框 -->
                    <a-input v-if="field.type === 'input'" v-model:value="formModel[field.name]"
                        :placeholder="field.placeholder || $t('policyorder.pleaseEnter') + field.label" />

                    <!-- 数字输入框 -->
                    <a-input-number v-else-if="field.type === 'number'" v-model:value="formModel[field.name]"
                        style="width: 100%" :min="field.min" :max="field.max" :precision="field.precision || 2"
                        :placeholder="field.placeholder || $t('policyorder.pleaseEnter') + field.label" />

                    <!-- 日期选择器 -->
                    <a-date-picker v-else-if="field.type === 'date'" v-model:value="formModel[field.name]"
                        style="width: 100%" :placeholder="field.placeholder || $t('policyorder.pleaseSelect') + field.label" />

                    <!-- 下拉选择框 -->
                    <a-select v-else-if="field.type === 'select'" v-model:value="formModel[field.name]"
                        style="width: 100%" :placeholder="field.placeholder || $t('policyorder.pleaseSelect') + field.label">
                        <a-select-option v-for="option in field.options" :key="option.value" :value="option.value">
                            {{ option.label }}
                        </a-select-option>
                    </a-select>

                    <!-- 单选框 -->
                    <a-radio-group v-else-if="field.type === 'radio'" v-model:value="formModel[field.name]">
                        <a-radio v-for="option in field.options" :key="option.value" :value="option.value">
                            {{ option.label }}
                        </a-radio>
                    </a-radio-group>

                    <!-- 多行文本框 -->
                    <a-textarea v-else-if="field.type === 'textarea'" v-model:value="formModel[field.name]"
                        :rows="field.rows || 3" :placeholder="field.placeholder || $t('policyorder.pleaseEnter') + field.label" />

                    <!-- 表格 -->
                    <div v-else-if="field.type === 'table'">
                        <a-table :dataSource="formModel[field.name] || []" :columns="field.columns" :pagination="false"
                            :rowKey="record => record.id || record.key" bordered size="small">
                            <template #bodyCell="{ column, record, index }">
                                <template v-if="column.dataIndex === 'action'">
                                    <a-button type="link" danger
                                        @click="removeTableRow(field.name, index)">{{ $t('policyorder.delete') }}</a-button>
                                </template>
                            </template>
                        </a-table>
                        <a-button type="dashed" block class="mt-2" @click="addTableRow(field.name, field.defaultRow)">
                            <Icon icon="ant-design:plus-outlined" /> {{ $t('policyorder.add') }}{{ field.itemLabel || $t('policyorder.item') }}
                        </a-button>
                    </div>
                </a-form-item>
            </template>
        </a-form>
    </a-modal>
</template>

<script setup>
import { ref, defineProps, defineEmits, reactive, watch, nextTick, onMounted, computed } from 'vue';
import { message, Form } from 'ant-design-vue';
import { Icon } from '@iconify/vue';
import dayjs from 'dayjs';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    title: {
        type: String,
        default: ''
    },
    formFields: {
        type: Array,
        default: () => []
    },
    initialValues: {
        type: Object,
        default: () => ({})
    },
    loading: {
        type: Boolean,
        default: false
    },
    modalWidth: {
        type: [Number, String],
        default: 700
    }
});

const emit = defineEmits(['update:visible', 'submit']);
const formRef = ref();
const formModel = reactive({});

// 计算属性：如果没有提供标题，则使用默认的编辑信息标题
const modalTitle = computed(() => {
    return props.title || t('policyorder.editInfo');
});

// 初始化表单模型，确保表格类型字段有默认值
const initFormModel = () => {
    // 清空现有数据
    Object.keys(formModel).forEach(key => {
        delete formModel[key];
    });

    // 为表格类型字段设置默认空数组
    props.formFields.forEach(field => {
        if (field.type === 'table') {
            formModel[field.name] = [];
        }
    });

    // 处理初始值
    if (props.initialValues && Object.keys(props.initialValues).length > 0) {
        const processedValues = { ...props.initialValues };

        // 处理日期字段
        props.formFields.forEach(field => {
            if (field.type === 'date' && processedValues[field.name]) {
                processedValues[field.name] = dayjs(processedValues[field.name]);
            }
        });

        // 更新表单数据
        Object.keys(processedValues).forEach(key => {
            formModel[key] = processedValues[key];
        });
    }
};

// 监听表单初始值变化
watch(
    () => props.initialValues,
    () => {
        initFormModel();
    },
    { immediate: true, deep: true }
);

// 监听formFields变化
watch(
    () => props.formFields,
    () => {
        // 确保表格类型字段有默认值
        props.formFields.forEach(field => {
            if (field.type === 'table' && !formModel[field.name]) {
                formModel[field.name] = [];
            }
        });
    },
    { immediate: true, deep: true }
);

// 监听visible变化，当打开modal时重置表单
watch(
    () => props.visible,
    (newVal) => {
        if (newVal) {
            // 确保表单数据正确初始化
            initFormModel();

            nextTick(() => {
                // 重置表单验证状态
                formRef.value?.resetFields();
            });
        }
    }
);

// 组件挂载时初始化
onMounted(() => {
    initFormModel();
});

// 添加表格行
const addTableRow = (fieldName, defaultRow) => {
    if (!formModel[fieldName]) {
        formModel[fieldName] = [];
    }

    const newRow = { ...defaultRow, key: Date.now() };
    formModel[fieldName].push(newRow);
};

// 删除表格行
const removeTableRow = (fieldName, index) => {
    if (formModel[fieldName] && Array.isArray(formModel[fieldName])) {
        formModel[fieldName].splice(index, 1);
    }
};

// 处理提交
const handleSubmit = async () => {
    try {
        // 表单验证
        await formRef.value.validate();

        // 处理表单数据
        const formData = { ...formModel };

        // 处理日期字段，将dayjs对象转换为时间戳或字符串
        props.formFields.forEach(field => {
            if (field.type === 'date' && formData[field.name]) {
                if (field.valueFormat === 'timestamp') {
                    formData[field.name] = formData[field.name].valueOf();
                } else {
                    formData[field.name] = formData[field.name].format(field.valueFormat || 'YYYY-MM-DD');
                }
            }
        });

        // 提交表单数据
        emit('submit', formData);
    } catch (error) {
        console.error(t('policyorder.formValidationFailed'), error);
    }
};

// 处理取消
const handleCancel = () => {
    emit('update:visible', false);

    // 重置表单验证状态
    if (formRef.value) {
        formRef.value.resetFields();
    }
};
</script>

<style scoped>
:deep(.ant-form-item-label) {
    padding-bottom: 4px;
}

:deep(.ant-modal-body) {
    max-height: 80vh;
    overflow-y: auto;
}
</style>