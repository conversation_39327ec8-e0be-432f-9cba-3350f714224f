<template>
  <a-modal :visible="visible" @update:visible="(val) => $emit('update:visible', val)" :title="$t('policyorder.uploadFile')" @ok="handleSubmit"
    @cancel="handleCancel" :okText="$t('policyorder.upload')" :cancelText="$t('policyorder.cancel')" :maskClosable="false" :destroyOnClose="true"
    :okButtonProps="{ loading: loading }">
    <a-form ref="formRef" :model="formModel" layout="vertical">
      <a-form-item :label="$t('policyorder.fileType')" name="fileType" :rules="[{ required: true, message: $t('policyorder.fileTypeRequired') }]">
        <a-select v-model:value="formModel.fileType">
          <a-select-option v-for="option in fileTypeOptions" :key="option.value" :value="option.value">
            {{ option.label }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item :label="$t('policyorder.status')" name="status" :rules="[{ required: true, message: $t('policyorder.pleaseSelectStatus') }]">
        <a-select v-model:value="formModel.status" :disabled="statusDisabled">
          <a-select-option v-for="option in policyStatusOptions" :key="option.value" :value="option.value">
            {{ option.label }}
          </a-select-option>
        </a-select>
        <div v-if="statusDisabled && statusLabel" class="text-sm text-gray-500 mt-2 flex items-center">
          <Icon icon="mdi:information-outline" class="mr-1 text-red-500 text-sm" />
          <span class="text-red-500 text-xs" >{{ $t('policyorder.currentStatus') }}：{{ statusLabel }}</span>
        </div>
      </a-form-item>

      <a-form-item :label="$t('policyorder.uploadFile')" name="file" :rules="[{ required: true, message: $t('policyorder.fileRequired') }]">
        <a-upload-dragger v-model:fileList="fileList" name="file" :multiple="false" :before-upload="beforeUpload"
          @remove="handleRemove" :maxCount="1">
          <p class="ant-upload-drag-icon flex items-center justify-center">
            <Icon icon="material-symbols:upload-file" class="text-3xl text-blue-500" />
          </p>
          <p class="ant-upload-text">{{ $t('policyorder.dragFileText') }}</p>
          <p class="ant-upload-hint">{{ $t('policyorder.uploadHint') }}</p>
        </a-upload-dragger>
      </a-form-item>
      <a-form-item :label="$t('policyorder.remarks')" name="remark">
        <a-textarea v-model:value="formModel.remark" :rows="3" :placeholder="$t('policyorder.remarkPlaceholder')" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, defineProps, defineEmits, reactive, watch, computed } from 'vue';
import { message, Form } from 'ant-design-vue';
import { Icon } from '@iconify/vue';
import { PolicyStatusEnum } from '@/store/modules/order';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  policyOrderId: {
    type: [String, Number],
    default: ''
  },
  loading: {
    type: Boolean,
    default: false
  },
  orderStatus: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:visible', 'upload', 'upload-complete']);
const formRef = ref();

// 表单数据模型
const formModel = reactive({
  fileType: 'POLICY_DOCUMENT',
  status: '',
  file: null,
  remark: ''
});

// 监听 orderStatus 变化，自动设置表单状态值
watch(() => props.orderStatus, (newVal) => {
  if (newVal) {
    formModel.status = newVal;
  }
}, { immediate: true });

// 监听模态框可见性变化，重置表单
watch(() => props.visible, (newVal) => {
  if (newVal && props.orderStatus) {
    formModel.status = props.orderStatus;
  }
});

const fileList = ref([]);
const currentFile = ref(null);

// 判断是否应该禁用状态选择
const statusDisabled = computed(() => !!props.orderStatus);

// 获取当前状态的文本标签
const statusLabel = computed(() => {
  if (!formModel.status) return '';
  const option = policyStatusOptions.find(opt => opt.value === formModel.status);
  return option ? option.label : '';
});

// 文件类型选项
const fileTypeOptions = [
  { value: 'POLICY_DOCUMENT', label: t('policyorder.policyFiles') },
  { value: 'PAYMENT_PROOF', label: t('policyorder.paymentProof') },
  { value: 'IDENTIFICATION', label: t('policyorder.identification') },
  { value: 'MEDICAL_REPORT', label: t('policyorder.medicalReport') },
  { value: 'OTHER', label: t('policyorder.otherFile') }
];

// 保单状态选项
const policyStatusOptions = [
  { value: '', label: t('policyorder.selectUploadStatus') },
  { value: PolicyStatusEnum.APPOINTMENT, label: t('policyorder.appointment') },
  { value: PolicyStatusEnum.SIGN_APPOINTMENT, label: t('policyorder.signAppointment') },
  { value: PolicyStatusEnum.SIGN, label: t('policyorder.sign') },
  { value: PolicyStatusEnum.PAYMENT_GUIDE, label: t('policyorder.paymentGuide') },
  { value: PolicyStatusEnum.PAYMENT_RECORD, label: t('policyorder.paymentRecord') },
  { value: PolicyStatusEnum.EFFECTIVE, label: t('policyorder.effective') },
  { value: PolicyStatusEnum.CONTRACT_SENT, label: t('policyorder.contractSent') },
  { value: PolicyStatusEnum.SIGNATURE, label: t('policyorder.signature') },
  { value: PolicyStatusEnum.QUIET_PERIOD_EXPIRED, label: t('policyorder.quietPeriodExpired') }
];

// 上传前检查
const beforeUpload = (file) => {
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error(t('policyorder.fileSizeLimit'));
    return false;
  }

  // 直接保存文件对象
  currentFile.value = file;
  formModel.file = file; // 同时更新表单模型中的file字段
  return false; // 阻止自动上传
};

// 移除文件
const handleRemove = () => {
  fileList.value = [];
  currentFile.value = null;
  formModel.file = null; // 同时更新表单模型中的file字段
};

// 处理提交
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value.validate();

    // 检查文件是否已选择
    if (!currentFile.value) {
      message.error(t('policyorder.fileRequired'));
      return;
    }

    const formData = new FormData();
    formData.append('file', currentFile.value);
    formData.append('fileType', formModel.fileType);
    formData.append('policyId', props.policyOrderId);
    
    // 优先使用表单中的状态，如果为空则使用 props 中的状态
    const status = formModel.status || props.orderStatus;
    if (!status) {
      message.error(t('policyorder.pleaseSelectStatus'));
      return;
    }
    formData.append('status', status);
    
    if (formModel.remark) {
      formData.append('remark', formModel.remark);
    }

    // 发送上传事件，由父组件处理上传逻辑
    // 不再自动关闭模态框，等待父组件通知上传完成
    emit('upload', formData);
  } catch (error) {
    console.error(t('policyorder.formValidationFailed'), error);
  }
};

// 处理取消
const handleCancel = () => {
  emit('update:visible', false);
  fileList.value = [];
  currentFile.value = null;

  // 重置表单模型
  formModel.fileType = 'POLICY_DOCUMENT';
  formModel.status = props.orderStatus || ''; // 保持使用传入的状态
  formModel.file = null;
  formModel.remark = '';

  // 重置表单验证状态
  if (formRef.value) {
    formRef.value.resetFields();
    
    // 如果有指定状态，重置后再次设置状态值
    if (props.orderStatus) {
      setTimeout(() => {
        formModel.status = props.orderStatus;
      }, 0);
    }
  }
};

// 处理上传完成
const handleUploadComplete = (success = true) => {
  // 如果上传成功，关闭模态框并重置
  if (success) {
    handleCancel();
  }
};
</script>

<style scoped>
:deep(.ant-upload-list-item) {
  margin-top: 8px;
}

:deep(.ant-upload-drag) {
  padding: 16px;
}
</style>