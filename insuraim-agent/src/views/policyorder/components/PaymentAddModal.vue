<template>
  <a-modal :visible="visible" @update:visible="(val) => $emit('update:visible', val)" :title="$t('policyorder.addPaymentRecord')" @ok="handleSubmit" @cancel="handleCancel" :okText="$t('policyorder.save')"
    :cancelText="$t('policyorder.cancel')" :maskClosable="false" :destroyOnClose="true">
    <a-form :model="formState" layout="vertical" ref="formRef">
      <a-form-item :label="$t('policyorder.paymentDate')" name="paymentDate" :rules="[{ required: true, message: $t('policyorder.pleaseSelectPaymentDate') }]">
        <a-date-picker v-model:value="formState.paymentDate" style="width: 100%" />
      </a-form-item>
      <a-form-item :label="$t('policyorder.paymentAmount')" name="amount" :rules="[{ required: true, message: $t('policyorder.pleaseEnterPaymentAmount') }]">
        <a-input-number v-model:value="formState.amount" style="width: 100%" :min="0" :precision="2" />
      </a-form-item>
      <a-form-item :label="$t('policyorder.currency')" name="currency" :rules="[{ required: true, message: $t('policyorder.pleaseSelectCurrency') }]">
        <a-select v-model:value="formState.currency">
          <a-select-option value="HKD">{{ $t('policyorder.hkd') }}</a-select-option>
          <a-select-option value="USD">{{ $t('policyorder.usd') }}</a-select-option>
          <a-select-option value="CNY">{{ $t('policyorder.cny') }}</a-select-option>
          <a-select-option value="SGD">{{ $t('policyorder.sgd') }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item :label="$t('policyorder.paymentMethod')" name="paymentMethod" :rules="[{ required: true, message: $t('policyorder.pleaseSelectPaymentMethod') }]">
        <a-select v-model:value="formState.paymentMethod">
          <a-select-option value="BANK_TRANSFER">{{ $t('policyorder.bankTransfer') }}</a-select-option>
          <a-select-option value="CREDIT_CARD">{{ $t('policyorder.creditCard') }}</a-select-option>
          <a-select-option value="CHEQUE">{{ $t('policyorder.cheque') }}</a-select-option>
          <a-select-option value="CASH">{{ $t('policyorder.cash') }}</a-select-option>
          <a-select-option value="OTHER">{{ $t('policyorder.other') }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item :label="$t('policyorder.remarks')" name="remark">
        <a-textarea v-model:value="formState.remark" :rows="3" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, defineProps, defineEmits, watch } from 'vue';
import dayjs from 'dayjs';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  policyOrder: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'save']);

const formRef = ref(null);
const formState = reactive({
  paymentDate: dayjs(),
  amount: null,
  currency: props.policyOrder?.currency || 'HKD',
  paymentMethod: 'BANK_TRANSFER',
  remark: ''
});

// 监听policyOrder变化，更新币种
watch(() => props.policyOrder, (newVal) => {
  if (newVal?.currency) {
    formState.currency = newVal.currency;
  }
}, { deep: true });

// 处理提交
const handleSubmit = () => {
  formRef.value.validate().then(() => {
    const paymentData = {
      ...formState,
      paymentDate: formState.paymentDate.format('YYYY-MM-DD'),
      policyOrderId: props.policyOrder?.id
    };
    
    emit('save', paymentData);
    handleCancel();
  }).catch(error => {
    console.log(t('policyorder.validationFailed'), error);
  });
};

// 处理取消
const handleCancel = () => {
  emit('update:visible', false);
  formRef.value?.resetFields();
};
</script> 