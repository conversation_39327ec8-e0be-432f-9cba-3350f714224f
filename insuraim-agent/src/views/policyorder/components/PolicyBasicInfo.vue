<template>
  <section class="mb-8">
    <div class="title flex items-center justify-between border-b  ">
      <h3 class="text-lg font-medium mt-0 mb-0">{{ $t('policyorder.appointmentInfo') }}</h3>
      <a-tooltip :title="$t('policyorder.editInfo')">
        <a-button type="primary" size="middle" class="mr-2 mb-1" @click="showEditModal">
          <template #icon>
            <Icon icon="ant-design:edit-outlined" />
          </template>
          {{ $t('policyorder.edit') }}
        </a-button>
      </a-tooltip>
    </div>
    <div class="space-y-4 bg-gray-50 p-4 rounded-lg mac-style-info-panel">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-3">
        <div class="flex items-start">
          <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.orderNo') }}：</span>
          <span class="text-base font-medium break-words">{{ policyData.policyOrder?.orderNo }}</span>
        </div>
        <div class="flex items-start">
          <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.appointmentTime') }}：</span>
          <span class="text-base font-medium break-words">{{ formatDate(policyData.policyOrder?.appointmentTime, true)
            ||
            'N/A'
          }}</span>
        </div>
        <div class="flex items-start">
          <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.firstPremiumPaymentMethod')
            }}：</span>
          <span class="text-base font-medium break-words">{{ policyData.policyInfo?.firstPremiumPaymentMethod || 'N/A'
            }}</span>
        </div>
        <div class="flex items-start">
          <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.renewalPaymentMethod') }}：</span>
          <span class="text-base font-medium break-words">{{ policyData.policyInfo?.renewalPaymentMethod || 'N/A'
            }}</span>
        </div>
        <div class="flex items-start">
          <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.customerName') }}：</span>
          <span class="text-base font-medium break-words">{{ policyData.policyOrder?.customerName }}</span>
        </div>
        <div class="flex items-start">
          <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.policyNo') }}：</span>
          <span class="text-base font-medium break-words">{{ policyData.policyInfo?.policyNo || 'N/A' }}</span>
        </div>
        <div class="flex items-start">
          <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.policyholder') }}：</span>
          <span class="text-base font-medium break-words">{{ policyData.policyOrder?.policyholderNameCn }}
            <span v-if="policyData.policyOrder?.policyholderNameEn" class="text-xs text-gray-500 italic ml-1">
              ({{ policyData.policyOrder?.policyholderNameEn }})
            </span>
          </span>
        </div>
        <div class="flex items-start">
          <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.insured') }}：</span>
          <span class="text-base font-medium break-words">{{ policyData.policyOrder?.insuredNameCn }}
            <span v-if="policyData.policyOrder?.insuredNameEn" class="text-xs text-gray-500 italic ml-1">
              ({{ policyData.policyOrder?.insuredNameEn }})
            </span>
          </span>
        </div>
        <div class="flex items-start">
          <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.region') }}：</span>
          <span class="text-base break-words">{{ policyData.policyOrder?.region }}</span>
        </div>
        <div class="flex items-start">
          <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.company') }}：</span>
          <span class="text-base break-words">{{ policyData.policyOrder?.company }}</span>
        </div>
        <div class="flex items-start">
          <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.product') }}：</span>
          <span class="text-base break-words">{{ policyData.policyOrder?.product }}</span>
        </div>
        <div class="flex items-start">
          <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.status') }}：</span>
          <a-tag v-if="policyData.policyOrder?.orderStatus" :color="getStatusColor(policyData.policyOrder.orderStatus)"
            class="text-base">
            {{ getStatusText(policyData.policyOrder.orderStatus) }}
          </a-tag>
          <span v-else class="text-base break-words">N/A</span>
        </div>
        <div class="flex items-start">
          <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.paymentTerm') }}：</span>
          <span class="text-base break-words">{{ policyData.policyOrder?.paymentTerm }}</span>
        </div>
        <div class="flex items-start">
          <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.annualPremium') }}：</span>
          <span class="text-base break-words">{{ formatAmount(policyData.policyOrder?.annualPremium,
            policyData.policyOrder?.currency) }}</span>
        </div>
        <div class="flex items-start">
          <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.effectiveDate') }}：</span>
          <span class="text-base break-words">{{ formatDate(policyData.policyInfo?.effectiveDate) || 'N/A'
            }}</span>
        </div>
        <div class="flex items-start">
          <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.nextRenewalAmount') }}：</span>
          <span class="text-base break-words">{{ formatAmount(policyData.policyInfo?.nextRenewalAmount,
            policyData.policyInfo?.currency || policyData.policyOrder?.currency) }}</span>
        </div>
        <div class="flex items-start">
          <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.nextRenewalDate') }}：</span>
          <span class="text-base break-words">{{ formatDate(policyData.policyInfo?.nextRenewalDate) }}</span>
        </div>
        <div class="flex items-start">
          <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.paymentAmount') }}：</span>
          <span class="text-base break-words">{{ formatAmount(policyData.policyInfo?.coverageAmount,
            policyData.policyOrder?.currency) || 'N/A' }}</span>
        </div>
      </div>
      <div class="flex items-start mt-4">
        <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.remarks') }}：</span>
        <span class="text-base whitespace-pre-wrap break-words">{{ policyData.policyOrder?.remark || $t('common.noData')
          }}</span>
      </div>
    </div>

    <!-- 编辑信息Modal -->
    <edit-info-modal v-model:visible="editModalVisible" :title="$t('policyorder.editInfo')" :form-fields="formFields"
      :initial-values="initialFormValues" :loading="loading" @submit="handleSubmit"
      @update:visible="handleModalVisibleChange" />
  </section>
</template>

<script setup>
import { defineProps, defineEmits, ref, computed, reactive, onMounted, watch } from 'vue';
import { useOrderStore, OrderStatusEnum } from '@/store/modules/order';
import { policyOrderAPI } from '@/api';
import dayjs from 'dayjs';
import { message } from 'ant-design-vue';
import { Icon } from '@iconify/vue';
import EditInfoModal from './EditInfoModal.vue';
import { useI18n } from 'vue-i18n';
import { formatNumber } from '@/utils/number';

const { t } = useI18n();

const props = defineProps({
  policyData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update']);

// 使用store中的枚举
const orderStore = useOrderStore();

// 编辑模态框状态
const editModalVisible = ref(false);
const loading = ref(false);

// 格式化日期
const formatDate = (dateString, includeTime = false) => {
  if (!dateString) return 'N/A';
  const format = includeTime ? 'YYYY-MM-DD HH:mm' : 'YYYY-MM-DD';
  return dayjs(dateString).isValid() ? dayjs(dateString).format(format) : dateString;
};

// 格式化金额
const formatAmount = (amount, currency) => {
  if (!amount || amount === 'N/A') return 'N/A';

  // 根据货币类型设置格式化选项
  const options = {
    decimals: 2,
    currency: currency,
    useGrouping: true,
    defaultValue: 'N/A'
  };

  return formatNumber(amount, options);
};

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    [OrderStatusEnum.APPOINTMENT]: 'processing',
    [OrderStatusEnum.WAIT_PAYMENT]: 'gold',
    [OrderStatusEnum.QUIET_PERIOD]: 'warning',
    [OrderStatusEnum.IN_EFFECT]: 'success',
    [OrderStatusEnum.EXPIRED]: 'error'
  };
  return colorMap[status] || 'default';
};

// 将状态枚举值转换为中文描述
const getStatusText = (status) => {
  const statusMap = {
    [OrderStatusEnum.APPOINTMENT]: t('policyorder.appointment'),
    [OrderStatusEnum.WAIT_PAYMENT]: t('policyorder.waitPayment'),
    [OrderStatusEnum.QUIET_PERIOD]: t('policyorder.quietPeriod'),
    [OrderStatusEnum.IN_EFFECT]: t('policyorder.inEffect'),
    [OrderStatusEnum.EXPIRED]: t('policyorder.expired')
  };
  return statusMap[status] || status;
};

// 货币选项
const currencyOptions = [
  { value: 'CNY', label: t('policyorder.cny') },
  { value: 'HKD', label: t('policyorder.hkd') },
  { value: 'USD', label: t('policyorder.usd') },
  { value: 'SGD', label: t('policyorder.sgd') }
];

// 状态选项
const statusOptions = computed(() => {
  return Object.keys(OrderStatusEnum).map(key => ({
    value: OrderStatusEnum[key],
    label: getStatusText(OrderStatusEnum[key])
  }));
});

// 表单字段配置 - 使用响应式对象
const formFields = reactive([
  {
    label: t('policyorder.customerName'),
    name: 'customerName',
    type: 'input',
    rules: [{ required: true, message: t('policyorder.pleaseEnterCustomerName') }]
  },
  {
    label: t('policyorder.region'),
    name: 'region',
    type: 'input',
    rules: [{ required: true, message: t('policyorder.pleaseEnterRegion') }]
  },
  {
    label: t('policyorder.company'),
    name: 'company',
    type: 'input',
    rules: [{ required: true, message: t('policyorder.pleaseEnterCompany') }]
  },

  {
    label: t('policyorder.product'),
    name: 'product',
    type: 'input',
    rules: [{ required: true, message: t('policyorder.pleaseEnterProduct') }]
  },
  {
    label: t('policyorder.firstPremiumPaymentMethod'),
    name: 'firstPremiumPaymentMethod',
    type: 'input',
    rules: [{ required: true, message: t('policyorder.pleaseSelectFirstPremiumPaymentMethod') }]
  },
  {
    label: t('policyorder.renewalPaymentMethod'),
    name: 'renewalPaymentMethod',
    type: 'input',
    rules: [{ required: true, message: t('policyorder.pleaseSelectRenewalPaymentMethod') }]
  },
  {
    label: t('policyorder.phoneNumber'),
    name: 'phone',
    type: 'input',
    rules: [
      { required: true, message: t('policyorder.pleaseEnterPhoneNumber') },
      { pattern: /^1[3-9]\d{9}$/, message: t('policyorder.pleaseEnterValidPhoneNumber') }
    ]
  },
  {
    label: t('policyorder.email'),
    name: 'email',
    type: 'input',
    rules: [
      { required: true, message: t('policyorder.pleaseEnterEmail') },
      { type: 'email', message: t('policyorder.pleaseEnterValidEmail') }
    ]
  },
  {
    label: t('policyorder.team'),
    name: 'team',
    type: 'input',
    rules: [{ required: false, message: t('policyorder.pleaseEnterTeam') }]
  },
  {
    label: t('policyorder.referrer'),
    name: 'referrer',
    type: 'input',
    rules: [{ required: false, message: t('policyorder.pleaseEnterReferrer') }]
  },
  {
    label: t('policyorder.appointmentTime'),
    name: 'appointmentTime',
    type: 'date',
    valueFormat: 'timestamp',
    rules: [{ required: true, message: t('policyorder.pleaseSelectAppointmentTime') }]
  },
  {
    label: t('policyorder.paymentTerm'),
    name: 'paymentTerm',
    type: 'input',
    rules: [{ required: true, message: t('policyorder.pleaseEnterPaymentTerm') }]
  },
  {
    label: t('policyorder.currency'),
    name: 'currency',
    type: 'select',
    options: currencyOptions,
    rules: [{ required: true, message: t('policyorder.pleaseSelectCurrency') }]
  },
  {
    label: t('policyorder.coverageAmount'),
    name: 'coverageAmount',
    type: 'number',
    min: 0,
    precision: 2,
    rules: [{ required: true, message: t('policyorder.pleaseEnterCoverageAmount') }]
  },
  {
    label: t('policyorder.annualPremium'),
    name: 'annualPremium',
    type: 'number',
    min: 0,
    precision: 2,
    rules: [{ required: true, message: t('policyorder.pleaseEnterAnnualPremium') }]
  },
  // {
  //   label: t('policyorder.nextRenewal'),
  //   name: 'nextRenewalDate',
  //   type: 'date',
  //   valueFormat: 'timestamp',
  //   rules: [{ required: false, message: t('policyorder.pleaseSelectNextRenewalDate') }]
  // },
  // {
  //   label: t('policyorder.status'),
  //   name: 'orderStatus',
  //   type: 'select',
  //   options: [], // 初始为空数组，在updateFormFields函数中更新
  //   rules: [{ required: true, message: t('policyorder.pleaseSelectStatus') }]
  // },
  {
    label: t('policyorder.remarks'),
    name: 'remark',
    type: 'textarea',
    rows: 3,
    rules: [{ required: false, message: t('policyorder.pleaseEnterRemarks') }]
  }
]);

// 更新表单字段中的动态选项
const updateFormFields = () => {
  // 找到orderStatus字段并更新其options
  const orderStatusField = formFields.find(field => field.name === 'orderStatus');
  if (orderStatusField) {
    orderStatusField.options = statusOptions.value;
  }
};

// 监听statusOptions的变化
watch(statusOptions, () => {
  updateFormFields();
}, { immediate: true });

// 组件挂载时更新表单字段
onMounted(() => {
  updateFormFields();
});

// 初始表单值
const initialFormValues = computed(() => {
  if (!props.policyData?.policyOrder) return {};
  return {
    ...props.policyData.policyOrder,
    coverageAmount: props.policyData.policyInfo?.coverageAmount || 0,
    firstPremiumPaymentMethod: props.policyData.policyInfo?.firstPremiumPaymentMethod || '',
    renewalPaymentMethod: props.policyData.policyInfo?.renewalPaymentMethod || ''
  };
});

// 显示编辑模态框
const showEditModal = () => {
  // 确保在打开模态框前更新表单字段中的选项
  updateFormFields();
  editModalVisible.value = true;
};

// 处理模态框可见性变化
const handleModalVisibleChange = (visible) => {
  if (visible) {
    // 模态框打开时更新表单字段
    updateFormFields();
  }
};

// 处理表单提交
const handleSubmit = async (formData) => {
  loading.value = true;
  try {
    // 调用真实API更新数据
    const updateData = {
      ...formData,
      policyId: props.policyData.policyInfo?.id,
      orderId: props.policyData.policyOrder?.id,
      // 确保这两个字段被正确传递给API
      firstPremiumPaymentMethod: formData.firstPremiumPaymentMethod,
      renewalPaymentMethod: formData.renewalPaymentMethod

    };

    await policyOrderAPI.updatePolicyBasicInfo(updateData);

    message.success(t('policyorder.basicInfoUpdateSuccess'));
    editModalVisible.value = false;

    // 通知父组件更新数据
    emit('update');
  } catch (error) {
    console.error(t('policyorder.updateBasicInfoFailed'), error);
    message.error(t('policyorder.updateBasicInfoFailed') + ': ' + (error.message || t('common.errors.serverError')));
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.mac-style-info-panel {
  border-radius: 8px;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-medium);
}

.whitespace-pre-wrap {
  white-space: pre-wrap;
  /* 保留换行符 */
}
</style>