<template>
  <section class="mb-8">
    <div class="title flex items-center justify-between border-b  ">
      <h3 class="text-lg font-medium mt-0 mb-0">{{ $t('policyorder.beneficiaryInfo') }}</h3>
      <a-tooltip :title="$t('policyorder.addBeneficiary')">
        <a-button type="primary" size="middle" class="mr-2 mb-1" @click="showAddModal">
          <template #icon>
            <Icon icon="ant-design:plus-outlined" />
          </template>
          {{ $t('policyorder.add') }}
        </a-button>
      </a-tooltip>
    </div>

    <div class="space-y-4 bg-gray-50 p-4 rounded-lg mac-style-info-panel">
      <div v-if="!beneficiaries || beneficiaries.length === 0" class="text-center py-4 text-gray-500">
        <a-empty :description="$t('policyorder.noBeneficiaryInfo')" />
      </div>

      <div v-else class="space-y-4">
        <a-table :dataSource="beneficiaries" :pagination="false" :rowKey="record => record.id" bordered
          class="mac-style-table-sm">
          <a-table-column :title="$t('policyorder.name')" dataIndex="name" />
          <a-table-column :title="$t('policyorder.gender')" dataIndex="gender">
            <template #default="{ text }">
              {{ formatGender(text) }}
            </template>
          </a-table-column>
          <a-table-column :title="$t('policyorder.relationship')" dataIndex="relationship" />
          <a-table-column :title="$t('policyorder.idNumber')" dataIndex="idCardNo" />
          <a-table-column :title="$t('policyorder.beneficiaryShare')" dataIndex="benefitPercentage">
            <template #default="{ text }">
              {{ text }}%
            </template>
          </a-table-column>
          <a-table-column :title="$t('policyorder.isTrustee')" dataIndex="isTrustee">
            <template #default="{ text }">
              {{ text === 1 ? $t('policyorder.yes') : $t('policyorder.no') }}
            </template>
          </a-table-column>
          <a-table-column :title="$t('policyorder.action')">
            <template #default="{ record }">
              <a-space size="small">
                <a-button type="primary" shape="circle" size="middle" @click="showEditModal(record)">
                  <template #icon>
                    <Icon icon="material-symbols:edit-outline" />
                  </template>
                </a-button>
                <a-button type="primary" shape="circle" size="middle" danger @click="confirmDelete(record)">
                  <template #icon>
                    <Icon icon="material-symbols:delete-outline" />
                  </template>
                </a-button>
              </a-space>
            </template>
          </a-table-column>
        </a-table>

        <div class="pt-2 text-right text-sm text-gray-500">
          {{ $t('policyorder.totalBenefitPercentage') }}: {{ getTotalPercentage() }}%
        </div>
      </div>
    </div>

    <!-- 编辑信息Modal -->
    <edit-info-modal v-model:visible="editModalVisible" :title="modalTitle" :form-fields="formFields"
      :initial-values="initialFormValues" :loading="loading" @submit="handleSubmit"
      @update:visible="handleModalVisibleChange" />
  </section>
</template>

<script setup>
import { defineProps, defineEmits, ref, computed } from 'vue';
import { Empty, message, Modal } from 'ant-design-vue';
import { Icon } from '@iconify/vue';
import EditInfoModal from './EditInfoModal.vue';
import { useI18n } from 'vue-i18n';
import { policyOrderAPI } from '@/api';

const { t } = useI18n();

const props = defineProps({
  beneficiaries: {
    type: Array,
    default: () => []
  },
  policyId: {
    type: Number,
    default: 0
  }
});

const emit = defineEmits(['update']);

// 编辑模态框状态
const editModalVisible = ref(false);
const loading = ref(false);
const currentEditItem = ref(null); // 当前正在编辑的单个受益人
const isAdding = ref(false); // 标记是添加模式还是编辑模式

// 格式化性别
const formatGender = (gender) => {
  if (gender === 1) return t('policyorder.male');
  if (gender === 0) return t('policyorder.female');
  return 'N/A';
};

// 计算总受益比例
const getTotalPercentage = () => {
  if (!props.beneficiaries || props.beneficiaries.length === 0) return 0;

  return props.beneficiaries.reduce((total, current) => {
    return total + (current.benefitPercentage || 0);
  }, 0);
};

// 关系选项
const relationshipOptions = [
  { value: t('policyorder.spouse'), label: t('policyorder.spouse') },
  { value: t('policyorder.child'), label: t('policyorder.child') },
  { value: t('policyorder.parent'), label: t('policyorder.parent') },
  { value: t('policyorder.sibling'), label: t('policyorder.sibling') },
  { value: t('policyorder.friend'), label: t('policyorder.friend') },
  { value: t('policyorder.other'), label: t('policyorder.other') }
];

// 性别选项
const genderOptions = [
  { value: 1, label: t('policyorder.male') },
  { value: 0, label: t('policyorder.female') }
];

// 是否托管人选项
const trusteeOptions = [
  { value: 1, label: t('policyorder.yes') },
  { value: 0, label: t('policyorder.no') }
];

// 根据是否有currentEditItem决定表单字段
const formFields = computed(() => {
  if (currentEditItem.value) {
    // 编辑单个受益人的表单字段
    return [
      {
        label: t('policyorder.name'),
        name: 'name',
        type: 'input',
        rules: [{ required: true, message: t('policyorder.pleaseEnterName') }]
      },
      {
        label: t('policyorder.gender'),
        name: 'gender',
        type: 'radio',
        options: genderOptions,
        rules: [{ required: true, message: t('policyorder.pleaseSelectGender') }]
      },
      {
        label: t('policyorder.relationship'),
        name: 'relationship',
        type: 'select',
        options: relationshipOptions,
        rules: [{ required: true, message: t('policyorder.pleaseSelectRelationship') }]
      },
      {
        label: t('policyorder.idNumber'),
        name: 'idCardNo',
        type: 'input',
        rules: [{ required: true, message: t('policyorder.pleaseEnterIdNumber') }]
      },
      {
        label: t('policyorder.beneficiaryShare') + '(%)',
        name: 'benefitPercentage',
        type: 'number',
        min: 0,
        max: 100,
        precision: 2,
        rules: [{ required: true, message: t('policyorder.pleaseEnterBenefitPercentage') }]
      },
      {
        label: t('policyorder.isTrustee'),
        name: 'isTrustee',
        type: 'radio',
        options: trusteeOptions,
        rules: [{ required: true, message: t('policyorder.pleaseSelectIsTrustee') }]
      }
    ];
  } else {
    // 编辑整个受益人列表的表单字段
    return [
      {
        label: t('policyorder.beneficiaryList'),
        name: 'beneficiaries',
        type: 'table',
        itemLabel: t('policyorder.beneficiary'),
        columns: [
          {
            title: t('policyorder.name'),
            dataIndex: 'name',
            key: 'name',
            width: 120
          },
          {
            title: t('policyorder.gender'),
            dataIndex: 'gender',
            key: 'gender',
            width: 80,
            render: (text) => (text === 1 ? t('policyorder.male') : t('policyorder.female'))
          },
          {
            title: t('policyorder.relationship'),
            dataIndex: 'relationship',
            key: 'relationship',
            width: 100
          },
          {
            title: t('policyorder.idNumber'),
            dataIndex: 'idCardNo',
            key: 'idCardNo',
            width: 180
          },
          {
            title: t('policyorder.beneficiaryShare') + '(%)',
            dataIndex: 'benefitPercentage',
            key: 'benefitPercentage',
            width: 100
          },
          {
            title: t('policyorder.isTrustee'),
            dataIndex: 'isTrustee',
            key: 'isTrustee',
            width: 100,
            render: (text) => (text === 1 ? t('policyorder.yes') : t('policyorder.no'))
          },
          {
            title: t('policyorder.action'),
            dataIndex: 'action',
            key: 'action',
            width: 80
          }
        ],
        defaultRow: {
          name: '',
          gender: 1,
          relationship: t('policyorder.spouse'),
          idCardNo: '',
          benefitPercentage: 0,
          isTrustee: 0,
          key: Date.now()
        },
        rules: [{ required: true, message: t('policyorder.pleaseAddBeneficiary') }]
      }
    ];
  }
});

// 模态框标题
const modalTitle = computed(() => {
  if (!currentEditItem.value) {
    return t('policyorder.beneficiaryList');
  }
  return isAdding.value ? t('policyorder.addBeneficiary') : t('policyorder.editBeneficiary');
});

// 初始表单值
const initialFormValues = computed(() => {
  if (currentEditItem.value) {
    // 编辑单个受益人
    return { ...currentEditItem.value };
  } else {
    // 编辑整个受益人列表
    if (!props.beneficiaries || props.beneficiaries.length === 0) {
      return { beneficiaries: [] };
    }

    // 为每个受益人添加key属性，用于表格渲染
    const beneficiariesWithKey = props.beneficiaries.map(item => ({
      ...item,
      key: item.id || Date.now()
    }));

    return { beneficiaries: beneficiariesWithKey };
  }
});

// 显示添加模态框
const showAddModal = () => {
  // 创建一个带有默认值的新受益人对象
  currentEditItem.value = {
    name: '',
    gender: 1,
    relationship: t('policyorder.spouse'),
    idCardNo: '',
    benefitPercentage: 0,
    isTrustee: 0,
    key: Date.now()
  };
  isAdding.value = true;
  editModalVisible.value = true;
};

// 显示编辑模态框
const showEditModal = (record) => {
  currentEditItem.value = { ...record };
  isAdding.value = false;
  editModalVisible.value = true;
};

// 确认删除
const confirmDelete = (record) => {
  Modal.confirm({
    title: t('policyorder.confirmDelete'),
    content: t('policyorder.confirmDeleteBeneficiary', { name: record.name }),
    okText: t('policyorder.confirm'),
    cancelText: t('policyorder.cancel'),
    onOk: () => handleDelete(record)
  });
};

// 处理删除
const handleDelete = async (record) => {
  try {
    // 调用真实API删除数据
    await policyOrderAPI.deletePolicyBeneficiaryInfo(record.id);

    message.success(t('policyorder.beneficiaryDeleteSuccess'));

    // 通知父组件更新数据
    emit('update');
  } catch (error) {
    console.error(t('policyorder.deleteBeneficiaryFailed'), error);
    message.error(t('policyorder.deleteBeneficiaryFailed') + ': ' + (error.message || t('common.errors.serverError')));
  }
};

// 处理模态框可见性变化
const handleModalVisibleChange = (visible) => {
  if (!visible) {
    // 模态框关闭时重置状态
    currentEditItem.value = null;
    isAdding.value = false;
  }
};

// 处理表单提交
const handleSubmit = async (formData) => {
  loading.value = true;
  try {
    if (currentEditItem.value) {
      if (isAdding.value) {
        // 添加新受益人
        // 检查受益比例总和是否超过100%
        const currentTotal = getTotalPercentage();
        const newPercentage = parseFloat(formData.benefitPercentage) || 0;

        if (currentTotal + newPercentage > 100) {
          message.error(t('policyorder.benefitPercentageExceed', { current: currentTotal }));
          loading.value = false;
          return;
        }
        const data = {
          ...formData,
          policyId: parseInt(props.policyId)
        }

        // 调用真实API添加受益人
        await policyOrderAPI.addPolicyBeneficiaryInfo(data);

        message.success(t('policyorder.beneficiaryAddSuccess'));
      } else {
        // 编辑单个受益人
        // 调用真实API更新受益人
        await policyOrderAPI.updatePolicyBeneficiaryInfo(formData);

        message.success(t('policyorder.beneficiaryUpdateSuccess'));
      }
    } else {
      // 编辑整个受益人列表
      // 检查受益比例总和是否为100%
      const totalPercentage = formData.beneficiaries.reduce((total, current) => {
        return total + (parseFloat(current.benefitPercentage) || 0);
      }, 0);

      if (Math.abs(totalPercentage - 100) > 0.01) {
        message.error(t('policyorder.benefitPercentageMustBe100'));
        loading.value = false;
        return;
      }

      // 调用真实API更新受益人列表
      await policyOrderAPI.updatePolicyBeneficiaryInfo(formData);

      message.success(t('policyorder.beneficiaryUpdateSuccess'));
    }

    editModalVisible.value = false;

    // 通知父组件更新数据
    emit('update');
  } catch (error) {
    console.error(t('policyorder.updateBeneficiaryFailed'), error);
    message.error(t('policyorder.updateBeneficiaryFailed') + ': ' + (error.message || t('common.errors.serverError')));
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.mac-style-info-panel {
  border-radius: 8px;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-medium);
}

:deep(.mac-style-table-sm .ant-table) {
  border-radius: 6px;
  overflow: hidden;
}
</style>