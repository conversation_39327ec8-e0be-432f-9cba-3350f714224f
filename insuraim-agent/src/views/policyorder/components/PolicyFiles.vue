<template>
  <section class="mb-8">
    <div class="title flex items-center justify-between border-b  ">
      <h3 class="text-lg font-medium mt-0 mb-0">保单文件</h3>
      <a-button type="primary" size="middle" class="mr-2 mb-1" @click="openUploadModal">
        <template #icon>
          <Icon icon="material-symbols:upload-file-outline-rounded" />
        </template>
        上传
      </a-button>
    </div>
    <a-empty v-if="!attachments?.length" description="暂无保单文件" />
    <a-list v-else size="small" bordered class="mac-style-list">
      <a-list-item v-for="file in attachments" :key="file.id">
        <a-list-item-meta>
          <template #title>
            <div class="flex items-center">
              <Icon :icon="getFileIcon(file.fileType)" class="text-xl mr-2" :class="getFileIconColor(file.fileType)" />
              <a :href="file.ossUrl" target="_blank" rel="noopener noreferrer">{{ file.fileName }}</a>
            </div>
          </template>
          <template #description>
            <span>上传时间: {{ formatDate(file.createdAt, true) }}</span>
            <span v-if="file.fileSize" class="ml-2"> ({{ formatFileSize(file.fileSize) }})</span>
          </template>
        </a-list-item-meta>
        <template #actions>
          <a-tooltip title="预览">
            <a-button type="link" size="small" @click="previewFile(file)">
              <template #icon>
                <Icon icon="material-symbols:visibility-outline-rounded" class="text-lg" />
              </template>
            </a-button>
          </a-tooltip>
          <a-tooltip title="下载">
            <a-button type="link" size="small" @click="downloadFile(file)">
              <template #icon>
                <Icon icon="material-symbols:download-rounded" class="text-lg" />
              </template>
            </a-button>
          </a-tooltip>
        </template>
      </a-list-item>
    </a-list>
  </section>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue';
import { Icon } from '@iconify/vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

const props = defineProps({
  attachments: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['upload']);

// 格式化日期
const formatDate = (dateString, includeTime = false) => {
  if (!dateString) return 'N/A';
  const format = includeTime ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD';
  return dayjs(dateString).isValid() ? dayjs(dateString).format(format) : dateString;
};

// 获取文件图标
const getFileIcon = (fileType) => {
  if (fileType?.includes('pdf')) return 'mdi:file-pdf-box';
  if (fileType?.includes('image')) return 'mdi:file-image';
  if (fileType?.includes('word')) return 'mdi:file-word-box';
  if (fileType?.includes('excel') || fileType?.includes('spreadsheetml')) return 'mdi:file-excel-box';
  if (fileType?.includes('presentation')) return 'mdi:file-powerpoint-box';
  return 'mdi:file-document-outline';
};

// 获取文件图标颜色
const getFileIconColor = (fileType) => {
  if (fileType?.includes('pdf')) return 'text-red-500';
  if (fileType?.includes('image')) return 'text-purple-500';
  if (fileType?.includes('word')) return 'text-blue-500';
  if (fileType?.includes('excel') || fileType?.includes('spreadsheetml')) return 'text-green-500';
  if (fileType?.includes('presentation')) return 'text-orange-500';
  return 'text-gray-500';
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 预览文件
const previewFile = (file) => {
  if (file.ossUrl && (file.fileType?.includes('image') || file.fileType?.includes('pdf'))) {
    window.open(file.ossUrl, '_blank');
  } else if (file.ossUrl) {
    message.info('文件类型不支持直接预览，请下载后查看。');
  } else {
    message.error('文件链接无效');
  }
};

// 下载文件
const downloadFile = (file) => {
  if (file.ossUrl) {
    const link = document.createElement('a');
    link.href = file.ossUrl;
    link.setAttribute('download', file.fileName || 'download');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    message.success(`开始下载文件: ${file.fileName}`);
  } else {
    message.error('文件链接无效');
  }
};

// 打开上传模态框
const openUploadModal = () => {
  emit('upload');
};
</script>

<style scoped>
:deep(.mac-style-list) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.mac-style-list .ant-list-item:hover) {
  background-color: #f0f7ff;
}
</style>