<template>
  <div class="flex items-center">

    <span class="mr-2 text-sm">地区</span>
    <a-select v-model:value="filterParams.region" :placeholder="$t('policyorder.region')"
      style="width: 120px; margin-right: 12px;" size="middle" @change="handleRegionChange">
      <a-select-option value="">{{ $t('policyorder.all') }}</a-select-option>
      <a-select-option v-for="item in regionOptions" :key="item.code" :value="item.name">
        {{ $t(`policyorder.${item.i18nKey}`) }}
      </a-select-option>
    </a-select>
    <span class="mr-2 text-sm ">保司</span>
    <a-select v-model:value="filterParams.company" :placeholder="$t('policyorder.insuranceCompany')"
      style="width: 140px; margin-right: 12px;" size="middle" @change="handleCompanyChange">
      <a-select-option value="">{{ $t('policyorder.all') }}</a-select-option>
      <a-select-option v-for="item in companyOptions" :key="item.code" :value="item.name">
        {{ $t(`policyorder.${item.i18nKey}`) }}
      </a-select-option>
    </a-select>
    <span class="mr-2 text-sm">订单状态</span>
    <a-select v-model:value="filterParams.status" :placeholder="$t('policyorder.status')"
      style="width: 120px; margin-right: 12px;" size="middle" @change="handleStatusChange">
      <a-select-option value="">{{ $t('policyorder.all') }}</a-select-option>
      <a-select-option v-for="(text, status) in statusOptions" :key="status" :value="status">
        {{ text }}
      </a-select-option>
    </a-select>
    <span class="mr-2 text-sm">保单状态</span>
    <a-select v-model:value="filterParams.policyStatus" :placeholder="$t('policyorder.pleaseSelectPolicyStatus')"
      style="width: 140px; margin-right: 12px;" size="middle" @change="handlePolicyStatusChange">
      <a-select-option value="">{{ $t('policyorder.all') }}</a-select-option>
      <a-select-option v-for="item in policyStatusOptions" :key="item.value" :value="item.value">
        {{ $t(`policyorder.${item.i18nKey}`) }}
      </a-select-option>
    </a-select>

    <a-input-search :placeholder="$t('policyorder.searchPlaceholder')" style="width: 280px" size="middle"
      @search="searchPolicy" v-model:value="filterParams.search" />
  </div>
</template>

<script setup>
import { ref, reactive, defineProps, defineEmits } from 'vue';
import { useOrderStore, OrderStatusEnum, PolicyStatusEnum } from '@/store/modules/order';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const props = defineProps({
  initialParams: {
    type: Object,
    default: () => ({

      status: '',
      region: '',
      company: '',
      policyStatus: '',
      search: ''
    })
  }
});

const emit = defineEmits(['update:params', 'search', 'statusChange', 'regionChange', 'companyChange', 'policyStatusChange']);

// 使用store中的枚举
const orderStore = useOrderStore();

// 筛选参数
const filterParams = reactive({
  status: props.initialParams.status || '',
  region: props.initialParams.region || '',
  company: props.initialParams.company || '',
  search: props.initialParams.search || '',
  policyStatus: props.initialParams.policyStatus || ''
});

// 状态选项
const statusOptions = {
  [OrderStatusEnum.APPOINTMENT]: t('policyorder.appointment'),
  [OrderStatusEnum.WAIT_PAYMENT]: t('policyorder.waitPayment'),
  [OrderStatusEnum.QUIET_PERIOD]: t('policyorder.quietPeriod'),
  [OrderStatusEnum.IN_EFFECT]: t('policyorder.inEffect'),
  [OrderStatusEnum.EXPIRED]: t('policyorder.expired')
};

// 地区选项列表
const regionOptions = ref([
  { code: 'HK', name: '香港', i18nKey: 'hongKong' },
  { code: 'MO', name: '澳门', i18nKey: 'macau' },
  { code: 'SG', name: '新加坡', i18nKey: 'singapore' },
  // { code: 'CN', name: '中国大陆', i18nKey: 'mainlandChina' },
  { code: 'BM', name: '百慕大', i18nKey: 'bermuda' }
]);

// 保险公司选项
const companyOptions = ref([
  { code: 'CHINALIFE_SG', name: '国寿（新加坡）', i18nKey: 'chinaLifeSG' },
  { code: 'CHINALIFE_HK', name: '国寿（香港）', i18nKey: 'chinaLifeHK' },
  { code: 'AIA_HK', name: '友邦（香港）', i18nKey: 'aiaHK' },
  { code: 'PRUDENTIAL_SG', name: '保诚（新加坡）', i18nKey: 'prudentialSG' },
  { code: 'HSBC_LIFE', name: '汇丰人寿', i18nKey: 'hsbcLife' },
  { code: 'MANULIFE', name: '宏利', i18nKey: 'manulife' },
  { code: 'SUNLIFE', name: '永明', i18nKey: 'sunlife' },
  { code: 'BOC_LIFE', name: '中银人寿', i18nKey: 'bocLife' },
  { code: 'CHINA_TAIPING', name: '中国太平', i18nKey: 'chinaTaiping' }
]);


// 保单状态
// 保单状态选项
const policyStatusOptions = [
  { value: PolicyStatusEnum.APPOINTMENT, label: t('policyorder.appointment'), i18nKey: 'appointment' },
  { value: PolicyStatusEnum.SIGN_APPOINTMENT, label: t('policyorder.signAppointment'), i18nKey: 'signAppointment' },
  { value: PolicyStatusEnum.SIGN, label: t('policyorder.sign'), i18nKey: 'sign' },
  { value: PolicyStatusEnum.PAYMENT_GUIDE, label: t('policyorder.paymentGuide'), i18nKey: 'paymentGuide' },
  { value: PolicyStatusEnum.PAYMENT_RECORD, label: t('policyorder.paymentRecord'), i18nKey: 'paymentRecord' },
  { value: PolicyStatusEnum.EFFECTIVE, label: t('policyorder.effective'), i18nKey: 'effective' },
  { value: PolicyStatusEnum.CONTRACT_SENT, label: t('policyorder.contractSent'), i18nKey: 'contractSent' },
  { value: PolicyStatusEnum.SIGNATURE, label: t('policyorder.signature'), i18nKey: 'signature' },
  { value: PolicyStatusEnum.QUIET_PERIOD_EXPIRED, label: t('policyorder.quietPeriodExpired'), i18nKey: 'quietPeriodExpired' }
];
// 处理状态变化
const handleStatusChange = () => {
  emit('update:params', { ...filterParams });
  emit('statusChange', filterParams.status);
};


// 处理地区变化
const handleRegionChange = () => {

  emit('update:params', { ...filterParams });
  emit('regionChange', filterParams.region);

};

// 处理保险公司变化
const handleCompanyChange = () => {
  emit('update:params', { ...filterParams });
  emit('companyChange', filterParams.company);

};

// 处理保单状态变化
const handlePolicyStatusChange = () => {
  emit('update:params', { ...filterParams });
  emit('policyStatusChange', filterParams.policyStatus);
};

// 搜索保单
const searchPolicy = (value) => {
  console.log('value', value);
  filterParams.search = value;
  emit('update:params', { ...filterParams });
  emit('search', value);

};

// 重置筛选条件
const resetFilters = () => {
  filterParams.status = '';
  filterParams.region = '';
  filterParams.company = '';
  filterParams.search = '';
  filterParams.policyStatus = '';
  emit('update:params', { ...filterParams });
};

// 暴露方法给父组件
defineExpose({
  resetFilters
});
</script>