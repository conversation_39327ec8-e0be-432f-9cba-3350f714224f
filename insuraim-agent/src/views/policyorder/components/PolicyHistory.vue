<template>
  <section class="mb-8">

    <div class="title flex items-center justify-between border-b mb-4 ">
      <h3 class="text-lg font-medium mt-0 mb-1">{{ $t('policyorder.policyHistory') }}</h3>
      <a-tooltip :title="$t('policyorder.editInsuredInfo')">
        <a-button type="primary" size="middle" class="mr-2 mb-1" @click="showAppointmentModal">
          <template #icon>
            <Icon icon="ant-design:edit-outlined" />
          </template>
          查看邀请链接
        </a-button>
      </a-tooltip>
    </div>
    <a-empty v-if="!sortedHistories?.length" :description="$t('policyorder.noHistoryRecords')" />
    <div class="history-timeline-container">
      <a-timeline>
        <a-timeline-item v-for="(item, index) in displayHistories" :key="item.id"
          :color="getHistoryStatusColor(item.recordStatus, item.statusCode)">
          <template #dot>
            <div class="timeline-dot-container ml-1"
              :class="{ 'active-status': item.statusCode === props.currentStatus }">
              <Icon :icon="getStatusIcon(item.statusCode)" class="timeline-icon" />
            </div>
          </template>
          <div class="history-item p-3 rounded-lg border border-gray-100 shadow-sm hover:shadow-md transition-shadow">
            <div class="flex justify-between items-start">
              <p class="font-medium text-base flex items-center">
                <span v-if="index === 0 && item.recordStatus === PolicyRecordStatusEnum.PENDING"
                  class="relative flex h-3 w-3 mr-2">
                  <span
                    class="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
                  <span class="relative inline-flex rounded-full h-3 w-3 bg-red-500"></span>
                </span>
                {{ getPolicyStatusText(item.statusCode) }}
                <a-tag :color="getRecordStatusTagColor(item.recordStatus)" class="ml-2">
                  {{ getRecordStatusText(item.recordStatus) }}
                </a-tag>

              </p>
              <p class="text-xs text-gray-400 whitespace-nowrap">{{ formatTimeAgo(item.updatedAt) }}</p>
            </div>

            <div class="flex items-center justify-between">
              <p class="text-sm text-gray-600 mt-1 flex items-center ">
                <Icon icon="mdi:calendar" class="mr-1" />
                {{ formatDate(item.updatedAt, true) }}
              </p>


            </div>
            <p v-if="item.updateBy" class="text-sm text-gray-500 mt-1 flex items-center">
              <Icon icon="mdi:account" class="mr-1" />
              {{ $t('policyorder.operator') }}：{{ item.updateBy }}
            </p>

            <p v-if="item.remark" class="text-sm text-gray-500 mt-1 flex items-center">
              <Icon icon="mdi:comment-text-outline" class="mr-1" />
              {{ $t('policyorder.remarks') }}：{{ item.remark }}
            </p>

            <!-- 附件列表区域 -->
            <div v-if="item.attachments && item.attachments.length" class="mt-3 border-t pt-2">
              <div class="flex items-center text-sm text-gray-600 mb-2">
                <Icon icon="mdi:attachment" class="mr-1" />
                <span>{{ $t('policyorder.attachments') }} ({{ item.attachments.length }})</span>
                <a-button v-if="item.attachments.length > 1 && !expandedAttachments[item.id]" type="link" size="small"
                  @click="toggleAttachments(item.id)">
                  {{ $t('policyorder.expandAll') }}
                </a-button>
                <a-button v-if="expandedAttachments[item.id]" type="link" size="small"
                  @click="toggleAttachments(item.id)">
                  {{ $t('policyorder.collapse') }}
                </a-button>
              </div>

              <div class="attachment-list">
                <div v-for="(file, fileIndex) in getVisibleAttachments(item)" :key="file.id"
                  class="attachment-item p-2 mb-1 rounded-md hover:bg-gray-50 flex justify-between items-center">
                  <div class="flex items-center flex-1 overflow-hidden">
                    <Icon :icon="getFileIcon(file.fileType)" class="text-xl mr-2"
                      :class="getFileIconColor(file.fileType)" />
                    <div class="truncate flex-1">
                      <div class="text-sm font-medium truncate hover:text-blue-500 cursor-pointer mb-1"
                        @click="previewFile(file)" :title="$t('policyorder.clickToPreview')">{{ file.fileName }}</div>
                      <div class="text-xs text-gray-400">
                        {{ formatDate(file.createdAt) }}
                        <span v-if="file.fileSize" class="ml-1">({{ formatFileSize(file.fileSize) }})</span>
                      </div>
                    </div>
                  </div>
                  <div class="flex">
                    <a :href="file.ossUrl" target="_blank" rel="noopener noreferrer" download>
                      <a-button type="primary" shape="circle" size="small" class="text-xs">
                        <template #icon>
                          <Icon icon="mdi:download-outline" size="14" />
                        </template>
                      </a-button>
                    </a>
                  </div>

                </div>

                <div v-if="item.attachments.length > 3 && !expandedAttachments[item.id]" class="text-center mt-1">
                  <a-button type="link" size="small" @click="toggleAttachments(item.id)">
                    {{ $t('policyorder.showAllAttachments', { count: item.attachments.length }) }}
                    <Icon icon="mdi:chevron-down" class="ml-1" />
                  </a-button>
                </div>
              </div>
            </div>

            <div class="mt-2 text-right absolute right-2 bottom-2" v-if="item.statusCode === currentStatus">

              <a-tooltip :title="$t('policyorder.nextStatusTooltip')">
                <a-button type="primary" size="small" class="mr-2" :loading="confirmLoading"
                  @click="handleConfirmStatus(item)">
                  <template #icon>
                    <Icon icon="ant-design:edit-outlined" />
                  </template>
                  {{ $t('policyorder.confirm') }}
                </a-button>

              </a-tooltip>
              <a-tooltip :title="$t('policyorder.previousStatusTooltip')">
                <a-button type="primary" danger size="small" :loading="rollbackLoading"
                  @click="handleRollbackStatus(item)">
                  <template #icon>
                    <Icon icon="ant-design:delete" />
                  </template>
                  {{ $t('policyorder.rollback') }}
                </a-button>
              </a-tooltip>
            </div>

            <!-- 添加上传文件按钮，对所有状态项都显示 -->
            <div class="mt-2">
              <a-tooltip :title="$t('policyorder.uploadRelatedFileTooltip')">
                <a-button type="primary" size="small" @click="openUploadModal(item.statusCode)">
                  <template #icon>
                    <Icon icon="material-symbols:upload-file" />
                  </template>
                  {{ $t('policyorder.uploadFile') }}
                </a-button>
              </a-tooltip>
            </div>
          </div>
        </a-timeline-item>
      </a-timeline>


    </div>
  </section>

  <!-- 文件上传模态框 -->
  <FileUploadModal v-model:visible="uploadModalVisible" :policy-order-id="props.policyOrderId" :loading="uploadLoading"
    :order-status="currentUploadStatus" @upload="handleFileUpload"
    @upload-complete="(success) => { if (success) uploadModalVisible = false; }" />

  <a-modal v-model:open="open" title="邀请信息" @ok="handleOk" :footer="null" width="500px">
    <div class="invite-modal-content">
      <!-- 二维码 -->
      <div class="flex flex-col items-center mb-4">
        <p class="text-center mb-3">扫描二维码或分享链接，邀请客户填写信息</p>
        <div id="qrcode-container" class="mb-3">
          <a-qrcode :value="inviteCodeUrl" :size="200" id="qrcode" />
        </div>
        <a-button type="primary" size="small" @click="downloadQRCode">
          <template #icon>
            <Icon icon="material-symbols:download" />
          </template>
          下载二维码
        </a-button>
      </div>

      <!-- 链接 -->
      <div class="mb-4">
        <div class="flex items-center bg-gray-50 p-3 rounded-md">
          <div class="flex-1 truncate mr-2">{{ inviteCodeUrl }}</div>
          <a-button type="primary" size="small" @click="copyLink">
            <!-- <template #icon>
                                            <Icon icon="material-symbols:content-copy" />
                                        </template> -->
            复制
          </a-button>
        </div>
      </div>

      <!-- 过期时间 -->
      <div class="text-gray-500 text-sm text-center flex items-center justify-center" v-if="expireTime">
        <Icon icon="material-symbols:timer-outline" class="mr-1" />
        <span>链接有效期至: {{ formatExpireTime(expireTime) }}</span>
      </div>

      <!-- 底部按钮 -->
      <div class="flex justify-center mt-6">
        <a-button @click="closeModal">关闭</a-button>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { defineProps, ref, computed, defineEmits, watch } from 'vue';
import { useOrderStore, PolicyStatusEnum, PolicyRecordStatusEnum } from '@/store/modules/order';
import { policyOrderAPI } from '@/api';
import { formAPI } from '@/api/form';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import 'dayjs/locale/zh-cn';
import { Icon } from '@iconify/vue';
import { message, Modal } from 'ant-design-vue';
import FileUploadModal from './FileUploadModal.vue';
import { useI18n } from 'vue-i18n';

// 配置dayjs
dayjs.extend(relativeTime);
dayjs.locale('zh-cn');

const { t, locale } = useI18n();

// 根据当前语言设置dayjs的locale
watch(() => locale.value, (newLocale) => {
  const localeMap = {
    'zh-CN': 'zh-cn',
    'en-US': 'en',
    'zh-HK': 'zh-hk'
  };
  dayjs.locale(localeMap[newLocale] || 'zh-cn');
}, { immediate: true });

const props = defineProps({
  histories: {
    type: Array,
    default: () => []
  },
  currentStatus: {
    type: String,
    default: ''
  },
  policyOrderId: {
    type: String,
    required: true
  }
});

const emit = defineEmits(['refresh']);

// 按钮加载状态
const confirmLoading = ref(false);
const rollbackLoading = ref(false);

// 文件上传相关状态
const uploadModalVisible = ref(false);
const currentUploadStatus = ref('');
const uploadLoading = ref(false);

// 邀请信息相关状态
const open = ref(false);
const inviteLink = ref('');
const expireTime = ref(null);
const baseUrl = import.meta.env.DEV
  ? (import.meta.env.VITE_BASE_URL || '')
  : (typeof window !== 'undefined' ? window.location.origin : '');

// 邀请链接完整URL
const inviteCodeUrl = computed(() => {
  return baseUrl + inviteLink.value;
});

// 附件展开状态管理
const expandedAttachments = ref({});

// 按时间倒序排列历史记录
const sortedHistories = computed(() => {
  if (!props.histories || props.histories.length === 0) return [];
  return [...props.histories].sort((a, b) => b.createdAt - a.createdAt);
});

// 根据展开状态显示历史记录
const displayHistories = computed(() => {
  return sortedHistories.value;
});

// 历史记录展开状态
const expanded = ref(false);

// 切换展开/收起状态
const toggleExpand = () => {
  expanded.value = !expanded.value;
};

// 获取指定历史记录中的可见附件
const getVisibleAttachments = (item) => {
  if (!item.attachments || !item.attachments.length) return [];
  // 如果已展开或附件数量小于等于3，返回全部附件
  if (expandedAttachments.value[item.id] || item.attachments.length <= 1) {
    return item.attachments;
  }
  // 否则只返回前3个
  return item.attachments.slice(0, 1);
};

// 切换附件展开/收起
const toggleAttachments = (historyId) => {
  expandedAttachments.value = {
    ...expandedAttachments.value,
    [historyId]: !expandedAttachments.value[historyId]
  };
};

// 获取文件图标
const getFileIcon = (fileType) => {
  if (!fileType) return 'mdi:file-outline';

  const type = (typeof fileType === 'string') ? fileType.toLowerCase() : '';

  if (type.includes('pdf')) return 'mdi:file-pdf-box';
  if (type.includes('image') || ['jpg', 'jpeg', 'png', 'gif'].some(ext => type.includes(ext)))
    return 'mdi:file-image';
  if (type.includes('word') || type.includes('doc')) return 'mdi:file-word-box';
  if (type.includes('excel') || type.includes('spreadsheet') || type.includes('xls'))
    return 'mdi:file-excel-box';
  if (type.includes('presentation') || type.includes('powerpoint') || type.includes('ppt'))
    return 'mdi:file-powerpoint-box';
  if (type.includes('text') || type.includes('txt')) return 'mdi:file-text-outline';
  if (type.includes('zip') || type.includes('rar') || type.includes('compressed'))
    return 'mdi:file-zip-outline';

  return 'mdi:file-document-outline';
};

// 获取文件图标颜色
const getFileIconColor = (fileType) => {
  if (!fileType) return 'text-gray-500';

  const type = (typeof fileType === 'string') ? fileType.toLowerCase() : '';

  if (type.includes('pdf')) return 'text-red-500';
  if (type.includes('image') || ['jpg', 'jpeg', 'png', 'gif'].some(ext => type.includes(ext)))
    return 'text-purple-500';
  if (type.includes('word') || type.includes('doc')) return 'text-blue-500';
  if (type.includes('excel') || type.includes('spreadsheet') || type.includes('xls'))
    return 'text-green-500';
  if (type.includes('presentation') || type.includes('powerpoint') || type.includes('ppt'))
    return 'text-orange-500';
  if (type.includes('text') || type.includes('txt')) return 'text-gray-600';
  if (type.includes('zip') || type.includes('rar') || type.includes('compressed'))
    return 'text-amber-500';

  return 'text-gray-500';
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 预览文件
const previewFile = (file) => {
  if (!file.ossUrl) {
    message.error(t('policyorder.invalidFileLink'));
    return;
  }

  const fileType = (typeof file.fileType === 'string') ? file.fileType.toLowerCase() : '';

  // 判断是否为可直接预览的文件类型
  const isPdf = fileType.includes('pdf');
  const isImage = fileType.includes('image') || ['jpg', 'jpeg', 'png', 'gif'].some(ext => fileType.includes(ext));

  if (isPdf || isImage) {
    window.open(file.ossUrl, '_blank');
    return;
  }

  // 如果不是可预览的类型，提示下载
  Modal.confirm({
    title: t('policyorder.filePreviewTip'),
    content: t('policyorder.filePreviewNotSupported', { type: file.fileType || t('policyorder.unknown') }),
    okText: t('policyorder.downloadFile'),
    cancelText: t('policyorder.cancel'),
    onOk: () => downloadFile(file)
  });
};

// 下载文件
const downloadFile = (file) => {
  if (!file.ossUrl) {
    message.error(t('policyorder.invalidFileLink'));
    return;
  }

  try {
    // 在新标签页中打开文件链接
    const newWindow = window.open(file.ossUrl, '_blank');

    // 如果被浏览器阻止了弹出窗口
    if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
      message.warning(t('policyorder.browserBlockedPopup'));
      // 提供备用信息
      Modal.info({
        title: t('policyorder.downloadTip'),
        content: t('policyorder.downloadAlternative', { url: file.ossUrl }),
        okText: t('policyorder.iKnow')
      });
    } else {
      message.success(t('policyorder.fileWillOpenInNewTab', { name: file.fileName }));
    }
  } catch (error) {
    console.error(t('policyorder.fileDownloadError'), error);
    message.error(t('policyorder.fileDownloadFailed'));
  }
};

// 使用store中的枚举
const orderStore = useOrderStore();

// 处理确认状态
const handleConfirmStatus = (item) => {
  Modal.confirm({
    title: t('policyorder.confirmOperation'),
    content: t('policyorder.confirmNextStatus', { status: getPolicyStatusText(item.statusCode) }),
    okText: t('policyorder.confirm'),
    cancelText: t('policyorder.cancel'),
    onOk: async () => {
      try {
        confirmLoading.value = true;
        await policyOrderAPI.nextPolicyStatus({
          policyId: parseInt(props.policyOrderId),
        });
        message.success(t('policyorder.statusUpdateSuccess'));
        emit('refresh');
      } catch (error) {
        console.error(t('policyorder.statusUpdateFailed'), error);
        message.error(t('policyorder.statusUpdateFailed') + ': ' + (error.message || t('common.errors.serverError')));
      } finally {
        confirmLoading.value = false;
      }
    }
  });
};

// 处理撤回状态
const handleRollbackStatus = (item) => {
  Modal.confirm({
    title: t('policyorder.confirmRollback'),
    content: t('policyorder.confirmRollbackStatus', { status: getPolicyStatusText(item.statusCode) }),
    okText: t('policyorder.confirm'),
    okType: 'danger',
    cancelText: t('policyorder.cancel'),
    onOk: async () => {
      try {
        rollbackLoading.value = true;
        await policyOrderAPI.prePolicyStatus({
          policyId: parseInt(props.policyOrderId),
        });
        message.success(t('policyorder.statusRollbackSuccess'));
        emit('refresh');
      } catch (error) {
        console.error(t('policyorder.statusRollbackFailed'), error);
        message.error(t('policyorder.statusRollbackFailed') + ': ' + (error.message || t('common.errors.serverError')));
      } finally {
        rollbackLoading.value = false;
      }
    }
  });
};

// 格式化日期
const formatDate = (dateString, includeTime = false) => {
  if (!dateString) return 'N/A';
  const format = includeTime ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD';
  return dayjs(dateString).isValid() ? dayjs(dateString).format(format) : dateString;
};

// 处理模态框确认
const handleOk = () => {
  open.value = false;
};

// 关闭邀请信息模态框
const closeModal = () => {
  open.value = false;
};

// 复制链接到剪贴板
const copyLink = () => {
  navigator.clipboard.writeText(inviteCodeUrl.value)
    .then(() => {
      message.success('链接已复制到剪贴板');
    })
    .catch(() => {
      message.error('复制失败，请手动复制');
    });
};

// 下载二维码
const downloadQRCode = () => {
  const qrcodeContainer = document.getElementById('qrcode');
  if (!qrcodeContainer) {
    message.error('二维码元素不存在');
    return;
  }

  // 查找二维码容器内的SVG或Canvas元素
  const svgElement = qrcodeContainer.querySelector('svg');
  const canvasElement = qrcodeContainer.querySelector('canvas');

  if (svgElement) {
    // 如果是SVG元素，使用SVG转换为图片
    const svgData = new XMLSerializer().serializeToString(svgElement);
    const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
    const svgUrl = URL.createObjectURL(svgBlob);

    const img = new Image();
    img.onload = () => {
      const canvas = document.createElement('canvas');
      canvas.width = svgElement.width.baseVal.value;
      canvas.height = svgElement.height.baseVal.value;

      const ctx = canvas.getContext('2d');
      ctx.drawImage(img, 0, 0);

      try {
        const url = canvas.toDataURL('image/png');
        const link = document.createElement('a');
        link.download = '邀请二维码.png';
        link.href = url;
        link.click();

        message.success('二维码已下载');
      } catch (error) {
        message.error('下载二维码失败，请稍后重试');
        console.error('下载二维码失败:', error);
      }

      URL.revokeObjectURL(svgUrl);
    };

    img.src = svgUrl;
  } else if (canvasElement) {
    // 如果是Canvas元素，直接使用
    try {
      const url = canvasElement.toDataURL('image/png');
      const link = document.createElement('a');
      link.download = '邀请二维码.png';
      link.href = url;
      link.click();

      message.success('二维码已下载');
    } catch (error) {
      message.error('下载二维码失败，请稍后重试');
      console.error('下载二维码失败:', error);
    }
  } else {
    // 如果都找不到，使用更简单的方法：创建一个新的二维码图像
    message.info('正在生成二维码图片，请稍候...');

    // 创建一个临时的img元素来保存二维码
    const img = new Image();
    img.crossOrigin = 'anonymous';
    img.onload = function () {
      // 创建canvas
      const canvas = document.createElement('canvas');
      canvas.width = img.width;
      canvas.height = img.height;

      // 绘制图像
      const ctx = canvas.getContext('2d');
      ctx.drawImage(img, 0, 0);

      // 导出为图片并下载
      try {
        const url = canvas.toDataURL('image/png');
        const link = document.createElement('a');
        link.download = '邀请二维码.png';
        link.href = url;
        link.click();

        message.success('二维码已下载');
      } catch (error) {
        message.error('下载二维码失败，请稍后重试');
        console.error('下载二维码失败:', error);
      }
    };

    // 设置图像源为当前页面的二维码
    // 这里我们直接使用一个新的QR code API生成图像URL
    img.src = `https://api.qrserver.com/v1/create-qr-code/?data=${encodeURIComponent(inviteCodeUrl.value)}&size=200x200`;

    img.onerror = function () {
      message.error('生成二维码图片失败，请稍后重试');
    };
  }
};

// 格式化过期时间
const formatExpireTime = (timestamp) => {
  const date = new Date(timestamp);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
};

// 格式化相对时间
const formatTimeAgo = (dateString) => {
  if (!dateString) return '';
  return dayjs(dateString).fromNow();
};

// 获取保单状态文本
const getPolicyStatusText = (statusCode) => {
  const statusMap = {
    [PolicyStatusEnum.APPOINTMENT]: t('policyorder.appointment'),
    [PolicyStatusEnum.SIGN_APPOINTMENT]: t('policyorder.signAppointment'),
    [PolicyStatusEnum.SIGN]: t('policyorder.sign'),
    [PolicyStatusEnum.PAYMENT_GUIDE]: t('policyorder.paymentGuide'),
    [PolicyStatusEnum.PAYMENT_RECORD]: t('policyorder.paymentRecord'),
    [PolicyStatusEnum.EFFECTIVE]: t('policyorder.effective'),
    [PolicyStatusEnum.CONTRACT_SENT]: t('policyorder.contractSent'),
    [PolicyStatusEnum.SIGNATURE]: t('policyorder.signature'),
    [PolicyStatusEnum.QUIET_PERIOD_EXPIRED]: t('policyorder.quietPeriodExpired')
  };
  return statusMap[statusCode] || statusCode;
};

// 获取记录状态文本
const getRecordStatusText = (recordStatus) => {
  const statusMap = {
    [PolicyRecordStatusEnum.COMPLETED]: t('policyorder.completed'),
    [PolicyRecordStatusEnum.PENDING_COMPLETION]: t('policyorder.pendingCompletion'),
    [PolicyRecordStatusEnum.PENDING]: t('policyorder.pending')
  };
  return statusMap[recordStatus] || recordStatus;
};

// 获取记录状态标签颜色
const getRecordStatusTagColor = (recordStatus) => {
  const colorMap = {
    [PolicyRecordStatusEnum.COMPLETED]: 'success',
    [PolicyRecordStatusEnum.PENDING_COMPLETION]: 'warning',
    [PolicyRecordStatusEnum.PENDING]: 'processing'
  };
  return colorMap[recordStatus] || 'default';
};

// 获取历史状态颜色
const getHistoryStatusColor = (recordStatus, statusCode) => {
  // 如果不是当前状态，返回灰色
  if (statusCode !== props.currentStatus) {
    return 'gray';
  }

  // 是当前状态，根据记录状态设置时间线颜色
  const colorMap = {
    [PolicyRecordStatusEnum.COMPLETED]: 'green',
    [PolicyRecordStatusEnum.PENDING_COMPLETION]: 'blue',
    [PolicyRecordStatusEnum.PENDING]: 'red'
  };
  return colorMap[recordStatus] || 'gray';
};

// 获取状态图标
const getStatusIcon = (statusCode) => {
  const iconMap = {
    [PolicyStatusEnum.APPOINTMENT]: 'mdi:calendar-clock',
    [PolicyStatusEnum.SIGN_APPOINTMENT]: 'mdi:calendar-check',
    [PolicyStatusEnum.SIGN]: 'mdi:file-sign',
    [PolicyStatusEnum.PAYMENT_GUIDE]: 'mdi:cash',
    [PolicyStatusEnum.PAYMENT_RECORD]: 'mdi:receipt',
    [PolicyStatusEnum.EFFECTIVE]: 'mdi:check-circle',
    [PolicyStatusEnum.CONTRACT_SENT]: 'mdi:email-send',
    [PolicyStatusEnum.SIGNATURE]: 'mdi:pen',
    [PolicyStatusEnum.QUIET_PERIOD_EXPIRED]: 'mdi:timer-sand-complete'
  };
  return iconMap[statusCode] || 'mdi:information';
};

// 打开文件上传模态框
const openUploadModal = (statusCode) => {
  currentUploadStatus.value = statusCode;
  uploadModalVisible.value = true;
};

// 显示邀请信息模态框
const showAppointmentModal = async () => {
  try {
    // 获取邀请链接信息
    const res = await formAPI.getFormInvite(props.policyOrderId);
    inviteLink.value = res.inviteLink;
    expireTime.value = res.expireTime;
    open.value = true; // 打开邀请链接弹窗
  } catch (error) {
    console.error('获取邀请链接失败:', error);
    message.error('没有邀请信息');
  }
};

// 处理文件上传
const handleFileUpload = async (formData) => {
  try {
    uploadLoading.value = true;
    await policyOrderAPI.uploadAttachment(formData);
    message.success(t('policyorder.fileUploadSuccess'));
    // 通知文件上传成功，可以关闭模态框
    uploadModalVisible.value = false;
    emit('refresh');
  } catch (error) {
    console.error(t('policyorder.fileUploadFailed'), error);
    message.error(t('policyorder.fileUploadFailed') + ': ' + (error.message || t('common.errors.serverError')));
    // 通知上传失败，但不自动关闭模态框
  } finally {
    uploadLoading.value = false;
  }
};
</script>

<style scoped>
.history-timeline-container {
  transition: max-height 0.3s ease;
}

.timeline-dot-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #f0f0f0;
  transition: all 0.3s ease;
  opacity: 0.6;
}

.timeline-dot-container.active-status {
  background-color: #e6f7ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  opacity: 1;
}

.timeline-icon {
  font-size: 18px;
  color: #8c8c8c;
}

.active-status .timeline-icon {
  color: #1890ff;
}

.history-item {
  background-color: var(--bg-secondary);
  transition: all 0.3s ease;
  position: relative;
  padding-bottom: 10px;
  /* 为底部按钮预留更多空间 */
}

/* 附件列表样式 */
.attachment-list {
  max-width: 100%;
}

.attachment-item {
  border: 1px solid var(--border-medium);
  transition: all 0.2s ease;
}

.attachment-item:hover {
  border-color: #d9d9d9;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* 文件图标样式 */
.attachment-item .iconify {
  font-size: 1.25rem;
}

/* 展开/收起按钮样式 */
.attachment-list .ant-btn-link {
  padding: 0 4px;
  height: auto;
}

/* 针对不同文件类型的颜色 */
.text-red-500 {
  color: #f5222d;
}

.text-blue-500 {
  color: #1890ff;
}

.text-green-500 {
  color: #52c41a;
}

.text-purple-500 {
  color: #722ed1;
}

.text-orange-500 {
  color: #fa8c16;
}

.text-amber-500 {
  color: #faad14;
}

.text-gray-500 {
  color: #8c8c8c;
}

.text-gray-600 {
  color: #595959;
}

:deep(.ant-timeline-item-content) {
  padding-bottom: 0px;
  margin-left: 30px;
}

:deep(.ant-timeline-item-head) {
  width: 16px;
  height: 16px;
}

:deep(.ant-timeline-item-head-custom) {
  width: auto;
  height: auto;
  margin-top: 0;
  padding: 0;
  top: 10px;
  left: -0px;
}
</style>