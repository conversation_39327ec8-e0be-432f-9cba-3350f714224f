<template>
    <section class="mb-8">
        <div class="title flex items-center justify-between border-b  ">
            <h3 class="text-lg font-medium mt-0 mb-0">{{ $t('policyorder.insuredInfo') }}</h3>
            <a-tooltip :title="$t('policyorder.editInsuredInfo')">
                <a-button type="primary" size="middle" class="mr-2 mb-1" @click="showEditModal">
                    <template #icon>
                        <Icon icon="ant-design:edit-outlined" />
                    </template>
                    {{ $t('policyorder.edit') }}
                </a-button>
            </a-tooltip>
        </div>
        <div v-if="!insured" class="text-center py-4 text-gray-500">
            <a-empty :description="$t('common.noData')" />
        </div>
        <div v-else class="space-y-4 bg-gray-50 p-4 rounded-lg mac-style-info-panel">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-3">
                <div class="flex items-start">
                    <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.name') }}：</span>
                    <span class="text-base font-medium break-words">{{ insured?.nameCn }}
                        <span v-if="insured?.nameEn" class="text-xs text-gray-500 italic ml-1">
                            ({{ insured?.nameEn }})
                        </span>
                    </span>
                </div>
                <div class="flex items-start">
                    <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.gender') }}：</span>
                    <span class="text-base break-words">{{ formatGender(insured?.gender) }}</span>
                </div>
                <div class="flex items-start">
                    <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.birthdate') }}：</span>
                    <span class="text-base break-words">{{ formatDate(insured?.birthDate) }}</span>
                </div>
                <div class="flex items-start">
                    <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.idNumber') }}：</span>
                    <span class="text-base break-words">{{ insured?.idCardNo }}</span>
                </div>
                <div class="flex items-start">
                    <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.travelPermitNo')
                        }}：</span>
                    <span class="text-base break-words">{{ insured?.travelPermitNo || 'N/A' }}</span>
                </div>
                <div class="flex items-start">
                    <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.phoneNumber') }}：</span>
                    <span class="text-base break-words">{{ insured?.mobile }}</span>
                </div>
                <div class="flex items-start">
                    <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.email') }}：</span>
                    <span class="text-base break-words">{{ insured?.email }}</span>
                </div>
                <div class="flex items-start">
                    <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.nationality') }}：</span>
                    <span class="text-base break-words">{{ insured?.nationality }}</span>
                </div>
                <div class="flex items-start">
                    <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.maritalStatus') }}：</span>
                    <span class="text-base break-words">{{ insured?.maritalStatus }}</span>
                </div>
                <div class="flex items-start">
                    <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.educationLevel')
                        }}：</span>
                    <span class="text-base break-words">{{ insured?.educationLevel }}</span>
                </div>
                <div class="flex items-start">
                    <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.height') }}：</span>
                    <span class="text-base break-words">{{ insured?.height }} cm</span>
                </div>
                <div class="flex items-start">
                    <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.weight') }}：</span>
                    <span class="text-base break-words">{{ insured?.weight }} kg</span>
                </div>
                <div class="flex items-start">
                    <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.isSmoker') }}：</span>
                    <span class="text-base break-words">{{ insured?.isSmoker === 1 ? $t('policyorder.yes') :
                        $t('policyorder.no') }}</span>
                </div>
                <div class="flex items-start">
                    <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.idCardAddress') }}：</span>
                    <span class="text-base break-words">{{ insured?.idCardAddress }}</span>
                </div>
            </div>
        </div>

        <!-- 编辑信息Modal -->
        <edit-info-modal v-model:visible="editModalVisible" :title="$t('policyorder.editInsuredInfo')"
            :form-fields="formFields" :initial-values="initialFormValues" :loading="loading" @submit="handleSubmit" />
    </section>
</template>

<script setup>
import { defineProps, defineEmits, ref, computed } from 'vue';
import dayjs from 'dayjs';
import { message } from 'ant-design-vue';
import { Icon } from '@iconify/vue';
import EditInfoModal from './EditInfoModal.vue';
import { useI18n } from 'vue-i18n';
import { policyOrderAPI } from '@/api';

const { t } = useI18n();

const props = defineProps({
    insured: {
        type: Object,
        default: () => ({})
    }
});

const emit = defineEmits(['update']);

// 编辑模态框状态
const editModalVisible = ref(false);
const loading = ref(false);

// 格式化日期
const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return dayjs(dateString).isValid() ? dayjs(dateString).format('YYYY-MM-DD') : dateString;
};

// 格式化性别
const formatGender = (gender) => {
    if (gender === 1) return t('policyorder.male');
    if (gender === 0) return t('policyorder.female');
    return 'N/A';
};

// 婚姻状况选项
const maritalStatusOptions = [
    { value: t('policyorder.single'), label: t('policyorder.single') },
    { value: t('policyorder.married'), label: t('policyorder.married') },
    { value: t('policyorder.divorced'), label: t('policyorder.divorced') },
    { value: t('policyorder.widowed'), label: t('policyorder.widowed') }
];

// 教育程度选项
const educationOptions = [
    { value: t('policyorder.primarySchool'), label: t('policyorder.primarySchool') },
    { value: t('policyorder.middleSchool'), label: t('policyorder.middleSchool') },
    { value: t('policyorder.highSchool'), label: t('policyorder.highSchool') },
    { value: t('policyorder.college'), label: t('policyorder.college') },
    { value: t('policyorder.bachelor'), label: t('policyorder.bachelor') },
    { value: t('policyorder.master'), label: t('policyorder.master') },
    { value: t('policyorder.doctor'), label: t('policyorder.doctor') }
];

// 表单字段配置
const formFields = [
    {
        label: t('policyorder.nameCn'),
        name: 'nameCn',
        type: 'input',
        rules: [{ required: true, message: t('policyorder.pleaseEnterName') }]
    },
    {
        label: t('policyorder.nameEn'),
        name: 'nameEn',
        type: 'input',
        rules: [{ required: false, message: t('policyorder.pleaseEnterNameEn') }]
    },
    {
        label: t('policyorder.gender'),
        name: 'gender',
        type: 'radio',
        options: [
            { value: 1, label: t('policyorder.male') },
            { value: 0, label: t('policyorder.female') }
        ],
        rules: [{ required: true, message: t('policyorder.pleaseSelectGender') }]
    },
    {
        label: t('policyorder.birthdate'),
        name: 'birthDate',
        type: 'date',
        valueFormat: 'timestamp',
        rules: [{ required: true, message: t('policyorder.pleaseSelectBirthdate') }]
    },
    {
        label: t('policyorder.idNumber'),
        name: 'idCardNo',
        type: 'input',
        rules: [
            { required: true, message: t('policyorder.pleaseEnterIdNumber') },
            { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: t('policyorder.pleaseEnterValidIdNumber') }
        ]
    },
    {
        label: t('policyorder.travelPermitNo'),
        name: 'travelPermitNo',
        type: 'input',
        rules: [{ required: false, message: t('policyorder.pleaseEnterTravelPermitNo') }]
    },
    {
        label: t('policyorder.phoneNumber'),
        name: 'mobile',
        type: 'input',
        rules: [
            { required: true, message: t('policyorder.pleaseEnterPhoneNumber') },
            { pattern: /^1[3-9]\d{9}$/, message: t('policyorder.pleaseEnterValidPhoneNumber') }
        ]
    },
    {
        label: t('policyorder.email'),
        name: 'email',
        type: 'input',
        rules: [
            { required: true, message: t('policyorder.pleaseEnterEmail') },
            { type: 'email', message: t('policyorder.pleaseEnterValidEmail') }
        ]
    },
    {
        label: t('policyorder.nationality'),
        name: 'nationality',
        type: 'input',
        rules: [{ required: true, message: t('policyorder.pleaseEnterNationality') }]
    },
    {
        label: t('policyorder.maritalStatus'),
        name: 'maritalStatus',
        type: 'select',
        options: maritalStatusOptions,
        rules: [{ required: true, message: t('policyorder.pleaseSelectMaritalStatus') }]
    },
    {
        label: t('policyorder.educationLevel'),
        name: 'educationLevel',
        type: 'select',
        options: educationOptions,
        rules: [{ required: true, message: t('policyorder.pleaseSelectEducationLevel') }]
    },
    {
        label: t('policyorder.height') + '(cm)',
        name: 'height',
        type: 'number',
        min: 0,
        max: 300,
        precision: 2,
        rules: [{ required: true, message: t('policyorder.pleaseEnterHeight') }]
    },
    {
        label: t('policyorder.weight') + '(kg)',
        name: 'weight',
        type: 'number',
        min: 0,
        max: 500,
        precision: 2,
        rules: [{ required: true, message: t('policyorder.pleaseEnterWeight') }]
    },
    {
        label: t('policyorder.isSmoker'),
        name: 'isSmoker',
        type: 'radio',
        options: [
            { value: 1, label: t('policyorder.yes') },
            { value: 0, label: t('policyorder.no') }
        ],
        rules: [{ required: true, message: t('policyorder.pleaseSelectIsSmoker') }]
    },
    {
        label: t('policyorder.idCardAddress'),
        name: 'idCardAddress',
        type: 'input',
        rules: [{ required: true, message: t('policyorder.pleaseEnterIdCardAddress') }]
    }
];

// 初始表单值
const initialFormValues = computed(() => {
    if (!props.insured) return {};
    return { ...props.insured };
});

// 显示编辑模态框
const showEditModal = () => {
    editModalVisible.value = true;
};

// 处理表单提交
const handleSubmit = async (formData) => {
    loading.value = true;
    try {
        // 调用真实API更新数据
        await policyOrderAPI.updatePolicyInsuredInfo(formData);

        message.success(t('policyorder.insuredInfoUpdateSuccess'));
        editModalVisible.value = false;

        // 通知父组件更新数据
        emit('update');
    } catch (error) {
        console.error(t('policyorder.updateInsuredInfoFailed'), error);
        message.error(t('policyorder.updateInsuredInfoFailed') + ': ' + (error.message || t('common.errors.serverError')));
    } finally {
        loading.value = false;
    }
};
</script>

<style scoped>
.mac-style-info-panel {
    border-radius: 8px;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-medium);
}
</style>