<template>
  <section class="mb-8">
    <div class="flex justify-between items-center mb-4 border-b pb-2">
      <h3 class="text-lg font-medium">{{ $t('policyorder.paymentRecords') }}</h3>
      <a-button type="primary" size="middle" @click="openAddPaymentModal">
        <template #icon>
          <Icon icon="material-symbols:add-card-outline-rounded" />
        </template>
        {{ $t('policyorder.addPayment') }}
      </a-button>
    </div>
    <a-empty v-if="!payments?.length" :description="$t('policyorder.noPaymentRecords')" />
    <a-table v-else :dataSource="payments" :columns="paymentColumns" :pagination="false" size="small"
      :rowClassName="(record, index) => (index % 2 === 1 ? 'table-striped' : '')" class="mac-style-table-sm">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'paymentDate'">
          <span>{{ formatDate(record.paymentDate) }}</span>
        </template>
        <template v-if="column.dataIndex === 'amount'">
          <span>{{ record.amount }} {{ record.currency }}</span>
        </template>
        <template v-if="column.dataIndex === 'paymentMethod'">
          <span>{{ getPaymentMethodText(record.paymentMethod) }}</span>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <div class="flex space-x-2">
            <a-tooltip :title="$t('policyorder.edit')">
              <a-button type="link" size="small" @click="editPayment(record)">
                <template #icon>
                  <Icon icon="material-symbols:edit-outline" class="text-lg" />
                </template>
              </a-button>
            </a-tooltip>
            <a-tooltip :title="$t('policyorder.delete')">
              <a-popconfirm :title="$t('policyorder.confirmDeletePayment')" :ok-text="$t('policyorder.confirm')" :cancel-text="$t('policyorder.cancel')" @confirm="deletePayment(record)">
                <a-button type="link" size="small" danger>
                  <template #icon>
                    <Icon icon="material-symbols:delete-outline" class="text-lg" />
                  </template>
                </a-button>
              </a-popconfirm>
            </a-tooltip>
          </div>
        </template>
      </template>
    </a-table>
  </section>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue';
import { Icon } from '@iconify/vue';
import dayjs from 'dayjs';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const props = defineProps({
  payments: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['add', 'edit', 'delete']);

// 表格列定义
const paymentColumns = [
  { title: t('policyorder.paymentDate'), dataIndex: 'paymentDate', key: 'paymentDate', width: 120 },
  { title: t('policyorder.paymentAmount'), dataIndex: 'amount', key: 'amount', width: 100 },
  { title: t('policyorder.paymentMethod'), dataIndex: 'paymentMethod', key: 'paymentMethod', width: 120 },
  { title: t('policyorder.remarks'), dataIndex: 'remark', key: 'remark', ellipsis: true },
  { title: t('policyorder.action'), dataIndex: 'action', key: 'action', width: 100, fixed: 'right' }
];

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  return dayjs(dateString).isValid() ? dayjs(dateString).format('YYYY-MM-DD') : dateString;
};

// 获取支付方式文本
const getPaymentMethodText = (method) => {
  const methodMap = {
    'BANK_TRANSFER': t('policyorder.bankTransfer'),
    'CREDIT_CARD': t('policyorder.creditCard'),
    'CHEQUE': t('policyorder.cheque'),
    'CASH': t('policyorder.cash'),
    'OTHER': t('policyorder.other')
  };
  return methodMap[method] || method;
};

// 打开添加缴费记录模态框
const openAddPaymentModal = () => {
  emit('add');
};

// 编辑缴费记录
const editPayment = (record) => {
  emit('edit', record);
};

// 删除缴费记录
const deletePayment = (record) => {
  emit('delete', record);
};
</script>

<style scoped>
:deep(.mac-style-table-sm .ant-table) {
  border-radius: 6px;
  overflow: hidden;
}

:deep(.table-striped) {
  background-color: var(--bg-tertiary);
}
</style>