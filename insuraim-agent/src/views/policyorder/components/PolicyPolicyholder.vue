<template>
  <section class="mb-8">
    <div class="title flex items-center justify-between border-b  ">
      <h3 class="text-lg font-medium mt-0 mb-0">{{ $t('policyorder.policyholderInfo') }}</h3>
      <a-tooltip :title="$t('policyorder.editPolicyholderInfo')">
        <a-button type="primary" size="middle" class="mr-2 mb-1" @click="showEditModal">
          <template #icon>
            <Icon icon="ant-design:edit-outlined" />
          </template>
          {{ $t('policyorder.edit') }}
        </a-button>
      </a-tooltip>
    </div>
    <div v-if="!policyholder" class="text-center py-4 text-gray-500">
      <a-empty :description="$t('common.noData')" />
    </div>
    <div v-else class="space-y-4 bg-gray-50 p-4 rounded-lg mac-style-info-panel">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-3">
        <div class="flex items-start">
          <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.name') }}：</span>
          <span class="text-base font-medium break-words">{{ policyholder?.nameCn }}
            <span v-if="policyholder?.nameEn" class="text-xs text-gray-500 italic ml-1">
              ({{ policyholder?.nameEn }})
            </span>
          </span>
        </div>
        <div class="flex items-start">
          <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.gender') }}：</span>
          <span class="text-base break-words">{{ formatGender(policyholder?.gender) }}</span>
        </div>
        <div class="flex items-start">
          <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.birthdate') }}：</span>
          <span class="text-base break-words">{{ formatDate(policyholder?.birthDate) }}</span>
        </div>
        <div class="flex items-start">
          <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.idNumber') }}：</span>
          <span class="text-base break-words">{{ policyholder?.idCardNo }}</span>
        </div>
        <div class="flex items-start">
          <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.travelPermitNo') }}：</span>
          <span class="text-base break-words">{{ policyholder?.travelPermitNo || 'N/A' }}</span>
        </div>
        <div class="flex items-start">
          <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.phoneNumber') }}：</span>
          <span class="text-base break-words">{{ policyholder?.mobile }}</span>
        </div>
        <div class="flex items-start">
          <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.email') }}：</span>
          <span class="text-base break-words">{{ policyholder?.email }}</span>
        </div>
        <div class="flex items-start">
          <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.nationality') }}：</span>
          <span class="text-base break-words">{{ policyholder?.nationality }}</span>
        </div>
        <div class="flex items-start">
          <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.birthPlace') }}：</span>
          <span class="text-base break-words">{{ policyholder?.birthPlace || 'N/A' }}</span>
        </div>
        <div class="flex items-start">
          <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.maritalStatus') }}：</span>
          <span class="text-base break-words">{{ policyholder?.maritalStatus }}</span>
        </div>
        <div class="flex items-start">
          <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.educationLevel') }}：</span>
          <span class="text-base break-words">{{ policyholder?.educationLevel }}</span>
        </div>
        <div class="flex items-start">
          <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.height') }}：</span>
          <span class="text-base break-words">{{ policyholder?.height }} cm</span>
        </div>
        <div class="flex items-start">
          <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.weight') }}：</span>
          <span class="text-base break-words">{{ policyholder?.weight }} kg</span>
        </div>
        <div class="flex items-start">
          <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.isSmoker') }}：</span>
          <span class="text-base break-words">{{ policyholder?.isSmoker === 1 ? $t('policyorder.yes') :
            $t('policyorder.no')
            }}</span>
        </div>
      </div>

      <div class="mt-4 pt-4 border-t border-gray-200">
        <h4 class="text-md font-medium mb-3">{{ $t('policyorder.addressInfo') }}</h4>
        <div class="grid grid-cols-1 gap-y-3">
          <div class="flex items-start">
            <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.idCardAddress') }}：</span>
            <span class="text-base break-words">{{ policyholder?.idCardAddress }}</span>
          </div>
          <div class="flex items-start">
            <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.residentialAddress') }}：</span>
            <span class="text-base break-words">{{ policyholder?.residentialAddress }}</span>
          </div>
          <div class="flex items-start">
            <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.mailingAddress') }}：</span>
            <span class="text-base break-words">{{ policyholder?.mailingAddress }}</span>
          </div>
        </div>
      </div>

      <div class="mt-4 pt-4 border-t border-gray-200">
        <h4 class="text-md font-medium mb-3">{{ $t('policyorder.occupationInfo') }}</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-3">
          <div class="flex items-start">
            <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.companyName') }}：</span>
            <span class="text-base break-words">{{ policyholder?.companyNameCn }}
              <span v-if="policyholder?.companyNameEn" class="text-xs text-gray-500 italic ml-1">
                ({{ policyholder?.companyNameEn }})
              </span>
            </span>
          </div>
          <div class="flex items-start">
            <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.companyAddress') }}：</span>
            <span class="text-base break-words">{{ policyholder?.companyAddress }}</span>
          </div>
          <div class="flex items-start">
            <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.companyIndustry') }}：</span>
            <span class="text-base break-words">{{ policyholder?.companyIndustry }}</span>
          </div>
          <div class="flex items-start">
            <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.position') }}：</span>
            <span class="text-base break-words">{{ policyholder?.position }}</span>
          </div>
          <div class="flex items-start">
            <span class="text-sm text-gray-600 w-28 shrink-0 mr-2">{{ $t('policyorder.annualIncome') }}：</span>
            <span class="text-base break-words">{{ formatCurrency(policyholder?.annualIncome) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑信息Modal -->
    <edit-info-modal v-model:visible="editModalVisible" :title="$t('policyorder.editPolicyholderInfo')"
      :form-fields="formFields" :initial-values="initialFormValues" :loading="loading" @submit="handleSubmit" />
  </section>
</template>

<script setup>
import { defineProps, defineEmits, ref, computed } from 'vue';
import dayjs from 'dayjs';
import { message } from 'ant-design-vue';
import { Icon } from '@iconify/vue';
import EditInfoModal from './EditInfoModal.vue';
import { useI18n } from 'vue-i18n';
import { policyOrderAPI } from '@/api';

const { t } = useI18n();

const props = defineProps({
  policyholder: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update']);

// 编辑模态框状态
const editModalVisible = ref(false);
const loading = ref(false);

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  return dayjs(dateString).isValid() ? dayjs(dateString).format('YYYY-MM-DD') : dateString;
};

// 格式化性别
const formatGender = (gender) => {
  if (gender === 1) return t('policyorder.male');
  if (gender === 0) return t('policyorder.female');
  return 'N/A';
};

// 格式化货币
const formatCurrency = (value) => {
  if (!value && value !== 0) return 'N/A';
  return new Intl.NumberFormat('zh-CN', { style: 'currency', currency: 'CNY' }).format(value);
};

// 婚姻状况选项
const maritalStatusOptions = [
  { value: t('policyorder.single'), label: t('policyorder.single') },
  { value: t('policyorder.married'), label: t('policyorder.married') },
  { value: t('policyorder.divorced'), label: t('policyorder.divorced') },
  { value: t('policyorder.widowed'), label: t('policyorder.widowed') }
];

// 教育程度选项
const educationOptions = [
  { value: t('policyorder.primarySchool'), label: t('policyorder.primarySchool') },
  { value: t('policyorder.middleSchool'), label: t('policyorder.middleSchool') },
  { value: t('policyorder.highSchool'), label: t('policyorder.highSchool') },
  { value: t('policyorder.college'), label: t('policyorder.college') },
  { value: t('policyorder.bachelor'), label: t('policyorder.bachelor') },
  { value: t('policyorder.master'), label: t('policyorder.master') },
  { value: t('policyorder.doctor'), label: t('policyorder.doctor') }
];

// 表单字段配置
const formFields = [
  {
    label: t('policyorder.nameCn'),
    name: 'nameCn',
    type: 'input',
    rules: [{ required: true, message: t('policyorder.pleaseEnterName') }]
  },
  {
    label: t('policyorder.nameEn'),
    name: 'nameEn',
    type: 'input',
    rules: [{ required: false, message: t('policyorder.pleaseEnterNameEn') }]
  },
  {
    label: t('policyorder.gender'),
    name: 'gender',
    type: 'radio',
    options: [
      { value: 1, label: t('policyorder.male') },
      { value: 0, label: t('policyorder.female') }
    ],
    rules: [{ required: true, message: t('policyorder.pleaseSelectGender') }]
  },
  {
    label: t('policyorder.birthdate'),
    name: 'birthDate',
    type: 'date',
    valueFormat: 'timestamp',
    rules: [{ required: true, message: t('policyorder.pleaseSelectBirthdate') }]
  },
  {
    label: t('policyorder.idNumber'),
    name: 'idCardNo',
    type: 'input',
    rules: [
      { required: true, message: t('policyorder.pleaseEnterIdNumber') },
      { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: t('policyorder.pleaseEnterValidIdNumber') }
    ]
  },
  {
    label: t('policyorder.travelPermitNo'),
    name: 'travelPermitNo',
    type: 'input',
    rules: [{ required: false, message: t('policyorder.pleaseEnterTravelPermitNo') }]
  },
  {
    label: t('policyorder.phoneNumber'),
    name: 'mobile',
    type: 'input',
    rules: [
      { required: true, message: t('policyorder.pleaseEnterPhoneNumber') },
      { pattern: /^1[3-9]\d{9}$/, message: t('policyorder.pleaseEnterValidPhoneNumber') }
    ]
  },
  {
    label: t('policyorder.email'),
    name: 'email',
    type: 'input',
    rules: [
      { required: true, message: t('policyorder.pleaseEnterEmail') },
      { type: 'email', message: t('policyorder.pleaseEnterValidEmail') }
    ]
  },
  {
    label: t('policyorder.nationality'),
    name: 'nationality',
    type: 'input',
    rules: [{ required: true, message: t('policyorder.pleaseEnterNationality') }]
  },
  {
    label: t('policyorder.birthPlace'),
    name: 'birthPlace',
    type: 'input',
    rules: [{ required: false, message: t('policyorder.pleaseEnterBirthPlace') }]
  },
  {
    label: t('policyorder.maritalStatus'),
    name: 'maritalStatus',
    type: 'select',
    options: maritalStatusOptions,
    rules: [{ required: true, message: t('policyorder.pleaseSelectMaritalStatus') }]
  },
  {
    label: t('policyorder.educationLevel'),
    name: 'educationLevel',
    type: 'select',
    options: educationOptions,
    rules: [{ required: true, message: t('policyorder.pleaseSelectEducationLevel') }]
  },
  {
    label: t('policyorder.height') + '(cm)',
    name: 'height',
    type: 'number',
    min: 0,
    max: 300,
    precision: 2,
    rules: [{ required: true, message: t('policyorder.pleaseEnterHeight') }]
  },
  {
    label: t('policyorder.weight') + '(kg)',
    name: 'weight',
    type: 'number',
    min: 0,
    max: 500,
    precision: 2,
    rules: [{ required: true, message: t('policyorder.pleaseEnterWeight') }]
  },
  {
    label: t('policyorder.isSmoker'),
    name: 'isSmoker',
    type: 'radio',
    options: [
      { value: 1, label: t('policyorder.yes') },
      { value: 0, label: t('policyorder.no') }
    ],
    rules: [{ required: true, message: t('policyorder.pleaseSelectIsSmoker') }]
  },
  {
    type: 'group',
    label: t('policyorder.addressInfo')
  },
  {
    label: t('policyorder.idCardAddress'),
    name: 'idCardAddress',
    type: 'input',
    rules: [{ required: true, message: t('policyorder.pleaseEnterIdCardAddress') }]
  },
  {
    label: t('policyorder.residentialAddress'),
    name: 'residentialAddress',
    type: 'input',
    rules: [{ required: true, message: t('policyorder.pleaseEnterResidentialAddress') }]
  },
  {
    label: t('policyorder.mailingAddress'),
    name: 'mailingAddress',
    type: 'input',
    rules: [{ required: true, message: t('policyorder.pleaseEnterMailingAddress') }]
  },
  {
    type: 'group',
    label: t('policyorder.occupationInfo')
  },
  {
    label: t('policyorder.companyNameCn'),
    name: 'companyNameCn',
    type: 'input',
    rules: [{ required: true, message: t('policyorder.pleaseEnterCompanyName') }]
  },
  {
    label: t('policyorder.companyNameEn'),
    name: 'companyNameEn',
    type: 'input',
    rules: [{ required: false, message: t('policyorder.pleaseEnterCompanyNameEn') }]
  },
  {
    label: t('policyorder.companyAddress'),
    name: 'companyAddress',
    type: 'input',
    rules: [{ required: true, message: t('policyorder.pleaseEnterCompanyAddress') }]
  },
  {
    label: t('policyorder.companyIndustry'),
    name: 'companyIndustry',
    type: 'input',
    rules: [{ required: true, message: t('policyorder.pleaseEnterCompanyIndustry') }]
  },
  {
    label: t('policyorder.position'),
    name: 'position',
    type: 'input',
    rules: [{ required: true, message: t('policyorder.pleaseEnterPosition') }]
  },
  {
    label: t('policyorder.annualIncome'),
    name: 'annualIncome',
    type: 'number',
    min: 0,
    precision: 2,
    rules: [{ required: true, message: t('policyorder.pleaseEnterAnnualIncome') }]
  }
];

// 初始表单值
const initialFormValues = computed(() => {
  if (!props.policyholder) return {};
  return { ...props.policyholder };
});

// 显示编辑模态框
const showEditModal = () => {
  editModalVisible.value = true;
};

// 处理表单提交
const handleSubmit = async (formData) => {
  loading.value = true;
  try {
    // 调用真实API更新数据
    await policyOrderAPI.updatePolicyPolicyholderInfo(formData);

    message.success(t('policyorder.policyholderInfoUpdateSuccess'));
    editModalVisible.value = false;

    // 通知父组件更新数据
    emit('update');
  } catch (error) {
    console.error(t('policyorder.updatePolicyholderInfoFailed'), error);
    message.error(t('policyorder.updatePolicyholderInfoFailed') + ': ' + (error.message || t('common.errors.serverError')));
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.mac-style-info-panel {
  border-radius: 8px;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-medium);
}
</style>