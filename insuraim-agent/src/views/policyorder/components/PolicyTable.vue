<template>
  <a-table :dataSource="policyList" :columns="localizedPolicyColumns" :pagination="{
    total: total,
    current: currentPage,
    pageSize: pageSize,
    showSizeChanger: true,
    showQuickJumper: true,
    pageSizeOptions: ['10', '20', '50', '100'],
    showTotal: (total) => `${$t('common.totalItems', { total })}`,
    size: 'middle'
  }" class="mac-style-table" size="middle" :rowClassName="(record, index) => (index % 2 === 1 ? 'table-striped' : '')"
    bordered :loading="loading" @change="handleTableChange" :customRow="customRowHandler"
    @resizeColumn="handleResizeColumn" :scroll="{ x: 900 }">
    <template #bodyCell="{ column, record }">
      <!-- 投保人姓名列 -->
      <template v-if="column.dataIndex === 'policyOrder.policyholderNameCn'">
        <div>
          <a-tooltip :title="record.policyOrder.policyholderNameEn">
            <span>{{ record.policyOrder.policyholderNameCn }}{{ `(${record.policyOrder.policyholderNameEn})` }}</span>

          </a-tooltip>
        </div>
      </template>

      <!-- 被保人姓名列 -->
      <template v-if="column.dataIndex === 'policyOrder.insuredNameCn'">
        <div>
          <a-tooltip :title="record.policyOrder.insuredNameEn">
            <span>{{ record.policyOrder.insuredNameCn }}{{ `(${record.policyOrder.insuredNameEn})` }}</span>
          </a-tooltip>
        </div>
      </template>
      <!-- 订单号列 -->
      <template v-if="column.dataIndex === 'policyOrder.orderNo'">
        <a-tooltip :title="record.policyOrder.orderNo">
          <span class="truncate block">{{ record.policyOrder.orderNo }}</span>
        </a-tooltip>
      </template>

      <!-- 地区列 -->
      <template v-if="column.dataIndex === 'policyOrder.region'">
        <a-tooltip :title="record.policyOrder.region">
          <span>{{ record.policyOrder.region }}</span>
        </a-tooltip>
      </template>

      <!-- 保险公司列 -->
      <template v-if="column.dataIndex === 'policyOrder.company'">
        <a-tooltip :title="record.policyOrder.company">
          <span>{{ record.policyOrder.company }}</span>
        </a-tooltip>
      </template>

      <!-- 签约产品列 -->
      <template v-if="column.dataIndex === 'policyOrder.product'">
        <a-tooltip :title="record.policyOrder.product">
          <span class="truncate block">{{ record.policyOrder.product }}</span>
        </a-tooltip>
      </template>

      <!-- 保单号列 -->
      <template v-if="column.dataIndex === 'policy.policyNo'">
        <a-tooltip :title="record.policy?.policyNo">
          <span>{{ record.policy?.policyNo ? record.policy?.policyNo : '--' }}</span>
        </a-tooltip>
      </template>

      <!-- 缴费期限列 -->
      <template v-if="column.dataIndex === 'policyOrder.paymentTerm'">
        <a-tooltip :title="record.policyOrder.paymentTerm">
          <span>{{ record.policyOrder.paymentTerm }}</span>
        </a-tooltip>
      </template>

      <!-- 年缴保费列 -->
      <template v-if="column.dataIndex === 'policyOrder.annualPremium'">
        <a-tooltip :title="record.policyOrder.annualPremium">
          <span>{{ record.policyOrder.annualPremium }} {{ record.policyOrder.currency }}</span>
        </a-tooltip>
      </template>

      <!-- 生效日期列 -->
      <template v-if="column.dataIndex === 'policy.effectiveDate'">
        <a-tooltip :title="record.policy?.effectiveDate">
          <span>{{ record.policy?.effectiveDate || $t('policyorder.notEffective') }}</span>
        </a-tooltip>
      </template>

      <!-- 下次续保列 -->
      <template v-if="column.dataIndex === 'policy.nextRenewal'">
        <div>
          <!-- <span>{{ record.policy?.nextRenewalAmount }} {{ record.policyOrder?.currency }}</span>
          <br /> -->
          <a-tooltip :title="record.policy?.nextRenewalDate">
            <span class="text-xs text-gray-500 italic">{{ record.policy?.nextRenewalDate ?
              record.policy?.nextRenewalDate
              : '--' }}</span>
          </a-tooltip>
        </div>
      </template>

      <!-- 状态列 -->
      <template v-if="column.dataIndex === 'policy.policyStatus'">
        <a-tag v-if="record.policy?.policyStatus" :color="getStatusColor(record.policy.policyStatus)"
          class="mac-style-tag">
          {{ getPolicyStatusText(record.policy.policyStatus) }}
        </a-tag>
        <span v-else>N/A</span>
      </template>
      <!-- 状态列 -->
      <template v-if="column.dataIndex === 'policy.orderStatus'">
        <a-tag v-if="record.policyOrder?.orderStatus" :color="getStatusColor(record.policyOrder.orderStatus)"
          class="mac-style-tag">
          {{ getStatusText(record.policyOrder.orderStatus) }}
        </a-tag>
        <span v-else>N/A</span>
      </template>

      <!-- 操作列 -->
      <template v-if="column.dataIndex === 'action'">
        <div class="flex space-x-2 justify-center">
          <a-tooltip :title="$t('policyorder.deletePolicy')">
            <a-popconfirm :title="$t('policyorder.confirmDelete')" :ok-text="$t('policyorder.confirm')"
              :cancel-text="$t('policyorder.cancel')" @confirm="deletePolicy(record)">
              <a-button type="link" size="small" danger class="mac-style-icon-button">
                <template #icon>
                  <Icon icon="material-symbols:delete-outline" class="text-red-500 text-lg" />
                </template>
              </a-button>
            </a-popconfirm>
          </a-tooltip>
        </div>
      </template>
    </template>
  </a-table>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits } from 'vue';
import { Icon } from '@iconify/vue';
import { useOrderStore, OrderStatusEnum, PolicyStatusEnum } from '@/store/modules/order';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';

const { t } = useI18n();
const router = useRouter();

const props = defineProps({
  policyList: {
    type: Array,
    default: () => []
  },
  total: {
    type: Number,
    default: 0
  },
  currentPage: {
    type: Number,
    default: 1
  },
  pageSize: {
    type: Number,
    default: 10
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['view-detail', 'delete', 'page-change']);

// 使用store中的枚举
const orderStore = useOrderStore();

// 表格列定义
const policyColumns = ref([
  {
    title: 'policyorder.orderNo',
    dataIndex: 'policyOrder.orderNo',
    key: 'orderNo',
    width: 100,
    ellipsis: true,
    resizable: true,
    minWidth: 100,
  },
  {
    title: 'policyorder.policyholder',
    dataIndex: 'policyOrder.policyholderNameCn',
    key: 'policyholderName',
    width: 90,
    ellipsis: true,
    resizable: true,
    minWidth: 90,
  },
  {
    title: 'policyorder.insured',
    dataIndex: 'policyOrder.insuredNameCn',
    key: 'insuredName',
    width: 90,
    ellipsis: true,
    resizable: true,
    minWidth: 90,
  },
  {
    title: t('policyorder.region'),
    dataIndex: 'policyOrder.region',
    key: 'region',
    width: 60,
    ellipsis: true,
    resizable: true,
    minWidth: 60,
  },
  {
    title: 'policyorder.company',
    dataIndex: 'policyOrder.company',
    key: 'company',
    width: 90,
    ellipsis: true,
    resizable: true,
    minWidth: 90,
  },
  {
    title: 'policyorder.product',
    dataIndex: 'policyOrder.product',
    key: 'product',
    width: 160,
    ellipsis: true,
    resizable: true,
    minWidth: 160,
  },
  {
    title: 'policyorder.policyNo',
    dataIndex: 'policy.policyNo',
    key: 'policyNo',
    width: 80,
    ellipsis: true,
    resizable: true,
    minWidth: 80,
  },
  {
    title: 'policyorder.paymentTerm',
    dataIndex: 'policyOrder.paymentTerm',
    key: 'paymentTerm',
    width: 80,
    ellipsis: true,
    resizable: true,
    minWidth: 80,
  },
  {
    title: 'policyorder.annualPremium',
    dataIndex: 'policyOrder.annualPremium',
    key: 'annualPremium',
    width: 100,
    ellipsis: true,
    resizable: true,
    minWidth: 100,
  },
  {
    title: 'policyorder.effectiveDate',
    dataIndex: 'policy.effectiveDate',
    key: 'effectiveDate',
    width: 100,
    ellipsis: true,
    resizable: true,
    minWidth: 100,
  },
  {
    title: 'policyorder.nextRenewal',
    dataIndex: 'policy.nextRenewal',
    key: 'nextRenewal',
    width: 120,
    ellipsis: true,
    resizable: true,

  },
  {
    title: 'policyorder.policyStatus',
    dataIndex: 'policy.policyStatus',
    key: 'policyStatus',
    width: 80,
    ellipsis: true,
    resizable: true,
    minWidth: 80,
    fixed: 'right'
  },
  {
    title: 'policyorder.orderStatus',
    dataIndex: 'policy.orderStatus',
    key: 'orderStatus',
    width: 80,
    ellipsis: true,
    resizable: true,
    minWidth: 90,
    fixed: 'right'
  },
  {
    title: 'policyorder.action',
    dataIndex: 'action',
    key: 'action',
    width: 'auto',
  },

]);

const localizedPolicyColumns = computed(() => {
  return policyColumns.value.map(column => ({
    ...column,
    title: column.title ? t(column.title) : column.title
  }));
});
// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    [OrderStatusEnum.APPOINTMENT]: 'processing',
    [OrderStatusEnum.WAIT_PAYMENT]: 'gold',
    [OrderStatusEnum.QUIET_PERIOD]: 'warning',
    [OrderStatusEnum.IN_EFFECT]: 'success',
    [OrderStatusEnum.EXPIRED]: 'error',
    'PENDING': 'orange'
  };
  return colorMap[status] || 'default';
};

// 将状态枚举值转换为中文描述
const getStatusText = (status) => {
  const statusMap = {
    [OrderStatusEnum.APPOINTMENT]: t('policyorder.appointment'),
    [OrderStatusEnum.WAIT_PAYMENT]: t('policyorder.waitPayment'),
    [OrderStatusEnum.QUIET_PERIOD]: t('policyorder.quietPeriod'),
    [OrderStatusEnum.IN_EFFECT]: t('policyorder.inEffect'),
    [OrderStatusEnum.EXPIRED]: t('policyorder.expired'),
    'PENDING': t('policyorder.pending')
  };
  return statusMap[status] || status;
};
// 将状态枚举值转换为中文描述
// 获取保单状态文本
const getPolicyStatusText = (statusCode) => {
  const statusMap = {
    [PolicyStatusEnum.APPOINTMENT]: t('policyorder.appointment'),
    [PolicyStatusEnum.SIGN_APPOINTMENT]: t('policyorder.signAppointment'),
    [PolicyStatusEnum.SIGN]: t('policyorder.sign'),
    [PolicyStatusEnum.PAYMENT_GUIDE]: t('policyorder.paymentGuide'),
    [PolicyStatusEnum.PAYMENT_RECORD]: t('policyorder.paymentRecord'),
    [PolicyStatusEnum.EFFECTIVE]: t('policyorder.effective'),
    [PolicyStatusEnum.CONTRACT_SENT]: t('policyorder.contractSent'),
    [PolicyStatusEnum.SIGNATURE]: t('policyorder.signature'),
    [PolicyStatusEnum.QUIET_PERIOD_EXPIRED]: t('policyorder.quietPeriodExpired')
  };
  return statusMap[statusCode] || statusCode;
};

// 处理表格分页变化
const handleTableChange = (pagination, filters, sorter) => {
  emit('page-change', {
    page: pagination.current,
    pageSize: pagination.pageSize
  });
};

// 查看详情
const viewDetail = (record) => {
  emit('view-detail', record);
};

// 删除保单
const deletePolicy = (record) => {
  emit('delete', record);
};

// 自定义行点击处理函数
const customRowHandler = (record) => {
  return {
    onClick: (event) => {
      // 检查点击是否来自删除按钮或其他控件
      const isActionClick = event.target.closest('.flex.space-x-2') !== null;
      if (!isActionClick) {
        viewDetail(record);
      }
    },
    style: 'cursor: pointer;'
  };
};

// 处理列宽度调整
const handleResizeColumn = (w, col) => {
  // 查找并更新对应列的宽度
  const targetColumn = policyColumns.value.find(column => column.key === col.key);
  if (targetColumn) {
    targetColumn.width = w;
  }
};
</script>

<style scoped>
/* 表格样式 */
:deep(.mac-style-table .ant-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.mac-style-table .ant-table-thead > tr > th) {
  background-color: var(--bg-tertiary);
  font-weight: 500;
  padding: 8px 12px;
  font-size: 13px;
}

:deep(.mac-style-table .ant-table-tbody > tr:hover > td) {
  background-color: var(--bg-secondary) !important;
}

/* 表格条纹样式 */
:deep(.table-striped) {
  background-color: var(--bg-tertiary);
}

.hover-pointer {
  cursor: pointer;
  color: var(--primary);
}

.hover-pointer:hover {
  color: var(--primary);
  text-decoration: underline;
}

/* 行高 */
/* :deep(.ant-table-tbody > tr > td) {
  padding: 8px 12px;
  font-size: 13px;
} */

/* 悬停效果 */
:deep(.ant-table-tbody > tr:hover > td) {
  background-color: var(--bg-secondary) !important;
}

/* 标签样式 */
:deep(.mac-style-tag) {
  border-radius: 4px;
  font-weight: 500;
}

/* 表格列宽调整样式 */
:deep(.react-resizable) {
  position: relative;
  background-clip: padding-box;
}

:deep(.react-resizable-handle) {
  position: absolute;
  right: -5px;
  bottom: 0;
  z-index: 1;
  width: 10px;
  height: 100%;
  cursor: col-resize;
}

/* 表格内容截断样式 */
:deep(.ant-table-cell) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>