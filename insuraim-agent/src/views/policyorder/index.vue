<template>
  <!-- 主要内容区域 -->
  <div class="container mx-auto py-6 px-4">
    <!-- 页面标题 -->
    <div
      class="title-section mb-6 p-6 rounded-lg shadow-md text-white bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700">
      <div class="flex items-center">
        <Icon icon="mdi:file-document-multiple" class="text-4xl mr-3" />
        <h1 class="text-2xl font-bold page-title">{{ $t('policyorder.policyManagement') }}</h1>
      </div>
      <p class="mt-2 page-description">
        {{ $t('policyorder.browseAndManage') }}
      </p>
    </div>

    <!-- 统计卡片 -->
    <a-card class="mb-6 rounded-lg" :bordered="false">
      <h1 class="text-2xl font-bold mb-4">{{ $t('policyorder.statistics') }}</h1>
      <!-- 订单统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- 保单总数卡片 -->
        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-blue-500">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">{{ $t('policyorder.totalPolicies') }}</p>
              <p class="text-2xl font-bold">{{ policyTotal || 0 }}</p>
            </div>
            <div class="bg-blue-100 p-2 rounded-full">
              <Icon icon="mdi:file-document" class="text-2xl text-blue-500" />
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-500">
            <span class="flex items-center">
              <Icon icon="mdi:information-outline" class="mr-1" />
              {{ $t('policyorder.totalPoliciesDesc') }}
            </span>
          </div>
        </div>

        <!-- 生效保单卡片 -->
        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-green-500">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <p class="text-gray-500 text-sm">{{ $t('policyorder.effectivePolicies') }}</p>
              <p class="text-2xl font-bold">{{ effectivePolicyCount || 0 }}</p>
            </div>
            <div class="bg-green-100 p-2 rounded-full">
              <Icon icon="mdi:check-circle" class="text-2xl text-green-500" />
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-500">
            <span class="flex items-center">
              <Icon icon="mdi:information-outline" class="mr-1" />
              {{ $t('policyorder.effectivePoliciesDesc') }}
            </span>
          </div>
        </div>

        <!-- 待处理保单卡片 -->
        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-orange-500">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">{{ $t('policyorder.pendingPolicies') }}</p>
              <p class="text-2xl font-bold">{{ pendingPolicyCount || 0 }}</p>
            </div>
            <div class="bg-orange-100 p-2 rounded-full">
              <Icon icon="mdi:clock-outline" class="text-2xl text-orange-500" />
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-500">
            <span class="flex items-center">
              <Icon icon="mdi:information-outline" class="mr-1" />
              {{ $t('policyorder.pendingPoliciesDesc') }}
            </span>
          </div>
        </div>

        <!-- 即将到期保单卡片 -->
        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-purple-500">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">{{ $t('policyorder.expiringPolicies') }}</p>
              <p class="text-2xl font-bold">{{ expiringPolicyCount || 0 }}</p>
            </div>
            <div class="bg-purple-100 p-2 rounded-full">
              <Icon icon="mdi:alarm" class="text-2xl text-purple-500" />
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-500">
            <span class="flex items-center">
              <Icon icon="mdi:information-outline" class="mr-1" />
              {{ $t('policyorder.expiringPoliciesDesc') }}
            </span>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 保单管理 -->
    <div class="bg-white rounded-lg shadow-md p-6 mt-4">
      <!-- 筛选器 -->
      <div class="filter-section mb-6 p-6 rounded-lg bg-gradient-to-r from-gray-50 to-gray-100 border border-gray-200">
        <div class="flex flex-wrap items-center justify-between gap-4">
          <!-- 使用筛选组件 -->
          <policy-filter :initial-params="policyParams" @update:params="updateParams" @search="searchPolicy"
            @status-change="handleStatusChange" @region-change="handleRegionChange"
            @company-change="handleCompanyChange" @policy-status-change="handlePolicyStatusChange" ref="filterRef" />
          <a-button type="primary" size="middle" @click="refreshPolicyData">
            <template #icon>
              <Icon icon="mdi:refresh" />
            </template>
            <span>{{ $t('policyorder.reset') }}</span>
          </a-button>
        </div>
      </div>

      <!-- 使用表格组件 -->
      <policy-table :policy-list="policyList" :total="policyTotal" :current-page="policyParams.page"
        :page-size="policyParams.pageSize" :loading="policyLoading" @view-detail="viewPolicyDetail"
        @delete="deletePolicy" @page-change="handlePolicyTableChange" />
    </div>
  </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';
import { ref, reactive, onMounted, computed } from 'vue';
import { message } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { policyOrderAPI } from '@/api';
import { useOrderStore } from '@/store/modules/order';

// 导入组件
import PolicyFilter from './components/PolicyFilter.vue';
import PolicyTable from './components/PolicyTable.vue';

// 路由实例
const router = useRouter();

// 国际化
const { t } = useI18n();

// 获取订单状态枚举
const orderStore = useOrderStore();

// 状态和数据
const policyList = ref([]);
const policyTotal = ref(0);
const policyLoading = ref(false);
const filterRef = ref(null);

// 统计数据
const effectivePolicyCount = ref(0);
const pendingPolicyCount = ref(0);
const expiringPolicyCount = ref(0);

// 查询参数
const policyParams = reactive({
  page: 1,
  pageSize: 10,
  status: '',
  region: '',
  company: '',
  search: '',
  policyStatus: ''
});

// 生命周期钩子
onMounted(() => {
  loadPolicyData();
});

// 加载保单数据
const loadPolicyData = async () => {
  try {
    policyLoading.value = true;

    const params = {
      // orderNo: policyParams.search || undefined,
      // policyNo: policyParams.search || undefined,
      // policyholderName: policyParams.search || undefined,
      search: policyParams.search || undefined,
      status: policyParams.status || undefined,
      region: policyParams.region || undefined,
      company: policyParams.company || undefined,
      policyStatus: policyParams.policyStatus || undefined,
      page: policyParams.page,
      pageSize: policyParams.pageSize
    };

    const filteredParams = Object.fromEntries(
      Object.entries(params).filter(([_, v]) => v !== undefined)
    );

    const result = await policyOrderAPI.page(filteredParams);

    if (result) {
      policyList.value = result.records || [];
      policyTotal.value = result.total || 0;
      policyList.value.forEach((item, index) => {
        item.key = item.id || `policy-${index}`;
      });

      // 计算统计数据
      const currentDate = new Date();
      const thirtyDaysLater = new Date();
      thirtyDaysLater.setDate(currentDate.getDate() + 30);

      // 生效中的保单
      effectivePolicyCount.value = policyList.value.filter(item => item.policy.policyStatus === 'IN_EFFECT').length;
      // 待处理的保单（待缴费、预约等）
      pendingPolicyCount.value = policyList.value.filter(item =>
        ['APPOINTMENT', 'WAIT_PAYMENT', 'PENDING'].includes(item.policy.policyStatus)
      ).length;
      // 30天内到期的保单
      expiringPolicyCount.value = policyList.value.filter(item => {
        if (item.policy.policyStatus === 'IN_EFFECT' && item.nextRenewal) {
          const renewalDate = new Date(item.nextRenewal);
          return renewalDate > currentDate && renewalDate < thirtyDaysLater;
        }
        return false;
      }).length;
    } else {
      message.error(result.message || t('policyorder.loadPolicyDataFailed'));
      policyList.value = [];
      policyTotal.value = 0;
    }

    policyLoading.value = false;
  } catch (error) {
    console.error(t('policyorder.loadPolicyDataFailed'), error);
    message.error(t('policyorder.loadPolicyDataFailed'));
    policyLoading.value = false;
    policyList.value = [];
    policyTotal.value = 0;
  }
};

// 处理表格分页变化
const handlePolicyTableChange = (pagination) => {
  policyParams.page = pagination.page;
  policyParams.pageSize = pagination.pageSize;
  loadPolicyData();
};

// 更新参数
const updateParams = (params) => {
  Object.assign(policyParams, params);
};

// 处理状态变化
const handleStatusChange = () => {
  policyParams.page = 1;
  loadPolicyData();
};

// 处理地区变化
const handleRegionChange = () => {
  policyParams.page = 1;
  loadPolicyData();
};

// 处理保险公司变化
const handleCompanyChange = () => {
  policyParams.page = 1;
  loadPolicyData();
};

// 处理保单状态变化
const handlePolicyStatusChange = () => {
  policyParams.page = 1;
  loadPolicyData();
};

// 搜索保单
const searchPolicy = (value) => {
  policyParams.search = value;
  policyParams.page = 1;
  loadPolicyData();
};

// 刷新保单数据
const refreshPolicyData = () => {
  if (filterRef.value) {
    filterRef.value.resetFilters();
  } else {
    policyParams.status = '';
    policyParams.region = '';
    policyParams.company = '';
    policyParams.search = '';
    policyParams.policyStatus = '';
  }
  policyParams.page = 1;
  loadPolicyData();
};

// 查看保单详情 - 修改为路由跳转
const viewPolicyDetail = (record) => {
  if (record.policyOrder && record.policyOrder.id) {
    router.push({ name: 'PolicyDetail', params: { policyOrderId: record.policyOrder.id } });
  }
};

// 删除保单
const deletePolicy = async (record) => {
  try {
    if (!record.policyOrder || !record.policyOrder.id) {
      message.error(t('policyorder.loadPolicyDataFailed'));
      return;
    }

    const policyId = record.policy.id;
    console.log(t('policyorder.deletePolicy'), policyId);

    // 调用删除API
    await policyOrderAPI.deletePolicyOrder(policyId);

    message.success(t('policyorder.deleteSuccess'));

    // 重新加载数据
    loadPolicyData();
  } catch (error) {
    console.error(t('policyorder.deleteFailed'), error);
    message.error(error.message || t('policyorder.deleteFailed'));
  }
};
</script>

<style scoped>
/* 标题区域样式 */
.title-section {
  transition: all 0.3s ease;
}

.title-section:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.page-title {
  position: relative;
  color: white;
  display: inline-block;
}

.page-title::after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: white;
  border-radius: 3px;
}

.page-description {
  max-width: 600px;
  opacity: 0.9;
}

/* 统计卡片样式 */
.stat-card {
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

/* 筛选器样式 */
.filter-section {
  transition: all 0.3s ease;
}

.filter-section:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* 刷新按钮动画 */
.refresh-btn {
  transition: transform 0.3s ease;
}

.refresh-btn:hover {
  transform: rotate(180deg);
}

:deep(.mac-style-input:hover) {
  border-color: #40a9ff;
}

:deep(.mac-style-input:focus) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 选择器样式 */
:deep(.mac-style-select .ant-select-selector) {
  border-radius: 6px !important;
  transition: all 0.2s ease;
}

:deep(.mac-style-select:hover .ant-select-selector) {
  border-color: #40a9ff !important;
}

:deep(.mac-style-select-focused .ant-select-selector),
:deep(.mac-style-select .ant-select-selector:focus) {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

/* 日期选择器样式 */
:deep(.mac-style-datepicker) {
  border-radius: 6px;
  transition: all 0.2s ease;
}

:deep(.mac-style-datepicker:hover) {
  border-color: #40a9ff;
}

:deep(.mac-style-datepicker-focused),
:deep(.mac-style-datepicker:focus) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 文本域样式 */
:deep(.mac-style-textarea) {
  border-radius: 6px;
  transition: all 0.2s ease;
}

:deep(.mac-style-textarea:hover) {
  border-color: #40a9ff;
}

:deep(.mac-style-textarea:focus) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 表格样式 */
:deep(.mac-style-table .ant-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.mac-style-table .ant-table-thead > tr > th) {
  background-color: #f7f7f7;
  font-weight: 500;
}

:deep(.mac-style-table .ant-table-tbody > tr:hover > td) {
  background-color: #f0f7ff !important;
}

:deep(.mac-style-table-sm .ant-table) {
  border-radius: 6px;
  overflow: hidden;
}

/* 标签样式 */
:deep(.mac-style-tag) {
  border-radius: 4px;
  font-weight: 500;
}

/* 信息面板样式 */
.mac-style-info-panel {
  border-radius: 8px;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-medium);
}

/* 列表样式 */
:deep(.mac-style-list) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.mac-style-list .ant-list-item:hover) {
  background-color: var(--bg-secondary);
}

/* 表格条纹样式 */
:deep(.table-striped) {
  background-color: var(--bg-tertiary);
}

/* 行高 */
:deep(.ant-table-tbody > tr > td) {
  padding: 12px 16px;
  font-size: 14px;
}

/* 悬停效果 */
:deep(.ant-table-tbody > tr:hover > td) {
  background-color: #e6f7ff !important;
}

/* 抽屉样式 */
:deep(.mac-style-drawer .ant-drawer-content) {
  border-radius: 10px 0 0 10px;
}

:deep(.mac-style-drawer .ant-drawer-header) {
  border-radius: 10px 0 0 0;
  background-color: #f7f7f7;
}

/* 模态框样式 */
:deep(.mac-style-modal .ant-modal-content) {
  border-radius: 10px;
  background-color: #fff;
  overflow: hidden;
}

:deep(.mac-style-modal .ant-modal-header) {
  border-radius: 10px 10px 0 0;
  background-color: #fff;
}

:deep(.mac-style-modal .ant-modal-footer) {
  border-radius: 0 0 10px 10px;
}

/* 调整添加按钮的样式 */
:deep(.ant-btn-primary) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

:deep(.ant-btn-primary .anticon + span),
:deep(.ant-btn-primary .iconify + span) {
  margin-left: 6px;
}

/* 刷新按钮图标居中样式 */
:deep(.refresh-btn) {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

:deep(.refresh-btn .iconify) {
  margin: 0 !important;
  line-height: 1 !important;
}
</style>