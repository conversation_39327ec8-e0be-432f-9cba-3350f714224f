<template>
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6" v-if="hasData">
        <!-- 特性雷达图 -->
        <div class="bg-white p-6 rounded-lg shadow-md">
            <h3 class="text-lg font-medium text-gray-700 mb-4 flex items-center">
                <Icon icon="material-symbols:radar" class="text-blue-500 mr-2 text-xl" />
                产品特性对比雷达图
                <span v-if="products.length > 1" class="ml-2 text-sm text-gray-500">
                    ({{ products.length }}个产品)
                </span>
            </h3>
            <div class="relative" style="height: 400px;">
                <div v-if="loading"
                    class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 z-10">
                    <a-spin />
                </div>
                <div ref="radarChartRef" class="h-full w-full"></div>
            </div>
        </div>

        <!-- 条形对比图 -->
        <div class="bg-white p-6 rounded-lg shadow-md">
            <h3 class="text-lg font-medium text-gray-700 mb-4 flex items-center">
                <Icon icon="material-symbols:bar-chart" class="text-green-500 mr-2 text-xl" />
                产品年龄限制与保费范围对比
                <span v-if="products.length > 1" class="ml-2 text-sm text-gray-500">
                    ({{ products.length }}个产品)
                </span>
            </h3>
            <div class="relative" style="height: 400px;">
                <div v-if="loading"
                    class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 z-10">
                    <a-spin />
                </div>
                <div ref="barChartRef" class="h-full w-full"></div>
            </div>
        </div>

        <!-- 承保分布图 -->
        <div class="bg-white p-6 rounded-lg shadow-md">
            <h3 class="text-lg font-medium text-gray-700 mb-4 flex items-center">
                <Icon icon="material-symbols:pie-chart" class="text-purple-500 mr-2 text-xl" />
                承保特性分布
                <span v-if="products.length > 1" class="ml-2 text-sm text-gray-500">
                    (基于{{ products.length }}个产品统计)
                </span>
            </h3>
            <div class="relative" style="height: 400px;">
                <div v-if="loading"
                    class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 z-10">
                    <a-spin />
                </div>
                <div ref="pieChartRef" class="h-full w-full"></div>
            </div>
        </div>

        <!-- 货币分布图 -->
        <div class="bg-white p-6 rounded-lg shadow-md">
            <h3 class="text-lg font-medium text-gray-700 mb-4 flex items-center">
                <Icon icon="material-symbols:currency-exchange" class="text-orange-500 mr-2 text-xl" />
                货币支持分布
                <span v-if="products.length > 1" class="ml-2 text-sm text-gray-500">
                    (基于{{ products.length }}个产品统计)
                </span>
            </h3>
            <div class="relative" style="height: 400px;">
                <div v-if="loading"
                    class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 z-10">
                    <a-spin />
                </div>
                <div ref="currencyChartRef" class="h-full w-full"></div>
            </div>
        </div>
    </div>

    <!-- 无数据展示 -->
    <div v-else class="bg-white p-6 rounded-lg shadow-md mb-6 flex flex-col items-center justify-center">
        <Icon icon="material-symbols:chart-data-unavailable" class="text-6xl text-gray-300 mb-4" />
        <p class="text-gray-500">选择至少一个产品以查看对比图表</p>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, computed, markRaw, nextTick } from 'vue';
import { Icon } from '@iconify/vue';
import * as echarts from 'echarts';

const props = defineProps({
    products: {
        type: Array,
        default: () => []
    },
    selectedProducts: {
        type: Array,
        default: () => []
    },
    loading: {
        type: Boolean,
        default: false
    }
});

// 图表引用
const radarChartRef = ref(null);
const barChartRef = ref(null);
const pieChartRef = ref(null);
const currencyChartRef = ref(null);

// 图表实例 - 使用普通变量而不是响应式引用
let radarChart = null;
let barChart = null;
let pieChart = null;
let currencyChart = null;

// 统一的颜色配置
const colorPalette = [
    '#1890ff', // 蓝色
    '#52c41a', // 绿色
    '#faad14', // 黄色
    '#f5222d', // 红色
    '#722ed1', // 紫色
    '#13c2c2', // 青色
    '#eb2f96', // 粉色
    '#fa8c16', // 橙色
    '#a0d911', // 青柠色
    '#fadb14'  // 金色
];

// 获取产品颜色的函数，确保同一产品在不同图表中使用相同颜色
const getProductColor = (index) => {
    return colorPalette[index % colorPalette.length];
};

// 根据产品数量动态计算图表高度
const getChartHeight = computed(() => {
    const baseHeight = 320; // 基础高度
    const productCount = props.products.length;
    // 产品数量超过3个时，适当增加高度以容纳更多图例
    if (productCount > 3) {
        return baseHeight + (productCount - 3) * 20; // 每增加一个产品，高度增加20px
    }
    return baseHeight;
});

// 判断是否有足够数据显示图表
const hasData = computed(() => props.products.length >= 1);

// 从产品数据中提取年龄范围的数值
const extractAgeRange = (product) => {
    try {
        console.log('正在提取年龄范围，产品ID:', product.id);

        // 尝试从基本信息中查找受保人年龄限制
        const basicInfo = product.productDetails?.find(section => section.name === '基本信息');
        if (basicInfo) {
            console.log('找到基本信息部分');
            const ageAttr = basicInfo.attributes?.find(attr =>
                attr.name === '受保人年龄限制' ||
                attr.name === '投保年龄' ||
                attr.name.includes('年龄')
            );
            if (ageAttr && ageAttr.value) {
                console.log('找到年龄属性:', ageAttr.name, '值:', ageAttr.value);

                // 尝试提取年龄范围数字 - 支持多种格式
                let matches = ageAttr.value.match(/(\d+)天至(\d+)岁/);
                if (matches && matches.length >= 3) {
                    const result = {
                        min: parseInt(matches[1]) / 365, // 转换天数为年
                        max: parseInt(matches[2])
                    };
                    console.log('提取年龄范围成功(天至岁):', result);
                    return result;
                }

                // 尝试提取 "xx岁至xx岁" 格式
                matches = ageAttr.value.match(/(\d+)岁至(\d+)岁/);
                if (matches && matches.length >= 3) {
                    const result = {
                        min: parseInt(matches[1]),
                        max: parseInt(matches[2])
                    };
                    console.log('提取年龄范围成功(岁至岁):', result);
                    return result;
                }

                // 尝试提取 "xx至xx岁" 格式
                matches = ageAttr.value.match(/(\d+)至(\d+)岁/);
                if (matches && matches.length >= 3) {
                    const result = {
                        min: parseInt(matches[1]),
                        max: parseInt(matches[2])
                    };
                    console.log('提取年龄范围成功(至岁):', result);
                    return result;
                }

                // 尝试提取任何数字组合
                matches = ageAttr.value.match(/\d+/g);
                if (matches && matches.length >= 2) {
                    const numbers = matches.map(n => parseInt(n));
                    const result = {
                        min: Math.min(...numbers),
                        max: Math.max(...numbers)
                    };
                    console.log('提取年龄范围成功(数字组合):', result);
                    return result;
                }
            }
        }

        // 如果没找到或解析失败，尝试从ageRange获取
        if (product.ageRange) {
            console.log('尝试从ageRange属性获取:', product.ageRange);
            // 检查ageRange是否为对象格式
            if (typeof product.ageRange === 'object') {
                const min = typeof product.ageRange.min === 'number'
                    ? product.ageRange.min
                    : (typeof product.ageRange.min === 'string' && product.ageRange.min.match(/\d+/)
                        ? parseInt(product.ageRange.min.match(/\d+/)[0])
                        : 0);

                const max = typeof product.ageRange.max === 'number'
                    ? product.ageRange.max
                    : (typeof product.ageRange.max === 'string' && product.ageRange.max.match(/\d+/)
                        ? parseInt(product.ageRange.max.match(/\d+/)[0])
                        : 80);

                const result = { min, max };
                console.log('从ageRange提取结果:', result);
                return result;
            }
        }

        // 尝试从子产品中查找
        if (product.subProducts && product.subProducts.length > 0) {
            console.log('尝试从子产品获取年龄范围');
            const subProduct = product.subProducts[0];
            if (subProduct.ageRange) {
                console.log('找到子产品ageRange:', subProduct.ageRange);
                // 解析子产品的年龄范围
                const min = typeof subProduct.ageRange.min === 'number'
                    ? subProduct.ageRange.min
                    : (typeof subProduct.ageRange.min === 'string' && subProduct.ageRange.min.match(/\d+/)
                        ? parseInt(subProduct.ageRange.min.match(/\d+/)[0])
                        : 0);

                const max = typeof subProduct.ageRange.max === 'number'
                    ? subProduct.ageRange.max
                    : (typeof subProduct.ageRange.max === 'string' && subProduct.ageRange.max.match(/\d+/)
                        ? parseInt(subProduct.ageRange.max.match(/\d+/)[0])
                        : 80);

                const result = { min, max };
                console.log('从子产品提取结果:', result);
                return result;
            }
        }

        // 默认返回空对象
        console.log('无法提取年龄范围，使用默认值');
        return { min: 0, max: 80 };
    } catch (error) {
        console.error('提取年龄范围出错:', error);
        return { min: 0, max: 80 };
    }
};

// 从产品数据中提取保费范围的数值
const extractPremiumRange = (product) => {
    try {
        console.log('正在提取保费范围，产品ID:', product.id);

        // 尝试从基本信息中查找基本保费限制
        const basicInfo = product.productDetails?.find(section => section.name === '基本信息');
        if (basicInfo) {
            console.log('找到基本信息部分');
            const premiumAttr = basicInfo.attributes?.find(attr =>
                attr.name === '基本保费限制' ||
                attr.name === '保费限制' ||
                attr.name.includes('保费')
            );
            if (premiumAttr && premiumAttr.value) {
                console.log('找到保费属性:', premiumAttr.name, '值:', premiumAttr.value);

                // 尝试提取最低和最高保费 - 支持多种格式
                let matches = premiumAttr.value.match(/最低[：:]\s*([\d,]+)港元\/([\d,]+)美元\s*最高[：:]\s*([\d,]+)港元\/([\d,]+)美元/);
                if (matches && matches.length >= 5) {
                    const result = {
                        min: parseInt(matches[1].replace(/,/g, '')),
                        max: parseInt(matches[3].replace(/,/g, ''))
                    };
                    console.log('提取保费范围成功(港元/美元格式):', result);
                    return result;
                }

                // 尝试提取 "最低：xxx 最高：xxx" 格式
                matches = premiumAttr.value.match(/最低[：:]\s*([\d,]+)\s*最高[：:]\s*([\d,]+)/);
                if (matches && matches.length >= 3) {
                    const result = {
                        min: parseInt(matches[1].replace(/,/g, '')),
                        max: parseInt(matches[2].replace(/,/g, ''))
                    };
                    console.log('提取保费范围成功(最低/最高格式):', result);
                    return result;
                }

                // 尝试匹配任何数字
                const allNumbers = premiumAttr.value.match(/\d+,?\.?\d*/g);
                if (allNumbers && allNumbers.length >= 2) {
                    // 将带逗号的数字字符串转换为数字
                    const values = allNumbers.map(n => {
                        try {
                            return parseInt(n.replace(/,/g, ''));
                        } catch (e) {
                            console.error('解析数字错误:', n, e);
                            return 0;
                        }
                    }).filter(n => n > 0); // 过滤掉无效值

                    if (values.length >= 2) {
                        const result = {
                            min: Math.min(...values),
                            max: Math.max(...values)
                        };
                        console.log('提取保费范围成功(提取所有数字):', result);
                        return result;
                    }
                }
            }
        }

        // 如果没找到或解析失败，尝试从premiumRange获取
        if (product.premiumRange) {
            console.log('尝试从premiumRange属性获取:', product.premiumRange);
            if (Array.isArray(product.premiumRange) && product.premiumRange.length >= 2) {
                const result = {
                    min: parseFloat(product.premiumRange[0]),
                    max: parseFloat(product.premiumRange[1])
                };
                console.log('从premiumRange提取结果:', result);
                return result;
            }
        }

        // 尝试从子产品获取
        if (product.subProducts && product.subProducts.length > 0) {
            console.log('尝试从子产品获取保费范围');
            for (const subProduct of product.subProducts) {
                if (subProduct.premiumRange && Array.isArray(subProduct.premiumRange) && subProduct.premiumRange.length >= 2) {
                    const result = {
                        min: parseFloat(subProduct.premiumRange[0]),
                        max: parseFloat(subProduct.premiumRange[1])
                    };
                    console.log('从子产品提取保费范围结果:', result);
                    return result;
                }
            }
        }

        // 默认返回默认值
        console.log('无法提取保费范围，使用默认值');
        return { min: 10000, max: 1000000 };
    } catch (error) {
        console.error('提取保费范围出错:', error);
        return { min: 10000, max: 1000000 };
    }
};

// 提取雷达图数据
const getRadarChartData = () => {
    console.log('提取雷达图数据，产品数量:', props.products.length);
    if (props.products.length === 0) {
        console.log('没有产品数据，返回空数据');
        return { indicators: [], series: [] };
    }

    // 雷达图指标（从所有产品的关键特性中提取）
    const indicators = [
        { name: '保障范围', max: 5 },
        { name: '保障期限', max: 5 },
        { name: '投保年龄', max: 5 },
        { name: '投保额度', max: 5 },
        { name: '货币选择', max: 5 },
        { name: '缴费方式', max: 5 }
    ];

    const series = [];

    // 为每个产品创建一个数据系列
    props.products.forEach((product, index) => {
        try {
            if (!product) {
                console.warn(`第${index + 1}个产品为空`);
                return;
            }

            console.log(`处理第${index + 1}个产品:`, product.id);
            const productName = props.selectedProducts[index]?.productName || `产品${index + 1}`;
            console.log('产品名称:', productName);

            // 提取产品特点
            let guaranteePeriodScore = 3; // 默认中等评分
            let ageRangeScore = 3;
            let currencyScore = 2;
            let paymentMethodScore = 2;
            let coverageScore = 3;
            let premiumScore = 3;

            try {
                // 保障期限评分
                console.log('保障期限:', product.guaranteePeriod);
                if (product.guaranteePeriod === '终身') {
                    guaranteePeriodScore = 5; // 终身保障得最高分
                    console.log('终身保障，评分:', guaranteePeriodScore);
                }

                // 投保年龄范围评分
                const ageRange = extractAgeRange(product);
                console.log('年龄范围:', ageRange);
                if (ageRange.max >= 80) {
                    ageRangeScore = 5; // 年龄上限高得高分
                } else if (ageRange.max >= 65) {
                    ageRangeScore = 4;
                }
                console.log('年龄范围评分:', ageRangeScore);

                // 货币选择评分
                if (product.currencies && Array.isArray(product.currencies)) {
                    currencyScore = Math.min(5, product.currencies.length);
                    console.log('货币选择:', product.currencies, '评分:', currencyScore);
                }

                // 缴费方式评分
                if (product.paymentMethods && Array.isArray(product.paymentMethods)) {
                    paymentMethodScore = Math.min(5, product.paymentMethods.length);
                    console.log('缴费方式:', product.paymentMethods, '评分:', paymentMethodScore);
                }

                // 保障范围评分(基于产品详情部分数量)
                if (product.productDetails && Array.isArray(product.productDetails)) {
                    coverageScore = Math.min(5, Math.max(1, product.productDetails.length));
                    console.log('保障范围评分:', coverageScore);
                }

                // 保费范围评分
                const premiumRange = extractPremiumRange(product);
                console.log('保费范围:', premiumRange);
                if (premiumRange.max >= 5000000) {
                    premiumScore = 5; // 保费上限高得高分
                } else if (premiumRange.max >= 1000000) {
                    premiumScore = 4;
                }
                console.log('保费范围评分:', premiumScore);
            } catch (error) {
                console.error('评分计算出错:', error);
            }

            // 组装雷达图数据
            series.push({
                name: productName,
                value: [
                    coverageScore,     // 保障范围
                    guaranteePeriodScore, // 保障期限
                    ageRangeScore,     // 投保年龄
                    premiumScore,      // 投保额度
                    currencyScore,     // 货币选择
                    paymentMethodScore // 缴费方式
                ]
            });
            console.log('添加产品系列数据:', productName);
        } catch (error) {
            console.error(`处理产品${index + 1}出错:`, error);
        }
    });

    console.log('雷达图数据准备完成, 系列数:', series.length);
    return {
        indicators,
        series
    };
};

// 提取柱状图数据（年龄范围和保费范围）
const getBarChartData = () => {
    console.log('提取柱状图数据，产品数量:', props.products.length);
    if (props.products.length === 0) {
        console.log('没有产品数据，返回空数据');
        return { xAxis: [], series: [] };
    }

    try {
        const productNames = props.products.map((product, index) => {
            try {
                return props.selectedProducts[index]?.productName || `产品${index + 1}`;
            } catch (error) {
                console.error(`获取产品${index + 1}名称失败:`, error);
                return `产品${index + 1}`;
            }
        });
        console.log('产品名称列表:', productNames);

        // 提取最小投保年龄
        const minAges = props.products.map((product, index) => {
            try {
                const ageRange = extractAgeRange(product);
                return ageRange.min;
            } catch (error) {
                console.error(`提取产品${index + 1}最小投保年龄失败:`, error);
                return 0;
            }
        });
        console.log('最小投保年龄列表:', minAges);

        // 提取最大投保年龄
        const maxAges = props.products.map((product, index) => {
            try {
                const ageRange = extractAgeRange(product);
                return ageRange.max;
            } catch (error) {
                console.error(`提取产品${index + 1}最大投保年龄失败:`, error);
                return 80;
            }
        });
        console.log('最大投保年龄列表:', maxAges);

        // 提取最低保费(以对数表示，便于展示)
        const minPremiums = props.products.map((product, index) => {
            try {
                const premiumRange = extractPremiumRange(product);
                return premiumRange.min > 0 ? Math.log10(premiumRange.min) : 0;
            } catch (error) {
                console.error(`提取产品${index + 1}最低保费失败:`, error);
                return 0;
            }
        });
        console.log('最低保费(对数)列表:', minPremiums);

        // 提取最高保费(以对数表示，便于展示)
        const maxPremiums = props.products.map((product, index) => {
            try {
                const premiumRange = extractPremiumRange(product);
                return premiumRange.max > 0 ? Math.log10(premiumRange.max) : 0;
            } catch (error) {
                console.error(`提取产品${index + 1}最高保费失败:`, error);
                return 0;
            }
        });
        console.log('最高保费(对数)列表:', maxPremiums);

        return {
            productNames,
            series: [
                {
                    name: '最小投保年龄',
                    type: 'bar',
                    stack: 'age',
                    emphasis: { focus: 'series' },
                    data: minAges
                },
                {
                    name: '最大投保年龄',
                    type: 'bar',
                    stack: 'age',
                    emphasis: { focus: 'series' },
                    data: maxAges
                },
                {
                    name: '最低保费(对数)',
                    type: 'bar',
                    stack: 'premium',
                    emphasis: { focus: 'series' },
                    data: minPremiums
                },
                {
                    name: '最高保费(对数)',
                    type: 'bar',
                    stack: 'premium',
                    emphasis: { focus: 'series' },
                    data: maxPremiums
                }
            ]
        };
    } catch (error) {
        console.error('提取柱状图数据失败:', error);
        return { productNames: [], series: [] };
    }
};

// 提取饼图数据（承保特性）
const getPieChartData = () => {
    if (props.products.length === 0) return { data: [] };

    // 统计所有产品的承保特性
    const underwritingFeatures = {};

    props.products.forEach(product => {
        try {
            // 查找承保相关部分
            const underwritingSection = product.productDetails?.find(section =>
                section.name === '承保相關' || section.name === '承保相关'
            );

            if (underwritingSection && underwritingSection.attributes) {
                underwritingSection.attributes.forEach(attr => {
                    // 只统计"适用"的特性
                    if (attr.value === '適用' || attr.value === '适用') {
                        if (!underwritingFeatures[attr.name]) {
                            underwritingFeatures[attr.name] = 0;
                        }
                        underwritingFeatures[attr.name]++;
                    }
                });
            }
        } catch (error) {
            console.error('提取承保特性出错:', error);
        }
    });

    // 转换为饼图数据格式
    const data = Object.keys(underwritingFeatures).map(name => ({
        name,
        value: underwritingFeatures[name]
    }));

    return { data };
};

// 提取货币分布数据
const getCurrencyDistributionData = () => {
    if (props.products.length === 0) return { data: [] };

    // 统计所有产品支持的货币
    const currencyCount = {};

    props.products.forEach(product => {
        try {
            if (product.currencies && Array.isArray(product.currencies)) {
                product.currencies.forEach(currency => {
                    if (!currencyCount[currency]) {
                        currencyCount[currency] = 0;
                    }
                    currencyCount[currency]++;
                });
            }
        } catch (error) {
            console.error('提取货币分布出错:', error);
        }
    });

    // 转换为饼图数据格式
    const data = Object.keys(currencyCount).map(name => ({
        name,
        value: currencyCount[name]
    }));

    return { data };
};

// 初始化雷达图
const initRadarChart = () => {
    console.log('初始化雷达图');
    try {
        if (radarChart) {
            console.log('销毁旧的雷达图实例');
            radarChart.dispose();
            radarChart = null;
        }

        if (!radarChartRef.value) {
            console.warn('雷达图DOM引用不存在，无法初始化图表');
            return;
        }

        console.log('创建新的雷达图实例');
        radarChart = markRaw(echarts.init(radarChartRef.value));
        updateRadarChart();
    } catch (error) {
        console.error('初始化雷达图失败:', error);
    }
};

// 更新雷达图数据
const updateRadarChart = () => {
    console.log('更新雷达图数据');
    if (!radarChart) {
        console.warn('雷达图实例不存在，无法更新数据');
        return;
    }

    try {
        const { indicators, series } = getRadarChartData();
        console.log('雷达图数据:', { indicators, seriesLength: series.length });

        const option = {
            tooltip: {
                trigger: 'item',
                formatter: (params) => {
                    let result = `<div style="font-weight: bold">${params.name}</div>`;
                    params.value.forEach((val, index) => {
                        if (indicators[index]) {
                            result += `<div>${indicators[index].name}: ${val}/5</div>`;
                        }
                    });
                    return result;
                }
            },
            legend: {
                type: 'scroll',
                bottom: 0,
                data: series.map(item => item.name),
                selected: {}, // 默认全选中
                selector: [
                    {
                        type: 'all',
                        title: '全选'
                    },
                    {
                        type: 'inverse',
                        title: '反选'
                    }
                ]
            },
            radar: {
                indicator: indicators,
                radius: '65%',
                center: ['50%', '50%'],
                splitNumber: 5,
                splitArea: {
                    areaStyle: {
                        color: ['rgba(255, 255, 255, 0.5)']
                    }
                },
                name: {
                    textStyle: {
                        color: '#333',
                        fontSize: 12
                    }
                },
                axisLine: {
                    lineStyle: {
                        color: 'rgba(0, 0, 0, 0.2)'
                    }
                }
            },
            series: [
                {
                    type: 'radar',
                    emphasis: {
                        lineStyle: {
                            width: 4
                        }
                    },
                    data: series.map((item, index) => ({
                        value: item.value,
                        name: item.name,
                        itemStyle: {
                            color: getProductColor(index)
                        },
                        lineStyle: {
                            width: 2,
                            color: getProductColor(index)
                        },
                        areaStyle: {
                            color: getProductColor(index),
                            opacity: 0.3
                        }
                    }))
                }
            ],
            color: colorPalette
        };

        radarChart.setOption(option);
    } catch (error) {
        console.error('更新雷达图失败:', error);
    }
};

// 初始化柱状图
const initBarChart = () => {
    console.log('初始化柱状图');
    try {
        if (barChart) {
            console.log('销毁旧的柱状图实例');
            barChart.dispose();
            barChart = null;
        }

        if (!barChartRef.value) {
            console.warn('柱状图DOM引用不存在，无法初始化图表');
            return;
        }

        console.log('创建新的柱状图实例');
        barChart = markRaw(echarts.init(barChartRef.value));
        updateBarChart();
    } catch (error) {
        console.error('初始化柱状图失败:', error);
    }
};

// 更新柱状图数据
const updateBarChart = () => {
    console.log('更新柱状图数据');
    if (!barChart) {
        console.warn('柱状图实例不存在，无法更新数据');
        return;
    }

    try {
        const { productNames, series } = getBarChartData();
        console.log('柱状图数据:', { productNames, seriesLength: series.length });

        const option = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                },
                formatter: function (params) {
                    let html = `<div style="font-weight:bold;margin-bottom:5px;">${params[0].name}</div>`;

                    params.forEach(param => {
                        const seriesName = param.seriesName;
                        let value = param.value;

                        // 对保费数据进行特殊处理，将对数值转回原值
                        if (seriesName.includes('保费')) {
                            value = Math.pow(10, value).toLocaleString();
                            html += `${param.marker}${seriesName.replace('(对数)', '')}: ${value}<br/>`;
                        } else {
                            html += `${param.marker}${seriesName}: ${value}<br/>`;
                        }
                    });

                    return html;
                }
            },
            legend: {
                data: ['最小投保年龄', '最大投保年龄', '最低保费(对数)', '最高保费(对数)'],
                type: 'scroll',
                bottom: 0,
                selector: [
                    {
                        type: 'all',
                        title: '全选'
                    },
                    {
                        type: 'inverse',
                        title: '反选'
                    }
                ]
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '15%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: productNames,
                axisLabel: {
                    interval: 0,
                    rotate: productNames.length > 3 ? 45 : 30, // 产品多时增加旋转角度
                    formatter: function (value) {
                        const maxLength = productNames.length > 3 ? 8 : 10; // 产品多时减少显示长度
                        if (value.length > maxLength) {
                            return value.substring(0, maxLength) + '...';
                        }
                        return value;
                    }
                }
            },
            yAxis: [
                {
                    type: 'value',
                    name: '年龄',
                    position: 'left',
                    axisLabel: {
                        formatter: '{value}'
                    }
                },
                {
                    type: 'value',
                    name: '保费对数',
                    position: 'right',
                    axisLabel: {
                        formatter: '{value}'
                    }
                }
            ],
            series: [
                {
                    ...series[0],
                    yAxisIndex: 0,
                    itemStyle: { color: colorPalette[0] }
                },
                {
                    ...series[1],
                    yAxisIndex: 0,
                    itemStyle: { color: colorPalette[1] }
                },
                {
                    ...series[2],
                    yAxisIndex: 1,
                    itemStyle: { color: colorPalette[2] }
                },
                {
                    ...series[3],
                    yAxisIndex: 1,
                    itemStyle: { color: colorPalette[3] }
                }
            ],
            color: colorPalette
        };

        barChart.setOption(option);
    } catch (error) {
        console.error('更新柱状图失败:', error);
    }
};

// 初始化饼图
const initPieChart = () => {
    console.log('初始化饼图');
    try {
        if (pieChart) {
            console.log('销毁旧的饼图实例');
            pieChart.dispose();
            pieChart = null;
        }

        if (!pieChartRef.value) {
            console.warn('饼图DOM引用不存在，无法初始化图表');
            return;
        }

        console.log('创建新的饼图实例');
        pieChart = markRaw(echarts.init(pieChartRef.value));
        updatePieChart();
    } catch (error) {
        console.error('初始化饼图失败:', error);
    }
};

// 更新饼图数据
const updatePieChart = () => {
    console.log('更新饼图数据');
    if (!pieChart) {
        console.warn('饼图实例不存在，无法更新数据');
        return;
    }

    try {
        const { data } = getPieChartData();
        console.log('饼图数据:', { dataLength: data.length });

        // 对数据进行排序，使较大的值排在前面，改善图例显示
        const sortedData = [...data].sort((a, b) => b.value - a.value);

        // 计算图例的位置，根据数据项数量动态调整
        const legendPosition = {
            right: data.length > 8 ? '5%' : '10%',
            top: data.length > 8 ? '5%' : '20%',
            bottom: data.length > 8 ? '5%' : '20%',
            // 当数据过多时，减小图例项之间的间距
            itemGap: data.length > 8 ? 5 : 10
        };

        const option = {
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                type: 'scroll',
                orient: 'vertical',
                ...legendPosition,
                formatter: function (name) {
                    // 当图例项太多时，缩短名称
                    if (data.length > 8 && name.length > 10) {
                        return name.substring(0, 10) + '...';
                    }
                    return name;
                },
                tooltip: {
                    show: true,
                    formatter: function (params) {
                        return params.name;
                    }
                },
                data: sortedData.map(item => item.name)
            },
            series: [
                {
                    name: '承保特性',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '16',
                            fontWeight: 'bold',
                            formatter: '{b}\n{c} ({d}%)'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: sortedData
                }
            ],
            color: colorPalette
        };

        pieChart.setOption(option);
    } catch (error) {
        console.error('更新饼图失败:', error);
    }
};

// 初始化货币分布图
const initCurrencyChart = () => {
    console.log('初始化货币分布图');
    try {
        if (currencyChart) {
            console.log('销毁旧的货币分布图实例');
            currencyChart.dispose();
            currencyChart = null;
        }

        if (!currencyChartRef.value) {
            console.warn('货币分布图DOM引用不存在，无法初始化图表');
            return;
        }

        console.log('创建新的货币分布图实例');
        currencyChart = markRaw(echarts.init(currencyChartRef.value));
        updateCurrencyChart();
    } catch (error) {
        console.error('初始化货币分布图失败:', error);
    }
};

// 更新货币分布图
const updateCurrencyChart = () => {
    console.log('更新货币分布图数据');
    if (!currencyChart) {
        console.warn('货币分布图实例不存在，无法更新数据');
        return;
    }

    try {
        const { data } = getCurrencyDistributionData();
        console.log('货币分布图数据:', { dataLength: data.length });

        // 对数据进行排序，使较大的值排在前面
        const sortedData = [...data].sort((a, b) => b.value - a.value);

        // 根据货币种类自动调整布局
        const isSmallDataSet = data.length <= 5;

        // 当货币数量少时，显示标签；当数量多时，隐藏标签以避免重叠
        const showLabels = isSmallDataSet;

        const option = {
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                type: 'scroll',
                orient: 'vertical',
                right: '10%',
                top: '20%',
                bottom: '20%',
                // 当货币种类较多时显示滚动条
                pageButtonPosition: data.length > 6 ? 'end' : 'start',
                data: sortedData.map(item => item.name)
            },
            series: [
                {
                    name: '货币支持',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: showLabels,
                        position: 'outside',
                        formatter: '{b}: {c}',
                        fontSize: 12
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '16',
                            fontWeight: 'bold',
                            formatter: '{b}\n{c} ({d}%)'
                        },
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    },
                    labelLine: {
                        show: showLabels,
                        length: 10,
                        length2: 10
                    },
                    data: sortedData
                }
            ],
            color: colorPalette
        };

        currencyChart.setOption(option);
    } catch (error) {
        console.error('更新货币分布图失败:', error);
    }
};

// 防抖函数，避免频繁resize导致性能问题
const debounce = (fn, delay) => {
    let timer = null;
    return function () {
        const context = this;
        const args = arguments;
        clearTimeout(timer);
        timer = setTimeout(() => {
            fn.apply(context, args);
        }, delay);
    };
};

// 监听窗口大小变化，重新调整图表大小
const handleResize = debounce(() => {
    if (radarChart) radarChart.resize();
    if (barChart) barChart.resize();
    if (pieChart) pieChart.resize();
    if (currencyChart) currencyChart.resize();
}, 200); // 200ms防抖

// 更新所有图表的函数
const updateAllCharts = () => {
    console.log('更新所有图表，hasData:', hasData.value);
    if (hasData.value) {
        // 如果图表实例不存在，先创建实例
        if (!radarChart && radarChartRef.value) {
            console.log('雷达图实例不存在，初始化雷达图');
            initRadarChart();
        } else {
            updateRadarChart();
        }

        if (!barChart && barChartRef.value) {
            console.log('柱状图实例不存在，初始化柱状图');
            initBarChart();
        } else {
            updateBarChart();
        }

        if (!pieChart && pieChartRef.value) {
            console.log('饼图实例不存在，初始化饼图');
            initPieChart();
        } else {
            updatePieChart();
        }

        if (!currencyChart && currencyChartRef.value) {
            console.log('货币分布图实例不存在，初始化货币分布图');
            initCurrencyChart();
        } else {
            updateCurrencyChart();
        }
    } else {
        console.log('没有足够数据显示图表');
    }
};

// 监听产品数据变化，更新图表
watch(
    () => props.products,
    (newVal) => {
        console.log('产品数据变化:', newVal);
        console.log('产品数量:', newVal.length);
        if (newVal.length > 0) {
            console.log('第一个产品数据示例:', JSON.stringify(newVal[0], null, 2).substring(0, 300) + '...');
        }
        updateAllCharts();
    },
    { deep: true }
);

// 监听加载状态变化
watch(
    () => props.loading,
    (newVal) => {
        console.log('加载状态变化:', newVal);
        if (!newVal && hasData.value) {
            console.log('加载完成，开始更新图表，hasData:', hasData.value);
            updateAllCharts();
        }
    }
);

// 监听图表高度变化，重新调整图表大小
watch(
    () => getChartHeight.value,
    () => {
        // 使用nextTick确保DOM已更新
        nextTick(() => {
            handleResize();
        });
    }
);

onMounted(() => {
    console.log('组件挂载，检查图表初始化条件');
    // 确保DOM已完全渲染后再初始化图表
    nextTick(() => {
        console.log('组件DOM已渲染，检查是否有数据:', hasData.value);
        if (hasData.value) {
            console.log('有数据，开始初始化图表');
            // 双重保险：延迟一帧再初始化，确保图表容器已完全渲染
            setTimeout(() => {
                console.log('延迟初始化图表');
                initRadarChart();
                initBarChart();
                initPieChart();
                initCurrencyChart();
            }, 0);
        } else {
            console.log('没有足够数据，暂不初始化图表');
        }
    });

    // 添加窗口大小调整监听
    window.addEventListener('resize', handleResize);
    console.log('添加窗口大小调整监听');
});

onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
    if (radarChart) radarChart.dispose();
    if (barChart) barChart.dispose();
    if (pieChart) pieChart.dispose();
    if (currencyChart) currencyChart.dispose();
});
</script>

<style scoped>
/* 图表容器样式 */
.chart-container {
    width: 100%;
    height: 100%;
}
</style>