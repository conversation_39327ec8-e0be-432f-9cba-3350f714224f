<template>
    <div class="comparison-area">
        <!-- 产品标题行 -->
        <div :class="getGridClass()" class="border-b border-gray-200 product-title-row">
            <div class="p-4 bg-gray-50 flex items-center border-r border-gray-100">
                <span class="text-gray-700 font-medium">产品名称</span>
            </div>
            <div v-for="(product, index) in products" :key="'title-' + index"
                class="p-4 flex flex-col items-start justify-center product-title-cell"
                :class="{ 'border-r border-gray-100': index < products.length - 1 }">
                <h3 class="font-semibold text-gray-800 w-full product-name"
                    :title="selectedProducts[index]?.productName">
                    {{ selectedProducts[index]?.productName }}
                </h3>
                <div class="text-sm text-gray-600 mt-1 w-full company-name"
                    :title="selectedProducts[index]?.companyName">
                    {{ selectedProducts[index]?.companyName }}
                </div>
            </div>
        </div>

        <!-- 基本信息 -->
        <div :class="getGridRowClass()" class="border-b border-gray-100">
            <div class="p-4 bg-gray-50 flex items-center border-r border-gray-100">
                <span class="text-gray-600 font-medium">险种类型</span>
            </div>
            <div v-for="(product, index) in products" :key="'category-' + index" class="p-4 flex items-center"
                :class="{ 'border-r border-gray-100': index < products.length - 1 }">
                <span class="font-medium truncate w-full" :title="selectedProducts[index]?.categoryName">
                    {{ selectedProducts[index]?.categoryName }}
                </span>
            </div>
        </div>

        <div :class="getGridRowClass()" class="border-b border-gray-100">
            <div class="p-4 bg-gray-50 flex items-center border-r border-gray-100">
                <span class="text-gray-600 font-medium">产品状态</span>
            </div>
            <div v-for="(product, index) in products" :key="'status-' + index" class="p-4 flex items-center"
                :class="{ 'border-r border-gray-100': index < products.length - 1 }">
                <a-tag :color="selectedProducts[index]?.status === 1 ? 'success' : 'default'">
                    {{ selectedProducts[index]?.status === 1 ? '在售' : '停售' }}
                </a-tag>
            </div>
        </div>

        <!-- 产品详情对比 -->
        <template v-for="(section, secIndex) in mergedSections" :key="section.name">
            <div :class="getGridRowClass()" class="border-b border-gray-200 bg-gray-100">
                <div class="p-4 col-span-full font-medium text-xl text-gray-700">{{ section.name }}</div>
            </div>

            <template v-for="(attr, attrIndex) in section.attributes" :key="attr.name">
                <div :class="[getGridRowClass(), { 'bg-gray-50': attrIndex % 2 === 0 }]"
                    class="border-b border-gray-100">
                    <div class="p-4 flex items-start border-r border-gray-100">
                        <span class="text-gray-600">{{ attr.name }}</span>
                    </div>
                    <div v-for="(product, index) in products" :key="'attr-' + section.name + '-' + index"
                        class="p-4 flex items-start whitespace-pre-wrap" :class="{
                            'border-r border-gray-100': index < products.length - 1,
                            'font-medium': products.length > 1 && index > 0 && isAttributeDifferent(section.name, attr.name, index)
                        }">
                        <span class="attr-value">{{ getAttributeDisplayValue(product, section.name, attr.name) }}</span>
                    </div>
                </div>
            </template>
        </template>

        <!-- 加载中或无数据状态 -->
        <div v-if="products.length === 0" class="p-12 text-center text-gray-500">
            <a-spin v-if="loading" :indicator="loadingIcon">
                <div class="mt-4 text-gray-500">加载产品数据中...</div>
            </a-spin>
            <template v-else>
                <Icon icon="material-symbols:info-outline" class="text-4xl text-gray-400" />
                <div class="mt-2">暂无详细对比数据</div>
            </template>
        </div>
    </div>
</template>

<script setup>
import { computed, h } from 'vue';
import { Icon } from '@iconify/vue';
import { LoadingOutlined } from '@ant-design/icons-vue';

const props = defineProps({
    selectedProducts: {
        type: Array,
        required: true
    },
    products: {
        type: Array,
        required: true
    },
    loading: {
        type: Boolean,
        default: false
    }
});

// 加载图标
const loadingIcon = h(LoadingOutlined, { style: { fontSize: '24px' } });

// 合并所有产品的部分，生成统一的比较结构
const mergedSections = computed(() => {
    if (!props.products.length) return [];

    const allSections = new Map();

    // 遍历所有产品的所有部分
    props.products.forEach(product => {
        if (!product.productDetails) return;

        product.productDetails.forEach(section => {
            if (!allSections.has(section.name)) {
                // 保留部分的原始信息
                allSections.set(section.name, {
                    name: section.name,
                    rankValue: section.rankValue,
                    attributes: new Map()
                });
            }

            // 添加该部分的所有属性
            section.attributes?.forEach(attr => {
                const sectionData = allSections.get(section.name);
                if (!sectionData.attributes.has(attr.name)) {
                    // 保存完整的属性信息到Map中
                    sectionData.attributes.set(attr.name, {
                        name: attr.name,
                        rankValue: attr.rankValue,
                        value: attr.value,
                        attribute: attr.attribute
                    });
                }
            });
        });
    });

    // 将Map转换为数组并排序
    const sortedSections = Array.from(allSections.values())
        .sort((a, b) => a.rankValue - b.rankValue)
        .map(section => ({
            ...section,
            attributes: Array.from(section.attributes.values())
                .sort((a, b) => a.rankValue - b.rankValue)
        }));

    return sortedSections;
});

// 获取特定产品、特定部分的特定属性值
const getAttributeValue = (product, sectionName, attrName) => {
    if (!product || !product.productDetails) return null;

    const section = product.productDetails.find(s => s.name === sectionName);
    if (!section || !section.attributes) return null;

    // 查找完整的属性对象
    const attribute = section.attributes.find(a => a.name === attrName);

    // 返回完整的属性对象
    return attribute || null;
};

// 获取属性的显示值
const getAttributeDisplayValue = (product, sectionName, attrName) => {
    const attr = getAttributeValue(product, sectionName, attrName);
    if (!attr) return '-';

    // 返回属性值
    return attr.value || '-';
};

// 判断某个属性值是否与主产品不同
const isAttributeDifferent = (sectionName, attrName, productIndex) => {
    if (!props.products.length || productIndex < 1) return false;

    const mainValue = getAttributeDisplayValue(props.products[0], sectionName, attrName);
    const compareValue = getAttributeDisplayValue(props.products[productIndex], sectionName, attrName);

    // 如果值不同，返回true
    return mainValue !== compareValue && mainValue !== '-' && compareValue !== '-';
};

// 获取网格类名
const getGridClass = () => {
    // 如果没有产品，返回默认值
    if (props.products.length === 0) return 'grid grid-cols-2';

    // 最多显示6个产品，加上属性名列，最多7列
    const colCount = Math.min(props.products.length, 6) + 1;

    // 如果只有一个产品，就显示两列（属性名+产品）
    if (props.products.length === 1) return 'grid grid-cols-2';

    return `grid grid-cols-${colCount}`;
};

// 获取网格行类名
const getGridRowClass = () => {
    // 如果没有产品，返回默认值
    if (props.products.length === 0) return 'grid grid-cols-2';

    // 最多显示6个产品，加上属性名列，最多7列
    const colCount = Math.min(props.products.length, 6) + 1;

    // 如果只有一个产品，就显示两列（属性名+产品）
    if (props.products.length === 1) return 'grid grid-cols-2';

    return `grid grid-cols-${colCount}`;
};
</script>

<style scoped>
/* 比较结果表格样式 */
.comparison-area {
    overflow-x: auto;
}

/* 产品标题行样式 */
.product-title-row {
    min-height: 85px;
}

.product-title-row>div {
    min-height: 85px;
}

/* 产品标题单元格样式 */
.product-title-cell {
    display: flex !important;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    height: 100%;
}

/* 产品名称样式 */
.product-name {
    word-break: break-word;
    line-height: 1.4;
    max-height: 2.8em;
    /* 限制最多显示2行 */
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
}

/* 公司名称样式 */
.company-name {
    word-break: break-word;
    line-height: 1.3;
    max-height: 1.3em;
    /* 限制显示1行 */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 支持多列布局 */
.grid.grid-cols-2 {
    grid-template-columns: 200px 1fr;
}

.grid.grid-cols-3 {
    grid-template-columns: 200px 1fr 1fr;
}

.grid.grid-cols-4 {
    grid-template-columns: 200px 1fr 1fr 1fr;
}

.grid.grid-cols-5 {
    grid-template-columns: 200px 1fr 1fr 1fr 1fr;
}

.grid.grid-cols-6 {
    grid-template-columns: 200px 1fr 1fr 1fr 1fr 1fr;
}

.grid.grid-cols-7 {
    grid-template-columns: 200px 1fr 1fr 1fr 1fr 1fr 1fr;
}

.grid>div {
    display: flex;
    word-break: break-word;
    min-height: 46px;
}

.grid>div:first-child {
    font-weight: 500;
}

.grid>div:nth-child(n+2) {
    justify-content: flex-start;
}

.col-span-full {
    grid-column: 1 / -1;
}

.whitespace-pre-wrap {
    white-space: pre-wrap;
    line-height: 1.5;
}

.attr-value {
    max-width: 100%;
    word-break: break-word;
}

/* 响应式适配 */
@media (max-width: 1023px) {

    .grid.grid-cols-2,
    .grid.grid-cols-3,
    .grid.grid-cols-4,
    .grid.grid-cols-5,
    .grid.grid-cols-6,
    .grid.grid-cols-7 {
        grid-template-columns: repeat(2, 1fr);
    }

    .border-r {
        border-right-width: 0;
        border-bottom: 1px solid #f3f4f5;
    }
}

@media (max-width: 640px) {

    .grid.grid-cols-2,
    .grid.grid-cols-3,
    .grid.grid-cols-4,
    .grid.grid-cols-5,
    .grid.grid-cols-6,
    .grid.grid-cols-7 {
        grid-template-columns: 150px 1fr;
    }
}
</style>