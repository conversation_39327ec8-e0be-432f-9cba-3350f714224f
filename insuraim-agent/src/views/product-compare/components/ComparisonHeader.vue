<template>
    <div class="flex items-center justify-between px-8 py-5 border-b bg-gradient-to-r from-blue-50 to-indigo-50">
        <div class="flex items-center">
            <Icon icon="material-symbols:compare-arrows" class="text-2xl text-blue-500 mr-3" />
            <h2 class="text-xl font-semibold text-gray-800">产品对比结果</h2>
        </div>
        <a-button type="primary" class="bg-blue-500 border-none text-white rounded-button hover:bg-blue-600"
            @click="$emit('reset')">
            <template #icon>
                <Icon icon="material-symbols:refresh" />
            </template>
            重置对比
        </a-button>
    </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';

defineEmits(['reset']);
</script>

<style scoped>
/* 添加圆角按钮样式 */
.rounded-button {
    border-radius: 4px !important;
    transition: all 0.2s ease-in-out;
}

/* 按钮悬停效果 */
.rounded-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}
</style>