<template>
    <transition name="fade">
        <div v-if="show" class="bg-white rounded-lg shadow-sm overflow-hidden mb-8">
            <component :is="Components.ComparisonHeader" @reset="$emit('reset')" />
            <component :is="Components.ComparisonGrid" :selected-products="selectedProducts" :products="products"
                :loading="loading" />
        </div>
    </transition>
</template>

<script setup>
import { markRaw } from 'vue';
import ComparisonHeader from './ComparisonHeader.vue';
import ComparisonGrid from './ComparisonGrid.vue';

// 使用markRaw防止组件被Vue响应式系统处理
const Components = {
    ComparisonHeader: markRaw(ComparisonHeader),
    ComparisonGrid: markRaw(ComparisonGrid)
};

const props = defineProps({
    show: {
        type: Boolean,
        default: false
    },
    selectedProducts: {
        type: Array,
        required: true
    },
    products: {
        type: Array,
        required: true
    },
    loading: {
        type: Boolean,
        default: false
    }
});

defineEmits(['reset']);
</script>

<style scoped>
/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s, transform 0.3s;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
    transform: translateY(10px);
}
</style>