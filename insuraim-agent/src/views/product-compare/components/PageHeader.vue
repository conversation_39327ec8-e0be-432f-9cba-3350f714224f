<template>
    <div
        class="title-section mb-6 p-6 rounded-lg shadow-md text-white bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700">
        <div class="flex items-center">
            <Icon icon="material-symbols:compare-arrows" class="text-4xl mr-3" />
            <h1 class="text-2xl font-bold page-title">{{ title }}</h1>
        </div>
        <p class="mt-2 page-description">
            {{ description }}
        </p>
    </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';

defineProps({
    title: {
        type: String,
        default: '产品对比'
    },
    description: {
        type: String,
        default: '多维度比较不同保险产品的特性和优势，帮助您做出更明智的选择。'
    }
});
</script>

<style scoped>
.title-section {
    background-size: 200% 200%;
    animation: gradientAnimation 5s ease infinite;
}

@keyframes gradientAnimation {
    0% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

.page-title {
    color: #fff;
}

.page-description {
    color: #e0e0e0;
}
</style>