<template>
    <!-- 筛选条件 -->
    <div class="relative mb-6">
        <div class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-primary transition-colors">
            <Icon icon="material-symbols:search" class="text-gray-400 mr-3" />
            <input type="text" placeholder="搜索关键词" class="w-full outline-none text-sm" v-model="searchText"
                @keyup.enter="handleSearch">
        </div>

        <div class="flex flex-wrap items-center gap-3 mt-3">
            <span class="mr-2 text-gray-600 text-sm">{{ t('products.region') }}:</span>
            <a-select v-model:value="modelValue.region" placeholder="地区" style="min-width: 100px;" size="middle"
                @change="handleSearch" class="filter-select">
                <a-select-option value="">全部</a-select-option>
                <a-select-option v-for="item in regionOptions" :key="item.code" :value="item.name">
                    {{ item.name }}
                </a-select-option>
            </a-select>

            <span class="mr-2 text-gray-600 text-sm">保司:</span>
            <a-select v-model:value="companyValue" placeholder="选择保司" style="min-width: 180px;" size="middle"
                @change="handleCompanyFilterChange" allowClear show-search :filter-option="false"
                @search="handleCompanySearch" class="filter-select" :options="filteredCompanyList">
            </a-select>

            <span class="mr-2 text-gray-600 text-sm">{{ t('products.category') }}:</span>
            <a-select v-model:value="modelValue.categoryCode" placeholder="险种" style="min-width: 120px;" size="middle"
                @change="handleSearch" class="filter-select" :disabled="disabled">
                <a-select-option value="">全部</a-select-option>
                <a-select-option v-for="item in categoryOptions" :key="item.code" :value="item.code">
                    {{ item.name }}
                </a-select-option>
            </a-select>

            <a-button type="default" size="middle" @click="handleSearch" class="ml-auto">
                搜索
            </a-button>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { Icon } from '@iconify/vue';
import { useI18n } from 'vue-i18n';
import { useProductStore } from '@/store/modules/product';
import { ProductTypeEnum, AreaEnum } from '@/store/modules/order';

const props = defineProps({
    modelValue: {
        type: Object,
        required: true
    },
    disabled: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['update:modelValue', 'search', 'company-select']);

// 初始化国际化
const { t } = useI18n();

// 获取产品store
const productStore = useProductStore();

// 本地搜索文本
const searchText = ref('');
// 本地保司选择值
const companyValue = ref(undefined);

// 监听搜索文本变化，同步到modelValue
watch(searchText, (newValue) => {
    // 如果搜索文本变化，清空保司选择
    if (companyValue.value) {
        companyValue.value = undefined;
    }
    emit('update:modelValue', { ...props.modelValue, search: newValue });
});

// 监听props.modelValue变化，同步到本地searchText
watch(() => props.modelValue?.search, (newValue) => {
    if (newValue !== undefined) {
        searchText.value = newValue;
    }
}, { immediate: true });

// 搜索关键词和过滤后的公司列表
const searchCompanyKeyword = ref('');
const filteredCompanyList = ref([]);

// 获取格式化后的公司列表
const formattedCompanyList = computed(() => productStore.getFormattedCompanyList);

// 险种分类列表
const categoryOptions = computed(() => {
    return productStore.productCategoryList || [];
});

// 地区选项列表
const regionOptions = computed(() => [
    { code: 'HK', name: AreaEnum.HONGKONG },
    { code: 'MO', name: AreaEnum.MACAO },
    { code: 'SG', name: AreaEnum.SINGAPORE },
    { code: 'BM', name: AreaEnum.BERMUDA },
]);

// 初始化公司列表
const initCompanyList = async () => {
    await productStore.getCompanyList();
    await productStore.getProductCategoryList();
    filteredCompanyList.value = formattedCompanyList.value || [];
};

// 组件挂载时初始化
initCompanyList();

// 处理搜索
const handleSearch = () => {
    emit('update:m～odelValue', { ...props.modelValue, pageNum: 1 });
    emit('search');
};

// 处理公司搜索
const handleCompanySearch = (value) => {
    searchCompanyKeyword.value = value;

    if (!value) {
        // 如果搜索关键词为空，显示所有公司
        filteredCompanyList.value = formattedCompanyList.value || [];
        return;
    }

    // 根据关键词过滤公司列表
    const keyword = value.toLowerCase();
    filteredCompanyList.value = (formattedCompanyList.value || []).filter(item => {
        return item.name && item.name.toLowerCase().includes(keyword);
    });
};

// 处理公司筛选变化
const handleCompanyFilterChange = (value) => {
    console.log("公司选择变化:", value);
    // 如果选择了保司
    if (value) {
        // 清空搜索文本输入框的值
        searchText.value = '';
        // 发出公司选择事件
        emit('company-select', value);
        console.log("发出company-select事件，值为:", value);
    } else {
        // 如果清空了保司选择，也清空搜索关键词
        searchText.value = '';
        emit('update:modelValue', { ...props.modelValue, search: '' });
        emit('company-select', ''); // 也发出清空事件
        console.log("清空search参数");
    }
    // 确保search参数已经被设置后再调用handleSearch
    setTimeout(() => {
        handleSearch();
    }, 0);
};

// 重置筛选条件
const resetFilters = () => {
    searchText.value = '';
    companyValue.value = undefined;

    // 更新父组件的值
    emit('update:modelValue', {
        ...props.modelValue,
        categoryCode: '',
        region: '',
        search: '',
        companyCode: '',
        pageNum: 1
    });

    // 重置搜索关键词
    searchCompanyKeyword.value = '';
    // 重置过滤后的公司列表
    filteredCompanyList.value = formattedCompanyList.value || [];
};

// 暴露方法给父组件
defineExpose({
    resetFilters
});
</script>

<style scoped>
/* 表单元素样式 */
.filter-select :deep(.ant-select-selector) {
    border-radius: 6px !important;
    height: 36px !important;
    display: flex;
    align-items: center;
}

/* 确保搜索框和选择框的对齐 */
:deep(.ant-input),
:deep(.ant-select) {
    display: inline-flex !important;
    align-items: center !important;
    height: 32px !important;
}

:deep(.ant-select-selector) {
    height: 32px !important;
    padding: 0 11px !important;
    display: flex !important;
    align-items: center !important;
}
</style>