<template>
    <div class="rounded-lg overflow-hidden">
        <a-table :dataSource="products" :columns="productColumns" :pagination="{
            total: total,
            current: current,
            showSizeChanger: true,
            pageSize: pageSize,
            pageSizeOptions: ['10', '20', '50'],
            showTotal: (total) => `共 ${total} 条`,
            size: 'small'
        }" size="middle"
            :rowClassName="(record) => isSelected(record.id) ? 'bg-blue-50 hover:bg-blue-100' : 'hover:bg-gray-50'"
            :scroll="{ y: 380 }" :loading="loading" @change="handleTableChange" class="product-table">
            <template #bodyCell="{ column, record }">
                <!-- 产品名称列 -->
                <template v-if="column.dataIndex === 'productName'">
                    <div class="flex items-center">
                        <a-tooltip :title="record.productName">
                            <span class="hover:text-blue-500 cursor-pointer truncate max-w-[200px] font-medium">
                                {{ record.productName }}
                            </span>
                        </a-tooltip>
                    </div>
                </template>

                <!-- 操作列 -->
                <template v-if="column.dataIndex === 'action'">
                    <div class="flex items-center justify-center">
                        <a-button v-if="!isSelected(record.id)" type="primary" size="small"
                            @click="handleSelect(record)" :disabled="selectedCount >= maxSelections"
                            class="min-w-20 transition-colors">
                            选择
                        </a-button>
                        <a-button v-else type="primary" danger size="small" @click="handleRemove(record)"
                            class="min-w-20 transition-colors">
                            移除
                        </a-button>
                    </div>
                </template>
            </template>
        </a-table>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
    products: {
        type: Array,
        default: () => []
    },
    selectedProducts: {
        type: Array,
        default: () => []
    },
    loading: {
        type: Boolean,
        default: false
    },
    total: {
        type: Number,
        default: 0
    },
    current: {
        type: Number,
        default: 1
    },
    pageSize: {
        type: Number,
        default: 10
    },
    maxSelections: {
        type: Number,
        default: 6
    }
});

const emit = defineEmits(['select', 'remove', 'page-change']);

// 产品数据列
const productColumns = [
    {
        title: '产品名称',
        dataIndex: 'productName',
        key: 'productName',
        ellipsis: true,
        width: '40%',
    },
    {
        title: '保险公司',
        dataIndex: 'companyName',
        key: 'companyName',
        width: '25%',
        align: 'center',
    },
    {
        title: '险种',
        dataIndex: 'categoryName',
        key: 'categoryName',
        width: '20%',
        align: 'center',
    },
    {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        width: '15%',
        align: 'center',
    }
];

// 计算已选产品数量
const selectedCount = computed(() => props.selectedProducts.length);

// 检查产品是否已经被选择
const isSelected = (id) => {
    return props.selectedProducts.some(p => p.id === id);
};

// 处理产品表格分页变化
const handleTableChange = (pagination) => {
    emit('page-change', {
        current: pagination.current,
        pageSize: pagination.pageSize
    });
};

// 选择产品
const handleSelect = (product) => {
    emit('select', product);
};

// 移除产品
const handleRemove = (product) => {
    emit('remove', product);
};
</script>

<style scoped>
/* 产品表格样式 */
.product-table :deep(.ant-table-thead > tr > th) {
    background-color: #f3f4f6;
    font-weight: 600;
}

.product-table :deep(.ant-table-tbody > tr:hover > td) {
    transition: background-color 0.3s;
}

:deep(.ant-table-tbody > tr > td) {
    padding: 8px 12px;
    font-size: 14px;
}

:deep(.ant-table-thead > tr > th) {
    font-weight: 500;
}

:deep(.ant-pagination) {
    margin: 16px 0;
}

/* 确保表格内的按钮也正确对齐 */
:deep(.ant-table) .ant-btn {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}
</style>