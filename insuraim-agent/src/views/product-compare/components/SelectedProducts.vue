<template>
    <div v-if="products.length > 0" class="mt-6">
        <div class="flex items-center text-md font-medium mb-3 text-gray-700">
            <Icon icon="material-symbols:list-alt" class="text-blue-500 mr-2" />
            已选择 {{ products.length }}/{{ maxSelections }} 个产品
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div v-for="(product, index) in products" :key="product.id"
                class="h-[150px] border border-gray-200 rounded-lg overflow-hidden bg-white shadow-sm selected-product-card">
                <div class="p-6 h-full flex flex-col justify-between">
                    <div>
                        <div class="flex items-center justify-between">
                            <div class="font-semibold text-gray-800 truncate" :title="product.productName">
                                <span v-if="index === 0"
                                    class="inline-block bg-blue-100 text-blue-600 text-xs px-2 py-0.5 rounded mr-1">主产品</span>
                                <span v-else
                                    class="inline-block bg-green-100 text-green-600 text-xs px-2 py-0.5 rounded mr-1">对比
                                    {{ index }}</span>
                                {{ product.productName }}
                            </div>
                        </div>
                        <div class="text-xs text-gray-600 mt-2 flex flex-wrap gap-2">
                            <span class="bg-blue-50 px-2 py-0.5 rounded-full text-blue-700">{{ product.companyName
                                }}</span>
                            <span class="bg-green-50 px-2 py-0.5 rounded-full text-green-700">{{ product.categoryName
                                }}</span>
                        </div>
                    </div>
                    <div class="flex justify-end">
                        <a-button type="primary" danger size="small" @click="handleRemove(product)"
                            class="hover:bg-red-500 transition-colors">
                            <template #icon>
                                <Icon icon="material-symbols:delete-outline" />
                            </template>
                            移除
                        </a-button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';

const props = defineProps({
    products: {
        type: Array,
        default: () => []
    },
    maxSelections: {
        type: Number,
        default: 6
    }
});

const emit = defineEmits(['remove']);

// 移除产品
const handleRemove = (product) => {
    emit('remove', product);
};
</script>

<style scoped>
/* 产品卡片的悬停效果增强 */
.selected-product-card {
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
    height: 150px;
    display: flex;
    flex-direction: column;
}

.selected-product-card:hover {
    border-color: #3B82F6;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
    transform: translateY(-3px);
}
</style>