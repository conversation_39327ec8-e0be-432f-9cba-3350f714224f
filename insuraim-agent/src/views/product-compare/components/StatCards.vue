<template>
    <a-card class="mb-6 rounded-lg" :bordered="false">
        <h1 class="text-2xl font-bold mb-4">对比数据</h1>
        <!-- 产品统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- 已选产品卡片 -->
            <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-blue-500">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm">已选产品</p>
                        <p class="text-2xl font-bold">{{ selectedCount }}/{{ maxSelections }}</p>
                    </div>
                    <div class="bg-blue-100 p-2 rounded-full">
                        <Icon icon="material-symbols:list-alt" class="text-2xl text-blue-500" />
                    </div>
                </div>
                <div class="mt-2 text-xs text-gray-500">
                    <span class="flex items-center">
                        <Icon icon="material-symbols:info-outline" class="mr-1" />
                        当前已选择的产品数量
                    </span>
                </div>
            </div>

            <!-- 主产品类型卡片 -->
            <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-green-500">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <p class="text-gray-500 text-sm">主产品类型</p>
                        <p class="text-lg font-bold truncate" :title="mainProductType">{{ mainProductType || '未选择' }}
                        </p>
                    </div>
                    <div class="bg-green-100 p-2 rounded-full">
                        <Icon icon="material-symbols:category" class="text-2xl text-green-500" />
                    </div>
                </div>
                <div class="mt-2 text-xs text-gray-500">
                    <span class="flex items-center">
                        <Icon icon="material-symbols:info-outline" class="mr-1" />
                        首选产品的类型，决定可比较范围
                    </span>
                </div>
            </div>

            <!-- 产品列表数量卡片 -->
            <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-orange-500">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm">可选产品数</p>
                        <p class="text-2xl font-bold">{{ totalProducts || 0 }}</p>
                    </div>
                    <div class="bg-orange-100 p-2 rounded-full">
                        <Icon icon="material-symbols:database" class="text-2xl text-orange-500" />
                    </div>
                </div>
                <div class="mt-2 text-xs text-gray-500">
                    <span class="flex items-center">
                        <Icon icon="material-symbols:info-outline" class="mr-1" />
                        当前筛选条件下的可选产品总数
                    </span>
                </div>
            </div>

            <!-- 加载状态卡片 -->
            <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-purple-500">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm">加载状态</p>
                        <p class="text-lg font-bold flex items-center">
                            <template v-if="loading">
                                <a-spin size="small" class="mr-2" />
                                <span>加载中...</span>
                            </template>
                            <template v-else>
                                <Icon icon="material-symbols:check-circle" class="text-green-500 mr-2" />
                                <span>数据就绪</span>
                            </template>
                        </p>
                    </div>
                    <div class="bg-purple-100 p-2 rounded-full">
                        <Icon icon="material-symbols:sync" class="text-2xl text-purple-500" />
                    </div>
                </div>
                <div class="mt-2 text-xs text-gray-500">
                    <span class="flex items-center">
                        <Icon icon="material-symbols:info-outline" class="mr-1" />
                        产品数据加载状态
                    </span>
                </div>
            </div>
        </div>
    </a-card>
</template>

<script setup>
import { Icon } from '@iconify/vue';

const props = defineProps({
    selectedCount: {
        type: Number,
        default: 0
    },
    maxSelections: {
        type: Number,
        default: 6
    },
    mainProductType: {
        type: String,
        default: ''
    },
    totalProducts: {
        type: Number,
        default: 0
    },
    loading: {
        type: Boolean,
        default: false
    }
});
</script>

<style scoped>
.stat-card {
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
</style>