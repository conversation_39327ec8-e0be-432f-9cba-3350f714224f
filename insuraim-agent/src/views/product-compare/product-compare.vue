<template>
  <!-- 主要内容区域 -->
  <div class="container mx-auto py-6 px-4">
    <!-- 页面标题 -->
    <component :is="Components.PageHeader" />

    <!-- 统计卡片 -->
    <component :is="Components.StatCards" :selected-count="selectedProducts.length" :max-selections="6"
      :main-product-type="selectedProducts[0]?.categoryName || ''" :total-products="productTotal"
      :loading="productLoading || loadingDetails" />

    <div class="bg-white rounded-lg shadow-md mb-8">
      <!-- 产品选择区域 -->
      <div class="mt-4">
        <div class="flex flex-col lg:flex-row gap-8">
          <!-- 产品选择 -->
          <div class="flex-1 bg-white p-8 rounded-lg border border-gray-100 shadow-sm">
            <div class="flex items-center justify-between mb-5">
              <h2 class="text-lg font-medium text-gray-800">选择产品</h2>
            </div>

            <!-- 主产品类型提示 -->
            <div v-if="selectedProducts.length > 0" class="mb-4 p-3 bg-blue-50 rounded-lg text-sm text-blue-800">
              <div class="flex items-center">
                <Icon icon="material-symbols:info-outline" class="mr-2 text-blue-500" />
                <span>已选择主产品类型: <b>{{ selectedProducts[0]?.categoryName }}</b>，只能选择相同类型的产品进行对比</span>
              </div>
            </div>

            <!-- 筛选条件 -->
            <component :is="Components.ProductFilter" v-model="productParams" :disabled="selectedProducts.length > 0"
              @search="loadProductData" @company-select="handleCompanySelect" ref="productFilterRef" />

            <!-- 产品列表 -->
            <component :is="Components.ProductTable" :products="productList" :selected-products="selectedProducts"
              :loading="productLoading" :total="productTotal" :current="productParams.pageNum"
              :page-size="productParams.pageSize" :max-selections="6" @select="selectProduct" @remove="removeProduct"
              @page-change="handleProductTableChange" />
          </div>
        </div>

        <!-- 已选产品 -->
        <component :is="Components.SelectedProducts" :products="selectedProducts" :max-selections="6"
          @remove="removeProduct" />
      </div>
    </div>

    <!-- 比较结果 -->
    <component :is="Components.ComparisonResult" :show="showComparisonResult" :selected-products="selectedProducts"
      :products="productsDetails" :loading="loadingDetails" @reset="resetSelection" />

    <!-- 图表对比区域 -->
    <!-- <component v-if="showComparisonResult" :is="Components.ChartComparison" :products="productsDetails"
      :selected-products="selectedProducts" :loading="loadingDetails" /> -->
  </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';
import { ref, reactive, onMounted, computed, markRaw } from 'vue';
import { productAPI } from '../../api';
import { message } from 'ant-design-vue';
import { useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { toTraditional } from '@/utils/chineseConverter';
import {
  ProductFilter,
  ProductTable,
  SelectedProducts,
  ComparisonResult,
  PageHeader,
  StatCards,
  ChartComparison
} from './components';

// 使用markRaw防止组件被变为响应式
const Components = {
  ProductFilter: markRaw(ProductFilter),
  ProductTable: markRaw(ProductTable),
  SelectedProducts: markRaw(SelectedProducts),
  ComparisonResult: markRaw(ComparisonResult),
  PageHeader: markRaw(PageHeader),
  StatCards: markRaw(StatCards),
  ChartComparison: markRaw(ChartComparison)
};

// 初始化国际化和路由
const { t } = useI18n();
const route = useRoute();

// 产品列表数据
const productList = ref([]);
const productLoading = ref(false);
const productTotal = ref(0);
const productFilterRef = ref(null);
const productParams = reactive({
  pageNum: 1,
  pageSize: 10,
  categoryCode: '',
  region: '',
  search: '',
  comparable: true,
  companyCode: undefined,
});

// 已选产品
const selectedProducts = ref([]);

// 比较结果相关
const showComparisonResult = ref(false);
const productsDetails = ref([]);
const loadingDetails = ref(false);

// 组件挂载时加载数据
onMounted(async () => {
  await loadProductData();

  // 检查URL参数，如果有productName和productId，则尝试自动添加产品
  const { productName, productId } = route.query;
  if (productName && productId) {
    await autoAddProductFromParams(productName, productId);
  }
});

// 加载产品数据
const loadProductData = async () => {
  try {
    productLoading.value = true;
    console.log("发起请求前的productParams:", JSON.stringify(productParams));

    // 确保search参数已经设置好（这是关键修复）
    if (productParams.search) {
      productParams.search = toTraditional(productParams.search);
    }

    console.log("转换后的productParams:", JSON.stringify(productParams));
    const result = await productAPI.getProductList(productParams);
    productList.value = result.records || [];
    productTotal.value = result.total || 0;
    return result; // 返回结果，以便函数可以被异步等待
  } catch (error) {
    console.error('加载产品数据失败:', error);
    message.error('加载产品数据失败');
    return null; // 出错时返回null
  } finally {
    productLoading.value = false;
  }
};

// 处理产品表格分页变化
const handleProductTableChange = (pagination) => {
  productParams.pageNum = pagination.current;
  productParams.pageSize = pagination.pageSize;
  loadProductData();
};

// 选择产品
const selectProduct = async (product) => {
  if (selectedProducts.value.length >= 6) {
    message.warning('最多只能选择6个产品进行对比');
    return;
  }

  // 如果已有主产品，检查类型是否匹配
  if (selectedProducts.value.length > 0) {
    const mainProduct = selectedProducts.value[0];
    if (mainProduct.categoryCode !== product.categoryCode) {
      message.warning(`只能选择与主产品相同类型(${mainProduct.categoryName})的产品进行对比`);
      return;
    }
  }

  // 检查是否已经选择了该产品
  if (isProductSelected(product.id)) {
    message.warning('该产品已经被选择');
    return;
  }

  // 添加到已选产品中
  selectedProducts.value.push({ ...product });

  // 如果是选择第一个产品（主产品）
  if (selectedProducts.value.length === 1) {
    // 更新分类筛选为当前产品类型，确保后续只能选择相同类型的产品
    productParams.categoryCode = product.categoryCode;
    // 重置页码到第一页
    productParams.pageNum = 1;

    // 重新加载产品列表数据，应用新的筛选条件
    await loadProductData();

    // 显示第一个产品的详情
    await loadProductsDetails();
  } else {
    // 如果已经有产品显示，则更新对比详情
    await loadProductsDetails();
  }
};

// 移除产品
const removeProduct = (product) => {
  // 判断移除的是不是主产品（第一个产品）
  const isMainProduct = selectedProducts.value[0]?.id === product.id;

  // 从已选产品中移除
  selectedProducts.value = selectedProducts.value.filter(
    p => p.id !== product.id
  );

  // 如果移除的是主产品，并且还有其他已选产品，则需要重置筛选条件
  if (isMainProduct && selectedProducts.value.length === 0) {
    // 重置分类筛选条件
    productParams.categoryCode = '';

    // 重新加载产品列表
    loadProductData();
  }

  // 如果移除后少于1个产品，隐藏对比结果
  if (selectedProducts.value.length < 1) {
    showComparisonResult.value = false;
    productsDetails.value = [];
  } else {
    // 如果还有产品，刷新对比报告
    loadProductsDetails();
  }
};

// 检查产品是否已经被选择
const isProductSelected = (id) => {
  return selectedProducts.value.some(p => p.id === id);
};

// 重置所有选择
const resetSelection = () => {
  // 清空已选产品
  selectedProducts.value = [];

  // 隐藏对比结果
  showComparisonResult.value = false;
  productsDetails.value = [];

  // 重置所有筛选条件
  if (productFilterRef.value) {
    productFilterRef.value.resetFilters();
  }

  // 重新加载数据
  loadProductData();
};

// 加载产品详情并显示对比结果
const loadProductsDetails = async () => {
  if (selectedProducts.value.length < 1) {
    message.warning('请至少选择一个产品');
    return null;
  }

  try {
    loadingDetails.value = true;
    showComparisonResult.value = true;
    productsDetails.value = [];

    // 获取所有选中产品的详情
    for (const product of selectedProducts.value) {
      const productDetail = await productAPI.getProductDetail(product.id);
      productsDetails.value.push(productDetail);
    }

    return productsDetails.value; // 返回加载的产品详情

  } catch (error) {
    console.error('获取产品详情失败:', error);
    message.error('获取产品详情失败');
    return null;
  } finally {
    loadingDetails.value = false;
  }
};

// 自动根据URL参数添加产品
const autoAddProductFromParams = async (productName, productId) => {
  try {
    // 设置搜索条件为产品名称
    productParams.search = productName;
    productParams.pageNum = 1;
    productParams.pageSize = 20; // 设置较大的页面大小以增加找到产品的概率

    // 执行搜索
    const result = await productAPI.getProductList(productParams);
    productList.value = result.records || [];
    productTotal.value = result.total || 0;

    // 在搜索结果中查找匹配的产品ID
    const matchedProduct = productList.value.find(p => String(p.id) === String(productId));

    // 如果找到匹配的产品，并且尚未添加到对比列表中
    if (matchedProduct && !isProductSelected(matchedProduct.id)) {
      // 添加产品到对比列表
      await selectProduct(matchedProduct);

      // 显示成功提示
      message.success(t('products.autoAddedProduct'));
    }

    // 恢复搜索条件
    productParams.search = '';
    await loadProductData();
  } catch (error) {
    console.error('自动添加产品失败:', error);
    // 不显示错误提示，静默失败

    // 恢复搜索条件
    productParams.search = '';
    await loadProductData();
  }
};

// 处理公司选择
const handleCompanySelect = (companyName) => {
  console.log("父组件收到company-select事件:", companyName);
  // 直接设置search参数
  productParams.search = companyName;
  // 调用加载数据
  loadProductData();
};
</script>

<style scoped>
/* 主题色定义 */
:root {
  --color-primary: #3B82F6;
  --color-primary-hover: #2563EB;
  --color-secondary: #6366F1;
}

.bg-primary {
  background-color: var(--color-primary) !important;
}

.bg-primary\/90 {
  background-color: var(--color-primary-hover) !important;
}

.hover\:border-primary:hover {
  border-color: var(--color-primary) !important;
}

.hover\:text-primary:hover {
  color: var(--color-primary) !important;
}

.text-primary {
  color: var(--color-primary) !important;
}

/* 添加过渡效果 */
a-button,
button,
input,
select {
  transition: all 0.2s ease-in-out !important;
}

.hover\:bg-primary\/90:hover {
  background-color: var(--color-primary-hover) !important;
}

/* 确保所有按钮的图标和文本垂直居中对齐 */
:deep(.ant-btn) {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 32px !important;
  padding: 0 15px !important;
}

:deep(.ant-btn-sm) {
  height: 24px !important;
  padding: 0 7px !important;
  font-size: 12px !important;
}

:deep(.ant-btn-lg) {
  height: 40px !important;
  padding: 0 15px !important;
  font-size: 16px !important;
}

:deep(.ant-btn .anticon) {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  line-height: 1 !important;
  vertical-align: middle !important;
}

:deep(.ant-btn .anticon + span),
:deep(.ant-btn .iconify + span),
:deep(.ant-btn svg + span),
:deep(.ant-btn span + .anticon),
:deep(.ant-btn span + .iconify),
:deep(.ant-btn span + svg) {
  margin-left: 8px !important;
}

/* 响应式容器 */
@media (max-width: 640px) {
  .container {
    padding: 0.5rem;
  }
}
</style>