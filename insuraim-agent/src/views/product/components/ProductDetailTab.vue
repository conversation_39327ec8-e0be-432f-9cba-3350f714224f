<template>
  <div class="space-y-6">
    <div v-for="(section, index) in filteredSections" :key="section.name" class="mb-6">
      <div class="flex items-center mb-4">
        <div class="w-1 h-6 bg-red-500 mr-2"></div>
        <h3 class="text-base font-medium">{{ section.name }}</h3>
      </div>
      <div class="space-y-2">
        <div v-for="attr in filteredAttributes(section.attributes)" :key="attr.name"
          class="flex py-2 border-b border-gray-100">
          <span class="text-gray-600 text-sm w-48 flex-shrink-0 mr-6">{{ attr.name }}</span>
          <span class="text-gray-900 text-sm flex-1" v-html="formatTextWithLineBreaks(attr.value)"></span>
        </div>
      </div>
    </div>

    <!-- 产品亮点内容 -->
    <div>
      <div class="flex items-center mb-2">
        <div class="w-1 h-6 bg-red-500 mr-2"></div>
        <h3 class="text-base font-medium">{{ t('products.productHighlights') }}</h3>
      </div>
      <div v-if="mergedHighlights && mergedHighlights.length > 0">
        <div v-for="(highlight, index) in mergedHighlights" :key="index" class="mb-4">
          <div class="flex items-start">
            <div class="mr-2 mt-1">
              <Icon icon="mdi:check-circle" class="text-green-500 text-lg" />
            </div>
            <div>
              <p class="text-gray-700">{{ highlight }}</p>
            </div>
          </div>
        </div>
      </div>


      <!-- 分红实现率 -->
      <div v-if="hasSubProductDividendRate">
        <div class="flex items-center mb-2">
          <div class="w-1 h-6 bg-red-500 mr-2"></div>
          <h3 class="text-base font-medium">{{ t('products.dividendRealizationRate') }}</h3>
        </div>

        <!-- 加载中状态 -->
        <div v-if="isLoadingIRR(selectedSubProductId)" class="py-4 text-center">
          <div class="inline-flex items-center">
            <svg class="animate-spin h-5 w-5 mr-3 text-gray-500" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none">
              </circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"></path>
            </svg>
            <span>{{ t('products.loadingData') }}</span>
          </div>
        </div>

        <!-- 无数据状态 -->
        <div v-else-if="!selectedSubProductDividendRate.length" class="py-4 text-center text-gray-500">
          {{ t('products.noDividendData') }}
        </div>

        <!-- 数据展示 -->
        <div v-else class="overflow-x-auto">
          <table class="min-w-full border border-gray-300 mb-4">
            <thead>
              <tr>
                <th v-for="item in selectedSubProductDividendRate" :key="item.year"
                  class="border border-gray-300 px-4 py-2 bg-gray-100 text-center">
                  {{ item.year }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td v-for="item in selectedSubProductDividendRate" :key="item.year"
                  class="border border-gray-300 px-4 py-2 text-center">
                  {{ item.percentage }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div class="flex items-center mb-2">
        <div class="w-1 h-6 bg-red-500 mr-2"></div>
        <h3 class="text-base font-medium">{{ t('products.compoundAndSimpleInterest') }}</h3>
      </div>
      <!-- IRR -->
      <div v-if="subProducts && subProducts.length > 0">
        <a-tabs>
          <a-tab-pane v-for="subProduct in subProducts" :key="subProduct.id" :tab="subProduct.productName">

            <!-- 加载状态 -->
            <div v-if="isLoadingIRR(subProduct.id)" class="py-8 text-center">
              <div class="inline-flex items-center">
                <svg class="animate-spin h-5 w-5 mr-3 text-gray-500" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none">
                  </circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"></path>
                </svg>
                <span>{{ t('products.loadingData') }}</span>
              </div>
            </div>

            <!-- 数据为空状态 -->
            <div v-else-if="!hasIRRData(subProduct.id)" class="py-8 text-center text-gray-500">
              {{ subProduct.productName }} - 没有IRR数据
            </div>

            <!-- 数据展示 -->
            <div v-else class="mb-4">
              <!-- 案例参考 - 美化后的卡片式设计 -->
              <div v-for="(item, index) in getSubProductIRR(subProduct.id)" :key="index" class="mb-8">
                <div class="bg-white rounded-lg shadow-md p-4 mb-4 border border-gray-200">
                  <h4 class="text-lg font-medium text-gray-800 mb-3 flex items-center">
                    <Icon icon="mdi:file-document-outline" class="mr-2 text-blue-500" />
                    {{ t('products.caseReference') }}
                  </h4>
                  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                    <div class="flex items-center">
                      <Icon icon="mdi:account" class="mr-2 text-gray-500" />
                      <span class="text-gray-600 font-medium">{{ t('products.gender') }}：</span>
                      <span class="ml-1">{{ item.gender }}</span>
                    </div>
                    <div class="flex items-center">
                      <Icon icon="mdi:calendar-account" class="mr-2 text-gray-500" />
                      <span class="text-gray-600 font-medium">{{ t('products.age') }}：</span>
                      <span class="ml-1">{{ item.years }}</span>
                    </div>
                    <div class="flex items-center">
                      <Icon icon="mdi:smoking-off" class="mr-2 text-gray-500" v-if="item.smoking_status === '否'" />
                      <Icon icon="mdi:smoking" class="mr-2 text-gray-500" v-else />
                      <span class="text-gray-600 font-medium">{{ t('products.smokingStatus') }}：</span>
                      <span class="ml-1">{{ item.smoking_status }}</span>
                    </div>
                    <div class="flex items-center">
                      <Icon icon="mdi:cash" class="mr-2 text-gray-500" />
                      <span class="text-gray-600 font-medium">{{ t('products.premium') }}：</span>
                      <span class="ml-1">{{ item.annualPremium }}</span>
                    </div>
                    <div class="flex items-center">
                      <Icon icon="mdi:shield-check" class="mr-2 text-gray-500" />
                      <span class="text-gray-600 font-medium">{{ t('products.coverageAmount') }}：</span>
                      <span class="ml-1">{{ item.coverageAmount }}</span>
                    </div>
                    <div class="flex items-center">
                      <Icon icon="mdi:credit-card-outline" class="mr-2 text-gray-500" />
                      <span class="text-gray-600 font-medium">{{ t('products.paymentMode') }}：</span>
                      <span class="ml-1">{{ item.paymentMode }}</span>
                    </div>
                    <div class="flex items-center">
                      <Icon icon="mdi:calendar-check" class="mr-2 text-gray-500" />
                      <span class="text-gray-600 font-medium">{{ t('products.coverageTerm') }}：</span>
                      <span class="ml-1">{{ item.coverageTerm }}</span>
                    </div>
                    <div class="flex items-center">
                      <Icon icon="mdi:calendar-clock" class="mr-2 text-gray-500" />
                      <span class="text-gray-600 font-medium">{{ t('products.premiumPaymentTerm') }}：</span>
                      <span class="ml-1">{{ item.premiumPaymentTerm }}</span>
                    </div>
                  </div>
                </div>

                <div class="overflow-x-auto">
                  <table class="min-w-full border border-gray-300">
                    <thead>
                      <tr>
                        <th class="border border-gray-300 px-4 py-2 bg-gray-100 text-center">{{ t('products.age') }}
                        </th>
                        <th class="border border-gray-300 px-4 py-2 bg-gray-100 text-center">{{ t('products.policyYear')
                          }}</th>
                        <th class="border border-gray-300 px-4 py-2 bg-gray-100 text-center">{{
                          t('products.totalPremium') }}</th>
                        <th class="border border-gray-300 px-4 py-2 bg-gray-100 text-center">{{
                          t('products.netCashFlow') }}</th>
                        <th class="border border-gray-300 px-4 py-2 bg-gray-100 text-center">{{ t('products.cashValue')
                          }}</th>
                        <th class="border border-gray-300 px-4 py-2 bg-gray-100 text-center">{{
                          t('products.simpleInterest') }}</th>
                        <th class="border border-gray-300 px-4 py-2 bg-gray-100 text-center">{{
                          t('products.compoundInterest') }}</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="itemChild in item.list" :key="itemChild.year">
                        <td class="border border-gray-300 px-4 py-2 text-center">{{ itemChild.age }}</td>
                        <td class="border border-gray-300 px-4 py-2 text-center">{{ itemChild.year }}</td>
                        <td class="border border-gray-300 px-4 py-2 text-center">{{
                          convertToThousandFormat(itemChild.totalPremum) }}</td>
                        <td class="border border-gray-300 px-4 py-2 text-center">{{
                          convertToThousandFormat(itemChild.netflow) }}</td>
                        <td class="border border-gray-300 px-4 py-2 text-center">{{
                          convertToThousandFormat(itemChild.surrender) }}</td>
                        <td class="border border-gray-300 px-4 py-2 text-center">
                          {{ isNaN(itemChild.singleton) ? itemChild.singleton : itemChild.singleton + '%' }}
                        </td>
                        <td class="border border-gray-300 px-4 py-2 text-center">
                          {{ isNaN(itemChild.IRR) ? itemChild.IRR : itemChild.IRR + '%' }}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { Icon } from '@iconify/vue';
import { productAPI } from '@/api';

// 初始化国际化
const { t, locale } = useI18n();

const props = defineProps({
  highlights: {
    type: Array,
    default: () => []
  },
  productId: {
    type: Number,
    default: 0
  }
});

// 产品详情
const productDetails = ref([]);

// 分红实现率
const dividendRealizationRate = ref([]);
// IRR
const irr = ref([]);

// 子产品列表
const subProducts = ref([]);
// 单利
const singleton = ref([]);
// 产品亮点
const extractedHighlights = ref([]);

// 存储子产品 ID 与其 IRR 数据的映射
const subProductIRRMap = ref(new Map());
// 当前正在加载 IRR 数据的子产品 ID
const loadingIRRProductId = ref(null);

/**
 * @description 从产品介绍数据中提取亮点
 * @param {Object} productIntro 产品介绍数据
 * @returns {Array} 亮点列表
 */
const extractHighlightsFromFeatures = (productIntro) => {
  if (!productIntro || !productIntro.product_info || !productIntro.product_info.features) {
    return [];
  }
  return productIntro.product_info.features.map(feature => feature.title);
};

/**
 * @description 转换金额为千位符格式
 * @param {number} value 金额
 * @returns {string} 千位符格式
 */
const convertToThousandFormat = (value) => {
  if (value === null || value === undefined) return '';
  if (value === 0) return '0';
  return value.toLocaleString();
};

/**
 * @description 将文本中的换行符转换为HTML的br标签
 * @param {string} text 原始文本
 * @returns {string} 转换后的HTML字符串
 */
const formatTextWithLineBreaks = (text) => {
  if (!text) return '';
  // 转义HTML特殊字符，然后将\n替换为<br>
  return String(text)
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;')
    .replace(/\n/g, '<br>');
};

// 工具函数：统一获取排序值（兼容 rank 与 rankValue）
const getRank = (obj) => obj?.rankValue ?? obj?.rank ?? 0;

// 过滤掉值为空串或 null 的属性（保留 '-'）
const filteredAttributes = (attributes) => {
  if (!attributes) return [];
  return attributes
    .filter(attr => attr.value !== null && attr.value !== '')
    .sort((a, b) => getRank(a) - getRank(b));
};

// 过滤掉在过滤后无属性的 section
const filteredSections = computed(() => {
  return productDetails.value
    .filter(section => filteredAttributes(section.attributes).length > 0)
    .sort((a, b) => getRank(a) - getRank(b));
});

// 合并来自 props 和 API 的亮点数据
const mergedHighlights = computed(() => {
  // 如果 props.highlights 有值且长度大于0，优先使用
  if (props.highlights && props.highlights.length > 0) {
    return props.highlights;
  }
  // 否则使用从 API 提取的亮点
  return extractedHighlights.value;
});

// const loadProductIRRAndDividend = async () => {
//   const result = await productAPI.getChinaLifeProductIRRAndDividend(props.productId);
//   dividendRealizationRate.value = result.data?.dividendRealizationRate || result.dividendRealizationRate || [];
//   irr.value = result.data?.irr || result.irr || [];
//   singleton.value = result.data?.singleton || result.singleton || [];
// };

const loadProductDetail = async () => {
  const result = await productAPI.getProductDetail(props.productId);
  // 兼容后端返回结构
  productDetails.value = result?.productDetails || result.productDetails || result;
  subProducts.value = result?.subProducts || result.subProducts || [];
  // 如果存在产品介绍数据，提取亮点
  if (result.data?.productIntro) {
    extractedHighlights.value = extractHighlightsFromFeatures(result.data.productIntro);
  }
};

onMounted(async () => {
  // await loadProductIRRAndDividend();
  await loadProductDetail();

  // 从产品数据中提取子产品列表
  if (subProducts.value) {
    console.log('找到子产品:', subProducts.value.length, '个');

    // 默认选中第一个子产品
    if (subProducts.value.length > 0) {
      selectedSubProductId.value = subProducts.value[0].id;
    }

    // 为每个子产品加载 IRR 数据
    await loadSubProductsIRR();
  } else {
    console.log('没有找到子产品');
  }
});

/**
 * @description 为所有子产品加载 IRR 数据
 */
const loadSubProductsIRR = async () => {
  if (!subProducts.value || subProducts.value.length === 0) return;

  try {
    // 异步加载每个子产品的 IRR 数据
    await Promise.all(subProducts.value.map(async (subProduct) => {
      await loadSubProductIRR(subProduct.id);
    }));

    console.log('所有子产品 IRR 数据加载完成');
  } catch (error) {
    console.error('加载子产品 IRR 数据失败:', error);
  }
};

/**
 * @description 加载指定子产品的 IRR 数据
 * @param {number} subProductId 子产品 ID
 */
const loadSubProductIRR = async (subProductId) => {
  if (!subProductId) return;

  try {
    // 设置当前正在加载的子产品 ID
    loadingIRRProductId.value = subProductId;

    console.log(`开始加载子产品 ${subProductId} 的 IRR 数据`);
    const subResult = await productAPI.getChinaLifeProductIRRAndDividend(subProductId);

    // 从结果中提取 IRR 数据
    const irrData = subResult.data?.irr || subResult.irr || [];

    // 存储到映射中
    subProductIRRMap.value.set(subProductId, {
      irr: irrData,
      singleton: subResult.data?.singleton || subResult.singleton || [],
      dividendRealizationRate: subResult.data?.dividendRealizationRate || subResult.dividendRealizationRate || []
    });

    console.log(`子产品 ${subProductId} 的 IRR 数据加载完成:`, irrData.length, '条');
  } catch (error) {
    console.error(`加载子产品 ${subProductId} 的 IRR 数据失败:`, error);
  } finally {
    // 清除加载状态
    if (loadingIRRProductId.value === subProductId) {
      loadingIRRProductId.value = null;
    }
  }
};

// 辅助函数：检查子产品是否正在加载 IRR 数据
const isLoadingIRR = (subProductId) => {
  return loadingIRRProductId.value === subProductId;
};

// 辅助函数：检查子产品是否有 IRR 数据
const hasIRRData = (subProductId) => {
  return subProductIRRMap.value.has(subProductId) &&
    subProductIRRMap.value.get(subProductId)?.irr?.length > 0;
};

// 辅助函数：获取子产品的 IRR 数据
const getSubProductIRR = (subProductId) => {
  if (!subProductIRRMap.value.has(subProductId)) return [];
  return subProductIRRMap.value.get(subProductId)?.irr || [];
};

// 辅助函数：获取子产品的单利数据
const getSubProductSingleton = (subProductId) => {
  if (!subProductIRRMap.value.has(subProductId)) return [];
  return subProductIRRMap.value.get(subProductId)?.singleton || [];
};

// 辅助函数：获取子产品的分红实现率
const getSubProductDividendRate = (subProductId) => {
  if (!subProductIRRMap.value.has(subProductId)) return [];
  return subProductIRRMap.value.get(subProductId)?.dividendRealizationRate || [];
};

// 辅助函数：检查子产品是否有分红实现率数据
const hasSubProductDividendRate = computed(() => {
  return subProducts.value.some(subProduct => getSubProductDividendRate(subProduct.id).length > 0);
});

// 辅助函数：获取选中的子产品的分红实现率数据
const selectedSubProductDividendRate = computed(() => {
  if (!selectedSubProductId.value) return [];
  return getSubProductDividendRate(selectedSubProductId.value);
});

// 辅助函数：获取选中的子产品的 ID
const selectedSubProductId = ref(null);
</script>