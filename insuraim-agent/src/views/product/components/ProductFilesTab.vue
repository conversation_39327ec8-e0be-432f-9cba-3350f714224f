<template>
  <div>
    <div v-if="files && files.length > 0" class="grid grid-cols-1 gap-4">
      <div v-for="file in files" :key="file.fileName" class="border rounded p-4 hover:shadow-md transition-shadow">
        <div class="flex flex-col">
          <div class="flex justify-between items-center mb-2">
            <div class="flex items-center">
              <Icon icon="mdi:file-pdf-box" class="text-red-500 text-xl mr-2" />
              <span class="text-gray-800 font-medium">{{ file.fileName }}</span>
            </div>
            <a-button type="link" @click="$emit('preview-file', file)" class="preview-button">
              <template #icon>
                <Icon icon="mdi:eye-outline" />
              </template>
              {{ t('products.preview') }}
            </a-button>
          </div>
          <div class="flex items-center text-sm text-gray-500 gap-4">
            <span>{{ t('products.uploader') }}：{{ file.author }}</span>
            <span>{{ t('products.uploadTime') }}：{{ new Date(file.uploadTime).toLocaleDateString() }}</span>
          </div>
        </div>
      </div>
    </div>
    <a-empty v-else :description="t('products.noDocuments')" />
  </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';
import { ref, onMounted } from 'vue';
import { productAPI } from '@/api';
import { message } from 'ant-design-vue';
import { useI18n } from 'vue-i18n';

// 初始化国际化
const { t } = useI18n();

const props = defineProps({
  productId: {
    type: Number,
    required: true
  }
});

// 定义emit事件
const emit = defineEmits(['preview-file']);

// 文件列表
const files = ref([]);
const brochureUrl = ref('');

onMounted(async () => {
  try {
    const result = await productAPI.getChinaLifeProductBrochureById(props.productId);

    if (result && result.length > 0) {
      // 从API结果中获取小册子URL
      brochureUrl.value = result[0]?.download || '';

      // 如果有小册子URL，添加到文件列表
      if (brochureUrl.value) {
        files.value.push({
          fileName: `${result[0]?.name || t('products.chinaLife')} - ${t('products.productFilesTab')}.pdf`,
          filePath: brochureUrl.value,
          author: t('products.chinaLife'),
          uploadTime: new Date().toISOString(),
          fileType: 'pdf'
        });
      }
    }
  } catch (error) {
    console.error('获取产品小册子失败:', error);
    message.error(t('products.getProductBrochureFailed'));
  }
});
</script>

<style scoped>
/* 预览按钮图标和文字对齐 */
.preview-button {
  display: inline-flex;
  align-items: center;
}

.preview-button :deep(.ant-btn-icon) {
  display: flex;
  align-items: center;
}
</style>