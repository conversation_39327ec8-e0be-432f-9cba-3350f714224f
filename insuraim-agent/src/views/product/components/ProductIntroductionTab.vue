<template>
  <div class="w-full relative text-left text-sm text-color3">
    <!-- 加载状态 -->
    <div v-if="loading" class="w-full flex items-center justify-center p-20">
      <div class="text-color1 text-lg">{{ t('products.loading') }}</div>
    </div>

    <!-- 错误提示 -->
    <div v-else-if="error" class="w-full flex items-center justify-center p-20">
      <div class="text-red-500 text-lg">{{ error }}</div>
    </div>

    <!-- 主要内容区域 -->
    <div v-else class="mx-auto p-3">
      <!-- 产品卡片 -->
      <div v-if="productData.productName" class="rounded-xl bg-color7 w-full mb-10">
        <!-- 产品大图 -->
        <div class="relative">
          <div class="[background:linear-gradient(180deg,_#fff,_#fff_50%,_rgba(255,_255,_255,_0))] w-full h-[200px]">
          </div>
          <div class="absolute top-[40px] left-0 right-0 text-center">
            <h2 class="text-4xl font-semibold">{{ productData.name }}</h2>
            <div class="mt-6 mx-auto max-w-[1000px] text-color2">
              <p class="m-0" v-if="productData.description">{{ productData.description }}</p>
            </div>
          </div>
          <img v-if="productData.imageAddress" class="w-full object-cover mt-4" alt=""
            :src="productData.imageAddress || defaultProductImage" />
        </div>

        <!-- 产品特色 -->
        <div class="p-6 text-center mt-4" v-if="productData.features && productData.features.length > 0">
          <h3 class="text-3xl capitalize font-semibold mb-8">{{ t('products.productFeatures') }}</h3>

          <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-8">
            <div v-for="(feature, index) in productData.features" :key="index"
              class="relative border border-gray-200 rounded-md overflow-hidden p-4 box-border bg-white flex flex-col items-center">
              <div class="absolute inset-4 bg-feature-bg rounded-sm -z-0"></div>
              <div class="relative z-10 flex flex-col items-center h-full w-full gap-4 py-4">
                <img class="rounded-[50%] w-12 h-12 object-cover" :src="feature.icon || 'https://placehold.co/48x48'"
                  :alt="feature.title" />
                <div class="capitalize font-semibold text-center w-full">{{ feature.title }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 产品详情选项卡 -->
        <div class="p-6 text-left" v-if="productData.detailsTabs && productData.detailsTabs.length > 0">
          <h3 class="text-3xl capitalize font-semibold text-center mb-6">{{ t('products.productDetails') }}</h3>

          <div class="flex flex-col md:flex-row">
            <!-- 左侧Tab选项 -->
            <div class="w-full md:w-[200px] flex flex-row md:flex-col overflow-x-auto mb-4 md:mb-0 md:mr-4">
              <div v-for="(tab, index) in productData.detailsTabs" :key="index" @click="activeDetailTab = tab.id"
                :class="['relative h-[50px] cursor-pointer flex items-center px-4 md:px-0 whitespace-nowrap',
                  activeDetailTab === tab.id ? 'text-color1 font-semibold' : 'text-color2 font-medium']">
                <div
                  class="absolute top-0 left-0 right-0 md:left-[calc(50%_-_100px)] rounded-md md:rounded-tl-none md:rounded-tr-md md:rounded-br-md md:rounded-bl-none w-full md:w-[200px] h-[50px]">
                </div>
                <Icon v-if="activeDetailTab === tab.id" icon="mdi:chevron-right" class="text-color1 mr-2 md:mr-0"
                  width="13" height="19" />
                <div class="relative md:absolute md:top-[14px] md:left-[calc(50%_-_77px)] capitalize">{{ tab.title }}
                </div>
              </div>
            </div>

            <!-- 右侧内容区 -->
            <div
              class="w-full relative min-h-[300px] md:min-h-[500px] rounded-xl border-gray-200 border--2 border-solid border-[1px] p-4 md:p-10">
              <div
                class="rounded-t-xl rounded-b-none [background:linear-gradient(0deg,_rgba(230,_244,_242,_0),_#e6f4f2)] w-full h-[100px] md:h-[150px] absolute top-0 left-0">
              </div>

              <div class="relative z-10">
                <h4 class="py-2 md:py-4 text-xl md:text-3xl capitalize font-semibold text-color1 mb-4">{{
                  activeDetailContent?.title }}
                </h4>

                <div class="w-full border-b border-gray-200 mb-4 md:mb-6"></div>

                <div v-if="activeDetailContent" class="space-y-4 md:space-y-6">
                  <div class="text-sm leading-[160%] font-medium text-color2 whitespace-pre-line">
                    {{ activeFeatureDetails }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 产品概览 -->
        <div class="p-6 text-center"
          v-if="productData.overviewTableHtml && productData.overviewTableHtml.includes('<table')">
          <!-- <h3 class="text-3xl capitalize font-semibold mb-8">{{ t('products.productOverview') }}</h3> -->
          <div v-html="productData.overviewTableHtml" class="w-full "></div>
        </div>

        <!-- 产品条款 -->
        <div class="p-6 text-center" v-if="productData.terms.length > 0">
          <h3 class="text-3xl capitalize font-semibold mb-8">{{ t('products.productTerms') }}</h3>

          <div class="text-left space-y-5">
            <div v-for="(term, index) in productData.terms" :key="index" class="flex gap-2">
              <div class="w-5 h-5 rounded-[50%] bg-mediumseagreen flex-shrink-0 flex items-center justify-center">
                <span class="text-sm font-semibold text-white">{{ index + 1 }}</span>
              </div>
              <div class="text-sm text-color2 font-medium">{{ term.content }}</div>
            </div>

            <div class="bg-[#006B5D]/10 rounded-md p-4 mt-6">
              <div class="text-color1 capitalize">
                <span class="font-semibold">{{ t('products.importantReminder') }}：</span>
                <span class="text-sm font-medium">{{ t('products.importantReminderText') }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 产品下载 -->
        <div class="p-6 text-center" v-if="productData.download">
          <div class="border border-color1 p-4 rounded inline-block">
            <a :href="productData.download" target="_blank" class="text-color1 flex items-center">
              <Icon icon="mdi:download" class="mr-2" />
              <span>{{ t('products.previewBrochure') }}</span>
            </a>
          </div>
        </div>

        <!-- 调试信息区域 - 临时启用，排查完成后可关闭 -->
        <!-- <div class="p-6 border-t border-gray-200 mt-4" v-if="isDev">
          <div class="flex items-center mb-4">
            <div class="w-1 h-6 bg-red-500 mr-2"></div>
            <h3 class="text-base font-medium">调试信息</h3>
            <button class="ml-auto bg-gray-200 px-2 py-1 rounded text-xs" @click="showDebugInfo = !showDebugInfo">
              {{ showDebugInfo ? '隐藏' : '显示' }}
            </button>
          </div>
          <pre v-if="showDebugInfo"
            class="bg-gray-100 p-4 rounded text-xs overflow-auto max-h-96">{{ rawApiData }}</pre>
        </div> -->
      </div>

      <!-- 数据加载成功但内容为空时的提示 -->
      <div v-else class="w-full flex flex-col items-center justify-center p-20 bg-gray-50 rounded-lg">
        <Icon icon="mdi:information-outline" class="text-6xl text-gray-400 mb-4" />
        <div class="text-xl font-medium text-gray-600 mb-2">未找到完整的产品介绍数据</div>
        <div class="text-sm text-gray-500">
          您可以尝试刷新页面，或联系管理员更新产品信息。
        </div>
        <button class="mt-6 px-4 py-2 bg-color1 text-white rounded hover:bg-opacity-90 transition-all"
          @click="fetchProductData">
          重新加载
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { productAPI } from '@/api';
import { Icon } from '@iconify/vue';
import { useI18n } from 'vue-i18n';

// 初始化国际化
const { t } = useI18n();

// 常量定义
const defaultProductImage = 'https://wp-cdn.yukuii.top/v2/Q4K4LGU.png';
const FEATURE_ICONS = [
  'https://wp-cdn.yukuii.top/v2/sWBkV6q.png',
  'https://wp-cdn.yukuii.top/v2/0sqxnYK.png',
  'https://wp-cdn.yukuii.top/v2/ED1E9LC.png',
  'https://wp-cdn.yukuii.top/v2/BlFdcAn.png',
  'https://wp-cdn.yukuii.top/v2/CoEIk5K.png'
];
const AGE_GROUPS = [
  { key: 'age_group_15_to_60', label: '15天至60歲' },
  { key: 'age_group_61_to_75', label: '61歲至75歲' },
  { key: 'age_group_76_to_80', label: '76歲至80歲' }
];

const route = useRoute();
const productId = computed(() => route.params.id); // 从路由获取产品ID

// 加载状态
const loading = ref(true);
const error = ref(null);
const showDebugInfo = ref(false);
const rawApiData = ref('');

// 产品数据
const productData = ref({
  name: '',
  productName: '',
  description: '',
  imageAddress: '',
  features: [],
  overviewTable: [],
  overviewTableHtml: '',
  detailsTabs: [],
  detailsContent: {},
  terms: []
});

// 当前选中的详情选项卡ID
const activeDetailTab = ref('');

// 计算当前选中的详情内容
const activeDetailContent = computed(() => {
  return productData.value.detailsContent[activeDetailTab.value] || null;
});

// 获取当前特性的详情文本
const activeFeatureDetails = computed(() => {
  if (!activeDetailTab.value || !productData.value.detailsContent) return '';
  const currentContent = productData.value.detailsContent[activeDetailTab.value];
  return currentContent ? currentContent.rawDetails || '' : '';
});

// 获取产品数据
const fetchProductData = async () => {
  try {
    loading.value = true;
    error.value = null;

    if (!productId.value) {
      error.value = '未找到产品ID';
      loading.value = false;
      return;
    }

    console.log('正在获取产品数据，ID:', productId.value);
    const res = await productAPI.getProductDetail(productId.value);
    console.log('API返回数据结构:', {
      hasData: !!res,
      hasProductIntro: !!res?.productIntro,
      nestedProductIntro: !!res?.productIntroduction?.product_info,
      features: res?.productIntroduction?.product_info?.features?.length || 0
    });

    // 保存原始数据用于调试
    rawApiData.value = JSON.stringify(res, null, 2);

    if (res) {
      try {
        transformProductData(res);
        console.log('转换后的产品数据:', {
          name: productData.value.name,
          features: productData.value.features.length,
          tabs: productData.value.detailsTabs.length,
          overview: productData.value.overviewTableHtml ? '有数据' : '无数据',
          terms: productData.value.terms.length
        });

        if (!productData.value.name || productData.value.features.length === 0) {
          console.warn('产品数据不完整:', productData.value);
        }
      } catch (transformError) {
        console.error('转换产品数据时出错:', transformError);
        error.value = '处理产品数据时出错，请稍后重试';
      }
    } else {
      error.value = '未找到产品数据';
      console.error('API返回空数据');
    }
  } catch (err) {
    console.error('获取产品数据失败:', err);
    error.value = '获取产品数据失败，请稍后重试';
  } finally {
    loading.value = false;
  }
};

// 设置基本信息
const setBasicInfo = (data) => {
  // 获取产品名称
  const productName = data.productName || '';

  // 获取图片地址和下载地址
  const imageAddress = data.imageAddress || '';
  const download = data.download || '';

  // 获取产品描述
  const summary = data.description || '';

  console.log('设置基本信息:', {
    productName,
    summary: summary ? (summary.length > 30 ? summary.substring(0, 30) + '...' : summary) : '无',
    imageAddress: imageAddress ? '有图片' : '无图片',
    download: download ? '有下载链接' : '无下载链接'
  });

  productData.value.name = productName;
  productData.value.productName = productName;
  productData.value.description = summary;
  productData.value.imageAddress = imageAddress;
  productData.value.download = download;
};

// 转换产品特性
const transformFeatures = (features) => {
  if (!features || features.length === 0) {
    console.log('没有找到产品特性数据');
    return [];
  }

  console.log('产品特性原始数据:', features.map(f => f.title || f.name));

  // 处理不同格式的特性数据，支持新的API格式(name/value)和旧格式(title/details)
  const transformedFeatures = features.map((feature, index) => ({
    title: feature.title || feature.name || '',
    details: feature.details || feature.value || '',
    icon: feature.iconUrl || FEATURE_ICONS[index % FEATURE_ICONS.length] // 优先使用API返回的图标，否则循环使用预定义图标
  }));

  console.log('处理后的产品特性:', transformedFeatures.length, '个');

  return transformedFeatures;
};

// 转换产品详情选项卡和内容
const transformDetailsContent = (features) => {
  if (!features || features.length === 0) {
    productData.value.detailsTabs = [];
    productData.value.detailsContent = {};
    console.log('没有产品特性，不生成详情选项卡');
    return;
  }

  console.log('处理特性详情内容:', features.length, '个特性');

  // 转换选项卡
  productData.value.detailsTabs = features.map((feature, index) => ({
    id: `feature${index + 1}`,
    title: feature.title || feature.name || `特性 ${index + 1}`
  }));

  console.log('生成选项卡:', productData.value.detailsTabs.length, '个');

  // 设置默认选中的选项卡
  if (productData.value.detailsTabs.length > 0) {
    activeDetailTab.value = productData.value.detailsTabs[0].id;
    console.log('设置默认选中选项卡:', activeDetailTab.value);
  }

  // 转换详情内容
  productData.value.detailsContent = {};
  let validDetailsCount = 0;

  features.forEach((feature, index) => {
    const featureId = `feature${index + 1}`;
    const title = feature.title || feature.name || `特性 ${index + 1}`;

    // 将特性详情内容存储为sections，每段内容作为一个section
    const sections = [];
    const details = feature.details || feature.value || '';

    if (details) {
      // 按段落分割内容
      const contentParts = details.split(/\n{2,}/).filter(part => part.trim());
      console.log(`特性 "${title}" 有 ${contentParts.length} 段内容`);

      contentParts.forEach((part, i) => {
        sections.push({
          title: i === 0 ? title : '', // 第一段使用特性标题
          content: part.trim()
        });
      });

      validDetailsCount++;
    } else {
      console.log(`特性 "${title}" 没有详情内容`);
    }

    productData.value.detailsContent[featureId] = {
      title: title,
      sections: sections,
      rawDetails: details // 存储原始详情文本
    };
  });

  console.log('处理后的特性内容:', Object.keys(productData.value.detailsContent).length, '个，其中有详情的:', validDetailsCount, '个');
};

// 转换产品概览表格
const transformOverviewTable = (policy_details) => {
  const overviewTable = [];

  if (!policy_details) {
    productData.value.overviewTable = [];
    return;
  }

  // 投保年龄
  if (policy_details.policy_age) {
    overviewTable.push({
      label: '投保年齡',
      value: `${policy_details.policy_age.min || ''}至${policy_details.policy_age.max || ''}`
    });
  }

  // 保障年期
  if (policy_details.coverage_period) {
    overviewTable.push({
      label: '保障年期',
      value: policy_details.coverage_period
    });
  }

  // 保费供款年期
  if (policy_details.premium_payment_term) {
    overviewTable.push({
      label: '保費供款年期',
      value: policy_details.premium_payment_term
    });
  }

  // 保费缴付模式
  if (policy_details.premium_payment_mode && policy_details.premium_payment_mode.length > 0) {
    overviewTable.push({
      label: '保費繳付模式',
      value: policy_details.premium_payment_mode.join('、')
    });
  }

  // 保单货币
  if (policy_details.policy_currency && policy_details.policy_currency.length > 0) {
    overviewTable.push({
      label: '保單貨幣',
      value: policy_details.policy_currency.join('/')
    });
  }

  // 最低基本金额
  if (policy_details.minimum_basic_amount) {
    const minAmounts = [];
    for (const [currency, amount] of Object.entries(policy_details.minimum_basic_amount)) {
      minAmounts.push(`${amount}${currency}`);
    }
    if (minAmounts.length > 0) {
      overviewTable.push({
        label: '最低基本金額',
        value: minAmounts.join('/')
      });
    }
  }

  // 最高基本金额
  if (policy_details.maximum_basic_amount) {
    const maxAmounts = [];

    // 处理不同年龄段的最高基本金额
    AGE_GROUPS.forEach(group => {
      if (policy_details.maximum_basic_amount[group.key]) {
        const amounts = [];
        for (const [currency, amount] of Object.entries(policy_details.maximum_basic_amount[group.key])) {
          amounts.push(`${amount}${currency}`);
        }
        if (amounts.length > 0) {
          maxAmounts.push(`${amounts.join('/')}（${group.label}）`);
        }
      }
    });

    if (maxAmounts.length > 0) {
      overviewTable.push({
        label: '最高基本金額',
        value: maxAmounts.join('\n')
      });
    }
  }

  productData.value.overviewTable = overviewTable;
};

// 转换产品条款
const transformTerms = (note) => {
  if (!note) {
    productData.value.terms = [];
    return;
  }

  // 分割注释成条款项
  const noteLines = note.split(/\r?\n/).filter(line => line.trim());
  const noteItems = [];

  // 尝试提取数字前缀的条款项
  let currentItem = '';
  for (const line of noteLines) {
    if (/^\d+\./.test(line)) {
      // 如果当前已有内容，先保存
      if (currentItem) {
        noteItems.push({ content: currentItem.trim() });
      }
      // 移除数字前缀
      currentItem = line.replace(/^\d+\.\s*/, '');
    } else {
      // 继续添加到当前项
      currentItem += ' ' + line;
    }
  }

  // 添加最后一项
  if (currentItem) {
    noteItems.push({ content: currentItem.trim() });
  }

  // 如果没有提取到条款项，则直接使用整个note
  if (noteItems.length === 0 && note.trim()) {
    noteItems.push({ content: note.trim() });
  }

  productData.value.terms = noteItems;
};

// 转换API数据到组件所需格式
const transformProductData = (apiData) => {
  // 支持新的API返回格式
  const data = apiData.data || apiData;

  if (!data) return;

  // 检查产品介绍数据是否存在，同时支持productIntro和productIntroduction两种结构
  const productIntro = data.productIntro || data.productIntroduction;

  if (!productIntro) {
    console.error('产品介绍数据不存在');
    return;
  }

  console.log('处理产品介绍数据:', productIntro);

  // 处理可能的二级嵌套 - 有些 API 返回 productIntro.productIntro 结构
  const productInfo = productIntro.productIntro || productIntro;

  // 提取关键数据，考虑多级嵌套可能
  const product_info = productInfo.product_info || {};
  const policy_details = productInfo.policy_details || '';

  // 处理带有HTML表格的productOverview
  const overviewTableHtml = productIntro.productOverview || policy_details || '';

  const note = productIntro.note || data.note || '';
  const download = productIntro.download || data.download || productInfo.productBrochure || data.productBrochure || '';
  const imageAddress = productIntro.bannerImageUrl;

  // 设置小册子的 HTML
  productData.value.overviewTableHtml = overviewTableHtml;

  // 设置基本信息
  setBasicInfo({
    productName: productIntro.productName || productIntro.name || data.productName || '',
    imageAddress,
    download,
    description: productIntro.productSummary || product_info.summary || data.description || '',
    product_info // 传递完整的 product_info 对象
  });

  // 转换产品特性 - 优先使用productFeatures格式
  const features = productIntro.productFeatures || (product_info && product_info.features) || [];

  if (features && Array.isArray(features) && features.length > 0) {
    console.log('找到产品特性:', features.length, '个');
    productData.value.features = transformFeatures(features);
    transformDetailsContent(features);
  } else {
    console.warn('产品特性数据不存在或格式不正确');
    productData.value.features = [];
    productData.value.detailsTabs = [];
    productData.value.detailsContent = {};
  }

  // 处理产品条款
  transformTerms(note);
};

// 在组件挂载时获取数据
onMounted(() => {
  fetchProductData();
});

// 是否为开发环境
const isDev = computed(() => {
  return process.env.NODE_ENV === 'development';
});
</script>

<style scoped>
.bg-color1 {
  background-color: #006B5D;
}

.text-color1 {
  color: #006B5D;
}

.text-color2 {
  color: #333333;
}

.text-color3 {
  color: #1A1A1A;
}

.text-color4 {
  color: #666666;
}

.text-color5 {
  color: #2BB039;
}

.text-color7 {
  color: #FFFFFF;
}

.border-color1 {
  border-color: #006B5D;
}

.border-color4 {
  border-color: #666666;
}

.bg-mediumseagreen {
  background-color: #2BB039;
}

.bg-cornsilk {
  background-color: #FFF8E1;
}

.bg-feature-bg {
  background: radial-gradient(circle, rgba(44, 176, 57, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
}

@media (max-width: 768px) {
  .flex {
    flex-direction: column;
  }

  .flex-direction-row {
    flex-direction: row;
  }
}
</style>