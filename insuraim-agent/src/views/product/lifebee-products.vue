<template>
  <!-- 主要内容区域 -->
  <div class="container mx-auto py-6 px-4">
    <!-- 页面标题 -->
    <div
      class="title-section mb-6 p-6 rounded-lg shadow-md text-white bg-gradient-to-r from-green-500 via-green-600 to-green-700">
      <div class="flex items-center">
        <Icon icon="material-symbols:inventory-2" class="text-4xl mr-3" />
        <h1 class="text-2xl font-bold page-title">LifeBee 产品列表</h1>
      </div>
      <p class="mt-2 page-description">
        浏览和查找 LifeBee 保险产品
      </p>
    </div>

    <!-- 统计卡片 -->
    <a-card class="mb-6 rounded-lg" :bordered="false">
      <h1 class="text-2xl font-bold mb-4">产品统计</h1>
      <!-- 产品统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- 产品总数卡片 -->
        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-green-500">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">产品总数</p>
              <p class="text-2xl font-bold">{{ productTotal || 0 }}</p>
            </div>
            <div class="bg-green-100 p-2 rounded-full">
              <Icon icon="material-symbols:list-alt" class="text-2xl text-green-500" />
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-500">
            <span class="flex items-center">
              <Icon icon="material-symbols:info-outline" class="mr-1" />
              LifeBee 平台产品总数
            </span>
          </div>
        </div>

        <!-- 产品分类卡片 -->
        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-blue-500">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">产品分类</p>
              <p class="text-2xl font-bold">{{ categoryOptions.length || 0 }}</p>
            </div>
            <div class="bg-blue-100 p-2 rounded-full">
              <Icon icon="material-symbols:category" class="text-2xl text-blue-500" />
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-500">
            <span class="flex items-center">
              <Icon icon="material-symbols:info-outline" class="mr-1" />
              可用产品分类数量
            </span>
          </div>
        </div>

        <!-- 地区分布卡片 -->
        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-orange-500">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">销售地区</p>
              <p class="text-2xl font-bold">{{ regionOptions.length || 0 }}</p>
            </div>
            <div class="bg-orange-100 p-2 rounded-full">
              <Icon icon="material-symbols:public" class="text-2xl text-orange-500" />
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-500">
            <span class="flex items-center">
              <Icon icon="material-symbols:info-outline" class="mr-1" />
              产品销售地区数量
            </span>
          </div>
        </div>

        <!-- 保险公司卡片 -->
        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-purple-500">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">保险公司</p>
              <p class="text-2xl font-bold">{{ companyOptions.length || 0 }}</p>
            </div>
            <div class="bg-purple-100 p-2 rounded-full">
              <Icon icon="mdi:office-building" class="text-2xl text-purple-500" />
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-500">
            <span class="flex items-center">
              <Icon icon="material-symbols:info-outline" class="mr-1" />
              合作保险公司数量
            </span>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 产品列表 -->
    <div class="bg-white rounded-lg shadow-md p-6 mt-4">
      <!-- 筛选器 -->
      <div class="filter-section mb-6 p-6 rounded-lg bg-gradient-to-r from-gray-50 to-gray-100 border border-gray-200">
        <div class="flex flex-wrap items-center justify-between gap-4">
          <div class="filter-group flex flex-wrap items-center gap-3">
            <span class="text-gray-600 text-sm">地区:</span>
            <a-select v-model:value="queryParams.region" placeholder="选择地区" style="width: 120px;" class="region-select"
              size="middle" @change="handleRegionChange">
              <a-select-option value="">全部</a-select-option>
              <a-select-option v-for="item in regionOptions" :key="item.code" :value="item.name">
                {{ item.name }}
              </a-select-option>
            </a-select>

            <span class="text-gray-600 text-sm">保司:</span>
            <a-select v-model:value="queryParams.companyName" size="middle" style="width: 180px;"
              @change="handleCompanyFilterChange" allowClear show-search :filter-option="false"
              @search="handleCompanySearch" placeholder="选择保司" :options="filteredCompanyList">
            </a-select>

            <span class="text-gray-600 text-sm">分类:</span>
            <a-select v-model:value="queryParams.categoryCode" placeholder="选择分类" style="width: 150px;"
              class="category-select" size="middle" @change="handleCategoryChange">
              <a-select-option value="">全部</a-select-option>
              <a-select-option v-for="item in categoryOptions" :key="item.code" :value="item.code">
                {{ item.name }}
              </a-select-option>
            </a-select>

            <span class="text-gray-600 text-sm">可比较:</span>
            <a-switch v-model:checked="queryParams.comparable" @change="handleComparableChange" />
          </div>

          <div class="search-group flex items-center gap-3">
            <a-input-search placeholder="搜索产品名称或保司名称" style="width: 220px" size="middle" @search="searchProduct" />
            <a-button type="primary" size="middle" @click="refreshProductData" class="refresh-btn">
              <template #icon>
                <Icon icon="material-symbols:refresh" />
              </template>
              <span>重置</span>
            </a-button>
          </div>
        </div>
      </div>

      <!-- 产品表格 -->
      <a-table class="product-table" :dataSource="productList" :columns="productColumns" :pagination="{
        total: productTotal,
        showSizeChanger: true,
        showQuickJumper: true,
        pageSize: queryParams.pageSize,
        pageSizeOptions: ['10', '20', '50', '100'],
        showTotal: (total) => `共 ${total} 条`,
        size: 'default'
      }" size="middle" :rowClassName="(record, index) => (index % 2 === 1 ? 'table-striped' : '')" bordered
        :loading="productLoading" @change="handleProductTableChange" :customRow="customRowHandler"
        @resizeColumn="handleResizeColumn">
        <template #bodyCell="{ column, record }">
          <!-- 产品名称列 -->
          <template v-if="column.dataIndex === 'productName'">
            <div class="flex items-center">
              <div class="w-6 h-6 mr-2 flex-shrink-0">
                <img v-if="record.logoUrl && !record.logoError" :src="record.logoUrl" :alt="record.productName"
                  class="w-full h-full object-contain rounded product-logo" @error="() => handleImageError(record)" />
                <div v-else class="w-full h-full rounded logo-placeholder flex items-center justify-center">
                  <span class="text-xs text-gray-500">无</span>
                </div>
              </div>
              <a-tooltip :title="record.productName">
                <span class="truncate">{{ record.productName }}</span>
              </a-tooltip>
            </div>
          </template>

          <!-- 产品类型列 -->
          <template v-if="column.dataIndex === 'productType'">
            <span>{{ record.productType === 'BASIC' ? '基本计划' : record.productType }}</span>
          </template>

          <!-- 销售状态列 -->
          <template v-if="column.dataIndex === 'sellStatus'">
            <a-tag :color="record.sellStatus === 'SELLING' ? 'success' : 'error'" class="rounded-full px-3">
              <div class="flex items-center">
                <div class="w-2 h-2 rounded-full mr-1"
                  :class="record.sellStatus === 'SELLING' ? 'bg-green-500' : 'bg-red-500'">
                </div>
                <span>{{ record.sellStatus === 'SELLING' ? '在售' : '停售' }}</span>
              </div>
            </a-tag>
          </template>

          <!-- 是否可比较列 -->
          <template v-if="column.dataIndex === 'isComparable'">
            <a-tag :color="record.isComparable === 1 ? 'blue' : 'default'">
              {{ record.isComparable === 1 ? '可比较' : '不可比较' }}
            </a-tag>
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, reactive, ref } from 'vue';
import { productAPI } from '@/api';
import { message } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { Icon } from '@iconify/vue';

// 路由实例
const router = useRouter();

// 搜索关键词
const searchCompanyKeyword = ref('');
const filteredCompanyList = ref([]);

// 产品数据列和配置
const productColumns = ref([
  {
    title: '产品名称',
    dataIndex: 'productName',
    key: 'productName',
    width: 200,
    ellipsis: true,
    resizable: true,
    minWidth: 150,
  },
  {
    title: '产品类型',
    dataIndex: 'productType',
    key: 'productType',
    width: 100,
    resizable: true,
    minWidth: 80,
  },
  {
    title: '产品分类',
    dataIndex: 'categoryName',
    key: 'categoryName',
    width: 120,
    resizable: true,
    minWidth: 100,
  },
  {
    title: '保险公司',
    dataIndex: 'companyName',
    key: 'companyName',
    width: 150,
    resizable: true,
    minWidth: 120,
  },
  {
    title: '销售地区',
    dataIndex: 'region',
    key: 'region',
    width: 100,
    resizable: true,
    minWidth: 80,
  },
  {
    title: '销售状态',
    dataIndex: 'sellStatus',
    key: 'sellStatus',
    width: 100,
    resizable: true,
    minWidth: 80,
  },
  {
    title: '可比较',
    dataIndex: 'isComparable',
    key: 'isComparable',
    width: 100,
    resizable: true,
    minWidth: 80,
  },
]);

// 产品列表数据
const productList = ref([]);
const productLoading = ref(false);
const productTotal = ref(0);
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  categoryCode: undefined,
  search: undefined,
  region: undefined,
  companyCode: undefined,
  companyName: undefined,
  comparable: false,
});

// 选项数据
const categoryOptions = ref([]);
const regionOptions = ref([]);
const companyOptions = ref([]);

// 组件挂载时初始化
onMounted(() => {
  loadProductData();
  loadOptionsData();
});

// 加载产品数据
const loadProductData = async () => {
  try {
    productLoading.value = true;
    const result = await productAPI.getLifeBeeProductPage(queryParams);
    productList.value = result.records || [];
    productTotal.value = result.total || 0;
  } catch (error) {
    console.error('加载产品数据失败:', error);
    message.error('加载产品数据失败');
  } finally {
    productLoading.value = false;
  }
};

// 加载选项数据
const loadOptionsData = async () => {
  try {
    // 从产品数据中提取唯一的分类、地区和公司选项
    const allProducts = await productAPI.getLifeBeeProductPage({ pageNum: 1, pageSize: 1000 });
    const products = allProducts.records || [];

    // 提取分类选项
    const categories = [...new Set(products.map(p => ({ code: p.categoryCode, name: p.categoryName })).filter(c => c.code))];
    categoryOptions.value = categories.filter((item, index, self) =>
      index === self.findIndex(t => t.code === item.code)
    );

    // 提取地区选项
    const regions = [...new Set(products.map(p => p.region).filter(r => r))];
    regionOptions.value = regions.map(r => ({ code: r, name: r }));

    // 提取公司选项
    const companies = [...new Set(products.map(p => ({ code: p.companyCode, name: p.companyName })).filter(c => c.code))];
    companyOptions.value = companies.filter((item, index, self) =>
      index === self.findIndex(t => t.code === item.code)
    );

    // 初始化过滤列表
    filterCompanyList();
  } catch (error) {
    console.error('加载选项数据失败:', error);
  }
};

// 导航到产品详情页面
const navigateToProductDetail = (record) => {
  router.push(`/lifebee-product/${record.productCode}`);
};

// 搜索产品
const searchProduct = (value) => {
  queryParams.search = value;
  queryParams.pageNum = 1;
  loadProductData();
};

// 处理产品表格分页变化
const handleProductTableChange = (pagination) => {
  queryParams.pageNum = pagination.current;
  queryParams.pageSize = pagination.pageSize;
  loadProductData();
};

// 刷新产品数据
const refreshProductData = () => {
  queryParams.categoryCode = undefined;
  queryParams.search = undefined;
  queryParams.region = undefined;
  queryParams.companyCode = undefined;
  queryParams.companyName = undefined;
  queryParams.comparable = false;
  queryParams.pageNum = 1;
  loadProductData();

  // 重置搜索关键词并重新过滤保司列表
  searchCompanyKeyword.value = '';
  filterCompanyList();
};

// 处理分类变化
const handleCategoryChange = (value) => {
  queryParams.pageNum = 1;
  queryParams.categoryCode = value;
  loadProductData();
};

// 处理地区变化
const handleRegionChange = () => {
  queryParams.pageNum = 1;
  // 清空当前选中的保司，因为地区变化可能导致保司不可用
  queryParams.companyName = undefined;
  // 重新过滤保司列表
  filterCompanyList();
  // 重新加载产品数据
  loadProductData();
};

// 处理可比较开关变化
const handleComparableChange = (checked) => {
  queryParams.comparable = checked;
  queryParams.pageNum = 1;
  loadProductData();
};

// 自定义行点击处理函数
const customRowHandler = (record) => {
  return {
    onClick: () => {
      navigateToProductDetail(record);
    },
    style: 'cursor: pointer;'
  };
};

// 处理列宽度调整
const handleResizeColumn = (w, col) => {
  // 查找并更新对应列的宽度
  const targetColumn = productColumns.value.find(column => column.key === col.key);
  if (targetColumn) {
    targetColumn.width = w;
  }
};

// 统一的保司过滤函数
const filterCompanyList = () => {
  let filtered = companyOptions.value || [];

  // 根据选中的地区过滤
  if (queryParams.region) {
    // 这里需要根据实际数据结构调整过滤逻辑
    // 暂时显示所有公司，实际使用时可能需要调整
  }

  // 根据搜索关键词过滤
  if (searchCompanyKeyword.value) {
    filtered = filtered.filter(company =>
      company.name.toLowerCase().includes(searchCompanyKeyword.value.toLowerCase())
    );
  }

  filteredCompanyList.value = filtered.map(company => ({
    label: company.name,
    value: company.name,
    code: company.code
  }));
};

// 处理公司搜索
const handleCompanySearch = (value) => {
  searchCompanyKeyword.value = value;
  filterCompanyList();
};

// 处理公司筛选变化
const handleCompanyFilterChange = (value) => {
  queryParams.companyName = value;
  queryParams.pageNum = 1;
  loadProductData();
};

// 处理图片加载错误
const handleImageError = (record) => {
  // 当图片加载失败时，标记该记录的logo为错误状态
  record.logoError = true;
};
</script>

<style scoped>
/* 主题色定义 */
:root {
  --color-primary: #10B981;
  --color-primary-hover: #059669;
  --color-secondary: #6366F1;
  --color-success: #10B981;
  --color-warning: #F59E0B;
  --color-error: #EF4444;
  --color-info: #6B7280;
}

/* 标题区域动画 */
.title-section {
  background-size: 200% 200%;
  animation: gradientAnimation 5s ease infinite;
}

@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

.page-title {
  color: #fff;
}

.page-description {
  color: #e0e0e0;
}

/* 统计卡片样式 */
.stat-card {
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 筛选区域样式 */
.filter-section {
  transition: all 0.3s ease;
}

/* 表格样式 */
:deep(.ant-table-pagination) {
  margin: 24px 0;
}

/* 表格条纹样式 */
:deep(.table-striped) {
  background-color: rgba(241, 245, 249, 0.5);
}

/* 行高 */
:deep(.ant-table-tbody > tr > td) {
  padding: 12px;
  font-size: 14px;
}

/* 悬停效果 */
:deep(.ant-table-tbody > tr:hover > td) {
  background-color: rgba(236, 255, 236, 0.7) !important;
}

/* 表头样式 */
:deep(.ant-table-thead > tr > th) {
  padding: 12px;
  font-size: 14px;
  font-weight: 600;
  background-color: #f8fafc;
}

/* 按钮样式 */
.refresh-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  transform: scale(1.05);
}

/* 产品logo样式 */
.product-logo {
  transition: transform 0.2s ease;
}

.product-logo:hover {
  transform: scale(1.1);
}

/* logo占位符样式 */
.logo-placeholder {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border: 1px solid #e1e5e9;
}

/* 响应式布局调整 */
@media (max-width: 768px) {
  .filter-section {
    flex-direction: column;
  }

  .filter-group,
  .search-group {
    width: 100%;
    margin-bottom: 12px;
  }
}

/* 确保所有按钮的图标和文本垂直居中对齐 */
:deep(.ant-btn) {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

:deep(.ant-btn .anticon),
:deep(.ant-btn .iconify) {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  line-height: 1 !important;
  vertical-align: middle !important;
}

:deep(.ant-btn .anticon + span),
:deep(.ant-btn .iconify + span),
:deep(.ant-btn span + .anticon),
:deep(.ant-btn span + .iconify) {
  margin-left: 8px !important;
}
</style>
