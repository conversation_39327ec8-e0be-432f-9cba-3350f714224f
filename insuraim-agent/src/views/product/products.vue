<template>
  <!-- 主要内容区域 -->
  <div class="container mx-auto py-6 px-4">
    <!-- 页面标题 -->
    <div
      class="title-section mb-6 p-6 rounded-lg shadow-md text-white bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700">
      <div class="flex items-center">
        <Icon icon="material-symbols:inventory-2" class="text-4xl mr-3" />
        <h1 class="text-2xl font-bold page-title">{{ t('products.productList') }}</h1>
      </div>
      <p class="mt-2 page-description">
        {{ t('products.browseAndFind') }}
      </p>
    </div>

    <!-- 统计卡片 -->
    <a-card class="mb-6 rounded-lg" :bordered="false">
      <h1 class="text-2xl font-bold mb-4">{{ t('products.statistics') }}</h1>
      <!-- 产品统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- 产品总数卡片 -->
        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-blue-500">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">{{ t('products.totalProducts') }}</p>
              <p class="text-2xl font-bold">{{ productTotal || 0 }}</p>
            </div>
            <div class="bg-blue-100 p-2 rounded-full">
              <Icon icon="material-symbols:list-alt" class="text-2xl text-blue-500" />
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-500">
            <span class="flex items-center">
              <Icon icon="material-symbols:info-outline" class="mr-1" />
              {{ t('products.totalProductsDesc') }}
            </span>
          </div>
        </div>

        <!-- 收藏产品卡片 -->
        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-green-500">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <p class="text-gray-500 text-sm">{{ t('products.favoriteProducts') }}</p>
              <p class="text-2xl font-bold">{{ favoriteProductIds.length || 0 }}</p>
            </div>
            <div class="bg-green-100 p-2 rounded-full">
              <Icon icon="material-symbols:star" class="text-2xl text-green-500" />
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-500">
            <span class="flex items-center">
              <Icon icon="material-symbols:info-outline" class="mr-1" />
              {{ t('products.favoriteProductsDesc') }}
            </span>
          </div>
        </div>

        <!-- 产品分类卡片 -->
        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-orange-500">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">{{ t('products.categories') }}</p>
              <p class="text-2xl font-bold">{{ categoryOptions.length || 0 }}</p>
            </div>
            <div class="bg-orange-100 p-2 rounded-full">
              <Icon icon="material-symbols:category" class="text-2xl text-orange-500" />
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-500">
            <span class="flex items-center">
              <Icon icon="material-symbols:info-outline" class="mr-1" />
              {{ t('products.categoriesDesc') }}
            </span>
          </div>
        </div>

        <!-- 地区分布卡片 -->
        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-purple-500">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-500 text-sm">{{ t('products.regions') }}</p>
              <p class="text-2xl font-bold">{{ regionOptions.length || 0 }}</p>
            </div>
            <div class="bg-purple-100 p-2 rounded-full">
              <Icon icon="material-symbols:public" class="text-2xl text-purple-500" />
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-500">
            <span class="flex items-center">
              <Icon icon="material-symbols:info-outline" class="mr-1" />
              {{ t('products.regionsDesc') }}
            </span>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 产品列表 -->
    <div class="bg-white rounded-lg shadow-md p-6 mt-4">
      <!-- 筛选器 -->
      <div class="filter-section mb-6 p-6 rounded-lg bg-gradient-to-r from-gray-50 to-gray-100 border border-gray-200">
        <div class="flex flex-wrap items-center justify-between gap-4">
          <div class="filter-group flex flex-wrap items-center gap-3">
            <span class="text-gray-600 text-sm">{{ t('products.region') }}:</span>
            <a-select v-model:value="queryParams.region" :placeholder="t('products.region')" style="width: 120px;"
              class="region-select" size="middle" @change="handleRegionChange">
              <a-select-option value="">{{ t('products.all') }}</a-select-option>
              <a-select-option v-for="item in regionOptions" :key="item.code" :value="item.name">
                {{ item.name }}
              </a-select-option>
            </a-select>

            <span class="text-gray-600 text-sm">保司:</span>
            <a-select v-model:value="queryParams.companyName" size="middle" style="width: 180px;"
              @change="handleCompanyFilterChange" allowClear show-search :filter-option="false"
              @search="handleCompanySearch" placeholder="选择保司" :options="filteredCompanyList">
            </a-select>

            <span class="text-gray-600 text-sm">{{ t('products.category') }}:</span>
            <a-select v-model:value="queryParams.categoryCode" :placeholder="t('products.category')"
              style="width: 150px;" class="category-select" size="middle" @change="handleCategoryChange">
              <a-select-option value="">{{ t('products.all') }}</a-select-option>
              <a-select-option v-for="item in categoryOptions" :key="item.code" :value="item.code">
                {{ item.name }}
              </a-select-option>
            </a-select>
          </div>

          <div class="search-group flex items-center gap-3">
            <a-input-search :placeholder="t('products.searchPlaceholder')" style="width: 220px" size="middle"
              @search="searchProduct" />
            <a-button type="primary" size="middle" @click="refreshProductData" class="refresh-btn">
              <template #icon>
                <Icon icon="material-symbols:refresh" />
              </template>
              <span>{{ t('products.reset') }}</span>
            </a-button>
          </div>
        </div>
      </div>

      <!-- 产品表格 -->
      <a-table class="product-table" :dataSource="productList" :columns="productColumns" :pagination="{
        total: productTotal,
        showSizeChanger: true,
        showQuickJumper: true,
        pageSize: queryParams.pageSize,
        pageSizeOptions: ['10', '20', '50', '100'],
        showTotal: (total) => t('products.total', { total }),
        size: 'default'
      }" size="middle" :rowClassName="(record, index) => (index % 2 === 1 ? 'table-striped' : '')" bordered
        :loading="productLoading" @change="handleProductTableChange" :customRow="customRowHandler"
        @resizeColumn="handleResizeColumn">
        <template #bodyCell="{ column, record }">
          <!-- 产品名称列 -->
          <template v-if="column.dataIndex === 'productName'">
            <div class="flex items-center">
              <div class="w-6 h-6 mr-2 flex-shrink-0">
                <img v-if="record.logoUrl && !record.logoError" :src="record.logoUrl" :alt="record.productName"
                  class="w-full h-full object-contain rounded product-logo" @error="() => handleImageError(record)" />
                <div v-else class="w-full h-full rounded logo-placeholder flex items-center justify-center">
                  <span class="text-xs text-gray-500">无</span>
                </div>
              </div>
              <a-tooltip :title="record.productName">
                <span class="truncate">{{ record.productName }}</span>
              </a-tooltip>
              <StarFilled v-if="record.isFavorited" class="text-yellow-400 ml-1 flex-shrink-0" />
            </div>
          </template>

          <!-- 产品类型列 -->
          <template v-if="column.dataIndex === 'productType'">
            <span>{{ record.productType === 'BASIC' ? '基本计划' : record.productType }}</span>
          </template>

          <!-- 状态列 -->
          <template v-if="column.dataIndex === 'status'">
            <a-tag :color="record.status === 1 ? 'success' : 'error'" class="rounded-full px-3">
              <div class="flex items-center">
                <div class="w-2 h-2 rounded-full mr-1" :class="record.status === 1 ? 'bg-green-500' : 'bg-red-500'">
                </div>
                <span>{{ record.status === 1 ? '在售' : '停售' }}</span>
              </div>
            </a-tag>
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, reactive, ref } from 'vue';
import { companyAPI, productAPI } from '@/api';
import { message } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { useProductStore } from '@/store/modules/product';
import { AreaEnum } from '@/store/modules/order';
import { StarFilled } from '@ant-design/icons-vue';
import { Icon } from '@iconify/vue';

// 初始化国际化
const { t } = useI18n();

// 获取产品store
const productStore = useProductStore();
const companyList = ref([]);

// 用户收藏的产品ID列表
const favoriteProductIds = ref([]);

// 加载用户收藏产品
const loadFavoriteProducts = async () => {
  try {
    const res = await productAPI.getFavoriteProducts();
    favoriteProductIds.value = (res || []).map(p => p.id);
    // 如果产品列表已加载，则补充收藏标记
    if (productList.value.length) {
      productList.value = productList.value.map(item => ({
        ...item,
        isFavorited: favoriteProductIds.value.includes(item.id),
      }));
    }
  } catch (error) {
    console.error('加载收藏列表失败:', error);
  }
};

// 加载保司列表
const loadCompanyList = async () => {
  try {
    const result = await companyAPI.getCompanyPage();
    companyList.value = (result || []).map(company => ({
      label: company.name,
      value: company.name,
      code: company.code,
      region: company.region
    }));
    // 初始化过滤列表
    filterCompanyList();
  } catch (error) {
    console.error('加载保司列表失败:', error);
    message.error('加载保司列表失败');
  }
};

// 路由实例
const router = useRouter();

// 搜索关键词
const searchCompanyKeyword = ref('');
const filteredCompanyList = ref([]);

// 险种分类列表
const categoryOptions = computed(() => productStore.productCategoryList);

// 地区选项列表
const regionOptions = computed(() => [
  { code: 'HK', name: AreaEnum.HONGKONG },
  { code: 'MO', name: AreaEnum.MACAO },
  { code: 'SG', name: AreaEnum.SINGAPORE },
  { code: 'BM', name: AreaEnum.BERMUDA },
]);

// 组件挂载时初始化
onMounted(() => {
  loadFavoriteProducts();
  loadProductData();
  loadCompanyList();
  productStore.getProductCategoryList();
});

// 产品数据列和配置
const productColumns = ref([
  {
    title: t('products.productName'),
    dataIndex: 'productName',
    key: 'productName',
    width: 180,
    ellipsis: true,
    resizable: true,
    minWidth: 120,
  },
  {
    title: t('products.productType'),
    dataIndex: 'productType',
    key: 'productType',
    width: 80,
    resizable: true,
    minWidth: 80,
  },
  {
    title: t('products.category'),
    dataIndex: 'categoryName',
    key: 'categoryName',
    width: 80,
    resizable: true,
    minWidth: 80,
  },
  {
    title: t('products.insuranceCompany'),
    dataIndex: 'companyName',
    key: 'companyName',
    width: 100,
    resizable: true,
    minWidth: 100,
  },
  {
    title: '地区',
    dataIndex: 'region',
    key: 'region',
    width: 80,
    resizable: true,
    minWidth: 80,
  },
  {
    title: '保障期限',
    dataIndex: 'guaranteePeriod',
    key: 'guaranteePeriod',
    width: 100,
    resizable: true,
    minWidth: 80,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80,
    resizable: true,
    minWidth: 80,
  },

]);

// 产品列表数据
const productList = ref([]);
const productLoading = ref(false);
const productTotal = ref(0);
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  productName: undefined,        // 产品名称（模糊查询）
  companyCode: undefined,        // 保险公司代码
  companyName: undefined,        // 保险公司名称（模糊查询）
  categoryCode: undefined,       // 产品类别代码
  categoryName: undefined,       // 产品类别名称
  region: undefined,             // 销售地区
  status: undefined,             // 启用状态
  isPrepayment: undefined,       // 是否允许预缴
  keyword: undefined,            // 关键词搜索（产品名称或公司名称）
});

// 加载产品数据
const loadProductData = async () => {
  try {
    productLoading.value = true;
    const result = await productAPI.getProductList(queryParams);
    const records = result.records || [];
    const favIds = favoriteProductIds.value;
    productList.value = records.map(item => ({
      ...item,
      isFavorited: favIds.includes(item.id),
    }));
    productTotal.value = result.total || 0;
  } catch (error) {
    console.error('加载产品数据失败:', error);
    message.error(t('products.loadProductError'));
  } finally {
    productLoading.value = false;
  }
};

// 导航到产品详情页面
const navigateToProductDetail = (record) => {
  router.push(`/product/${record.id}`);
};

// 搜索产品
const searchProduct = (value) => {
  queryParams.keyword = value;
  queryParams.pageNum = 1;
  loadProductData();
};

// 处理产品表格分页变化
const handleProductTableChange = (pagination) => {
  queryParams.pageNum = pagination.current;
  queryParams.pageSize = pagination.pageSize;
  loadProductData();
};

// 刷新产品数据
const refreshProductData = () => {
  queryParams.productName = undefined;
  queryParams.companyCode = undefined;
  queryParams.companyName = undefined;
  queryParams.categoryCode = undefined;
  queryParams.categoryName = undefined;
  queryParams.region = undefined;
  queryParams.status = undefined;
  queryParams.isPrepayment = undefined;
  queryParams.keyword = undefined;
  queryParams.pageNum = 1;
  loadProductData();

  // 重置搜索关键词并重新过滤保司列表
  searchCompanyKeyword.value = '';
  filterCompanyList();
};

// 处理险种变化
const handleCategoryChange = (value) => {
  queryParams.pageNum = 1;
  queryParams.categoryCode = value;
  loadProductData();
};

// 处理地区变化
const handleRegionChange = () => {
  queryParams.pageNum = 1;
  // 清空当前选中的保司，因为地区变化可能导致保司不可用
  queryParams.companyName = undefined;
  // 重新过滤保司列表
  filterCompanyList();
  // 重新加载产品数据
  loadProductData();
};

// 自定义行点击处理函数
const customRowHandler = (record) => {
  return {
    onClick: () => {
      navigateToProductDetail(record);
    },
    style: 'cursor: pointer;'
  };
};

// 处理列宽度调整
const handleResizeColumn = (w, col) => {
  // 查找并更新对应列的宽度
  const targetColumn = productColumns.value.find(column => column.key === col.key);
  if (targetColumn) {
    targetColumn.width = w;
  }
};

// 统一的保司过滤函数
const filterCompanyList = () => {
  let filtered = companyList.value || [];

  // 根据选中的地区过滤
  if (queryParams.region) {
    filtered = filtered.filter(company => company.region === queryParams.region);
  }

  filteredCompanyList.value = filtered;
};

// 处理公司搜索
const handleCompanySearch = (value) => {
  searchCompanyKeyword.value = value;
  filterCompanyList();
};

// 处理公司筛选变化
const handleCompanyFilterChange = (value) => {
  queryParams.companyName = value;
  queryParams.pageNum = 1;
  loadProductData();
};

// 处理图片加载错误
const handleImageError = (record) => {
  // 当图片加载失败时，标记该记录的logo为错误状态
  record.logoError = true;
};
</script>

<style scoped>
/* 主题色定义 */
:root {
  --color-primary: #3B82F6;
  --color-primary-hover: #2563EB;
  --color-secondary: #6366F1;
  --color-success: #10B981;
  --color-warning: #F59E0B;
  --color-error: #EF4444;
  --color-info: #6B7280;
}

/* 标题区域动画 */
.title-section {
  background-size: 200% 200%;
  animation: gradientAnimation 5s ease infinite;
}

@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

.page-title {
  color: #fff;
}

.page-description {
  color: #e0e0e0;
}

/* 统计卡片样式 */
.stat-card {
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 筛选区域样式 */
.filter-section {
  transition: all 0.3s ease;
}

/* 表格样式 */
:deep(.ant-table-pagination) {
  margin: 24px 0;
}

/* 表格条纹样式 */
:deep(.table-striped) {
  background-color: rgba(241, 245, 249, 0.5);
}

/* 行高 */
:deep(.ant-table-tbody > tr > td) {
  padding: 12px;
  font-size: 14px;
}

/* 悬停效果 */
:deep(.ant-table-tbody > tr:hover > td) {
  background-color: rgba(236, 240, 255, 0.7) !important;
}

/* 表头样式 */
:deep(.ant-table-thead > tr > th) {
  padding: 12px;
  font-size: 14px;
  font-weight: 600;
  background-color: #f8fafc;
}

/* 按钮样式 */
.refresh-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  transform: scale(1.05);
}

/* 产品logo样式 */
.product-logo {
  transition: transform 0.2s ease;
}

.product-logo:hover {
  transform: scale(1.1);
}

/* logo占位符样式 */
.logo-placeholder {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border: 1px solid #e1e5e9;
}

/* 收藏星标颜色 */
.text-yellow-400 {
  color: #FADB14;
}

/* 响应式布局调整 */
@media (max-width: 768px) {
  .filter-section {
    flex-direction: column;
  }

  .filter-group,
  .search-group {
    width: 100%;
    margin-bottom: 12px;
  }
}

/* 确保所有按钮的图标和文本垂直居中对齐 */
:deep(.ant-btn) {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

:deep(.ant-btn .anticon),
:deep(.ant-btn .iconify) {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  line-height: 1 !important;
  vertical-align: middle !important;
}

:deep(.ant-btn .anticon + span),
:deep(.ant-btn .iconify + span),
:deep(.ant-btn span + .anticon),
:deep(.ant-btn span + .iconify) {
  margin-left: 8px !important;
}
</style>