<template>
  <div class="mx-auto">
    <!-- 产品详情内容 -->
    <div class="bg-white p-6 shadow-sm rounded-lg">
      <!-- 显示加载状态 -->
      <div v-if="loading" class="flex justify-center items-center h-64">
        <a-spin size="large" />
      </div>

      <template v-else-if="product">
        <!-- 产品标题和标签 -->
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center">
            <img v-if="product.logoUrl" :src="product.logoUrl" :alt="product.companyName"
              class="h-8 w-auto mr-3 object-contain" />
            <h2 class="text-xl font-medium mr-3">{{ product.productName }}</h2>
            <a-tag color="blue" class="rounded-sm">{{ product.categoryName || t('products.category') }}</a-tag>
          </div>
          <a-button type="primary" ghost @click="goBack" class="back-button">
            <template #icon>
              <Icon icon="mdi:arrow-left" />
            </template>
            <span>{{ t('products.backToList') }}</span>
          </a-button>
        </div>
        <span> LifeBeeProductDetail</span>
        <!-- 产品描述 -->
        <p class="text-gray-600 mb-6">{{ product.description }}</p>

        <!-- 选项卡 -->
        <a-tabs v-model:activeKey="activeTab">
          <a-tab-pane key="info" :tab="t('products.productInfoTab')">
            <div class="space-y-6">
              <div v-for="section in sortedProductDetails" :key="section.key" class="mb-6">
                <h3 class="text-base font-medium mb-4">{{ getLocalizedName(section.nameI18n) }}</h3>
                <div class="space-y-2">
                  <div v-for="attr in sortedAttributes(section.attributes)" :key="attr.name"
                    class="flex py-2 border-b border-gray-100">
                    <span class="text-gray-600 text-sm w-48 flex-shrink-0 mr-6">{{ getLocalizedName(attr.nameI18n)
                      }}</span>
                    <span class="text-gray-900 text-sm flex-1">{{ attr.value }}</span>
                  </div>
                </div>
              </div>
            </div>
          </a-tab-pane>

          <a-tab-pane key="highlights" :tab="t('products.productHighlightsTab')">
            <div v-if="product.productHighlights && product.productHighlights.length > 0">
              <div v-for="(highlight, index) in product.productHighlights" :key="index" class="mb-4">
                <div class="flex items-start">
                  <div class="mr-2 mt-1">
                    <Icon icon="mdi:check-circle" class="text-green-500 text-lg" />
                  </div>
                  <div>
                    <p class="text-gray-700">{{ highlight }}</p>
                  </div>
                </div>
              </div>
            </div>
            <a-empty v-else :description="t('products.noHighlights')" />
          </a-tab-pane>

          <a-tab-pane key="files" :tab="t('products.productFilesTab')">
            <div v-if="product.productFiles && product.productFiles.length > 0" class="grid grid-cols-1 gap-4">
              <div v-for="file in product.productFiles" :key="file.fileName"
                class="border rounded p-4 hover:shadow-md transition-shadow">
                <div class="flex flex-col">
                  <div class="flex justify-between items-center mb-2">
                    <div class="flex items-center">
                      <Icon icon="mdi:file-pdf-box" class="text-red-500 text-xl mr-2" />
                      <span class="text-gray-800 font-medium">{{ file.fileName }}</span>
                    </div>
                    <a-button type="link" @click="previewFile(file)" class="preview-button">
                      <template #icon>
                        <Icon icon="mdi:eye-outline" />
                      </template>
                      {{ t('products.preview') }}
                    </a-button>
                  </div>
                  <div class="flex items-center text-sm text-gray-500 gap-4">
                    <span>{{ t('products.uploader') }}：{{ file.author }}</span>
                    <span>{{ t('products.uploadTime') }}：{{ new Date(file.uploadTime).toLocaleDateString() }}</span>
                  </div>
                </div>
              </div>
            </div>
            <a-empty v-else :description="t('products.noFiles')" />
          </a-tab-pane>
        </a-tabs>
      </template>

      <!-- 无数据展示 -->
      <a-empty v-else :description="t('products.noProductInfo')" />
    </div>

    <!-- PDF预览弹窗 -->
    <a-modal v-model:visible="filePreviewVisible" :title="t('products.filePreview')" :footer="null" width="80%"
      @cancel="closeFilePreview">
      <template #title>
        <div class="flex justify-between items-center">
          <span>{{ t('products.filePreview') }}</span>
        </div>
      </template>
      <div class="pdf-container">
        <PDF v-if="fileUrl" :src="fileUrl" :pdfWidth="800" />
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import { productAPI } from '@/api';
import { Icon } from '@iconify/vue';
import PDF from 'pdf-vue3';
import { useI18n } from 'vue-i18n';

// 初始化国际化
const { t, locale } = useI18n();

const router = useRouter();
const route = useRoute();
const productCode = route.params.code;

// 状态
const product = ref(null);
const loading = ref(true);
const activeTab = ref('info');
const filePreviewVisible = ref(false);
const fileUrl = ref(null);

// 获取产品详情
const fetchProductDetail = async () => {
  if (!productCode) {
    message.error(t('products.productCodeNotExist'));
    router.push('/products');
    return;
  }

  try {
    loading.value = true;
    const result = await productAPI.getLifeBeeProductDetail(productCode);

    if (!result) {
      message.error(t('products.getProductDetailFailed'));
      return;
    }

    product.value = result;
  } catch (error) {
    console.error('获取产品详情失败:', error);
    message.error(t('products.getProductDetailFailed'));
  } finally {
    loading.value = false;
  }
};

// 计算属性：按 rank 对产品详情区块进行排序
const sortedProductDetails = computed(() => {
  if (!product.value || !product.value.productDetails) {
    return [];
  }
  return [...product.value.productDetails].sort((a, b) => a.rank - b.rank);
});

// 方法：按 rank 对属性进行排序
const sortedAttributes = (attributes) => {
  if (!attributes) {
    return [];
  }
  return [...attributes].sort((a, b) => a.rank - b.rank);
};

// 返回上一页
const goBack = () => {
  router.push({ path: '/products', query: { tab: 'all' } });
};

// 预览文件
const previewFile = (file) => {
  if (!file.filePath) {
    message.error(t('products.invalidFilePath'));
    return;
  }
  fileUrl.value = file.filePath;
  filePreviewVisible.value = true;
};

// 关闭文件预览
const closeFilePreview = () => {
  filePreviewVisible.value = false;
  fileUrl.value = null;
};

// 获取当前语言对应的翻译文本
const getLocalizedName = (nameI18n) => {
  if (!nameI18n) return '';

  // 获取当前语言
  const currentLocale = locale.value;

  // 将语言代码转换为nameI18n对象中的键
  const localeKeyMap = {
    'zh-CN': 'zhHansCN',
    'en-US': 'enUS',
    'zh-HK': 'zhHantHK',
    'ja-JP': 'jaJP',
    'ko-KR': 'koKR'
  };

  const key = localeKeyMap[currentLocale];

  // 如果有当前语言的翻译且不为null，则使用该翻译
  if (key && nameI18n[key]) {
    return nameI18n[key];
  }

  // 否则，按优先级尝试其他语言
  if (currentLocale.startsWith('zh')) {
    // 中文优先级：当前中文变体 > 其他中文变体 > 英文 > 原始名称
    return nameI18n.zhHansCN || nameI18n.zhHantHK || nameI18n.enUS || nameI18n.name || '';
  } else {
    // 其他语言优先级：当前语言 > 英文 > 中文简体 > 中文繁体 > 原始名称
    return nameI18n.enUS || nameI18n.zhHansCN || nameI18n.zhHantHK || nameI18n.name || '';
  }
};

// 在组件挂载时获取产品详情
onMounted(() => {
  fetchProductDetail();
});
</script>

<style scoped>
:deep(.ant-tabs-nav) {
  padding: 0;
  margin-bottom: 16px;
}

:deep(.ant-tabs-tab) {
  padding: 12px 24px !important;
  margin: 0 8px !important;
}

:deep(.ant-tabs-tab:first-child) {
  margin-left: 0 !important;
}

:deep(.ant-tabs-ink-bar) {
  height: 2px !important;
}

/* 隐藏选项卡下拉菜单 */
:deep(.ant-tabs-nav-operations) {
  display: none !important;
}

/* 返回按钮图标和文字对齐 */
.back-button {
  display: inline-flex;
  align-items: center;
}

.back-button :deep(.ant-btn-icon) {
  display: flex;
  align-items: center;
}

/* 预览按钮图标和文字对齐 */
.preview-button {
  display: inline-flex;
  align-items: center;
}

.preview-button :deep(.ant-btn-icon) {
  display: flex;
  align-items: center;
}

/* PDF预览样式 */
.pdf-container {
  height: 70vh;
  overflow: auto;
}

.pdf-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}
</style>