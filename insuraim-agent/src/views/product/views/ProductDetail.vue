<template>
  <div class="mx-auto">
    <!-- 产品详情内容 -->
    <div class="bg-white p-6 shadow-sm rounded-lg">
      <!-- 显示加载状态 -->
      <div v-if="loading" class="flex justify-center items-center h-64">
        <a-spin size="large" />
      </div>

      <template v-else-if="product">
        <!-- 产品标题和标签 -->
        <div class="flex items-center mb-4 justify-between">
          <div class="flex items-center">
            <img :src="product.logoUrl || 'https://suboga.oss-ap-southeast-1.aliyuncs.com/Insuriam/icon/chinalife.png'"
              :alt="t('products.chinaLife')" class="h-8 w-auto mr-3 object-contain" />
            <h2 class="text-xl font-medium mr-3">{{ product.productName }}（{{ product.companyName }}）</h2>
            <a-tag color="blue" class="rounded-sm">{{ product.categoryName || t('products.category') }}</a-tag>
            <!-- 收藏按钮 -->
            <a-tooltip :title="isFavorited ? t('products.unfavorite') : t('products.favorite')">
              <a-button type="text" @click="toggleFavorite" class="favorite-button ml-1">
                <Icon :icon="isFavorited ? 'mdi:star' : 'mdi:star-outline'" class="text-yellow-400 text-2xl" />
              </a-button>
            </a-tooltip>
          </div>
          <div class="grid grid-cols-2 gap-2">
            <!-- 第一行：保费试算和产品对比-->
            <a-button class="premium-calc-button" @click="goToPremiumCalculation">
              <template #icon>
                <Icon icon="mdi:calculator" />
              </template>
              <span>{{ t('products.premiumCalculation') }}</span>
            </a-button>
            <a-button class="product-compare-button" @click="goToProductComparison">
              <template #icon>
                <Icon icon="mdi:compare" />
              </template>
              <span>{{ t('products.productComparison') }}</span>
            </a-button>
            <!-- 第二行：返回列表和生成计划书 -->
            <a-button type="primary" ghost @click="goBack" class="back-button">
              <template #icon>
                <Icon icon="mdi:arrow-left" />
              </template>
              <span>{{ t('products.backToList') }}</span>
            </a-button>
            <a-button type="primary" @click="generateProposal" class="proposal-button">
              <template #icon>
                <Icon icon="mdi:file-document-outline" />
              </template>
              <span>{{ t('products.generateProposal') }}</span>
            </a-button>
          </div>
        </div>

        <!-- 产品描述 -->
        <p v-if="product.description" class="text-gray-600 mb-6">{{ product.description }}</p>

        <!-- 选项卡 -->
        <a-tabs v-model:activeKey="activeTab">
          <!-- 产品介绍 -->
          <a-tab-pane key="info" :tab="t('products.productIntroductionTab')">
            <ProductIntroductionTab :product-id="productId" />
          </a-tab-pane>
          <!-- 产品详情 -->
          <a-tab-pane key="detail" :tab="t('products.productDetailTab')">
            <ProductDetailTab :highlights="productHighlights" :product-id="productId" />
          </a-tab-pane>
          <!-- 产品评测 -->
          <a-tab-pane key="review" :tab="t('products.productReviewTab')">
            <ProductReviewTab />
          </a-tab-pane>
          <!-- 产品文档 -->
          <a-tab-pane key="files" :tab="t('products.productDocumentsTab')">
            <ProductFilesTab :product-id="productId" @preview-file="previewFile" />
          </a-tab-pane>
        </a-tabs>
      </template>

      <!-- 无数据展示 -->
      <a-empty v-else :description="t('products.noProductInfo')" />
    </div>

    <!-- PDF预览弹窗 -->
    <a-modal v-model:visible="filePreviewVisible" :title="t('products.filePreview')" :footer="null" width="80%"
      @cancel="closeFilePreview" :height="800">
      <template #title>
        <div class="flex justify-between items-center">
          <span>{{ t('products.filePreview') }}</span>
        </div>
      </template>
      <div class="pdf-container">
        <PDF v-if="fileUrl" :src="fileUrl" :pdfWidth="800" :height="750" />
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import { productAPI } from '@/api';
import { Icon } from '@iconify/vue';
import PDF from 'pdf-vue3';
import { useI18n } from 'vue-i18n';

// 导入组件
import ProductIntroductionTab from '../components/ProductIntroductionTab.vue';
import ProductReviewTab from '../components/ProductReviewTab.vue';
import ProductFilesTab from '../components/ProductFilesTab.vue';
import ProductDetailTab from '../components/ProductDetailTab.vue';

// 初始化国际化
const { t } = useI18n();

const router = useRouter();
const route = useRoute();
const productId = route.params.id;

// 状态
const product = ref(null);
const loading = ref(true);
const activeTab = ref('info');
const filePreviewVisible = ref(false);
const fileUrl = ref(null);

// 收藏状态
const isFavorited = ref(false);

const productCode = ref([]);
const productName = ref('');

// /**
//  * @description 获取产品代码列表
//  */
// const getProductCodeList = async () => {
//   const result = await productAPI.getChinaLifeProductCodeList(productId);
//   productCode.value = result[0].code;
//   productName.value = result[0].name;
// }

/**
 * @description 获取收藏状态
 */
const fetchFavoriteStatus = async () => {
  try {
    if (!productId) return;
    const result = await productAPI.checkProductFavorited(productId);
    isFavorited.value = !!result;
  } catch (error) {
    console.error('检查收藏状态失败:', error);
  }
};

/**
 * @description 切换收藏
 */
const toggleFavorite = async () => {
  const token = localStorage.getItem('token');
  if (!token) {
    message.warning(t('products.pleaseLogin') || '请先登录');
    router.push('/login');
    return;
  }

  try {
    if (isFavorited.value) {
      await productAPI.unfavoriteProduct(productId);
      isFavorited.value = false;
      message.success(t('products.unfavoriteSuccess') || '取消收藏成功');
    } else {
      await productAPI.favoriteProduct(productId);
      isFavorited.value = true;
      message.success(t('products.favoriteSuccess') || '收藏成功');
    }
  } catch (error) {
    console.error('切换收藏状态失败:', error);
    message.error(t('products.actionFailed') || '操作失败');
  }
};

// 跳转到保费试算页面
const goToPremiumCalculation = () => {
  router.push({ path: '/tools/calculator/premium', query: { productName: productName.value, productId: productId } });
  // 测试数据
  // router.push({ path: '/tools/calculator/premium', query: { productName: '衛您摯愛保險計劃',productId:25 } });
}

// 跳转到产品对比页面
const goToProductComparison = () => {

  router.push({ path: '/product-compare', query: { productName: productName.value, productId: productId } });
  // 测试数据
  // router.push({ path: '/product-compare', query: { productName: '5年GoalAhead储蓄计划',productId:32 } });
}

// 获取中国人寿产品详情
const fetchChinaLifeProductDetail = async () => {
  if (!productId) {
    message.error(t('products.productCodeNotExist'));
    router.push('/products');
    return;
  }

  try {
    loading.value = true;
    const chinaLifeProducts = await productAPI.getProductDetail(productId);
    if (!chinaLifeProducts) {
      message.error(t('products.noProductFound'));
      return;
    }

    // 处理中国人寿产品数据格式
    product.value = {
      productId: productId,
      productName: chinaLifeProducts.productName || t('products.chinaLife'),
      categoryName: chinaLifeProducts.categoryName || t('products.category'),
      companyName: chinaLifeProducts.companyName || t('products.chinaLife'),
      description: chinaLifeProducts.description || '',
      logoUrl: chinaLifeProducts.logoUrl,
      originalData: chinaLifeProducts,
      productDetails: formatChinaLifeProductDetails(chinaLifeProducts),
      productFiles: formatChinaLifeFiles(chinaLifeProducts),
    };
  } catch (error) {
    console.error('获取中国人寿产品详情失败:', error);
    message.error(t('products.getProductDetailFailed'));
  } finally {
    loading.value = false;
  }
};

// 计算属性：产品亮点
const productHighlights = computed(() => {
  if (!product.value || !product.value.originalData) {
    return [];
  }
  return extractChinaLifeHighlights(product.value.originalData);
});


// 生成计划书
const generateProposal = () => {
  if (!product.value || !productId) {
    message.error(t('products.incompleteProductInfo'));
    return;
  }

  try {
    router.push({
      path: `/plan-creator`,
      query: {
        productName: productName.value,
      }
    });
  } catch (error) {
    console.error('生成计划书失败:', error);
    message.error(t('products.generateProposalFailed'));
  }
};

// 预览文件
const previewFile = (file) => {
  if (!file.filePath) {
    message.error(t('products.invalidFilePath'));
    return;
  }
  fileUrl.value = file.filePath;
  filePreviewVisible.value = true;
};

// 关闭文件预览
const closeFilePreview = () => {
  filePreviewVisible.value = false;
  fileUrl.value = null;
};

// 格式化中国人寿产品详情
const formatChinaLifeProductDetails = (chinaLifeData) => {
  const productDetails = chinaLifeData.product_details || {};
  const features = chinaLifeData.features?.product_features || {};
  const levels = chinaLifeData.levels || null;

  // 基础信息区块
  const basicSection = {
    key: 'basic',
    name: '基本信息',
    rank: 1,
    attributes: [
      { name: '产品名称', value: productDetails.productName || '无资料', rank: 1 },
      { name: '产品类型', value: productDetails.productType || '无资料', rank: 2 },
      { name: '产品子类型', value: productDetails.productSubType || '无资料', rank: 3 },
      { name: '保单期限', value: productDetails.policyTerm || '无资料', rank: 4 },
      { name: '货币', value: productDetails.currency || '无资料', rank: 5 }
    ]
  };

  // 年龄限制区块
  const ageSection = {
    key: 'age',
    name: '年龄限制',
    rank: 2,
    attributes: [
      {
        name: '最小年龄限制',
        value: productDetails.ageRestriction?.minimumAge?.restriction || '无资料',
        rank: 1
      },
      {
        name: '最小年龄数值',
        value: productDetails.ageRestriction?.minimumAge?.amount || '无资料',
        rank: 2
      },
      {
        name: '最大年龄限制',
        value: productDetails.ageRestriction?.maximumAge?.restriction || '无资料',
        rank: 3
      },
      {
        name: '最大年龄数值',
        value: productDetails.ageRestriction?.maximumAge?.amount || '无资料',
        rank: 4
      }
    ]
  };

  // 保费限制区块
  const feeSection = {
    key: 'fee',
    name: '保额限制',
    rank: 3,
    attributes: []
  };

  // 处理最小保额
  if (productDetails.fee?.minimumSumInsured?.length > 0) {
    const minSumInsured = productDetails.fee.minimumSumInsured[0];
    feeSection.attributes.push({
      name: '最小保额限制',
      value: minSumInsured.restriction || '无资料',
      rank: 1
    });

    if (minSumInsured.amount) {
      let minValues = [];
      for (const [currency, amount] of Object.entries(minSumInsured.amount)) {
        minValues.push(`${currency}: ${amount}`);
      }
      feeSection.attributes.push({
        name: '最小保额',
        value: minValues.join(', '),
        rank: 2
      });
    } else {
      feeSection.attributes.push({
        name: '最小保额',
        value: '无资料',
        rank: 2
      });
    }
  } else {
    feeSection.attributes.push({
      name: '最小保额',
      value: '无资料',
      rank: 1
    });
  }

  // 处理最大保额
  if (productDetails.fee?.maximumSumInsured?.length > 0) {
    const maxSumInsured = productDetails.fee.maximumSumInsured[0];
    feeSection.attributes.push({
      name: '最大保额限制',
      value: maxSumInsured.restriction || '无资料',
      rank: 3
    });

    if (maxSumInsured.amount) {
      let maxValues = [];
      for (const [currency, amount] of Object.entries(maxSumInsured.amount)) {
        maxValues.push(`${currency}: ${amount}`);
      }
      feeSection.attributes.push({
        name: '最大保额',
        value: maxValues.join(', '),
        rank: 4
      });
    } else {
      feeSection.attributes.push({
        name: '最大保额',
        value: '无资料',
        rank: 4
      });
    }
  } else {
    feeSection.attributes.push({
      name: '最大保额',
      value: '无资料',
      rank: 3
    });
  }

  // 特色功能区块
  const featuresSection = {
    key: 'features',
    name: '产品特色',
    rank: 4,
    attributes: []
  };

  // 添加所有特色功能，包括"无资料"的项目
  let featureRank = 1;
  for (const [key, value] of Object.entries(features)) {
    featuresSection.attributes.push({
      name: formatFeatureName(key),
      value: value || '无资料',
      rank: featureRank++
    });
  }

  // 新增保险相关性区块
  const insuranceRelevanceSection = {
    key: 'insuranceRelevance',
    name: '保险相关性',
    rank: 5,
    attributes: []
  };

  // 处理保险相关性数据
  if (productDetails.InsuranceRelevance) {
    let relevanceRank = 1;
    for (const [key, value] of Object.entries(productDetails.InsuranceRelevance)) {
      insuranceRelevanceSection.attributes.push({
        name: formatInsuranceRelevanceName(key),
        value: value || '无资料',
        rank: relevanceRank++
      });
    }
  }

  // 创建一个结果数组，包含基本区块
  const resultSections = [basicSection, ageSection, feeSection, featuresSection, insuranceRelevanceSection];

  // 处理 levels 数据，如果存在
  if (levels) {
    // 遍历每个级别
    for (const [levelKey, levelData] of Object.entries(levels)) {
      const levelName = getLevelDisplayName(levelKey);
      const levelRank = getLevelRank(levelKey);

      // 创建级别区块
      const levelSection = {
        key: levelKey,
        name: levelName,
        rank: 6 + levelRank, // 确保级别显示在基本信息和保险相关性之后
        attributes: []
      };

      // 处理该级别的 unique 字段
      const uniqueKey = `${levelKey}Unique`;
      if (levelData[uniqueKey] && Array.isArray(levelData[uniqueKey])) {
        levelData[uniqueKey].forEach((item, index) => {
          for (const [key, value] of Object.entries(item)) {
            levelSection.attributes.push({
              name: formatLevelFeatureName(key),
              value: value || '无资料',
              rank: levelSection.attributes.length + 1
            });
          }
        });
      }

      // 处理该级别的 deathBenefits
      if (levelData.deathBenefits) {
        for (const [key, value] of Object.entries(levelData.deathBenefits)) {
          levelSection.attributes.push({
            name: formatLevelFeatureName(key),
            value: value || '无资料',
            rank: levelSection.attributes.length + 1
          });
        }
      }

      // 处理该级别的其他属性...
      // [省略部分重复处理逻辑，实际项目中应保留完整处理逻辑]

      // 只有当有属性时才添加该级别
      if (levelSection.attributes.length > 0) {
        resultSections.push(levelSection);
      }
    }
  }

  return resultSections;
};

// 提取中国人寿产品亮点
const extractChinaLifeHighlights = (chinaLifeData) => {
  const features = chinaLifeData.features?.product_features || {};
  const highlights = [];

  // 添加主要产品特点作为亮点
  if (features.productFeatures && features.productFeatures !== '无资料') {
    highlights.push(features.productFeatures);
  }

  // 添加其他非空值的重要特性作为亮点
  const importantFeatures = [
    'terminalDividend', 'terminalDividendNonGuarantee', 'parentalWaiverBenefit',
    'spousalWaiverBenefit', 'extendedCoverageForChildren',
    'accidentalDeathBenefit', 'policyReverseMortgageLoan'
  ];

  for (const feature of importantFeatures) {
    if (features[feature] && features[feature] !== '无资料') {
      highlights.push(`${formatFeatureName(feature)}: ${features[feature].split('\r\n')[0]}`);
    }
  }

  return highlights.length > 0 ? highlights : ['暂无产品亮点'];
};

// 格式化中国人寿产品文件
const formatChinaLifeFiles = (chinaLifeData) => {
  const files = [];

  // 如果有PDF文件URL，添加到文件列表
  if (chinaLifeData.pdfUrl) {
    files.push({
      fileName: `${chinaLifeData.product_details?.productName || '产品'} - 产品说明书.pdf`,
      filePath: chinaLifeData.pdfUrl,
      author: '中国人寿',
      uploadTime: new Date().toISOString(),
      fileType: 'pdf'
    });
  }

  return files;
};

// 获取级别显示名称
const getLevelDisplayName = (levelKey) => {
  const nameMap = {
    'level1': '计划一级别',
    'level2': '计划二级别',
    'level3': '计划三级别',
    'level4': '计划四级别',
    'level5': '计划五级别',
  };

  return nameMap[levelKey] || levelKey;
};

// 获取级别排序序号
const getLevelRank = (levelKey) => {
  const rankMap = {
    'level1': 1,
    'level2': 2,
    'level3': 3,
    'level4': 4,
    'level5': 5,
  };

  return rankMap[levelKey] || 99;
};

// 格式化级别特性名称
const formatLevelFeatureName = (key) => {
  const nameMap = {
    // unique 字段
    'OutsideTheCoverageArea': '保障区域外',
    'AnnualMaximumIndemnityLimit': '年度最高赔偿限额',
    'PrivateNursingFee': '私人看护费',
    'MentalDisorderTreatment': '精神障碍治疗',
    'OutpatientSurgeryAllowance': '门诊手术津贴',
    'PostHospitalizationNursingFee': '出院后看护费',
    'HomeEquipmentUpgrade': '家居设备升级',
    'StrokeSupportiveCare_ProfessionalServices': '中风支持护理-专业服务',
    'StrokeSupportiveCare_SpecialistServices': '中风支持护理-专科服务',
    'HIVTreatment': 'HIV治疗',
    'TCM_Treatment': '中医治疗',
    'PalliativeCare': '姑息治疗',
    'PregnancyComplications': '妊娠并发症',

    // 身故福利
    'compassionateDeathBenefit': '恩恤身故赔偿',
    'additionalAccidentalDeathBenefit': '额外意外身故赔偿',

    // 保单细节
    'coveragePeriod': '保障期',
    'premiumStructure': '保费结构',
    'renewalGuarantee': '续保保证',
    'premiumPaymentPeriod': '保费缴付期',
    'premiumPaymentMethods': '保费缴付方式',
    'lifetimeCompensationLimit': '终身赔偿限额',

    // 预防护理
    'healthCheckup': '健康检查',
  };

  return nameMap[key] || key;
};

// 格式化特性名称
const formatFeatureName = (key) => {
  const nameMap = {
    productFeatures: '产品特点',
    targetAudience: '目标客户',
    premiumRate: '保费率',
    nonGuaranteedDividend: '非保证红利',
    terminalDividend: '终期红利',
    terminalDividendNonGuarantee: '终期红利(非保证)',
    terminalDividendManagementBenefit: '终期红利管理权益',
    guaranteedMonthlyAnnuityIncome: '保证每月年金收入',
    nonGuaranteedMonthlyAnnuityIncome: '非保证每月年金收入',
    highCoverageDiscount: '高保额折扣',
    specialDiseaseCoverage: '特殊疾病保障',
    premiumHoliday: '保费假期',
    policySplitRights: '保单分拆权',
    currencyConversionRights: '货币转换权',
    additionalCriticalIllnessCoverageForChildren: '子女额外危疾保障',
    earlyStageIllnessCoverage: '早期疾病保障',
    criticalIllnessCoverage: '危疾保障',
    waiverOfPremiumOnCriticalIllnessBenefit: '危疾豁免保费',
    additionalCriticalIllnessCoverage: '额外危疾保障',
    multipleCriticalIllnessCoverage: '多重危疾保障',
    familySharedCoverage: '家庭共享保障',
    allRoundProtectionPlan: '全方位保障计划',
    doubleIndemnityBenefit: '双倍赔偿',
    compassionateDeathBenefit: '恩恤身故保障',
    additionalAllRoundProtectionPlan: '额外全方位保障计划',
    medicalExpensesCoverage: '医疗费用保障',
    rehabilitationSupportServices: '康复支持服务',
    additionalHospitalCashBenefit: '额外住院现金保障',
    hospitalSurgeryReimbursement: '住院手术报销',
    noLifetimeLimitAndUnknownPreexistingCovered: '无终身限额且承保未知既往症',
    additionalDeathBenefit: '额外身故保障',
    accidentalBurnBenefit: '意外烧伤保障',
    flexibleDeathAndAccidentalPayoutMethod: '灵活身故及意外赔付方式',
    accidentalDeathBenefit: '意外身故保障',
    cancerCoverage: '癌症保障',
    accidentalTotalAndPermanentDisability: '意外完全及永久残疾',
    accidentalDeathAndDismembermentBenefit: '意外身故及断肢保障',
    accidentalTpdWaiverOfPremium: '意外TPD豁免保费',
    suddenDeathCoverage: '猝死保障',
    changeOfInsuredPerson: '更换受保人',
    substituteInsuredPerson: '替代受保人',
    parentalWaiverBenefit: '父母身故豁免保费',
    spousalWaiverBenefit: '配偶身故豁免保费',
    extendedCoverageForChildren: '子女延伸保障',
    policyTermination: '保单终止条件',
    otherBenefits: '其他保障',
    specialRemarks: '特别备注',
    policyReverseMortgageLoan: '保单逆按贷款',
    rateTable: '费率表'
  };

  return nameMap[key] || key;
};

// 格式化保险相关性名称
const formatInsuranceRelevanceName = (key) => {
  const nameMap = {
    // 核保相关
    medicalUnderwriting: '医疗核保',
    financialUnderwriting: '财务核保',
    occupationalUnderwriting: '职业核保',
    geographicalUnderwriting: '地域核保',
    noMedicalExamLimit: '免体检限额',
    additionalLoadingApplicable: '适用额外加费',
    specialDocumentationRequired: '需要特殊文件',

    // 保障相关
    criticalIllnessCoverage1: '危疾保障',
    hospitalizationBenefitNonPremiumPlan: '住院保障(非高端计划)',
    hospitalizationBenefitPremiumPlan: '住院保障(高端计划)',
    accidentalBenefit: '意外保障',
    premiumWaiverBenefit: '保费豁免',
    longTermSickLeaveBenefit: '长期病假保障',
    outpatientBenefit: '门诊保障',
    femaleSpecificBenefit: '女性特定保障',
    babyRewardBenefit: '婴儿奖励',
    disabilityBenefit: '伤残保障',

    // 保单管理相关
    increaseSumInsured: '增加保额',
    decreaseSumInsured: '减少保额',
    addBaseCoverage: '增加基本保障',
    reduceBaseCoverage: '减少基本保障',
    addRider: '增加附加险',
    removeRider: '移除附加险',
    policyLoan: '保单贷款',
    renewalPaymentMethod: '续保付款方式',
    prepaidPremium: '预缴保费',
    policyReinstatement: '保单复效',
    automaticPremiumLoan: '自动保费贷款',
    changePlan: '变更计划',
    reducedPaidUpOption: '减额缴清选项',
    changeInSmokingStatus: '吸烟状态变更'
  };

  return nameMap[key] || key;
};

// 在组件挂载时获取产品详情
onMounted(() => {
  fetchChinaLifeProductDetail();
  // getProductCodeList();
  fetchFavoriteStatus();
});
</script>

<style scoped>
:deep(.ant-tabs-nav) {
  padding: 0;
  margin-bottom: 16px;
}

:deep(.ant-tabs-tab) {
  padding: 12px 24px !important;
  margin: 0 8px !important;
}

:deep(.ant-tabs-tab:first-child) {
  margin-left: 0 !important;
}

:deep(.ant-tabs-ink-bar) {
  height: 2px !important;
}

/* 隐藏选项卡下拉菜单 */
:deep(.ant-tabs-nav-operations) {
  display: none !important;
}

/* 返回按钮图标和文字对齐 */
.back-button {
  display: inline-flex;
  align-items: center;
}

.back-button :deep(.ant-btn-icon) {
  display: flex;
  align-items: center;
}

/* 生成计划书按钮图标和文字对齐 */
.proposal-button {
  display: inline-flex;
  align-items: center;
}

.proposal-button :deep(.ant-btn-icon) {
  display: flex;
  align-items: center;
}

/* 保费试算按钮图标和文字对齐 */
.premium-calc-button {
  display: inline-flex;
  align-items: center;
}

.premium-calc-button :deep(.ant-btn-icon) {
  display: flex;
  align-items: center;
}

/* 产品对比按钮图标和文字对齐 */
.product-compare-button {
  display: inline-flex;
  align-items: center;
}

.product-compare-button :deep(.ant-btn-icon) {
  display: flex;
  align-items: center;
}

/* 预览按钮图标和文字对齐 */
.preview-button {
  display: inline-flex;
  align-items: center;
}

.preview-button :deep(.ant-btn-icon) {
  display: flex;
  align-items: center;
}

/* PDF预览样式 */
.pdf-container {
  width: 100%;
  background-color: #f5f5f5;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  height: 700px;
}
</style>