<template>
    <div class="container mx-auto px-6 py-6">
      <!-- 页面标题 -->
      <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-800">个人中心</h1>
        <p class="text-gray-600">{{ currentDate }} · {{ weekday }}</p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- 左侧：用户基本信息卡片 -->
        <div class="bg-white rounded-xl shadow-sm p-6">
          <div class="flex flex-col items-center">
            <div class="relative">
              <div class="w-24 h-24 rounded-full overflow-hidden border-4 border-white shadow">
                <img v-if="userInfo.avatar" :src="userInfo.avatar" alt="用户头像" class="w-full h-full object-cover" />
                <div v-else class="w-full h-full bg-blue-100 flex items-center justify-center text-blue-500">
                  <Icon icon="mdi:account" class="w-12 h-12" />
                </div>
              </div>
              <div class="absolute bottom-0 right-0 bg-blue-500 rounded-full p-2 text-white cursor-pointer">
                <Icon icon="mdi:camera" class="w-4 h-4" />
              </div>
            </div>
            
            <h2 class="mt-4 text-xl font-semibold">{{ userInfo.name || userInfo.username }}</h2>
            <p class="text-gray-500">{{ userInfo.role || '保险代理人' }}</p>
            
            <div class="border-t border-gray-100 w-full my-4"></div>
            
            <div class="w-full space-y-3">
              <div class="flex items-center">
                <Icon icon="mdi:email" class="text-gray-400 mr-3 w-5 h-5" />
                <span class="text-gray-600">{{ userInfo.email || '未设置邮箱' }}</span>
              </div>
              <div class="flex items-center">
                <Icon icon="mdi:phone" class="text-gray-400 mr-3 w-5 h-5" />
                <span class="text-gray-600">{{ userInfo.phone || '未设置手机号' }}</span>
              </div>
              <div class="flex items-center">
                <Icon icon="mdi:office-building" class="text-gray-400 mr-3 w-5 h-5" />
                <span class="text-gray-600">{{ userInfo.orgName || '未设置所属机构' }}</span>
              </div>
              <div class="flex items-center">
                <Icon icon="mdi:calendar" class="text-gray-400 mr-3 w-5 h-5" />
                <span class="text-gray-600">加入时间: {{ userInfo.createTime || '未知' }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：个人资料设置等 -->
        <div class="md:col-span-2">
          <!-- 个人数据统计卡片 -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div v-for="(card, index) in statCards" :key="index" 
                 class="bg-white rounded-xl shadow-sm p-4 border-l-4" 
                 :class="card.borderColor">
              <div class="flex justify-between">
                <div>
                  <p class="text-gray-500 text-sm">{{ card.title }}</p>
                  <p class="text-2xl font-bold mt-1">{{ card.value }}</p>
                </div>
                <div :class="`rounded-full w-10 h-10 flex items-center justify-center ${card.iconBg}`">
                  <Icon :icon="card.icon" class="w-5 h-5 text-white" />
                </div>
              </div>
            </div>
          </div>

          <!-- 选项卡 -->
          <div class="bg-white rounded-xl shadow-sm">
            <!-- 选项卡头部 -->
            <div class="profile-tabs border-b border-gray-200">
              <div class="flex">
                <div 
                  v-for="tab in tabs" 
                  :key="tab.key" 
                  class="px-6 py-4 cursor-pointer text-base"
                  :class="activeTab === tab.key ? 'tab-active' : 'text-gray-500'"
                  @click="activeTab = tab.key"
                >
                  {{ tab.label }}
                </div>
              </div>
            </div>
            
            <!-- 选项卡内容 -->
            <div class="p-6">
              <!-- 基本资料 -->
              <div v-if="activeTab === 'basic'">
                <div class="grid grid-cols-2 gap-6">
                  <div class="col-span-1">
                    <div class="mb-4">
                      <label class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                      <input 
                        v-model="formState.username" 
                        type="text" 
                        placeholder="请输入用户名" 
                        class="profile-input w-full"
                      />
                    </div>
                  </div>
                  <div class="col-span-1">
                    <div class="mb-4">
                      <label class="block text-sm font-medium text-gray-700 mb-1">姓名</label>
                      <input 
                        v-model="formState.name" 
                        type="text" 
                        placeholder="请输入姓名" 
                        class="profile-input w-full"
                      />
                    </div>
                  </div>
                  <div class="col-span-1">
                    <div class="mb-4">
                      <label class="block text-sm font-medium text-gray-700 mb-1">邮箱</label>
                      <input 
                        v-model="formState.email" 
                        type="email" 
                        placeholder="请输入邮箱" 
                        class="profile-input w-full"
                      />
                    </div>
                  </div>
                  <div class="col-span-1">
                    <div class="mb-4">
                      <label class="block text-sm font-medium text-gray-700 mb-1">手机号</label>
                      <input 
                        v-model="formState.phone" 
                        type="text" 
                        placeholder="请输入手机号" 
                        class="profile-input w-full"
                      />
                    </div>
                  </div>
                  <div class="col-span-2">
                    <div class="mb-6">
                      <label class="block text-sm font-medium text-gray-700 mb-1">个人简介</label>
                      <textarea 
                        v-model="formState.bio" 
                        placeholder="请输入个人简介" 
                        rows="4" 
                        class="profile-input w-full"
                      ></textarea>
                    </div>
                  </div>
                </div>
                <div class="flex">
                  <button 
                    @click="handleUpdateProfile" 
                    class="bg-blue-500 hover:bg-blue-600 text-white px-5 py-2 rounded"
                  >
                    保存修改
                  </button>
                </div>
              </div>

              <!-- 密码修改 -->
              <div v-if="activeTab === 'password'">
                <div class="grid grid-cols-2 gap-6">
                  <div class="col-span-2">
                    <div class="mb-4">
                      <label class="block text-sm font-medium text-gray-700 mb-1">当前密码</label>
                      <input 
                        v-model="passwordForm.currentPassword" 
                        type="password" 
                        placeholder="请输入当前密码" 
                        class="profile-input w-full"
                      />
                    </div>
                  </div>
                  <div class="col-span-1">
                    <div class="mb-4">
                      <label class="block text-sm font-medium text-gray-700 mb-1">新密码</label>
                      <input 
                        v-model="passwordForm.newPassword" 
                        type="password" 
                        placeholder="请输入新密码" 
                        class="profile-input w-full"
                      />
                    </div>
                  </div>
                  <div class="col-span-1">
                    <div class="mb-4">
                      <label class="block text-sm font-medium text-gray-700 mb-1">确认新密码</label>
                      <input 
                        v-model="passwordForm.confirmPassword" 
                        type="password" 
                        placeholder="请再次输入新密码" 
                        class="profile-input w-full"
                      />
                    </div>
                  </div>
                </div>
                <div class="flex mt-2">
                  <button 
                    @click="handleChangePassword" 
                    class="bg-blue-500 hover:bg-blue-600 text-white px-5 py-2 rounded"
                  >
                    修改密码
                  </button>
                </div>
              </div>

              <!-- 通知设置 -->
              <div v-if="activeTab === 'notifications'">
                <div class="space-y-6">
                  <div class="flex justify-between items-center py-2">
                    <div>
                      <p class="font-medium">系统通知</p>
                      <p class="text-gray-500 text-sm">接收系统更新、维护等相关通知</p>
                    </div>
                    <a-switch v-model:checked="notificationSettings.system" />
                  </div>

                  <div class="flex justify-between items-center py-2 border-t border-gray-100">
                    <div>
                      <p class="font-medium">业务通知</p>
                      <p class="text-gray-500 text-sm">接收保单过期、续保等业务通知</p>
                    </div>
                    <a-switch v-model:checked="notificationSettings.business" />
                  </div>

                  <div class="flex justify-between items-center py-2 border-t border-gray-100">
                    <div>
                      <p class="font-medium">产品更新</p>
                      <p class="text-gray-500 text-sm">接收新产品上线、产品变更等通知</p>
                    </div>
                    <a-switch v-model:checked="notificationSettings.product" />
                  </div>

                  <div class="flex justify-between items-center py-2 border-t border-gray-100">
                    <div>
                      <p class="font-medium">市场活动</p>
                      <p class="text-gray-500 text-sm">接收促销、活动等市场营销通知</p>
                    </div>
                    <a-switch v-model:checked="notificationSettings.marketing" />
                  </div>

                  <div class="flex mt-4">
                    <button 
                      @click="handleSaveNotificationSettings" 
                      class="bg-blue-500 hover:bg-blue-600 text-white px-5 py-2 rounded"
                    >
                      保存设置
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import MainLayout from '@/layouts/MainLayout.vue';
import { userAPI } from '@/api';

// 路由实例
const router = useRouter();

// 选项卡状态
const activeTab = ref('basic');

// 选项卡定义
const tabs = [
  { key: 'basic', label: '基本资料' },
  { key: 'password', label: '密码修改' },
  { key: 'notifications', label: '通知设置' }
];

// 用户信息
const userInfo = ref({
  id: '',
  username: '',
  name: '',
  email: '',
  phone: '',
  avatar: '',
  role: '保险代理人',
  orgName: '',
  createTime: ''
});

// 表单状态
const formState = reactive({
  username: '',
  name: '',
  email: '',
  phone: '',
  bio: ''
});

// 密码修改表单
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
});

// 通知设置
const notificationSettings = reactive({
  system: true,
  business: true,
  product: true,
  marketing: false
});

// 当前日期
const now = new Date();
const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
const currentDate = computed(() => {
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
});
const weekday = computed(() => weekdays[now.getDay()]);

// 统计数据卡片
const statCards = ref([
  {
    title: '保单数量',
    value: '34',
    icon: 'mdi:file-document',
    iconBg: 'bg-blue-500',
    borderColor: 'border-blue-500'
  },
  {
    title: '客户数量',
    value: '108',
    icon: 'mdi:account-group',
    iconBg: 'bg-green-500',
    borderColor: 'border-green-500'
  },
  {
    title: '计划书数量',
    value: '61',
    icon: 'mdi:notebook',
    iconBg: 'bg-purple-500',
    borderColor: 'border-purple-500'
  }
]);

// 从localStorage获取用户信息
const getUserInfo = () => {
  try {
    const userInfoStr = localStorage.getItem('userInfo');
    if (userInfoStr) {
      const storedUserInfo = JSON.parse(userInfoStr);
      userInfo.value = {
        ...userInfo.value,
        ...storedUserInfo
      };
      
      // 同步到表单状态
      formState.username = storedUserInfo.username || '';
      formState.name = storedUserInfo.name || '';
      formState.email = storedUserInfo.email || '';
      formState.phone = storedUserInfo.phone || '';
      formState.bio = storedUserInfo.bio || '';
    }
  } catch (error) {
    console.error('获取用户信息失败', error);
    message.error('获取用户信息失败');
  }
};

// 获取最新用户信息
const fetchUserInfo = async () => {
  try {
    // 调用API获取最新用户信息
    const response = await userAPI.getUserInfo();
    if (response) {
      userInfo.value = {
        ...userInfo.value,
        ...response
      };
      
      // 同步到表单状态
      formState.username = response.username || '';
      formState.name = response.name || '';
      formState.email = response.email || '';
      formState.phone = response.phone || '';
      formState.bio = response.bio || '';
      
      // 更新localStorage
      localStorage.setItem('userInfo', JSON.stringify(response));
    }
  } catch (error) {
    console.error('获取用户信息失败', error);
    // 回退到本地存储
    getUserInfo();
  }
};

// 更新用户资料
const handleUpdateProfile = async () => {
  try {
    // 调用API更新用户信息
    const response = await userAPI.updateUserInfo(formState);
    if (response) {
      message.success('资料更新成功');
      // 刷新用户信息
      fetchUserInfo();
    }
  } catch (error) {
    console.error('更新用户资料失败', error);
    message.error('更新用户资料失败: ' + (error.message || '未知错误'));
  }
};

// 修改密码
const handleChangePassword = async () => {
  // 表单验证
  if (!passwordForm.currentPassword) {
    return message.error('请输入当前密码');
  }
  if (!passwordForm.newPassword) {
    return message.error('请输入新密码');
  }
  if (passwordForm.newPassword !== passwordForm.confirmPassword) {
    return message.error('两次输入的新密码不一致');
  }
  
  // 简单密码强度验证
  if (passwordForm.newPassword.length < 6) {
    return message.error('新密码长度不能少于6位');
  }
  
  try {
    // 调用修改密码API (假设API已存在)
    // const response = await userAPI.changePassword(passwordForm);
    message.success('密码修改成功，请重新登录');
    
    // 清除表单
    passwordForm.currentPassword = '';
    passwordForm.newPassword = '';
    passwordForm.confirmPassword = '';
    
    // 清除登录状态，返回登录页
    localStorage.removeItem('token');
    router.push('/login');
  } catch (error) {
    console.error('密码修改失败', error);
    message.error('密码修改失败: ' + (error.message || '未知错误'));
  }
};

// 保存通知设置
const handleSaveNotificationSettings = () => {
  // 假设保存到本地存储
  localStorage.setItem('notificationSettings', JSON.stringify(notificationSettings));
  message.success('通知设置保存成功');
};

// 获取通知设置
const getNotificationSettings = () => {
  try {
    const settingsStr = localStorage.getItem('notificationSettings');
    if (settingsStr) {
      const settings = JSON.parse(settingsStr);
      Object.assign(notificationSettings, settings);
    }
  } catch (error) {
    console.error('获取通知设置失败', error);
  }
};

// 组件挂载时获取用户信息
onMounted(() => {
  // 先从本地获取
  getUserInfo();
  // 再尝试从服务器获取最新数据
  fetchUserInfo();
  // 获取通知设置
  getNotificationSettings();
});
</script>

<style scoped>
/* 自定义样式 */
.profile-tabs .tab-active {
  color: #1890ff;
  border-bottom: 2px solid #1890ff;
  font-weight: 500;
}

.profile-input {
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  transition: all 0.3s;
  font-size: 14px;
}

.profile-input:hover {
  border-color: #40a9ff;
}

.profile-input:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  outline: none;
}
</style> 