<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-100 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 bg-white p-10 rounded-xl shadow-lg">
      <div class="text-center">
        <img class="mx-auto h-14 w-auto" src="" alt="BIS系统" />
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">用户注册</h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          欢迎注册BIS保险信息管理系统
        </p>
      </div>

      <a-form :model="formState" class="mt-8 space-y-6">
        <div class="rounded-md -space-y-px">
          <div class="mb-6">
            <a-form-item name="username" :rules="[{ required: true, message: '请输入用户名' }]">
              <a-input v-model:value="formState.username" size="large" placeholder="用户名">
                <template #prefix>
                  <Icon icon="material-symbols:person" class="text-gray-400" />
                </template>
              </a-input>
            </a-form-item>
          </div>

          <div class="mb-6">
            <a-form-item name="email" :rules="[{ type: 'email', message: '请输入有效的邮箱地址' }, { required: true, message: '请输入邮箱' }]">
              <a-input v-model:value="formState.email" size="large" placeholder="邮箱">
                <template #prefix>
                  <Icon icon="material-symbols:mail" class="text-gray-400" />
                </template>
              </a-input>
            </a-form-item>
          </div>

          <div class="mb-6">
            <a-form-item name="password" :rules="[{ required: true, message: '请输入密码' }]">
              <a-input-password v-model:value="formState.password" size="large" placeholder="密码">
                <template #prefix>
                  <Icon icon="material-symbols:lock" class="text-gray-400" />
                </template>
              </a-input-password>
            </a-form-item>
          </div>

          <div class="mb-6">
            <a-form-item name="confirmPassword" :rules="[
              { required: true, message: '请确认密码' },
              { validator: validatePass }
            ]">
              <a-input-password v-model:value="formState.confirmPassword" size="large" placeholder="确认密码">
                <template #prefix>
                  <Icon icon="material-symbols:lock" class="text-gray-400" />
                </template>
              </a-input-password>
            </a-form-item>
          </div>
        </div>

        <div class="flex items-center justify-between mb-2">
          <a-checkbox v-model:checked="formState.agree">我已阅读并同意用户协议</a-checkbox>
        </div>

        <div>
          <a-button type="primary" size="large" block :loading="loading" @click="handleSubmit" class="h-12">
            注册
          </a-button>
        </div>
      </a-form>

      <div class="mt-4 text-center">
        <p class="text-sm text-gray-600">
          已有账号？ <a href="/login" class="text-blue-500 hover:text-blue-700">立即登录</a>
        </p>
      </div>

      <div class="mt-8 pt-6 border-t border-gray-200">
        <div class="text-center text-sm text-gray-600">
          Copyright © {{ new Date().getFullYear() }} 保险信息系统
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { userAPI } from '@/api';

const router = useRouter();
const loading = ref(false);

// 表单数据
const formState = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  agree: false,
});

// 密码验证
const validatePass = async (_rule, value) => {
  if (value === '') {
    return Promise.reject('请确认密码');
  } else if (value !== formState.password) {
    return Promise.reject('两次输入的密码不一致');
  } else {
    return Promise.resolve();
  }
};

// 注册处理
const handleSubmit = async () => {
  if (!formState.agree) {
    message.warning('请同意用户协议');
    return;
  }

  loading.value = true;

  try {
    const result = await userAPI.register({
      username: formState.username,
      email: formState.email,
      password: formState.password
    });
    
    if (result.success) {
      message.success('注册成功');
      router.push('/login');
    }
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
:deep(.ant-input-affix-wrapper) {
  padding: 12px 16px;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-checkbox-wrapper) {
  font-size: 14px;
}

:deep(.ant-btn-primary) {
  background-color: #1890ff;
}

:deep(.ant-btn-primary:hover) {
  background-color: #40a9ff;
}
</style>