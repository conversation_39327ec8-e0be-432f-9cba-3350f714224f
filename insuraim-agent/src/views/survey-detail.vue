<template>
  <div class="container mx-auto px-8 py-8">
    <!-- 加载状态 -->
    <div v-if="loading" class="flex flex-col items-center justify-center py-20">
      <a-spin size="large" />
      <span class="mt-4 text-gray-600">加载问卷中...</span>
    </div>
    
    <!-- 错误提示 -->
    <div v-if="error" class="bg-red-50 p-4 rounded-lg text-red-600 mb-8">
      <div class="flex items-center">
        <Icon icon="mdi:alert-circle" class="text-xl mr-2" />
        <span>加载问卷失败，请稍后重试</span>
      </div>
      <a-button type="primary" class="mt-4" @click="fetchSurveyDetail">重新加载</a-button>
      <a-button class="mt-4 ml-4" @click="backToList">返回列表</a-button>
    </div>
    
    <template v-if="!loading && !error && survey">
      <!-- 问卷头部信息 -->
      <div v-if="!surveySubmitted" class="mb-6">
        <div class="flex items-center mb-2">
          <a-button @click="backToList" type="primary" class="flex items-center" style="margin-right: 1rem;">
            <template #icon><Icon icon="mdi:arrow-left" style="vertical-align: -2px;" /></template>
            返回问卷列表
          </a-button>
          <h1 class="text-2xl font-bold text-gray-800 flex items-center my-0 leading-none">{{ survey.title }}</h1>
        </div>
        <p class="text-gray-600 mt-2">{{ survey.description }}</p>
        <div class="flex items-center mt-2 space-x-4">
          <a-tag :color="getTagColor(survey.type)">{{ getTypeName(survey.type) }}</a-tag>
          <span class="text-sm text-gray-500">
            预计用时: {{ survey.estimatedTime }}分钟 | 问题数量: {{ survey.questions ? survey.questions.length : 0 }}
          </span>
        </div>
      </div>
      
      <!-- 问卷主体内容 -->
      <div v-if="!surveySubmitted" class="bg-white rounded-lg p-6 shadow-sm mb-6">
        <a-form
          :model="formState"
          @finish="submitSurvey"
          layout="vertical"
        >
          <!-- 进度指示器 -->
          <div class="mb-8">
            <a-steps :current="currentStep" size="small">
              <a-step v-for="(section, index) in surveyProgressSteps" :key="index" :title="section.title" />
            </a-steps>
          </div>
          
          <!-- 问题区域 -->
          <template v-if="survey.questions && survey.questions.length > 0">
            <div v-for="(question, qIndex) in currentSectionQuestions" :key="qIndex" class="mb-10">
              <div class="mb-4">
                <div class="flex items-start">
                  <div class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center mr-3 flex-shrink-0">
                    {{ getQuestionNumber(question) }}
                  </div>
                  <div>
                    <h3 class="text-xl font-medium text-gray-800 mb-1">{{ question.title }}</h3>
                    <p v-if="question.description" class="text-gray-600 text-sm">{{ question.description }}</p>
                    <p v-if="question.required" class="text-red-500 text-sm mt-1">* 必填</p>
                  </div>
                </div>
              </div>
              
              <!-- 单选题 -->
              <div v-if="question.type === 'radio'" class="ml-9">
                <a-form-item 
                  :name="['answers', question.id]"
                  :rules="question.required ? [{ required: true, message: '请选择一个选项' }] : []"
                >
                  <a-radio-group 
                    v-model:value="formState.answers[question.id]"
                    class="w-full"
                  >
                    <div class="space-y-3">
                      <a-radio 
                        v-for="(option, oIndex) in question.options" 
                        :key="oIndex" 
                        :value="option.value"
                        class="block w-full p-3 border border-gray-200 rounded-md hover:bg-blue-50 transition-colors radio-option"
                      >
                        {{ option.label }}
                      </a-radio>
                    </div>
                  </a-radio-group>
                </a-form-item>
              </div>
              
              <!-- 多选题 -->
              <div v-else-if="question.type === 'checkbox'" class="ml-9">
                <a-form-item 
                  :name="['answers', question.id]"
                  :rules="question.required ? [{ required: true, message: '请至少选择一个选项' }] : []"
                >
                  <a-checkbox-group 
                    v-model:value="formState.answers[question.id]"
                    class="w-full"
                  >
                    <div class="space-y-3">
                      <a-checkbox 
                        v-for="(option, oIndex) in question.options" 
                        :key="oIndex" 
                        :value="option.value"
                        class="block w-full p-3 border border-gray-200 rounded-md hover:bg-blue-50 transition-colors checkbox-option"
                      >
                        {{ option.label }}
                      </a-checkbox>
                    </div>
                  </a-checkbox-group>
                </a-form-item>
              </div>
              
              <!-- 评分题 -->
              <div v-else-if="question.type === 'rating'" class="ml-9">
                <a-form-item 
                  :name="['answers', question.id]"
                  :rules="question.required ? [{ required: true, message: '请选择评分' }] : []"
                >
                  <a-rate 
                    v-model:value="formState.answers[question.id]"
                    :count="question.maxRating || 5"
                    allow-half
                  />
                </a-form-item>
              </div>
              
              <!-- 文本题 -->
              <div v-else-if="question.type === 'text'" class="ml-9">
                <a-form-item 
                  :name="['answers', question.id]"
                  :rules="question.required ? [{ required: true, message: '请填写回答' }] : []"
                >
                  <a-textarea 
                    v-model:value="formState.answers[question.id]"
                    :placeholder="question.placeholder || '请输入您的回答'"
                    :rows="4"
                    :maxlength="question.maxLength"
                    :showCount="question.maxLength ? true : false"
                  />
                </a-form-item>
              </div>
            </div>
            
            <!-- 导航按钮 -->
            <div class="flex justify-between mt-10">
              <a-button 
                v-if="currentStep > 0" 
                @click="prevStep" 
                class="flex items-center"
              >
                <template #icon><Icon icon="mdi:chevron-left" style="vertical-align: -2px;" /></template>
                上一部分
              </a-button>
              <div v-else></div>
              
              <div>
                <a-button
                  v-if="currentStep < surveyProgressSteps.length - 1"
                  type="primary"
                  @click="nextStep"
                  class="flex items-center"
                >
                  下一部分
                  <template #icon><Icon icon="mdi:chevron-right" class="ml-1" style="vertical-align: -2px;" /></template>
                </a-button>
                <a-button
                  v-else
                  type="primary"
                  html-type="submit"
                  :loading="submitting"
                >
                  提交问卷
                </a-button>
                
                <a-button
                  v-if="currentStep < surveyProgressSteps.length - 1"
                  @click="saveProgress"
                  :loading="saving"
                  class="ml-2"
                >
                  保存进度
                </a-button>
              </div>
            </div>
          </template>
          
          <!-- 没有问题 -->
          <template v-else>
            <div class="flex flex-col items-center justify-center py-10">
              <Icon icon="mdi:help-circle-outline" class="text-5xl text-gray-400" />
              <p class="mt-4 text-gray-600">该问卷没有问题</p>
            </div>
          </template>
        </a-form>
      </div>
      
      <!-- 提交成功页面 -->
      <div v-if="surveySubmitted" class="bg-white rounded-lg p-8 shadow-sm text-center">
        <div class="inline-block p-5 rounded-full bg-green-100 mb-4">
          <Icon icon="mdi:check-circle" class="text-6xl text-green-500" />
        </div>
        <h2 class="text-2xl font-bold mb-4">问卷提交成功！</h2>
        <p class="text-gray-600 mb-6">感谢您的反馈，您的意见对我们非常重要。</p>
        <div class="flex justify-center gap-4">
          <a-button type="primary" @click="backToList">
            返回问卷列表
          </a-button>
          <a-button @click="goToHome">
            返回首页
          </a-button>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import { Icon } from '@iconify/vue';
import { surveyAPI } from '@/api/index';

// 路由实例
const router = useRouter();
const route = useRoute();

// 状态变量
const survey = ref(null);
const loading = ref(false);
const error = ref(false);
const submitting = ref(false);
const saving = ref(false);
const surveySubmitted = ref(false);
const currentStep = ref(0);

// 表单状态
const formState = reactive({
  answers: {}
});

// 获取问卷ID
const surveyId = computed(() => {
  return route.query.id;
});

// 获取当前部分的问题
const currentSectionQuestions = computed(() => {
  if (!survey.value || !survey.value.questions) return [];
  
  // 如果没有分区，则所有问题都在一个部分
  if (!survey.value.sections || survey.value.sections.length === 0) {
    return survey.value.questions;
  }
  
  // 获取当前部分的问题
  const section = survey.value.sections[currentStep.value];
  if (!section) return [];
  
  // 筛选属于当前部分的问题
  return survey.value.questions.filter(q => q.sectionId === section.id);
});

// 问卷进度步骤
const surveyProgressSteps = computed(() => {
  if (!survey.value) return [];
  
  // 如果没有分区，则创建一个默认步骤
  if (!survey.value.sections || survey.value.sections.length === 0) {
    return [{ title: survey.value.title || '问卷调查' }];
  }
  
  return survey.value.sections.map(section => ({
    id: section.id,
    title: section.title
  }));
});

// 获取标签颜色
const getTagColor = (type) => {
  const colorMap = {
    satisfaction: 'blue',
    feedback: 'purple',
    needs: 'orange',
    default: 'cyan'
  };
  return colorMap[type] || colorMap.default;
};

// 获取类型名称
const getTypeName = (type) => {
  const typeMap = {
    satisfaction: '满意度调查',
    feedback: '意见反馈',
    needs: '需求调研',
    default: '其他'
  };
  return typeMap[type] || typeMap.default;
};

// 获取问题编号
const getQuestionNumber = (question) => {
  if (!survey.value || !survey.value.questions) return 1;
  
  const index = survey.value.questions.findIndex(q => q.id === question.id);
  return index >= 0 ? index + 1 : 1;
};

// 获取问卷详情
const fetchSurveyDetail = async () => {
  if (!surveyId.value) {
    error.value = true;
    message.error('问卷ID不能为空');
    return;
  }
  
  loading.value = true;
  error.value = false;
  
  try {
    // 真实环境下使用API调用
    // const response = await surveyAPI.getSurveyDetail(surveyId.value);
    // survey.value = response;
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 800));
    
    // 使用模拟数据
    survey.value = mockSurveyDetail;
    
    // 初始化表单状态
    initFormState();
    
  } catch (err) {
    console.error('获取问卷详情失败', err);
    error.value = true;
    message.error('获取问卷详情失败');
  } finally {
    loading.value = false;
  }
};

// 初始化表单状态，预填问卷答案（如果有）
const initFormState = () => {
  if (!survey.value || !survey.value.questions) return;
  
  // 初始化所有问题的答案字段
  const initialAnswers = {};
  
  survey.value.questions.forEach(question => {
    // 如果有保存的答案，则使用保存的答案
    if (survey.value.savedAnswers && survey.value.savedAnswers[question.id] !== undefined) {
      initialAnswers[question.id] = survey.value.savedAnswers[question.id];
    } else {
      // 否则，根据问题类型设置初始值
      switch (question.type) {
        case 'checkbox':
          initialAnswers[question.id] = [];
          break;
        case 'rating':
          initialAnswers[question.id] = 0;
          break;
        default:
          initialAnswers[question.id] = '';
      }
    }
  });
  
  formState.answers = initialAnswers;
};

// 下一步
const nextStep = () => {
  // 检查当前步骤中的必填问题是否已填写
  const requiredQuestions = currentSectionQuestions.value.filter(q => q.required);
  const hasEmptyRequired = requiredQuestions.some(q => {
    const answer = formState.answers[q.id];
    if (q.type === 'checkbox') {
      return !answer || answer.length === 0;
    }
    return answer === undefined || answer === null || answer === '';
  });
  
  if (hasEmptyRequired) {
    message.warning('请填写所有必填问题');
    return;
  }
  
  if (currentStep.value < surveyProgressSteps.value.length - 1) {
    currentStep.value++;
    // 滚动到页面顶部
    window.scrollTo(0, 0);
  }
};

// 上一步
const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--;
    // 滚动到页面顶部
    window.scrollTo(0, 0);
  }
};

// 保存问卷进度
const saveProgress = async () => {
  saving.value = true;
  
  try {
    const data = {
      surveyId: surveyId.value,
      answers: formState.answers
    };
    
    // 真实环境下使用API调用
    // await surveyAPI.saveSurveyProgress(data);
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 800));
    
    message.success('进度保存成功');
  } catch (err) {
    console.error('保存问卷进度失败', err);
    message.error('保存问卷进度失败');
  } finally {
    saving.value = false;
  }
};

// 提交问卷
const submitSurvey = async () => {
  submitting.value = true;
  
  try {
    const data = {
      surveyId: surveyId.value,
      answers: formState.answers
    };
    
    // 真实环境下使用API调用
    // await surveyAPI.submitSurvey(data);
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    message.success('问卷提交成功');
    surveySubmitted.value = true;
  } catch (err) {
    console.error('提交问卷失败', err);
    message.error('提交问卷失败');
  } finally {
    submitting.value = false;
  }
};

// 返回问卷列表
const backToList = () => {
  router.push('/survey-list');
};

// 返回首页
const goToHome = () => {
  router.push('/');
};

// 模拟问卷详情数据
const mockSurveyDetail = {
  id: 1,
  title: '保险产品满意度调查',
  description: '为了提升我们的保险产品和服务质量，请您花几分钟时间完成这份满意度调查问卷。',
  type: 'satisfaction',
  status: 'pending',
  estimatedTime: 5,
  questionCount: 10,
  endTime: Date.now() + 7 * 24 * 60 * 60 * 1000, // 7天后
  completedTime: null,
  
  // 问卷分区
  sections: [
    { id: 's1', title: '基本信息' },
    { id: 's2', title: '产品体验' },
    { id: 's3', title: '服务评价' }
  ],
  
  // 问题列表
  questions: [
    {
      id: 'q1',
      sectionId: 's1',
      title: '您的年龄段是？',
      type: 'radio',
      required: true,
      options: [
        { value: '18-24', label: '18-24岁' },
        { value: '25-34', label: '25-34岁' },
        { value: '35-44', label: '35-44岁' },
        { value: '45-54', label: '45-54岁' },
        { value: '55+', label: '55岁及以上' }
      ]
    },
    {
      id: 'q2',
      sectionId: 's1',
      title: '您购买的保险产品类型有哪些？',
      type: 'checkbox',
      required: true,
      options: [
        { value: 'life', label: '人寿保险' },
        { value: 'health', label: '健康保险' },
        { value: 'accident', label: '意外险' },
        { value: 'property', label: '财产保险' },
        { value: 'car', label: '汽车保险' },
        { value: 'other', label: '其他' }
      ]
    },
    {
      id: 'q3',
      sectionId: 's2',
      title: '您对我们保险产品的总体满意度如何？',
      type: 'rating',
      required: true,
      maxRating: 5
    },
    {
      id: 'q4',
      sectionId: 's2',
      title: '您认为我们的保险产品在哪些方面最有价值？',
      type: 'checkbox',
      required: false,
      options: [
        { value: 'price', label: '价格合理' },
        { value: 'coverage', label: '保障范围广' },
        { value: 'service', label: '服务质量高' },
        { value: 'claim', label: '理赔便捷' },
        { value: 'brand', label: '品牌信誉好' }
      ]
    },
    {
      id: 'q5',
      sectionId: 's2',
      title: '您对保险产品有什么改进建议？',
      type: 'text',
      required: false,
      placeholder: '请输入您的建议...',
      maxLength: 500
    },
    {
      id: 'q6',
      sectionId: 's3',
      title: '您对我们的客户服务满意度如何？',
      type: 'rating',
      required: true,
      maxRating: 5
    },
    {
      id: 'q7',
      sectionId: 's3',
      title: '您在使用我们的服务过程中遇到过哪些问题？',
      type: 'checkbox',
      required: false,
      options: [
        { value: 'communication', label: '沟通不畅' },
        { value: 'response', label: '响应速度慢' },
        { value: 'attitude', label: '服务态度差' },
        { value: 'professional', label: '专业知识不足' },
        { value: 'none', label: '没有遇到问题' }
      ]
    },
    {
      id: 'q8',
      sectionId: 's3',
      title: '您对我们的服务有什么建议？',
      type: 'text',
      required: false,
      placeholder: '请输入您的建议...',
      maxLength: 500
    }
  ],
  
  // 已保存的答案（如果有）
  savedAnswers: null
};

// 组件挂载时获取问卷详情
onMounted(() => {
  fetchSurveyDetail();
});
</script>

<style scoped>
/* 单选框和复选框样式 */
:deep(.radio-option.ant-radio-wrapper),
:deep(.checkbox-option.ant-checkbox-wrapper) {
  margin-right: 0;
  padding: 12px 16px;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  margin-bottom: 8px;
  transition: all 0.3s;
}

:deep(.radio-option.ant-radio-wrapper-checked),
:deep(.checkbox-option.ant-checkbox-wrapper-checked) {
  background-color: #e6f7ff;
  border-color: #1890ff;
}

:deep(.radio-option:hover),
:deep(.checkbox-option:hover) {
  border-color: #1890ff;
}

/* 修复图标和文字对齐问题 */
:deep(.ant-btn) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

:deep(.ant-btn .anticon),
:deep(.ant-btn .iconify) {
  display: inline-flex;
  align-self: center;
  line-height: 0;
}
</style> 