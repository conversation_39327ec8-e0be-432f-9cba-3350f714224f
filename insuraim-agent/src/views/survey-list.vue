<template>
  <div class="container mx-auto px-8 py-8">
    <!-- 页面标题 -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-800">问卷调查</h1>
      <p class="text-gray-600 mt-2">请选择以下问卷进行填写，您的反馈对我们非常重要</p>
    </div>

    <!-- 筛选和搜索区域 -->
    <div class="mb-6 flex flex-wrap justify-between items-center gap-4">
      <div class="flex items-center space-x-4">
        <a-select v-model:value="filterParams.status" placeholder="状态" style="width: 120px;"
          @change="handleFilterChange">
          <a-select-option value="">全部问卷</a-select-option>
          <a-select-option value="pending">待确认</a-select-option>
          <a-select-option value="completed">已完成</a-select-option>
        </a-select>
        <a-select v-model:value="filterParams.type" placeholder="类型" style="width: 120px;" @change="handleFilterChange">
          <a-select-option value="">全部类型</a-select-option>
          <a-select-option value="satisfaction">满意度调查</a-select-option>
          <a-select-option value="feedback">意见反馈</a-select-option>
          <a-select-option value="needs">需求调研</a-select-option>
        </a-select>
      </div>
      <div class="flex items-center">
        <a-input-search placeholder="搜索问卷" style="width: 250px;" @search="handleSearch"
          v-model:value="filterParams.keyword" />
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="flex flex-col items-center justify-center py-20">
      <a-spin size="large" />
      <span class="mt-4 text-gray-600">加载问卷中...</span>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="bg-red-50 p-4 rounded-lg text-red-600 mb-8">
      <div class="flex items-center">
        <Icon icon="mdi:alert-circle" class="text-xl mr-2" />
        <span>加载问卷失败，请稍后重试</span>
      </div>
      <a-button type="primary" class="mt-4" @click="fetchSurveys">重新加载</a-button>
    </div>

    <!-- 问卷列表 -->
    <div v-if="!loading && !error && surveys.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div v-for="survey in surveys" :key="survey.id"
        class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow">
        <div class="p-6">
          <div class="flex items-center space-x-2 mb-3">
            <a-tag :color="getTagColor(survey.type)">{{ getTypeName(survey.type) }}</a-tag>
            <a-tag :color="survey.status === 'completed' ? 'green' : 'blue'">
              {{ survey.status === 'completed' ? '已完成' : '待确认' }}
            </a-tag>
          </div>

          <h2 class="text-xl font-semibold text-gray-800 mb-3 line-clamp-2">{{ survey.title }}</h2>

          <p class="text-gray-600 text-sm mb-4 line-clamp-2">{{ survey.description }}</p>

          <div class="flex space-x-2 mb-4">
            <div class="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs flex items-center">
              <Icon icon="mdi:clock-outline" class="mr-1 text-sm" />
              {{ survey.estimatedTime }}分钟
            </div>
            <div class="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs flex items-center">
              <Icon icon="mdi:help-circle-outline" class="mr-1 text-sm" />
              {{ survey.questionCount }}个问题
            </div>
          </div>

          <div class="flex items-center justify-between">
            <span class="text-xs text-gray-500">
              {{ survey.status === 'completed' ? '完成时间: ' + formatDate(survey.completedTime) : '截止时间: ' +
                formatDate(survey.endTime) }}
            </span>
            <a-button type="primary" @click="startSurvey(survey.id)" class="flex items-center">
              {{ survey.status === 'completed' ? '查看结果' : '开始填写' }}
            </a-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && !error && surveys.length === 0"
      class="flex flex-col items-center justify-center py-20 bg-gray-50 rounded-lg">
      <Icon icon="mdi:clipboard-text-outline" class="text-6xl text-gray-400 mb-4" />
      <p class="text-gray-600">暂无可用的问卷</p>
      <a-button type="primary" class="mt-4" @click="clearFilters">清除筛选</a-button>
    </div>

    <!-- 分页组件 -->
    <div v-if="!loading && !error && surveys.length > 0" class="flex justify-center mt-8">
      <a-pagination v-model:current="pagination.current" :total="pagination.total" :pageSize="pagination.pageSize"
        @change="handlePaginationChange" show-quick-jumper show-size-changer :pageSizeOptions="['6', '12', '24']" />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { Icon } from '@iconify/vue';
import { surveyAPI } from '@/api/index';

// 路由实例
const router = useRouter();

// 状态变量
const surveys = ref([]);
const loading = ref(false);
const error = ref(false);

// 分页参数
const pagination = reactive({
  current: 1,
  pageSize: 6,
  total: 0
});

// 筛选参数
const filterParams = reactive({
  status: '',
  type: '',
  keyword: ''
});

// 获取问卷列表
const fetchSurveys = async () => {
  loading.value = true;
  error.value = false;

  try {
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...filterParams
    };

    // 真实环境下使用API调用
    // const response = await surveyAPI.getSurveyList(params);
    // surveys.value = response.records || [];
    // pagination.total = response.total || 0;

    // 模拟数据
    await new Promise(resolve => setTimeout(resolve, 800));
    surveys.value = mockSurveys;
    pagination.total = mockSurveys.length;

  } catch (err) {
    console.error('获取问卷列表失败', err);
    error.value = true;
    message.error('获取问卷列表失败');
  } finally {
    loading.value = false;
  }
};

// 清除筛选条件
const clearFilters = () => {
  filterParams.status = '';
  filterParams.type = '';
  filterParams.keyword = '';
  pagination.current = 1;
  fetchSurveys();
};

// 处理筛选条件变更
const handleFilterChange = () => {
  pagination.current = 1;
  fetchSurveys();
};

// 处理搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchSurveys();
};

// 处理分页变化
const handlePaginationChange = (page, pageSize) => {
  pagination.current = page;
  pagination.pageSize = pageSize;
  fetchSurveys();
};

// 开始问卷
const startSurvey = (surveyId) => {
  router.push({
    path: '/survey-detail',
    query: { id: surveyId }
  });
};

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return '-';
  const date = new Date(timestamp);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

// 获取标签颜色
const getTagColor = (type) => {
  const colorMap = {
    satisfaction: 'blue',
    feedback: 'purple',
    needs: 'orange',
    default: 'cyan'
  };
  return colorMap[type] || colorMap.default;
};

// 获取类型名称
const getTypeName = (type) => {
  const typeMap = {
    satisfaction: '满意度调查',
    feedback: '意见反馈',
    needs: '需求调研',
    default: '其他'
  };
  return typeMap[type] || typeMap.default;
};

// 模拟数据
const mockSurveys = [
  {
    id: 1,
    title: '保险产品满意度调查',
    description: '为了提升我们的保险产品和服务质量，请您花几分钟时间完成这份满意度调查问卷。',
    type: 'satisfaction',
    status: 'pending',
    estimatedTime: 5,
    questionCount: 10,
    endTime: Date.now() + 7 * 24 * 60 * 60 * 1000, // 7天后
    completedTime: null
  },
  {
    id: 2,
    title: '客户服务体验反馈',
    description: '我们非常重视您的服务体验，请对我们最近的服务质量进行评价，帮助我们不断改进。',
    type: 'feedback',
    status: 'completed',
    estimatedTime: 3,
    questionCount: 5,
    endTime: Date.now() + 10 * 24 * 60 * 60 * 1000, // 10天后
    completedTime: Date.now() - 2 * 24 * 60 * 60 * 1000 // 2天前
  },
  {
    id: 3,
    title: '保险需求调研问卷',
    description: '为了更好地了解您的保险需求，请填写此问卷，帮助我们设计更符合您需求的产品。',
    type: 'needs',
    status: 'pending',
    estimatedTime: 8,
    questionCount: 15,
    endTime: Date.now() + 5 * 24 * 60 * 60 * 1000, // 5天后
    completedTime: null
  },
  {
    id: 4,
    title: '保险产品功能优先级调查',
    description: '我们正在计划新的保险产品功能开发，请帮助我们确定哪些功能对您更重要。',
    type: 'needs',
    status: 'pending',
    estimatedTime: 6,
    questionCount: 12,
    endTime: Date.now() + 14 * 24 * 60 * 60 * 1000, // 14天后
    completedTime: null
  },
  {
    id: 5,
    title: '线上理赔服务评价',
    description: '请对我们的线上理赔服务进行评价，您的反馈将帮助我们优化理赔流程。',
    type: 'feedback',
    status: 'completed',
    estimatedTime: 4,
    questionCount: 8,
    endTime: Date.now() + 3 * 24 * 60 * 60 * 1000, // 3天后
    completedTime: Date.now() - 1 * 24 * 60 * 60 * 1000 // 1天前
  },
  {
    id: 6,
    title: '保险代理人服务满意度评价',
    description: '请对为您服务的保险代理人进行评价，帮助我们提升客户服务质量。',
    type: 'satisfaction',
    status: 'pending',
    estimatedTime: 5,
    questionCount: 10,
    endTime: Date.now() + 6 * 24 * 60 * 60 * 1000, // 6天后
    completedTime: null
  }
];

// 组件挂载时获取问卷列表
onMounted(() => {
  fetchSurveys();
});
</script>

<style scoped>
/* 限制文本行数 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 修复图标和文字对齐问题 */
:deep(.ant-btn) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

:deep(.ant-btn .anticon),
:deep(.ant-btn .iconify) {
  display: inline-flex;
  align-self: center;
  line-height: 0;
}
</style>