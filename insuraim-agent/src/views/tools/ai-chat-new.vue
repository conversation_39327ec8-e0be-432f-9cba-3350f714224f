<template>
    <div class="neon-wave-chat">
        <XProvider>
            <Welcome />
            <div class="chat-container">
                <!-- 消息列表 -->
                <div class="messages-container" v-if="messages.length > 0" ref="msgContainer" :key="'container_'">
                    <div v-for="msg in messages.filter(m => m.role !== 'system')" :key="msg.id" class="message-item">
                        <span class="role-tag" :class="msg.role">{{ msg.role === 'assistant' ? '小保' : '我' }}</span>

                        <!-- 用户消息使用普通Bubble -->
                        <Bubble v-if="msg.role === 'user'" :type="msg.role" :content="msg.content">
                            <template #footer>
                                <span class="message-time">{{ formatTime(msg.timestamp) }}</span>
                            </template>
                        </Bubble>

                        <!-- AI消息使用流式显示和markdown渲染 -->
                        <Bubble :typing="false" v-else-if="msg.role === 'assistant'" :loading="loading"
                            :key="msg.id + '_' + (msg.streamContent ? msg.streamContent.length : 0)">
                            <template #footer>
                                <span class="message-time">{{ formatTime(msg.timestamp) }}</span>
                            </template>
                            <template #message>
                                <div class="message-content" v-html="renderMarkdown(msg.streamContent)"></div>
                            </template>
                        </Bubble>
                    </div>
                </div>

                <!-- 底部操作区 -->
                <div class="actions-container">
                    <div class="action-buttons">
                        <!-- 常用问题按钮 -->
                        <div class="quick-questions-buttons">
                            <button v-if="messages.length === 0" v-for="question in quickQuestions" :key="question.id"
                                class="quick-question-btn" @click="handleQuickQuestion(question.content)"
                                :title="question.content">
                                {{ question.title }}
                            </button>
                        </div>
                        <button class="action-button" @click="handleClearChat">清空对话</button>
                    </div>
                    <!-- 输入框 -->
                    <Sender :value="content" :loading="loading" :onChange="handleInputChange" :onSubmit="handleSubmit"
                        placeholder="请输入您的问题..." send-text="发送" />
                </div>
            </div>
        </XProvider>
    </div>
</template>

<script setup lang="ts">
import { ref, nextTick, watch, h, reactive, onMounted, onUnmounted } from 'vue';
import { Bubble, Sender, XProvider } from 'ant-design-x-vue';
import { message, Typography } from 'ant-design-vue';
import markdownit from 'markdown-it';
import Welcome from './components/ai/Welcome.vue';
// 对话消息的类型定义
interface MyChatMessage {
    id?: string; // 消息ID
    role: 'user' | 'assistant' | 'system'; // 角色
    content: string; // 内容
    streamContent?: string; // 流式内容
    isStreaming?: boolean; // 是否流式
    timestamp?: number; // 时间戳
}

const messages = ref<MyChatMessage[]>([]);
const loading = ref(false);
const content = ref(''); // 输入框内容
const msgContainer = ref<HTMLElement | null>(null);




// 常用问题数据
const quickQuestions = ref([
    {
        id: 1,
        title: '💡 什么是保险？',
        content: '请详细解释一下什么是保险，保险的基本原理是什么？'
    },
    {
        id: 2,
        title: '🏥 医疗保险',
        content: '医疗保险有哪些类型？如何选择适合自己的医疗保险？'
    },
    {
        id: 3,
        title: '🚗 车险理赔',
        content: '车险出险后如何理赔？理赔流程是怎样的？'
    },
    {
        id: 4,
        title: '💰 人寿保险',
        content: '人寿保险有什么作用？什么情况下需要购买人寿保险？'
    },
    {
        id: 5,
        title: '🏠 财产保险',
        content: '家庭财产保险包括哪些内容？如何计算保险金额？'
    },
    {
        id: 6,
        title: '📋 保险条款',
        content: '如何理解保险条款？购买保险时需要注意哪些条款？'
    }
]);

// 初始化markdown渲染器
const md = markdownit({ html: true, breaks: true });

// 创建markdown渲染函数
const renderMarkdown = (content?: string) => {
    if (!content) return ''; // 处理undefined或null的情况

    try {
        // 使用markdown-it渲染内容并返回HTML
        return md.render(content);
    } catch (e) {
        console.error('Markdown渲染错误:', e);
        return content || '';
    }
};



// 处理消息发送的函数
const handleSend = async (inputContent: string) => {
    if (!inputContent) return;

    // 先发送system message
    const systemMessage: MyChatMessage = {
        role: 'system',
        content: "你是一个保险顾问，你的名字是小保-一个由insuraim开发的保险AI助手，无论用户问你是谁或者你是什么，都请回答：我是小保，一个由insuraim开发的保险AI助手" +
            "请根据用户的问题给出专业的回答。",
        id: `msg-${Date.now()}`,
        timestamp: Date.now()
    };

    // 1. 将用户消息添加到列表
    const userMessage: MyChatMessage = {
        role: 'user',
        content: inputContent,
        id: `msg-${Date.now()}`,
        timestamp: Date.now()
    };
    messages.value.push(systemMessage);
    messages.value.push(userMessage);

    // 清空输入框
    content.value = '';

    // 2. 设置加载中状态
    loading.value = true;

    // 3. 创建AI回复消息对象并立即显示
    let assistantMessage: MyChatMessage = {
        role: 'assistant',
        content: '',
        streamContent: '', // 确保初始化为空字符串而不是undefined
        id: `msg-${Date.now()}`,
        timestamp: Date.now()
    };
    messages.value.push(assistantMessage);

    // 立即触发UI更新
    await nextTick();

    try {
        // 4. 创建API请求选项
        const options = {
            method: 'POST',
            headers: {
                Authorization: 'Bearer sk-qjypedbqdcauirctshsyvpucfaxalvolhkcabwqeiusofimn',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: "Qwen/Qwen3-8B",
                messages: messages.value.map(item => ({ role: item.role, content: item.content })),
                stream: true,
                max_tokens: 512,
                enable_thinking: false,
                thinking_budget: 4096,
                min_p: 0.05,
                stop: null,
                temperature: 0.7,
                top_p: 0.7,
                top_k: 50,
                frequency_penalty: 0.5,
                n: 1,
                response_format: { type: 'text' }
            })
        };

        // 5. 直接处理流式响应，不使用XStream包装
        const response = await fetch('https://api.siliconflow.cn/v1/chat/completions', options);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        if (!response.body) {
            throw new Error('Response body is null');
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';

        // 6. 实时处理流式数据
        while (true) {
            const { done, value } = await reader.read();

            if (done) {
                // 流结束时设置loading为false
                loading.value = false;
                break;
            }

            const chunk = decoder.decode(value, { stream: true });
            buffer += chunk;

            const lines = buffer.split('\n');
            buffer = lines.pop() || '';

            for (const line of lines) {
                const trimmedLine = line.trim();

                if (trimmedLine.startsWith('data: ')) {
                    const data = trimmedLine.slice(6);


                    if (data === '[DONE]') {
                        break;
                    }

                    if (!data) continue;

                    try {
                        const parsed = JSON.parse(data);
                        const deltaContent = parsed.choices?.[0]?.delta?.content;

                        // 只有当deltaContent不是undefined和null时才更新消息内容
                        if (deltaContent !== undefined && deltaContent !== null) {
                            // 流式更新
                            loading.value = false;

                            // 创建新的消息对象，保证响应式更新
                            const newContent = assistantMessage.content + deltaContent;
                            const newStreamContent = (assistantMessage.streamContent || '') + deltaContent;

                            // 提前渲染Markdown，检查是否有语法错误
                            try {
                                md.render(newStreamContent);
                            } catch (e) {

                            }

                            // 创建全新对象，添加一个timestamp，确保每次都是不同的对象
                            const updatedMessage = {
                                ...assistantMessage,
                                content: newContent,
                                streamContent: newStreamContent,
                                id: `msg-${Date.now()}` // 每次更新生成新ID，以免key值重复导致视图不更新
                            };

                            // 直接替换最后一个消息
                            messages.value.splice(messages.value.length - 1, 1, updatedMessage);

                            // 强制触发响应式更新
                            messages.value = [...messages.value];

                            // 更新引用
                            assistantMessage = updatedMessage;
                            await nextTick();
                            await new Promise(resolve => requestAnimationFrame(resolve));
                            scrollToBottom();
                        }
                    } catch (parseError) {
                        // 忽略解析错误
                    }
                }
            }
        }

    } catch (err: any) {
        message.error(`请求失败：${err.message}`);
        const lastMessage = messages.value[messages.value.length - 1];
        if (lastMessage && lastMessage.role === 'assistant' && !lastMessage.content && !lastMessage.streamContent) {
            messages.value.pop();
        }
    } finally {
        loading.value = false;
    }
};

// 清空对话
const handleClearChat = () => {
    messages.value = [];
    message.success('对话已清空');
};

// 格式化时间
const formatTime = (timestamp?: number) => {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
};

// 处理输入框变化
const handleInputChange = (value: string) => {
    content.value = value;
};

// 处理输入框提交
const handleSubmit = (value: string) => {
    handleSend(value);
};

// 处理常用问题点击
const handleQuickQuestion = (questionContent: string) => {
    handleSend(questionContent);
};

// 滚动到底部函数
const scrollToBottom = () => {
    if (msgContainer.value) {
        msgContainer.value.scrollTop = msgContainer.value.scrollHeight;
    }
};

// 监听消息变动（深度），每次变化后滚动
watch(
    messages,
    async () => {
        await nextTick();
        scrollToBottom();
    },
    { deep: true }
);
</script>


<style>
.neon-wave-chat {
    height: calc(100vh - 64px - 32px);
    background: #ffffff;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.chat-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    padding: 16px;
    min-height: 0;
}

.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 16px 0;
    display: flex;
    flex-direction: column;
    gap: 16px;
    min-height: 0;
}

.welcome-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #E0E8FF;
}

.welcome-container h2 {
    font-size: 24px;
    margin-bottom: 16px;
    background: linear-gradient(to right, #EC008C, #A000FF, #00BFFF);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.welcome-container p {
    font-size: 16px;
    color: #8A94AD;
}

.actions-container {
    margin-top: auto;
    padding-top: 20px;
}

.action-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    gap: 12px;
}

.action-button {
    background: #f5f5f5;
    border: 1px solid #d9d9d9;
    color: #666;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
}

.action-button:hover {
    background: #fff2f0;
    border-color: #ff4d4f;
    color: #ff4d4f;
}

.action-button:active {
    background: #ffccc7;
}

/* 常用问题样式 */
.quick-questions-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    align-items: center;
}

.quick-question-btn {
    background: #f5f5f5;
    border: 1px solid #d9d9d9;
    color: #666;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.quick-question-btn:hover {
    background: #e6f7ff;
    border-color: #1890ff;
    color: #1890ff;
}

.quick-question-btn:active {
    background: #bae7ff;
}

.message-time {
    font-size: 12px;
    color: #8A94AD;
    margin-top: 4px;
}

.message-content {
    max-width: 100%;
    overflow-x: auto;
    word-break: break-word;
    line-height: 2.5;
}

/* 角色标签 */
.role-tag {
    font-size: 12px;
    margin-bottom: 4px;
    display: block;
    user-select: none;
    font-weight: 600;
}

.role-tag.assistant {
    color: #A000FF;
    text-align: left;
}

.role-tag.user {
    color: #1890ff;
    text-align: left;
}

/* 玻璃态效果 */
:deep(.ant-design-x-bubble) {
    backdrop-filter: blur(8px);
    background: rgba(16, 20, 42, 0.6) !important;
    border: 1px solid transparent !important;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.2) !important;
}

/* AI消息气泡 */
:deep(.ant-design-x-bubble[data-role="assistant"]) {
    border-image: linear-gradient(to right, #EC008C, #A000FF, #00BFFF) 1 !important;
    animation: gradientFlow 3s infinite linear;
}

/* 用户消息气泡 */
:deep(.ant-design-x-bubble[data-role="user"]) {
    border-color: #00F6FF !important;
    box-shadow: 0 0 8px rgba(0, 246, 255, 0.3) !important;
}

/* 文字颜色 */
:deep(.ant-design-x-bubble) {
    color: #E0E8FF !important;
}

/* Markdown渲染样式 */
:deep(.ant-design-x-bubble .ant-typography),
:deep(.message-content) {
    color: #E0E8FF !important;
}

:deep(.ant-design-x-bubble .ant-typography h1),
:deep(.ant-design-x-bubble .ant-typography h2),
:deep(.ant-design-x-bubble .ant-typography h3),
:deep(.ant-design-x-bubble .ant-typography h4),
:deep(.ant-design-x-bubble .ant-typography h5),
:deep(.ant-design-x-bubble .ant-typography h6),
:deep(.message-content h1),
:deep(.message-content h2),
:deep(.message-content h3),
:deep(.message-content h4),
:deep(.message-content h5),
:deep(.message-content h6) {
    color: #E0E8FF !important;
    margin-top: 1em;
    margin-bottom: 0.5em;
    font-weight: 600;
}

:deep(.ant-design-x-bubble .ant-typography p),
:deep(.message-content p) {
    color: #E0E8FF !important;
    margin-bottom: 1em;
    line-height: 1.6;
}

:deep(.ant-design-x-bubble .ant-typography ul),
:deep(.ant-design-x-bubble .ant-typography ol),
:deep(.message-content ul),
:deep(.message-content ol) {
    color: #E0E8FF !important;
    padding-left: 1.5em;
    margin-bottom: 1em;
}

:deep(.ant-design-x-bubble .ant-typography li),
:deep(.message-content li) {
    color: #E0E8FF !important;
    margin-bottom: 0.5em;
}

:deep(.ant-design-x-bubble .ant-typography code),
:deep(.message-content code) {
    background: rgba(255, 255, 255, 0.1) !important;
    color: #00BFFF !important;
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}

:deep(.ant-design-x-bubble .ant-typography pre),
:deep(.message-content pre) {
    background: rgba(0, 0, 0, 0.3) !important;
    color: #E0E8FF !important;
    padding: 1em;
    border-radius: 6px;
    overflow-x: auto;
    margin: 1em 0;
}

:deep(.ant-design-x-bubble .ant-typography pre code),
:deep(.message-content pre code) {
    background: transparent !important;
    color: #E0E8FF !important;
    padding: 0;
}

:deep(.ant-design-x-bubble .ant-typography blockquote),
:deep(.message-content blockquote) {
    border-left: 4px solid #A000FF;
    padding-left: 1em;
    margin: 1em 0;
    color: #B8C5E8 !important;
    font-style: italic;
}

:deep(.ant-design-x-bubble .ant-typography a),
:deep(.message-content a) {
    color: #00BFFF !important;
    text-decoration: underline;
}

:deep(.ant-design-x-bubble .ant-typography a:hover),
:deep(.message-content a:hover) {
    color: #40a9ff !important;
}

/* 输入框样式 */
:deep(.ant-design-x-sender .ant-input) {
    background: rgba(16, 20, 42, 0.8) !important;
    border: none !important;
    color: #E0E8FF !important;
    transition: all 0.3s ease;
}

:deep(.ant-design-x-sender .ant-input:focus) {
    box-shadow: 0 2px 0 #A000FF !important;
    animation: borderPulse 2s infinite;
}

/* 发送按钮 */
:deep(.ant-design-x-sender .ant-btn-primary) {
    background: linear-gradient(to right, #EC008C, #A000FF, #00BFFF) !important;
    border: none !important;
    transition: all 0.3s ease;
}

:deep(.ant-design-x-sender .ant-btn-primary:hover) {
    transform: scale(1.05);
    background: linear-gradient(to right, #EC008C, #A000FF, #00BFFF) !important;
    background-size: 200% 100% !important;
    animation: gradientMove 2s infinite linear;
}

/* 动画效果 */
@keyframes gradientFlow {
    0% {
        border-image: linear-gradient(to right, #EC008C, #A000FF, #00BFFF) 1;
    }

    50% {
        border-image: linear-gradient(to right, #00BFFF, #EC008C, #A000FF) 1;
    }

    100% {
        border-image: linear-gradient(to right, #A000FF, #00BFFF, #EC008C) 1;
    }
}

@keyframes borderPulse {
    0% {
        box-shadow: 0 2px 0 #EC008C;
    }

    33% {
        box-shadow: 0 2px 0 #A000FF;
    }

    66% {
        box-shadow: 0 2px 0 #00BFFF;
    }

    100% {
        box-shadow: 0 2px 0 #EC008C;
    }
}

@keyframes gradientMove {
    0% {
        background-position: 0% 50%;
    }

    100% {
        background-position: 100% 50%;
    }
}

@keyframes neonPulse {
    0% {
        box-shadow: 0 0 5px #EC008C;
    }

    50% {
        box-shadow: 0 0 15px #A000FF;
    }

    100% {
        box-shadow: 0 0 5px #00BFFF;
    }
}

/* 消息进入动画 */
:deep(.ant-design-x-bubble) {
    animation: slideUp 0.3s ease-out forwards;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>