<template>
    <div class="mx-auto">
        <div class="bg-white rounded-lg shadow-md p-8">
            <!-- 页面标题 -->
            <div class="flex items-center mb-8">
                <Icon icon="material-symbols:calculate" class="text-blue-500 text-2xl mr-2 flex-shrink-0" />
                <h1 class="text-2xl font-medium text-gray-800">收益演算</h1>
            </div>

            <!-- 表单内容 -->
            <a-form :model="formState" name="benefit-calculator-form" :label-col="{ span: 6 }"
                :wrapper-col="{ span: 18 }" @finish="onFinish">

                <!-- 产品选择区域 -->
                <div class="mb-8">
                    <div class="form-section-title flex items-center">
                        <Icon icon="material-symbols:inventory-2" class="text-gray-500 text-xl mr-2 flex-shrink-0" />
                        <h2 class="text-lg font-medium text-gray-800">选择产品</h2>
                    </div>

                    <a-form-item label="产品选择" name="productId" :rules="[{ required: true, message: '请选择产品!' }]"
                        class="form-item-with-label">
                        <a-select v-model:value="formState.productId" placeholder="选择产品" :options="productOptions"
                            show-search :filter-option="filterOption" option-label-prop="label"
                            @change="handleProductChange" size="large" class="product-select"
                            :loading="loadingProducts">
                            <template #option="{ productName, productCode, companyLogoUrl }">
                                <div class="flex items-center py-2">
                                    <img v-if="companyLogoUrl" :src="companyLogoUrl"
                                        class="w-6 h-6 mr-3 object-contain flex-shrink-0"
                                        onerror="this.style.display='none'" />
                                    <div class="flex items-center">
                                        <span class="font-medium">{{ productName }}</span>
                                        <span class="text-gray-500 ml-2 text-sm">{{ productCode }}</span>
                                    </div>
                                </div>
                            </template>
                        </a-select>
                    </a-form-item>
                </div>

                <!-- 客户信息 -->
                <div class="mb-8" v-if="formState.productId">
                    <div class="form-section-title flex items-center">
                        <Icon icon="material-symbols:person" class="text-gray-500 text-xl mr-2 flex-shrink-0" />
                        <h2 class="text-lg font-medium text-gray-800">客户信息</h2>
                    </div>

                    <a-form-item label="客户姓名" name="customerName" :rules="[{ required: true, message: '请输入客户姓名!' }]"
                        class="form-item-with-label">
                        <a-input v-model:value="formState.customerName" placeholder="输入客户姓名" />
                    </a-form-item>

                    <a-form-item label="客户年龄" name="customerAge" :rules="[{ required: true, message: '请输入客户年龄!' }]"
                        class="form-item-with-label">
                        <a-input-number v-model:value="formState.customerAge" :min="0" :max="100" style="width: 100%"
                            placeholder="输入客户年龄" />
                    </a-form-item>

                    <a-form-item label="客户性别" name="customerGender" :rules="[{ required: true, message: '请选择客户性别!' }]"
                        class="form-item-with-label">
                        <a-select v-model:value="formState.customerGender" :options="genderOptions"
                            placeholder="选择客户性别" />
                    </a-form-item>
                </div>

                <!-- 保费信息 -->
                <div class="mb-8" v-if="formState.productId">
                    <div class="form-section-title flex items-center">
                        <Icon icon="material-symbols:payments" class="text-gray-500 text-xl mr-2 flex-shrink-0" />
                        <h2 class="text-lg font-medium text-gray-800">保费信息</h2>
                    </div>

                    <a-form-item label="保费金额" name="premiumAmount" :rules="[{ required: true, message: '请输入保费金额!' }]"
                        class="form-item-with-label">
                        <a-input-number v-model:value="formState.premiumAmount" :min="1" style="width: 100%"
                            placeholder="输入保费金额" :prefix="currentCurrencySymbol" :formatter="formatAmount" />
                    </a-form-item>

                    <a-form-item label="缴费年期" name="paymentYear" class="form-item-with-label">
                        <a-input v-model:value="paymentYearDisplay" readonly placeholder="根据产品自动设置" />
                    </a-form-item>

                    <a-form-item label="货币类型" name="currency" class="form-item-with-label">
                        <a-input v-model:value="currencyDisplay" readonly placeholder="根据产品自动设置" />
                    </a-form-item>
                </div>

                <!-- 操作按钮 -->
                <a-form-item :wrapper-col="{ span: 24 }" class="mt-8">
                    <div class="flex justify-center">
                        <a-space size="middle">
                            <a-button type="primary" html-type="submit" class="flex items-center justify-center"
                                :loading="calculating">
                                <Icon icon="material-symbols:calculate" class="mr-1 align-middle inline-flex" />
                                <span class="align-middle">开始演算</span>
                            </a-button>

                            <a-button @click="resetForm">
                                重置
                            </a-button>
                        </a-space>
                    </div>
                </a-form-item>
            </a-form>

            <!-- 演算结果 -->
            <div v-if="calculationResult" class="mt-12">
                <a-divider>
                    <div class="text-lg font-bold flex items-center justify-center">
                        <Icon icon="material-symbols:analytics" class="text-xl mr-2" />
                        演算结果
                    </div>
                </a-divider>

                <!-- 结果概览 -->
                <div class="mb-6 p-6 bg-gray-50 rounded-lg">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                        <div class="text-center">
                            <div class="text-sm text-gray-500">产品名称</div>
                            <div class="text-lg font-medium">{{ calculationResult.productName }}</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-500">客户姓名</div>
                            <div class="text-lg font-medium">{{ formState.customerName }}</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-500">客户年龄</div>
                            <div class="text-lg font-medium">{{ calculationResult.customerAge }}岁</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-500">客户性别</div>
                            <div class="text-lg font-medium">{{ getGenderLabel(calculationResult.customerGender) }}
                            </div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-500">保费金额</div>
                            <div class="text-lg font-medium">{{ formatCurrency(calculationResult.premiumAmount,
                                calculationResult.currency) }}</div>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="overflow-x-auto">
                    <a-table :columns="tableColumns" :data-source="calculationResult.data" :pagination="false"
                        :scroll="{ x: 800 }" size="middle" bordered>
                        <template #bodyCell="{ column, record }">
                            <template v-if="column.key === 'totalPremiumPaid'">
                                {{ formatCurrency(record.totalPremiumPaid, calculationResult.currency) }}
                            </template>
                            <template v-if="column.key === 'surrenderGuaranteed'">
                                {{ formatCurrency(record.surrenderGuaranteed, calculationResult.currency) }}
                            </template>
                            <template v-if="column.key === 'terminalBonus'">
                                {{ formatCurrency(record.terminalBonus, calculationResult.currency) }}
                            </template>
                            <template v-if="column.key === 'totalAmount'">
                                <span class="font-medium text-green-600">
                                    {{ formatCurrency(record.totalAmount, calculationResult.currency) }}
                                </span>
                            </template>
                        </template>
                    </a-table>
                </div>

                <!-- 导出按钮 -->
                <div class="mt-6 text-center">
                    <a-button type="default" @click="exportToExcel" class="flex items-center justify-center mx-auto">
                        <Icon icon="material-symbols:download" class="mr-1 align-middle inline-flex" />
                        <span class="align-middle">导出Excel</span>
                    </a-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';
import { ref, reactive, onMounted, computed } from 'vue';
import { message } from 'ant-design-vue';
import { planAPI } from '@/api';
import ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';

// ==================== 常量定义 ====================

/**
 * 货币符号映射表
 */
const currencySymbolMap = {
    'CNY': '¥',    // 人民币
    'HKD': 'HK$',  // 港币
    'USD': '$',    // 美元
    'AUD': 'A$',   // 澳元
    'EUR': '€',    // 欧元
    'GBP': '£',    // 英镑
    'CAD': 'C$',   // 加拿大元
    'SGD': 'S$',   // 新加坡元
    'CHF': 'CHF',  // 瑞士法郎
    'JPY': '¥',    // 日元
    'NZD': 'NZ$',  // 新西兰元
    'KRW': '₩',    // 韩元
    '': '¥'        // 默认使用人民币符号
};

// ==================== 选项数据 ====================

/**
 * 性别选项
 */
const genderOptions = [
    { value: 'male', label: '男性' },
    { value: 'female', label: '女性' },
];

/**
 * 货币类型选项
 */
const currencyOptions = [
    { value: 'CNY', label: '人民币' },
    { value: 'HKD', label: '港币' },
    { value: 'USD', label: '美元' },
    { value: 'AUD', label: '澳元' },
    { value: 'EUR', label: '欧元' },
    { value: 'GBP', label: '英镑' },
    { value: 'CAD', label: '加拿大元' },
    { value: 'SGD', label: '新加坡元' },
    { value: 'CHF', label: '瑞士法郎' },
    { value: 'JPY', label: '日元' },
    { value: 'NZD', label: '新西兰元' },
    { value: 'KRW', label: '韩元' },
];

// ==================== 状态定义 ====================

// 产品数据
const productOptions = ref([]);
const loadingProducts = ref(false);
const calculating = ref(false);
const calculationResult = ref(null);

// 当前选中的产品信息
const selectedProduct = ref(null);

/**
 * 表单数据
 */
const formState = reactive({
    // 产品相关
    productId: undefined,

    // 客户信息
    customerName: undefined,
    customerAge: undefined,
    customerGender: 'male',

    // 保费信息
    premiumAmount: undefined,
    paymentYear: undefined,
    currency: 'HKD',
});

// ==================== 计算属性 ====================

/**
 * 当前货币符号
 */
const currentCurrencySymbol = computed(() => {
    return currencySymbolMap[formState.currency] || '¥';
});

/**
 * 缴费年期显示文本
 */
const paymentYearDisplay = computed(() => {
    if (!selectedProduct.value || !selectedProduct.value.paymentYears) {
        return '';
    }

    // 确保 paymentYears 是数组或字符串
    const paymentYears = selectedProduct.value.paymentYears;

    if (Array.isArray(paymentYears)) {
        return paymentYears.length > 0 ? `${paymentYears[0]}年` : '';
    } else if (typeof paymentYears === 'string' || typeof paymentYears === 'number') {
        return `${paymentYears}年`;
    }

    return '';
});

const formatAmount = (value) => {
    if (!value) return '';
    return Number(value).toLocaleString();
};

/**
 * 获取实际的缴费年期数值
 */
const getPaymentYearValue = () => {
    if (!selectedProduct.value || !selectedProduct.value.paymentYears) {
        return undefined;
    }

    const paymentYears = selectedProduct.value.paymentYears;

    if (Array.isArray(paymentYears)) {
        return paymentYears.length > 0 ? parseInt(paymentYears[0]) : undefined;
    } else if (typeof paymentYears === 'string' || typeof paymentYears === 'number') {
        return parseInt(paymentYears);
    }

    return undefined;
};

/**
 * 货币类型显示文本
 */
const currencyDisplay = computed(() => {
    if (!selectedProduct.value || !selectedProduct.value.supportCurrency) {
        return '';
    }

    const supportCurrency = selectedProduct.value.supportCurrency;

    // 获取实际的货币代码（处理数组情况）
    let currencyCode;
    if (Array.isArray(supportCurrency)) {
        currencyCode = supportCurrency.length > 0 ? supportCurrency[0] : 'HKD';
    } else if (typeof supportCurrency === 'string') {
        currencyCode = supportCurrency;
    } else {
        currencyCode = 'HKD';
    }

    // 查找货币标签
    const currencyOption = currencyOptions.find(option => option.value === currencyCode);
    return currencyOption ? currencyOption.label : currencyCode;
});

/**
 * 获取实际的货币类型值
 */
const getCurrencyValue = () => {
    if (!selectedProduct.value || !selectedProduct.value.supportCurrency) {
        return 'HKD'; // 默认港币
    }

    const supportCurrency = selectedProduct.value.supportCurrency;

    // 如果是数组，取第一个值；如果是字符串，直接返回
    if (Array.isArray(supportCurrency)) {
        return supportCurrency.length > 0 ? supportCurrency[0] : 'HKD';
    } else if (typeof supportCurrency === 'string') {
        return supportCurrency;
    }

    return 'HKD'; // 默认港币
};

/**
 * 表格列定义
 */
const tableColumns = [
    {
        title: '保单年度',
        dataIndex: 'policyYear',
        key: 'policyYear',
        width: 100,
        align: 'center'
    },
    {
        title: '年龄',
        dataIndex: 'age',
        key: 'age',
        width: 80,
        align: 'center'
    },
    {
        title: '已缴保费总额',
        dataIndex: 'totalPremiumPaid',
        key: 'totalPremiumPaid',
        width: 150,
        align: 'right'
    },
    {
        title: '退保保证金额',
        dataIndex: 'surrenderGuaranteed',
        key: 'surrenderGuaranteed',
        width: 150,
        align: 'right'
    },
    {
        title: '终期红利',
        dataIndex: 'terminalBonus',
        key: 'terminalBonus',
        width: 150,
        align: 'right'
    },
    {
        title: '总金额',
        dataIndex: 'totalAmount',
        key: 'totalAmount',
        width: 150,
        align: 'right'
    }
];

// ==================== 方法定义 ====================

/**
 * 加载产品列表
 */
const loadProducts = async () => {
    loadingProducts.value = true;
    try {
        const response = await planAPI.getBenefitCalculateList();

        if (response && response.length > 0) {
            productOptions.value = response.map(product => ({
                value: product.id,
                label: product.productName,
                productName: product.productName,
                productCode: product.productCode,
                companyLogoUrl: product.companyLogoUrl,
                ...product
            }));
        } else {
            message.warning('暂无可用的产品');
        }
    } catch (error) {
        console.error('加载产品列表失败:', error);
        message.error('加载产品列表失败');
    } finally {
        loadingProducts.value = false;
    }
};

/**
 * 产品搜索过滤
 */
const filterOption = (input, option) => {
    const searchText = input.toLowerCase();
    return (
        option.productName?.toLowerCase().includes(searchText) ||
        option.productCode?.toLowerCase().includes(searchText)
    );
};

/**
 * 处理产品选择变化
 */
const handleProductChange = (productId) => {
    const product = productOptions.value.find(p => p.value === productId);
    selectedProduct.value = product;

    // 自动设置缴费年期和货币类型
    formState.paymentYear = getPaymentYearValue();
    formState.currency = getCurrencyValue();

    // 清除之前的计算结果
    calculationResult.value = null;
};

/**
 * 获取性别标签
 */
const getGenderLabel = (gender) => {
    const option = genderOptions.find(opt => opt.value === gender);
    return option ? option.label : gender;
};

/**
 * 格式化货币
 */
const formatCurrency = (amount, currency) => {
    if (amount === null || amount === undefined) return '-';
    const symbol = currencySymbolMap[currency] || '¥';
    return `${symbol}${Number(amount).toLocaleString()}`;
};

/**
 * 获取性别中文字符串
 */
const getGenderString = (genderValue) => {
    const option = genderOptions.find(opt => opt.value === genderValue);
    return option ? option.label : genderValue;
};

/**
 * 表单提交处理
 */
const onFinish = async () => {
    calculating.value = true;
    try {
        // 确保 currency 是字符串而不是数组
        const currencyValue = Array.isArray(formState.currency)
            ? (formState.currency.length > 0 ? formState.currency[0] : 'HKD')
            : formState.currency;

        const requestData = {
            id: formState.productId,
            customerName: formState.customerName,
            customerAge: formState.customerAge,
            customerGender: getGenderString(formState.customerGender), // 传递中文字符串
            premiumAmount: formState.premiumAmount,
            paymentYear: formState.paymentYear,
            currency: currencyValue // 确保传递字符串，如 "USD", "HKD"
        };

        console.log('提交的请求数据:', requestData); // 临时调试日志

        const response = await planAPI.calculateForExcel(requestData);
        if (response) {
            calculationResult.value = response;
            message.success('演算完成！');
        }
    } catch (error) {
        console.error('演算失败:', error);
        message.error('演算失败，请检查输入参数');
    } finally {
        calculating.value = false;
    }
};

/**
 * 重置表单
 */
const resetForm = () => {
    formState.productId = undefined;
    formState.customerName = undefined;
    formState.customerAge = undefined;
    formState.customerGender = 'male';
    formState.premiumAmount = undefined;
    formState.paymentYear = undefined;
    formState.currency = 'HKD';

    selectedProduct.value = null;
    calculationResult.value = null;
};

/**
 * 导出Excel
 */
const exportToExcel = async () => {
    if (!calculationResult.value || !calculationResult.value.data) {
        message.warning('没有可导出的数据');
        return;
    }

    try {
        // 创建工作簿
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('收益演算结果');

        // 添加产品信息标题
        worksheet.addRow(['收益演算结果']);
        worksheet.getRow(1).font = { bold: true, size: 16 };
        worksheet.getRow(1).alignment = { horizontal: 'center' };
        worksheet.mergeCells('A1:F1');

        // 添加空行
        worksheet.addRow([]);

        // 添加产品信息
        worksheet.addRow(['产品信息']);
        worksheet.getRow(3).font = { bold: true, size: 14 };
        worksheet.getRow(3).fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFF2F2F2' }
        };

        worksheet.addRow(['产品名称:', calculationResult.value.productName || '']);
        worksheet.addRow(['产品编码:', selectedProduct.value?.productCode || '']);
        worksheet.addRow(['客户姓名:', formState.customerName || '']);
        worksheet.addRow(['客户年龄:', `${calculationResult.value.customerAge}岁`]);
        worksheet.addRow(['客户性别:', getGenderLabel(calculationResult.value.customerGender)]);
        worksheet.addRow(['保费金额:', formatCurrency(calculationResult.value.premiumAmount, calculationResult.value.currency)]);
        worksheet.addRow(['缴费年期:', `${calculationResult.value.paymentYear}年`]);
        worksheet.addRow(['货币类型:', currencyDisplay.value]);

        // 添加空行
        worksheet.addRow([]);

        // 添加演算数据标题
        worksheet.addRow(['演算数据']);
        worksheet.getRow(12).font = { bold: true, size: 14 };
        worksheet.getRow(12).fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFF2F2F2' }
        };

        // 添加数据表头
        const headerRow = worksheet.addRow([
            '保单年度',
            '年龄',
            '已缴保费总额',
            '退保保证金额',
            '终期红利',
            '总金额'
        ]);

        // 设置数据表头样式
        headerRow.font = { bold: true };
        headerRow.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFE6F3FF' }
        };

        // 添加演算数据
        calculationResult.value.data.forEach(item => {
            worksheet.addRow([
                item.policyYear,
                item.age,
                item.totalPremiumPaid,
                item.surrenderGuaranteed,
                item.terminalBonus,
                item.totalAmount
            ]);
        });

        // 设置列宽
        worksheet.getColumn(1).width = 12; // 保单年度
        worksheet.getColumn(2).width = 10; // 年龄
        worksheet.getColumn(3).width = 18; // 已缴保费总额
        worksheet.getColumn(4).width = 18; // 退保保证金额
        worksheet.getColumn(5).width = 15; // 终期红利
        worksheet.getColumn(6).width = 15; // 总金额

        // 设置产品信息区域的样式
        for (let i = 4; i <= 11; i++) {
            const row = worksheet.getRow(i);
            row.getCell(1).font = { bold: true };
            row.getCell(1).fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'FFF9F9F9' }
            };
        }

        // 生成文件
        const buffer = await workbook.xlsx.writeBuffer();
        const customerNamePart = calculationResult.value.customerName ? `_${calculationResult.value.customerName}` : '';
        const fileName = `收益演算_${calculationResult.value.productName}${customerNamePart}_${new Date().toISOString().slice(0, 10)}.xlsx`;

        // 保存文件
        const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        saveAs(blob, fileName);

        message.success('导出成功！');
    } catch (error) {
        console.error('导出失败:', error);
        message.error('导出失败');
    }
};

// ==================== 生命周期 ====================

/**
 * 组件挂载时加载产品列表
 */
onMounted(() => {
    loadProducts();
});
</script>

<style scoped>
.form-section-title {
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
}

.form-item-with-label {
    margin-bottom: 24px;
}

.product-select {
    width: 100%;
}

:deep(.ant-form-item-label) {
    font-weight: 500;
}

:deep(.ant-table-thead > tr > th) {
    background-color: #fafafa;
    font-weight: 600;
}

:deep(.ant-table-tbody > tr:hover > td) {
    background-color: #f5f5f5;
}

.grid {
    display: grid;
}

.grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
}

.gap-4 {
    gap: 1rem;
}

@media (min-width: 768px) {
    .md\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}

@media (min-width: 1024px) {
    .lg\:grid-cols-5 {
        grid-template-columns: repeat(5, minmax(0, 1fr));
    }
}

.text-center {
    text-align: center;
}

.text-sm {
    font-size: 0.875rem;
}

.text-lg {
    font-size: 1.125rem;
}

.text-xl {
    font-size: 1.25rem;
}

.text-2xl {
    font-size: 1.5rem;
}

.font-medium {
    font-weight: 500;
}

.font-bold {
    font-weight: 700;
}

.text-gray-500 {
    color: #6b7280;
}

.text-gray-800 {
    color: #1f2937;
}

.text-blue-500 {
    color: #3b82f6;
}

.text-green-600 {
    color: #059669;
}

.bg-gray-50 {
    background-color: #f9fafb;
}

.bg-white {
    background-color: #ffffff;
}

.rounded-lg {
    border-radius: 0.5rem;
}

.shadow-md {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.p-6 {
    padding: 1.5rem;
}

.p-8 {
    padding: 2rem;
}

.mb-6 {
    margin-bottom: 1.5rem;
}

.mb-8 {
    margin-bottom: 2rem;
}

.mt-6 {
    margin-top: 1.5rem;
}

.mt-8 {
    margin-top: 2rem;
}

.mt-12 {
    margin-top: 3rem;
}

.mr-1 {
    margin-right: 0.25rem;
}

.mr-2 {
    margin-right: 0.5rem;
}

.ml-2 {
    margin-left: 0.5rem;
}

.mx-auto {
    margin-left: auto;
    margin-right: auto;
}

.flex {
    display: flex;
}

.items-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.flex-shrink-0 {
    flex-shrink: 0;
}

.align-middle {
    vertical-align: middle;
}

.inline-flex {
    display: inline-flex;
}

.overflow-x-auto {
    overflow-x: auto;
}

.w-6 {
    width: 1.5rem;
}

.h-6 {
    height: 1.5rem;
}

.mr-3 {
    margin-right: 0.75rem;
}

.object-contain {
    object-fit: contain;
}

.py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}
</style>