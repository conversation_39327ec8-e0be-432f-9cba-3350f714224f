<script setup>
import { Sender } from 'ant-design-x-vue';
import { useAIChat } from '@/store/chat';
import { ref } from 'vue';

defineOptions({ name: 'AXSenderComponent' });

const chatStore = useAIChat();
const message = ref('');

// 发送消息
const handleSend = async (content) => {
    if (!content.trim()) return;

    message.value = '';
    await chatStore.sendMessage(content);
};
</script>

<template>
    <div class="sender-container">
        <Sender v-model:value="message" placeholder="请输入您的问题..." :loading="chatStore.loading.value" @submit="handleSend"
            send-text="发送" :auto-size="{ minRows: 1, maxRows: 4 }" />
    </div>
</template>

<style scoped>
.sender-container {
    padding: 16px;
    border-top: 1px solid #f0f0f0;
    background-color: #fff;
}
</style>