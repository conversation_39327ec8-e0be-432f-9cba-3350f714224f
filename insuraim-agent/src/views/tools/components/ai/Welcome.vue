<template>
    <Flex vertical>
        <ConfigProvider v-for="({ algorithm, background }, index) in items" :key="index" :theme="{ algorithm }">
            <Card :style="{ borderRadius: 0 }">
                <Welcome :style="{ backgroundImage: background, borderStartStartRadius: 4 }"
                    title="Hello,我是小保, 很高兴为您服务!" description="您可以问我任何问题, 我会尽力为您解答。">
                    <template #icon>
                        <img :src="chat" alt="chat" class="w-10 h-10" />
                    </template>
                </Welcome>
            </Card>
        </ConfigProvider>
    </Flex>
</template>
<script setup>
import { Card, ConfigProvider, Flex, theme } from 'ant-design-vue';
import { Welcome } from 'ant-design-x-vue';
import chat from '@/assets/images/ai/chat.png';

defineOptions({ name: 'AXWelcomeBackgroundSetup' });

const items = [
    {
        algorithm: theme.defaultAlgorithm,
        background: 'linear-gradient(97deg, #f2f9fe 0%, #f7f3ff 100%)',
    },
];
</script>
