<template>
    <div class="grid grid-cols-1 lg:grid-cols-1 gap-6">
        <!-- 年度对比图 -->
        <div class="bg-white p-6 rounded-lg shadow-md flex-1">
            <h3 class="text-lg font-medium text-gray-700 mb-4 flex items-center">
                <Icon icon="material-symbols:bar-chart" class="text-blue-500 mr-2 text-xl" />
                中国人寿（海外）产品年度实现率对比
            </h3>
            <div class="h-80 relative">
                <div v-if="loading"
                    class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 z-10">
                    <a-spin />
                </div>
                <div ref="yearComparisonChartRef" class="h-full w-full"></div>
            </div>
        </div>


    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { Icon } from '@iconify/vue';
import * as echarts from 'echarts';

const props = defineProps({
    data: {
        type: Array,
        default: () => []
    },
    loading: {
        type: Boolean,
        default: false
    }
});

// 图表引用
const yearComparisonChartRef = ref(null);
const currencyDistributionChartRef = ref(null);

// 图表实例
let yearComparisonChart = null;
let currencyDistributionChart = null;

// 初始化年度对比图表
const initYearComparisonChart = () => {
    if (yearComparisonChart) {
        yearComparisonChart.dispose();
    }

    if (!yearComparisonChartRef.value) return;

    yearComparisonChart = echarts.init(yearComparisonChartRef.value);

    // 获取所有产品名称
    const productNames = props.data.map(item => item.productName);

    // 准备图表数据
    const data2022 = props.data.map(item => Number(item.avgRate2022));
    const data2023 = props.data.map(item => Number(item.avgRate2023));
    const growthRates = props.data.map(item => Number(item.growthRate));

    const option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
            formatter: function (params) {
                const productName = productNames[params[0].dataIndex];
                let html = `<div style="font-weight:bold;margin-bottom:5px;">${productName}</div>`;

                params.forEach(param => {
                    const color = param.color;
                    const seriesName = param.seriesName;
                    const value = param.value;
                    const marker = `<span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${color};"></span>`;

                    if (seriesName === '同比增长') {
                        html += `${marker}${seriesName}: ${value >= 0 ? '+' : ''}${value}%<br/>`;
                    } else {
                        html += `${marker}${seriesName}: ${value}%<br/>`;
                    }
                });

                return html;
            }
        },
        legend: {
            data: ['2022年平均实现率', '2023年平均实现率', '同比增长'],
            selected: {
                '同比增长': false
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: [
            {
                type: 'category',
                data: productNames.map(name => name.length > 10 ? name.substring(0, 10) + '...' : name),
                axisLabel: {
                    interval: 0,
                    rotate: 20
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
                name: '实现率(%)',
                position: 'left',
                axisLabel: {
                    formatter: '{value}%'
                }
            },
            {
                type: 'value',
                name: '同比增长(%)',
                position: 'right',
                axisLabel: {
                    formatter: '{value}%'
                }
            }
        ],
        series: [
            {
                name: '2022年平均实现率',
                type: 'bar',
                barWidth: 25,
                emphasis: {
                    focus: 'series'
                },
                itemStyle: {
                    color: '#faad14'
                },
                data: data2022
            },
            {
                name: '2023年平均实现率',
                type: 'bar',
                barWidth: 25,
                emphasis: {
                    focus: 'series'
                },
                itemStyle: {
                    color: '#1890ff',
                    width: 10
                },
                data: data2023
            },
            {
                name: '同比增长',
                type: 'line',
                yAxisIndex: 1,
                symbol: 'circle',
                symbolSize: 8,
                itemStyle: {
                    color: function (params) {
                        const value = params.value;
                        return value >= 0 ? '#52c41a' : '#f5222d';
                    }
                },
                lineStyle: {
                    width: 2,
                    type: 'dashed'
                },
                data: growthRates
            }
        ]
    };

    yearComparisonChart.setOption(option);
};

// 初始化货币分布图表
const initCurrencyDistributionChart = () => {
    if (currencyDistributionChart) {
        currencyDistributionChart.dispose();
    }

    if (!currencyDistributionChartRef.value) return;

    currencyDistributionChart = echarts.init(currencyDistributionChartRef.value);

    // 统计不同货币类型的数量
    const currencyCount = {};
    props.data.forEach(item => {
        const currency = item.currency || '未知';
        if (!currencyCount[currency]) {
            currencyCount[currency] = 0;
        }
        currencyCount[currency]++;
    });

    const data = Object.keys(currencyCount).map(currency => ({
        name: currency,
        value: currencyCount[currency]
    }));

    const option = {
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
            orient: 'vertical',
            left: 10,
            data: data.map(item => item.name)
        },
        series: [
            {
                name: '货币分布',
                type: 'pie',
                radius: ['40%', '70%'],
                avoidLabelOverlap: false,
                itemStyle: {
                    borderRadius: 10,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                label: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: '18',
                        fontWeight: 'bold'
                    }
                },
                labelLine: {
                    show: false
                },
                data: data
            }
        ],
        color: ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1']
    };

    currencyDistributionChart.setOption(option);
};

// 监听窗口大小变化，重新调整图表大小
const handleResize = () => {
    if (yearComparisonChart) {
        yearComparisonChart.resize();
    }
    if (currencyDistributionChart) {
        currencyDistributionChart.resize();
    }
};

// 监听数据变化，更新图表
watch(() => props.data, () => {
    if (props.data.length > 0 && !props.loading) {
        initYearComparisonChart();
        initCurrencyDistributionChart();
    }
}, { deep: true });

// 监听加载状态变化，更新图表
watch(() => props.loading, (newVal) => {
    if (!newVal && props.data.length > 0) {
        initYearComparisonChart();
        initCurrencyDistributionChart();
    }
});

onMounted(() => {
    window.addEventListener('resize', handleResize);

    // 如果已有数据，初始化图表
    if (props.data.length > 0 && !props.loading) {
        initYearComparisonChart();
        initCurrencyDistributionChart();
    }
});

onUnmounted(() => {
    window.removeEventListener('resize', handleResize);

    // 销毁图表实例
    if (yearComparisonChart) {
        yearComparisonChart.dispose();
    }
    if (currencyDistributionChart) {
        currencyDistributionChart.dispose();
    }
});
</script>