<template>
    <div class="data-table bg-white p-6 rounded-lg shadow-md mb-6">
        <a-spin :spinning="loading">
            <div class="mb-4 flex items-center justify-between">
                <h2 class="text-lg font-medium text-gray-700 flex items-center">
                    <Icon icon="material-symbols:table-chart" class="text-blue-500 mr-2 text-xl" />
                    中国人寿（海外）分红实现率数据
                </h2>
                <!-- <div class="flex items-center gap-2">
                    <a-button type="primary" @click="exportAllData" :disabled="!dataSource.length">
                        <template #icon>
                            <Icon icon="material-symbols:download" />
                        </template>
批量导出
</a-button>
<a-tag color="blue" class="flex items-center justify-center">
    <span>共 {{ total }} 条记录</span>
</a-tag>
</div> -->
            </div>

            <a-table :dataSource="dataSource" :columns="columns" :pagination="paginationConfig"
                @change="handleTableChange" :rowKey="record => record.key" :scroll="{ x: 'max-content' }"
                :bordered="true" :loading="loading" :expandedRowKeys="expandedRowKeys" @expand="onExpand"
                :customRow="customRowHandler">
                <template #headerCell="{ column }" class="parent">
                    <template v-if="column.key === 'productName'">
                        <span class="flex items-center">
                            <Icon icon="material-symbols:description-outline" class="mr-1 text-blue-500" />
                            {{ column.title }}
                        </span>
                    </template>
                    <template v-else-if="column.key === 'currency'">
                        <span class="flex items-center">
                            <Icon icon="material-symbols:payments-outline" class="mr-1 text-green-100" />
                            {{ column.title }}
                        </span>
                    </template>
                    <template v-else-if="column.key === 'yearCount'">
                        <span class="flex items-center">
                            <Icon icon="material-symbols:calendar-month" class="mr-1 text-orange-500" />
                            {{ column.title }}
                        </span>
                    </template>
                    <template v-else-if="column.key === 'avgRate2022'">
                        <span class="flex items-center">
                            <Icon icon="material-symbols:percent" class="mr-1 text-red-500" />
                            {{ column.title }}
                        </span>
                    </template>
                    <template v-else-if="column.key === 'avgRate2023'">
                        <span class="flex items-center">
                            <Icon icon="material-symbols:percent" class="mr-1 text-red-500" />
                            {{ column.title }}
                        </span>
                    </template>
                    <template v-else-if="column.key === 'growthRate'">
                        <span class="flex items-center">
                            <Icon icon="material-symbols:trending-up" class="mr-1 text-purple-500" />
                            {{ column.title }}
                        </span>
                    </template>
                    <template v-else-if="column.key === 'action'">
                        <span class="flex items-center">
                            <Icon icon="material-symbols:settings" class="mr-1 text-gray-500" />
                            {{ column.title }}
                        </span>
                    </template>
                </template>

                <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'avgRate2022' || column.key === 'avgRate2023'">
                        <div class="flex items-center">
                            <a-progress :percent="Number(record[column.dataIndex])" :showInfo="false"
                                :strokeColor="getProgressColor(record[column.dataIndex])" :strokeWidth="8"
                                style="width: 80px; margin-right: 8px;" />
                            <span :style="{ color: getTextColor(record[column.dataIndex]) }">
                                {{ record[column.dataIndex] }}%
                            </span>
                        </div>
                    </template>
                    <template v-else-if="column.key === 'action'">
                        <a-button type="link" size="small" @click="exportSingleProduct(record)">
                            <Icon icon="material-symbols:download" class="mr-1" />
                            导出
                        </a-button>
                    </template>
                </template>

                <template #expandedRowRender="{ record }">
                    <div class="p-4 bg-gray-50">
                        <!-- <div class="flex justify-between items-center mb-3">
                            <h4 class="text-base font-medium text-gray-700 flex items-center">
                                <Icon icon="material-symbols:history" class="text-blue-500 mr-2" />
                                {{ record.productName }} - 历年分红实现率
                            </h4>
                            <a-button type="primary" size="small" @click="exportSingleProduct(record)">
                                <Icon icon="material-symbols:download" class="mr-1" />
                                导出数据
                            </a-button>
                        </div> -->

                        <a-table :columns="expandedColumns" :dataSource="getExpandedData(record)" :pagination="false"
                            :rowKey="record => record.key" :bordered="true" size="small" class="child">
                            <template #bodyCell="{ column, text }">
                                <template v-if="column.key === 'rate2022' || column.key === 'rate2023'">
                                    <div class="flex items-center">
                                        <a-progress :percent="Number(text)" :showInfo="false"
                                            :strokeColor="getChildProgressColor(text)" :strokeWidth="8"
                                            style="width: 80px; margin-right: 8px;" />
                                        <span :style="{ color: getTextColor(text) }">
                                            {{ text }}%
                                        </span>
                                    </div>
                                </template>
                                <template v-else-if="column.key === 'growthRate'">
                                    <div class="flex items-center">
                                        <Icon
                                            :icon="Number(text) >= 0 ? 'material-symbols:arrow-upward' : 'material-symbols:arrow-downward'"
                                            :style="{ color: Number(text) >= 0 ? 'green' : 'red', marginRight: '4px' }" />
                                        <span :style="{ color: Number(text) >= 0 ? 'green' : 'red' }">
                                            {{ Number(text) >= 0 ? '+' : '' }}{{ text }}%
                                        </span>
                                    </div>
                                </template>
                            </template>
                        </a-table>
                    </div>
                </template>
            </a-table>
        </a-spin>
    </div>
</template>

<script setup>
import { ref, h, computed } from 'vue';
import { Icon } from '@iconify/vue';
import { getProgressColor, getTextColor, getExpandedData, getChildProgressColor } from './utils/helpers';
import { exportExcel } from '@/utils/excel';
import { message } from 'ant-design-vue';

const props = defineProps({
    dataSource: {
        type: Array,
        default: () => []
    },
    loading: {
        type: Boolean,
        default: false
    },
    total: {
        type: Number,
        default: 0
    },
    pagination: {
        type: Object,
        default: () => ({
            current: 1,
            pageSize: 10,
            total: 0
        })
    }
});

const emit = defineEmits(['change', 'update:pagination']);
// 自定义行点击处理函数
const customRowHandler = (record) => {
    return {
        onClick: () => {
            console.log(record);
        },
        style: 'cursor: pointer;'
    };
};
// 表格列定义
const columns = [
    {
        title: '产品名称',
        dataIndex: 'productName',
        key: 'productName',
        fixed: 'left',
        width: 220,
    },
    {
        title: '货币',
        dataIndex: 'currency',
        key: 'currency',
        width: 100,
    },
    {
        title: '年份数据',
        dataIndex: 'yearCount',
        key: 'yearCount',
        width: 100,
        customRender: ({ record }) => `${record.yearInfo.length}个年份`,
    },
    {
        title: '2022年平均实现率',
        dataIndex: 'avgRate2022',
        key: 'avgRate2022',
        width: 180,
        sorter: (a, b) => Number(a.avgRate2022) - Number(b.avgRate2022),
    },
    {
        title: '2023年平均实现率',
        dataIndex: 'avgRate2023',
        key: 'avgRate2023',
        width: 180,
        sorter: (a, b) => Number(a.avgRate2023) - Number(b.avgRate2023),
    },
    {
        title: '同比增长',
        dataIndex: 'growthRate',
        key: 'growthRate',
        width: 150,
        sorter: (a, b) => Number(a.growthRate) - Number(b.growthRate),
        customRender: ({ text }) => {
            const value = Number(text);
            const color = value >= 0 ? 'green' : 'red';
            const icon = value >= 0 ? 'material-symbols:arrow-upward' : 'material-symbols:arrow-downward';
            return h('div', { style: 'display: flex; align-items: center;' }, [
                h(Icon, {
                    icon: icon,
                    style: `color: ${color}; margin-right: 4px;`
                }),
                h('span', {
                    style: `color: ${color};`
                }, `${value >= 0 ? '+' : ''}${text}%`)
            ]);
        }
    },
    {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        width: 100,
        fixed: 'right'
    },
];

// 展开行的列定义
const expandedColumns = [
    { title: '签发年份', dataIndex: 'year', key: 'year', width: 120 },
    { title: '红利类型', dataIndex: 'type', key: 'type', width: 150 },
    { title: '货币', dataIndex: 'currency', key: 'currency', width: 100 },
    { title: '2022年实现率', dataIndex: 'rate2022', key: 'rate2022', width: 180 },
    { title: '2023年实现率', dataIndex: 'rate2023', key: 'rate2023', width: 180 },
    { title: '同比增长', dataIndex: 'growthRate', key: 'growthRate', width: 150 },
];

// 分页配置 - 改为计算属性，确保响应props的变化
const paginationConfig = computed(() => {
    return {
        ...props.pagination,
        current: props.pagination.current,
        pageSize: props.pagination.pageSize,
        total: props.total, // 确保使用props.total而不是props.pagination.total
        showSizeChanger: true,
        pageSizeOptions: ['10', '20', '50', '100'],
        showTotal: (total) => `共 ${total} 条记录`,
    };
});

// 展开行状态管理
const expandedRowKeys = ref([]);

const onExpand = (expanded, record) => {
    if (expanded) {
        expandedRowKeys.value.push(record.key);
    } else {
        expandedRowKeys.value = expandedRowKeys.value.filter(key => key !== record.key);
    }
};

// 表格变化处理
const handleTableChange = (pagination, filters, sorter) => {
    // 触发update:pagination事件，实现双向绑定
    emit('update:pagination', {
        ...props.pagination,
        current: pagination.current,
        pageSize: pagination.pageSize,
        total: props.total
    });

    // 同时保留原有的change事件
    emit('change', { pagination, filters, sorter });
};

// 导出单个产品数据
const exportSingleProduct = async (record) => {
    try {
        const data = getExpandedData(record);

        // 确保数据中的百分比值正确显示
        const formattedData = data.map(item => ({
            ...item,
            rate2022: `${item.rate2022}%`,
            rate2023: `${item.rate2023}%`,
            growthRate: `${item.growthRate}%`
        }));

        // 导出配置
        const exportOptions = {
            sheetName: record.productName,
            headerStyle: {
                font: { bold: true, color: { argb: 'FFFFFF' } },
                fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: '1890FF' } },
                alignment: { vertical: 'middle', horizontal: 'center' }
            },
            cellStyle: {
                alignment: { vertical: 'middle', horizontal: 'center' },
                border: {
                    top: { style: 'thin', color: { argb: '144deb' } },
                    left: { style: 'thin', color: { argb: '144deb' } },
                    bottom: { style: 'thin', color: { argb: '144deb' } },
                    right: { style: 'thin', color: { argb: '144deb' } }
                }
            },

        };

        const result = await exportExcel(
            formattedData,
            expandedColumns,
            `${record.productName}_分红实现率.xlsx`,
            exportOptions
        );

        if (result) {
            message.success(`${record.productName} 数据导出成功`);
        } else {
            message.error(`${record.productName} 数据导出失败`);
        }
    } catch (error) {
        console.error('导出失败:', error);
        message.error('导出失败，请稍后重试');
    }
};

// 批量导出所有产品数据
const exportAllData = async () => {
    try {
        if (props.dataSource.length === 0) {
            message.warning('暂无数据可导出');
            return;
        }

        // 创建工作簿数据结构
        const workbookData = props.dataSource.map(product => {
            const data = getExpandedData(product);

            // 确保数据中的百分比值正确显示
            const formattedData = data.map(item => ({
                ...item,
                rate2022: `${item.rate2022}%`,
                rate2023: `${item.rate2023}%`,
                growthRate: `${item.growthRate}%`
            }));

            return {
                sheetName: product.productName.substring(0, 30), // Excel工作表名称最大长度限制
                data: formattedData
            };
        });

        // 创建多个工作表的导出选项
        const exportOptions = {
            multipleSheets: true,
            sheetsData: workbookData,
            headerStyle: {
                font: { bold: true, color: { argb: 'FFFFFF' } },
                fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: '1890FF' } },
                alignment: { vertical: 'middle', horizontal: 'center' }
            },
            cellStyle: {
                alignment: { vertical: 'middle', horizontal: 'center' },
                border: {
                    top: { style: 'thin', color: { argb: '144deb' } },
                    left: { style: 'thin', color: { argb: '144deb' } },
                    bottom: { style: 'thin', color: { argb: '144deb' } },
                    right: { style: 'thin', color: { argb: '144deb' } }
                }
            },

        };

        const result = await exportExcel(
            null,
            expandedColumns,
            `分红实现率汇总_${new Date().toISOString().split('T')[0]}.xlsx`,
            exportOptions
        );

        if (result) {
            message.success('所有产品数据导出成功');
        } else {
            message.error('数据导出失败');
        }
    } catch (error) {
        console.error('批量导出失败:', error);
        message.error('批量导出失败，请稍后重试');
    }
};
</script>

<style scoped>
:deep(.ant-table-thead > tr > th) {
    background-color: #dcebff;
    font-weight: 600;
}

:deep(.child .ant-table-thead > tr > th) {
    background-color: #f0f1f2;
    font-weight: 600;
}

#f0f1f2:deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
    background-color: #e6f7ff;
}

:deep(.ant-table-tbody > tr:nth-child(even)) {
    background-color: #fafafa;
}

:deep(.ant-progress-bg) {
    transition: all 0.8s cubic-bezier(0.08, 0.82, 0.17, 1);
}
</style>