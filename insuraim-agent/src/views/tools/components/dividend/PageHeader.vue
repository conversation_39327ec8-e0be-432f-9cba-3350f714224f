<template>
    <div
        class="title-section mb-6 p-6 rounded-lg shadow-md text-white bg-gradient-to-r from-green-500 via-green-600 to-green-700">
        <div class="flex items-center">
            <Icon icon="material-symbols:bar-chart" class="text-4xl mr-3" />
            <h1 class="text-2xl font-bold page-title">{{ title }}</h1>
        </div>
        <p class="mt-2 page-description">
            {{ description }}
        </p>
    </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';

defineProps({
    title: {
        type: String,
        default: '分红实现率查询'
    },
    description: {
        type: String,
        default: '查询各保险产品历年分红实现率数据，帮助您了解产品的历史表现和投资回报情况。'
    }
});
</script>

<style scoped>
.title-section {
    background-size: 200% 200%;
    animation: gradientAnimation 5s ease infinite;
}

@keyframes gradientAnimation {
    0% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

.page-title {
    color: #fff;
}

.page-description {
    color: #e0e0e0;
}
</style>