<template>
    <div class="search-section bg-white p-6 rounded-lg shadow-md mb-6">
        <div class="flex flex-wrap items-center justify-between gap-4">
            <div class="flex-1 min-w-[280px] flex items-center gap-4">
                <div class="flex items-center flex-1">
                    <div class="mr-4">
                        <a-select v-model:value="filters.mainType" placeholder="请选择产品类型" class="w-full"
                            @change="handleFilterChange" :disabled="true">
                            <a-select-option v-for="type in productTypes" :key="type.value" :value="type.value">
                                {{ type.label }}
                            </a-select-option>
                        </a-select>
                    </div>
                    <a-cascader v-model:value="selectedRegionCompany" :options="cascaderOptions" placeholder="选择地区和保司"
                        @change="handleRegionCompanyChange" :loading="loadingCompanies" :show-search="true"
                        :filter-option="filterCascaderOption" class="w-full mr-2" :disabled="true" />
                    <a-input-search v-model:value="searchValue" placeholder="搜索产品名称" enter-button @search="handleSearch"
                        class="flex-1 max-w-[200px] ml-4" />
                </div>

                <a-button type="primary" @click="handleReset">
                    <template #icon>
                        <Icon icon="material-symbols:refresh" />
                    </template>
                    重置
                </a-button>
            </div>
            <div class="flex flex-wrap gap-4">
                <slot name="additional-filters"></slot>
            </div>
            <div class="flex flex-wrap items-center justify-start gap-4">
                <div class="stat-card bg-blue-50 px-4 py-2 rounded-lg border-l-4 border-blue-500 flex items-center">

                    <div>
                        <span class="text-gray-700 text-sm font-medium">产品总数：</span>
                        <span class="text-lg font-bold text-blue-600">{{ productCount }}</span>
                    </div>
                </div>
            </div>
        </div>

    </div>
</template>

<script setup>
import { ref, watch, computed, onMounted, reactive } from 'vue';
import { Icon } from '@iconify/vue';
import { useProductStore } from '@/store/modules/product';

// 初始化产品store
const productStore = useProductStore();

const props = defineProps({
    initialValue: {
        type: String,
        default: ''
    },
    productCount: {
        type: Number,
        default: 0
    },
    productTypes: {
        type: Array,
        default: () => []
    }
});

const emit = defineEmits(['search', 'reset']);

const searchValue = ref(props.initialValue);
const selectedRegionCompany = ref([]);
const companyOptions = ref([]);
const loadingCompanies = ref(false);

// 筛选条件
const filters = reactive({
    mainType: null,
    // 其他筛选条件可以添加在这里
});

// 地区选项
const regionOptions = [
    { label: '香港', value: '香港' },
    { label: '澳门', value: '澳门' },
    { label: '新加坡', value: '新加坡' },
    { label: '百慕大', value: '百慕大' }
];

// 监听初始值变化
watch(() => props.initialValue, (newVal) => {
    searchValue.value = newVal;
});

// 处理筛选条件变化
const handleFilterChange = () => {
    // 在这里可以添加对筛选条件变化的处理逻辑
    // 触发搜索，将当前筛选条件和搜索文本一起发送
    handleSearch(searchValue.value);
};

const handleSearch = (value) => {
    // 解析级联选择器的值
    const region = selectedRegionCompany.value && selectedRegionCompany.value.length > 0 ? selectedRegionCompany.value[0] : '';
    const company = selectedRegionCompany.value && selectedRegionCompany.value.length > 1 ? selectedRegionCompany.value[1] : '';

    // 发送搜索事件，包含文本搜索值和级联选择的值
    emit('search', {
        searchText: value,
        region: region,
        company: company,
        mainType: filters.mainType // 添加险种筛选
    });
};

const handleReset = () => {
    searchValue.value = '';
    selectedRegionCompany.value = []; // 清空级联选择器的值
    filters.mainType = null; // 清空险种筛选
    emit('reset');
};

// 组件挂载时获取公司列表
onMounted(() => {
    getCompanyList();
});

// 级联选择器选项
const cascaderOptions = computed(() => {
    // 构建级联选择器的数据结构
    return regionOptions.map(region => {
        // 筛选该地区的公司
        const regionCompanies = companyOptions.value
            .filter(company => company.region === region.value)
            .map(company => ({
                value: company.value,
                label: company.label
            }));

        // 如果没有公司数据，添加一个默认的"所有公司"选项
        if (regionCompanies.length === 0) {
            regionCompanies.push({
                value: '',
                label: '所有公司'
            });
        }

        return {
            value: region.value,
            label: region.label,
            children: regionCompanies
        };
    });
});

// 级联选择器过滤选项
const filterCascaderOption = (inputValue, path) => {
    return path.some(option => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0);
};

// 获取公司列表
const getCompanyList = async () => {
    loadingCompanies.value = true;
    try {
        const res = await productStore.getCompanyListOrigin();
        companyOptions.value = res.map(item => ({
            value: item.name,
            label: item.name,
            region: item.region || '香港' // 设置默认地区为香港
        }));
    } catch (error) {
        console.error('获取公司列表失败:', error);
    } finally {
        loadingCompanies.value = false;
    }
};

// 地区-保司级联选择变化处理
const handleRegionCompanyChange = (value) => {
    console.log('级联选择值变化:', value);
    // 在下一次搜索时会自动将级联选择的值发送给父组件
};
</script>

<style scoped>
.stat-card {
    transition: all 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}
</style>