<template>
    <a-card class="mb-6 rounded-lg" :bordered="false">
        <h1 class="text-2xl font-bold mb-4">推荐产品</h1>
        <!-- 产品总数卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 ">
            <!-- 明星产品卡片 - 点击后显示所有明星产品 -->
            <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-blue-500 cursor-pointer"
                @click="showAllStarProducts">
                <div class="flex items-center justify-between">
                    <div class="flex-1 line-clamp-2">
                        <p class="text-gray-500 text-sm mb-1">明星产品</p>
                        <p class="text-lg font-bold line-clamp-2 mb-2 mt-2">{{ topStarProduct.productName }}</p>
                        <div class="flex items-center mt-1">
                            <span class="bg-blue-100 text-blue-700 text-xs font-medium px-2 py-0.5 rounded">
                                分红实现率: {{ topStarProduct.topStar2023 || '0/0' }}
                            </span>
                            <span v-if="topStarProduct.topStar2023 === '2/2'"
                                class="ml-1 bg-green-100 text-green-700 text-xs font-medium px-2 py-0.5 rounded">
                                100% 达成
                            </span>
                        </div>
                    </div>
                    <div class="ml-2 relative">
                        <img :src="top" alt="TOP 1" class="w-12 h-12">

                    </div>
                </div>
                <div class="mt-2 text-xs text-gray-500">
                    <span class="flex items-center">
                        <Icon icon="material-symbols:info-outline" class="mr-1" />
                        2023年签发分红实现率TOP one
                    </span>
                    <span class="flex items-center mt-1 text-blue-500">
                        <Icon icon="material-symbols:touch-app" class="mr-1" />
                        点击查看所有明星产品
                    </span>
                </div>
            </div>

            <!-- 2023年平均实现率卡片 -->
            <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-green-500">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm">平均实现率 (2023)</p>
                        <p class="text-2xl font-bold">{{ rate2023 }}%</p>
                    </div>
                    <div class="bg-green-100 p-2 rounded-full">
                        <Icon icon="material-symbols:trending-up" class="text-2xl text-green-500" />
                    </div>
                </div>
                <div class="mt-2 text-xs text-gray-500">
                    <span class="flex items-center">
                        <Icon icon="material-symbols:info-outline" class="mr-1" />
                        当前分页产品的2023年平均实现率
                    </span>
                </div>
            </div>

            <!-- 2022年平均实现率卡片 -->
            <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-orange-500">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm">平均实现率 (2022)</p>
                        <p class="text-2xl font-bold">{{ rate2022 }}%</p>
                    </div>
                    <div class="bg-orange-100 p-2 rounded-full">
                        <Icon icon="material-symbols:trending-up" class="text-2xl text-orange-500" />
                    </div>
                </div>
                <div class="mt-2 text-xs text-gray-500">
                    <span class="flex items-center">
                        <Icon icon="material-symbols:info-outline" class="mr-1" />
                        当前分页产品的2022年平均实现率
                    </span>
                </div>
            </div>

            <!-- 同比增长卡片 -->
            <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-purple-500">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm">同比增长</p>
                        <p class="text-2xl font-bold" :class="growthRate >= 0 ? 'text-green-500' : 'text-red-500'">
                            {{ growthRate >= 0 ? '+' : '' }}{{ growthRate }}%
                        </p>
                    </div>
                    <div class="bg-purple-100 p-2 rounded-full">
                        <Icon
                            :icon="growthRate >= 0 ? 'material-symbols:arrow-upward' : 'material-symbols:arrow-downward'"
                            :class="growthRate >= 0 ? 'text-green-500' : 'text-red-500'" class="text-2xl" />
                    </div>
                </div>
                <div class="mt-2 text-xs text-gray-500">
                    <span class="flex items-center">
                        <Icon icon="material-symbols:info-outline" class="mr-1" />
                        当前分页产品的2023年相比2022年实现率变化
                    </span>
                </div>
            </div>
        </div>
    </a-card>
    <div class="mb-5"></div>

    <!-- 明星产品抽屉 -->
    <a-drawer :visible="drawerVisible" title="明星产品列表（以下产品排名不分先后）" placement="right" width="500"
        @close="drawerVisible = false">
        <div class="star-products-container">
            <div v-if="loading" class="flex justify-center items-center py-10">
                <a-spin />
            </div>
            <template v-else>
                <div v-if="starProducts.length === 0" class="text-center py-10 text-gray-500">
                    暂无明星产品数据
                </div>
                <div v-else class="space-y-4">
                    <div v-for="(product, index) in starProducts" :key="product.id"
                        class="product-card bg-white p-4 rounded-lg shadow-md border-l-4"
                        :class="getProductBorderClass(index)">
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <div class="flex items-center">
                                    <div class="rank-badge" :class="getRankBadgeClass(index)">
                                        {{ index + 1 }}
                                    </div>
                                    <p class="text-lg font-bold ml-2">{{ product.productName }}</p>
                                </div>
                                <div class="mt-3 flex flex-wrap gap-2">
                                    <span class="bg-blue-100 text-blue-700 text-xs font-medium px-2 py-1 rounded">
                                        分红实现率: {{ product.topStar2023 || '0/0' }}
                                    </span>
                                    <span v-if="product.topStar2023 === '2/2'"
                                        class="bg-green-100 text-green-700 text-xs font-medium px-2 py-1 rounded flex items-center">
                                        <Icon icon="material-symbols:check-circle" class="mr-1" />
                                        100% 达成
                                    </span>
                                    <span v-if="product.currency"
                                        class="bg-gray-100 text-gray-700 text-xs font-medium px-2 py-1 rounded">
                                        币种: {{ product.currency }}
                                    </span>
                                    <span v-if="product.dividendType"
                                        class="bg-purple-100 text-purple-700 text-xs font-medium px-2 py-1 rounded">
                                        分红类型: {{ product.dividendType }}
                                    </span>
                                    <span v-if="product.issueYear"
                                        class="bg-yellow-100 text-yellow-700 text-xs font-medium px-2 py-1 rounded">
                                        发行年份: {{ product.issueYear }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </a-drawer>
</template>

<script setup>
import { Icon } from '@iconify/vue';
import top from '@/assets/images/dividend/top.png';
import { productAPI } from '@/api';
import { ref, onMounted } from 'vue';

// 状态变量
const topStarProduct = ref({
    productName: '',
    topStar2023: '0/0'
});
const starProducts = ref([]);
const drawerVisible = ref(false);
const loading = ref(false);

defineProps({
    productCount: {
        type: Number,
        default: 0
    },
    rate2022: {
        type: [Number, String],
        default: 0
    },
    rate2023: {
        type: [Number, String],
        default: 0
    },
    growthRate: {
        type: [Number, String],
        default: 0
    }
});

// 获取明星产品列表
const getProductRealizationStar = async () => {
    loading.value = true;
    try {
        const res = await productAPI.getProductRealizationStar();
        if (res && res.length > 0) {
            // 保存完整列表
            starProducts.value = res;
            // 第一个卡片只展示排位为1的产品
            topStarProduct.value = res[0];
        }
    } catch (error) {
        console.error('获取明星产品失败:', error);
    } finally {
        loading.value = false;
    }
};

// 显示所有明星产品
const showAllStarProducts = () => {
    drawerVisible.value = true;
    // 如果还没有加载数据，则加载
    if (starProducts.value.length === 0) {
        getProductRealizationStar();
    }
};

// 获取产品卡片边框颜色
const getProductBorderClass = (index) => {
    const classes = [
        'border-blue-500',   // 第1名
        'border-green-500',  // 第2名
        'border-orange-500', // 第3名
    ];
    return index < classes.length ? classes[index] : 'border-gray-300';
};

// 获取排名徽章样式
const getRankBadgeClass = (index) => {
    const classes = [
        'bg-blue-500',   // 第1名
        'bg-green-500',  // 第2名
        'bg-orange-500', // 第3名
    ];
    return index < classes.length ? classes[index] : 'bg-gray-500';
};

onMounted(() => {
    getProductRealizationStar();
});
</script>

<style scoped>
.stat-card {
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.product-card {
    transition: all 0.3s ease;
}

.product-card:hover {
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.rank-badge {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    color: white;
    font-weight: bold;
    font-size: 14px;
}
</style>