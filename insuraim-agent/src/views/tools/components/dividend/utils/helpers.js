// /**
//  * 根据实现率获取进度条颜色
//  * @param {number|string} rate 实现率
//  * @returns {string} 颜色代码
//  */
// export const getProgressColor = (rate) => {
//     const numRate = Number(rate);
//     if (numRate >= 90) return '#52c41a'; // 绿色
//     if (numRate >= 70) return '#1890ff'; // 蓝色
//     if (numRate >= 50) return '#faad14'; // 黄色
//     return '#f5222d'; // 红色
// };
// /**
//  * 获取下拉列表颜色
//  * @param {number|string} rate 实现率
//  * @returns {string} 颜色代码
//  */
// export const getChildProgressColor = (rate) => {
//     const numRate = Number(rate);
//     if (numRate >= 90) return '#52c41a'; // 绿色
//     if (numRate >= 70) return '#1890ff'; // 蓝色
//     if (numRate >= 50) return '#faad14'; // 黄色
//     return '#f5222d'; // 红色
// };

/**
 * 根据实现率获取进度条颜色
 * @param {number|string} rate 实现率
 * @returns {string} 颜色代码
 */
export const getProgressColor = (rate) => {
    const numRate = Number(rate);
    if (numRate >= 60) return '#52c41a'; // 绿色
    return '#f5222d'; // 红色
};
/**
 * 获取下拉列表颜色
 * @param {number|string} rate 实现率
 * @returns {string} 颜色代码
 */
export const getChildProgressColor = (rate) => {
    const numRate = Number(rate);
    if (numRate >= 60) return '#52c41a'; // 绿色
    return '#f5222d'; // 红色
};
/**
 * 根据实现率获取文字颜色
 * @param {number|string} rate 实现率
 * @returns {string} 颜色代码
 */
export const getTextColor = (rate) => {
    const numRate = Number(rate);
    if (numRate >= 60) return '#52c41a'; // 绿色

    return '#f5222d'; // 红色
};

// /**
//  * 根据实现率获取文字颜色
//  * @param {number|string} rate 实现率
//  * @returns {string} 颜色代码
//  */
// export const getTextColor = (rate) => {
//     const numRate = Number(rate);
//     if (numRate >= 90) return '#52c41a'; // 绿色
//     if (numRate >= 70) return '#1890ff'; // 蓝色
//     if (numRate >= 50) return '#faad14'; // 黄色
//     return '#f5222d'; // 红色
// };

/**
 * 计算单个产品的同比增长率
 * @param {Array} infoArray 产品年份数据数组
 * @returns {string} 增长率（百分比）
 */
export const calculateGrowthRate = (infoArray) => {
    if (!infoArray || infoArray.length === 0) return '0';

    const sumRate2022 = infoArray.reduce((sum, item) => sum + Number(item.rate2022 || 0), 0);
    const sumRate2023 = infoArray.reduce((sum, item) => sum + Number(item.rate2023 || 0), 0);

    if (sumRate2022 === 0) return '0';

    const growth = ((sumRate2023 - sumRate2022) / sumRate2022) * 100;
    return growth.toFixed(1);
};

/**
 * 处理表格数据
 * @param {Array} productList 产品列表
 * @returns {Array} 处理后的表格数据
 */
export const processTableData = (productList) => {
    return productList.map(product => ({
        key: product.productName,
        productName: product.productName,
        currency: product.data?.info?.[0]?.currency || '-',
        yearInfo: product.data?.info || [],
        // 计算该产品2022年的平均实现率
        avgRate2022: product.data?.info ?
            (product.data.info.reduce((sum, item) => sum + Number(item.rate2022 || 0), 0) / product.data.info.length).toFixed(1) :
            '0',
        // 计算该产品2023年的平均实现率
        avgRate2023: product.data?.info ?
            (product.data.info.reduce((sum, item) => sum + Number(item.rate2023 || 0), 0) / product.data.info.length).toFixed(1) :
            '0',
        // 计算同比增长率
        growthRate: product.data?.info ?
            calculateGrowthRate(product.data.info) :
            '0'
    }));
};

/**
 * 获取展开行数据
 * @param {Object} record 表格行记录
 * @returns {Array} 展开行数据
 */
export const getExpandedData = (record) => {
    return record.yearInfo.map(item => {
        // 计算每年的同比增长
        const rate2022 = Number(item.rate2022 || 0);
        const rate2023 = Number(item.rate2023 || 0);
        let growthRate = '0';

        if (rate2022 > 0) {
            growthRate = (((rate2023 - rate2022) / rate2022) * 100).toFixed(1);
        }

        return {
            key: `${record.key}-${item.year}-${item.type}`,
            year: item.year,
            type: item.type,
            currency: item.currency,
            rate2022: item.rate2022,
            rate2023: item.rate2023,
            growthRate
        };
    });
}; 