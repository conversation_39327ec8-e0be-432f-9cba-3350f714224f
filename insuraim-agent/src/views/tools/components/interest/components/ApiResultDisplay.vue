<template>
    <ApiResult ref="apiResultRef" :apiCalculationResult="apiCalculationResult" @tab-change="handleTabChange"
        @exportToExcel="exportToExcel" @apply-stress-test="handleApplyStressTest"
        @reset-stress-test="handleResetStressTest" :currency="currency" :selectedProduct="selectedProduct" />
</template>

<script setup>
import { ref } from 'vue';
import ApiResult from './api_result/index.vue';
import html2canvas from 'html2canvas';

const props = defineProps({
    apiCalculationResult: {
        type: Object,
        required: true
    },
    currency: {
        type: String,
        default: 'CNY'
    },
    selectedProduct: {
        type: Object,
        default: () => ({})
    }
});

// 定义emit事件
const emit = defineEmits(['tab-change', 'exportToExcel', 'apply-stress-test', 'reset-stress-test']);

// API结果组件引用
const apiResultRef = ref(null);

// 图表原始DOM和图像缓存
const chartOriginalContainers = ref({});
const chartImages = ref({});

// 处理子组件的tab-change事件
const handleTabChange = (tabIndex) => {
    emit('tab-change', tabIndex);
};

// 处理子组件的exportToExcel事件
const exportToExcel = () => {
    emit('exportToExcel');
};

// 处理压力测试应用事件
const handleApplyStressTest = (stressTestSettings) => {
    emit('apply-stress-test', stressTestSettings);
};

// 处理压力测试重置事件
const handleResetStressTest = () => {
    emit('reset-stress-test');
};

/**
 * 准备图表用于PDF导出
 * 将图表转换为图像以便在PDF中正确显示
 */
const prepareForPDFExport = async () => {
    try {
        console.log('准备图表用于PDF导出...');
        const charts = document.querySelectorAll('.chart-container');

        if (!charts || charts.length === 0) {
            console.log('未找到图表容器元素');
            return;
        }

        // 记录原始容器和图表ID
        for (let i = 0; i < charts.length; i++) {
            const chart = charts[i];
            const chartId = chart.querySelector('div[id^="interest-rate-chart-"], div[id^="cash-value-chart-"]')?.id;

            if (chartId) {
                // 保存原始容器引用
                chartOriginalContainers.value[chartId] = {
                    container: chart,
                    innerHTML: chart.innerHTML
                };

                // 使用html2canvas将图表转换为图像
                const canvas = await html2canvas(chart, {
                    scale: 2, // 提高分辨率
                    useCORS: true, // 允许跨域图片
                    logging: false,
                    backgroundColor: '#ffffff'
                });

                // 将canvas转换为图像
                const img = document.createElement('img');
                img.src = canvas.toDataURL('image/png');
                img.style.width = '100%';
                img.style.height = '320px';
                img.style.objectFit = 'contain';

                // 保存图像以便恢复
                chartImages.value[chartId] = img.src;

                // 清空容器并添加图像
                chart.innerHTML = '';
                chart.appendChild(img);
            }
        }

        console.log(`成功准备${Object.keys(chartImages.value).length}个图表用于PDF导出`);
    } catch (error) {
        console.error('准备图表用于PDF导出失败:', error);
        throw error;
    }
};

/**
 * 导出后恢复图表
 * 恢复为交互式ECharts实例
 */
const restoreChartsAfterExport = () => {
    try {
        console.log('恢复图表的交互式状态...');

        // 恢复所有图表容器的原始HTML
        Object.entries(chartOriginalContainers.value).forEach(([chartId, data]) => {
            if (data.container) {
                data.container.innerHTML = data.innerHTML;

                // 通过ApiResult组件重新初始化图表
                if (apiResultRef.value && typeof apiResultRef.value.refreshCharts === 'function') {
                    apiResultRef.value.refreshCharts();
                }
            }
        });

        // 清空缓存
        chartOriginalContainers.value = {};
        chartImages.value = {};

        console.log('图表已恢复');
    } catch (error) {
        console.error('恢复图表失败:', error);
    }
};

/**
 * 同步压力测试状态到ApiResult组件
 * @param {boolean} status 压力测试状态
 * @param {number} rate 分红实现率
 */
const syncStressTestStatus = (status, rate) => {
    console.log('ApiResultDisplay: 同步压力测试状态', status, rate);
    if (apiResultRef.value && typeof apiResultRef.value.syncStressTestStatus === 'function') {
        apiResultRef.value.syncStressTestStatus(status, rate);
    } else {
        console.error('ApiResult组件未提供syncStressTestStatus方法');
    }
};

// 导出方法供父组件调用
defineExpose({
    prepareForPDFExport,
    restoreChartsAfterExport,
    syncStressTestStatus
});
</script>