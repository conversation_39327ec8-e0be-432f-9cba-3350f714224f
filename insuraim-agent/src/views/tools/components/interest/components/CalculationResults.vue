<template>
    <div v-if="calculationResult || apiCalculationResult" class="mt-8">
        <h2 class="text-lg font-semibold mb-4">计算结果</h2>
        <div class="bg-tertiary p-4 rounded-lg">
            <!-- 结果切换标签 -->
            <a-tabs v-if="apiCalculationResult" default-active-key="api">
                <a-tab-pane key="api" tab="产品单复利">
                    <ApiResultDisplay :apiCalculationResult="apiCalculationResult" />
                </a-tab-pane>
            </a-tabs>

            <!-- 如果没有API结果，只显示普通单复利计算结果 -->
            <LocalResultDisplay v-if="!apiCalculationResult && calculationResult" :calculationResult="calculationResult"
                :formData="formData" />
        </div>
    </div>
</template>

<script setup>
import ApiResultDisplay from './ApiResultDisplay.vue';
import LocalResultDisplay from './LocalResultDisplay.vue';

defineProps({
    calculationResult: {
        type: Object,
        default: null
    },
    apiCalculationResult: {
        type: Object,
        default: null
    },
    formData: {
        type: Object,
        required: true
    }
});
</script>