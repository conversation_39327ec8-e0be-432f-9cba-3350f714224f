<template>
    <div class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">年缴保费（元）</label>
                <a-input-number v-model:value="formValue.annualPremium" :min="0" :precision="2" class="w-full"
                    placeholder="请输入年缴保费" @change="emitUpdate" />
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">缴费期（年）</label>
                <a-input-number v-model:value="formValue.paymentYears" :min="1" :max="100" :precision="2" class="w-full"
                    placeholder="请输入缴费期" @change="emitUpdate" />
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">持有期（年）</label>
                <a-input-number v-model:value="formValue.holdingYears" :min="1" class="w-full" placeholder="请输入持有年数"
                    @change="emitUpdate" />
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">保证现金价值（元）</label>
                <a-input-number v-model:value="formValue.guaranteedValue" :min="0" :precision="2" class="w-full"
                    placeholder="请输入保证现金价值" @change="emitUpdate" />
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">非保证红利（元）</label>
                <a-input-number v-model:value="formValue.nonGuaranteedBonus" :min="0" :precision="2" class="w-full"
                    placeholder="请输入非保证红利" @change="emitUpdate" />
            </div>
        </div>
    </div>
</template>

<script setup>
import { reactive, watch } from 'vue';

const props = defineProps({
    modelValue: {
        type: Object,
        required: true
    }
});

const emit = defineEmits(['update:modelValue']);

// 创建本地响应式表单数据
const formValue = reactive({
    annualPremium: null,
    paymentYears: null,
    holdingYears: null,
    guaranteedValue: null,
    nonGuaranteedBonus: null
});

// 监听props变化，更新本地表单
watch(
    () => props.modelValue,
    (newVal) => {
        if (newVal) {
            Object.assign(formValue, newVal);
        }
    },
    { deep: true, immediate: true }
);

// 发送更新事件给父组件
const emitUpdate = () => {
    console.log('formValue', formValue);
    emit('update:modelValue', {
        ...props.modelValue, // 保留原始对象的所有属性
        annualPremium: formValue.annualPremium,
        paymentYears: formValue.paymentYears,
        holdingYears: formValue.holdingYears,
        guaranteedValue: formValue.guaranteedValue,
        nonGuaranteedBonus: formValue.nonGuaranteedBonus
    });
};
</script>

<style scoped>
:deep(.ant-input-number) {
    width: 100%;
}

:deep(.ant-input-number-input) {
    height: 32px;
}
</style>