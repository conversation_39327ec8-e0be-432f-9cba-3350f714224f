<template>
    <div>
        <div class="flex justify-between items-center mb-3">
            <label class="block text-sm font-medium text-gray-700">保费优惠设置</label>
            <a-button type="primary" size="small" @click="addDiscount" class="flex items-center">
                <template #icon>
                    <Icon icon="material-symbols:add" />
                </template>
                添加优惠
            </a-button>
        </div>

        <div v-if="discounts.length === 0" class="text-center py-3 bg-gray-50 rounded-md text-gray-500 text-sm">
            暂无优惠，点击"添加优惠"按钮添加
        </div>

        <div v-else class="space-y-2 bg-gray-50 p-3 rounded-md">
            <div v-for="(discount, index) in discounts" :key="index"
                class="flex items-center gap-2 bg-white p-2 rounded-md shadow-sm">
                <div class="flex-shrink-0 bg-blue-50 px-2 py-1 rounded text-blue-600 text-sm font-medium">
                    第
                </div>
                <a-input-number v-model:value="discount.year" :min="1" :max="maxPaymentYears" placeholder="几"
                    class="w-16" controls-position="right" @change="() => updateDiscount(index)" />
                <div class="flex-shrink-0 text-gray-500">年</div>

                <div class="flex-shrink-0 ml-2 text-gray-500">优惠</div>
                <a-input-number v-model:value="discount.rate" :min="0" :max="100" :precision="2" placeholder="比例"
                    class="w-20" @change="() => updateDiscount(index)" />
                <div class="flex-shrink-0 text-gray-500">%</div>

                <a-button type="text" danger class="ml-auto" @click="removeDiscount(index)">
                    <template #icon>
                        <Icon icon="material-symbols:delete-outline" />
                    </template>
                </a-button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';

const props = defineProps({
    discounts: {
        type: Array,
        required: true
    },
    maxPaymentYears: {
        type: Number,
        default: 30
    }
});

const emit = defineEmits(['add', 'remove', 'update']);

const addDiscount = () => {
    console.log('addDiscount', props.discounts);
    emit('add', { year: null, rate: null });
};

const removeDiscount = (index) => {
    emit('remove', index);
};

const updateDiscount = (index) => {
    emit('update', index, props.discounts[index]);
};
</script>

<style scoped>
:deep(.ant-input-number) {
    width: 100%;
}
</style>