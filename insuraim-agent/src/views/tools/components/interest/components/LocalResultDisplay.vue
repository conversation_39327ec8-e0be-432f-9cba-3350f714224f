<template>
    <div>

        <!-- 单复利折线图 -->
        <div class="mb-6">
            <div class="h-80 relative" ref="chartRef">
                <div v-if="loading"
                    class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 z-10">
                    <a-spin />
                </div>
            </div>
        </div>

        <!-- 现金价值与累积保费折线图 -->
        <div class="mb-6">
            <div class="h-80 relative" ref="cashValueChartRef">
                <div v-if="loading"
                    class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 z-10">
                    <a-spin />
                </div>
            </div>
        </div>
        <div class="flex justify-end mb-2">

            <a-button type="primary" @click="exportToExcel">
                <Icon icon="mdi:export" />
                <span>导出Excel</span>
            </a-button>
        </div>
        <!-- 计算过程详情 -->
        <div class="grid grid-cols-1 gap-6">
            <!-- 单利计算过程 -->
            <div class="bg-white p-4 rounded-lg border border-gray-200">
                <h3 class="text-lg font-semibold mb-3 text-blue-600">单利计算过程</h3>
                <div class="space-y-2 text-gray-700">
                    <p>1. 计算总投入本金：</p>
                    <div class="pl-4">
                        <p>年缴保费：{{ formData.annualPremium }}元</p>
                        <p>缴费期：{{ formData.paymentYears }}年</p>
                        <p>总投入本金 = {{ formData.annualPremium }} × {{ formData.paymentYears }} = {{
                            formData.annualPremium * formData.paymentYears }}元</p>
                    </div>
                    <p>2. 计算总收益：</p>
                    <div class="pl-4">
                        <p>保证现金价值：{{ formData.guaranteedValue || 0 }}元</p>
                        <p>非保证红利：{{ formData.nonGuaranteedBonus || 0 }}元</p>
                        <p>总收益 = {{ formData.guaranteedValue || 0 }} + {{ formData.nonGuaranteedBonus || 0 }} = {{
                            (formData.guaranteedValue || 0) + (formData.nonGuaranteedBonus || 0) }}元</p>
                    </div>
                </div>
            </div>

            <!-- 复利计算过程 -->
            <div class="bg-white p-4 rounded-lg border border-gray-200">
                <h3 class="text-lg font-semibold mb-3 text-green-600">复利计算过程</h3>
                <div class="space-y-2 text-gray-700">
                    <p>1. 计算现金流：</p>
                    <div class="pl-4">
                        <p>每年现金流（负值表示支出，正值表示收入）：</p>
                        <div class="overflow-x-auto">
                            <table class="min-w-full border-collapse">
                                <thead>
                                    <tr class="bg-tertiary">
                                        <th class="border px-4 py-2">年份</th>
                                        <th class="border px-4 py-2">现金流（元）</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="(flow, index) in calculationResult.cashFlows" :key="index">
                                        <td class="border px-4 py-2">第{{ index + 1 }}年</td>
                                        <td class="border px-4 py-2"
                                            :class="flow < 0 ? 'text-red-600' : 'text-green-600'">
                                            {{ flow.toLocaleString() }}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <p>2. 计算复利收益率：</p>
                    <div class="pl-4">
                        <p>使用内部收益率(IRR)方法计算复利收益率</p>
                        <p>IRR是使得现金流净现值为0的折现率</p>
                        <p class="font-bold text-green-600">最终复利收益率：{{ calculationResult.compoundInterest }}%</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick, onUnmounted } from 'vue';
import { Spin } from 'ant-design-vue';
import * as echarts from 'echarts';

const props = defineProps({
    calculationResult: {
        type: Object,
        required: true
    },
    formData: {
        type: Object,
        required: true
    }
});
const emit = defineEmits(['exportToExcel']);
// 图表引用和加载状态
const chartRef = ref(null);
const cashValueChartRef = ref(null);
const loading = ref(false);
let chart = null;
let cashValueChart = null;
const exportToExcel = () => {
    emit('exportToExcel');
};

// 初始化图表
const initChart = () => {
    loading.value = true;

    if (chart) {
        chart.dispose();
    }

    if (!chartRef.value) {
        loading.value = false;
        return;
    }

    chart = echarts.init(chartRef.value);

    // 准备图表数据
    const years = Array.from({ length: props.formData.holdingYears }, (_, i) => i + 1);
    const simpleInterest = props.calculationResult.simpleInterest;
    const compoundInterest = props.calculationResult.compoundInterest;

    // 创建单利和复利的数据系列
    const simpleInterestData = years.map(() => simpleInterest);
    const compoundInterestData = years.map(() => compoundInterest);

    const option = {
        title: {
            text: '单复利收益率对比',
            left: 'center'
        },
        tooltip: {
            trigger: 'axis',
            formatter: function (params) {
                return `第${params[0].dataIndex + 1}年<br/>
                       单利收益率: ${params[0].value.toFixed(2)}%<br/>
                       复利收益率: ${params[1].value.toFixed(2)}%`;
            }
        },
        legend: {
            data: ['单利收益率', '复利收益率'],
            bottom: 10
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            top: '15%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: years,
            name: '年份'
        },
        yAxis: [
            {
                type: 'value',
                name: '单利收益率 (%)',
                axisLabel: {
                    formatter: '{value}%'
                },
                position: 'left'
            },
            {
                type: 'value',
                name: '复利收益率 (%)',
                axisLabel: {
                    formatter: '{value}%'
                },
                position: 'right'
            }
        ],
        series: [
            {
                name: '单利收益率',
                type: 'line',
                data: simpleInterestData,
                itemStyle: {
                    color: '#1890ff'
                },
                yAxisIndex: 0
            },
            {
                name: '复利收益率',
                type: 'line',
                data: compoundInterestData,
                itemStyle: {
                    color: '#52c41a'
                },
                yAxisIndex: 1,
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [{
                            offset: 0,
                            color: 'rgba(82, 196, 26, 0.3)'
                        }, {
                            offset: 1,
                            color: 'rgba(82, 196, 26, 0.1)'
                        }]
                    }
                }
            }
        ]
    };

    chart.setOption(option);
    loading.value = false;
};

// 初始化现金价值与累积保费折线图
const initCashValueChart = () => {
    loading.value = true;

    if (cashValueChart) {
        cashValueChart.dispose();
    }

    if (!cashValueChartRef.value) {
        loading.value = false;
        return;
    }

    cashValueChart = echarts.init(cashValueChartRef.value);

    // 准备图表数据
    const years = Array.from({ length: props.formData.holdingYears }, (_, i) => i + 1);
    const annualPremium = props.formData.annualPremium || 0;
    const paymentYears = props.formData.paymentYears || 0;
    const holdingYears = props.formData.holdingYears || 0;
    const finalValue = (props.formData.guaranteedValue || 0) + (props.formData.nonGuaranteedBonus || 0);

    const premiums = [];
    const values = [];

    for (let i = 1; i <= holdingYears; i++) {
        // 累计保费
        premiums.push(i <= paymentYears ? annualPremium * i : annualPremium * paymentYears);

        // 简化模型：线性增长的现金价值
        if (i === holdingYears) {
            values.push(finalValue);
        } else {
            values.push(Math.round(finalValue * i / holdingYears));
        }
    }

    const option = {
        title: {
            text: '现金价值与累计保费对比',
            left: 'center'
        },
        tooltip: {
            trigger: 'axis',
            formatter: function (params) {
                const yearIndex = params[0].dataIndex;
                return `第${yearIndex + 1}年<br/>
                       累计保费: ${params[0].value.toLocaleString()}元<br/>
                       预估现金价值: ${params[1].value.toLocaleString()}元`;
            }
        },
        legend: {
            data: ['累计保费', '预估现金价值'],
            bottom: 10
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            top: '15%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: years,
            name: ''
        },
        yAxis: [
            {
                type: 'value',
                name: '累计保费 (元)',
                position: 'left'
            },
            {
                type: 'value',
                name: '预估现金价值 (元)',
                position: 'right'
            }
        ],
        series: [
            {
                name: '累计保费',
                type: 'line',
                data: premiums,
                itemStyle: {
                    color: '#1890ff'
                },
                yAxisIndex: 0
            },
            {
                name: '预估现金价值',
                type: 'line',
                data: values,
                itemStyle: {
                    color: '#52c41a'
                },
                yAxisIndex: 1,
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [{
                            offset: 0,
                            color: 'rgba(82, 196, 26, 0.3)'
                        }, {
                            offset: 1,
                            color: 'rgba(82, 196, 26, 0.1)'
                        }]
                    }
                }
            }
        ]
    };

    cashValueChart.setOption(option);
    loading.value = false;
};

// 监听窗口大小变化，重绘图表
const handleResize = () => {
    if (chart) {
        chart.resize();
    }
    if (cashValueChart) {
        cashValueChart.resize();
    }
};

// 组件挂载时初始化
onMounted(() => {
    // 初始化图表
    nextTick(() => {
        initChart();
        initCashValueChart();
    });

    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize);
});

// 组件卸载前清理
onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
    if (chart) {
        chart.dispose();
    }
    if (cashValueChart) {
        cashValueChart.dispose();
    }
});

// 监听计算结果变化，重新渲染图表
watch(() => props.calculationResult, () => {
    nextTick(() => {
        initChart();
        initCashValueChart();
    });
}, { deep: true });
</script>