<template>
    <a-modal :open="visible" title="选择产品" width="800px" :footer="null" @cancel="handleCancel">
        <!-- 筛选区域 -->
        <div class="filter-section mb-4">
            <div class="grid grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">名称</label>
                    <a-input-search v-model:value="searchValue" placeholder="搜索产品名称" enter-button @search="handleSearch"
                        class="w-full" />
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">险种</label>
                    <a-select v-model:value="filters.mainType" placeholder="请选择产品类型" class="w-full"
                        @change="handleFilterChange">
                        <a-select-option v-for="type in productTypes" :key="type.value" :value="type.value">
                            {{ type.label }}
                        </a-select-option>
                    </a-select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">地区-保司</label>
                    <a-cascader v-model:value="filters.region" :options="cascaderOptions" placeholder="请选择地区和保司"
                        @change="handleFilterChange" class="w-full" :show-search="true"
                        :filter-option="filterCascaderOption" style="width: 100%" />
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">货币</label>
                    <a-select v-model:value="filters.currency" placeholder="请选择货币" @change="handleFilterChange"
                        class="w-full">
                        <a-select-option v-for="currency in currencyList" :key="currency.value" :value="currency.value">
                            {{ currency.label }}
                        </a-select-option>
                    </a-select>
                </div>
            </div>
        </div>

        <!-- 产品列表 -->
        <div class="product-list">
            <a-table :columns="columns" :data-source="productList" :pagination="pagination" :loading="loading"
                row-key="productId" @change="handleTableChange" :scroll="{ y: 400 }" :draggable="false"
                :show-sorter="false" :custom-row="() => ({})">

            </a-table>
        </div>
    </a-modal>
</template>

<script setup>
import { ref, reactive, watch, onMounted, computed, h } from 'vue';
import { message } from 'ant-design-vue';
import { productAPI } from '@/api';
import { useProductStore } from '@/store/modules/product';
import { toTraditional } from '@/utils/chineseConverter.js';
const { getIRRCurrencyList } = productAPI;
// 初始化产品store
const productStore = useProductStore();
const currencyList = ref([]);
const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    productTypes: {
        type: Array,
        default: () => []
    }
});
const searchValue = ref('');
const emit = defineEmits(['update:visible', 'select']);

// 响应式数据
const loading = ref(false);
const productList = ref([]);

// 筛选条件
const filters = reactive({
    mainType: null,
    region: ['香港'],
    currency: undefined
});

// 公司选项
const companyOptions = ref([]);
const fetchCompanyList = async () => {
    let res = await productStore.getCompanyListOrigin();
    companyOptions.value = res.map(item => ({
        value: item.name,
        label: item.name,
        region: item.region
    }));
};

// 地区选项
const regionOptions = [
    { label: '香港', value: '香港' },
    { label: '澳门', value: '澳门' },
    { label: '新加坡', value: '新加坡' },
    { label: '百慕大', value: '百慕大' }
];

// 货币选项
const currencyOptions = [
    { label: 'HKD', value: 'HKD' },
    { label: 'USD', value: 'USD' },
    { label: 'CNY', value: 'CNY' },
    { label: 'SGD', value: 'SGD' }
];

// 级联选择器选项
const cascaderOptions = computed(() => {
    // 构建级联选择器的数据结构
    return regionOptions.map(region => {
        // 筛选该地区的公司
        const regionCompanies = companyOptions.value
            .filter(company => company.region === region.value)
            .map(company => ({
                value: company.value,
                label: company.label
            }));

        // 如果没有公司数据，添加一个默认的"所有公司"选项
        if (regionCompanies.length === 0) {
            regionCompanies.push({
                value: '',
                label: '所有公司'
            });
        }

        return {
            value: region.value,
            label: region.label,
            children: regionCompanies
        };
    });
});
const handleSearch = () => {
    console.log('搜索产品名称:', searchValue.value);
    pagination.current = 1;
    fetchProductList();
};

// 级联选择器过滤选项
const filterCascaderOption = (inputValue, path) => {
    return path.some(option => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0);
};

// 分页配置
const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条记录`
});

// 表格列配置
const columns = [
    {
        title: '产品名称',
        dataIndex: 'name',
        key: 'name',
        width: 300,
        ellipsis: true,
        customRender: ({ record }) => {
            return h(
                'div',
                { class: 'product-name' },
                [h('div', { class: 'font-medium' }, record.name), h('div', { class: 'text-xs text-gray-500' }, record.companyName)]
            );
        }
    },
    {
        title: '险种',
        dataIndex: 'mainType',
        key: 'mainType',
        width: 120,
        customRender: ({ text }) => {
            return h('span', {}, text || '--');
        }
    },
    {
        title: '货币',
        dataIndex: 'currency',
        key: 'currency',
        width: 100,
        customRender: ({ text }) => {
            return text ? h('span', {}, text) : h('span', {}, '--');
        }
    },
    {
        title: '地区',
        dataIndex: 'region',
        key: 'region',
        width: 100,
        customRender: ({ text }) => {
            return text ? h('span', {}, text) : h('span', {}, '--');
        }
    },
    {
        title: '操作',
        key: 'action',
        width: 80,
        fixed: 'right',
        customRender: ({ record }) => {
            return h(
                'button',
                {
                    type: 'button',
                    class: 'ant-btn ant-btn-primary ant-btn-sm',
                    onClick: () => handleSelectProduct(record)
                },
                '选择'
            );
        }
    }
];

const getCurrencyList = async () => {
    let res = await getIRRCurrencyList();
    currencyList.value = res.map(item => ({
        value: item,
        label: item

    }));
};

// 处理筛选条件变化
const handleFilterChange = () => {
    console.log('筛选条件变化:', filters);
    pagination.current = 1;
    fetchProductList();
};

// 获取产品列表
const fetchProductList = async () => {
    try {
        loading.value = true;

        // 解析级联选择器的值
        const region = filters.region && filters.region.length > 0 ? filters.region[0] : '香港';
        const company = filters.region && filters.region.length > 1 ? filters.region[1] : undefined;

        const params = {
            search: searchValue.value,
            pageNum: pagination.current,
            pageSize: pagination.pageSize,
            type: filters.mainType,
            insurer: company ? toTraditional(company) : undefined,
            region: region,
            currency: filters.currency,
            age: 0
        };

        setTimeout(() => {
            console.log('请求参数:', params);
        }, 1000);

        const response = await productAPI.getIrrProductList(params);

        if (response && response.records) {
            productList.value = response.records;
            pagination.total = response.total;
        } else {
            productList.value = [];
            pagination.total = 0;
        }
    } catch (error) {
        console.error('获取产品列表失败:', error);
        message.error('获取产品列表失败，请稍后重试');
        productList.value = [];
        pagination.total = 0;
    } finally {
        loading.value = false;
    }
};

// 表格变化处理
const handleTableChange = (pag) => {
    pagination.current = pag.current;
    pagination.pageSize = pag.pageSize;
    fetchProductList();
};

// 选择产品
const handleSelectProduct = (product) => {
    // 解析级联选择器的值
    const region = filters.region && filters.region.length > 0 ? filters.region[0] : '香港';
    const company = filters.region && filters.region.length > 1 ? filters.region[1] : undefined;

    // 将筛选条件一并传递回父组件
    emit('select', {
        ...product,
        selectedFilters: {
            mainType: filters.mainType,
            company: company,
            region: region,
            currency: filters.currency
        }
    });
    handleCancel();
};

// 取消
const handleCancel = () => {
    emit('update:visible', false);
};

// 监听visible变化
watch(() => props.visible, (newVal) => {
    if (newVal) {
        // 重置分页
        pagination.current = 1;
        pagination.pageSize = 10;

        // 确保region是数组格式
        if (!Array.isArray(filters.region)) {
            filters.region = [filters.region];
        }

        // 获取产品列表
        fetchProductList();
    }
});

// 组件挂载后执行
onMounted(() => {
    // 获取公司列表
    fetchCompanyList();
    getCurrencyList();
});
</script>

<style scoped>
.product-name {
    line-height: 1.4;
}

:deep(.ant-table-tbody > tr > td) {
    padding: 12px 16px;
}

:deep(.ant-table-thead > tr > th) {
    background: #fafafa;
    font-weight: 600;
}

.filter-section {
    background-color: #f9fafb;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 16px;
}
</style>
