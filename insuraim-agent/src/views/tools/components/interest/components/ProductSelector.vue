<template>
    <div>
        <div class="product-selector-container">
            <!-- 步骤指示器 -->
            <div class="steps-container mb-3">
                <a-steps :current="currentStep" size="small">
                    <a-step title="选择产品" />
                    <a-step title="选择详细计划" />
                    <a-step title="填写客户信息" />
                </a-steps>
            </div>

            <!-- 步骤1：选择父产品 -->
            <div v-if="currentStep === 0" class="step-content">
                <!-- 搜索和筛选区域 -->
                <div class="search-filter-area mb-3">
                    <!-- 产品名称搜索 -->

                    <!-- 筛选条件行 -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-2 mb-2">
                        <div class="mb-2">
                            <a-input-search v-model:value="searchValue" placeholder="搜索产品名称" @search="handleSearch"
                                allow-clear class="w-full" />
                        </div>

                        <!-- 产品类型选择 -->
                        <a-select v-model:value="selectedProductType" placeholder="选择产品类型"
                            @change="handleProductTypeChange" :loading="loadingProductTypes" allow-clear class="w-full">
                            <a-select-option value="">所有类型</a-select-option>
                            <a-select-option v-for="type in productTypes" :key="type.value" :value="type.value">
                                {{ type.label }}
                            </a-select-option>
                        </a-select>

                        <!-- 地区-保司级联选择 -->
                        <a-cascader v-model:value="selectedRegionCompany" :options="cascaderOptions"
                            placeholder="选择地区和保司" @change="handleRegionCompanyChange" :loading="loadingCompanies"
                            :show-search="true" :filter-option="filterCascaderOption" allow-clear class="w-full" />
                    </div>

                    <!-- 当前筛选条件标签和操作按钮 -->
                    <div class="flex flex-wrap items-center justify-between">
                        <!-- 筛选条件标签 -->
                        <div class="filter-tags flex-1 flex flex-wrap items-center">
                            <template v-if="selectedProductType || selectedRegion || searchValue">
                                <span class="text-xs text-gray-500 mr-2">筛选条件:</span>

                                <a-tag v-if="searchValue" closable @close="searchValue = ''; handleSearch()"
                                    color="blue" class="mr-1 mb-1">
                                    关键词: {{ searchValue }}
                                </a-tag>

                                <a-tag v-if="selectedProductType" closable @close="handleProductTypeChange('')"
                                    color="green" class="mr-1 mb-1">
                                    类型: {{productTypes.find(t => t.value === selectedProductType)?.label ||
                                        selectedProductType}}
                                </a-tag>

                                <a-tag v-if="selectedRegion" closable
                                    @close="selectedRegionCompany = []; handleRegionCompanyChange([]);" color="orange"
                                    class="mr-1 mb-1">
                                    地区: {{ selectedRegion }}
                                </a-tag>

                                <a-tag v-if="selectedCompany" closable
                                    @close="selectedRegionCompany = [selectedRegion]; handleRegionCompanyChange([selectedRegion]);"
                                    color="purple" class="mr-1 mb-1">
                                    保司: {{ selectedCompany }}
                                </a-tag>

                                <a-button type="link" size="small" @click="clearAllFilters" class="text-xs">
                                    清除全部
                                </a-button>
                            </template>
                            <span v-else class="text-xs text-gray-400">未设置筛选条件</span>
                        </div>

                        <!-- 刷新按钮 -->
                        <a-button type="primary" @click="refreshProducts" :loading="loading" size="small">
                            刷新
                        </a-button>
                    </div>
                </div>

                <div class="product-list">
                    <a-spin :spinning="loading">
                        <div v-if="filteredProducts.length === 0" class="empty-state">
                            <a-empty description="暂无产品" />
                            <div class="text-center mt-2">
                                <p class="text-gray-500 text-sm">
                                    <span
                                        v-if="selectedProductType || selectedRegion || selectedCompany || searchValue">
                                        没有符合当前筛选条件的产品，请尝试调整筛选条件
                                    </span>
                                    <span v-else>
                                        暂无产品数据，请尝试刷新或联系管理员
                                    </span>
                                </p>
                                <a-button type="link" @click="clearAllFilters"
                                    v-if="selectedProductType || selectedRegion || selectedCompany || searchValue">
                                    清除筛选条件
                                </a-button>
                            </div>
                        </div>
                        <div v-else class="grid grid-cols-2 gap-3">
                            <div v-for="product in filteredProducts" :key="product.id" class="product-card"
                                :class="{ 'selected': selectedProduct && selectedProduct.id === product.id }"
                                @click="selectProduct(product)">
                                <div class="flex items-start">
                                    <div class="company-logo mr-2">
                                        <img v-if="product.logoUrl" :src="product.logoUrl" alt="公司logo"
                                            class="h-6 w-6 object-contain" />
                                        <div v-else
                                            class="logo-placeholder h-6 w-6 flex items-center justify-center bg-gray-100 rounded-full">
                                            <span class="text-xs">{{ product.companyName?.substring(0, 2) || '无'
                                            }}</span>
                                        </div>
                                    </div>
                                    <div class="product-info flex-1 min-w-0">
                                        <div class="product-name font-medium truncate">{{ product.productName }}</div>
                                        <div class="flex items-center justify-between mt-1">
                                            <div class="product-company text-xs text-gray-500">{{ product.companyName }}
                                            </div>
                                            <div
                                                class="product-type text-xs px-2 py-0.5 bg-blue-50 text-blue-600 rounded-full">
                                                {{ product.categoryName }}</div>
                                        </div>
                                        <div class="product-details mt-1 text-xs text-gray-500 flex flex-wrap">
                                            <span class="mr-2">{{ product.region }}</span>
                                            <span class="mr-2">保障期: {{ product.guaranteePeriod }}</span>
                                            <span>年龄: {{ product.ageRange?.min || 0 }}-{{ product.ageRange?.max || 0
                                            }}岁</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="pagination mt-4 flex justify-center">
                            <a-button @click="loadMoreProducts" type="primary" :loading="loadingMore"
                                v-if="filteredProducts.length < totalProducts">
                                加载更多
                            </a-button>
                            <p v-else class="text-gray-500 text-sm">没有更多了～</p>
                        </div>
                    </a-spin>
                </div>
            </div>

            <!-- 步骤2：选择子产品 -->
            <div v-if="currentStep === 1" class="step-content">
                <div class="selected-product-info mb-2 p-2 bg-gray-50 rounded-md">
                    <div class="font-medium text-sm">已选父产品: {{ selectedProduct.productName }}</div>
                    <div class="text-xs text-gray-500">{{ selectedProduct.companyName }} | {{
                        selectedProduct.categoryName }}</div>
                </div>

                <div class="sub-product-list">
                    <a-spin :spinning="loading">
                        <div v-if="!selectedProduct.subProducts || selectedProduct.subProducts.length === 0"
                            class="empty-state">
                            <a-empty description="暂无子产品" />
                            <div class="text-center mt-2">
                                <p class="text-gray-500 text-xs">当前父产品下没有可选的子产品</p>
                            </div>
                        </div>
                        <div v-else class="grid grid-cols-2 gap-3">

                            <div v-for="subProduct in selectedProduct.subProducts.filter(item => item.hasCashValue === 1 || item.hasCashValue)"
                                :key="subProduct.id" class="product-card"
                                :class="{ 'selected': selectedSubProduct && selectedSubProduct.id === subProduct.id }"
                                @click="selectSubProduct(subProduct)">
                                <div class="product-info">
                                    <div class="product-name font-medium truncate">{{ subProduct.productName }}</div>
                                    <div class="product-details text-xs text-gray-500 mt-1">
                                        <div class="flex justify-between items-center">
                                            <span
                                                class="currency-badge bg-blue-50 text-blue-600 px-2 py-0.5 rounded-full">{{
                                                    subProduct.currency }}</span>
                                            <span class="payment-term text-xs">{{ subProduct.paymentTerm }}年缴</span>

                                        </div>
                                    </div>
                                    <div class="product-details mt-1 text-xs text-gray-500 flex flex-wrap">
                                        <span class="mr-2">年龄: {{ subProduct.ageRange?.min || '' }}-{{
                                            subProduct.ageRange?.max || '' }}</span>
                                        <span>保费: {{ formatPremiumRange(subProduct.premiumRange) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </a-spin>
                </div>
            </div>

            <!-- 步骤3：填写客户信息 -->
            <div v-if="currentStep === 2" class="step-content">
                <!-- 模板选择区域 -->
                <div class="template-selector mb-4">
                    <div class="flex justify-between items-center mb-2">
                        <h3 class="text-base font-medium">选择预设方案</h3>
                        <!-- <a-switch v-model:checked="showTemplates" checked-children="显示模板" un-checked-children="隐藏模板" /> -->
                    </div>

                    <div v-if="showTemplates" class="template-cards">
                        <a-row :gutter="[16, 16]">
                            <a-col :span="24">
                                <div class="template-scroll-container">
                                    <div class="template-card-wrapper" v-for="template in templates" :key="template.id">
                                        <div class="template-card"
                                            :class="{ 'template-card-selected': selectedTemplateId === template.id }"
                                            :style="{ borderColor: selectedTemplateId === template.id ? template.color : '' }"
                                            @click="applyTemplate(template.id)">
                                            <div class="template-card-header"
                                                :style="{ backgroundColor: template.color }">
                                                <span class="template-card-icon">
                                                    <component :is="template.icon"
                                                        style="color: white; font-size: 16px;" />
                                                </span>
                                                <span class="template-card-name">{{ template.name }}</span>
                                            </div>
                                            <div class="template-card-body">
                                                <p class="template-card-desc">{{ template.description }}</p>
                                                <div class="template-card-details">
                                                    <div class="template-card-detail">
                                                        <span class="detail-label">年龄:</span>
                                                        <span class="detail-value">{{ template.values.startAge
                                                            }}岁</span>
                                                    </div>
                                                    <div class="template-card-detail">
                                                        <span class="detail-label">保费:</span>
                                                        <span class="detail-value">{{
                                                            formatNumber(template.values.precisePremium, {
                                                                currency: selectedSubProduct.currency
                                                            })
                                                        }} </span>
                                                    </div>
                                                    <div class="template-card-detail">
                                                        <span class="detail-label">类型:</span>
                                                        <span class="detail-value">{{ template.values.type ===
                                                            'standard' ? '累积生息' :
                                                            '现金提取' }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </a-col>
                        </a-row>
                    </div>
                </div>

                <div class="customer-info-container bg-white rounded-lg relative">
                    <!-- <h3 class="text-lg font-semibold mb-3">客户信息</h3> -->

                    <!-- 客户基本信息 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">客户姓名</label>
                            <a-input v-model:value="customerInfo.customerName" class="w-full" placeholder="请输入客户姓名" />
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">客户年龄</label>
                            <a-input-number v-model:value="customerInfo.startAge" :min="ageRange.min"
                                :max="ageRange.max" class="w-full" placeholder="请输入客户年龄" />
                            <div v-if="ageRange.min !== 0 || ageRange.max !== 100" class="text-xs text-gray-500 mt-1">
                                年龄范围: {{ ageRange.min }}-{{ ageRange.max }}岁
                            </div>
                        </div>
                    </div>

                    <!-- 缴费信息 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">缴纳保费</label>
                            <a-input-number v-model:value="customerInfo.precisePremium" :min="premiumRange[0] || 0"
                                :max="premiumRange[1] || Number.MAX_SAFE_INTEGER" :precision="2" class="w-full"
                                placeholder="请输入年缴保费" :formatter="formatAmount" :parser="parseAmount" />
                            <div v-if="premiumRange && premiumRange.length === 2" class="text-xs text-gray-500 mt-1">
                                保费范围: {{ formatNumber(premiumRange[0], {
                                    currency: selectedSubProduct.currency
                                }) }}-{{ formatNumber(premiumRange[1], {
                                    currency: selectedSubProduct.currency
                                }) }}
                            </div>
                        </div>
                        <!-- <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">预缴年份</label>
                            <a-select v-model:value="customerInfo.selectedPrepaymentYear" class="w-full"
                                placeholder="请选择缴费年期">
                                <a-select-option value="不适用">不适用</a-select-option>
                                <a-select-option v-for="year in prepaymentYears" :key="year" :value="year">
                                    {{ year }}年缴
                                </a-select-option>
                            </a-select>
                        </div> -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">缴费方式</label>
                            <a-select v-model:value="customerInfo.selectedPaymentMethod" class="w-full"
                                placeholder="请选择缴费方式">
                                <a-select-option v-for="method in paymentMethods" :key="method" :value="method">
                                    {{ getPaymentMethodName(method) }}
                                </a-select-option>
                            </a-select>
                        </div>
                    </div>

                    <!-- 缴费方式和持有期 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">

                        <div>
                            <a-tooltip title="暂未开放">
                                <label class="block text-sm font-medium text-gray-700 mb-1">持有期</label>
                                <a-input-number v-model:value="customerInfo.holdingYears" :min="1" class="w-full"
                                    placeholder="请输入持有年数" :disabled="true" />
                            </a-tooltip>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">计算方式</label>
                            <a-select v-model:value="customerInfo.type" placeholder="请选择" :options="props.typeOptions"
                                class="w-full" size="middle">
                            </a-select>
                        </div>
                    </div>



                    <!-- 优惠管理 -->
                    <div class="mt-4 w-full">
                        <DiscountManager :discounts="customerInfo.discounts"
                            :maxPaymentYears="customerInfo.selectedPrepaymentYear || selectedSubProduct.paymentTerm"
                            @add="addDiscount" @remove="removeDiscount" @update="updateDiscount" />
                    </div>
                </div>
            </div>

            <!-- 步骤导航按钮 -->
            <div class="step-actions mt-3 flex justify-end gap-4">
                <a-button type="default" @click="resetToParentStep" size="large" v-if="currentStep === 2"
                    :loading="calculating">
                    重置
                </a-button>
                <a-button v-if="currentStep > 0" @click="prevStep" size="large">
                    上一步
                </a-button>
                <div v-else></div>

                <a-button v-if="currentStep < 2" type="primary" @click="nextStep" :disabled="!canProceed" size="large">
                    下一步
                </a-button>
                <a-button v-else type="primary" @click="confirmSelection" :disabled="!canProceed" size="large"
                    :loading="calculating">
                    计算收益
                </a-button>




            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick, reactive } from 'vue';
import { message } from 'ant-design-vue';
import { productAPI, companyAPI } from '@/api';
import { toTraditional } from '@/utils/chineseConverter';
import { formatNumber } from '@/utils/number';

// 引入DiscountManager组件
import DiscountManager from '@/views/tools/components/interest/components/DiscountManager.vue';
// 引入图标组件
import {
    UserOutlined,
    SafetyOutlined,
    CrownOutlined,
    TeamOutlined,
    CoffeeOutlined,
    BulbOutlined
} from '@ant-design/icons-vue';

const props = defineProps({
    modelValue: {
        type: Object,
        default: () => ({})
    },
    // 新增typeOptions属性
    typeOptions: {
        type: Array,
        default: () => [
            { value: 'standard', label: '累积生息' },
            { value: 'non_interest', label: '现金提取' }
        ]
    }
});

const emit = defineEmits(['update:modelValue', 'select', 'ageRange', 'confirm', 'step-change', 'complete-status-change']);

// 步骤控制
const currentStep = ref(0);
const canProceed = computed(() => {
    switch (currentStep.value) {
        case 0: return !!selectedProduct.value;
        case 1: return !!selectedSubProduct.value;
        case 2: return true
    }
});

// 是否已完成所有步骤
const isComplete = computed(() => {
    return currentStep.value === 2 && canProceed.value;
});



// 产品列表数据
const loading = ref(false);
const loadingMore = ref(false);
const products = ref([]);
const filteredProducts = ref([]);
const searchValue = ref('');
const currentPage = ref(1);
const pageSize = ref(20);
const totalProducts = ref(0);

// 产品类型选择相关
const productTypes = ref([]);
const selectedProductType = ref('');
const loadingProductTypes = ref(false);

// 地区-保司级联选择相关
const companyList = ref([]);
const loadingCompanies = ref(false);
const selectedRegionCompany = ref([]);
const selectedRegion = ref('');
const selectedCompany = ref('');

// 地区选项
const regionOptions = [
    { code: 'HK', name: '香港', value: '香港', label: '香港' },
    { code: 'MO', name: '澳门', value: '澳门', label: '澳门' },
    { code: 'SG', name: '新加坡', value: '新加坡', label: '新加坡' },
    { code: 'BM', name: '百慕大', value: '百慕大', label: '百慕大' }
];

// 选中的值
const selectedProduct = ref(null);
const selectedSubProduct = ref(null);

// 模板相关数据
const templates = ref([]);
const selectedTemplateId = ref(null);
const showTemplates = ref(true); // 控制模板选择器的显示/隐藏

// 客户信息数据
const customerInfo = reactive({
    customerName: '',
    startAge: null,
    precisePremium: null,
    selectedPrepaymentYear: null,
    selectedPaymentMethod: null,
    holdingYears: null,
    type: 'standard',
    discounts: []
});

// const reset = () => {
//     currentStep.value = 0;
//     selectedProduct.value = null;
//     selectedSubProduct.value = null;
//     resetForm();
// };
const resetForm = () => {
    reset();
    customerInfo.customerName = '';
    customerInfo.startAge = null;
    customerInfo.precisePremium = null;
    customerInfo.selectedPrepaymentYear = null;
    customerInfo.selectedPaymentMethod = null;
};
// 更新选择数据到父组件的函数
const updateSelectionData = () => {
    if (!selectedProduct.value || !selectedSubProduct.value) {
        return;
    }

    const selection = {
        // 产品信息
        product: selectedProduct.value,
        subProduct: selectedSubProduct.value,
        currency: selectedSubProduct.value.currency,
        paymentTerm: selectedSubProduct.value.paymentTerm,
        paymentMethod: customerInfo.selectedPaymentMethod || (selectedSubProduct.value.paymentMethods ? selectedSubProduct.value.paymentMethods[0] : null),
        ageRange: selectedSubProduct.value.ageRange || selectedProduct.value.ageRange,
        productId: selectedSubProduct.value.id, // 使用子产品ID作为productId

        // 客户信息
        customerName: customerInfo.customerName,
        startAge: customerInfo.startAge,
        precisePremium: customerInfo.precisePremium,
        selectedPrepaymentYear: customerInfo.selectedPrepaymentYear,
        selectedPaymentMethod: customerInfo.selectedPaymentMethod,
        holdingYears: customerInfo.holdingYears,
        type: customerInfo.type,
        discounts: customerInfo.discounts
    };

    emit('update:modelValue', selection);

    // 只在所有字段都有效时才发送select事件
    if (canProceed.value) {
        emit('select', selection);
    }
};

// 监听客户信息变化，实时更新和验证
watch([
    () => customerInfo.customerName,
    () => customerInfo.startAge,
    () => customerInfo.precisePremium,
    () => customerInfo.holdingYears,
    () => customerInfo.selectedPaymentMethod,
    () => customerInfo.selectedPrepaymentYear,
    () => customerInfo.type,
    () => customerInfo.discounts
], () => {
    // 只在第三步时自动更新
    if (currentStep.value === 2 && selectedProduct.value && selectedSubProduct.value) {
        // 自动更新数据到父组件
        updateSelectionData();
    }
}, { deep: true });

// 保费范围
const premiumRange = ref([]);

// 年龄范围
const ageRange = ref({
    min: 0,
    max: 100
});

// 支持的缴费方式和年期
const paymentMethods = ref([]);
const prepaymentYears = ref([]);

// 级联选择器选项
const cascaderOptions = computed(() => {
    // 构建级联选择器的数据结构
    return regionOptions.map(region => {
        // 筛选该地区的公司
        const regionCompanies = companyList.value
            .filter(company => company.region === region.value)
            .map(company => ({
                value: company.value,
                label: company.label
            }));

        // 如果没有公司数据，添加一个默认的"所有公司"选项
        if (regionCompanies.length === 0) {
            regionCompanies.push({
                value: '',
                label: '所有公司'
            });
        }

        return {
            value: region.value,
            label: region.label,
            children: regionCompanies
        };
    });
});

// 级联选择器过滤选项
const filterCascaderOption = (inputValue, path) => {
    return path.some(option => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0);
};

// 获取产品类型列表
const getProductTypes = async () => {
    loadingProductTypes.value = true;
    try {
        // 根据实际API修改，这里假设使用getChinaLifeProductCalculationType API
        const res = await productAPI.getChinaLifeProductCalculationType();
        if (res && Array.isArray(res)) {
            productTypes.value = res.map(item => ({
                value: item.code,
                label: item.name,
                code: item.code
            }));
        } else {
            console.error('获取产品类型列表失败，返回格式不正确');
        }
    } catch (error) {
        console.error('获取产品类型列表失败:', error);
    } finally {
        loadingProductTypes.value = false;
    }
};

// 获取保险公司列表
const getCompanyList = async () => {
    loadingCompanies.value = true;
    try {
        // 根据实际API修改，这里假设使用getCompanyList API
        const res = await companyAPI.getCompanyPage();
        if (res && Array.isArray(res)) {
            companyList.value = res.map(item => ({
                value: item.name || item.companyName,
                label: item.name || item.companyName,
                region: item.region || '香港' // 设置默认地区为香港
            }));
        } else {
            console.error('获取公司列表失败，返回格式不正确');
        }
    } catch (error) {
        console.error('获取公司列表失败:', error);
    } finally {
        loadingCompanies.value = false;
    }
};

// 获取产品列表
const fetchProducts = async (isLoadMore = false) => {
    if (isLoadMore) {
        loadingMore.value = true;
    } else {
        loading.value = true;
        // 如果不是加载更多，则重置产品列表
        if (!isLoadMore) {
            products.value = [];
            filteredProducts.value = [];
        }
    }
    try {
        const companyNameTranditon = toTraditional(selectedCompany.value)
        // 构建查询参数
        const params = {
            pageNum: currentPage.value,
            pageSize: pageSize.value,
            productName: searchValue.value || undefined,
            categoryCode: selectedProductType.value || undefined,
            region: selectedRegion.value || undefined,
            companyName: companyNameTranditon || undefined,
            isCashValue: 1
        };
        console.log('selectedCompany.value',);
        console.log('查询产品参数:', params);

        // 调用API获取产品列表
        const res = await productAPI.getProductList(params);
        if (res && res.records) {
            // 处理返回的数据
            const newProducts = res.records;

            if (isLoadMore) {
                // 加载更多时，追加数据
                products.value = [...products.value, ...newProducts];
                filteredProducts.value = [...filteredProducts.value, ...newProducts];
            } else {
                // 初次加载或刷新时，替换数据
                products.value = newProducts;
                filteredProducts.value = newProducts;
            }
            totalProducts.value = res.total;
        } else {
            message.error('获取产品列表失败，请稍后重试');
        }
    } catch (error) {
        console.error('获取产品列表失败:', error);
        message.error('获取产品列表失败，请稍后重试');
    } finally {
        loading.value = false;
        loadingMore.value = false;
    }
};

// 加载更多产品
const loadMoreProducts = () => {
    // 如果已经加载完所有数据，则不再加载
    if (filteredProducts.value.length >= totalProducts.value) {
        message.info('已加载全部产品');
        return;
    }

    // 页码加1，继续加载
    currentPage.value += 1;
    fetchProducts(true);
};

// 搜索处理
const handleSearch = () => {
    currentPage.value = 1;
    fetchProducts();
};

// 产品类型变更处理
const handleProductTypeChange = (value) => {
    console.log('产品类型变更:', value);
    selectedProductType.value = value;
    // 重置产品选择
    selectedProduct.value = null;
    selectedSubProduct.value = null;
    // 重置分页
    currentPage.value = 1;
    // 重新获取产品列表
    fetchProducts();
};

// 地区-保司变更处理
const handleRegionCompanyChange = (value) => {
    console.log('地区-保司变更:', value);

    // 解析级联选择器的值
    const region = value && value.length > 0 ? value[0] : '';
    const company = value && value.length > 1 ? value[1] : '';

    // 更新内部状态
    selectedRegion.value = region;
    selectedCompany.value = company;

    // 重置产品选择
    selectedProduct.value = null;
    selectedSubProduct.value = null;
    // 重置分页
    currentPage.value = 1;
    // 重新获取产品列表
    fetchProducts();
};

// 清除所有筛选条件
const clearAllFilters = () => {
    // 重置所有筛选条件
    searchValue.value = '';
    selectedProductType.value = '';
    selectedRegionCompany.value = [];
    selectedRegion.value = '';
    selectedCompany.value = '';

    // 重置产品选择
    selectedProduct.value = null;
    selectedSubProduct.value = null;
    // 重置分页
    currentPage.value = 1;
    // 重新获取产品列表
    fetchProducts();
};

// 解析年龄范围，处理包含"天"或"岁"的字符串
const parseAgeRange = (ageStr) => {
    if (!ageStr || typeof ageStr !== 'string') return 0;

    // 处理数字类型，直接返回
    if (!isNaN(ageStr)) return Number(ageStr);

    // 移除所有空格
    const trimmedStr = ageStr.trim();

    // 提取数字部分
    const numMatch = trimmedStr.match(/\d+/);
    if (!numMatch) return 0;

    const num = parseInt(numMatch[0]);

    // 检查是否包含"天"，如果是，则转换为年龄（假设365天=1岁）
    if (trimmedStr.includes('天') || trimmedStr.includes('日') ||
        trimmedStr.includes('day') || trimmedStr.includes('days')) {
        return Math.max(0, Math.floor(num / 365));
    }

    // 其他情况（如包含"岁"、"歲"等），直接返回数字部分
    return num;
};

const handleAgeRange = (range) => {
    if (range) {
        // 处理字符串格式的年龄范围（如"15天"、"80岁"）
        const minAge = typeof range.min === 'string' ? parseAgeRange(range.min) : range.min;
        const maxAge = typeof range.max === 'string' ? parseAgeRange(range.max) : range.max;

        // 更新年龄范围
        ageRange.value = {
            min: minAge,
            max: maxAge
        };


    }
};
// 修改选择产品和子产品函数，使其调用handleAgeRange
const selectProduct = (product) => {
    selectedProduct.value = product;
    selectedSubProduct.value = null;

    // 触发年龄范围事件并本地处理
    if (product.ageRange) {
        emit('ageRange', product.ageRange);
        handleAgeRange(product.ageRange);
    }
};

// 选择子产品
const selectSubProduct = (subProduct) => {
    selectedSubProduct.value = subProduct;
    console.log('subProduct', subProduct);
    // 如果子产品有自己的年龄范围，则触发年龄范围事件并本地处理
    if (subProduct.ageRange) {
        emit('ageRange', subProduct.ageRange);
        handleAgeRange(subProduct.ageRange);
    }

    // 更新保费范围
    if (subProduct.premiumRange && Array.isArray(subProduct.premiumRange)) {
        premiumRange.value = subProduct.premiumRange.map(val => parseFloat(val));
    } else {
        premiumRange.value = [1, Number.MAX_SAFE_INTEGER]; // 默认无限制
    }

    // 更新缴费方式选项
    if (subProduct.paymentMethods && Array.isArray(subProduct.paymentMethods)) {
        paymentMethods.value = subProduct.paymentMethods;
        customerInfo.selectedPaymentMethod = subProduct.paymentMethods[0];
    } else {
        paymentMethods.value = ['ANNUALLY']; // 默认提供年缴选项
        customerInfo.selectedPaymentMethod = 'ANNUALLY';
    }

    // 更新缴费年期选项
    if (subProduct.prepaymentYears && Array.isArray(subProduct.prepaymentYears)) {
        prepaymentYears.value = subProduct.prepaymentYears;

        // 默认选择当前的paymentTerm，如果在列表中的话
        const currentPaymentTerm = parseInt(subProduct.paymentTerm);
        if (prepaymentYears.value.includes(currentPaymentTerm)) {
            customerInfo.selectedPrepaymentYear = currentPaymentTerm;
        } else if (prepaymentYears.value.length > 0) {
            customerInfo.selectedPrepaymentYear = prepaymentYears.value[0];
        }
    } else {
        // 如果没有缴费年期选项，默认使用当前paymentTerm
        prepaymentYears.value = [parseInt(subProduct.paymentTerm)];
        customerInfo.selectedPrepaymentYear = parseInt(subProduct.paymentTerm);
    }

    // 设置默认持有期
    // if (!customerInfo.holdingYears) {
    //     customerInfo.holdingYears = 5; // 设置一个合理的默认值
    // }

    // 提前更新产品信息到父组件
    updateSelectionData();
};

// 下一步
const nextStep = () => {
    if (canProceed.value) {
        // 在从步骤1进入步骤2时，确保子产品已经选择并初始化相关表单字段
        if (currentStep.value === 1 && selectedSubProduct.value) {
            // 更新年龄范围
            if (selectedSubProduct.value.ageRange) {
                emit('ageRange', selectedSubProduct.value.ageRange);
                handleAgeRange(selectedSubProduct.value.ageRange);
            }

            // 确保保费范围、缴费方式和年期已设置
            if (selectedSubProduct.value.premiumRange && Array.isArray(selectedSubProduct.value.premiumRange)) {
                premiumRange.value = selectedSubProduct.value.premiumRange.map(val => parseFloat(val));
            }

            if (selectedSubProduct.value.paymentMethods && Array.isArray(selectedSubProduct.value.paymentMethods)) {
                paymentMethods.value = selectedSubProduct.value.paymentMethods;
                customerInfo.selectedPaymentMethod = selectedSubProduct.value.paymentMethods[0];
            }

            if (selectedSubProduct.value.prepaymentYears && Array.isArray(selectedSubProduct.value.prepaymentYears)) {
                prepaymentYears.value = selectedSubProduct.value.prepaymentYears;
                const currentPaymentTerm = parseInt(selectedSubProduct.value.paymentTerm);
                if (prepaymentYears.value.includes(currentPaymentTerm)) {
                    customerInfo.selectedPrepaymentYear = currentPaymentTerm;
                } else if (prepaymentYears.value.length > 0) {
                    customerInfo.selectedPrepaymentYear = prepaymentYears.value[0];
                }
            }

            // // 设置默认客户年龄（如果有年龄范围）
            // if (ageRange.value && !customerInfo.startAge) {
            //     customerInfo.startAge = ageRange.value.min;
            // }

            // 设置默认持有期
            if (!customerInfo.holdingYears) {
                customerInfo.holdingYears = 5; // 设置一个合理的默认值
            }
        }

        currentStep.value++;
        console.log('currentStep.value', currentStep.value);

        // 如果进入第三步，自动更新数据
        if (currentStep.value === 2) {
            console.log('currentStep.value', currentStep.value);

            updateSelectionData();

            // 生成模板并显示模板选择器
            templates.value = generateTemplates();
            showTemplates.value = true;

            // 通知父组件步骤已经到了最后一步
            emit('complete-status-change', true);
        }
    } else {
        // 根据当前步骤显示提示
        switch (currentStep.value) {
            case 0:
                message.warning('请先选择一个产品');
                break;
            case 1:
                message.warning('请先选择一个子产品');
                break;
            default:
                message.warning('请完成当前步骤的所有必填项');
        }
    }
};

// 上一步
const prevStep = () => {
    if (currentStep.value > 0) {
        currentStep.value--;
    }
};
const calculating = ref(false);
// 确认选择并计算
const confirmSelection = () => {
    calculating.value = true;
    if (!selectedProduct.value || !selectedSubProduct.value) {
        message.warning('请完成所有选择');
        calculating.value = false;
        return;
    }

    // 验证客户信息
    if (currentStep.value === 2) {
        // 验证客户姓名
        if (!customerInfo.customerName) {
            message.warning('请输入客户姓名');
            calculating.value = false;
            return;
        }
        console.log('customerInfo.startAge', customerInfo.startAge);

        // 验证客户年龄
        // if (customerInfo.startAge == '') {
        //     message.warning('请输入客户年龄');
        //     calculating.value = false;
        //     return;
        // }

        // 验证保费
        if (!customerInfo.precisePremium) {
            message.warning('请输入缴纳保费');
            calculating.value = false;
            return;
        }

        // 验证保费范围
        if (premiumRange.value && premiumRange.value.length === 2) {
            const minPremium = premiumRange.value[0];
            const maxPremium = premiumRange.value[1];

            if (customerInfo.precisePremium < minPremium || customerInfo.precisePremium > maxPremium) {
                message.error(`保费必须在允许范围内: ${formatAmount(minPremium)}-${formatAmount(maxPremium)}元`);
                calculating.value = false;
                return;
            }
        }

        // 验证持有期
        // if (!customerInfo.holdingYears) {
        //     message.warning('请输入持有期');
        //     calculating.value = false;
        //     return;
        // }

        // 验证缴费方式
        if (paymentMethods.value.length > 0 && !customerInfo.selectedPaymentMethod) {
            message.warning('请选择缴费方式');
            calculating.value = false;
            return;
        }
    }

    // 最终更新所有数据
    updateSelectionData();

    // 记录使用的模板信息（可选，用于数据分析）
    const templateInfo = selectedTemplateId.value ?
        templates.value.find(t => t.id === selectedTemplateId.value)?.name : '自定义填写';

    // 发送确认信号
    emit('confirm', {
        product: selectedProduct.value,
        subProduct: selectedSubProduct.value,
        customerInfo: { ...customerInfo },
        templateUsed: templateInfo // 添加模板使用信息
    });

    // 通知父组件，数据已准备好，可以计算了
    message.success('信息已确认，准备计算');
    calculating.value = false;
};

// 刷新产品列表
const refreshProducts = () => {
    console.log('手动刷新产品列表');
    currentPage.value = 1;
    fetchProducts();
};

// 初始化
onMounted(() => {
    console.log('ProductSelector组件已挂载，准备获取产品列表');
    // 加载产品类型列表
    getProductTypes();
    // 加载公司列表
    getCompanyList();
    // 加载产品列表
    fetchProducts();
});

// 监听filteredProducts变化
watch(filteredProducts, (newVal) => {
    console.log('filteredProducts已更新:', newVal.length, '条记录');
}, { deep: true });

// 监听当前步骤变化
watch(currentStep, (newVal) => {
    emit('step-change', newVal);
}, { immediate: true });

// 监听完成状态变化
watch(isComplete, (newVal) => {
    emit('complete-status-change', newVal);
}, { immediate: true });

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
    if (newVal && newVal.product) {
        selectedProduct.value = newVal.product;
        selectedSubProduct.value = newVal.subProduct;

        // 根据已有选择设置当前步骤
        // if (selectedSubProduct.value) currentStep.value = 1;
        // else currentStep.value = 0;
    } else if (newVal && Object.keys(newVal).length === 0) {
        // 当父组件传入空对象时，也重置组件状态
        // 这处理了resetToParentStep中emit('update:modelValue', {})的情况
        selectedProduct.value = null;
        selectedSubProduct.value = null;
        currentStep.value = 0;
    }
}, { deep: true, immediate: true });

// 恢复状态到选择父产品
const resetToParentStep = () => {
    console.log('恢复状态到选择父产品');
    currentStep.value = 0;

    // 重置产品选择
    selectedProduct.value = null;
    selectedSubProduct.value = null;

    // 重置客户信息
    customerInfo.customerName = '';
    customerInfo.startAge = null;
    customerInfo.precisePremium = null;
    customerInfo.selectedPrepaymentYear = null;
    customerInfo.selectedPaymentMethod = null;
    customerInfo.holdingYears = null;
    customerInfo.type = 'standard';
    customerInfo.discounts = [];

    // 重置模板相关数据
    templates.value = [];
    selectedTemplateId.value = null;
    showTemplates.value = true;

    // 重置其他相关数据
    premiumRange.value = [];
    paymentMethods.value = [];
    prepaymentYears.value = [];

    // 重置筛选条件
    searchValue.value = '';
    selectedProductType.value = '';
    selectedRegionCompany.value = [];
    selectedRegion.value = '';
    selectedCompany.value = '';

    // 通知父组件更新v-model绑定值，确保双向数据流的一致性
    emit('update:modelValue', {});

    // 重新加载产品列表
    currentPage.value = 1;
    refreshProducts();
};

// 立即调用API测试
(async () => {
    console.log('ProductSelector组件创建后立即测试API...');
    try {
        const testParams = { current: 1, size: 5 };
        const testRes = await productAPI.getProductList(testParams);
        console.log('API测试结果:', testRes);
    } catch (error) {
        console.error('API测试失败:', error);
    }
})();

// 格式化保费范围
const formatPremiumRange = (range) => {
    if (!range || !Array.isArray(range) || range.length < 2) return '未知';

    // 格式化数字，大于等于10000显示为"x万"
    const formatNumber1 = (num) => {
        if (num >= 10000) {
            return (num / 10000).toFixed(1) + '万';
        }
        return num;
    };

    return formatNumber1(range[0]) + '-' + formatNumber1(range[1]);
};

// 格式化金额
const formatAmount = (value) => {
    if (!value) return '';
    return Number(value).toLocaleString();
};

// 解析金额
const parseAmount = (value) => {
    if (!value) return '';

    // 处理"w"或"W"表示的万元
    if (value.toString().toLowerCase().includes('w')) {
        // 提取数字部分
        const numPart = value.toString().toLowerCase().replace(/[^0-9.]/g, '');
        if (numPart) {
            // 将数字乘以10000（万）
            return parseFloat(numPart) * 10000;
        }
    }

    // 移除货币符号和千位分隔符
    return value.replace(/[^\d.]/g, '');
};

// 获取缴费方式名称
const getPaymentMethodName = (method) => {
    const methods = {
        "ANNUALLY": "年缴",
        "MONTHLY": "月缴",
        "QUARTERLY": "季缴",
        "SEMI_ANNUALLY": "半年缴",
        "SINGLE_PREMIUM": "趸缴",
        "SINGLE_PAYMENT": "整付保费"
    };
    return methods[method] || method;
};

// 保留大整数 例如100000 200000 
const formatPremium = (premium) => {
    // 处理边界情况
    if (!premium || isNaN(premium)) return 0;
    if (premium < 1000) return Math.round(premium);

    // 根据数值大小确定舍入精度
    let roundBase;
    if (premium < 10000) {
        // 小于1万，舍入到1000的倍数
        roundBase = 1000;
    } else if (premium < 100000) {
        // 1万到10万，舍入到5000的倍数
        roundBase = 5000;
    } else if (premium < 1000000) {
        // 10万到100万，舍入到10000的倍数
        roundBase = 10000;
    } else {
        // 100万以上，舍入到100000的倍数
        roundBase = 100000;
    }

    // 舍入到最接近的roundBase的倍数
    return Math.round(premium / roundBase) * roundBase;
};

// 生成客户信息模板
const generateTemplates = () => {
    if (!selectedProduct.value || !selectedSubProduct.value) return [];

    const templates = [];
    const minAge = ageRange.value.min || 18;
    const maxAge = ageRange.value.max || 60;
    const midAge = Math.floor((minAge + maxAge) / 2);

    const minPremium = premiumRange.value[0] || 5000;
    const maxPremium = premiumRange.value[1] || 100000;
    const midPremium = Math.floor((minPremium + maxPremium) / 2);
    if (selectedProduct.value.categoryCode == 'SAVINGS') {
        // 基础方案 - 年轻客户，较低保费
        templates.push({
            id: 'basic',
            name: '教育基金',
            icon: UserOutlined,
            color: '#1890ff',
            description: '为子女教育规划的长期储蓄方案',
            values: {
                customerName: '张先生',
                startAge: 0,
                precisePremium: formatPremium(Math.max(minPremium * 1.05, 10000)), // 降低保费基数
                // precisePremium: Math.max(minPremium * 1.05, 100000 / parseInt(selectedSubProduct.value.paymentTerm)),
                selectedPrepaymentYear: customerInfo.selectedPrepaymentYear,
                selectedPaymentMethod: customerInfo.selectedPaymentMethod,
                holdingYears: 5,
                type: 'standard',
                discounts: []
            }
        });

        // 标准方案 - 中年客户，中等保费
        templates.push({
            id: 'standard',
            name: '家庭保障',
            icon: SafetyOutlined,
            color: '#52c41a',
            description: '适合家庭客户的均衡方案',
            values: {
                customerName: '李女士',
                startAge: 30,
                precisePremium: formatPremium(maxPremium * 0.15), // 改为基于最小保费的计算
                // precisePremium: Math.max(minPremium * 1.05, 300000 / parseInt(selectedSubProduct.value.paymentTerm)),
                selectedPrepaymentYear: customerInfo.selectedPrepaymentYear,
                selectedPaymentMethod: customerInfo.selectedPaymentMethod,
                holdingYears: 10,
                type: 'standard',
                discounts: []
            }
        });

        // 高端方案 - 较高年龄，高保费
        templates.push({
            id: 'premium',
            name: '退休养老',
            icon: CrownOutlined,
            color: '#faad14',
            description: '为退休生活规划的收益方案',
            values: {
                customerName: '王总',
                startAge: 45,
                precisePremium: formatPremium(Math.min(maxPremium * 0.2, 10000000)), // 降低比例系数
                // precisePremium: Math.max(minPremium * 1.05, 500000 / parseInt(selectedSubProduct.value.paymentTerm)),
                selectedPrepaymentYear: customerInfo.selectedPrepaymentYear,
                selectedPaymentMethod: customerInfo.selectedPaymentMethod,
                holdingYears: 15,
                type: 'standard',
                discounts: []
            }
        });


        // 退休方案 - 适合退休规划
        if (maxAge > 50) {
            templates.push({
                id: 'retirement',
                name: '传承计划',
                icon: CoffeeOutlined,
                color: '#eb2f96',
                description: '为传承规划的收益方案',
                values: {
                    customerName: '赵董',
                    startAge: 60,
                    precisePremium: formatPremium(Math.max(minPremium * 1.5, midPremium * 0.5)), // 降低比例系数
                    // precisePremium: Math.max(minPremium * 1.05, 10000000 / parseInt(selectedSubProduct.value.paymentTerm)),
                    selectedPrepaymentYear: customerInfo.selectedPrepaymentYear,
                    selectedPaymentMethod: customerInfo.selectedPaymentMethod,
                    holdingYears: 10,
                    type: 'standard',
                    discounts: []
                }
            });
        }

    } else {
        // 基础方案 - 年轻客户，较低保费
        templates.push({
            id: 'basic',
            name: '教育基金',
            icon: UserOutlined,
            color: '#1890ff',
            description: '为子女教育规划的长期储蓄方案',
            values: {
                customerName: '张先生',
                startAge: 5,
                precisePremium: formatPremium(Math.max(minPremium * 1.05, 10000)), // 降低保费基数
                // precisePremium: Math.max(minPremium * 1.05, 100000 / parseInt(selectedSubProduct.value.paymentTerm)),
                selectedPrepaymentYear: customerInfo.selectedPrepaymentYear,
                selectedPaymentMethod: customerInfo.selectedPaymentMethod,
                holdingYears: 5,
                type: 'standard',
                discounts: []
            }
        });



        // 高端方案 - 较高年龄，高保费
        templates.push({
            id: 'premium',
            name: '青年保障',
            icon: CrownOutlined,
            color: '#faad14',
            description: '为青年客户规划的收益方案',
            values: {
                customerName: '王总',
                startAge: 20,
                precisePremium: formatPremium(Math.min(maxPremium * 0.2, 10000000)), // 降低比例系数
                // precisePremium: Math.max(minPremium * 1.05, 500000 / parseInt(selectedSubProduct.value.paymentTerm)),
                selectedPrepaymentYear: customerInfo.selectedPrepaymentYear,
                selectedPaymentMethod: customerInfo.selectedPaymentMethod,
                holdingYears: 15,
                type: 'standard',
                discounts: []
            }
        });


        // 退休方案 - 适合退休规划
        if (maxAge > 50) {
            templates.push({
                id: 'retirement',
                name: '毕业保障',
                icon: CoffeeOutlined,
                color: '#eb2f96',
                description: '为毕业客户规划的收益方案',
                values: {
                    customerName: '赵董',
                    startAge: 25,
                    precisePremium: formatPremium(Math.max(minPremium * 1.5, midPremium * 0.5)), // 降低比例系数
                    // precisePremium: Math.max(minPremium * 1.05, 10000000 / parseInt(selectedSubProduct.value.paymentTerm)),
                    selectedPrepaymentYear: customerInfo.selectedPrepaymentYear,
                    selectedPaymentMethod: customerInfo.selectedPaymentMethod,
                    holdingYears: 10,
                    type: 'standard',
                    discounts: []
                }
            });
        }

    }



    return templates;
};

// 应用选中的模板
const applyTemplate = (templateId) => {
    const template = templates.value.find(t => t.id === templateId);
    if (!template) return;

    // 应用模板值到表单
    customerInfo.customerName = template.values.customerName;
    customerInfo.startAge = template.values.startAge;
    customerInfo.precisePremium = template.values.precisePremium;
    customerInfo.selectedPrepaymentYear = template.values.selectedPrepaymentYear;
    customerInfo.selectedPaymentMethod = template.values.selectedPaymentMethod;
    customerInfo.holdingYears = template.values.holdingYears;
    customerInfo.type = template.values.type;
    customerInfo.discounts = [...template.values.discounts];

    // 更新选中状态
    selectedTemplateId.value = templateId;

    // 提示用户
    message.success(`已应用"${template.name}"模板`);

    // 更新数据到父组件
    updateSelectionData();
};

// 添加折扣处理函数
const addDiscount = (discount) => {
    customerInfo.discounts.push(discount);
};

const removeDiscount = (index) => {
    customerInfo.discounts.splice(index, 1);
};

const updateDiscount = (index, discount) => {
    if (index >= 0 && index < customerInfo.discounts.length) {
        customerInfo.discounts[index] = { ...discount };
    }
};

defineExpose({
    resetToParentStep
});
</script>

<style scoped>
.product-selector-container {
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    padding: 12px;
    background-color: #fff;
}

.steps-container {
    margin-bottom: 10px;
}

.step-content {
    min-height: 250px;
    max-height: 700px;
    overflow-y: auto;
    padding-right: 4px;
}

/* 模板选择器样式 */
.template-selector {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
}

.template-scroll-container {
    display: flex;
    justify-content: space-evenly;
    gap: 16px;
    flex-wrap: wrap;
    overflow-x: auto;
    padding: 8px 4px;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

.template-scroll-container::-webkit-scrollbar {
    height: 6px;
}

.template-scroll-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.template-scroll-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 10px;
}

.template-scroll-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.template-card-wrapper {
    flex: 0 0 auto;
    width: 220px;
    margin-right: 16px;
}

.template-card {
    border: 2px solid #e8e8e8;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s;
    cursor: pointer;
    background-color: white;
}

.template-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

.template-card-selected {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
    transform: translateY(-4px);
}

.template-card-header {
    padding: 12px;
    color: white;
    display: flex;
    align-items: center;
}

.template-card-icon {
    margin-right: 8px;
    display: flex;
    align-items: center;
}

.template-card-name {
    font-weight: 600;
    font-size: 16px;
}

.template-card-body {
    padding: 12px;
}

.template-card-desc {
    color: #666;
    font-size: 12px;
    margin-bottom: 8px;
    height: 32px;
}

.template-card-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.template-card-detail {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
}

.detail-label {
    color: #888;
}

.detail-value {
    font-weight: 500;
    color: #333;
}

/* 搜索和筛选区域样式 */
.search-filter-area {
    background-color: #f9f9f9;
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 12px;
}

.filter-tags {
    min-height: 24px;
}

/* 产品卡片样式 */
.product-card,
.currency-card,
.payment-term-card,
.payment-method-card {
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    padding: 8px;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.85rem;
}

.product-card:hover,
.currency-card:hover,
.payment-term-card:hover,
.payment-method-card:hover {
    border-color: #1890ff;
    box-shadow: 0 0 0 1px rgba(24, 144, 255, 0.2);
}

.product-card.selected,
.currency-card.selected,
.payment-term-card.selected,
.payment-method-card.selected {
    border-color: #1890ff;
    background-color: #e6f7ff;
}

.empty-state {
    padding: 30px 0;
    text-align: center;
}

.pagination {
    margin-top: 12px;
    display: flex;
    justify-content: center;
}

.product-name {
    font-size: 0.9rem;
    line-height: 1.2;
    margin-bottom: 2px;
}

.product-company,
.product-region {
    font-size: 0.75rem;
}

.product-details {
    margin-top: 4px;
    font-size: 0.75rem;
    color: #666;
    line-height: 1.2;
}

.product-type {
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 10px;
}

.selected-product-info {
    margin-bottom: 8px;
    padding: 8px;
    border-radius: 4px;
}

.company-logo img,
.logo-placeholder {
    height: 24px;
    width: 24px;
}

.step-actions {
    margin-top: 12px;
}

/* 自定义滚动条样式 */
.step-content::-webkit-scrollbar {
    width: 6px;
}

.step-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 6px;
}

.step-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 6px;
}

.step-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 媒体查询，移动端优化 */
@media (max-width: 640px) {
    .search-filter-area {
        padding: 6px;
    }

    .step-content {
        max-height: 400px;
    }

    .product-card {
        padding: 6px;
    }

    .template-card-wrapper {
        width: 180px;
    }
}
</style>