<template>
    <div class="overflow-x-auto">
        <ITable :dataSource="data" :columns="tableColumns" rowKey="year" :highlightRow="highlightTableRow"
            :highlightStyle="getHighlightStyle" :theme="tableTheme" :rowStep="rowStep" bordered />
    </div>
</template>

<script setup>
import { computed, h } from 'vue';
import ITable from '@/components/iTable.vue';
import { Tag } from 'ant-design-vue';
import { formatNumber } from '@/utils/number';

const props = defineProps({
    data: {
        type: Array,
        default: () => []
    },
    tableTheme: {
        type: String,
        default: 'default'
    },
    rowStep: {
        type: Number,
        default: 1
    },
    enableHighlight: {
        type: Boolean,
        default: true
    },
    highlightRules: {
        type: Array,
        default: () => []
    },
    highlightStyle: {
        type: Object,
        default: () => ({ backgroundColor: 'rgba(22, 163, 74, 0.05)' })
    },
    columnsVisible: {
        type: Object,
        default: () => ({
            age: true,
            year: true,
            totalPremum: true,
            netflow: true,
            insuredAmount: true,
            terminalBonusNg: true,
            surrender: true,
            singleton: true,
            IRR: true,
            cashValueOfReversionaryBonusNg: true,
            deathBenefitG: true,
            deathBenefitTerminalBonusNg: true,
            deathBenefitTotal: true,
            totalWithdrawableCashAndInterest: true,
            totalMonthlyAnnuityAndInterest: true,
            insuredIRR: true,
            insuredSingleInterest: true
        })
    }
});

// 过滤表格数据，去除单利<0或复利==0的行
// const tableData = computed(() => {
//     if (!props.data) return [];
//     return props.data.filter(item => {
//         const singleton = Number(item.singleton);
//         const irr = Number(item.IRR);
//         return singleton >= 0 && irr > 0;
//     });
// });

// 获取主题对应的背景颜色
const getThemeColor = (theme) => {
    switch (theme) {
        case 'default': return '#374151';
        case 'primary': return 'var(--primary)';
        case 'success': return '#16a34a';
        case 'warning': return '#d97706';
        case 'danger': return '#dc2626';
        default: return '#374151';
    }
};

// 获取主题对应的文字颜色
const getThemeTextColor = (theme) => {
    switch (theme) {
        case 'default': return '#000000';
        case 'primary': return '#000000';
        case 'success': return '#000000';
        case 'warning': return '#000000';
        case 'danger': return '#000000';
        default: return '#000000';
    }
};

// 表格列配置
const tableColumns = computed(() => {
    // 检查数据中是否包含某个字段（如果所有记录该字段都为null，则不显示该列）
    const hasFieldData = (fieldName) => {
        if (!props.data || props.data.length === 0) return false;
        return props.data.some(item => item[fieldName] !== null && item[fieldName] !== undefined);
    };

    const allColumns = [

        {
            title: '年龄',
            dataIndex: 'age',
            width: 50,
            key: 'age',
            align: 'center',
            visible: props.columnsVisible.age && hasFieldData('age')
        },
        {
            title: '持有时间',
            dataIndex: 'year',
            width: 100,
            key: 'year',
            align: 'center',
            visible: props.columnsVisible.year && hasFieldData('year'),
            customCell: (record) => {
                return {
                    style: record.multiple && record.multiple > 0
                        ? { color: getThemeTextColor(props.tableTheme) }
                        : {}
                };
            }
        },
        {
            title: '总保费',
            dataIndex: 'totalPremum',
            key: 'totalPremum',
            align: 'center',
            visible: props.columnsVisible.totalPremum && hasFieldData('totalPremum'),
            customRender: ({ text }) => {
                if (text) {
                    return formatNumber(text, { decimals: 2 });
                }
                return text;
            },
            customCell: (record) => {
                return {}; // 添加空的customCell以保持一致性
            }
        },
        {
            title: '净现金流',
            dataIndex: 'netflow',
            key: 'netflow',
            align: 'center',
            visible: props.columnsVisible.netflow && hasFieldData('netflow'),
            customRender: ({ text }) => {
                if (text && text != 0) {
                    return formatNumber(text, { decimals: 2 });
                } else {
                    return '不再缴费'
                }

            },
        },
        {
            title: '保证退保现金',
            dataIndex: 'insuredAmount',
            key: 'insuredAmount',
            align: 'center',
            visible: props.columnsVisible.insuredAmount && hasFieldData('insuredAmount'),
            customRender: ({ text }) => {
                if (text) {
                    return formatNumber(text, { decimals: 2 });
                }
                return text;
            },
        },
        {
            title: '终期红利(非保证)',
            dataIndex: 'terminalBonusNg',
            key: 'terminalBonusNg',
            align: 'center',
            visible: props.columnsVisible.terminalBonusNg && hasFieldData('terminalBonusNg'),
            customRender: ({ text }) => {
                if (text) {
                    return formatNumber(text, { decimals: 2 });
                }
                return text;
            },
        },
        {
            title: '复归红利(非保证)',
            dataIndex: 'cashValueOfReversionaryBonusNg',
            key: 'cashValueOfReversionaryBonusNg',
            align: 'center',
            visible: props.columnsVisible.cashValueOfReversionaryBonusNg && hasFieldData('cashValueOfReversionaryBonusNg'),
            customRender: ({ text }) => {
                if (text) {
                    return formatNumber(text, { decimals: 2 });
                }
                return text;
            },
        },
        {
            title: '退保总额',
            dataIndex: 'surrender',
            key: 'surrender',
            align: 'center',
            visible: props.columnsVisible.surrender && hasFieldData('surrender'),
            customRender: ({ text, record }) => {
                let formattedText = text;
                if (text) {
                    formattedText = formatNumber(text, { decimals: 2 });
                }

                if (record.multiple) {
                    return h('div', { class: 'flex items-center justify-center' }, [
                        h('span', { class: 'mr-2' }, formattedText),
                        h(Tag, { color: getThemeColor(props.tableTheme) }, () => 'x' + record.multiple)
                    ]);
                }
                return formattedText;
                return text;
            },
            // 使用customCell代替在customRender中返回props
            customCell: (record) => {
                return {
                    style: record.multiple ? { backgroundColor: 'rgba(240, 240, 240, 0.3)' } : {}
                };
            }
        },
        {
            title: '单利',
            dataIndex: 'singleton',
            key: 'singleton',
            align: 'center',
            visible: props.columnsVisible.singleton && hasFieldData('singleton'),
            customRender: ({ text }) => {
                return isNaN(text) ? text : text + '%';
            },
            customCell: (record) => {
                return {}; // 添加空的customCell以保持一致性
            }
        },
        {
            title: '复利',
            dataIndex: 'IRR',
            key: 'IRR',
            align: 'center',
            visible: props.columnsVisible.IRR && hasFieldData('IRR'),
            customRender: ({ text }) => {
                return isNaN(text) ? text : text + '%';
            },
            customCell: (record) => {
                return {}; // 添加空的customCell以保持一致性
            }
        },
        {
            title: '保证单利',
            dataIndex: 'insuredSingleInterest',
            key: 'insuredSingleInterest',
            align: 'center',
            visible: props.columnsVisible.insuredSingleInterest && hasFieldData('insuredSingleInterest'),
            customRender: ({ text }) => {
                return isNaN(text) ? text : text + '%';
            },
            customCell: (record) => {
                return {}; // 添加空的customCell以保持一致性
            }
        },
        {
            title: '保证复利',
            dataIndex: 'insuredIRR',
            key: 'insuredIRR',
            align: 'center',
            visible: props.columnsVisible.insuredIRR && hasFieldData('insuredIRR'),
            customRender: ({ text }) => {
                return isNaN(text) ? text : text + '%';
            },
            customCell: (record) => {
                return {}; // 添加空的customCell以保持一致性
            }
        },
        {
            title: '身故保证金额',
            dataIndex: 'deathBenefitG',
            key: 'deathBenefitG',
            align: 'center',
            visible: props.columnsVisible.deathBenefitG && hasFieldData('deathBenefitG'),
            customRender: ({ text }) => {
                if (text) {
                    return formatNumber(text, { decimals: 2 });
                }
                return text;
            },
        },
        {
            title: '身故终期红利',
            dataIndex: 'deathBenefitTerminalBonusNg',
            key: 'deathBenefitTerminalBonusNg',
            align: 'center',
            visible: props.columnsVisible.deathBenefitTerminalBonusNg && hasFieldData('deathBenefitTerminalBonusNg'),
            customRender: ({ text }) => {
                if (text) {
                    return formatNumber(text, { decimals: 2 });
                }
                return text;
            },
        },
        {
            title: '身故总额',
            dataIndex: 'deathBenefitTotal',
            key: 'deathBenefitTotal',
            align: 'center',
            visible: props.columnsVisible.deathBenefitTotal && hasFieldData('deathBenefitTotal'),
            customRender: ({ text }) => {
                if (text) {
                    return formatNumber(text, { decimals: 2 });
                }
                return text;
            },
        },
        {
            title: '累积可提取现金利息总额',
            dataIndex: 'totalWithdrawableCashAndInterest',
            key: 'totalWithdrawableCashAndInterest',
            align: 'center',
            visible: props.columnsVisible.totalWithdrawableCashAndInterest && hasFieldData('totalWithdrawableCashAndInterest'),
            customRender: ({ text }) => {
                if (text) {
                    return formatNumber(text, { decimals: 2 });
                }
                return text;
            },
        },
        {
            title: '累积每月年金入利总额',
            dataIndex: 'totalMonthlyAnnuityAndInterest',
            key: 'totalMonthlyAnnuityAndInterest',
            align: 'center',
            visible: props.columnsVisible.totalMonthlyAnnuityAndInterest && hasFieldData('totalMonthlyAnnuityAndInterest'),
            customRender: ({ text }) => {
                if (text) {
                    return formatNumber(text, { decimals: 2 });
                }
                return text;
            },
        },
    ];

    // 返回visible为true的列
    return allColumns.filter(col => col.visible !== false);
});

// 表格行高亮条件
const highlightTableRow = (row) => {
    // 只有在启用高亮时才应用高亮条件
    if (!props.enableHighlight) return false;

    // 如果没有高亮规则，则不高亮
    if (!props.highlightRules || props.highlightRules.length === 0) return false;

    // 过滤出启用的规则
    const enabledRules = [...props.highlightRules]
        .filter(rule => rule.enabled)
        .sort((a, b) => a.priority - b.priority);

    // 如果没有启用的规则，则不高亮
    if (enabledRules.length === 0) return false;

    // 检查是否所有规则都匹配
    for (const rule of enabledRules) {
        const fieldValue = row[rule.field];
        // 如果字段值为null或undefined，则该规则不匹配
        if (fieldValue === null || fieldValue === undefined) return false;

        let matches = false;

        switch (rule.operator) {
            case '>':
                matches = fieldValue > rule.value;
                break;
            case '>=':
                matches = fieldValue >= rule.value;
                break;
            case '<':
                matches = fieldValue < rule.value;
                break;
            case '<=':
                matches = fieldValue <= rule.value;
                break;
            case '==':
                matches = fieldValue == rule.value;
                break;
            default:
                matches = false;
        }

        // 如果任何一个规则不匹配，整行就不高亮
        if (!matches) return false;
    }

    // 所有规则都匹配，使用第一个规则的样式
    if (enabledRules.length > 0) {
        row._matchedRule = enabledRules[0];
    }

    // 所有规则都匹配，返回true
    return true;
};

// 获取高亮样式
const getHighlightStyle = (row) => {
    if (!row._matchedRule) return props.highlightStyle;

    // 使用匹配规则的颜色和透明度
    const rule = row._matchedRule;
    const opacity = rule.opacity || 0.2;
    const hexOpacity = Math.round(opacity * 255).toString(16).padStart(2, '0');

    return {
        backgroundColor: `${rule.color}${hexOpacity}`
    };
};
</script>