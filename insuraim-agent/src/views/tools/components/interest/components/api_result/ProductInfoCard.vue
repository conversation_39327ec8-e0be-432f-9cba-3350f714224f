<template>
    <div class="bg-white rounded-lg shadow-md p-4 mb-4 border border-gray-200">
        <h4 class="text-lg font-medium text-gray-800 mb-3 flex items-center">
            <Icon icon="mdi:file-document-outline" class="mr-2 text-blue-500" />
            案例参考
        </h4>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            <div class="flex items-center">
                <Icon icon="mdi:file-document-outline" class="mr-2 text-gray-500" />
                <span class="text-gray-600 font-medium">产品名称：</span>
                <span class="ml-1">{{ productData.productName }}</span>
            </div>
            <div class="flex items-center">
                <Icon icon="mdi:file-document-outline" class="mr-2 text-gray-500" />
                <span class="text-gray-600 font-medium">年龄：</span>
                <span class="ml-1">{{ productInfo.age || 0 }}</span>
            </div>
            <div class="flex items-center">
                <Icon icon="mdi:file-document-outline" class="mr-2 text-gray-500" />
                <span class="text-gray-600 font-medium">吸烟状态：</span>
                <span class="ml-1">{{ productInfo.smokingStatus }}</span>
            </div>
            <div class="flex items-center" v-if="productInfo.gender">
                <Icon icon="mdi:account" class="mr-2 text-gray-500" />
                <span class="text-gray-600 font-medium">性别：</span>
                <span class="ml-1">{{ productInfo.gender }}</span>
            </div>
            <div class="flex items-center" v-if="productInfo.smoking_status">
                <Icon icon="mdi:smoking-off" class="mr-2 text-gray-500" v-if="productInfo.smoking_status === '非吸煙者'" />
                <Icon icon="mdi:smoking" class="mr-2 text-gray-500" v-else />
                <span class="text-gray-600 font-medium">吸烟状态：</span>
                <span class="ml-1">{{ productInfo.smoking_status }}</span>
            </div>
            <div class="flex items-center" v-if="productInfo.annualPremium">
                <Icon icon="mdi:cash" class="mr-2 text-gray-500" />
                <span class="text-gray-600 font-medium">保费：</span>
                <span class="ml-1">{{ getCurrencySymbol(productInfo.policyCurrency) }}{{
                    thousandSeparator(productInfo.annualPremium)
                    }}</span>
            </div>
            <div class="flex items-center" v-if="productInfo.paymentMode">
                <Icon icon="mdi:credit-card-outline" class="mr-2 text-gray-500" />
                <span class="text-gray-600 font-medium">缴费方式：</span>
                <span class="ml-1">{{ productInfo.paymentMode }}</span>
            </div>
            <div class="flex items-center" v-if="productInfo.coverageTerm">
                <Icon icon="mdi:calendar-check" class="mr-2 text-gray-500" />
                <span class="text-gray-600 font-medium">保障期限：</span>
                <span class="ml-1">{{ productInfo.coverageTerm }}</span>
            </div>
            <div class="flex items-center" v-if="productInfo.premiumPaymentTerm">
                <Icon icon="mdi:calendar-clock" class="mr-2 text-gray-500" />
                <span class="text-gray-600 font-medium">缴费年限：</span>
                <span class="ml-1">{{ productInfo.premiumPaymentTerm }}</span>
            </div>
            <div class="flex items-center" v-if="productInfo.policyCurrency">
                <Icon icon="mdi:currency-usd" class="mr-2 text-gray-500" />
                <span class="text-gray-600 font-medium">保单币种：</span>
                <span class="ml-1">{{ productInfo.policyCurrency }}</span>
            </div>
        </div>
    </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';
import { computed, onMounted } from 'vue';
import { formatNumber } from '@/utils/number';
import { productAPI } from '@/api';
const props = defineProps({
    productData: {
        type: Object,
        required: true
    },

});

const currency = ref('CNY');

const getCurrentList = () => {

    productAPI.getIRRCurrencyList().then(res => {
        console.log(res);
    });
}
onMounted(() => {
    getCurrentList();
})
// 根据货币获取
// 货币符号
const getCurrencySymbol = (currency) => {
    // "data": [
    //     "美元",
    //     "港元",
    //     "人民幣",
    //     "澳元",
    //     "加拿大元",
    //     "英鎊",
    //     "紐西蘭元",
    //     "歐羅",
    //     "新加坡元"
    // ],
    if (currency === '美元') {
        return '$';
    } else if (currency === '港元') {
        return 'HK$';
    } else if (currency === '人民幣') {
        return '¥';
    } else if (currency === '澳元') {
        return 'AU$';
    } else if (currency === '加拿大元') {
        return 'CA$';
    } else if (currency === '英鎊') {
        return '£';
    } else if (currency === '紐西蘭元') {
        return 'NZ$';
    } else if (currency === '歐羅') {
        return '€';
    } else if (currency === '新加坡元') {
        return 'S$';
    } else {
        return '$';
    }

}

// 千分位分割
const thousandSeparator = (value) => {
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}
// 获取产品信息
const productInfo = computed(() => {
    const item = props.productData;
    const result = {};

    // 如果有 dynamicIRRData 属性，说明这是新格式的数据
    if (item.dynamicIRRData) {
        // 只添加非null的属性
        if (item.dynamicIRRData.age != null) result.age = item.dynamicIRRData.age;
        if (item.dynamicIRRData.gender) result.gender = item.dynamicIRRData.gender;
        if (item.dynamicIRRData.assumedAge) result.assumedAge = item.dynamicIRRData.assumedAge;
        if (item.dynamicIRRData.smoking_status) result.smoking_status = item.dynamicIRRData.smoking_status;
        if (item.dynamicIRRData.annualPremium) result.annualPremium = item.dynamicIRRData.annualPremium;
        if (item.dynamicIRRData.paymentMode) result.paymentMode = item.dynamicIRRData.paymentMode;
        if (item.dynamicIRRData.coverageTerm) result.coverageTerm = item.dynamicIRRData.coverageTerm;
        if (item.dynamicIRRData.smokingStatus) result.smokingStatus = item.dynamicIRRData.smokingStatus;
        if (item.dynamicIRRData.premiumPaymentTerm) result.premiumPaymentTerm = item.dynamicIRRData.premiumPaymentTerm;
        if (item.dynamicIRRData.policyCurrency) result.policyCurrency = item.dynamicIRRData.policyCurrency;
        if (item.dynamicIRRData.cashValueOfReversionaryBonusNg) result.cashValueOfReversionaryBonusNg = item.dynamicIRRData.cashValueOfReversionaryBonusNg;
    } else {
        // 兼容旧格式的数据，只添加非null的属性
        if (item.gender) result.gender = item.gender;
        if (item.years) result.assumedAge = item.years;
        else if (item.assumedAge) result.assumedAge = item.assumedAge;
        if (item.smoking_status) result.smoking_status = item.smoking_status;
        if (item.annualPremium) result.annualPremium = item.annualPremium;
        if (item.paymentMode) result.paymentMode = item.paymentMode;
        if (item.coverageTerm) result.coverageTerm = item.coverageTerm;
        if (item.premiumPaymentTerm) result.premiumPaymentTerm = item.premiumPaymentTerm;
        if (item.policyCurrency) result.policyCurrency = item.policyCurrency;
        if (item.surrender) result.surrender = item.surrender;
        if (item.terminalBonusNg) result.terminalBonusNg = item.terminalBonusNg;
        if (item.cashValueOfReversionaryBonusNg) result.cashValueOfReversionaryBonusNg = item.cashValueOfReversionaryBonusNg;
    }

    return result;
});
const setCurrency = (pcurrency) => {
    currency.value = pcurrency;
};
defineExpose({
    setCurrency
});
</script>