<template>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <!-- 单利收益率卡片 -->
        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4"
            :style="{ borderColor: getThemeColor(theme) }">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-500 text-sm">单利收益率</p>
                    <p class="text-2xl font-bold" :style="{ color: getThemeColor(theme) }">
                        {{ lastYearData?.singleInterest !== undefined ? lastYearData?.singleInterest :
                            lastYearData?.singleton || '0' }}%
                    </p>
                </div>
                <div class="p-2 rounded-full" :style="{ backgroundColor: getThemeColor(theme) + '20' }">
                    <Icon icon="material-symbols:trending-up" class="text-2xl"
                        :style="{ color: getThemeColor(theme) }" />
                </div>
            </div>
            <div class="mt-2 text-xs text-gray-500">
                <span class="flex items-center">
                    <Icon icon="material-symbols:info-outline" class="mr-1" />
                    单利计算的年化收益率
                </span>
            </div>
        </div>

        <!-- 复利收益率卡片 -->
        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-green-500">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-500 text-sm">复利收益率</p>
                    <p class="text-2xl font-bold text-green-600">
                        {{ lastYearData?.irr !== undefined ? (lastYearData?.irr === 'NaN' ? '0' : lastYearData?.irr) :
                            lastYearData?.IRR || '0' }}%
                    </p>
                </div>
                <div class="bg-green-100 p-2 rounded-full">
                    <Icon icon="material-symbols:finance" class="text-2xl text-green-500" />
                </div>
            </div>
            <div class="mt-2 text-xs text-gray-500">
                <span class="flex items-center">
                    <Icon icon="material-symbols:info-outline" class="mr-1" />
                    复利计算的内部收益率
                </span>
            </div>
        </div>

        <!-- 总投入卡片 -->
        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-orange-500">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-500 text-sm">总投入</p>
                    <p class="text-2xl font-bold text-orange-600">
                        {{ getCurrencySymbol(currency) }}{{ Number(lastYearData?.totalPremium !== undefined ? lastYearData?.totalPremium :
                            lastYearData?.totalPremum || 0).toLocaleString() }}
                    </p>
                </div>
                <div class="bg-orange-100 p-2 rounded-full">
                    <Icon icon="material-symbols:payments" class="text-2xl text-orange-500" />
                </div>
            </div>
            <div class="mt-2 text-xs text-gray-500">
                <span class="flex items-center">
                    <Icon icon="material-symbols:info-outline" class="mr-1" />
                    总缴纳保费
                </span>
            </div>
        </div>

        <!-- 总收益卡片 -->
        <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-purple-500">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-500 text-sm">总收益</p>
                    <p class="text-2xl font-bold text-purple-600">
                        {{ getCurrencySymbol(currency) }}{{ totalProfit.toLocaleString() }}
                    </p>
                </div>
                <div class="bg-purple-100 p-2 rounded-full">
                    <Icon icon="material-symbols:savings" class="text-2xl text-purple-500" />
                </div>
            </div>
            <div class="mt-2 text-xs text-gray-500">
                <span class="flex items-center">
                    <Icon icon="material-symbols:info-outline" class="mr-1" />
                    现金价值 - 总保费
                </span>
            </div>
        </div>
    </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';

const props = defineProps({
    lastYearData: {
        type: Object,
        default: () => ({})
    },
    totalProfit: {
        type: Number,
        default: 0
    },
    theme: {
        type: String,
        default: 'default'
    },
    // 货币
    currency:{
        type:String,
        default:'CNY'
    }
});

// 获取主题对应的背景颜色
const getThemeColor = (theme) => {
    switch (theme) {
        case 'default': return '#374151';
        case 'primary': return '#0369a1';
        case 'success': return '#16a34a';
        case 'warning': return '#d97706';
        case 'danger': return '#dc2626';
        default: return '#374151';
    }
};

// 获取货币符号
const getCurrencySymbol = (currencyCode) => {
    const currencySymbols = {
        'CNY': '¥', // 人民币
        'USD': '$', // 美元
        'EUR': '€', // 欧元
        'GBP': '£', // 英镑
        'JPY': '¥', // 日元
        'HKD': 'HK$', // 港币
        'TWD': 'NT$', // 新台币
        'KRW': '₩', // 韩元
        'SGD': 'S$', // 新加坡元
        'MYR': 'RM', // 马来西亚林吉特
        'THB': '฿', // 泰铢
        'IDR': 'Rp', // 印尼盾
        'PHP': '₱', // 菲律宾比索
        'AUD': 'A$', // 澳元
        'NZD': 'NZ$', // 新西兰元
        'CAD': 'C$', // 加元
        'CHF': 'Fr', // 瑞士法郎
        'RUB': '₽', // 俄罗斯卢布
        'INR': '₹', // 印度卢比
        'BRL': 'R$', // 巴西雷亚尔
    };
    
    return currencySymbols[currencyCode] || currencyCode;
};
</script>

<style scoped>
/* 统计卡片样式 */
.stat-card {
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}
</style>