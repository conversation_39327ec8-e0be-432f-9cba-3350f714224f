<template>
    <a-spin :spinning="false">
        <div class="flex justify-end mb-2">
            <a-space>

                <a-button type="default" @click="handleGetPlanBookLink" id="no-pdf">
                    <Icon icon="mdi:link" />
                    <span>示例计划书</span>
                </a-button>
                <a-button type="default" @click="handleExport" id="no-pdf">
                    <Icon icon="mdi:export" />
                    <span>导出Excel</span>
                </a-button>


                <a-button type="primary" @click="handleEdit" id="no-pdf">
                    <Icon icon="mdi:pencil" />
                    <span>编辑表格</span>
                </a-button>

            </a-space>
        </div>

        <!-- 示例计划书选择模态窗口 -->
        <a-modal v-model:visible="planBookModalVisible" title="选择示例计划书" :footer="null" width="600px" centered
            :destroyOnClose="true" class="plan-book-modal">
            <div class="plan-book-content">
                <!-- 加载状态 -->
                <div v-if="planBooksLoading" class="flex justify-center items-center p-8">
                    <a-spin size="large" />
                </div>

                <!-- 无数据状态 -->
                <div v-else-if="!planBooks || planBooks.length === 0"
                    class="text-center py-12 flex flex-col items-center">
                    <Icon icon="mdi:file-document-outline" class="text-6xl text-gray-300" />
                    <p class="text-gray-500 mt-4">该产品暂无示例计划书</p>
                </div>

                <!-- 计划书列表 -->
                <div v-else class="plan-books-list">
                    <div v-for="(book, index) in planBooks" :key="index" class="plan-book-card">
                        <div class="flex items-start">
                            <div class="book-icon">
                                <Icon icon="mdi:file-pdf-box" class="text-red-500 text-3xl" />
                            </div>
                            <div class="book-info flex-1 ml-3">
                                <h4 class="book-title text-base font-medium text-gray-900 mb-1 line-clamp-2">
                                    {{ book.productName }}
                                </h4>
                                <div class="book-meta text-sm text-gray-500">
                                    <div class="flex items-center flex-wrap gap-x-4 mt-1">
                                        <span class="flex items-center">
                                            <Icon icon="mdi:office-building" class="mr-1" />
                                            {{ book.company || '未知公司' }}
                                        </span>
                                        <span class="flex items-center" v-if="book.age">
                                            <Icon icon="mdi:account" class="mr-1" />
                                            {{ book.age }}岁
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="book-actions mt-1">
                                <a-button type="primary" size="small" @click="openPlanBook(book)" :loading="false">
                                    <Icon icon="mdi:eye" class="mr-1" />
                                    查看
                                </a-button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-4 flex justify-end plan-book-footer">
                <a-button @click="planBookModalVisible = false">关闭</a-button>
            </div>
        </a-modal>
    </a-spin>
</template>

<script setup>
import { ref } from 'vue';
import { Icon } from '@iconify/vue';
import { message } from 'ant-design-vue';
import { productAPI } from '@/api';
import ApiResultDisplay from '../ApiResultDisplay.vue';
import { log } from 'sockjs-client/dist/sockjs';
const isProduction = import.meta.env.VITE_APP_ENV === 'production';
const props = defineProps({
    // 添加必要的props
    productName: {
        type: String,
        default: ''
    },
    age: {
        type: Number,
        default: 0
    },
    selectedProduct: {
        type: Object,
        default: () => ({})
    },
    apiCalculationResult: {
        type: Object,
        default: () => ({})
    }
});

// 计划书模态窗口状态
const planBookModalVisible = ref(false);
const planBooks = ref([]);
const planBooksLoading = ref(false);

const emit = defineEmits(['export', 'edit']);

// 获取示例计划书链接
const handleGetPlanBookLink = () => {
    const productName = props.productName;
    if (!productName) {
        message.error('请先选择产品');
        return;
    }

    planBooksLoading.value = true;
    planBookModalVisible.value = true;

    productAPI.getPlanBookLink({
        productName: productName,
        // age: props.age
    }).then(res => {
        planBooks.value = res || [];

        // 如果只返回一个计划书，可以直接打开
        if (res && res.length === 1) {
            planBookModalVisible.value = false;
            openPlanBook(res[0]);

        }
        // else {

        //     res.forEach(item => {
        //         if (item.productName.includes(props.apiCalculationResult.irr[0].dynamicIRRData.policyCurrency)) {
        //             planBookModalVisible.value = false;
        //             openPlanBook(item);
        //             return;
        //         }
        //     });
        // }
    }).catch(error => {
        console.error('获取示例计划书失败:', error);
        message.error('获取示例计划书失败，请稍后重试');
        planBooks.value = [];
    }).finally(() => {
        planBooksLoading.value = false;
    });
};

// 打开计划书
const openPlanBook = (book) => {
    if (!book || !book.link) {
        message.error('计划书链接无效');
        return;
    }
    window.open(book.link, '_blank');
};

const handleExport = () => {
    emit('export');
};

const handleEdit = () => {
    emit('edit');
};

const handleHighlight = () => {
    emit('highlight');
};
</script>

<style scoped>
.plan-books-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
    max-height: 400px;
    overflow-y: auto;
}

.plan-book-card {
    padding: 16px;
    border-radius: 8px;
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    background-color: #ffffff;
}

.plan-book-card:hover {
    border-color: #d9d9d9;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.book-title {
    word-break: break-all;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.plan-book-footer {
    border-top: 1px solid #f0f0f0;
    padding-top: 16px;
}

:deep(.plan-book-modal .ant-modal-body) {
    padding: 24px;
}
</style>