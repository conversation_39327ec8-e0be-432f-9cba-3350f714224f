<template>
    <div class="mb-6">
        <div class="chart-container" style="height: 320px; width: 100%; position: relative;">
            <div v-if="loading" class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 z-10">
                <a-spin />
            </div>
            <div :id="chartId" style="height: 320px; width: 100%;"></div>
        </div>
    </div>
</template>

<script setup>
import { onMounted, onUnmounted, watch, computed } from 'vue';
import { useChartRenderer } from '../hooks/useChartRenderer';

const props = defineProps({
    data: {
        type: Array,
        default: () => []
    },
    tabIndex: {
        type: Number,
        required: true
    }
});

// 使用图表渲染钩子
const { loading, initInterestRateChart } = useChartRenderer();

// 计算图表ID
const chartId = computed(() => `interest-rate-chart-${props.tabIndex}`);

// 过滤图表数据
const filteredData = computed(() => {
    return props.data?.filter(item => {
        // 支持新旧字段名称，优先使用新字段名
        const singleInterestValue = item.singleInterest !== undefined ? Number(item.singleInterest) : (item.singleton !== undefined ? Number(item.singleton) : 0);


        // 处理irr值，可能是"NaN"字符串
        const irrValue = item.irr !== undefined ? item.irr : item.IRR;
        const irrNum = typeof irrValue === 'string' && irrValue === 'NaN' ? 0 : Number(irrValue);

        // 只要单利值是有效数字且大于0就显示
        return singleInterestValue > 0;
    }) || [];
});

// 初始化图表
const initChart = () => {


    initInterestRateChart(chartId.value, filteredData.value);
};

// 组件挂载时初始化图表
onMounted(() => {
    initChart();
});

// 监听数据变化，重新初始化图表
watch(() => [props.data, props.tabIndex], () => {
    initChart();
}, { deep: true });
</script>

<style scoped>
.chart-container {
    width: 100%;
    height: 320px;
    position: relative;
    margin-bottom: 20px;
}
</style>