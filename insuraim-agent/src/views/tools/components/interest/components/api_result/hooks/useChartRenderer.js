import { ref, onMounted, onUnmounted } from 'vue';
import * as echarts from 'echarts';
import { formatNumber } from '@/utils/number';

/**
 * 图表渲染相关的状态和方法
 * @returns {Object} 图表渲染相关的状态和方法
 */
export function useChartRenderer() {
    // 图表实例存储
    const chartInstances = ref({});

    // 加载状态
    const loading = ref(false);

    // 图表数据缓存（用于导出）
    const chartImageCache = ref({});
    const originalChartContainers = ref({});

    /**
     * 确保容器尺寸
     * @param {HTMLElement} container 图表容器元素
     */
    const ensureContainerSize = (container) => {
        if (!container) return;

        // 如果容器尺寸为0，设置明确的尺寸
        if (container.clientWidth <= 0 || container.clientHeight <= 0) {
            container.style.width = '100%';
            container.style.height = '320px';
            container.style.display = 'block';
            container.style.visibility = 'visible';
            container.style.position = 'relative';
        }
    };

    /**
     * 初始化利率图表
     * @param {string} chartId 图表ID
     * @param {Array} data 图表数据
     */
    const initInterestRateChart = (chartId, data) => {
        try {
            // 获取DOM元素
            const chartDom = document.getElementById(chartId);
            if (!chartDom) {
                console.error(`找不到图表容器元素: ${chartId}`);
                return;
            }

            // 确保容器尺寸
            ensureContainerSize(chartDom);

            // 如果已经有图表实例，先销毁
            if (chartInstances.value[chartId]) {
                try {
                    chartInstances.value[chartId].dispose();
                } catch (e) {
                    console.error('清理旧图表实例失败:', e);
                }
            }

            // 初始化图表
            const chart = echarts.init(chartDom);
            chartInstances.value[chartId] = chart;

            if (!data || data.length === 0) {
                chart.setOption({
                    title: {
                        text: '无有效收益率数据',
                        left: 'center',
                        top: 'center',
                        textStyle: {
                            fontSize: 16,
                            color: '#999'
                        }
                    }
                });
                return;
            }
            // 筛选单利》0的行
            data = data.filter(item => {
                return item.singleton > 0;
            });

            // 检查数据字段是否存在
            const hasData = {
                singleton: data.some(item => {
                    const value = item.singleInterest !== undefined ? item.singleInterest : item.singleton;
                    return value !== null && value !== undefined;
                }),
                irr: data.some(item => {
                    const value = item.irr !== undefined ? item.irr : item.IRR;
                    return value !== null && value !== undefined && value !== 'NaN';
                }),
            };

            // 准备图表数据
            const years = data.map(item => item.year);

            // 支持新旧字段名称，优先使用新字段名
            const singletonValues = hasData.singleton ? data.map(item => {
                // 优先使用新字段名，如果不存在则使用旧字段名
                const value = item.singleInterest !== undefined ? item.singleInterest : item.singleton;
                return value !== null && value !== undefined ? Number(value) : null;
            }) : [];

            const irrValues = hasData.irr ? data.map(item => {
                // 优先使用新字段名，如果不存在则使用旧字段名
                const irrValue = item.irr !== undefined ? item.irr : item.IRR;
                // 处理特殊值"NaN"，转换为null
                return (typeof irrValue === 'string' && irrValue === 'NaN') || irrValue === null || irrValue === undefined ?
                    null : Number(irrValue);
            }) : [];

            // 准备系列数据
            const series = [];
            const legendData = [];
            const yAxis = [];

            // 只添加有数据的系列
            if (hasData.singleton) {
                series.push({
                    name: '单利收益率',
                    type: 'line',
                    data: singletonValues,
                    itemStyle: {
                        color: '#1890ff'
                    },
                    yAxisIndex: 0
                });
                legendData.push('单利收益率');
                yAxis.push({
                    type: 'value',
                    name: '单利收益率 (%)',
                    axisLabel: {
                        formatter: '{value}%'
                    },
                    position: 'left'
                });
            }

            if (hasData.irr) {
                series.push({
                    name: '复利收益率',
                    type: 'line',
                    data: irrValues,
                    itemStyle: {
                        color: '#52c41a'
                    },
                    yAxisIndex: hasData.singleton ? 1 : 0,
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [{
                                offset: 0,
                                color: 'rgba(82, 196, 26, 0.3)'
                            }, {
                                offset: 1,
                                color: 'rgba(82, 196, 26, 0.1)'
                            }]
                        }
                    }
                });
                legendData.push('复利收益率');
                yAxis.push({
                    type: 'value',
                    name: '复利收益率 (%)',
                    axisLabel: {
                        formatter: '{value}%'
                    },
                    position: hasData.singleton ? 'right' : 'left'
                });
            }

            // 如果两个系列都没有数据，显示无数据提示
            if (series.length === 0) {
                chart.setOption({
                    title: {
                        text: '无有效收益率数据',
                        left: 'center',
                        top: 'center',
                        textStyle: {
                            fontSize: 16,
                            color: '#999'
                        }
                    }
                });
                return;
            }

            const option = {
                title: {
                    text: '单复利收益率对比',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    formatter: function (params) {
                        const yearIndex = params[0].dataIndex;
                        let tooltipHtml = `第${years[yearIndex]}年<br/>`;

                        // 为所有系列添加提示信息，但只有当该系列有数据时
                        params.forEach(param => {
                            if (param.value !== null && param.value !== undefined) {
                                tooltipHtml += `${param.seriesName}: ${param.value.toFixed(2)}%<br/>`;
                            }
                        });

                        return tooltipHtml;
                    }
                },
                legend: {
                    data: legendData,
                    bottom: 10
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '15%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: years,
                    name: '',
                    axisLabel: {
                        show: false
                    }
                },
                yAxis: yAxis,
                series: series
            };

            chart.setOption(option);
        } catch (error) {
            console.error('初始化利率图表失败:', error);
        }
    };

    /**
     * 初始化现金价值图表
     * @param {string} chartId 图表ID
     * @param {Array} data 图表数据
     */
    const initCashValueChart = (chartId, data) => {
        try {
            // 获取DOM元素
            const chartDom = document.getElementById(chartId);
            if (!chartDom) {
                console.error(`找不到图表容器元素: ${chartId}`);
                return;
            }

            // 确保容器尺寸
            ensureContainerSize(chartDom);

            // 如果已经有图表实例，先销毁
            if (chartInstances.value[chartId]) {
                try {
                    chartInstances.value[chartId].dispose();
                } catch (e) {
                    console.error('清理旧图表实例失败:', e);
                }
            }

            // 初始化图表
            const chart = echarts.init(chartDom);
            chartInstances.value[chartId] = chart;

            if (!data || data.length === 0) {
                chart.setOption({
                    title: {
                        text: '无有效现金价值数据',
                        left: 'center',
                        top: 'center',
                        textStyle: {
                            fontSize: 16,
                            color: '#999'
                        }
                    }
                });
                return;
            }

            // 准备图表数据
            const years = data.map(item => item.year);

            // 检查数据字段是否存在
            const hasData = {
                surrender: data.some(item => item.surrender !== null && item.surrender !== undefined),
                totalPremium: data.some(item => (item.totalPremium !== null && item.totalPremium !== undefined) ||
                    (item.totalPremum !== null && item.totalPremum !== undefined)),
                deathBenefitTotal: data.some(item => item.deathBenefitTotal !== null && item.deathBenefitTotal !== undefined),
                totalWithdrawableCash: data.some(item => item.totalWithdrawableCashAndInterest !== null &&
                    item.totalWithdrawableCashAndInterest !== undefined),
                insuredSingleInterest: data.some(item => item.insuredSingleInterest !== null && item.insuredSingleInterest !== undefined),
                insuredIRR: data.some(item => item.insuredIRR !== null && item.insuredIRR !== undefined)
            };

            // 只处理非null的数据
            const surrenderValues = hasData.surrender ? data.map(item =>
                item.surrender !== null && item.surrender !== undefined ? Number(item.surrender) : null) : [];

            // 支持新旧字段名称，优先使用新字段名
            const totalPremiums = hasData.totalPremium ? data.map(item => {
                // 优先使用新字段名，如果不存在则使用旧字段名
                const value = item.totalPremium !== undefined ? item.totalPremium : item.totalPremum;
                return value !== null && value !== undefined ? Number(value) : null;
            }) : [];

            // 身故总额数据
            const deathBenefitValues = hasData.deathBenefitTotal ? data.map(item =>
                item.deathBenefitTotal !== null && item.deathBenefitTotal !== undefined ?
                    Number(item.deathBenefitTotal) : null) : [];

            // 可提取现金数据
            const withdrawableCashValues = hasData.totalWithdrawableCash ? data.map(item =>
                item.totalWithdrawableCashAndInterest !== null && item.totalWithdrawableCashAndInterest !== undefined ?
                    Number(item.totalWithdrawableCashAndInterest) : null) : [];

            // 准备系列数据
            const series = [];
            const legendData = [];

            // 只添加有数据的系列
            if (hasData.totalPremium) {
                series.push({
                    name: '累计保费',
                    type: 'line',
                    data: totalPremiums,
                    itemStyle: {
                        color: '#1890ff'
                    }
                });
                legendData.push('累计保费');
            }

            if (hasData.surrender) {
                series.push({
                    name: '现金价值',
                    type: 'line',
                    data: surrenderValues,
                    itemStyle: {
                        color: '#52c41a'
                    },
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [{
                                offset: 0,
                                color: 'rgba(82, 196, 26, 0.3)'
                            }, {
                                offset: 1,
                                color: 'rgba(82, 196, 26, 0.1)'
                            }]
                        }
                    }
                });
                legendData.push('现金价值');
            }

            // 如果有身故总额数据，添加到图表中
            if (hasData.deathBenefitTotal) {
                series.push({
                    name: '身故总额',
                    type: 'line',
                    data: deathBenefitValues,
                    itemStyle: {
                        color: '#ff4d4f'
                    }
                });
                legendData.push('身故总额');
            }

            // 如果有可提取现金数据，添加到图表中
            if (hasData.totalWithdrawableCash) {
                series.push({
                    name: '累积可提取现金利息',
                    type: 'line',
                    data: withdrawableCashValues,
                    itemStyle: {
                        color: '#722ed1'
                    }
                });
                legendData.push('累积可提取现金利息');
            }

            const option = {
                title: {
                    text: '现金价值与累计保费对比',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    formatter: function (params) {
                        const yearIndex = params[0].dataIndex;
                        let tooltipHtml = `第${years[yearIndex]}年<br/>`;

                        // 为所有系列添加提示信息，但只有当该系列有数据时
                        params.forEach(param => {
                            if (param.value !== null && param.value !== undefined) {
                                tooltipHtml += `${param.seriesName}: ${formatNumber(param.value)}元<br/>`;
                            }
                        });

                        return tooltipHtml;
                    }
                },
                legend: {
                    data: legendData,
                    bottom: 10
                },
                grid: {
                    left: '3%',
                    right: '10%',
                    bottom: '15%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: years,
                    name: '',
                    axisLabel: {
                        show: false
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '金额 (元)',
                    axisLabel: {
                        formatter: function (value) {
                            return formatNumber(value);
                        }
                    }
                },
                series: series
            };

            chart.setOption(option);
        } catch (error) {
            console.error('初始化现金价值图表失败:', error);
        }
    };

    /**
     * 初始化所有图表
     * @param {number} tabIndex 当前选项卡索引
     * @param {Array} data 当前选项卡的数据
     */
    const initCharts = (tabIndex, data) => {
        loading.value = true;

        try {
            const interestChartId = `interest-rate-chart-${tabIndex}`;
            const cashValueChartId = `cash-value-chart-${tabIndex}`;

            const filteredData = data?.filter(item => {
                // 支持新旧字段名称，优先使用新字段名
                const singleInterestValue = item.singleInterest !== undefined ? Number(item.singleInterest) : Number(item.singleton);

                // 处理irr值，可能是"NaN"字符串
                const irrValue = item.irr !== undefined ? item.irr : item.IRR;
                const irrNum = typeof irrValue === 'string' && irrValue === 'NaN' ? 0 : Number(irrValue);

                // 只要有单利值就显示数据，不再强制要求IRR有效
                return !isNaN(singleInterestValue) && singleInterestValue >= 0;
            }) || [];

            // 使用requestAnimationFrame确保在浏览器绘制前更新
            window.requestAnimationFrame(() => {
                initInterestRateChart(interestChartId, filteredData);

                window.requestAnimationFrame(() => {
                    initCashValueChart(cashValueChartId, filteredData);
                    loading.value = false;
                });
            });
        } catch (error) {
            console.error('初始化图表失败:', error);
            loading.value = false;
        }
    };

    /**
     * 重新调整图表大小
     */
    const resizeCharts = () => {
        // 遍历所有图表实例并调整大小
        Object.entries(chartInstances.value).forEach(([chartId, chartInstance]) => {
            if (chartInstance) {
                try {
                    const chartDom = document.getElementById(chartId);
                    if (chartDom && chartDom.clientWidth > 0 && chartDom.clientHeight > 0) {
                        chartInstance.resize();
                    } else if (chartDom) {
                        // 尝试设置明确的尺寸
                        ensureContainerSize(chartDom);
                        setTimeout(() => {
                            try {
                                chartInstance.resize();
                            } catch (e) {
                                console.error(`重置图表大小失败: ${chartId}`, e);
                            }
                        }, 100);
                    }
                } catch (error) {
                    console.error(`重置图表大小失败: ${chartId}`, error);
                }
            }
        });
    };

    /**
     * 监听窗口大小变化，重绘图表
     */
    const handleResize = () => {
        resizeCharts();
    };

    // 组件挂载时添加窗口大小变化监听
    onMounted(() => {
        window.addEventListener('resize', handleResize);
    });

    // 组件卸载时移除监听并清理图表实例
    onUnmounted(() => {
        window.removeEventListener('resize', handleResize);

        // 清理所有图表实例
        Object.values(chartInstances.value).forEach(chart => {
            if (chart) {
                try {
                    chart.dispose();
                } catch (e) {
                    console.error('清理图表实例失败:', e);
                }
            }
        });
        chartInstances.value = {};
    });

    return {
        loading,
        chartInstances,
        initCharts,
        resizeCharts,
        initInterestRateChart,
        initCashValueChart
    };
} 