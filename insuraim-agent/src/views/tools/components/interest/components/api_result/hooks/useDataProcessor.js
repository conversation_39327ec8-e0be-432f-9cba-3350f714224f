import { ref, computed, watch } from 'vue';

/**
 * 数据处理相关的状态和方法
 * @param {Object} props 组件props，包含apiCalculationResult
 * @returns {Object} 数据处理相关的状态和方法
 */
export function useDataProcessor(props) {
    // 当前选中的tab索引
    const currentTabIndex = ref(0);

    // 保存原始API数据和压力测试数据
    const originalApiData = ref(null);
    const stressTestData = ref(null);

    // 压力测试开关
    const enableStressTest = ref(true);

    // 过滤表格数据，去除单利<0的行
    const filterTableData = (data) => {
        if (!data) return [];
        // return data.filter(item => {
        //     const singleton = Number(item.singleton);
        //     const irr = Number(item.IRR);
        //     return singleton >= 0 && irr > 0;
        // });
        return data.filter(item => {
            // 支持新旧字段名称，优先使用新字段名
            const singleInterestValue = item.singleInterest !== undefined ? Number(item.singleInterest) : Number(item.singleton);
            // 只要单利值有效即可，不再要求复利值有效
            return !isNaN(singleInterestValue) && singleInterestValue >= 0;
        });
    };

    // 获取当前tab的数据
    const currentTabData = computed(() => {
        // 如果有压力测试数据且压力测试已启用，则使用压力测试数据
        const dataSource = enableStressTest.value && stressTestData.value ?
            stressTestData.value : props.apiCalculationResult;

        if (!dataSource?.irr || dataSource.irr.length === 0) {
            return null;
        }

        // 确保索引在有效范围内
        const index = Math.min(currentTabIndex.value, dataSource.irr.length - 1);
        // 不是压力测试模式 ，筛选        
        return dataSource.irr[index];
    });

    // 获取最后一年的数据
    const lastYearData = computed(() => {
        if (!currentTabData.value || !currentTabData.value.list || currentTabData.value.list.length === 0) {
            return null;
        }

        // 过滤数据，去除单利<0或复利==0的行
        const filteredList = filterTableData(currentTabData.value.list);

        if (filteredList.length === 0) {
            return currentTabData.value.list[currentTabData.value.list.length - 1]; // 如果过滤后没有数据，返回原始的最后一项
        }

        // 获取过滤后列表中的最后一项
        return filteredList[filteredList.length - 1];
    });

    // 计算总收益（现金价值 - 总保费）
    const totalProfit = computed(() => {
        if (!lastYearData.value) {
            return 0;
        }

        // 支持新旧字段名称，优先使用新字段名
        const singleInterestValue = lastYearData.value.singleInterest !== undefined ?
            Number(lastYearData.value.singleInterest) : Number(lastYearData.value.singleton);

        // 只检查单利值是否有效，不再要求复利值有效
        if (isNaN(singleInterestValue) || singleInterestValue < 0) {
            return 0;
        }

        const surrender = Number(lastYearData.value.surrender || 0);
        // 支持新旧字段名称，优先使用新字段名
        const totalPremium = lastYearData.value.totalPremium !== undefined ?
            Number(lastYearData.value.totalPremium) : Number(lastYearData.value.totalPremum || 0);

        return surrender - totalPremium
    });

    // 计算压力测试结果
    const calculateStressTest = (originalData, dividendRate) => {


        return originalData;
    };

    // 获取产品信息的方法
    const getProductInfo = (item) => {
        // 如果有 dynamicIRRData 属性，说明这是新格式的数据
        if (item.dynamicIRRData) {
            return {
                gender: item.dynamicIRRData.gender,
                assumedAge: item.dynamicIRRData.assumedAge,
                smoking_status: item.dynamicIRRData.smoking_status,
                annualPremium: item.dynamicIRRData.annualPremium,
                paymentMode: item.dynamicIRRData.paymentMode,
                coverageTerm: item.dynamicIRRData.coverageTerm,
                premiumPaymentTerm: item.dynamicIRRData.premiumPaymentTerm,
                policyCurrency: item.dynamicIRRData.policyCurrency
            };
        }

        // 兼容旧格式的数据
        return {
            gender: item.gender,
            assumedAge: item.years || item.assumedAge,
            smoking_status: item.smoking_status,
            annualPremium: item.annualPremium,
            paymentMode: item.paymentMode,
            coverageTerm: item.coverageTerm,
            premiumPaymentTerm: item.premiumPaymentTerm,
            policyCurrency: item.policyCurrency,
            surrender: item.surrender,
            terminalBonusNg: item.terminalBonusNg
        };
    };

    // 处理tab切换事件
    const handleTabChange = (activeKey) => {
        currentTabIndex.value = Number(activeKey);
        return currentTabIndex.value;
    };

    // 监听apiCalculationResult变化，重置当前tab索引
    watch(() => props.apiCalculationResult, () => {
        currentTabIndex.value = 0;
        // 重置原始数据，但不重置压力测试状态
        originalApiData.value = null;
        stressTestData.value = null;
        // 注意：不再重置enableStressTest，保留当前状态
    }, { deep: true });

    // 设置压力测试状态
    const setStressTestStatus = (status, rate, apiData) => {
        enableStressTest.value = status;

        // 如果启用压力测试且有API数据，则计算压力测试结果
        if (status && apiData) {
            stressTestData.value = calculateStressTest(apiData, rate);
        } else {
            stressTestData.value = null;
        }
    };

    return {
        currentTabIndex,
        originalApiData,
        stressTestData,
        enableStressTest,
        currentTabData,
        lastYearData,
        totalProfit,
        filterTableData,
        calculateStressTest,
        getProductInfo,
        handleTabChange,
        setStressTestStatus
    };
} 