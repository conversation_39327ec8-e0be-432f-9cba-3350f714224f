import { formatNumber } from '@/utils/number';
import { log } from 'sockjs-client/dist/sockjs';

/**
 * 表格导出相关的工具函数
 * @returns {Object} 表格导出相关的函数
 */
export function useTableExport() {
    /**
     * 将颜色转换为ARGB格式（用于Excel）
     * @param {String} color - 十六进制颜色格式 (#RRGGBB)
     * @returns {String} ARGB格式的颜色 (FFRRGGBB)
     */
    const hexToARGB = (color) => {
        if (!color || typeof color !== 'string') return 'FFE6F7FF'; // 默认颜色

        // 处理CSS变量
        if (color.startsWith('var(--')) {
            // 获取CSS变量值
            const varName = color.match(/var\(([^)]+)\)/)[1];
            const computedValue = getComputedStyle(document.documentElement).getPropertyValue(varName);
            if (computedValue) {
                color = computedValue.trim();
            } else {
                return 'FFE6F7FF'; // 默认颜色
            }
        }

        // 移除#前缀
        color = color.replace('#', '');

        // 确保是6位十六进制
        if (color.length === 3) {
            color = color[0] + color[0] + color[1] + color[1] + color[2] + color[2];
        }

        // 添加不透明度（FF）前缀
        return 'FF' + color.toUpperCase();
    };

    /**
     * 根据主题生成Excel表头样式
     * @param {String} theme - 主题名称
     * @param {String} customColor - 自定义主题颜色（仅当theme为'custom'时使用）
     * @returns {Object} 表头样式对象
     */
    const getExcelHeaderStyleForTheme = (theme, customColor) => {
        // 获取主题对应的颜色
        let themeColor;
        let textColor = 'FFFFFF'; // 默认白色文字

        switch (theme) {
            case 'default':
                themeColor = '374151'; // 默认主题表头背景颜色
                break;
            case 'primary':
                themeColor = '2563EB'; // 商务蓝主题表头背景颜色
                break;
            case 'success':
                themeColor = '16A34A'; // 成功绿主题表头背景颜色
                break;
            case 'warning':
                themeColor = 'D97707'; // 警告黄主题表头背景颜色
                break;
            case 'danger':
                themeColor = 'DC2625'; // 危险红主题表头背景颜色
                break;
            case 'custom':
                if (customColor) {
                    themeColor = customColor.replace('#', '');

                    // 计算文字颜色（黑色或白色，取决于背景亮度）
                    const r = parseInt(themeColor.substring(0, 2), 16);
                    const g = parseInt(themeColor.substring(2, 4), 16);
                    const b = parseInt(themeColor.substring(4, 6), 16);
                    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
                    textColor = luminance > 0.5 ? '000000' : 'FFFFFF';
                } else {
                    themeColor = '8B5CF6'; // 默认紫色
                }
                break;
            default:
                themeColor = '374151';
        }

        // 返回表头样式
        return {
            font: { bold: true, color: { argb: textColor }, name: 'PingFang SC' },
            fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: themeColor } },
            alignment: { vertical: 'middle', horizontal: 'center' },
            border: {
                top: { style: 'thin', color: { argb: 'D9D9D9' } },
                left: { style: 'thin', color: { argb: 'D9D9D9' } },
                bottom: { style: 'thin', color: { argb: 'D9D9D9' } },
                right: { style: 'thin', color: { argb: 'D9D9D9' } }
            }
        };
    };

    /**
     * 根据主题生成Excel单元格样式
     * @param {String} theme - 主题名称
     * @param {String} customColor - 自定义主题颜色（仅当theme为'custom'时使用）
     * @returns {Object} 单元格样式对象和高亮行样式对象
     */
    const getExcelCellStyleForTheme = (theme, customColor) => {
        // 获取主题对应的颜色
        let themeColor;
        let normalRowColor;
        let highlightRowColor;
        let textColor = '303540'; // 文字颜色统一为 #303540

        switch (theme) {
            case 'default':
                themeColor = '374151';
                normalRowColor = 'FFFFFF'; // 默认主题普通行背景颜色
                highlightRowColor = '91969E'; // 默认主题高亮行背景颜色
                break;
            case 'primary':
                themeColor = '2563EB';
                normalRowColor = 'FFFFFF'; // 商务蓝主题普通行背景颜色
                highlightRowColor = '89A5F3'; // 商务蓝主题高亮行背景颜色
                break;
            case 'success':
                themeColor = '16A34A';
                normalRowColor = 'FFFFFF'; // 成功绿主题普通行背景颜色
                highlightRowColor = '87C99B'; // 成功绿主题高亮行背景颜色
                break;
            case 'warning':
                themeColor = 'D97707';
                normalRowColor = 'FFFFFF'; // 警告黄主题普通行背景颜色
                highlightRowColor = 'E9B07F'; // 警告黄主题高亮行背景颜色
                break;
            case 'danger':
                themeColor = 'DC2625';
                normalRowColor = 'FFFFFF'; // 危险红主题普通行背景颜色
                highlightRowColor = 'E78389'; // 危险红主题高亮行背景颜色
                break;
            case 'custom':
                if (customColor) {
                    themeColor = customColor.replace('#', '');
                    // 自定义主题使用默认的普通行和高亮行颜色
                    normalRowColor = 'FFFFFF';
                    highlightRowColor = 'BAE7FF';
                } else {
                    themeColor = '8B5CF6'; // 默认紫色
                    normalRowColor = 'FFFFFF'; // 淡紫色背景
                    highlightRowColor = 'D8BFD8'; // 高亮淡紫色
                }
                break;
            default:
                themeColor = '374151';
                normalRowColor = 'FFFFFF';
                highlightRowColor = '91969E';
        }

        // 基本样式（普通行）
        const cellStyle = {
            alignment: { vertical: 'middle', horizontal: 'center' },
            font: { color: { argb: textColor }, name: 'PingFang SC' },
            fill: {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: normalRowColor }
            },
            border: {
                top: { style: 'thin', color: { argb: 'D9D9D9' } },
                left: { style: 'thin', color: { argb: 'D9D9D9' } },
                bottom: { style: 'thin', color: { argb: 'D9D9D9' } },
                right: { style: 'thin', color: { argb: 'D9D9D9' } }
            },
        };

        // 高亮行样式
        const highlightCellStyle = {
            ...cellStyle,
            fill: {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: highlightRowColor }
            }
        };
        return { cellStyle, highlightCellStyle };
    };

    /**
     * 根据columnsVisible过滤列定义
     * @param {Object} columnsVisible - 列可见性配置
     * @param {Array} tableData - 表格数据，用于检查列是否有数据
     * @returns {Array} 过滤后的列定义
     */
    const getFilteredColumns = (columnsVisible, tableData = []) => {
        // 检查列是否有数据的辅助函数
        const hasColumnData = (columnKey) => {
            if (!tableData || tableData.length === 0) return true; // 如果没有数据，默认所有列都可用

            // 检查所有行中是否至少有一行该列的值不为null
            return tableData.some(row => {
                return row[columnKey] !== null && row[columnKey] !== undefined;
            });
        };

        const allColumns = [
            { title: '年龄', dataIndex: 'age', visible: columnsVisible.age && hasColumnData('age') },
            { title: '退保倍数', dataIndex: 'multiple', visible: columnsVisible.multiple && hasColumnData('multiple') },
            { title: '保单持有时间', dataIndex: 'year', visible: columnsVisible.year && hasColumnData('year') },
            { title: '总保费', dataIndex: 'totalPremum', visible: columnsVisible.totalPremum && hasColumnData('totalPremum') },
            { title: '净现金流', dataIndex: 'netflow', visible: columnsVisible.netflow && hasColumnData('netflow') },
            { title: '保证退保现金', dataIndex: 'insuredAmount', visible: columnsVisible.insuredAmount && hasColumnData('insuredAmount') },
            { title: '终期红利(非保证)', dataIndex: 'terminalBonusNg', visible: columnsVisible.terminalBonusNg && hasColumnData('terminalBonusNg') },
            { title: '复归红利(非保证)', dataIndex: 'cashValueOfReversionaryBonusNg', visible: columnsVisible.cashValueOfReversionaryBonusNg && hasColumnData('cashValueOfReversionaryBonusNg') },
            { title: '退保总额', dataIndex: 'surrender', visible: columnsVisible.surrender && hasColumnData('surrender') },
            { title: '单利', dataIndex: 'singleton', visible: columnsVisible.singleton && hasColumnData('singleton') },
            { title: '复利', dataIndex: 'IRR', visible: columnsVisible.IRR && hasColumnData('IRR') },
            { title: '身故保证金额', dataIndex: 'deathBenefitG', visible: columnsVisible.deathBenefitG && hasColumnData('deathBenefitG') },
            { title: '身故终期红利', dataIndex: 'deathBenefitTerminalBonusNg', visible: columnsVisible.deathBenefitTerminalBonusNg && hasColumnData('deathBenefitTerminalBonusNg') },
            { title: '身故保险金(总)', dataIndex: 'deathBenefitTotal', visible: columnsVisible.deathBenefitTotal && hasColumnData('deathBenefitTotal') },
            { title: '累积可提取现金利息总额', dataIndex: 'totalWithdrawableCashAndInterest', visible: columnsVisible.totalWithdrawableCashAndInterest && hasColumnData('totalWithdrawableCashAndInterest') },
            { title: '每月年金及利息', dataIndex: 'totalMonthlyAnnuityAndInterest', visible: columnsVisible.totalMonthlyAnnuityAndInterest && hasColumnData('totalMonthlyAnnuityAndInterest') },
        ];

        // 返回visible为true的列
        return allColumns.filter(col => col.visible !== false);
    };

    /**
     * 根据表格设置过滤数据
     * @param {Array} data - 原始数据
     * @param {Number} rowStep - 行步长
     * @param {Number} rowStartIndex - 起始行索引
     * @returns {Array} 过滤后的数据
     */
    const getFilteredData = (data, rowStep, rowStartIndex = 0) => {
        if (!data) return [];

        // 应用行步长过滤
        if (rowStep > 1) {
            return data.filter((_, index) => (index + rowStartIndex) % rowStep === 0);
        }

        return data;
    };

    /**
     * 准备导出数据
     * @param {Array} data - 原始数据
     * @param {Array} columns - 列定义
     * @param {Array} highlightRules - 高亮规则
     * @param {Boolean} enableHighlight - 是否启用高亮
     * @returns {Array} 格式化后的数据
     */
    const prepareExportData = (data, columns, highlightRules, enableHighlight) => {
        if (!data || !columns) return [];

        return data.map(item => {
            const exportItem = {};

            columns.forEach(column => {
                let value = item[column.dataIndex];

                // 格式化数字
                if (['totalPremum', 'netflow', 'insuredAmount', 'terminalBonusNg', 'cashValueOfReversionaryBonusNg', 'surrender'].includes(column.dataIndex) && value) {
                    value = formatNumber(value, { decimals: 2 });
                }

                // 格式化百分比
                if (['singleton', 'IRR'].includes(column.dataIndex) && !isNaN(value)) {
                    value = value + '%';
                }

                exportItem[column.dataIndex] = value;
            });
            console.log('highlightRules', highlightRules);
            // 添加高亮标记
            if (enableHighlight && highlightRules && highlightRules.length > 0) {
                // 过滤出启用的规则
                const enabledRules = [...highlightRules]
                    .filter(rule => rule.enabled)
                    .sort((a, b) => a.priority - b.priority);

                // 如果有启用的规则，检查是否所有规则都匹配
                if (enabledRules.length > 0) {
                    let allRulesMatch = true;

                    for (const rule of enabledRules) {
                        const fieldValue = item[rule.field];
                        // 如果字段值为null或undefined，则该规则不匹配
                        if (fieldValue === null || fieldValue === undefined) {
                            allRulesMatch = false;
                            break;
                        }

                        let matches = false;

                        switch (rule.operator) {
                            case '>':
                                matches = fieldValue > rule.value;
                                break;
                            case '>=':
                                matches = fieldValue >= rule.value;
                                break;
                            case '<':
                                matches = fieldValue < rule.value;
                                break;
                            case '<=':
                                matches = fieldValue <= rule.value;
                                break;
                            case '==':
                                matches = fieldValue == rule.value;
                                break;
                            default:
                                matches = false;
                        }

                        // 如果任何一个规则不匹配，整行就不高亮
                        if (!matches) {
                            allRulesMatch = false;
                            break;
                        }
                    }

                    // 所有规则都匹配，添加高亮标记
                    if (allRulesMatch) {
                        exportItem._highlight = true;
                        exportItem._matchedRule = enabledRules[0];
                    }
                    delete exportItem.multiple;
                }
            }

            return exportItem;
        });
    };

    /**
     * 创建适合导出的Excel选项
     * @param {String} theme - 当前主题
     * @param {String} customColor - 自定义主题颜色
     * @param {Boolean} enableHighlight - 是否启用高亮
     * @returns {Object} Excel导出选项
     */
    const createExcelOptions = (theme, customColor, enableHighlight, productName, productInfo) => {
        const headerStyle = getExcelHeaderStyleForTheme(theme, customColor);
        const { cellStyle, highlightCellStyle } = getExcelCellStyleForTheme(theme, customColor);

        // 返回Excel导出选项
        return {
            sheetName: '单复利计算结果',
            autoWidth: true,
            headerStyle,
            cellStyle,
            highlightCellStyle,
            enableHighlight,
            theme,
            productName,
            productInfo
        };
    };

    /**
     * 导出表格到Excel
     * @param {Object} exportExcelFunc - 导出Excel的函数
     * @param {Object} options - 导出选项
     * @param {Array} data - 原始数据
     * @param {Object} columnsVisible - 列可见性配置
     * @param {String} theme - 当前主题
     * @param {String} customColor - 自定义主题颜色
     * @param {Number} rowStep - 行步长
     * @param {Boolean} enableHighlight - 是否启用高亮
     * @param {String} filename - 文件名
     * @returns {Object} 包含filteredColumns的结果对象
     */
    const exportTableToExcel = ({
        exportExcelFunc,
        data,
        columnsVisible,
        theme = 'default',
        customColor = null,
        rowStep = 1,
        enableHighlight = true,
        filename = '单复利计算结果.xlsx',
        productInfo = null,
        productName = null,
        highlightRules = []
    }) => {
        // 过滤显示的列
        const filteredColumns = getFilteredColumns(columnsVisible, data);

        // 过滤和处理数据
        const filteredData = getFilteredData(data, rowStep);

        // 准备导出数据
        const exportData = prepareExportData(filteredData, filteredColumns, highlightRules, enableHighlight);
        // 删除dataindex = multiple的列
        filteredColumns.splice(filteredColumns.findIndex(item => item.dataIndex === 'multiple'), 1);
        // 创建Excel选项
        const excelOptions = createExcelOptions(theme, customColor, enableHighlight, productName, productInfo);

        // 如果exportExcelFunc是空函数，只返回过滤后的列，不进行实际导出
        if (exportExcelFunc.toString().includes('() => { }')) {
            return { filteredColumns };
        }

        // 导出Excel
        exportExcelFunc(
            exportData,
            filteredColumns,
            filename,
            excelOptions
        );

        // 返回结果对象，包含过滤后的列
        return { filteredColumns };
    };

    return {
        hexToARGB,
        getExcelHeaderStyleForTheme,
        getExcelCellStyleForTheme,
        getFilteredColumns,
        getFilteredData,
        prepareExportData,
        createExcelOptions,
        exportTableToExcel
    };
} 