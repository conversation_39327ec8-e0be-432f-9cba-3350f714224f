import { ref, computed } from 'vue';
/**
 * 表格设置相关的状态和方法
 * @returns {Object} 表格设置相关的状态和方法
 */
export function useTableSettings() {
    // 表格编辑设置相关
    const editTableModal = ref(false);
    const tableTheme = ref('default'); // 默认主题
    const rowStepValue = ref(1); // 默认每1行显示一次
    const enableHighlight = ref(true); // 默认关闭
    const enableStressTest = ref(true); // 默认不启用压力测试
    const currentSettingTab = ref('theme'); // 当前设置选项卡
    const customThemeColor = ref('#8B5CF6'); // 自定义主题颜色，默认紫色

    // 高亮规则数组
    const highlightRules = ref([
        {
            id: 'default-multiple',
            name: '退保倍数1',
            enabled: true,
            field: 'multiple',
            operator: '==',
            value: 1,
            color: '#16a34a',
            opacity: 0.2,
            priority: 1
        },

    ]);

    // 临时设置，用于编辑中
    const tempSettings = ref({
        theme: 'default',
        rowStep: 1,
        enableHighlight: true,
        enableStressTest: false,
        dividendRate: 100,
        customThemeColor: '#8B5CF6', // 自定义主题颜色
        highlightRules: [...highlightRules.value], // 高亮规则
        columnsVisible: {
            age: true,
            year: true,
            totalPremum: true,
            netflow: true,
            insuredAmount: true,
            terminalBonusNg: true,
            cashValueOfReversionaryBonusNg: true,
            surrender: true,
            singleton: true,
            IRR: true,
            insuredSingleInterest: true,
            insuredIRR: true,
            deathBenefitG: true,
            deathBenefitTerminalBonusNg: true,
            deathBenefitTotal: true,
            totalWithdrawableCashAndInterest: true,
            totalMonthlyAnnuityAndInterest: true
        }
    });

    // 表格列可见性
    const columnsVisible = computed(() => {
        return tempSettings.value.columnsVisible;
    });

    // 所有可用的主题列表
    const themeOptions = [
        { label: '默认主题', value: 'default' },
        { label: '主要主题', value: 'primary' },
        { label: '成功主题', value: 'success' },
        { label: '警告主题', value: 'warning' },
        { label: '危险主题', value: 'danger' }
    ];

    // 获取主题对应的背景颜色
    const getThemeColor = (theme) => {
        switch (theme) {
            case 'default': return '#374151';
            case 'primary': return '#0369a1';
            case 'success': return '#16a34a';
            case 'warning': return '#d97706';
            case 'danger': return '#dc2626';
            case 'custom': return customThemeColor.value;
            default: return '#374151';
        }
    };

    // 获取主题对应的文字颜色
    const getThemeTextColor = (theme) => {
        switch (theme) {
            case 'default': return '#ffffff';
            case 'primary': return '#ffffff';
            case 'success': return '#ffffff';
            case 'warning': return '#ffffff';
            case 'danger': return '#ffffff';
            case 'custom': return getContrastColor(customThemeColor.value);
            default: return '#ffffff';
        }
    };

    // 根据背景色自动计算对比色（黑/白）
    const getContrastColor = (hexColor) => {
        // 转换hex到rgb
        const r = parseInt(hexColor.substring(1, 3), 16);
        const g = parseInt(hexColor.substring(3, 5), 16);
        const b = parseInt(hexColor.substring(5, 7), 16);

        // 计算亮度
        const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

        // 根据亮度返回黑色或白色
        return luminance > 0.5 ? '#000000' : '#ffffff';
    };

    // 编辑表格设置
    const editTable = () => {
        // 打开模态框前，先同步当前的设置状态到临时变量
        tempSettings.value = {
            theme: tableTheme.value,
            rowStep: rowStepValue.value,
            enableHighlight: enableHighlight.value,
            enableStressTest: enableStressTest.value,
            dividendRate: tempSettings.value.dividendRate || 60,
            customThemeColor: customThemeColor.value, // 记录自定义主题颜色
            highlightRules: [...highlightRules.value], // 复制高亮规则
            columnsVisible: tempSettings.value.columnsVisible // 保留现有的列设置，不重置
        };

        currentSettingTab.value = 'theme'; // 默认显示主题设置选项卡
        editTableModal.value = true;
    };

    // 切换设置选项卡
    const switchSettingTab = (tab) => {
        currentSettingTab.value = tab;
    };

    // 取消表格设置
    const cancelTableSettings = () => {
        // 不做任何更改，直接关闭模态框
        editTableModal.value = false;
    };

    return {
        editTableModal,
        tableTheme,
        rowStepValue,
        enableHighlight,
        enableStressTest,
        currentSettingTab,
        tempSettings,
        columnsVisible,
        customThemeColor,
        highlightRules,
        themeOptions,
        getThemeColor,
        getThemeTextColor,
        getContrastColor,
        editTable,
        switchSettingTab,
        cancelTableSettings
    };
} 