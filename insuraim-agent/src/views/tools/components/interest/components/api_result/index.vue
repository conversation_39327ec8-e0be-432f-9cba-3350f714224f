<template>

    <div id="api-result-display">
        <div v-if="apiCalculationResult && apiCalculationResult.irr && apiCalculationResult.irr.length > 0">
            <!-- 统计卡片 -->
            <StatisticsCards v-if="currentTabData" :lastYearData="lastYearData" :totalProfit="totalProfit"
                :theme="tableTheme" :currency="currency" ref="statisticsCardsRef"> </StatisticsCards>

            <a-tabs @change="handleTabChange">
                <a-tab-pane :key="index" v-for="(item, index) in apiCalculationResult.irr">
                    <div class="mb-4">
                        <!-- 单复利折线图 -->
                        <InterestRateChart :data="currentTabData?.list" :tabIndex="currentTabIndex" />

                        <!-- 现金价值与累积保费折线图 -->
                        <CashValueChart :data="currentTabData?.list" :tabIndex="currentTabIndex" />
                        <a-spin :spinning="stressTestLoading" :indicator="indicator" tip="压力测试计算中..."
                            class="absolute top-0 left-0 w-full h-full z-50">
                            <!-- <a-alert message="压力测试计算中..." type="info" /> -->
                            <!-- 案例参考 -->
                            <ProductInfoCard :productData="item" ref="productInfoCardRef" :currency="currency" />
                            <!-- 表格工具栏 -->
                            <TableToolbar @export="exportToExcel" @edit="editTable"
                                :productName="item.dynamicIRRData.productName" :selectedProduct="selectedProduct"
                                :apiCalculationResult="apiCalculationResult" />

                            <!-- 数据表格 -->
                            <DataTable :data="currentTabData?.list" :tableTheme="tableTheme" :rowStep="rowStepValue"
                                :enableHighlight="enableHighlight" :columnsVisible="columnsVisible"
                                :highlightRules="highlightRules" />
                        </a-spin>
                    </div>
                </a-tab-pane>
            </a-tabs>

            <!-- 表格设置模态框 -->
            <TableSettings v-model:open="editTableModal" :settings="tempSettings" @apply="applyTableSettings"
                @cancel="cancelTableSettings" :tableData="currentTabData?.list || []" />

            <!-- 压力测试模式指示器 -->
            <div v-if="enableStressTest && tempSettings.dividendRate < 100"
                class="fixed bottom-4 right-4 bg-yellow-100 text-yellow-800 px-4 py-2 rounded-md shadow-md z-50">
                <div class="flex items-center">
                    <Icon icon="material-symbols:warning" class="mr-2" />
                    <span v-if="!stressTestLoading">正在压力测试模式 - 分红实现率: {{ tempSettings.dividendRate }}%</span>

                    <a-button type="text" size="small" class="ml-2 text-yellow-800" @click="editTable">
                        <Icon icon="material-symbols:settings" />
                    </a-button>
                </div>
            </div>
        </div>
        <div v-else class="text-center py-4 text-gray-500">该产品无现金价值，请选择其他产品</div>
    </div>

</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { Icon } from '@iconify/vue';

// 导入子组件
import StatisticsCards from './StatisticsCards.vue';
import InterestRateChart from './charts/InterestRateChart.vue';
import CashValueChart from './charts/CashValueChart.vue';
import ProductInfoCard from './ProductInfoCard.vue';
import TableToolbar from './TableToolbar.vue';
import DataTable from './DataTable.vue';
import TableSettings from './settings/TableSettings.vue';

// 导入hooks
import { useTableSettings } from './hooks/useTableSettings';
import { useDataProcessor } from './hooks/useDataProcessor';
import { useChartRenderer } from './hooks/useChartRenderer';

const props = defineProps({
    apiCalculationResult: {
        type: Object,
        required: true
    },
    currency: {
        type: String,
        default: 'CNY'
    },
    selectedProduct: {
        type: Object,
        default: () => ({})
    }
});

// 定义emit事件
const emit = defineEmits(['tab-change', 'exportToExcel', 'apply-stress-test', 'reset-stress-test']);
const statisticsCardsRef = ref(null);
const productInfoCardRef = ref(null);
// 使用数据处理hook
const {
    currentTabIndex,
    originalApiData,
    stressTestData,
    enableStressTest,
    currentTabData,
    lastYearData,
    totalProfit,
    filterTableData,
    handleTabChange: dataHandleTabChange,
    setStressTestStatus
} = useDataProcessor(props);

// 使用表格设置hook
const {
    editTableModal,
    tableTheme,
    rowStepValue,
    enableHighlight,
    currentSettingTab,
    tempSettings,
    columnsVisible,
    customThemeColor,
    getThemeColor,
    getThemeTextColor,
    getContrastColor,
    highlightRules,
    editTable,
    switchSettingTab,
    cancelTableSettings
} = useTableSettings();

// 使用图表渲染hook
const {
    loading: chartLoading,
    initInterestRateChart,
    initCashValueChart
} = useChartRenderer();

// 添加压力测试加载状态
const stressTestLoading = ref(false);

// 处理tab切换事件
const handleTabChange = (activeKey) => {
    const newIndex = dataHandleTabChange(activeKey);
    // 触发tab-change事件，传递当前选中的tab索引
    emit('tab-change', newIndex);
};

// 导出Excel
const exportToExcel = () => {
    emit('exportToExcel');
};

// 应用表格设置
const applyTableSettings = (settings) => {
    console.log('应用表格设置:', settings);

    // 保存当前压力测试设置，用于后续比较
    const oldEnableStressTest = enableStressTest.value;
    const oldDividendRate = tempSettings.value.dividendRate;

    // 更新所有设置
    tableTheme.value = settings.theme;
    rowStepValue.value = settings.rowStep;
    enableHighlight.value = settings.enableHighlight;
    enableStressTest.value = settings.enableStressTest;
    customThemeColor.value = settings.customThemeColor;

    // 更新高亮规则
    if (settings.highlightRules) {
        highlightRules.value = [...settings.highlightRules];
    }
    console.log('settings', settings);
    // 检查是否是从"应用"按钮直接触发的压力测试事件
    // 如果是通过StressTestSettings.vue中的"应用"按钮触发的，会有一个特殊标记
    if (settings._fromStressTestApplyButton) {
        // 如果是从压力测试的"应用"按钮触发的，则执行API计算
        console.log('从压力测试的"应用"按钮触发的，则执行API计算');
        if (settings.enableStressTest) {
            emit('apply-stress-test', {
                enableStressTest: settings.enableStressTest,
                dividendRate: settings.dividendRate
            });
        } else {
            emit('reset-stress-test');
        }
    }
};

// 设置自定义主题CSS变量
const setCustomThemeVars = () => {
    if (tableTheme.value === 'custom' && customThemeColor.value) {
        // 解析颜色为RGB值，用于计算透明度变体
        const r = parseInt(customThemeColor.value.substring(1, 3), 16);
        const g = parseInt(customThemeColor.value.substring(3, 5), 16);
        const b = parseInt(customThemeColor.value.substring(5, 7), 16);

        // 设置CSS变量
        document.documentElement.style.setProperty('--custom-theme-color', customThemeColor.value);
        document.documentElement.style.setProperty('--custom-theme-text-color', getContrastColor(customThemeColor.value));
        document.documentElement.style.setProperty('--custom-theme-color-30', `rgba(${r}, ${g}, ${b}, 0.3)`);
        document.documentElement.style.setProperty('--custom-theme-color-60', `rgba(${r}, ${g}, ${b}, 0.6)`);
    }
};

// 监听apiCalculationResult变化
watch(() => props.apiCalculationResult, () => {
    // 如果是首次加载，延迟初始化
    if (currentTabIndex.value === 0) {
        setTimeout(() => {
            // 这里可以添加初始化逻辑
        }, 500);
    }
}, { deep: true });

// 监听主题变化，设置自定义主题CSS变量
watch(() => tableTheme.value, (newTheme) => {
    if (newTheme === 'custom') {
        setCustomThemeVars();
    }
});

// 监听自定义主题颜色变化，更新CSS变量
watch(() => customThemeColor.value, (newColor) => {
    if (tableTheme.value === 'custom') {
        setCustomThemeVars();
    }
});

// 组件挂载时
onMounted(() => {
    // 默认选中第一个tab
    currentTabIndex.value = 0;

    // 初始化图表
    setTimeout(() => {
        refreshCharts();
    }, 500);
});

/**
 * 刷新所有图表
 * 用于PDF导出后重新初始化图表
 */
const refreshCharts = () => {
    // 等待下一个渲染周期再刷新图表
    setTimeout(() => {
        // 重新获取当前tab的数据
        const charts = document.querySelectorAll('.chart-container');
        if (!charts || charts.length === 0) return;

        try {
            // 查找所有图表ID
            const chartIds = Array.from(charts).map(chart => {
                const chartElement = chart.querySelector('div[id^="interest-rate-chart-"], div[id^="cash-value-chart-"]');
                return chartElement?.id;
            }).filter(Boolean);

            // 重新初始化图表
            chartIds.forEach(chartId => {
                if (chartId.includes('interest-rate-chart-')) {
                    // 找到对应的InterestRateChart组件并调用其初始化方法
                    const tabIndex = parseInt(chartId.replace('interest-rate-chart-', ''));
                    if (!isNaN(tabIndex) && currentTabData.value) {
                        const chartContainer = document.getElementById(chartId);
                        if (chartContainer) {
                            // 使用useChartRenderer中的方法重新渲染
                            initInterestRateChart(chartId, currentTabData.value.list);
                        }
                    }
                } else if (chartId.includes('cash-value-chart-')) {
                    // 找到对应的CashValueChart组件并调用其初始化方法
                    const tabIndex = parseInt(chartId.replace('cash-value-chart-', ''));
                    if (!isNaN(tabIndex) && currentTabData.value) {
                        const chartContainer = document.getElementById(chartId);
                        if (chartContainer) {
                            // 使用useChartRenderer中的方法重新渲染
                            initCashValueChart(chartId, currentTabData.value.list);
                        }
                    }
                }
            });
        } catch (error) {
            console.error('刷新图表失败:', error);
        }
    }, 100);
};


// 恢复原始数据
const restoreOriginalData = () => {
    console.log('恢复原始数据');
    // 这里应该实现恢复原始数据的逻辑
    // 暂时保留为空函数，后续可以实现
};

// 同步压力测试状态
const syncStressTestStatus = (status, rate) => {
    console.log('同步压力测试状态:', status, rate);

    // 保存原始API数据，如果还没有保存的话
    if (!originalApiData.value && props.apiCalculationResult) {
        originalApiData.value = JSON.parse(JSON.stringify(props.apiCalculationResult));
    }

    // 更新临时设置中的压力测试相关值
    tempSettings.value.enableStressTest = status;
    tempSettings.value.dividendRate = rate;

    // 使用数据处理hook中的方法设置压力测试状态
    setStressTestStatus(status, rate, originalApiData.value || props.apiCalculationResult);
};

/**
 * 设置压力测试加载状态
 * @param {boolean} loading 是否加载中
 */
const setStressTestLoading = (loading) => {
    stressTestLoading.value = loading;
};

// 导出方法供父组件调用
defineExpose({
    refreshCharts,
    tableTheme,
    rowStepValue,
    enableHighlight,
    customThemeColor,
    columnsVisible,
    highlightRules,
    syncStressTestStatus,
    setStressTestLoading
});
</script>

<style scoped>
/* 可以添加必要的样式 */
</style>