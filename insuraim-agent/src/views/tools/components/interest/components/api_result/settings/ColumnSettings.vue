<template>
    <div>
        <!-- 列显示设置 -->
        <div v-if="currentSettingTab === 'columns'" class="setting-section">
            <h3 class="text-lg font-medium mb-4">列显示设置</h3>
            <div class="grid grid-cols-2 gap-2">
                <div v-for="column in columnDefs" :key="column.key"
                    class="py-3 px-3 rounded-[4px] flex items-center justify-center" :class="[
                        tempSettings.columnsVisible[column.key] && hasColumnData(column.key) ? 'text-primary' : 'text-gray-800',
                        hasColumnData(column.key) ? 'cursor-pointer' : 'cursor-not-allowed opacity-50'
                    ]" :style="{
                        border: tempSettings.columnsVisible[column.key] && hasColumnData(column.key)
                            ? '1px solid var(--primary)' : '1px solid transparent',
                        backgroundColor: 'transparent'
                    }" @click="toggleColumnVisibility(column.key)">
                    <span class="text-sm text-inherit" :style="tempSettings.columnsVisible[column.key] && hasColumnData(column.key)
                        ? { color: 'var(--primary)' } : {}">
                        {{ column.title }}
                        <span v-if="!hasColumnData(column.key)" class="text-xs text-gray-500 ml-1">(无数据)</span>
                    </span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

const props = defineProps({
    currentSettingTab: {
        type: String,
        required: true
    },
    tempSettings: {
        type: Object,
        required: true
    },
    tableData: {
        type: Array,
        default: () => []
    }
});

// 检查列是否有数据
const hasColumnData = (columnKey) => {
    if (!props.tableData || props.tableData.length === 0) return true; // 如果没有数据，默认所有列都可用

    // 兼容新旧字段名
    const fieldMapping = {
        'totalPremum': ['totalPremum', 'totalPremium'],
        'singleton': ['singleton', 'singleInterest'],
        'IRR': ['IRR', 'irr'],
        'netflow': ['netflow', 'netCashFlow'],
    };

    // 检查所有行中是否至少有一行该列的值不为null
    return props.tableData.some(row => {
        // 如果有字段映射，检查所有可能的字段名
        if (fieldMapping[columnKey]) {
            return fieldMapping[columnKey].some(field => row[field] !== null && row[field] !== undefined);
        }
        // 否则直接检查字段名
        return row[columnKey] !== null && row[columnKey] !== undefined;
    });
};

// 切换列可见性，仅当有数据时可切换
const toggleColumnVisibility = (columnKey) => {
    if (hasColumnData(columnKey)) {
        props.tempSettings.columnsVisible[columnKey] = !props.tempSettings.columnsVisible[columnKey];
    }
};

// 列定义，用于v-for渲染
const columnDefs = [
    { key: 'age', title: '年龄' },
    { key: 'year', title: '保单持有时间' },
    { key: 'totalPremum', title: '总保费' },
    { key: 'netflow', title: '净现金流' },
    { key: 'insuredAmount', title: '保证退保现金' },
    { key: 'terminalBonusNg', title: '终期红利(非保证)' },
    { key: 'cashValueOfReversionaryBonusNg', title: '复归红利(非保证)' },
    { key: 'surrender', title: '退保总额' },
    { key: 'singleton', title: '单利' },
    { key: 'IRR', title: '复利' },
    { key: 'insuredSingleInterest', title: '保证单利' },
    { key: 'insuredIRR', title: '保证复利' },
    { key: 'deathBenefitG', title: '身故保证金额' },
    { key: 'deathBenefitTerminalBonusNg', title: '身故终期红利' },
    { key: 'deathBenefitTotal', title: '身故总额' },
    { key: 'totalWithdrawableCashAndInterest', title: '累积可提取现金利息总额' },
    { key: 'totalMonthlyAnnuityAndInterest', title: '累积每月年金入利总额' }
];

// 初始化时，确保只有有数据的列才会被激活
onMounted(() => {
    // 遍历所有列
    columnDefs.forEach(column => {
        // 确保列配置存在
        if (props.tempSettings.columnsVisible[column.key] === undefined) {
            props.tempSettings.columnsVisible[column.key] = true;
        }

        // 如果列没有数据，则将其设置为不可见
        if (!hasColumnData(column.key)) {
            props.tempSettings.columnsVisible[column.key] = false;
        }
    });
});
</script>

<style scoped></style>