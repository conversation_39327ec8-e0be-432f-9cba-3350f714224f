<template>
    <div class="setting-section">
        <h3 class="text-lg font-medium mb-4">高亮设置</h3>

        <!-- 高亮总开关 -->
        <div class="flex items-center mb-4">
            <a-switch :checked="modelValue.enableHighlight" @change="handleEnableChange" />
            <span class="ml-2">启用高亮显示</span>
        </div>

        <!-- 高亮规则列表 -->
        <div v-if="modelValue && modelValue.enableHighlight" class="mb-4">
            <div class="flex justify-between items-center mb-2">
                <h4 class="text-base font-medium">高亮规则</h4>
                <a-button type="primary" size="small" @click="addRule">添加规则</a-button>
            </div>

            <!-- 空状态 -->
            <div v-if="!modelValue.highlightRules || !Array.isArray(modelValue.highlightRules) || modelValue.highlightRules.length === 0"
                class="text-center py-8 bg-gray-50 rounded-md border border-gray-200">
                <p class="text-gray-500">暂无高亮规则，请添加</p>
            </div>

            <!-- 规则列表 -->
            <a-collapse v-else v-model:activeKey="activeKeys">
                <a-collapse-panel v-for="(rule, index) in modelValue.highlightRules" :key="rule.id"
                    :header="getRuleTitle(rule)" :class="{ 'opacity-50': !rule.enabled }">
                    <div class="rule-content p-2">
                        <!-- 规则名称 -->
                        <div class="mb-3">
                            <label class="block text-sm font-medium text-gray-700 mb-1">规则名称</label>
                            <a-input v-model:value="rule.name" placeholder="输入规则名称" />
                        </div>

                        <!-- 规则条件 -->
                        <div class="mb-3 grid grid-cols-3 gap-2">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">字段</label>
                                <a-select v-model:value="rule.field" style="width: 100%">
                                    <a-select-option value="year">保单持有时间</a-select-option>
                                    <a-select-option value="age">年龄</a-select-option>
                                    <a-select-option value="multiple">退保倍数</a-select-option>
                                    <a-select-option value="singleton">单利</a-select-option>
                                    <a-select-option value="IRR">复利</a-select-option>
                                </a-select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">条件</label>
                                <a-select v-model:value="rule.operator" style="width: 100%">
                                    <a-select-option value=">">&gt;</a-select-option>
                                    <a-select-option value=">=">&ge;</a-select-option>
                                    <a-select-option value="<">&lt;</a-select-option>
                                    <a-select-option value="<=">&le;</a-select-option>
                                    <a-select-option value="==">等于</a-select-option>
                                </a-select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">值</label>
                                <a-input-number v-model:value="rule.value" style="width: 100%" />
                            </div>
                        </div>

                        <!-- 高亮样式 -->


                        <!-- 预览 -->


                        <!-- 规则操作 -->
                        <div class="flex justify-between items-center">
                            <div>
                                <a-switch v-model:checked="rule.enabled" size="small" />
                                <span class="ml-1 text-sm">{{ rule.enabled ? '启用' : '禁用' }}</span>
                            </div>
                            <div>
                                <a-button type="text" danger size="small" @click="removeRule(index)">
                                    删除
                                </a-button>
                                <a-button type="text" size="small" :disabled="index === 0" @click="moveRule(index, -1)">
                                    上移
                                </a-button>
                                <a-button type="text" size="small"
                                    :disabled="index === modelValue.highlightRules.length - 1"
                                    @click="moveRule(index, 1)">
                                    下移
                                </a-button>
                            </div>
                        </div>
                    </div>
                </a-collapse-panel>
            </a-collapse>
        </div>


    </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { v4 as uuidv4 } from 'uuid';

const props = defineProps({
    modelValue: {
        type: Object,
        required: true
    }
});

const emit = defineEmits(['update:modelValue']);

// 活动的折叠面板
const activeKeys = ref([]);

// 处理总开关状态变化
const handleEnableChange = (checked) => {
    // 确保有初始的highlightRules
    const currentRules = Array.isArray(props.modelValue.highlightRules)
        ? props.modelValue.highlightRules
        : [

        ];

    emit('update:modelValue', {
        ...props.modelValue,
        enableHighlight: checked,
        highlightRules: currentRules
    });
};

// 获取规则标题
const getRuleTitle = (rule) => {
    return `${rule.enabled ? '✓ ' : ''}${rule.name || '未命名规则'}`;
};

// 添加新规则
const addRule = () => {
    const newRule = {
        id: uuidv4(),
        name: '新规则',
        enabled: true,
        field: 'year',
        operator: '>',
        value: 5,
        color: '#16a34a',
        opacity: 0.2,
        priority: Array.isArray(props.modelValue.highlightRules) ? props.modelValue.highlightRules.length + 1 : 1
    };

    // 确保highlightRules是数组
    const currentRules = Array.isArray(props.modelValue.highlightRules) ? props.modelValue.highlightRules : [];
    const updatedRules = [...currentRules, newRule];

    emit('update:modelValue', {
        ...props.modelValue,
        highlightRules: updatedRules
    });

    // 展开新添加的规则
    activeKeys.value = [...activeKeys.value, newRule.id];
};

// 移除规则
const removeRule = (index) => {
    // 确保highlightRules是数组
    if (!Array.isArray(props.modelValue.highlightRules)) return;

    const updatedRules = [...props.modelValue.highlightRules];
    updatedRules.splice(index, 1);

    emit('update:modelValue', {
        ...props.modelValue,
        highlightRules: updatedRules
    });
};

// 移动规则（上移/下移）
const moveRule = (index, direction) => {
    // 确保highlightRules是数组
    if (!Array.isArray(props.modelValue.highlightRules)) return;

    const newIndex = index + direction;
    if (newIndex < 0 || newIndex >= props.modelValue.highlightRules.length) return;

    const updatedRules = [...props.modelValue.highlightRules];
    const temp = updatedRules[index];
    updatedRules[index] = updatedRules[newIndex];
    updatedRules[newIndex] = temp;

    // 更新优先级
    updatedRules.forEach((rule, idx) => {
        rule.priority = idx + 1;
    });

    emit('update:modelValue', {
        ...props.modelValue,
        highlightRules: updatedRules
    });
};

// 打开颜色选择器（这里简化处理，实际可以使用颜色选择器组件）
const openColorPicker = (index) => {
    // 在实际实现中，这里可以打开颜色选择器
    console.log('Open color picker for rule', index);
};

// 预览数据
const previewData = [
    { year: 1, multiple: null, singleton: -56.25, IRR: -56.25 },
    { year: 3, multiple: null, singleton: -46.77, IRR: -87.45 },
    { year: 5, multiple: null, singleton: -28.28, IRR: -42.57 },
    { year: 7, multiple: 1.2, singleton: 3.75, IRR: 3.47 },
    { year: 10, multiple: 1.5, singleton: 5.26, IRR: 4.46 }
];

// 获取行样式
const getRowStyle = (row) => {
    // 检查modelValue和enableHighlight
    if (!props.modelValue || !props.modelValue.enableHighlight) return {};

    // 确保highlightRules存在
    if (!props.modelValue.highlightRules || !Array.isArray(props.modelValue.highlightRules)) {
        return {};
    }

    // 按优先级从高到低检查规则
    const sortedRules = [...props.modelValue.highlightRules]
        .filter(rule => rule.enabled)
        .sort((a, b) => a.priority - b.priority);

    for (const rule of sortedRules) {
        const fieldValue = row[rule.field];
        if (fieldValue === null || fieldValue === undefined) continue;

        let matches = false;

        switch (rule.operator) {
            case '>':
                matches = fieldValue > rule.value;
                break;
            case '>=':
                matches = fieldValue >= rule.value;
                break;
            case '<':
                matches = fieldValue < rule.value;
                break;
            case '<=':
                matches = fieldValue <= rule.value;
                break;
            case '==':
                matches = fieldValue == rule.value;
                break;
            default:
                matches = false;
        }

        if (matches) {
            return {
                backgroundColor: `${rule.color}${Math.round(rule.opacity * 255).toString(16).padStart(2, '0')}`
            };
        }
    }

    return {};
};
</script>

<style scoped>
.rule-content {
    background-color: #f9fafb;
    border-radius: 4px;
}
</style>