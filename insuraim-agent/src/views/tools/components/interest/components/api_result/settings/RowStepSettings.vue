<template>
    <!-- 行间隔设置 -->
    <div v-if="currentSettingTab === 'rowStep'" class="setting-section">
        <h3 class="text-lg font-medium mb-4">行间隔设置</h3>
        <div class="flex items-center">
            <span class="mr-4">每</span>
            <a-input-number v-model:value="tempSettings.rowStep" :min="1" :max="10" />
            <span class="ml-4">行显示一次</span>
        </div>
        <div class="text-gray-500 text-sm mt-2">
            设置为1时显示全部数据行，大于1时将按照间隔显示数据
        </div>

        <div class="mt-6">

            <table class="w-full border border-gray-200 rounded">
                <thead>
                    <tr>
                        <th class="border px-4 py-2 bg-gray-100 text-center">年份</th>
                        <th class="border px-4 py-2 bg-gray-100 text-center">数据</th>
                    </tr>
                </thead>
                <tbody>
                    <template v-for="i in 10" :key="i">
                        <tr v-if="(i - 1) % tempSettings.rowStep === 0">
                            <td class="border px-4 py-2 text-center">{{ i }}年</td>
                            <td class="border px-4 py-2 text-center">示例数据{{ i }}</td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
    </div>
</template>

<script setup>
defineProps({
    currentSettingTab: {
        type: String,
        required: true
    },
    tempSettings: {
        type: Object,
        required: true
    }
});
</script>