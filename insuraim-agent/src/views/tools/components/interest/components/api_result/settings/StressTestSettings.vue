<template>
    <div class="setting-section">
        <h3 class="text-lg font-medium mb-4">压力测试</h3>
        <div class="mt-4" v-if="enableStressTest">
            <div class="flex items-center mb-2">
                <span>分红实现率：</span>
                <a-input-number :value="dividendRate" :min="30" :max="100" @change="updateDividendRate"
                    style="width: 100px;" />
                <span class="ml-2">%</span>
            </div>
            <div class="text-gray-500 text-sm mt-2">
                实现率范围：30% - 100%，默认为100%
            </div>

            <div class="mt-4">
                <a-button type="primary" @click="applyStressTest" :loading="calculating">
                    应用
                </a-button>
                <a-button class="ml-2" @click="resetStressTest" :disabled="!enableStressTest">
                    重置
                </a-button>
            </div>

            <div v-if="calculating" class="mt-4 text-blue-500 flex items-center">
                <a-spin />
                <span class="ml-2">正在重新计算，请稍候...</span>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { Switch, InputNumber, Button, Spin } from 'ant-design-vue';

const props = defineProps({
    modelValue: {
        type: Object,
        required: true
    }
});

const emit = defineEmits(['update:modelValue', 'apply-stress-test', 'reset-stress-test']);

// 解构出所需数据，创建本地响应式变量
const enableStressTest = ref(props.modelValue.enableStressTest);
const dividendRate = ref(props.modelValue.dividendRate || 100);
const calculating = ref(false);

// 更新压力测试开关
const updateStressTest = (checked) => {
    enableStressTest.value = checked;
    emit('update:modelValue', {
        ...props.modelValue,
        enableStressTest: checked
    });

    // 如果关闭压力测试，自动重置
    if (!checked) {
        resetStressTest();
    }
};

// 更新分红实现率
const updateDividendRate = (value) => {
    dividendRate.value = value;
    emit('update:modelValue', {
        ...props.modelValue,
        dividendRate: value
    });
};

// 应用压力测试并重新计算
const applyStressTest = () => {
    calculating.value = true;

    // 直接触发apply-stress-test事件，跳过TableSettings的apply处理
    emit('apply-stress-test', {
        enableStressTest: enableStressTest.value,
        dividendRate: dividendRate.value,
        _fromStressTestApplyButton: true // 添加标记，表示是从"应用"按钮触发的
    });

    // 模拟API调用延迟
    setTimeout(() => {
        calculating.value = false;
        // 关闭弹窗
        emit('cancel', false);
    }, 1000);
};

// 重置压力测试
const resetStressTest = () => {
    dividendRate.value = 100;
    // 直接触发apply-stress-test事件，跳过TableSettings的apply处理
    emit('apply-stress-test', {
        enableStressTest: enableStressTest.value,
        dividendRate: dividendRate.value,
        _fromStressTestApplyButton: true // 添加标记，表示是从"应用"按钮触发的
    });

};

// 监听外部属性变化，同步到本地变量
watch(() => props.modelValue, (newVal) => {
    enableStressTest.value = newVal.enableStressTest;
    dividendRate.value = newVal.dividendRate || 100;
}, { deep: true });
</script>