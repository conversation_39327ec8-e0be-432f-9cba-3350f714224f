<template>
    <a-modal :open="open" width="650px" @ok="applySettings" @cancel="cancelSettings" @update:open="handleOpenChange"
        :bodyStyle="{ backgroundColor: '#fff' }" wrapClassName="mac-style-modal" class="over-table-modal"
        cancelText="取 消" okText="确 定">
        <template #title>
            <div class="flex items-center justify-between border-b border-gray-200 pb-2 font-bold">
                <h2 class="text-lg font-medium mb-2">表格设置</h2>
            </div>
        </template>
        <div class="flex">
            <!-- 左侧设置分类 -->
            <div class="w-1/3 pr-4">
                <ul class="space-y-4 p-3 rounded-md" style="background-color: #f9fafb;">
                    <li :class="[
                        'cursor-pointer py-3 px-3 rounded-md flex items-center',
                        currentSettingTab === 'columns' ? 'bg-primary text-white font-medium' : 'hover:bg-gray-100'
                    ]" @click="switchSettingTab('columns')"
                        :style="currentSettingTab === 'columns' ? { backgroundColor: 'var(--primary)', color: 'white' } : {}">
                        <h3 class="text-sm text-inherit"
                            :style="currentSettingTab === 'columns' ? { color: 'white' } : {}">列设置
                        </h3>
                    </li>
                    <li :class="[
                        'cursor-pointer py-3 px-3 rounded-md flex items-center',
                        currentSettingTab === 'rowStep' ? 'bg-primary text-white font-medium' : 'hover:bg-gray-100'
                    ]" @click="switchSettingTab('rowStep')"
                        :style="currentSettingTab === 'rowStep' ? { backgroundColor: 'var(--primary)', color: 'white' } : {}">
                        <h3 class="text-sm text-inherit"
                            :style="currentSettingTab === 'rowStep' ? { color: 'white' } : {}">行设置
                        </h3>
                    </li>
                    <li :class="[
                        'cursor-pointer py-3 px-3 rounded-md flex items-center',
                        currentSettingTab === 'theme' ? 'bg-primary text-white font-medium' : 'hover:bg-gray-100'
                    ]" @click="switchSettingTab('theme')"
                        :style="currentSettingTab === 'theme' ? { backgroundColor: 'var(--primary)', color: 'white' } : {}">
                        <h3 class="text-sm text-inherit"
                            :style="currentSettingTab === 'theme' ? { color: 'white' } : {}">主题设置
                        </h3>
                    </li>
                    <li :class="[
                        'cursor-pointer py-3 px-3 rounded-md flex items-center',
                        currentSettingTab === 'highlight' ? 'bg-primary text-white font-medium' : 'hover:bg-gray-100'
                    ]" @click="switchSettingTab('highlight')"
                        :style="currentSettingTab === 'highlight' ? { backgroundColor: 'var(--primary)', color: 'white' } : {}">
                        <h3 class="text-sm text-inherit"
                            :style="currentSettingTab === 'highlight' ? { color: 'white' } : {}">
                            高亮设置
                        </h3>
                    </li>
                    <li :class="[
                        'cursor-pointer py-3 px-3 rounded-md flex items-center',
                        currentSettingTab === 'stressTest' ? 'bg-primary text-white font-medium' : 'hover:bg-gray-100'
                    ]" @click="switchSettingTab('stressTest')"
                        :style="currentSettingTab === 'stressTest' ? { backgroundColor: 'var(--primary)', color: 'white' } : {}">
                        <h3 class="text-sm text-inherit"
                            :style="currentSettingTab === 'stressTest' ? { color: 'white' } : {}">
                            压力测试
                        </h3>
                    </li>
                </ul>
            </div>

            <!-- 右侧设置内容 -->
            <div class="w-3/4 pl-6">
                <!-- 列设置 -->
                <ColumnSettings v-if="currentSettingTab === 'columns'" :currentSettingTab="currentSettingTab"
                    :tempSettings="tempSettings" :tableData="tableData" />

                <!-- 行设置 -->
                <RowStepSettings v-if="currentSettingTab === 'rowStep'" :currentSettingTab="currentSettingTab"
                    :tempSettings="tempSettings" />

                <!-- 主题设置 -->
                <ThemeSettings v-if="currentSettingTab === 'theme'" v-model="tempSettings.theme"
                    :themeOptions="themeOptions" v-model:customThemeColor="tempSettings.customThemeColor" />

                <!-- 高亮设置 -->
                <HighlightSettings v-if="currentSettingTab === 'highlight'" v-model="tempSettings" />

                <!-- 压力测试 -->
                <StressTestSettings v-if="currentSettingTab === 'stressTest'" v-model="tempSettings"
                    @apply-stress-test="applyStressTest" @cancel="applySettings" />
            </div>
        </div>
    </a-modal>
</template>

<script setup>
import { ref, watch } from 'vue';
import ThemeSettings from './ThemeSettings.vue';
import HighlightSettings from './HighlightSettings.vue';
import StressTestSettings from './StressTestSettings.vue';
import ColumnSettings from './ColumnSettings.vue';
import RowStepSettings from './RowStepSettings.vue';

const props = defineProps({
    open: {
        type: Boolean,
        required: true
    },
    settings: {
        type: Object,
        required: true
    },
    tableData: {
        type: Array,
        default: () => []
    }
});

const emit = defineEmits(['update:open', 'apply', 'cancel', 'apply-stress-test']);
const applyStressTest = (settings) => {
    emit('apply', settings);
};
// 当前设置选项卡
const currentSettingTab = ref('columns');

// 临时设置，用于编辑中
const tempSettings = ref({ ...props.settings });

// 所有可用的主题列表
const themeOptions = [
    { label: '默认主题', value: 'default' },
    { label: '商务蓝', value: 'primary' },
    { label: '成功绿', value: 'success' },
    { label: '警告黄', value: 'warning' },
    { label: '危险红', value: 'danger' }
];

// 切换设置选项卡
const switchSettingTab = (tab) => {
    currentSettingTab.value = tab;
};

// 处理modal打开状态变化
const handleOpenChange = (value) => {
    emit('update:open', value);
    // 如果是关闭，也触发cancel
    if (!value) {
        emit('cancel');
    }
};

// 应用表格设置
const applySettings = () => {
    emit('apply', { ...tempSettings.value });
    emit('update:open', false);
};

// 取消表格设置
const cancelSettings = () => {
    emit('update:open', false);
    // 不再需要显式调用cancel，因为handleOpenChange会处理
};

// 监听外部settings变化，同步到tempSettings
watch(() => props.settings, (newSettings) => {
    tempSettings.value = { ...newSettings };
}, { deep: true });

// 监听外部open变化，重置当前选项卡
watch(() => props.open, (newOpen) => {
    if (newOpen) {
        // 打开模态框时，重置当前选项卡
        currentSettingTab.value = 'columns';
        // 重置临时设置
        tempSettings.value = { ...props.settings };
    }
});
</script>

<style scoped>
:deep(.mac-style-modal) {
    --modal-bg-color: #fff;
}

:deep(.over-table-modal .ant-modal-header) {
    background-color: #fff !important;
}

:deep(.over-table-modal .ant-modal-body) {
    background-color: #fff !important;
}

:deep(.over-table-modal .ant-modal-content) {
    background-color: #fff !important;
}
</style>