<template>
    <div class="setting-section">
        <h3 class="text-lg font-medium mb-4">主题设置</h3>
        <div class="space-y-2">
            <div v-for="option in themeOptions" :key="option.value"
                class="cursor-pointer py-3 px-4 rounded-[4px] flex items-center justify-between" :style="{
                    backgroundColor: getThemeColor(option.value),
                    border: modelValue === option.value ? '2px solid white' : '2px solid transparent',
                    boxShadow: modelValue === option.value ? '0 0 0 1px var(--primary)' : 'none'
                }" @click="$emit('update:modelValue', option.value)">
                <span class="text-sm font-medium" :style="{ color: getThemeTextColor(option.value) }">
                    {{ option.label }}
                </span>
                <Icon v-if="modelValue === option.value" icon="material-symbols:check-circle" class="text-lg"
                    :style="{ color: getThemeTextColor(option.value) }" />
            </div>

            <!-- 自定义主题选项 -->
            <!-- <div class="cursor-pointer py-3 px-4 rounded-[4px] flex items-center justify-between" :style="{
                backgroundColor: customColor,
                border: modelValue === 'custom' ? '2px solid white' : '2px solid transparent',
                boxShadow: modelValue === 'custom' ? '0 0 0 1px var(--primary)' : 'none'
            }" @click="selectCustomTheme">
                <span class="text-sm font-medium" :style="{ color: getContrastColor(customColor) }">
                    自定义主题
                </span>
                <div class="flex items-center">
                    <Icon v-if="modelValue === 'custom'" icon="material-symbols:check-circle" class="text-lg mr-2"
                        :style="{ color: getContrastColor(customColor) }" />
                    <div class="color-picker-trigger w-6 h-6 rounded-full border border-white cursor-pointer"
                        :style="{ backgroundColor: customColor }" @click.stop="showColorPicker = true">
                    </div>
                </div>
            </div> -->

            <!-- 颜色选择器弹出层 -->
            <div v-if="showColorPicker"
                class="color-picker-container fixed inset-0 z-50 flex items-center justify-center"
                @click.self="showColorPicker = false">
                <div class="bg-white rounded-lg shadow-lg p-4 w-72">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-md font-medium">选择自定义主题颜色</h3>
                        <button @click="showColorPicker = false" class="text-gray-500 hover:text-gray-700">
                            <Icon icon="material-symbols:close" class="text-lg" />
                        </button>
                    </div>

                    <!-- 颜色选择区 -->
                    <div class="flex flex-wrap gap-2 mb-4">
                        <div v-for="(color, index) in presetColors" :key="index"
                            class="w-8 h-8 rounded-full cursor-pointer border border-gray-200"
                            :style="{ backgroundColor: color }" @click="selectPresetColor(color)">
                        </div>
                    </div>

                    <!-- 颜色输入 -->
                    <div class="flex items-center gap-2 mb-4">
                        <div class="w-10 h-10 rounded border" :style="{ backgroundColor: customColor }"></div>
                        <input v-model="customColor" type="text" class="flex-1 px-3 py-2 border border-gray-200 rounded"
                            placeholder="#RRGGBB">
                    </div>

                    <div class="flex justify-end space-x-2">
                        <button @click="showColorPicker = false"
                            class="px-4 py-2 border border-gray-200 rounded text-sm">取消</button>
                        <button @click="applyCustomColor"
                            class="px-4 py-2 bg-blue-500 text-white rounded text-sm">确定</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { Icon } from '@iconify/vue';

const props = defineProps({
    modelValue: {
        type: String,
        required: true
    },
    themeOptions: {
        type: Array,
        default: () => [
            { label: '默认主题', value: 'default' },
            { label: '主要主题', value: 'primary' },
            { label: '成功主题', value: 'success' },
            { label: '警告主题', value: 'warning' },
            { label: '危险主题', value: 'danger' }
        ]
    },
    customThemeColor: {
        type: String,
        default: '#8B5CF6' // 默认为紫色
    }
});

const emit = defineEmits(['update:modelValue', 'update:customThemeColor']);

// 自定义主题颜色
const customColor = ref(props.customThemeColor);
const showColorPicker = ref(false);

// 预设颜色
const presetColors = [
    '#1890ff', // 蓝色
    '#52c41a', // 绿色
    '#faad14', // 黄色
    '#f5222d', // 红色
    '#722ed1', // 紫色
    '#13c2c2', // 青色
    '#eb2f96', // 粉色
    '#fadb14', // 黄色
    '#a0d911', // 浅绿色
    '#fa541c', // 橙色
];

// 根据背景色自动计算对比色（黑/白）
const getContrastColor = (hexColor) => {
    // 转换hex到rgb
    const r = parseInt(hexColor.substring(1, 3), 16);
    const g = parseInt(hexColor.substring(3, 5), 16);
    const b = parseInt(hexColor.substring(5, 7), 16);

    // 计算亮度
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

    // 根据亮度返回黑色或白色
    return luminance > 0.5 ? '#000000' : '#ffffff';
};

// 选择自定义主题
const selectCustomTheme = () => {
    emit('update:modelValue', 'custom');
    emit('update:customThemeColor', customColor.value);
};

// 选择预设颜色
const selectPresetColor = (color) => {
    customColor.value = color;
};

// 应用自定义颜色
const applyCustomColor = () => {
    emit('update:customThemeColor', customColor.value);
    showColorPicker.value = false;

    // 如果当前已经是自定义主题，触发更新
    if (props.modelValue === 'custom') {
        emit('update:modelValue', 'custom');
    }
};

// 获取主题对应的背景颜色
const getThemeColor = (theme) => {
    switch (theme) {
        case 'default': return '#374151';
        case 'primary': return 'var(--primary)';
        case 'success': return '#16a34a';
        case 'warning': return '#d97706';
        case 'danger': return '#dc2626';
        case 'custom': return customColor.value;
        default: return '#374151';
    }
};

// 获取主题对应的文字颜色
const getThemeTextColor = (theme) => {
    switch (theme) {
        case 'custom': return getContrastColor(customColor.value);
        default: return '#ffffff'; // 所有其他主题使用白色文字
    }
};

// 监听自定义颜色变化
watch(() => props.customThemeColor, (newColor) => {
    customColor.value = newColor;
});
</script>

<style scoped>
.color-picker-container {
    background-color: rgba(0, 0, 0, 0.5);
}
</style>