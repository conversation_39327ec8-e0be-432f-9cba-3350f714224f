import { ref } from "vue";
import { message } from "ant-design-vue";
import { productAPI } from "@/api";

/**
 * API计算相关逻辑
 * @returns {Object} API计算相关的状态和方法
 */
export function useApiCalculation() {
  const apiCalculationResult = ref(null);
  const apiCalculating = ref(false);
  const apiError = ref(null);

  /**
   * 通过旧API获取产品的单复利计算结果（保留兼容性）
   * @param {string} productId 产品ID
   * @param {number} holdingYears 持有年数
   * @returns {Promise<Object|null>} 计算结果或null（出错时）
   */
  const calculateWithApi = async (productId, holdingYears) => {
    if (!productId || !holdingYears) {
      return null;
    }

    apiCalculating.value = true;
    apiError.value = null;

    try {
      const apiResult =
        await productAPI.getChinaLifeProductSingleAndDoubleInterest({
          productId: productId,
          year: holdingYears,
        });

      if (apiResult && apiResult.irr && apiResult.irr.length > 0) {
        apiCalculationResult.value = apiResult;
        return apiResult;
      } else {
        message.warning("该产品无现金价值，请选择其他产品");
        apiCalculationResult.value = null;
        return null;
      }
    } catch (error) {
      console.error("API计算失败:", error);
      message.error(typeof error === "string" ? error : "计算失败，请稍后重试");
      apiError.value = error;
      apiCalculationResult.value = null;
      return null;
    } finally {
      apiCalculating.value = false;
    }
  };

  /**
   * 使用新API进行动态IRR计算
   * @param {Object} formDataValue 表单数据
   * @returns {Promise<Object|null>} 计算结果或null（出错时）
   */
  const calculateWithNewApi = async (formDataValue) => {
    if (!formDataValue.productId) {
      return null;
    }

    apiCalculating.value = true;
    apiError.value = null;
    console.log('formDataValue.discounts', formDataValue.discounts);
    let tempDiscounts = formDataValue.discounts.map(item => {
      return {
        ...item,
        rate: item.rate / 100
      }
    });
    console.log('formDataValue.discounts', formDataValue.discounts);
    try {
      const params = {
        productId: formDataValue.productId,
        discounts: tempDiscounts || [], // 添加保费优惠
        premium: formDataValue.precisePremium || 10000, // 使用精确计算的保费
        holdYear: formDataValue.holdingYears,
        startAge: formDataValue.startAge || 0, // 默认年龄
        // 添加压力测试参数，如果启用压力测试则使用设置的分红率，否则使用默认值1
        realizationRate: formDataValue.enableStressTest ? (formDataValue.dividendRate / 100) : 1, // 压力测试分红率 0.3 - 1 范围
        // 添加新字段支持
        paymentTerm: formDataValue.selectedPrepaymentYear || formDataValue.paymentYears, // 使用选择的缴费年期
        paymentMethod: formDataValue.selectedPaymentMethod || formDataValue.paymentMethod, // 使用选择的缴费方式

        type: formDataValue.type || "standard", // 添加计算类型
      };

      const result = await productAPI.calculateProfitDynamic(params);

      if (result && result.calculationBaseDataItemBOList && Array.isArray(result.calculationBaseDataItemBOList)) {
        // 将新API结果转换为原有格式
        const convertedResult = {
          irr: [
            {
              productName: result.productName || "未知产品",
              list: convertApiResultToList(result.calculationBaseDataItemBOList),
              name: "计算结果",
              // 将产品信息数据传递给显示组件
              dynamicIRRData: {
                productName: result.productName,
                age: result.age,
                gender: result.gender,
                smokingStatus: result.smokingStatus,
                policyCurrency: result.policyCurrency,
                paymentMode: result.paymentMode,
                premiumPaymentTerm: result.premiumPaymentTerm,
                coverageTerm: result.coverageTerm,
                coverageAmount: result.coverageAmount,
                annualPremium: result.annualPremium,
                multiple: result.multiple
              }
            },
          ],
        };
        apiCalculationResult.value = convertedResult;
        message.success("API计算完成");
        return convertedResult;
      } else {
        message.warning("该产品无现金价值，请选择其他产品");
        apiCalculationResult.value = null;
        return null;
      }
    } catch (error) {
      console.error("新API计算失败:", error);
      message.warning("API计算失败，请稍后重试");
      apiError.value = error;
      return null;
    } finally {
      apiCalculating.value = false;
    }
  };

  /**
   * 转换API结果为列表格式
   * @param {Array} calculationBaseDataItemBOList 计算结果数据数组
   * @returns {Array} 转换后的列表数据
   */
  const convertApiResultToList = (calculationBaseDataItemBOList) => {
    if (!calculationBaseDataItemBOList || !Array.isArray(calculationBaseDataItemBOList)) {
      return [];
    }

    // 检查每个字段是否在所有记录中都为null
    const fieldNullStatus = {};
    const fieldKeys = [
      'age', 'year', 'totalPremium', 'insuredAmount', 'netCashFlow', 'surrender',
      'singleInterest', 'insuredSingleInterest', 'insuredIRR', 'terminalBonusNg',
      'cashValueOfReversionaryBonusNg', 'irr', 'deathBenefitG', 'deathBenefitTerminalBonusNg',
      'deathBenefitTotal', 'totalWithdrawableCashAndInterest', 'totalMonthlyAnnuityAndInterest', 'multiple'
    ];

    // 初始化所有字段状态为"全部为null"
    fieldKeys.forEach(key => {
      fieldNullStatus[key] = true;
    });

    // 检查每个字段是否所有记录都为null
    calculationBaseDataItemBOList.forEach(item => {
      fieldKeys.forEach(key => {
        // 如果有任何一条记录该字段不为null，则更新状态为false
        if (item[key] !== null && item[key] !== undefined && item[key] !== 'NaN' && item[key] !== 0) {
          fieldNullStatus[key] = false;
        }
      });
    });

    return calculationBaseDataItemBOList.map((item) => ({
      age: fieldNullStatus['age'] ? null : (item.age != null ? item.age : 0), // 年龄
      year: fieldNullStatus['year'] ? null : (item.year != null ? item.year : 0), // 保单持有时间
      totalPremum: fieldNullStatus['totalPremium'] ? null : (item.totalPremium != null ? item.totalPremium : 0), // 总保费
      insuredAmount: fieldNullStatus['insuredAmount'] ? null : (item.insuredAmount != null ? item.insuredAmount : 0), // 保证退保现金
      netflow: fieldNullStatus['netCashFlow'] ? null : (item.netCashFlow != null ? item.netCashFlow : 0), // 净现金流
      surrender: fieldNullStatus['surrender'] ? null : (item.surrender != null ? item.surrender : 0), // 退保总额
      singleton: fieldNullStatus['singleInterest'] ? null : (item.singleInterest != null ? item.singleInterest : 0), // 单利 (新字段名)
      insuredSingleInterest: fieldNullStatus['insuredSingleInterest'] ? null : (item.insuredSingleInterest != null ? item.insuredSingleInterest : 0), // 保证单利
      insuredIRR: fieldNullStatus['insuredIRR'] ? null : (item.insuredIRR != null ? item.insuredIRR : 0), // 保证复利
      terminalBonusNg: fieldNullStatus['terminalBonusNg'] ? null : (item.terminalBonusNg != null ? item.terminalBonusNg : 0), // 终期红利(非保证)
      cashValueOfReversionaryBonusNg: fieldNullStatus['cashValueOfReversionaryBonusNg'] ? null : (item.cashValueOfReversionaryBonusNg != null ? item.cashValueOfReversionaryBonusNg : 0), // 复归红利(非保证)
      IRR: fieldNullStatus['irr'] ? null : (item.irr === "NaN" ? 0 : item.irr || 0), // 复利
      deathBenefitG: fieldNullStatus['deathBenefitG'] ? null : (item.deathBenefitG != null ? item.deathBenefitG : 0), // 身故保证金额
      deathBenefitTerminalBonusNg: fieldNullStatus['deathBenefitTerminalBonusNg'] ? null : (item.deathBenefitTerminalBonusNg != null ? item.deathBenefitTerminalBonusNg : 0), // 身故终期红利
      deathBenefitTotal: fieldNullStatus['deathBenefitTotal'] ? null : (item.deathBenefitTotal != null ? item.deathBenefitTotal : 0), // 身故总额
      totalWithdrawableCashAndInterest: fieldNullStatus['totalWithdrawableCashAndInterest'] ? null : (item.totalWithdrawableCashAndInterest != null ? item.totalWithdrawableCashAndInterest : 0), // 累积可提取现金利息总额
      totalMonthlyAnnuityAndInterest: fieldNullStatus['totalMonthlyAnnuityAndInterest'] ? null : (item.totalMonthlyAnnuityAndInterest || null), // 累积每月年金入利总额
      multiple: fieldNullStatus['multiple'] ? null : (item.multiple != null ? item.multiple : 0), // 退保倍数
    }));
  };

  /**
   * 重置API计算结果
   */
  const resetApiCalculation = () => {
    apiCalculationResult.value = null;
    apiError.value = null;
  };

  return {
    apiCalculationResult,
    apiCalculating,
    apiError,
    calculateWithApi,
    calculateWithNewApi,
    resetApiCalculation,
  };
}
