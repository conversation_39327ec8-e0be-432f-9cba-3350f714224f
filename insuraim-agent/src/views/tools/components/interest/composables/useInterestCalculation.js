import { ref } from 'vue';
import { message } from 'ant-design-vue';
import { calculateIRR, getActualPremiumsPerYear, calculateSimpleInterest } from '@/utils/interestCalculator';

/**
 * 单复利计算相关逻辑
 * @returns {Object} 计算相关的状态和方法
 */
export function useInterestCalculation() {
    const calculating = ref(false);
    const calculationResult = ref(null);

    /**
     * 验证表单数据
     * @param {Object} formData 表单数据
     * @returns {boolean} 验证结果
     */
    const validateForm = (formData) => {
        console.log('formData', formData);
        // 检查持有年数，这是所有情况下都必需的
        // if (!formData.holdingYears || formData.holdingYears <= 0) {
        //     message.error('请填写有效的持有年数 (必须大于0)');
        //     return false;
        // }

        // 如果选择了产品（精确计算模式）
        if (formData.productId) {
            // 验证精确计算模式下的必填字段
            if (!formData.precisePremium || formData.precisePremium <= 0) {
                message.error('请填写有效的年缴保费 (必须大于0)');
                return false;
            }



            // if (parseInt(formData.paymentYears) > parseInt(formData.holdingYears)) {
            //     message.error('缴费期不能大于持有年数');
            //     return false;
            // }
        }
        // 通用计算模式
        else {
            if (
                !formData.annualPremium ||
                formData.annualPremium <= 0 ||
                !formData.paymentYears ||
                formData.paymentYears <= 0
            ) {
                message.error('请填写完整且有效的缴费信息 (所有数值必须大于0)');
                return false;
            }

            if (parseInt(formData.paymentYears) > parseInt(formData.holdingYears)) {
                message.error('缴费期不能大于持有年数');
                return false;
            }
        }

        // 验证保费优惠（适用于两种模式）
        if (formData.discounts && formData.discounts.length > 0) {
            for (const discount of formData.discounts) {
                if (!discount.year || !discount.rate || discount.year <= 0 || discount.rate <= 0) {
                    message.error('保费优惠信息不完整或无效');
                    return false;
                }

                // if (discount.year > (formData.paymentYears || formData.holdingYears)) {
                //     message.error(`第${discount.year}年的优惠超出了缴费期范围`);
                //     return false;
                // }
            }
        }

        return true;
    };

    /**
     * 执行前端计算
     * @param {Object} formData 表单数据
     * @returns {Object} 计算结果
     */
    const calculateLocal = (formData) => {
        // 确保持有年数是有效的整数
        const holdingYears = Math.max(1, parseInt(formData.holdingYears) || 1);

        let actualPremiumsPerYear = [];
        let paymentYearsList = [];

        // 处理缴费信息
        if (formData.productId) {
            // 精确计算模式
            const paymentYears = parseInt(formData.paymentYears) || 1;
            actualPremiumsPerYear = getActualPremiumsPerYear(
                parseFloat(formData.precisePremium) || 0,
                paymentYears,
                formData.discounts || []
            );
            paymentYearsList = Array.from({ length: paymentYears }, (_, i) => i + 1);
        } else {
            // 通用计算模式
            const paymentYears = parseInt(formData.paymentYears) || 1;
            actualPremiumsPerYear = getActualPremiumsPerYear(
                parseFloat(formData.annualPremium) || 0,
                paymentYears,
                formData.discounts || []
            );
            paymentYearsList = Array.from({ length: paymentYears }, (_, i) => i + 1);
        }

        // 计算最终收益（保证现金价值+非保证红利）
        const totalReturnAtEnd = (formData.guaranteedValue || 0) + (formData.nonGuaranteedBonus || 0);

        // 1. 单利计算
        const netOutflowsForSimpleInterest = actualPremiumsPerYear.map(p => -p);

        const simpleInterest = calculateSimpleInterest(
            holdingYears,
            paymentYearsList,
            netOutflowsForSimpleInterest,
            totalReturnAtEnd
        );

        // 2. 复利计算 (IRR)
        // 创建现金流数组，确保长度有效
        const cashFlows = Array(holdingYears + 1).fill(0);

        // 填充缴费数据
        for (let i = 0; i < actualPremiumsPerYear.length; i++) {
            if (i < cashFlows.length) {
                cashFlows[i] = -actualPremiumsPerYear[i];
            }
        }

        // 最后一年添加收益
        if (cashFlows.length > 0) {
            cashFlows[cashFlows.length - 1] += totalReturnAtEnd;
        }

        const compoundInterest = calculateIRR(cashFlows);

        return {
            simpleInterest: simpleInterest,
            compoundInterest: compoundInterest,
            cashFlows: cashFlows
        };
    };

    return {
        calculating,
        calculationResult,
        validateForm,
        calculateLocal
    };
} 