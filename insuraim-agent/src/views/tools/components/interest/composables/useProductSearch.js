import { ref } from 'vue';
import { productAPI } from '@/api';
import { message } from 'ant-design-vue';
import { toTraditional } from '@/utils/chineseConverter.js';
/**
 * 产品搜索和选择相关逻辑
 * @returns {Object} 产品搜索相关的状态和方法
 * @param {boolean} isTraditional 是否使用繁体中文搜索
 */
export function useProductSearch(isTraditional = false, { isCashValue = 0 }) {
    const productList = ref([]);
    const productLoading = ref(false);
    const searchKeyword = ref('');
    const selectedRegion = ref('香港'); // 默认地区
    const selectedMainType = ref(''); // 默认险种
    // 防抖处理
    let searchTimeout = null;

    /**
     * 获取产品列表
     */
    const getProductList = async () => {
        try {
            productLoading.value = true;

            // 构建API请求参数
            const params = {
                pageNum: 1,
                pageSize: 50, // 获取更多产品
            };

            // 添加地区筛选
            if (selectedRegion.value) {
                params.region = selectedRegion.value;
            }
            // 添加险种筛选
            if (selectedMainType.value) {
                params.categoryCode = selectedMainType.value;
            }
            // 只有当搜索关键字不为空时，才添加到请求参数中
            if (searchKeyword.value && searchKeyword.value.trim() !== '') {
                params.productName = isTraditional ? toTraditional(searchKeyword.value.trim()) : searchKeyword.value.trim();
            }
            if (isCashValue && isCashValue == 1) {
                params.isCashValue = isCashValue;
            }

            const res = await productAPI.getProductList(params);
            console.log('获取产品列表结果123:', res);
            // 处理产品数据，确保包含所有必要字段
            productList.value = (res.records || []).map(product => ({
                id: product.id,
                productId: product.id, // 兼容旧代码
                name: product.productName,
                productName: product.productName,
                companyName: product.companyName || '未知',
                productCode: product.id,
                region: product.region || selectedRegion.value,
                mainType: product.categoryName || selectedMainType.value,
                categoryName: product.categoryName,
                guaranteePeriod: product.guaranteePeriod,
                ageRange: product.ageRange,
                currencies: product.currencies,
                paymentTerm: product.paymentTerm,
                paymentMethods: product.paymentMethods,
                subProductList: product.subProductList
            }));

            console.log('搜索关键字:', searchKeyword.value, '地区:', selectedRegion.value, '获取产品列表结果:', productList.value);
        } catch (error) {
            console.error('获取产品列表失败', error);
            message.error('获取产品列表失败');
        } finally {
            productLoading.value = false;
        }
    }

    /**
     * 处理产品搜索输入
     * @param {string} value 搜索关键字
     * @param {string} region 地区筛选（可选）
     * @param {string} mainType 险种筛选（可选）
     */
    const onProductSearch = (value, region, mainType) => {
        searchKeyword.value = value;

        // 如果提供了地区参数，则更新地区
        if (region) {
            selectedRegion.value = region;
        }

        // 如果提供了险种参数，则更新险种
        if (mainType) {
            selectedMainType.value = mainType;
        }

        // 清除之前的定时器
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }

        // 设置新的定时器，延迟300ms执行搜索，避免频繁请求
        searchTimeout = setTimeout(() => {
            getProductList();
        }, 300);
    };

    /**
     * 处理产品选择变更
     * @param {string} productId 选中的产品ID
     * @returns {object|null} 选中的产品对象或null
     */
    const getSelectedProduct = (productId) => {
        if (!productId) {
            return null;
        }

        return productList.value.find(item => item.id === productId || item.productId === productId) || null;
    };

    /**
     * 设置筛选地区
     * @param {string} region 地区名称
     */
    const setRegion = (region) => {
        if (region && selectedRegion.value !== region) {
            selectedRegion.value = region;
            getProductList();
        }
    };
    const setMainType = (mainType) => {
        if (mainType && selectedMainType.value !== mainType) {
            selectedMainType.value = mainType;
            getProductList();
        }
    };
    // 初始加载产品列表
    getProductList();

    return {
        productList,
        productLoading,
        searchKeyword,
        selectedRegion,
        onProductSearch,
        getSelectedProduct,
        getProductList,
        setRegion,
        setMainType
    };
} 