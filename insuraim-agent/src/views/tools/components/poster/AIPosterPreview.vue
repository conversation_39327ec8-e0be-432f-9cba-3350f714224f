<template>
    <a-modal :visible="visible" @update:visible="$emit('update:visible', $event)" title="AI生成海报预览" width="800px"
        :footer="null" :destroyOnClose="true" @cancel="handleClose">
        <a-spin :spinning="loading">
            <div class="poster-preview-content">
                <div v-if="posterUrl" class="poster-image-container">
                    <img :src="posterUrl" alt="AI生成海报" class="poster-image" />

                    <div class="poster-info">
                        <h3>{{ styleName }}</h3>
                        <p>产品: {{ productName }}</p>
                    </div>

                    <div class="poster-actions">
                        <a-button type="primary" @click="downloadPoster">
                            <template #icon>
                                <Icon icon="material-symbols:download" class="mr-1" />
                            </template>
                            下载海报
                        </a-button>
                        <a-button @click="regeneratePoster" :loading="regenerating">
                            <template #icon>
                                <Icon icon="material-symbols:refresh" class="mr-1" />
                            </template>
                            重新生成
                        </a-button>
                    </div>
                </div>

                <div v-else-if="error" class="poster-error">
                    <Icon icon="material-symbols:error" class="error-icon" />
                    <p>{{ error }}</p>
                    <a-button @click="regeneratePoster" :loading="regenerating">
                        重新生成
                    </a-button>
                </div>
            </div>
        </a-spin>
    </a-modal>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue';
import { Icon } from '@iconify/vue';
import { message } from 'ant-design-vue';

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    posterUrl: {
        type: String,
        default: ''
    },
    productName: {
        type: String,
        default: ''
    },
    styleName: {
        type: String,
        default: ''
    },
    loading: {
        type: Boolean,
        default: false
    },
    error: {
        type: String,
        default: ''
    }
});

const emit = defineEmits(['update:visible', 'close', 'download', 'regenerate']);

// 是否正在重新生成海报
const regenerating = ref(false);

// 关闭对话框
const handleClose = () => {
    emit('update:visible', false);
    emit('close');
};

// 下载海报
const downloadPoster = () => {
    if (!props.posterUrl) {
        message.error('海报图片不存在，无法下载');
        return;
    }

    try {
        // 创建一个隐藏的a标签用于下载
        const link = document.createElement('a');
        link.href = props.posterUrl;
        link.download = `${props.productName || 'AI海报'}_${new Date().getTime()}.png`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // 通知下载事件
        emit('download', props.posterUrl);
        message.success('海报下载成功');
    } catch (error) {
        console.error('下载海报出错:', error);
        message.error('下载海报失败，请稍后再试');
    }
};

// 重新生成海报
const regeneratePoster = () => {
    regenerating.value = true;
    emit('regenerate');

    // 模拟重新生成完成，实际应该由父组件控制
    setTimeout(() => {
        regenerating.value = false;
    }, 1000);
};
</script>

<style scoped>
.poster-preview-content {
    padding: 16px 0;
    min-height: 300px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.poster-image-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
}

.poster-image {
    width: 100%;
    max-width: 500px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    object-fit: contain;
}

.poster-info {
    text-align: center;
    margin-top: 16px;
}

.poster-info h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
}

.poster-info p {
    font-size: 14px;
    color: #666;
}

.poster-actions {
    display: flex;
    gap: 12px;
    margin-top: 16px;
}

.poster-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 24px;
    text-align: center;
    gap: 16px;
}

.error-icon {
    font-size: 48px;
    color: #ff4d4f;
}

.poster-error p {
    color: #666;
    max-width: 400px;
}
</style>