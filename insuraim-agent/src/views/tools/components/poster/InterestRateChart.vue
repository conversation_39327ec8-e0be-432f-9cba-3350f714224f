<template>
    <div class="interest-rate-chart">
        <div class="chart-options mb-4 flex items-center">
            <a-radio-group v-model:value="chartType" button-style="solid">
                <a-radio-button value="line">
                    <div class="flex items-center">
                        <Icon icon="material-symbols:show-chart" />
                        <span>折线图</span>
                    </div>
                </a-radio-button>
                <a-radio-button value="bar">
                    <div class="flex items-center">
                        <Icon icon="material-symbols:bar-chart" />
                        <span>柱状图</span>
                    </div>
                </a-radio-button>
                <a-radio-button value="area">
                    <div class="flex items-center">
                        <Icon icon="material-symbols:area-chart" />
                        <span>面积图</span>
                    </div>
                </a-radio-button>
            </a-radio-group>

            <a-select v-model:value="dataType" style="width: 150px; margin-left: 16px" @change="handleDataTypeChange">
                <a-select-option value="surrender">退保价值</a-select-option>
                <a-select-option value="IRR">IRR</a-select-option>
                <a-select-option value="singleton">单利</a-select-option>
            </a-select>
        </div>

        <div ref="chartRef" class="chart-container h-80 bg-white rounded-lg shadow-sm"></div>

    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue';
import * as echarts from 'echarts';
import { Icon } from '@iconify/vue';

const props = defineProps({
    chartData: {
        type: Array,
        default: () => []
    }
});

const chartRef = ref(null);
const chartType = ref('line');
const dataType = ref('surrender');
let chart = null;

// 监听数据变化
watch(() => props.chartData, () => {
    renderChart();
}, { deep: true });

// 监听图表类型变化
watch(() => chartType.value, () => {
    renderChart();
});

// 监听数据类型变化
const handleDataTypeChange = () => {
    renderChart();
};

// 格式化货币
const formatCurrency = (value) => {
    if (!value) return '0';
    return new Intl.NumberFormat('zh-HK', {
        style: 'currency',
        currency: 'HKD',
        minimumFractionDigits: 2
    }).format(value);
};

// 初始化图表
onMounted(() => {
    if (chartRef.value) {
        chart = echarts.init(chartRef.value);
        renderChart();
        window.addEventListener('resize', handleResize);
    }
});

// 销毁图表
onUnmounted(() => {
    if (chart) {
        chart.dispose();
        window.removeEventListener('resize', handleResize);
    }
});

// 处理窗口大小变化
const handleResize = () => {
    if (chart) {
        chart.resize();
    }
};

// 渲染图表
const renderChart = () => {
    if (!chart || !props.chartData || props.chartData.length === 0) return;

    // 准备数据
    const years = props.chartData.map(item => item.year);
    const ages = props.chartData.map(item => item.age);
    const values = props.chartData.map(item => {
        // 根据选择的数据类型返回不同的值
        switch (dataType.value) {
            case 'surrender':
                return item.surrender;
            case 'IRR':
                return item.IRR === 'NaN' ? null : item.IRR;
            case 'singleton':
                return item.singleton;
            default:
                return item.surrender;
        }
    });

    // 图表配置
    const option = {
        tooltip: {
            trigger: 'axis',
            formatter: function (params) {
                const index = params[0].dataIndex;
                const item = props.chartData[index];

                let content = `<div style="font-weight:bold">第${item.year}年 (${item.age}岁)</div>`;
                content += `<div>累计保费: ${formatCurrency(item.totalPremum)}</div>`;

                switch (dataType.value) {
                    case 'surrender':
                        content += `<div>退保价值: ${formatCurrency(item.surrender)}</div>`;
                        break;
                    case 'IRR':
                        content += `<div>内部收益率: ${item.IRR === 'NaN' ? '暂无数据' : item.IRR + '%'}</div>`;
                        break;
                    case 'singleton':
                        content += `<div>单利: ${item.singleton === 'NaN' ? '暂无数据' : item.singleton + '%'}</div>`;
                        break;
                }

                return content;
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: years,
            name: '年份',
            nameLocation: 'middle',
            nameGap: 30,
            axisLabel: {
                formatter: '{value}年'
            }
        },
        yAxis: {
            type: 'value',
            name: getYAxisName(),
            axisLabel: {
                formatter: (value) => {
                    if (dataType.value === 'surrender') {
                        return formatCurrency(value).replace('HK$', '');
                    } else {
                        return value + '%';
                    }
                }
            }
        },
        series: [
            {
                name: getSeriesName(),
                type: chartType.value === 'area' ? 'line' : chartType.value,
                data: values,
                areaStyle: chartType.value === 'area' ? {} : null,
                smooth: true,
                lineStyle: {
                    width: 3
                },
                itemStyle: {
                    color: getSeriesColor()
                },
                areaStyle: chartType.value === 'area' ? {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: getSeriesColor(0.6) },
                        { offset: 1, color: getSeriesColor(0.1) }
                    ])
                } : null,
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                animationDuration: 1500,
                animationEasing: 'elasticOut'
            }
        ]
    };

    chart.setOption(option);
};

// 获取Y轴名称
const getYAxisName = () => {
    switch (dataType.value) {
        case 'surrender':
            return '退保价值';
        case 'IRR':
            return 'IRR (%)';
        case 'singleton':
            return '单利 (%)';
        default:
            return '';
    }
};

// 获取系列名称
const getSeriesName = () => {
    switch (dataType.value) {
        case 'surrender':
            return '退保价值';
        case 'IRR':
            return 'IRR';
        case 'singleton':
            return '单利';
        default:
            return '';
    }
};

// 获取系列颜色
const getSeriesColor = (alpha = 1) => {
    let color;
    switch (dataType.value) {
        case 'surrender':
            color = `rgba(24, 144, 255, ${alpha})`;
            break;
        case 'IRR':
            color = `rgba(82, 196, 26, ${alpha})`;
            break;
        case 'singleton':
            color = `rgba(250, 173, 20, ${alpha})`;
            break;
        default:
            color = `rgba(24, 144, 255, ${alpha})`;
    }
    return color;
};
</script>

<style scoped>
.chart-container {
    width: 100%;
    min-height: 320px;
}

:deep(.ant-radio-button-wrapper) {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

:deep(.ant-radio-button-wrapper .iconify) {
    margin-right: 4px;
}
</style>