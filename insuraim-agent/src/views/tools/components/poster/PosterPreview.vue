<template>
    <div class="poster-preview">
        <a-modal :visible="localVisible" title="海报预览" width="800px" :footer="null" :destroyOnClose="true"
            @cancel="handleCancel" :bodyStyle="{ padding: '0' }">
            <div class="preview-container">
                <div class="preview-actions">
                    <a-space>
                        <a-button type="primary" @click="handleDownload">
                            下载海报
                            <template #icon><download-outlined /></template>
                        </a-button>

                        <a-button @click="handleCancel">
                            关闭
                            <template #icon><close-outlined /></template>
                        </a-button>
                    </a-space>
                </div>

                <div class="poster-container" ref="posterRef">
                    <div class="poster">
                        <!-- 海报顶部图片区域 -->
                        <div class="poster-image-header">
                            <img :src="props.templateData.preview" alt="海报头图" class="header-image" />

                        </div>

                        <!-- 海报内容区域 -->
                        <div class="poster-content" :style="{ backgroundColor: contentBgColor }">
                            <h1 class="product-name" :style="{ color: props.templateData.titleColor }">{{
                                productData.productName }}
                            </h1>
                            <!-- 产品简介 -->
                            <div class="product-intro">
                                <h3 class="section-title" :style="{ color: props.templateData.titleColor }">产品简介</h3>
                                <p class="intro-text">{{ productData.introduction }}</p>
                            </div>

                            <!-- 产品特点 -->
                            <div class="product-features">
                                <h3 class="section-title" :style="{ color: props.templateData.titleColor }">产品特点</h3>
                                <div class="features-grid"
                                    :style="{ border: `2px solid ${props.templateData.titleColor}` }">
                                    <div v-for="(feature, index) in displayFeatures" :key="index" class="feature-item">
                                        <div class="feature-content">
                                            <h4 class="feature-title" :style="{ color: props.templateData.titleColor }">
                                                {{ index + 1 + '.' }} {{ feature.title
                                                }}</h4>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 案例演示 -->
                            <div class="case-demo" v-if="hasChartData">
                                <h3 class="section-title" :style="{ color: props.templateData.titleColor }">案例演示</h3>
                                <div class="case-card"
                                    :style="{ border: `2px solid ${props.templateData.titleColor}` }">
                                    <div class="case-grid">
                                        <div class="case-item">
                                            <span class="case-label">产品名称：</span>
                                            <span class="case-value">{{ productData.interestRate.productName }}</span>
                                        </div>
                                        <div class="case-item">
                                            <span class="case-label">性别：</span>
                                            <span class="case-value">{{ productData.interestRate.gender }}</span>
                                        </div>
                                        <div class="case-item">
                                            <span class="case-label">年龄：</span>
                                            <span class="case-value">{{ productData.interestRate.years }}</span>
                                        </div>
                                        <div class="case-item">
                                            <span class="case-label">吸烟状态：</span>
                                            <span class="case-value">{{ productData.interestRate.smoking_status
                                                }}</span>
                                        </div>
                                        <div class="case-item">
                                            <span class="case-label">保费：</span>
                                            <span class="case-value">{{ productData.interestRate.annualPremium }}</span>
                                        </div>
                                        <div class="case-item">
                                            <span class="case-label">缴费方式：</span>
                                            <span class="case-value">{{ productData.interestRate.paymentMode }}</span>
                                        </div>
                                        <div class="case-item">
                                            <span class="case-label">保障期限：</span>
                                            <span class="case-value">{{ productData.interestRate.coverageTerm }}</span>
                                        </div>
                                        <div class="case-item">
                                            <span class="case-label">缴费期限：</span>
                                            <span class="case-value">{{ productData.interestRate.premiumPaymentTerm
                                                }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 收益走势表格 -->
                            <div class="chart-section" v-if="hasChartData">
                                <h3 class="section-title" :style="{ color: props.templateData.titleColor }">收益走势</h3>
                                <div class="table-container"
                                    :style="{ border: `2px solid ${props.templateData.titleColor}` }">
                                    <table class="interest-table">
                                        <thead>
                                            <tr :style="{ backgroundColor: props.templateData.color }">
                                                <th :style="{ color: props.templateData.titleColor }">年龄</th>
                                                <th :style="{ color: props.templateData.titleColor }">保单年度</th>
                                                <th :style="{ color: props.templateData.titleColor }">总保费</th>
                                                <th :style="{ color: props.templateData.titleColor }">现金价值</th>
                                                <th :style="{ color: props.templateData.titleColor }">复利</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="(item, index) in displayInterestRateData" :key="index"
                                                :style="{ backgroundColor: index % 2 === 0 ? 'rgba(255,255,255,0.8)' : 'rgba(255,255,255,0.5)' }">
                                                <td>{{ item.age }}</td>
                                                <td>{{ item.year }}年</td>
                                                <td>{{ formatCurrency(item.totalPremum) }}</td>
                                                <td>{{ formatCurrency(item.surrender) }}</td>
                                                <td>{{ formatPercentage(item.IRR) }}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </a-modal>
    </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, computed, onMounted, watch } from 'vue';
import { DownloadOutlined, CloseOutlined } from '@ant-design/icons-vue';
import html2canvas from 'html2canvas';

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    templateData: {
        type: Object,
        default: () => ({})
    },
    productData: {
        type: Object,
        default: () => ({})
    }
});

const emit = defineEmits(['cancel', 'update:visible']);

const localVisible = ref(false);
const posterRef = ref(null);

// 监听props.visible变化，更新本地变量
watch(() => props.visible, (newVal) => {
    localVisible.value = newVal;
}, { immediate: true });

// 监听本地变量变化，通知父组件更新
watch(() => localVisible.value, (newVal) => {
    if (newVal !== props.visible) {
        emit('update:visible', newVal);
    }
});

// 计算内容区域背景色（浅色）
const contentBgColor = computed(() => {
    const baseColor = props.templateData.color
    return baseColor;
});

// 显示的产品特点（最多4个）
const displayFeatures = computed(() => {
    if (!props.productData.productFeature) return [];
    return props.productData.productFeature.slice(0, 4);
});

// 是否有图表数据
const hasChartData = computed(() => {
    return props.productData.interestRate && props.productData.interestRate.list && props.productData.interestRate.list.length > 0;
});

// 收益率数据
const interestRateData = computed(() => {
    if (!hasChartData.value) return [];
    return props.productData.interestRate.list;
});

// 显示的收益率数据（选择关键年份）
const displayInterestRateData = computed(() => {
    if (!interestRateData.value || interestRateData.value.length === 0) return [];

    // 筛选关键年份：第1年、第5年、第10年、第20年、第30年
    const keyYears = [1, 5, 10, 15, 30];
    const result = interestRateData.value.filter(item => keyYears.includes(item.year));

    // 如果结果少于5条，则补充到5条
    if (result.length < 5 && interestRateData.value.length > 5) {
        const additionalData = interestRateData.value
            .filter(item => !keyYears.includes(item.year))
            .slice(0, 5 - result.length);
        return [...result, ...additionalData].sort((a, b) => a.year - b.year);
    }

    return result;
});

// 格式化货币
const formatCurrency = (value) => {
    if (!value) return '0';
    return new Intl.NumberFormat('zh-HK', {
        style: 'currency',
        currency: 'HKD',
        minimumFractionDigits: 2
    }).format(value);
};

// 格式化百分比
const formatPercentage = (value) => {
    if (!value) return '0%';
    // 检查值是否已经是字符串形式的百分比
    if (typeof value === 'string' && value.includes('%')) {
        return value;
    }
    return isNaN(value) ? value : value + '%';
};


// 处理下载
const handleDownload = async () => {
    if (!posterRef.value) return;

    try {
        const canvas = await html2canvas(posterRef.value, {
            scale: 4, // 提高导出图片的清晰度
            useCORS: true, // 允许跨域请求
            allowTaint: true, // 允许污染
            logging: false, // 关闭日志以提高性能
            backgroundColor: '#ffffff' // 确保背景是白色
        });

        const link = document.createElement('a');
        link.download = `${props.productData.productCode}-海报.png`;
        link.href = canvas.toDataURL('image/png');
        link.click();
    } catch (error) {
        console.error('海报生成失败:', error);
    }
};

// 取消预览
const handleCancel = () => {
    localVisible.value = false;
    emit('cancel');
};
</script>

<style scoped>
.preview-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0px;
    background: #f0f2f5;
}

.preview-actions {
    margin-bottom: 10px;
    margin-top: 10px;
    width: 100%;
    display: flex;
    justify-content: center;
}

.poster-container {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.poster {
    width: 100%;
    min-height: 800px;
    background: #fff;
    display: flex;
    flex-direction: column;
}

/* 海报顶部图片区域 */
.poster-image-header {
    position: relative;
    overflow: hidden;
}

.header-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.overlay-title {
    position: absolute;
    bottom: -40px;
    left: 0;
    width: 100%;
    padding: 20px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
    text-align: center;
}

.product-name {
    font-size: 28px;
    font-weight: bold;
    margin: 0;
    color: #fff;
    text-align: center;
}

/* 海报内容区域 */
.poster-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 30px;
    padding: 40px 30px;
}

.section-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 15px;
    position: relative;
    padding-left: 12px;
}

/* .section-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 4px;
    height: 20px;
    width: 4px;
    background-color: currentColor;
    border-radius: 2px;
} */

.intro-text {
    font-size: 14px;
    line-height: 1.6;
    color: #333;
    margin: 0;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    border-radius: 8px;
    background-color: rgba(255, 255, 255, 0.8);
}

.feature-item {
    display: flex;
    align-items: flex-start;
    padding: 12px;
    border-radius: 8px;

    animation: fadeIn 0.5s ease forwards;
    opacity: 0;
    transform: translateY(10px);
}

@keyframes fadeIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.feature-content {
    flex: 1;
}

.feature-title {
    font-size: 14px;
    font-weight: 500;
    margin: 0;
    line-height: 1.4;
}

/* 案例演示样式 */
.case-demo {
    margin-top: 20px;
}

.case-card {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.case-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}

.case-item {
    display: flex;
    align-items: center;
    padding: 5px 0;
}

.case-label {
    font-weight: 500;
    color: #666;
    margin-right: 5px;
    font-size: 13px;
}

.case-value {
    color: #333;
    font-size: 13px;
}

.chart-section {
    margin-top: 20px;
}

/* 表格样式 */
.table-container {
    width: 100%;
    overflow-x: auto;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.interest-table {
    width: 100%;
    border-collapse: collapse;
    border-radius: 8px;
    overflow: hidden;
}

.interest-table th {
    padding: 12px 8px;
    text-align: center;
    font-weight: 500;
    font-size: 13px;
    border-right: 1px solid rgba(255, 255, 255, 0.3);
}

.interest-table th:last-child {
    border-right: none;
}

.interest-table td {
    padding: 10px 8px;
    text-align: center;
    color: #333;
    font-size: 12px;
    border-right: 1px solid rgba(0, 0, 0, 0.05);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.interest-table td:last-child {
    border-right: none;
}

.interest-table tr:last-child td {
    border-bottom: none;
}

.interest-table tr:hover {
    background-color: rgba(255, 255, 255, 0.95) !important;
}
</style>