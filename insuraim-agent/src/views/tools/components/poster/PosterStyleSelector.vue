<template>
    <a-modal :visible="visible" @update:visible="$emit('update:visible', $event)" title="选择AI海报风格" width="800px"
        :footer="null" :destroyOnClose="true" @cancel="handleClose">
        <a-spin :spinning="loading">
            <div class="style-selector-content">
                <p class="style-intro">
                    请选择您喜欢的海报风格，我们将为您的产品"{{ productData.productName }}"生成精美海报
                </p>

                <div class="style-grid">
                    <div v-for="style in posterStyles" :key="style.id" class="style-card"
                        :class="{ 'style-card-selected': selectedStyle === style.id }" @click="selectStyle(style.id)">
                        <div class="style-image" :style="{ backgroundColor: style.color }">
                            <Icon :icon="style.icon" class="style-icon" />
                        </div>
                        <div class="style-info">
                            <h3 class="style-name">{{ style.name }}</h3>
                            <p class="style-desc">{{ style.description }}</p>
                        </div>
                    </div>
                </div>

                <div class="action-buttons">
                    <a-button @click="handleClose">取消</a-button>
                    <a-button type="primary" :disabled="!selectedStyle" @click="handleGenerate" :loading="loading">
                        生成海报
                    </a-button>
                </div>
            </div>
        </a-spin>
    </a-modal>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue';
import { Icon } from '@iconify/vue';

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    productData: {
        type: Object,
        default: () => ({})
    },
    loading: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['update:visible', 'close', 'select-style']);

// 选中的风格
const selectedStyle = ref('');

// 海报风格选项
const posterStyles = [
    {
        id: 'modern',
        name: '现代简约风格',
        description: '干净、简洁的设计，突出产品的主要特点和数据',
        icon: 'material-symbols:architecture',
        color: '#3498db'
    },
    {
        id: 'business',
        name: '高端商务风格',
        description: '强调专业性和信任感，适合金融保险产品',
        icon: 'material-symbols:business-center',
        color: '#2c3e50'
    },
    {
        id: 'tech',
        name: '科技创新风格',
        description: '展现前沿科技感，突出产品的创新特性',
        icon: 'material-symbols:rocket-launch',
        color: '#9b59b6'
    },
    {
        id: 'traditional',
        name: '传统文化风格',
        description: '融合中国传统元素，强调传承和稳定',
        icon: 'material-symbols:temple-buddhist',
        color: '#e74c3c'
    },
    {
        id: 'nature',
        name: '自然生活风格',
        description: '以自然、和谐的元素为主，传达安心和舒适感',
        icon: 'material-symbols:forest',
        color: '#27ae60'
    }
];

// 监听visible变化，重置选中状态
watch(() => props.visible, (newVal) => {
    if (newVal) {
        selectedStyle.value = '';
    }
});

// 选择风格
const selectStyle = (styleId) => {
    selectedStyle.value = styleId;
};

// 关闭对话框
const handleClose = () => {
    emit('update:visible', false);
    emit('close');
};

// 生成海报
const handleGenerate = () => {
    if (!selectedStyle.value) return;

    // 找到选中的风格对象
    const selectedStyleObj = posterStyles.find(style => style.id === selectedStyle.value);

    // 触发选择风格事件
    emit('select-style', {
        styleId: selectedStyle.value,
        styleName: selectedStyleObj.name,
        productData: props.productData
    });
};
</script>

<style scoped>
.style-selector-content {
    padding: 16px 0;
}

.style-intro {
    margin-bottom: 24px;
    font-size: 16px;
    color: #666;
    text-align: center;
}

.style-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    margin-bottom: 24px;
}

.style-card {
    display: flex;
    background-color: #f9f9f9;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    cursor: pointer;
}

.style-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.style-card-selected {
    border-color: #1890ff;
    background-color: #e6f7ff;
}

.style-image {
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.style-icon {
    font-size: 40px;
    color: white;
}

.style-info {
    flex: 1;
    padding: 12px 16px;
}

.style-name {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
}

.style-desc {
    font-size: 13px;
    color: #666;
    line-height: 1.4;
}

.action-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

@media (max-width: 768px) {
    .style-grid {
        grid-template-columns: 1fr;
    }
}
</style>