<template>
    <div class="poster-templates">
        <a-modal :visible="localVisible" title="选择海报模板" width="80%" :footer="null" :destroyOnClose="true"
            @cancel="handleCancel">
            <div class="template-container">
                <div class="mb-4 flex items-center justify-between">
                    <h2 class="text-lg font-medium text-gray-700 flex items-center">
                        <Icon icon="material-symbols:template" class="text-blue-500 mr-2 text-xl" />
                        选择适合的海报模板
                    </h2>
                </div>

                <a-radio-group v-model:value="selectedTemplate" class="template-radio-group">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div v-for="template in templates" :key="template.id" class="template-item">
                            <a-radio :value="template.id" class="template-radio">
                                <div class="template-card"
                                    :class="{ 'template-selected': selectedTemplate === template.id }">
                                    <div class="template-preview">
                                        <img :src="template.preview" :alt="template.name" class="preview-image" />
                                        <div class="template-overlay">
                                            <Icon v-if="selectedTemplate === template.id"
                                                icon="material-symbols:check-circle" class="check-icon" />
                                        </div>
                                    </div>
                                    <div class="template-info">
                                        <h3 class="template-name">{{ template.name }}</h3>
                                        <p class="template-desc">{{ template.description }}</p>
                                    </div>
                                </div>
                            </a-radio>
                        </div>
                    </div>
                </a-radio-group>

                <div class="template-actions mt-6">
                    <a-button @click="handleCancel">取消</a-button>
                    <a-button type="primary" :disabled="!selectedTemplate" @click="handleConfirm" class="ml-3">
                        生成海报
                        <template #icon>
                            <Icon icon="material-symbols:image" />
                        </template>
                    </a-button>
                </div>
            </div>
        </a-modal>
    </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue';
import { Icon } from '@iconify/vue';
import yiwai from '@/assets/images/poster/yiwai.png';
import zhongji from '@/assets/images/poster/zhongji.png';


const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    productData: {
        type: Object,
        default: () => ({})
    }
});

const emit = defineEmits(['cancel', 'confirm', 'update:visible']);

const selectedTemplate = ref(null);
const localVisible = ref(false);

// 监听props.visible变化，更新本地变量
watch(() => props.visible, (newVal) => {
    localVisible.value = newVal;
    if (newVal) {
        selectedTemplate.value = null;
    }
}, { immediate: true });

// 监听本地变量变化，通知父组件更新
watch(() => localVisible.value, (newVal) => {
    if (newVal !== props.visible) {
        emit('update:visible', newVal);
    }
});

// 模板列表
const templates = ref([
    {
        id: 1,
        name: '意外险',
        description: '意外险海报模板',
        preview: yiwai,
        color: '#faf1b0',
        fontColor: '#333333',
        titleColor: '#86cc65',
    },
    {
        id: 2,
        name: '重疾险',
        description: '重疾险海报模板',
        preview: zhongji,
        color: '#fff1be',
        fontColor: '#ffffff',
        titleColor: '#ff9900',
    },
    {
        id: 3,
        name: '科技紫',
        description: '科技感风格，紫色主题，展示产品创新特性',
        preview: yiwai,
        color: '#faf1b0',
        fontColor: '#333333',
        titleColor: '#86cc65',
    },
    {
        id: 4,
        name: '活力绿',
        description: '活力风格，绿色主题，突出产品增长潜力',
        preview: yiwai,
        color: '#faf1b0',
        fontColor: '#333333',
        titleColor: '#86cc65',
    },
    {
        id: 5,
        name: '典雅红',
        description: '典雅风格，红色主题，强调产品传承价值',
        preview: yiwai,
        color: '#faf1b0',
        fontColor: '#333333',
        titleColor: '#86cc65',
    },
    {
        id: 6,
        name: '清新青',
        description: '清新风格，青色主题，展示产品创新特性',
        preview: yiwai,
        color: '#faf1b0',
        fontColor: '#333333',
        titleColor: '#86cc65',
    }
]);

// 取消选择
const handleCancel = () => {
    localVisible.value = false;
    emit('cancel');
};

// 确认选择
const handleConfirm = () => {
    if (!selectedTemplate.value) return;

    const template = templates.value.find(t => t.id === selectedTemplate.value);
    emit('confirm', {
        templateId: selectedTemplate.value,
        templateData: template,
        productData: props.productData
    });
};
</script>

<style scoped>
.template-container {
    padding: 16px 0;
}

.template-radio-group {
    width: 100%;
}

.template-item {
    position: relative;
    margin-bottom: 16px;
}

.template-radio {
    display: block;
    width: 100%;
    height: 100%;
}

.template-card {
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s;
    border: 2px solid transparent;
    height: 100%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.template-selected {
    border-color: #1890ff;
    box-shadow: 0 0 10px rgba(24, 144, 255, 0.3);
}

.template-preview {
    position: relative;
    height: 300px;
    overflow: hidden;
}

.preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s;
}

.template-card:hover .preview-image {
    transform: scale(1.05);
}

.template-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s;
}

.template-selected .template-overlay {
    opacity: 1;
    background: rgba(24, 144, 255, 0.2);
}

.check-icon {
    font-size: 48px;
    color: #fff;
}

.template-info {
    padding: 16px;
}

.template-name {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
}

.template-desc {
    font-size: 14px;
    color: #666;
}

.template-actions {
    display: flex;
    justify-content: flex-end;
}
</style>