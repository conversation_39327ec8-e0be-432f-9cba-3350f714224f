<template>
    <div class="product-detail-container">
        <a-spin :spinning="loading">

            <div
                class="title-section mb-6 p-6 rounded-lg shadow-md text-white bg-gradient-to-r from-purple-500 via-purple-600 to-purple-700">
                <div class="flex justify-between items-start">
                    <div class="flex items-center">
                        <button @click="$emit('back')"
                            class="flex items-center justify-center w-10 h-10 rounded-full bg-white hover:bg-gray-100 text-blue-600 transition-all mr-4 shadow-md">
                            <Icon icon="material-symbols:arrow-left" class="text-lg" />
                        </button>
                        <div>
                            <h1 class="text-2xl font-bold page-title" style="color: white;">{{
                                productDetail.productName || '产品详情'
                            }}</h1>
                        </div>
                    </div>
                    <div class="flex space-x-3">
                        <!-- <a-button type="primary" class="poster-action-btn" @click="generatePoster" id="no-pdf">
                            <template #icon>
                                <Icon icon="material-symbols:image" class="mr-1" />
                            </template>
AI生成海报
</a-button> -->
                        <a-button type="primary" class="poster-action-btn" @click="$emit('generate-poster')"
                            id="no-pdf">
                            <template #icon>
                                <Icon icon="material-symbols:image" class="mr-1" />
                            </template>
                            生成海报
                        </a-button>
                        <a-button type="primary" class="poster-action-btn" @click="exportPdf" id="no-pdf">
                            <template #icon>
                                <Icon icon="material-symbols:image" class="mr-1" />
                            </template>
                            导出PDF
                        </a-button>
                    </div>
                </div>

                <div class="mt-4 border-t border-white border-opacity-20 pt-4" v-if="productDetail.introduction">
                    <p class="text-white text-opacity-90 text-sm leading-relaxed">
                        {{ formatIntroduction(productDetail.introduction) }}
                    </p>
                </div>
            </div>

            <div class="product-content mt-6">
                <!-- 产品特点 -->
                <a-card class="mb-6 rounded-lg" :bordered="false">
                    <template #title>
                        <div class="flex items-center">
                            <Icon icon="material-symbols:star" class="text-blue-500 mr-2" />
                            <span>产品特点</span>
                        </div>
                    </template>
                    <div v-if="productDetail.productFeature && productDetail.productFeature.length > 0"
                        class="feature-tags-container">
                        <a-tag v-for="(feature, index) in productDetail.productFeature" :key="index" class="feature-tag"
                            color="blue">
                            <Icon icon="material-symbols:check-circle" class="feature-icon mr-1" />
                            {{ feature.title }}
                        </a-tag>
                    </div>
                    <a-empty v-else description="暂无产品特点" />
                </a-card>

                <!-- 产品概述 -->
                <a-card class="mb-6" :bordered="false">
                    <template #title>
                        <div class="flex items-center">
                            <Icon icon="material-symbols:description" class="text-blue-500 mr-2" />
                            <span>产品概述</span>
                        </div>
                    </template>
                    <div v-if="productDetail.productBasic" class="product-basic" v-html="productDetail.productBasic">
                    </div>
                    <a-empty v-else description="暂无产品概述" />
                </a-card>

                <!-- 利率图表 -->
                <a-card :bordered="false">
                    <template #title>
                        <div class="flex items-center">
                            <Icon icon="material-symbols:bar-chart" class="text-blue-500 mr-2" />
                            <span>利率走势</span>
                        </div>
                    </template>
                    <interest-rate-chart v-if="productDetail.interestRate && productDetail.interestRate.list"
                        :chart-data="productDetail.interestRate.list" id="no-pdf" />
                    <a-empty v-else description="暂无利率数据" />

                    <!-- 收益走势表格 -->
                    <div class="mt-6"
                        v-if="productDetail.interestRate && productDetail.interestRate.list && productDetail.interestRate.list.length > 0">
                        <div class="flex justify-between items-center mb-4" id="no-pdf">
                            <h3 class="text-lg font-medium">收益走势详情</h3>
                            <a-button type="primary" @click="exportToExcel">
                                <Icon icon="material-symbols:download" class="mr-1" />
                                导出Excel
                            </a-button>
                        </div>

                        <a-table :dataSource="filteredTableData" :columns="columns" :scroll="{ x: 800 }" size="middle"
                            :rowClassName="(record, index) => index % 2 === 0 ? 'table-row-light' : 'table-row-dark'"
                            :pagination="false" />
                    </div>
                </a-card>
            </div>
        </a-spin>
    </div>

    <PosterStyleSelector :visible="showStyleSelector" @update:visible="showStyleSelector = $event"
        :product-data="productDetail" :loading="generatingPoster" @close="handleCloseStyleSelector"
        @select-style="handleSelectStyle" />

    <AIPosterPreview :visible="showPosterPreview" @update:visible="showPosterPreview = $event" :poster-url="posterUrl"
        :product-name="productDetail.productName" :style-name="currentStyleName" :loading="generatingPoster"
        :error="posterError" @close="handleClosePosterPreview" @regenerate="handleRegeneratePoster" />
</template>

<script setup>
import { defineProps, defineEmits, ref, computed, watch } from 'vue';
import { Icon } from '@iconify/vue';
import InterestRateChart from './InterestRateChart.vue';
import { marked } from 'marked';
import { exportExcel } from '@/utils/excel';
import { message } from 'ant-design-vue';
import { exportElementToPDF } from '@/utils/pdf';
import PosterStyleSelector from './PosterStyleSelector.vue';
import AIPosterPreview from './AIPosterPreview.vue';
import { generateAIPoster } from '@/api/poster';

const props = defineProps({
    productDetail: {
        type: Object,
        default: () => ({})
    },
    loading: {
        type: Boolean,
        default: false
    }
});

defineEmits(['back', 'generate-poster']);

// 海报生成相关状态
const showStyleSelector = ref(false);
const generatingPoster = ref(false);
const showPosterPreview = ref(false);
const posterUrl = ref('');
const currentStyleName = ref('');
const posterError = ref('');

// AI生成海报
const generatePoster = () => {
    if (!props.productDetail || !props.productDetail.productName) {
        message.error('产品数据不完整，无法生成海报');
        return;
    }
    showStyleSelector.value = true;
};

// 处理关闭海报风格选择器
const handleCloseStyleSelector = () => {
    showStyleSelector.value = false;
};

// 处理选择海报风格
const handleSelectStyle = async (styleData) => {
    try {
        generatingPoster.value = true;
        showStyleSelector.value = false;
        posterError.value = '';
        currentStyleName.value = styleData.styleName;

        // 生成对应风格的提示词
        const prompt = generatePromptByStyle(styleData.styleId, props.productDetail);
        const negative_prompt = generateNegativePrompt();

        console.log('使用提示词:', prompt);

        // 调用AI生成海报API
        const response = await generateAIPoster({
            styleId: styleData.styleId,
            prompt,
            negative_prompt,
            productData: props.productDetail
        });

        if (response && response.data && response.data.length > 0) {
            // API返回的图片URL
            posterUrl.value = response.data[0].url;
            showPosterPreview.value = true;
        } else {
            throw new Error('生成海报失败，未获取到图片数据');
        }
    } catch (error) {
        console.error('生成海报错误:', error);
        posterError.value = error.message || '生成海报失败，请稍后再试';
        showPosterPreview.value = true;
    } finally {
        generatingPoster.value = false;
    }
};

// 处理关闭海报预览
const handleClosePosterPreview = () => {
    showPosterPreview.value = false;
};

// 处理重新生成海报
const handleRegeneratePoster = () => {
    // 模拟重新选择相同风格生成
    showPosterPreview.value = false;
    setTimeout(() => {
        showStyleSelector.value = true;
    }, 300);
};

// 生成风格对应的提示词
const generatePromptByStyle = (styleId, productData) => {
    // 提取产品数据中的关键信息
    const productName = productData.productName || '';
    const features = (productData.productFeature || []).map(feature => feature.title).join(', ');
    const introduction = productData.introduction || '';

    // 获取重要产品特性和数据
    let currencies = '多种货币';
    let coveragePeriod = '终身';
    let premiumPeriod = '5年';

    // 提取HTML表格中的数据
    if (productData.productBasic) {
        // 货币
        const currencyMatch = productData.productBasic.includes('保單貨幣') ?
            productData.productBasic.match(/保單貨幣[\s\S]*?<td[^>]*>([\s\S]*?)<\/td>/) : null;
        currencies = currencyMatch ? currencyMatch[1].trim() : '多种货币';

        // 保障期限
        const coverageMatch = productData.productBasic.includes('保障年期') ?
            productData.productBasic.match(/保障年期[\s\S]*?<td[^>]*>([\s\S]*?)<\/td>/) : null;
        coveragePeriod = coverageMatch ? coverageMatch[1].trim() : '终身';

        // 保费供款期
        const premiumMatch = productData.productBasic.includes('保費供款年期') ?
            productData.productBasic.match(/保費供款年期[\s\S]*?<td[^>]*>([\s\S]*?)<\/td>/) : null;
        premiumPeriod = premiumMatch ? premiumMatch[1].trim() : '5年';
    }

    // 提取利率数据亮点
    const longTermRate = productData.interestRate?.list?.find(item => item.year >= 30)?.IRR || '';
    const returnMultiple = productData.interestRate?.list?.find(item => item.multiple >= 5)?.multiple || '';

    // 提取主要产品亮点
    const keyFeatures = (productData.productFeature || [])
        .slice(0, 4)
        .map(feature => feature.title)
        .join('、');

    // 打印提取到的数据，方便调试
    console.log('提取的数据:', {
        currencies,
        coveragePeriod,
        premiumPeriod,
        longTermRate,
        returnMultiple,
        keyFeatures
    });

    // 根据不同风格生成提示词
    switch (styleId) {
        case 'modern':
            return generateModernStylePrompt(productName, features, introduction, currencies, coveragePeriod, longTermRate, keyFeatures);
        case 'business':
            return generateBusinessStylePrompt(productName, features, introduction, currencies, premiumPeriod, longTermRate, returnMultiple, keyFeatures);
        case 'tech':
            return generateTechStylePrompt(productName, features, introduction, currencies, longTermRate, returnMultiple, keyFeatures);
        case 'traditional':
            return generateTraditionalStylePrompt(productName, features, introduction, currencies, coveragePeriod, premiumPeriod, keyFeatures);
        case 'nature':
            return generateNatureStylePrompt(productName, features, introduction, currencies, coveragePeriod, keyFeatures);
        default:
            return generateDefaultPrompt(productName, features, introduction, keyFeatures);
    }
};

// 现代简约风格提示词
const generateModernStylePrompt = (productName, features, introduction, currencies, coveragePeriod, longTermRate, keyFeatures) => {
    return `Create a modern minimalist insurance poster for the financial product "${productName}". 
    Use clean lines, plenty of white space, and a limited color palette with blue and gray tones. 
    Include these key benefits as elegant typography points: "${keyFeatures}".
    Include important data points like "${currencies}" currencies and "${coveragePeriod}" coverage.
    Feature ${longTermRate ? `a clear visual showing ${longTermRate}% long-term return rate` : 'visual elements representing growth and stability'}.
    Include abstract geometric shapes that suggest protection, global finance, and wealth growth.
    The composition should be balanced with a clear hierarchy, emphasizing the product name prominently.
    Style: Minimalist, modern, clean, professional, elegant. 
    No people or faces. High quality advertising poster for financial products with a focus on clarity and elegance.`;
};

// 高端商务风格提示词
const generateBusinessStylePrompt = (productName, features, introduction, currencies, premiumPeriod, longTermRate, returnMultiple, keyFeatures) => {
    return `Create a premium business-style insurance poster for the financial product "${productName}". 
    Use rich deep colors like navy blue, gold, and dark gray representing wealth and stability. 
    Incorporate subtle luxury elements like marble textures, gold accents, and global finance imagery.
    Feature elegant serif typography highlighting: "${keyFeatures}".
    Include ${currencies} as a key selling point for global investment opportunities.
    ${returnMultiple ? `Prominently display "${returnMultiple}x return" as a central visual element.` : ''}
    ${longTermRate ? `Include "${longTermRate}% long-term return rate" as a key data point.` : ''}
    Mention "${premiumPeriod}" premium payment period in an elegant typographic style.
    The overall feel should convey trust, wealth management, and financial security with global perspective.
    Style: Luxurious, corporate, professional, trustworthy, elegant.
    No people or faces. High quality advertising poster for a premium financial product aimed at sophisticated investors.`;
};

// 科技创新风格提示词
const generateTechStylePrompt = (productName, features, introduction, currencies, longTermRate, returnMultiple, keyFeatures) => {
    return `Create a futuristic tech-inspired insurance poster for the financial product "${productName}". 
    Use a dark background with bright glowing elements in blue, purple and cyan representing digital finance.
    Incorporate digital elements like circuit patterns, data visualizations, financial graphs, and global network connections.
    Feature modern sans-serif typography displaying key benefits: "${keyFeatures}".
    Include visual representation of multi-currency capabilities: "${currencies}".
    ${longTermRate ? `Display "${longTermRate}%" in a futuristic digital counter or holographic display.` : ''}
    ${returnMultiple ? `Visualize "${returnMultiple}x return" as a central tech-inspired growth chart.` : ''}
    The overall aesthetic should feel innovative, forward-thinking and high-tech, representing the future of finance.
    Include abstract elements suggesting global connectivity, digital transformation of wealth, and technological innovation.
    Style: Futuristic, technological, innovative, digital, modern.
    No people or faces. High quality advertising poster for a cutting-edge financial product with a focus on innovation.`;
};

// 传统文化风格提示词
const generateTraditionalStylePrompt = (productName, features, introduction, currencies, coveragePeriod, premiumPeriod, keyFeatures) => {
    return `Create a traditional Chinese culture inspired insurance poster for the financial product "${productName}". 
    Use traditional colors like red, gold and cream with elegant Chinese motifs and patterns representing prosperity and fortune.
    Incorporate elements from Chinese art like mountains, clouds, dragons, phoenixes, or subtle ink-wash painting style.
    Feature elegant typography with a mix of modern and traditional elements highlighting: "${keyFeatures}".
    Include visual elements representing the "${coveragePeriod}" coverage period as a path to long-term prosperity.
    Emphasize "${premiumPeriod}" premium payment period with traditional Chinese numerical symbols.
    Represent multi-currency capabilities ("${currencies}") using traditional Chinese cultural symbols for global trade.
    The composition should feel balanced and harmonious with a sense of legacy, permanence, and generational wealth.
    Include subtle visual elements that represent family heritage, prosperity, and the passing down of wealth.
    Style: Traditional, cultural, elegant, balanced, timeless.
    No people or faces. High quality advertising poster with Chinese cultural elements for a financial product emphasizing heritage and legacy.`;
};

// 自然生活风格提示词
const generateNatureStylePrompt = (productName, features, introduction, currencies, coveragePeriod, keyFeatures) => {
    return `Create a nature-inspired serene insurance poster for the financial product "${productName}". 
    Use a soft, warm color palette with greens, earth tones and gentle blues representing growth and stability.
    Incorporate natural elements like trees, leaves, mountains, water, or sunrise imagery suggesting growth, protection, and longevity.
    Feature organic, flowing typography highlighting the key benefits: "${keyFeatures}".
    Include subtle visual metaphors for "${coveragePeriod}" coverage - like a tree growing through seasons or a river flowing continuously.
    Represent the multi-currency options ("${currencies}") as diverse natural elements coming together in harmony.
    Integrate visual metaphors of natural growth and prosperity that relate to financial security and long-term planning.
    The overall mood should feel calming, nurturing, and life-affirming while conveying financial stability and growth.
    Include subtle elements suggesting family protection, natural cycles of growth, and sustainable prosperity.
    Style: Natural, organic, calm, growth-oriented, serene.
    No people or faces. High quality advertising poster with natural elements for a financial product focusing on sustainable growth and prosperity.`;
};

// 默认提示词
const generateDefaultPrompt = (productName, features, introduction, keyFeatures) => {
    return `Create a professional insurance poster for the financial product "${productName}". 
    Use a balanced color scheme that feels trustworthy and professional.
    Highlight these key features prominently: "${keyFeatures}".
    Include visual elements representing financial security, growth, and protection.
    The composition should be clear and focused with strong visual hierarchy emphasizing the product name and key benefits.
    Style: Professional, clean, trustworthy, balanced.
    No people or faces. High quality advertising poster for a financial insurance product.`;
};

// 生成负面提示词（用于所有风格）
const generateNegativePrompt = () => {
    return "blurry, low quality, distorted, cartoon, childish, low resolution, draft, text, watermark, signature, bad anatomy, weird hands, ugly, bad proportions, chinese text, english text";
};

// 渲染Markdown内容
const renderMarkdown = (text) => {
    if (!text) return '';
    return marked(text);
};

// 表格相关数据
const searchText = ref('');
const pageSize = ref(10);

// 格式化货币
const formatCurrency = (value) => {
    if (!value && value !== 0) return '-';
    return new Intl.NumberFormat('zh-HK', {
        style: 'currency',
        currency: 'HKD',
        minimumFractionDigits: 2
    }).format(value);
};

// 格式化介绍 去除换行符号
const formatIntroduction = (text) => {
    if (!text) return '';
    return text.replace(/\n/g, '');
};

// 格式化百分比
const formatPercentage = (value) => {
    if (isNaN(value)) return '-';
    return value + '%';
};

// 表格列定义
const columns = [
    {
        title: '年龄',
        dataIndex: 'age',
        key: 'age',
        sorter: (a, b) => a.age - b.age,
    },
    {
        title: '保单年度',
        dataIndex: 'year',
        key: 'year',
        sorter: (a, b) => a.year - b.year,
        customRender: (text) => `${text.value}年`
    },
    {
        title: '总保费',
        dataIndex: 'totalPremum',
        key: 'totalPremum',
        sorter: (a, b) => a.totalPremum - b.totalPremum,
        customRender: (text) => formatCurrency(text.value)
    },
    {
        title: '现金价值',
        dataIndex: 'surrender',
        key: 'surrender',
        sorter: (a, b) => a.surrender - b.surrender,
        customRender: (text) => formatCurrency(text.value)
    },
    {
        title: '复利',
        dataIndex: 'IRR',
        key: 'IRR',
        sorter: (a, b) => {
            const aValue = a.IRR === 'NaN' ? -Infinity : parseFloat(a.IRR);
            const bValue = b.IRR === 'NaN' ? -Infinity : parseFloat(b.IRR);
            return aValue - bValue;
        },
        customRender: (text) => formatPercentage(text.value)
    },
    {
        title: '单利',
        dataIndex: 'singleton',
        key: 'singleton',
        sorter: (a, b) => {
            const aValue = a.singleton === 'NaN' ? -Infinity : parseFloat(a.singleton);
            const bValue = b.singleton === 'NaN' ? -Infinity : parseFloat(b.singleton);
            return aValue - bValue;
        },
        customRender: (text) => formatPercentage(text.value)
    }
];

// 导出到Excel
const exportToExcel = () => {
    try {
        // 创建用于导出的列定义 - 不使用render函数，因为Excel导出不会执行它们
        const exportColumns = [
            { title: '年龄', dataIndex: 'age' },
            { title: '保单年度', dataIndex: 'year' },
            { title: '总保费', dataIndex: 'totalPremum' },
            { title: '现金价值', dataIndex: 'surrender' },
            { title: '复利', dataIndex: 'IRR' },
            { title: '单利', dataIndex: 'singleton' }
        ];

        // 验证数据是否存在
        if (!filteredTableData.value || !Array.isArray(filteredTableData.value) || filteredTableData.value.length === 0) {
            // 如果没有数据，创建一个空数组作为导出数据
            console.warn('没有可导出的数据，将导出空表格');

            // 执行导出（空表格）
            const fileName = `${props.productDetail.productName || '产品'}-收益走势`;
            const options = {
                sheetName: '收益走势详情',
                autoWidth: true,
                headerStyle: {
                    color: { rgb: 'FFFFFF' },
                    fill: { patternType: 'solid', fgColor: { rgb: '1890FF' } },
                    font: { bold: true }
                },
                cellStyle: {
                    alignment: { vertical: 'middle', horizontal: 'center' },
                    border: {
                        top: { style: 'thin', color: { argb: 'D9D9D9' } },
                        left: { style: 'thin', color: { argb: 'D9D9D9' } },
                        bottom: { style: 'thin', color: { argb: 'D9D9D9' } },
                        right: { style: 'thin', color: { argb: 'D9D9D9' } }
                    }
                }
            };

            exportExcel([], exportColumns, fileName, options);
            return;
        }

        // 处理导出数据，确保格式正确，并直接应用格式化
        const exportData = filteredTableData.value.map(item => {
            // 确保每个字段都有值，防止undefined错误，并直接应用格式化
            return {
                age: item.age || '-',
                year: `${item.year || '-'}年`, // 直接格式化年份
                totalPremum: formatCurrency(item.totalPremum || 0), // 直接格式化货币
                surrender: formatCurrency(item.surrender || 0), // 直接格式化货币
                IRR: isNaN(item.IRR) ? '-' : `${item.IRR}%`, // 直接格式化百分比
                singleton: isNaN(item.singleton) ? '-' : `${item.singleton}%` // 直接格式化百分比
            };
        });

        // 导出配置
        const fileName = `${props.productDetail.productName || '产品'}-收益走势`;
        const options = {
            sheetName: '收益走势详情',
            autoWidth: true,
            headerStyle: {
                font: { bold: true, color: { argb: '000000' } },
                fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: '1890FF' } },
                alignment: { vertical: 'middle', horizontal: 'center' },
            },
            cellStyle: {
                alignment: { vertical: 'middle', horizontal: 'center' },
                border: {
                    top: { style: 'thin', color: { argb: '144deb' } },
                    left: { style: 'thin', color: { argb: '144deb' } },
                    bottom: { style: 'thin', color: { argb: '144deb' } },
                    right: { style: 'thin', color: { argb: '144deb' } }
                }
            }
        };

        // 执行导出
        exportExcel(exportData, exportColumns, fileName, options);
    } catch (error) {
        console.error('导出Excel时发生错误:', error);
        // 使用ant-design-vue的消息提示
        message.error('导出Excel失败，请稍后再试');
    }
};
const exportPdf = async () => {
    try {
        // 显示加载提示
        message.loading('正在生成PDF，请稍候...', 0);

        // 获取原始DOM元素
        const originalDom = document.querySelector('.product-detail-container');
        if (!originalDom) {
            message.destroy();
            message.error('未找到产品详情容器，导出失败');
            return;
        }

        // 创建深度克隆
        const clonedDom = originalDom.cloneNode(true);

        // 设置克隆DOM的样式为不可见但保持结构
        clonedDom.style.position = 'absolute';
        clonedDom.style.left = '-9999px';
        clonedDom.style.top = '-9999px';
        clonedDom.style.opacity = '1';
        clonedDom.style.width = originalDom.offsetWidth + 'px';

        // 查找所有id为no-pdf的元素
        const noPdfElements = clonedDom.querySelectorAll('[id="no-pdf"]');

        // 移除找到的所有元素
        noPdfElements.forEach(element => {
            if (element && element.parentNode) {
                element.parentNode.removeChild(element);
            }
        });

        // 将克隆DOM临时附加到body
        document.body.appendChild(clonedDom);

        try {
            // 使用处理后的DOM调用exportElementToPDF
            const fileName = props.productDetail.productName || '产品详情';
            const result = await exportElementToPDF(clonedDom, fileName);

            // 显示结果提示
            if (result) {
                message.success('PDF导出成功');
            } else {
                message.error('PDF导出失败，请稍后重试');
            }
        } finally {
            // 无论成功与否，都从文档中移除临时DOM
            if (clonedDom && clonedDom.parentNode) {
                clonedDom.parentNode.removeChild(clonedDom);
            }

            // 关闭加载提示
            message.destroy();
        }
    } catch (error) {
        console.error('导出PDF时发生错误:', error);
        message.destroy();
        message.error('PDF导出失败，请稍后重试');
    }
};

// 过滤表格数据
const filteredTableData = computed(() => {
    if (!props.productDetail.interestRate || !props.productDetail.interestRate.list) {
        return [];
    }

    return props.productDetail.interestRate.list.map((item, index) => ({
        key: index,
        age: item.age || '-',
        year: item.year || '-',
        totalPremum: item.totalPremum || 0,
        surrender: item.surrender || 0,
        IRR: item.IRR || 'NaN',
        singleton: item.singleton || 'NaN'
    }));
});
</script>

<style scoped>
.product-detail-container {
    padding: 0;
}

.product-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.feature-tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.feature-tag {
    display: flex;
    align-items: center;
    margin-right: 8px;
    margin-bottom: 8px;
    padding: 6px 12px;
    border-radius: 16px;
}

.feature-icon {
    margin-right: 4px;
}

/* 富文本基础样式 */
.product-basic {
    line-height: 1.8;
    color: #333;
    font-size: 14px;
}

/* 富文本段落样式 */
.product-basic :deep(p) {
    margin-bottom: 16px;
    text-align: justify;
}

/* 富文本标题样式 */
.product-basic :deep(h1),
.product-basic :deep(h2),
.product-basic :deep(h3),
.product-basic :deep(h4),
.product-basic :deep(h5),
.product-basic :deep(h6) {
    margin-top: 24px;
    margin-bottom: 16px;
    font-weight: 600;
    line-height: 1.4;
    color: #333;
}

.product-basic :deep(h1) {
    font-size: 24px;
    border-bottom: 1px solid #eee;
    padding-bottom: 8px;
}

.product-basic :deep(h2) {
    font-size: 20px;
}

.product-basic :deep(h3) {
    font-size: 18px;
}

.product-basic :deep(h4) {
    font-size: 16px;
}

/* 富文本列表样式 */
.product-basic :deep(ul),
.product-basic :deep(ol) {
    padding-left: 24px;
    margin-bottom: 16px;
}

.product-basic :deep(li) {
    margin-bottom: 8px;
}

.product-basic :deep(ul li) {
    list-style-type: disc;
}

.product-basic :deep(ol li) {
    list-style-type: decimal;
}

/* 富文本链接样式 */
.product-basic :deep(a) {
    color: #1890ff;
    text-decoration: none;
    transition: color 0.3s;
}

.product-basic :deep(a:hover) {
    color: #40a9ff;
    text-decoration: underline;
}

/* 富文本引用样式 */
.product-basic :deep(blockquote) {
    padding: 8px 16px;
    margin: 16px 0;
    border-left: 4px solid #1890ff;
    background-color: #f0f7ff;
    color: #666;
}

/* 富文本代码样式 */
.product-basic :deep(code) {
    background-color: #f5f5f5;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: Consolas, Monaco, 'Andale Mono', monospace;
    font-size: 13px;
}

.product-basic :deep(pre) {
    background-color: #f5f5f5;
    padding: 16px;
    border-radius: 4px;
    overflow-x: auto;
    margin: 16px 0;
}

.product-basic :deep(pre code) {
    background-color: transparent;
    padding: 0;
}

/* 富文本图片样式 */
.product-basic :deep(img) {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin: 16px 0;
}

/* 富文本分割线样式 */
.product-basic :deep(hr) {
    border: none;
    border-top: 1px solid #eee;
    margin: 24px 0;
}

/* 富文本表格样式 - 通用样式 */
.product-basic :deep(table) {
    width: 100%;
    border-collapse: collapse;
    border-radius: 8px;
    overflow: hidden;
    margin: 16px 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #f0f0f0;
    table-layout: fixed;
    /* 固定表格布局，提高渲染性能 */
}

/* 通用表头和单元格样式 */
.product-basic :deep(th) {
    background-color: #f5f7fa;
    color: #333;
    font-weight: 600;
    text-align: left;
    padding: 12px 16px;
    border-bottom: 2px solid #e8e8e8;
    transition: background-color 0.3s;
}

.product-basic :deep(td) {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.3s;
}

.product-basic :deep(tr:last-child td) {
    border-bottom: none;
}

.product-basic :deep(tr:nth-child(even)) {
    background-color: #fafafa;
}

.product-basic :deep(tr:hover td) {
    background-color: #e6f7ff;
}

/* 两列表格特殊样式 - 常见于保险产品概述 */
.product-basic :deep(table.two-column),
.product-basic :deep(table:not([class])) {
    /* 默认应用于无类名的表格，通常是产品概述表格 */
    border: none;
    box-shadow: none;
}

.product-basic :deep(table.two-column tr),
.product-basic :deep(table:not([class]) tr) {
    border-bottom: 1px solid #f0f0f0;
}

.product-basic :deep(table.two-column tr:last-child),
.product-basic :deep(table:not([class]) tr:last-child) {
    border-bottom: none;
}

.product-basic :deep(table.two-column th),
.product-basic :deep(table:not([class]) th) {
    width: 30%;
    background-color: #f0f7ff;
    color: #1890ff;
    font-weight: 600;
    text-align: right;
    padding: 12px 16px;
    border-right: 1px solid #e8e8e8;
    border-bottom: 1px solid #e8e8e8;
}

.product-basic :deep(table.two-column td),
.product-basic :deep(table:not([class]) td) {
    width: 70%;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
}

.product-basic :deep(table.two-column tr:last-child th),
.product-basic :deep(table:not([class]) tr:last-child th),
.product-basic :deep(table.two-column tr:last-child td),
.product-basic :deep(table:not([class]) tr:last-child td) {
    border-bottom: none;
}

/* 表格响应式样式 */
@media (max-width: 768px) {
    .product-basic :deep(table) {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
        -webkit-overflow-scrolling: touch;
    }

    .product-basic :deep(th),
    .product-basic :deep(td) {
        padding: 8px 12px;
    }
}

/* 表格单元格内容对齐 */
.product-basic :deep(th[align="center"]),
.product-basic :deep(td[align="center"]) {
    text-align: center;
}

.product-basic :deep(th[align="right"]),
.product-basic :deep(td[align="right"]) {
    text-align: right;
}

/* 表格单元格内容垂直对齐 */
.product-basic :deep(th[valign="middle"]),
.product-basic :deep(td[valign="middle"]) {
    vertical-align: middle;
}

.product-basic :deep(th[valign="bottom"]),
.product-basic :deep(td[valign="bottom"]) {
    vertical-align: bottom;
}

/* 表格单元格内容不换行 */
.product-basic :deep(th.nowrap),
.product-basic :deep(td.nowrap) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 表格单元格内容换行 */
.product-basic :deep(th.wrap),
.product-basic :deep(td.wrap) {
    white-space: normal;
    word-break: break-word;
}

/* 表格单元格内容溢出处理 */
.product-basic :deep(th.overflow-auto),
.product-basic :deep(td.overflow-auto) {
    overflow: auto;
    max-height: 100px;
}

/* 打印友好的表格样式 */
@media print {
    .product-basic :deep(table) {
        box-shadow: none;
        border: 1px solid #ddd;
    }

    .product-basic :deep(th) {
        background-color: #f9f9f9 !important;
        color: #333 !important;
    }

    .product-basic :deep(tr:nth-child(even)) {
        background-color: #f9f9f9 !important;
    }
}

/* 强调内容样式 */
.product-basic :deep(strong),
.product-basic :deep(b) {
    font-weight: 600;
    color: #333;
}

.product-basic :deep(em),
.product-basic :deep(i) {
    font-style: italic;
}

.product-basic :deep(.highlight) {
    background-color: #fffbe6;
    padding: 2px 4px;
    border-radius: 2px;
}

.product-basic :deep(.note) {
    background-color: #e6f7ff;
    border-left: 4px solid #1890ff;
    padding: 8px 16px;
    margin: 16px 0;
    border-radius: 0 4px 4px 0;
}

.product-basic :deep(.warning) {
    background-color: #fff2e8;
    border-left: 4px solid #fa8c16;
    padding: 8px 16px;
    margin: 16px 0;
    border-radius: 0 4px 4px 0;
}

.table-row-light {
    background-color: #fff;
}

.table-row-dark {
    background-color: #f5f5f5;
}

:deep(.ant-card-head) {
    border-bottom: 1px solid #f0f0f0;
    padding: 0 0 16px 0;
}

:deep(.ant-card-body) {
    padding-top: 16px;
}

.poster-action-btn {
    background-color: white;
    color: #1890ff;
    border-color: white;
    font-weight: 500;
    display: flex;
    align-items: center;
}

.poster-action-btn:hover {
    background-color: rgba(255, 255, 255, 0.9);
    color: #096dd9;
    border-color: rgba(255, 255, 255, 0.9);
}
</style>