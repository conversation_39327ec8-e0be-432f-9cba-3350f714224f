<template>
    <div class="bg-white p-6 rounded-lg shadow-md">
        <div class="mb-4 flex items-center justify-between">
            <h2 class="text-lg font-medium text-gray-700 flex items-center">
                <Icon icon="material-symbols:list-alt" class="text-blue-500 mr-2 text-xl" />
                产品列表
            </h2>
        </div>

        <a-spin :spinning="loading">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <a-card v-for="product in productList" :key="product.id"
                    class="product-card hover:shadow-lg transition-all duration-300" :hoverable="true"
                    @click="selectProduct(product)">
                    <template #cover>
                        <div class="product-card-header" :class="`bg-gradient-${product.id % 5}`">
                            <Icon icon="material-symbols:inventory-2" class="text-white text-3xl" />
                            <div class="product-code">{{ product.productCode }}</div>
                        </div>
                    </template>
                    <a-card-meta :title="product.productName">
                        <template #description>
                            <div class="product-intro">{{ truncateText(product.introduction, 80) }}</div>
                        </template>
                    </a-card-meta>
                    <div class="card-footer mt-4">
                        <a-button type="primary" @click.stop="selectProduct(product)">
                            查看详情
                            <template #icon>
                                <Icon icon="material-symbols:arrow-right-alt" />
                            </template>
                        </a-button>
                    </div>
                </a-card>
            </div>

            <div class="pagination-container mt-6 flex justify-center">
                <a-pagination v-model:current="currentPage" :total="total" :pageSize="pageSize" show-size-changer
                    @change="handlePageChange" @showSizeChange="handleSizeChange" />
            </div>

            <div v-if="productList.length === 0 && !loading" class="empty-container">
                <a-empty description="暂无产品数据" />
            </div>
        </a-spin>
    </div>
</template>

<script setup>
import { ref, defineEmits, defineProps, watch } from 'vue';
import { Icon } from '@iconify/vue';

const props = defineProps({
    loading: {
        type: Boolean,
        default: false
    },
    products: {
        type: Array,
        default: () => []
    },
    total: {
        type: Number,
        default: 0
    }
});

const emit = defineEmits(['search', 'page-change', 'size-change', 'select-product']);

const searchKeyword = ref('');
const currentPage = ref(1);
const pageSize = ref(10);
const productList = ref([]);

// 监听产品数据变化
watch(() => props.products, (newValue) => {
    productList.value = newValue;
}, { immediate: true });

// 处理搜索
const handleSearch = () => {
    currentPage.value = 1;
    emit('search', searchKeyword.value);
};

// 处理页码变化
const handlePageChange = (page) => {
    currentPage.value = page;
    emit('page-change', page);
};

// 处理每页条数变化
const handleSizeChange = (current, size) => {
    pageSize.value = size;
    emit('size-change', size);
};

// 选择产品
const selectProduct = (product) => {
    emit('select-product', product);
};

// 截断文本
const truncateText = (text, length) => {
    if (!text) return '';
    return text.length > length ? text.substring(0, length) + '...' : text;
};
</script>

<style scoped>
.product-card {
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s;
}

.product-card:hover {
    transform: translateY(-5px);
}

.product-card-header {
    height: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    padding: 16px;
}

.product-code {
    font-size: 18px;
    font-weight: bold;
    margin-top: 8px;
}

.product-intro {
    height: 60px;
    overflow: hidden;
    color: #666;
}

.card-footer {
    display: flex;
    justify-content: flex-end;
}

.empty-container {
    padding: 40px 0;
}

/* 渐变背景 */
.bg-gradient-0 {
    background: linear-gradient(135deg, #1890ff, #096dd9);
}

.bg-gradient-1 {
    background: linear-gradient(135deg, #52c41a, #389e0d);
}

.bg-gradient-2 {
    background: linear-gradient(135deg, #722ed1, #531dab);
}

.bg-gradient-3 {
    background: linear-gradient(135deg, #faad14, #d48806);
}

.bg-gradient-4 {
    background: linear-gradient(135deg, #13c2c2, #08979c);
}
</style>