<template>
    <div class="calculation-section  mt-2">
        <a-card class="rounded-lg" :bordered="false">
            <template #title>
                <div class="flex items-center">
                    <Icon icon="material-symbols:calculate" class="mr-2 text-blue-500" />
                    <span class="text-lg font-medium">{{ t('premiumCalculator.calculation') }}</span>
                </div>
            </template>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- 左侧：计算表单 -->
                <div class="calculation-form bg-white rounded-lg">
                    <a-form :model="formState" :rules="rules" ref="formRef" layout="vertical">

                        <!-- 使用网格布局，每行两个表单项 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-2">
                            <!-- 产品信息卡片 - 当选中产品时显示 (全宽) -->
                            <div class="col-span-2 product-info mb-4 p-4 bg-blue-50 rounded" v-if="selectedProduct">
                                <div class="text-lg font-medium mb-2">{{ selectedProduct?.productName || '-' }}</div>
                                <div class="text-sm text-gray-500 mb-1">
                                    <span>{{ t('premiumCalculator.region') }}: </span>
                                    <a-tag color="blue">{{ selectedProduct?.region || '-' }}</a-tag>
                                </div>
                                <div class="text-sm text-gray-500">
                                    <span>{{ t('premiumCalculator.ageRange') }}: </span>
                                    <span>{{ selectedProduct?.minAge || '-' }} {{ t('premiumCalculator.to') }} {{
                                        selectedProduct?.maxAge || '-'
                                    }} {{ t('premiumCalculator.years') }}</span>
                                </div>
                            </div>

                            <!-- 产品类型选择 -->
                            <a-form-item :label="t('premiumCalculator.productType')" name="productType">
                                <a-select v-model:value="selectedTypeValue"
                                    :placeholder="t('premiumCalculator.selectProductType')" @change="handleTypeChange">
                                    <a-select-option value="">{{ t('premiumCalculator.allTypes') }}</a-select-option>
                                    <a-select-option v-for="type in productTypes" :key="type.value" :value="type.value"
                                        :disabled="type.code != 'SAVINGS' && type.code != 'CRITICAL_ILLNESS'">
                                        {{ type.label }}
                                    </a-select-option>
                                </a-select>
                            </a-form-item>

                            <!-- 地区-保司选择 -->
                            <a-form-item :label="tWithDefault('premiumCalculator.regionCompany')" name="regionCompany">
                                <a-cascader v-model:value="selectedRegionCompany" :options="cascaderOptions"
                                    :placeholder="tWithDefault('premiumCalculator.selectRegionCompany')"
                                    @change="handleRegionCompanyChange" :loading="loadingCompanies" :show-search="true"
                                    :filter-option="filterCascaderOption" />
                            </a-form-item>

                            <!-- 产品选择下拉框 (全宽) -->
                            <a-form-item :label="t('premiumCalculator.selectProduct')" name="productId"
                                class="col-span-2">
                                <a-select v-model:value="formState.productId"
                                    :placeholder="t('premiumCalculator.searchOrSelectProduct')"
                                    :loading="loadingProducts" show-search :filter-option="false"
                                    @search="handleProductSearch" @change="handleProductChange"
                                    option-label-prop="label">
                                    <a-select-option v-for="product in productsData" :key="product.id"
                                        :value="product.id" :label="product.productName">
                                        <div class="flex items-center py-1">
                                            <div class="flex items-center">
                                                <span class="font-medium">{{ product.productName }}</span>
                                                <a-tag color="blue" class="ml-2" v-if="product.type">{{ product.region
                                                }}</a-tag>

                                            </div>
                                        </div>
                                    </a-select-option>
                                </a-select>
                            </a-form-item>
                            <!-- 支付方式 -->
                            <a-form-item :label="t('premiumCalculator.paymentMode')" name="paymentMode">
                                <a-select v-model:value="formState.paymentMode">
                                    <a-select-option :value="1">{{ t('premiumCalculator.paymentModes.annual')
                                    }}</a-select-option>
                                    <a-select-option :value="2">{{ t('premiumCalculator.paymentModes.semiAnnual')
                                    }}</a-select-option>
                                    <a-select-option :value="3">{{ t('premiumCalculator.paymentModes.quarterly')
                                    }}</a-select-option>
                                    <a-select-option :value="4">{{ t('premiumCalculator.paymentModes.monthly')
                                    }}</a-select-option>
                                </a-select>
                            </a-form-item>
                            <!-- 年龄 -->
                            <a-form-item :label="t('premiumCalculator.age')" name="age">
                                <a-input-number v-model:value="formState.age" :min="selectedProduct?.minAge || 1"
                                    :max="selectedProduct?.maxAge || 100" style="width: 100%" />
                            </a-form-item>

                            <!-- 性别 -->
                            <a-form-item :label="t('premiumCalculator.sex')" name="sex">
                                <a-select v-model:value="formState.sex">
                                    <a-select-option :value="1">{{ t('premiumCalculator.sexOptions.male')
                                    }}</a-select-option>
                                    <a-select-option :value="2">{{ t('premiumCalculator.sexOptions.female')
                                    }}</a-select-option>
                                </a-select>
                            </a-form-item>
                            <!-- 是否吸烟 -->
                            <a-tooltip :title="t('policyorder.isSmoker')">
                                <a-form-item name="isSmoker" :label="t('policyorder.pleaseSelectIsSmoker')" required>
                                    <a-radio-group v-model:value="formState.isSmoker">
                                        <a-radio :value="1">是</a-radio>
                                        <a-radio :value="0">否</a-radio>
                                    </a-radio-group>
                                </a-form-item>
                            </a-tooltip>

                            <!-- 保险金额 -->
                            <a-tooltip :title="t('premiumCalculator.noInsuredFee')">
                                <a-form-item :label="t('premiumCalculator.insuredFee')" name="insuredFee">
                                    <a-input-number v-model:value="formState.insuredFee" :min="1"
                                        :formatter="formatAmount" :parser="parseAmount" :addon-before="currencySymbol"
                                        style="width: 100%" :disabled="selectedProduct?.insuredRelated === 0" />
                                </a-form-item>
                            </a-tooltip>

                            <!-- 选择货币 -->
                            <a-form-item label="货币" name="currency">
                                <a-select v-model:value="formState.currency">
                                    <a-select-option :value="1">港币</a-select-option>
                                    <a-select-option :value="2"
                                        :disabled="selectedProduct?.currency !== 2">人民币</a-select-option>
                                    <a-select-option :value="3"
                                        :disabled="selectedProduct?.currency !== 3">澳元</a-select-option>
                                    <a-select-option :value="4"
                                        :disabled="selectedProduct?.currency !== 4">新加坡元</a-select-option>
                                    <a-select-option :value="5"
                                        :disabled="selectedProduct?.currency !== 5">百慕大元</a-select-option>
                                </a-select>
                            </a-form-item>



                            <!-- 计算按钮 (全宽) -->
                            <a-form-item class="col-span-2">
                                <a-button type="primary" @click="onSubmit" :loading="loading" block>
                                    {{ t('premiumCalculator.calculate') }}
                                </a-button>
                            </a-form-item>
                        </div>
                    </a-form>
                </div>

                <!-- 右侧：计算结果 -->
                <div class="calculation-result bg-white rounded-lg">
                    <div v-if="loading" class="flex justify-center items-center h-full">
                        <a-spin />
                    </div>
                    <div v-else-if="error" class="flex flex-col justify-center items-center h-full p-6 text-center">
                        <Icon icon="material-symbols:error-outline" class="text-5xl text-red-500 mb-4" />
                        <p class="text-lg text-gray-700">{{ t('premiumCalculator.calculationError') }}</p>
                        <p class="text-sm text-gray-500 mt-2">{{ t('premiumCalculator.tryAgain') }}</p>
                    </div>
                    <div v-else-if="!result" class="flex flex-col justify-center items-center h-full p-6 text-center">
                        <Icon icon="material-symbols:info-outline" class="text-5xl text-blue-500 mb-4" />
                        <p class="text-lg text-gray-700">{{ t('premiumCalculator.noResult') }}</p>
                        <p class="text-sm text-gray-500 mt-2">{{ t('premiumCalculator.fillFormAndCalculate') }}</p>
                    </div>
                    <div v-else class="p-6">
                        <div class="mb-6">
                            <div class="flex justify-between items-center mb-4">
                                <span class="text-lg font-medium">{{ selectedProduct?.productName }}</span>
                                <a-tag color="blue">{{ selectedProduct?.region }}</a-tag>
                            </div>

                            <!-- 基本信息 -->
                            <div class="mb-6 pb-4 border-b border-gray-100">
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <div class="text-gray-500 text-sm">{{ t('premiumCalculator.age') }}</div>
                                        <div class="font-medium">{{ formState.age }} {{ t('premiumCalculator.years') }}
                                        </div>
                                    </div>
                                    <div>
                                        <div class="text-gray-500 text-sm">{{ t('premiumCalculator.sex') }}</div>
                                        <div class="font-medium">{{ getSexName(formState.sex) }}</div>
                                    </div>
                                    <div>
                                        <div class="text-gray-500 text-sm">{{ t('premiumCalculator.paymentMode') }}
                                        </div>
                                        <div class="font-medium">{{ getPaymentModeName(formState.paymentMode) }}</div>
                                    </div>
                                    <div>
                                        <div class="text-gray-500 text-sm">{{ t('premiumCalculator.insuredFee') }}</div>
                                        <div class="font-medium">{{ currencySymbol }} {{
                                            formatNumber(formState.insuredFee) }}</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 计算结果 -->
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <div class="text-gray-700 text-sm mb-1">{{ t('premiumCalculator.premium') }}</div>
                                <div class="flex items-center">
                                    <div class="text-3xl font-bold text-blue-600">
                                        {{ currencySymbol }} {{ formatNumber(result.toFixed(2)) }}
                                    </div>
                                    <div class="ml-auto">
                                        <a-tooltip :title="t('premiumCalculator.saveQuote')">
                                            <a-button type="primary" shape="circle">
                                                <template #icon>
                                                    <Icon icon="material-symbols:bookmark-outline" />
                                                </template>
                                            </a-button>
                                        </a-tooltip>
                                    </div>
                                </div>
                                <div class="text-xs text-red-500 mt-2">
                                    <span>{{ t('common.resultTips') }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </a-card>
    </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { Icon } from '@iconify/vue';
import { useProductStore } from '@/store/modules/product';
import { toTraditional } from '@/utils/chineseConverter';

const { t, locale } = useI18n();
// 为新添加的字符串设置默认翻译
const translationDefaults = {
    'premiumCalculator.regionCompany': '地区-保司',
    'premiumCalculator.selectRegionCompany': '请选择地区和保司'
};
// 使用t函数时，如果没有对应的翻译，则使用默认值
const tWithDefault = (key) => {
    const translation = t(key);
    // 如果翻译与key相同，说明没有找到翻译
    if (translation === key && translationDefaults[key]) {
        return translationDefaults[key];
    }
    return translation;
};

const formRef = ref(null);
const productStore = useProductStore();

const props = defineProps({
    selectedProduct: {
        type: Object,
        default: () => null
    },
    loading: {
        type: Boolean,
        default: false
    },
    result: {
        type: [Number, String],
        default: null
    },
    error: {
        type: Boolean,
        default: false
    },
    productsData: {
        type: Array,
        default: () => []
    },
    loadingProducts: {
        type: Boolean,
        default: false
    },
    productTypes: {
        type: Array,
        default: () => []
    },
    selectedTypeValue: {
        type: String,
        default: 'all'
    }
});

const emit = defineEmits(['calculate', 'product-search', 'product-select', 'type-change', 'company-change', 'region-change']);

// 内部状态
const selectedTypeValue = ref(props.selectedTypeValue || '');
const selectedCompany = ref('');
const selectedRegion = ref('');
const selectedRegionCompany = ref([]);
const companyList = ref([]);
const loadingCompanies = ref(false);

// 地区选项
const regionOptions = [
    { code: 'HK', name: '香港', value: '香港', label: '香港' },
    { code: 'MO', name: '澳门', value: '澳门', label: '澳门' },
    { code: 'SG', name: '新加坡', value: '新加坡', label: '新加坡' },
    // { code: 'CN', name: '中国大陆', value: '中国大陆', label: '中国大陆' },
    { code: 'BM', name: '百慕大', value: '百慕大', label: '百慕大' }
];

// 级联选择器选项
const cascaderOptions = computed(() => {
    // 构建级联选择器的数据结构
    return regionOptions.map(region => {
        // 筛选该地区的公司
        const regionCompanies = companyList.value
            .filter(company => company.region === region.value)
            .map(company => ({
                value: company.value,
                label: company.label
            }));

        // 如果没有公司数据，添加一个默认的"所有公司"选项
        if (regionCompanies.length === 0) {
            regionCompanies.push({
                value: '',
                label: t('premiumCalculator.allCompanies')
            });
        }

        return {
            value: region.value,
            label: region.label,
            children: regionCompanies
        };
    });
});

// 级联选择器过滤选项
const filterCascaderOption = (inputValue, path) => {
    return path.some(option => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0);
};

// 获取公司列表
const getCompanyList = async () => {
    loadingCompanies.value = true;
    try {
        const res = await productStore.getCompanyListOrigin();
        companyList.value = res.map(item => ({
            value: item.name,
            label: item.name,
            region: item.region || '香港' // 设置默认地区为香港
        }));
    } catch (error) {
        console.error('获取公司列表失败:', error);
    } finally {
        loadingCompanies.value = false;
    }
};

// 地区-保司级联选择变化处理
const handleRegionCompanyChange = (value) => {
    console.log('级联选择值变化:', value);

    // 解析级联选择器的值
    const region = value && value.length > 0 ? value[0] : '';
    const company = value && value.length > 1 ? value[1] : '';

    // 更新内部状态
    selectedRegion.value = region;
    selectedCompany.value = company;

    // 重置产品选择
    formState.productId = undefined;

    // 触发事件
    emit('region-change', region);
    emit('company-change', company);
};

// 产品类型变化处理
const handleTypeChange = (value) => {
    // 重置产品选择
    formState.productId = undefined;
    emit('type-change', value);
};

// 表单状态
const formState = reactive({
    productId: 1,
    age: 30,
    sex: 1,
    insuredFee: 10000,
    paymentMode: 1,
    isSmoker: 0,
    currency: 1
});

// 产品搜索处理
const handleProductSearch = (value) => {
    // 转换为繁体中文
    const traditionalValue = value ? toTraditional(value) : '';

    // 如果有选择公司，则在搜索关键词中加上公司名
    let searchValue = traditionalValue;
    if (selectedCompany.value) {
        // 如果搜索词已经包含公司名，则不需要再添加
        const companyName = selectedCompany.value;
        if (searchValue && !searchValue.includes(companyName)) {
            searchValue = `${companyName} ${searchValue}`;
        } else if (!searchValue) {
            searchValue = companyName;
        }
    }

    // 输出日志以便调试
    console.log('产品搜索值:', {
        original: value,
        traditional: traditionalValue,
        company: selectedCompany.value,
        final: searchValue,
        region: selectedRegion.value
    });

    emit('product-search', searchValue);
};

// 产品选择处理
const handleProductChange = (productId) => {
    const product = props.productsData.find(p => p.id === productId);
    if (product) {
        emit('product-select', product);
        // 设置年龄默认值为范围中间值
        if (product.minAge && product.maxAge) {
            formState.age = Math.floor((Number(product.minAge) + Number(product.maxAge)) / 2);
        }
    }
};

// 根据当前语言获取货币符号
const currencySymbol = computed(() => {
    const currencyMap = {
        1: 'HK$', // 港币
        2: '¥', // 人民币
        3: 'A$', // 澳元
        4: 'S$', // 新加坡元
        5: 'BD$', // 百慕大元
    };
    return currencyMap[formState.currency] || 'HK$';
});

// 格式化金额
const formatAmount = (value) => {
    if (!value) return '';

    // 添加千位分隔符
    const parts = value.toString().split('.');
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');

    return `${parts.join('.')}`;
};

// 解析金额
const parseAmount = (value) => {
    if (!value) return '';

    // 处理"w"或"W"表示的万元
    if (value.toString().toLowerCase().includes('w')) {
        // 提取数字部分
        const numPart = value.toString().toLowerCase().replace(/[^0-9.]/g, '');
        if (numPart) {
            // 将数字乘以10000（万）
            return parseFloat(numPart) * 10000;
        }
    }
    return value.replace(/[^\d.]/g, '');
}

// 表单验证规则
const rules = {
    productId: [
        { required: true, message: t('premiumCalculator.selectProductRequired') }
    ],
    age: [
        { required: true, message: t('premiumCalculator.ageRequired') },
        { type: 'number', min: computed(() => props.selectedProduct?.minAge || 0), message: t('premiumCalculator.ageMinError') },
        { type: 'number', max: computed(() => props.selectedProduct?.maxAge || 100), message: t('premiumCalculator.ageMaxError') }
    ],
    sex: [
        { required: true, message: t('premiumCalculator.sexRequired') }
    ],
    insuredFee: [
        { required: true, message: t('premiumCalculator.insuredFeeRequired') },
        { type: 'number', min: 1, message: t('premiumCalculator.insuredFeeMinError') }
    ],
    paymentMode: [
        { required: true, message: t('premiumCalculator.paymentModeRequired') }
    ]
};

// 获取性别名称
const getSexName = (sex) => {
    const sexMap = {
        1: t('premiumCalculator.sexOptions.male'),
        2: t('premiumCalculator.sexOptions.female')
    };
    return sexMap[sex] || '-';
};

// 获取缴费方式名称
const getPaymentModeName = (mode) => {
    const modeMap = {
        1: t('premiumCalculator.paymentModes.annual'),
        2: t('premiumCalculator.paymentModes.semiAnnual'),
        3: t('premiumCalculator.paymentModes.quarterly'),
        4: t('premiumCalculator.paymentModes.monthly')
    };
    return modeMap[mode] || '-';
};

// 格式化数字
const formatNumber = (value) => {
    if (value === null || value === undefined) return '-';
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

// 提交表单
const onSubmit = () => {
    formRef.value.validate().then(() => {
        const data = {
            productId: formState.productId,
            ...formState
        };
        emit('calculate', data);
    }).catch(error => {
        console.log('表单验证失败:', error);
    });
};

// 监听产品变化，更新表单数据
watch(() => props.selectedProduct, (newProduct) => {
    if (newProduct) {
        // 更新产品ID
        formState.productId = newProduct.id;
    }
}, { deep: true });

// 监听产品类型变化
watch(() => props.selectedTypeValue, (newValue) => {
    selectedTypeValue.value = newValue;
}, { immediate: true });

// 组件挂载时获取公司列表
onMounted(() => {
    getCompanyList();
});
</script>

<style scoped>
.calculation-section {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.calculation-form,
.calculation-result {
    min-height: 500px;
}

@media (max-width: 768px) {
    .calculation-result {
        margin-top: 2rem;
    }
}
</style>