<template>
    <div
        class="title-section mb-6 p-6 rounded-lg shadow-md text-white bg-gradient-to-r from-green-500 via-green-600 to-green-700">
        <div class="flex items-center">
            <Icon icon="material-symbols:calculate" class="text-4xl mr-3" />
            <h1 class="text-2xl font-bold page-title">{{ title }}</h1>
        </div>
        <p class="mt-2 page-description">
            {{ description }}
        </p>
    </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';

defineProps({
    title: {
        type: String,
        default: '保费试算'
    },
    description: {
        type: String,
        default: '快速计算产品保费，帮助您选择最合适的保险计划。输入您的基本信息即可获得精准保费预估。'
    }
});
</script>

<style scoped>
.title-section {
    background-size: 200% 200%;
    animation: gradientAnimation 5s ease infinite;
}

@keyframes gradientAnimation {
    0% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

.page-title {
    color: #fff;
}

.page-description {
    color: #e0e0e0;
}
</style>