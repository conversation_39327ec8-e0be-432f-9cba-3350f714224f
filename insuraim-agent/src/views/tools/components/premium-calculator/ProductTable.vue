<template>
    <div class="product-table  mt-2">
        <a-card class="rounded-lg" :bordered="false" :bodyStyle="{ padding: '0' }">
            <template #title>
                <div class="flex items-center">
                    <Icon icon="material-symbols:format-list-bulleted" class="mr-2 text-blue-500" />
                    <span class="text-lg font-medium">{{ t('premiumCalculator.productList') }}</span>
                </div>
            </template>
            <a-spin :spinning="loading">
                <a-table :dataSource="dataSource" :columns="columns" :pagination="paginationConfig"
                    @change="handleTableChange" :rowKey="record => record.id" :scroll="{ x: 'max-content' }"
                    :bordered="true" :loading="loading">
                    <template #headerCell="{ column }">
                        <template v-if="column.key === 'productName'">
                            <span class="flex items-center">
                                <Icon icon="material-symbols:description-outline" class="mr-1 text-blue-500" />
                                {{ column.title }}
                            </span>
                        </template>
                        <template v-else-if="column.key === 'type'">
                            <span class="flex items-center">
                                <Icon icon="material-symbols:category-outline" class="mr-1 text-green-500" />
                                {{ column.title }}
                            </span>
                        </template>
                        <template v-else-if="column.key === 'ageRange'">
                            <span class="flex items-center">
                                <Icon icon="material-symbols:calendar-month" class="mr-1 text-orange-500" />
                                {{ column.title }}
                            </span>
                        </template>
                        <template v-else-if="column.key === 'region'">
                            <span class="flex items-center">
                                <Icon icon="material-symbols:location-on-outline" class="mr-1 text-red-500" />
                                {{ column.title }}
                            </span>
                        </template>
                    </template>
                    <template #bodyCell="{ column, record }">
                        <template v-if="column.key === 'productName'">
                            <div class="font-medium text-blue-600">{{ record.productName }}</div>
                        </template>
                        <template v-else-if="column.key === 'type'">
                            <a-tag color="blue">{{ record.type }}</a-tag>
                        </template>
                        <template v-else-if="column.key === 'ageRange'">
                            <span>{{ record.minAge }} - {{ record.maxAge }} {{ t('premiumCalculator.years') }}</span>
                        </template>
                        <template v-else-if="column.key === 'action'">
                            <a-button type="primary" size="small" @click="calculatePremium(record)">
                                {{ t('premiumCalculator.calculate') }}
                            </a-button>
                        </template>
                    </template>
                </a-table>
            </a-spin>
        </a-card>
    </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { Icon } from '@iconify/vue';

const { t } = useI18n();

const props = defineProps({
    dataSource: {
        type: Array,
        default: () => []
    },
    loading: {
        type: Boolean,
        default: false
    },
    total: {
        type: Number,
        default: 0
    },
    pagination: {
        type: Object,
        default: () => ({
            current: 1,
            pageSize: 100,
            total: 0
        })
    }
});

const emit = defineEmits(['change', 'calculate', 'update:pagination']);

// 配置分页
const paginationConfig = computed(() => ({
    ...props.pagination,
    showSizeChanger: true,
    pageSizeOptions: ['10', '20', '50', '100'],
    showTotal: (total) => `${t('premiumCalculator.total')}: ${total} ${t('premiumCalculator.items')}`,
}));

// 表格列定义
const columns = [
    {
        title: t('premiumCalculator.productName'),
        dataIndex: 'productName',
        key: 'productName',
        fixed: 'left',
        width: 220,
    },
    {
        title: t('premiumCalculator.productType'),
        dataIndex: 'type',
        key: 'type',
        width: 120,
    },
    {
        title: t('premiumCalculator.ageRange'),
        dataIndex: 'ageRange',
        key: 'ageRange',
        width: 120,
    },
    {
        title: t('premiumCalculator.region'),
        dataIndex: 'region',
        key: 'region',
        width: 100,
    },
    {
        title: t('premiumCalculator.action'),
        key: 'action',
        fixed: 'right',
        width: 100,
    },
];

// 处理表格变化
const handleTableChange = (pagination, filters, sorter) => {
    emit('update:pagination', pagination);
    emit('change', { pagination, filters, sorter });
};

// 计算保费
const calculatePremium = (record) => {
    emit('calculate', record);
};

// 监听分页变化
watch(() => props.pagination, (newVal) => {
    if (JSON.stringify(paginationConfig.value) !== JSON.stringify(newVal)) {
        emit('update:pagination', newVal);
    }
}, { deep: true });
</script>

<style scoped>
.product-table :deep(.ant-table-thead > tr > th) {
    background-color: #f5f7fa;
    font-weight: 600;
}

.product-table :deep(.ant-table-row:hover) {
    cursor: pointer;
}

.product-table :deep(.ant-pagination) {
    margin: 16px;
}
</style>