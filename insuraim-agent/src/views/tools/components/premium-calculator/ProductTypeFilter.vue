<template>
    <div class="product-type-filter">
        <div class="flex flex-wrap gap-2">
            <a-button v-for="type in productTypes" :key="type.value"
                :type="selectedType === type.value ? 'primary' : 'default'" @click="selectType(type.value)" class="mb-2"
                size="middle">
                {{ type.label }}
            </a-button>
        </div>
    </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n';
import { watch } from 'vue';

const { t } = useI18n();

const props = defineProps({
    productTypes: {
        type: Array,
        default: () => []
    },
    selectedType: {
        type: String,
        default: 'all'
    }
});

const emit = defineEmits(['update:selectedType', 'change']);

const selectType = (type) => {
    emit('update:selectedType', type);
    emit('change', type);
};

// 监听selectedType的变化
watch(() => props.selectedType, (newType) => {
    if (newType !== 'all' && !props.productTypes.includes(newType)) {
        selectType('all');
    }
});
</script>

<style scoped>
.product-type-filter {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}

@media (max-width: 640px) {
    .product-type-filter {
        margin-top: 0.5rem;
        width: 100%;
    }
}
</style>