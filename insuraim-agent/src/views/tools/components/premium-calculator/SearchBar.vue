<template>
    <div class="search-section bg-white p-6 rounded-lg shadow-md mt-2">
        <div class="flex flex-wrap items-center justify-between gap-4">
            <div class="flex-1 min-w-[280px] flex items-center gap-4">
                <div class="flex items-center">
                    <a-input-search v-model:value="searchValue" :placeholder="t('premiumCalculator.searchPlaceholder')"
                        enter-button @search="handleSearch" class="flex-1 max-w-[400px]" />
                </div>
                <a-button type="primary" @click="handleReset">
                    <template #icon>
                        <Icon icon="material-symbols:refresh" />
                    </template>
                    {{ t('premiumCalculator.reset') }}
                </a-button>
            </div>
            <div class="flex flex-wrap gap-4">
                <product-type-filter :product-types="productTypes" v-model:selected-type="selectedType"
                    @change="handleTypeChange" />
            </div>
            <div class="flex flex-wrap items-center justify-start gap-4">
                <div class="stat-card bg-blue-50 px-4 py-2 rounded-lg border-l-4 border-blue-500 flex items-center">
                    <div>
                        <span class="text-gray-700 text-sm font-medium">{{ t('premiumCalculator.productCount')
                            }}：</span>
                        <span class="text-lg font-bold text-blue-600">{{ productCount }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { Icon } from '@iconify/vue';
import { useI18n } from 'vue-i18n';
import ProductTypeFilter from './ProductTypeFilter.vue';

const { t } = useI18n();

const props = defineProps({
    initialValue: {
        type: String,
        default: ''
    },
    productCount: {
        type: Number,
        default: 0
    },
    productTypes: {
        type: Array,
        default: () => []
    },
    selectedTypeValue: {
        type: String,
        default: 'all'
    }
});

const emit = defineEmits(['search', 'reset', 'type-change']);

const searchValue = ref(props.initialValue);
const selectedType = ref(props.selectedTypeValue);

// 监听初始值变化
watch(() => props.initialValue, (newVal) => {
    searchValue.value = newVal;
});

// 监听产品类型值变化
watch(() => props.selectedTypeValue, (newVal) => {
    selectedType.value = newVal;
});

const handleSearch = (value) => {
    emit('search', value);
};

const handleReset = () => {
    searchValue.value = '';
    selectedType.value = 'all';
    emit('reset');
};

const handleTypeChange = (type) => {
    emit('type-change', type);
};
</script>

<style scoped>
.stat-card {
    transition: all 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}
</style>