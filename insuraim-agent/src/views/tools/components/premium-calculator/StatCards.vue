<template>
    <a-card class="mb-6 rounded-lg mt-2 mb-2" :bordered="false">
        <h1 class="text-2xl font-bold mb-4">{{ t('premiumCalculator.popularProducts') }}</h1>
        <!-- 产品统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-blue-500">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm">{{ t('premiumCalculator.productCount') }}</p>
                        <p class="text-2xl font-bold">{{ productCount }}</p>
                    </div>
                    <div class="bg-blue-100 p-2 rounded-full">
                        <Icon icon="material-symbols:inventory-2" class="text-2xl text-blue-500" />
                    </div>
                </div>
                <div class="mt-2 text-xs text-gray-500">
                    <span class="flex items-center">
                        <Icon icon="material-symbols:info-outline" class="mr-1" />
                        {{ t('premiumCalculator.productCountDesc') }}
                    </span>
                </div>
            </div>

            <!-- 平均保费卡片 -->
            <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-green-500">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm">{{ t('premiumCalculator.avgPremium') }}</p>
                        <p class="text-2xl font-bold">{{ currencySymbol }} {{ formatNumber(avgPremium) }}</p>
                    </div>
                    <div class="bg-green-100 p-2 rounded-full">
                        <Icon icon="material-symbols:payments-outline" class="text-2xl text-green-500" />
                    </div>
                </div>
                <div class="mt-2 text-xs text-gray-500">
                    <span class="flex items-center">
                        <Icon icon="material-symbols:info-outline" class="mr-1" />
                        {{ t('premiumCalculator.avgPremiumDesc') }}
                    </span>
                </div>
            </div>

            <!-- 最高保费卡片 -->
            <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-orange-500">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm">{{ t('premiumCalculator.maxPremium') }}</p>
                        <p class="text-2xl font-bold">{{ currencySymbol }} {{ formatNumber(maxPremium) }}</p>
                    </div>
                    <div class="bg-orange-100 p-2 rounded-full">
                        <Icon icon="material-symbols:trending-up" class="text-2xl text-orange-500" />
                    </div>
                </div>
                <div class="mt-2 text-xs text-gray-500">
                    <span class="flex items-center">
                        <Icon icon="material-symbols:info-outline" class="mr-1" />
                        {{ t('premiumCalculator.maxPremiumDesc') }}
                    </span>
                </div>
            </div>

            <!-- 最低保费卡片 -->
            <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-purple-500">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm">{{ t('premiumCalculator.minPremium') }}</p>
                        <p class="text-2xl font-bold">{{ currencySymbol }} {{ formatNumber(minPremium) }}</p>
                    </div>
                    <div class="bg-purple-100 p-2 rounded-full">
                        <Icon icon="material-symbols:trending-down" class="text-2xl text-purple-500" />
                    </div>
                </div>
                <div class="mt-2 text-xs text-gray-500">
                    <span class="flex items-center">
                        <Icon icon="material-symbols:info-outline" class="mr-1" />
                        {{ t('premiumCalculator.minPremiumDesc') }}
                    </span>
                </div>
            </div>
        </div>
    </a-card>
</template>

<script setup>
import { Icon } from '@iconify/vue';
import { useI18n } from 'vue-i18n';
import { computed } from 'vue';

const { t, locale } = useI18n();

const props = defineProps({
    productCount: {
        type: Number,
        default: 0
    },
    avgPremium: {
        type: [Number, String],
        default: 0
    },
    maxPremium: {
        type: [Number, String],
        default: 0
    },
    minPremium: {
        type: [Number, String],
        default: 0
    }
});

// 根据当前语言获取货币符号
const currencySymbol = computed(() => {
    const localeMap = {
        'zh-CN': '¥',
        'en-US': '$',
        'zh-HK': 'HK$'
    };
    return localeMap[locale.value] || '¥';
});

// 格式化数字
const formatNumber = (value) => {
    if (!value || isNaN(Number(value))) return '0';
    return Number(value).toLocaleString();
};
</script>

<style scoped>
.stat-card {
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}
</style>