<template>
  <div class="calculation-form">
    <h3 class="text-base font-medium mb-4">{{ t('premiumCalculator.calculationForm') }}</h3>

    <a-form :model="formState" :rules="rules" ref="formRef" layout="vertical">
      <div class="product-info mb-4 p-4 bg-tertiary rounded">
        <div class="text-lg font-medium mb-2">{{ selectedProduct?.productName || '-' }}</div>
        <div class="text-sm text-gray-500 mb-1">
          <span>{{ t('premiumCalculator.productType') }}: </span>
          <span>{{ selectedProduct?.type || '-' }}</span>
        </div>
        <div class="text-sm text-gray-500">
          <span>{{ t('premiumCalculator.ageRange') }}: </span>
          <span>{{ selectedProduct?.minAge || '-' }} {{ t('premiumCalculator.to') }} {{ selectedProduct?.maxAge || '-'
            }} {{ t('premiumCalculator.years') }}</span>
        </div>
      </div>

      <a-form-item :label="t('premiumCalculator.age')" name="age">
        <a-input-number v-model:value="formState.age" :min="selectedProduct?.minAge || 1"
          :max="selectedProduct?.maxAge || 100" style="width: 100%" />
      </a-form-item>
      <a-form-item :label="t('premiumCalculator.sex')" name="sex">
        <a-select v-model:value="formState.sex">
          <a-select-option :value="1">{{ t('premiumCalculator.sexOptions.male') }}</a-select-option>
          <a-select-option :value="2">{{ t('premiumCalculator.sexOptions.female') }}</a-select-option>
        </a-select>

      </a-form-item>
      <a-tooltip :title="t('premiumCalculator.noInsuredFee')">
        <a-form-item :label="t('premiumCalculator.insuredFee')" name="insuredFee">
          <a-input-number v-model:value="formState.insuredFee" :min="1"
            :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
            :parser="value => value.replace(/\$\s?|(,*)/g, '')" :addon-before="currencySymbol" style="width: 100%"
            :disabled="selectedProduct.insuredRelated === 0" />
        </a-form-item>

      </a-tooltip>
      <a-tooltip :title="t('policyorder.isSmoker')">
        <a-form-item name="isSmoker" :label="t('policyorder.pleaseSelectIsSmoker')" required>
          <a-radio-group v-model:value="formState.isSmoker">
            <a-radio :value="1">是</a-radio>
            <a-radio :value="0">否</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-tooltip>
      <a-form-item :label="t('premiumCalculator.paymentMode')" name="paymentMode">
        <a-select v-model:value="formState.paymentMode">
          <a-select-option :value="1">{{ t('premiumCalculator.paymentModes.annual') }}</a-select-option>
          <a-select-option :value="2">{{ t('premiumCalculator.paymentModes.semiAnnual') }}</a-select-option>
          <a-select-option :value="3">{{ t('premiumCalculator.paymentModes.quarterly') }}</a-select-option>
          <a-select-option :value="4">{{ t('premiumCalculator.paymentModes.monthly') }}</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item>
        <a-button type="primary" @click="onSubmit" :loading="loading" block>
          {{ t('premiumCalculator.calculate') }}
        </a-button>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';

const { t, locale } = useI18n();

const props = defineProps({
  selectedProduct: {
    type: Object,
    default: () => null
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['calculate']);

const formRef = ref(null);

// 表单状态
const formState = reactive({
  age: 30,
  insuredFee: 100000,
  paymentMode: 1,
  sex: 1,
  isSmoker: 0,
});

// 根据当前语言获取货币符号
const currencySymbol = computed(() => {
  const localeMap = {
    'zh-CN': '¥',
    'en-US': '$',
    'zh-HK': 'HK$'
  };
  return localeMap[locale.value] || '¥';
});

// 表单验证规则
const rules = computed(() => {
  return {
    age: [
      { required: true, message: t('premiumCalculator.formValidation.ageRequired') },
      {
        validator: (_, value) => {
          if (props.selectedProduct) {
            const { minAge, maxAge } = props.selectedProduct;
            if (value < minAge || value > maxAge) {
              return Promise.reject(t('premiumCalculator.formValidation.ageRange', { min: minAge, max: maxAge }));
            }
          }
          return Promise.resolve();
        }
      }
    ],
    insuredFee: props.selectedProduct.insuredRelated === 0 ? [] : [
      { required: true, message: t('premiumCalculator.formValidation.insuredFeeRequired') },
      { type: 'number', min: 1, message: t('premiumCalculator.formValidation.insuredFeePositive') }
    ],
    // insuredFee: [
    //   { required: true, message: t('premiumCalculator.formValidation.insuredFeeRequired') },
    //   { type: 'number', min: 1, message: t('premiumCalculator.formValidation.insuredFeePositive') }
    // ],
    paymentMode: [
      { required: true }
    ]
  };
});

// 监听选中产品变化，重置表单
watch(() => props.selectedProduct, (newProduct) => {
  if (newProduct) {
    // 重置年龄为默认值或者范围内的值
    if (newProduct.minAge && newProduct.maxAge) {
      const defaultAge = 30;
      formState.age = defaultAge >= newProduct.minAge && defaultAge <= newProduct.maxAge
        ? defaultAge
        : Math.min(Math.max(newProduct.minAge, defaultAge), newProduct.maxAge);
    }
  }
}, { immediate: true });

// 提交表单
const onSubmit = () => {
  formRef.value.validate().then(() => {
    // 表单验证通过
    if (props.selectedProduct) {
      const calculationData = {
        id: props.selectedProduct.id,
        ...formState
      };
      emit('calculate', calculationData);
    }
  }).catch(error => {
    console.error('Validation failed:', error);
  });
};
</script>

<style scoped>
.calculation-form {
  background-color: var(--bg-secondary);
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
</style>