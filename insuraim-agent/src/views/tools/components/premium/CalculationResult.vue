<template>
  <div class="calculation-result">
    <h3 class="text-base font-medium mb-4">{{ t('premiumCalculator.calculationResult') }}</h3>

    <!-- 未选择产品时的提示 -->
    <div v-if="!selectedProduct" class="no-product-selected p-4 bg-yellow-50 text-yellow-700 rounded mb-4">
      <div class="flex items-start">
        <div class="mr-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd"
              d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
              clip-rule="evenodd" />
          </svg>
        </div>
        <div>
          <p class="font-medium">{{ t('premiumCalculator.noProductSelected') }}</p>
          <p class="text-sm mt-1">{{ t('premiumCalculator.selectProductFirst') }}</p>
        </div>
      </div>
    </div>

    <div v-if="loading" class="flex justify-center py-8">
      <a-spin />
      <span class="ml-2">{{ t('premiumCalculator.loading') }}</span>
    </div>

    <div v-else-if="error" class="error-message p-4 bg-tertiary text-red-500 rounded">
      {{ t('premiumCalculator.error') }}
    </div>

    <div v-else-if="!result" class="no-result p-4 bg-tertiary text-gray-500 rounded text-center">
      {{ t('premiumCalculator.noResult') }}
    </div>

    <div v-else class="result-content">
      <a-card>
        <template #title> 
          <div class="flex justify-between items-center">
            <span>{{ selectedProduct?.productName }}</span>
            <a-tag color="blue">{{ selectedProduct?.type }}</a-tag>
          </div>
        </template>

        <div class="result-details">
          <!-- 基本信息 -->
          <div class="mb-4 pb-4 border-b border-gray-100">
            <div class="grid grid-cols-2 gap-4">
              <div>
                <div class="text-gray-500 text-sm">{{ t('premiumCalculator.age') }}</div>
                <div class="font-medium">{{ formData.age }} {{ t('premiumCalculator.years') }}</div>
              </div>
              <div>
                <div class="text-gray-500 text-sm">{{ t('premiumCalculator.paymentMode') }}</div>
                <div class="font-medium">{{ getPaymentModeName(formData.paymentMode) }}</div>
              </div>
              <div>
                <div class="text-gray-500 text-sm">{{ t('premiumCalculator.insuredFee') }}</div>
                <div class="font-medium">{{ currencySymbol }} {{ formatNumber(formData.insuredFee) }}</div>
              </div>
            </div>
          </div>

          <!-- 计算结果 -->
          <div>
            <div class="text-gray-500 text-sm mb-1">{{ t('premiumCalculator.premium') }}</div>
            <div class="text-2xl font-bold text-blue-600">
              {{ currencySymbol }} {{ result }}
            </div>

            <div class="text-xs font-bold text-red-500 mt-2">
              <span> {{ t('common.resultTips') }}</span>
            </div>
          </div>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

const { t, locale } = useI18n();

const props = defineProps({
  selectedProduct: {
    type: Object,
    default: () => null
  },
  formData: {
    type: Object,
    default: () => ({})
  },
  result: {
    type: [Number, String],
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  },
  error: {
    type: Boolean,
    default: false
  }
});

// 根据当前语言获取货币符号
const currencySymbol = computed(() => {
  const localeMap = {
    'zh-CN': '¥',
    'en-US': '$',
    'zh-HK': 'HK$'
  };
  return localeMap[locale.value] || '¥';
});

// 格式化数字
const formatNumber = (value) => {
  if (value === null || value === undefined) return '-';
  return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

// 获取缴费方式名称
const getPaymentModeName = (mode) => {
  const modeMap = {
    1: t('premiumCalculator.paymentModes.annual'),
    2: t('premiumCalculator.paymentModes.semiAnnual'),
    3: t('premiumCalculator.paymentModes.quarterly'),
    4: t('premiumCalculator.paymentModes.monthly')
  };
  return modeMap[mode] || '-';
};
</script>

<style scoped>
.calculation-result {
  background-color: var(--bg-secondary);
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.result-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>