<template>
  <div class="product-list">
    <h3 class="text-base font-medium mb-4">{{ t('premiumCalculator.productList') }}</h3>
    
    <div v-if="loading" class="flex justify-center py-8">
      <a-spin />
    </div>
    
    <div v-else-if="products.length === 0" class="text-center py-8 text-gray-500">
      {{ t('premiumCalculator.noProducts') }}
    </div>
    
    <div v-else class="space-y-4">
      <a-card v-for="product in products" :key="product.id" class="product-card" hoverable>
        <template #title>
          <div class="flex  items-center gap-3">
            <span>{{ product.productName }}</span>
            <a-tag color="blue">{{ product.type }}</a-tag>
  
          </div>
        </template>
        <template #extra>
          <a-button type="primary" @click="onCalculate(product)">
            {{ t('premiumCalculator.calculate') }}
          </a-button>
        </template>
        <div class="product-info">
          <div class="mb-2">
            <span class="text-gray-600 mr-2">{{ t('premiumCalculator.ageRange') }}:</span>
            <span>{{ product.minAge }} {{ t('premiumCalculator.to') }} {{ product.maxAge }} {{ t('premiumCalculator.years') }}</span>
            <span> | </span>
            <a-text>{{ product.region ? product.region : '暂无地区' }}</a-text>
          </div>
        </div>
      </a-card>
    </div>
    
    <div class="pagination-container mt-4 flex justify-center">
      <a-pagination
        v-if="products.length > 0"
        :current="currentPage"
        @update:current="onPageChange"
        :total="total"
        :pageSize="pageSize"
        show-size-changer
        :pageSizeOptions="['10', '20', '50']"
        :showTotal="(total) => `${total} 项`"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const props = defineProps({
  products: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  total: {
    type: Number,
    default: 0
  },
  currentPage: {
    type: Number,
    default: 1
  },
  pageSize: {
    type: Number,
    default: 10
  }
});

const emit = defineEmits(['calculate', 'page-change', 'update:currentPage']);

const onCalculate = (product) => {
  emit('calculate', product);
};

const onPageChange = (page) => {
  emit('update:currentPage', page);
  emit('page-change', page);
};
</script>

<style scoped>
.product-card {
  margin-bottom: 16px;
  transition: all 0.3s;
}

.product-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.product-info {
  font-size: 14px;
}

.pagination-container {
  margin-top: 20px;
}
</style> 