<template>
  <div class="product-search-bar mb-6">
    <a-input-search
      v-model:value="searchValue"
      :placeholder="t('premiumCalculator.searchPlaceholder')"
      enter-button
      @search="onSearch"
      @change="onChange"
      allow-clear
    />
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:modelValue', 'search']);

const searchValue = ref(props.modelValue);

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  searchValue.value = newValue;
});

// 监听内部值变化
watch(searchValue, (newValue) => {
  emit('update:modelValue', newValue);
});

const onSearch = () => {
  emit('search', searchValue.value);
};

const onChange = () => {
  emit('update:modelValue', searchValue.value);
};
</script>

<style scoped>
.product-search-bar {
  max-width: 500px;
}
</style> 