<template>
  <div class="product-type-filter mb-6">
    <h3 class="text-base font-medium mb-3">{{ t('premiumCalculator.productType') }}</h3>
    <div class="flex flex-wrap gap-2">
      <a-button v-for="type in ['all', ...productTypes]" :key="type.value"
        :type="selectedType === type ? 'primary' : 'default'" @click="selectType(type)" class="mb-2">
        {{ type === 'all' ? t('premiumCalculator.allTypes') : type }}
      </a-button>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const props = defineProps({
  productTypes: {
    type: Array,
    default: () => []
  },
  selectedType: {
    type: String,
    default: 'all'
  }
});

const emit = defineEmits(['update:selectedType']);

const selectType = (type) => {
  emit('update:selectedType', type);
};
</script>

<style scoped>
.product-type-filter {
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 1rem;
}
</style>