<template>
    <div class="container mx-auto py-6 px-4">
        <!-- 页面标题 -->
        <page-header />

        <!-- 数据概览卡片 -->
        <stat-cards :product-count="productList.length" :rate2022="averageRate2022" :rate2023="averageRate2023"
            :growth-rate="yearOverYearGrowth" />
        <!-- 搜索区域 -->
        <search-bar :initial-value="searchParams.search" :product-count="pagination.total" :product-types="productTypes"
            @search="handleSearch" @reset="resetSearch" />

        <!-- 数据表格 -->
        <data-table :data-source="tableData" :loading="loading" :total="pagination.total"
            v-model:pagination="pagination" @change="handleTableChange" />

        <!-- 数据可视化 -->
        <chart-section :data="tableData" :loading="loading" />
    </div>
</template>

<script setup>
import { productAPI } from '../../api';
import { processTableData, calculateGrowthRate } from './components/dividend/utils/helpers';
import { toTraditional, isTraditional } from '../../utils/chineseConverter';

// 导入组件
import { PageHeader, SearchBar, StatCards, DataTable, ChartSection } from './components/dividend';

// 数据状态
const productList = ref([]);
const loading = ref(false);
const productTypes = ref([]);
const searchParams = reactive({
    pageNum: 1,
    pageSize: 10,
    search: '',
    currency: '',
    mainType: null,
    // region: '',
    // company: ''
});

// 处理表格数据
const tableData = computed(() => {
    return processTableData(productList.value);
});

// 计算2022年平均实现率
const averageRate2022 = computed(() => {
    if (tableData.value.length === 0) return 0;

    const sum = tableData.value.reduce((acc, item) => {
        return acc + Number(item.avgRate2022 || 0);
    }, 0);

    return (sum / tableData.value.length).toFixed(1);
});

// 计算2023年平均实现率
const averageRate2023 = computed(() => {
    if (tableData.value.length === 0) return 0;

    const sum = tableData.value.reduce((acc, item) => {
        return acc + Number(item.avgRate2023 || 0);
    }, 0);

    return (sum / tableData.value.length).toFixed(1);
});


// 计算同比增长率
const yearOverYearGrowth = computed(() => {
    if (Number(averageRate2022.value) === 0) return 0;
    const growth = ((Number(averageRate2023.value) - Number(averageRate2022.value)) / Number(averageRate2022.value)) * 100;
    return growth.toFixed(1);
});

// 分页配置
const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    pageSizeOptions: ['10', '20', '50', '100'],
    showTotal: (total) => `共 ${total} 条记录`,
});

// 获取产品类型列表
const getProductTypes = async () => {
    try {
        const res = await productAPI.getChinaLifeProductCalculationType();
        if (res && res !== undefined) {
            productTypes.value = res.map(item => {
                return {
                    value: item.code,
                    label: item.name,
                    code: item.code
                }
            });
        }
        return res;
    } catch (error) {
        console.error('获取产品类型失败:', error);
        return null;
    }
};

// 获取数据
const fetchData = async () => {
    loading.value = true;
    try {
        // 在发送请求前将搜索关键字转换为繁体中文
        const requestParams = { ...searchParams };
        if (requestParams.search) {
            // 只有当搜索关键字不包含繁体字时，才需要转换
            if (!isTraditional(requestParams.search)) {
                requestParams.search = toTraditional(requestParams.search);
                console.log('搜索关键字已转换为繁体:', requestParams.search);
            } else {
                console.log('搜索关键字已包含繁体，无需转换');
            }
        }

        // 如果有公司名，也需要转换为繁体
        if (requestParams.company && !isTraditional(requestParams.company)) {
            requestParams.company = toTraditional(requestParams.company);
        }

        console.log('发起请求前的参数:', {
            pageNum: requestParams.pageNum,
            pageSize: requestParams.pageSize,
            search: requestParams.search,
            region: requestParams.region,
            company: requestParams.company,
            mainType: requestParams.mainType,
            pagination: pagination.current
        });

        const res = await productAPI.getChinaLifeProductRealizationDetails(requestParams);
        productList.value = res.records;
        pagination.total = res.total;

        // 添加日志输出，查看后端返回的数据
        console.log('后端返回数据:', {
            records: res.records.length,
            total: res.total,
            current: res.current,
            pageSize: res.size,
            pages: res.pages
        });

        // 确保pagination.current与后端返回的当前页一致
        if (res.current) {
            pagination.current = res.current;
        }
    } catch (error) {
        console.error('获取分红实现率数据失败:', error);
    } finally {
        loading.value = false;
    }
};

// 处理搜索
const handleSearch = (value) => {
    if (typeof value === 'object') {
        // 新版本的搜索参数
        searchParams.search = value.searchText;
        searchParams.region = value.region;
        searchParams.company = value.company;
        searchParams.mainType = value.mainType; // 添加险种筛选
    } else {
        // 兼容旧版本的搜索参数（纯文本）
        searchParams.search = value;
    }

    pagination.current = 1; // 重置到第一页
    fetchData();
};

// 重置搜索
const resetSearch = () => {
    searchParams.search = '';
    searchParams.currency = '';
    searchParams.region = '';
    searchParams.company = '';
    searchParams.mainType = null; // 重置险种筛选
    pagination.current = 1; // 重置到第一页
    fetchData();
};

// 处理表格变化
const handleTableChange = ({ pagination, filters, sorter }) => {
    // 重新添加对pagination参数的处理，确保使用最新的页码
    if (pagination) {
        console.log('分页变化:', {
            当前页: pagination.current,
            每页条数: pagination.pageSize,
            总条数: pagination.total
        });

        // 更新searchParams中的分页参数
        searchParams.pageNum = pagination.current;
        searchParams.pageSize = pagination.pageSize;
    }

    // 处理排序
    if (sorter && sorter.field) {
        searchParams.sortField = sorter.field;
        searchParams.sortOrder = sorter.order === 'ascend' ? 'asc' : 'desc';
    } else {
        searchParams.sortField = undefined;
        searchParams.sortOrder = undefined;
    }

    // 在fetchData之前添加日志，查看请求参数
    console.log('请求参数:', { ...searchParams });

    fetchData();
};

// 组件挂载时获取数据
onMounted(() => {
    fetchData();
    getProductTypes(); // 获取产品类型列表
    productAPI.getChinaLifeProductRealizationName({
        pageNum: 1,
        pageSize: 10,

    });
});
</script>

<style scoped>
@media (max-width: 640px) {
    .container {
        padding: 0.5rem;
    }
}
</style>