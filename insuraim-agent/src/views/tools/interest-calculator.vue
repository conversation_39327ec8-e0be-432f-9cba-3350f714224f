<template>



    <div class="container  py-6 px-4">
        <!-- 页面标题 -->

        <div class="title-section mb-6 p-6 rounded-lg shadow-md text-white bg-gradient-to-r from-green-500 via-green-600 to-green-700"
            id="no-pdf">
            <div class="flex items-center">
                <Icon icon="material-symbols:calculate" class="text-4xl mr-3" />
                <h1 class="text-2xl font-bold page-title">收益演算</h1>
            </div>
            <p class="mt-2 page-description">
                计算保险产品的单利和复利收益，帮助您评估产品的投资回报情况。
            </p>
        </div>

        <!-- 搜索和计算方式选择区域 -->
        <div class="search-section bg-white p-6 rounded-lg shadow-md mb-6">
            <div class="flex flex-wrap items-center justify-between gap-4">
                <div class="flex-1 min-w-[280px] flex items-center gap-4">
                    <a-radio-group v-model:value="calculationType" button-style="solid"
                        @change="handleCalculationTypeChange">
                        <a-radio-button value="precise">
                            <div class="flex items-center">
                                <Icon icon="material-symbols:precision-manufacturing" class="mr-1" />
                                <span>收益演算</span>
                            </div>
                        </a-radio-button>
                        <a-radio-button value="general">
                            <div class="flex items-center">
                                <Icon icon="material-symbols:calculate-outline" class="mr-1" />
                                <span>单复利计算</span>
                            </div>
                        </a-radio-button>
                    </a-radio-group>
                </div>
                <!-- <div class="flex flex-wrap gap-4" v-if="calculationType === 'precise'">
                    <a-input-search v-model:value="searchValue" placeholder="搜索产品名称" enter-button
                        @search="onProductSearch" class="flex-1 max-w-[400px]" />
                </div> -->
                <div class="flex flex-wrap items-center justify-start gap-4" v-if="calculationType === 'precise'">
                    <div class="stat-card bg-blue-50 px-4 py-2 rounded-lg border-l-4 border-blue-500 flex items-center">
                        <div>
                            <span class="text-gray-700 text-sm font-medium">产品总数：</span>
                            <span class="text-lg font-bold text-blue-600">{{ productList.length }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6 "
            v-if="(calculationResult || apiCalculationResult) && calculationType === 'general'">
            <!-- 单利收益率卡片 -->
            <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-blue-500">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm">单利收益率</p>
                        <p class="text-2xl font-bold text-blue-600">
                            {{ calculationResult?.simpleInterest || '0' }}%
                        </p>
                    </div>
                    <div class="bg-blue-100 p-2 rounded-full">
                        <Icon icon="material-symbols:trending-up" class="text-2xl text-blue-500" />
                    </div>
                </div>
                <div class="mt-2 text-xs text-gray-500">
                    <span class="flex items-center">
                        <Icon icon="material-symbols:info-outline" class="mr-1" />
                        单利计算的年化收益率
                    </span>
                </div>
            </div>

            <!-- 复利收益率卡片 -->
            <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-green-500">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm">复利收益率</p>
                        <p class="text-2xl font-bold text-green-600">
                            {{ calculationResult?.compoundInterest || '0' }}%
                        </p>
                    </div>
                    <div class="bg-green-100 p-2 rounded-full">
                        <Icon icon="material-symbols:finance" class="text-2xl text-green-500" />
                    </div>
                </div>
                <div class="mt-2 text-xs text-gray-500">
                    <span class="flex items-center">
                        <Icon icon="material-symbols:info-outline" class="mr-1" />
                        复利计算的内部收益率
                    </span>
                </div>
            </div>

            <!-- 总投入卡片 -->
            <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-orange-500">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm">总投入</p>
                        <p class="text-2xl font-bold text-orange-600">
                            {{ formData.value.annualPremium && formData.value.paymentYears ?
                                (formData.value.annualPremium * formData.value.paymentYears).toLocaleString() :
                                '0'
                            }}元
                        </p>
                    </div>
                    <div class="bg-orange-100 p-2 rounded-full">
                        <Icon icon="material-symbols:payments" class="text-2xl text-orange-500" />
                    </div>
                </div>
                <div class="mt-2 text-xs text-gray-500">
                    <span class="flex items-center">
                        <Icon icon="material-symbols:info-outline" class="mr-1" />
                        总缴纳保费
                    </span>
                </div>
            </div>

            <!-- 总收益卡片 -->
            <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-purple-500">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-500 text-sm">总收益</p>
                        <p class="text-2xl font-bold text-purple-600">
                            {{ ((formData.value.guaranteedValue || 0) + (formData.value.nonGuaranteedBonus
                                ||
                                0)) - (formData.value.annualPremium * formData.value.paymentYears) }}元
                        </p>
                    </div>
                    <div class="bg-purple-100 p-2 rounded-full">
                        <Icon icon="material-symbols:savings" class="text-2xl text-purple-500" />
                    </div>
                </div>
                <div class="mt-2 text-xs text-gray-500">
                    <span class="flex items-center">
                        <Icon icon="material-symbols:info-outline" class="mr-1" />
                        保证现金价值 + 非保证红利
                    </span>
                </div>
            </div>
        </div>
        <a-spin :spinning="calculating">
            <div class="flex flex-wrap items-center justify-between gap-4 w-full">
                <!-- 表单区域 -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6 flex-1 min-h-[464px]">
                    <h2 class="text-lg font-semibold mb-4 flex items-center">
                        <Icon icon="material-symbols:edit-document" class="mr-2 text-blue-500" />
                        缴费信息
                    </h2>

                    <!-- 精确计算模式 -->
                    <div v-if="calculationType === 'precise'" class="space-y-4 ">
                        <!-- 保险公司和地区选择区域 - 移除 -->

                        <!-- 产品选择区域 -->
                        <div class="mb-2">
                            <div class="flex flex-col gap-2">
                                <ProductSelector v-model="formData.value.productSelection"
                                    @select="handleProductSelectionComplete" @ageRange="handleAgeRangeChange"
                                    @complete-status-change="handleCompleteStatusChange" @step-change="handleStepChange"
                                    @confirm="handleConfirmAndCalculate" :typeOptions="typeOptions"
                                    ref="productSelectorRef" />

                                <div v-if="formData.value.productSelection && formData.value.productSelection.product"
                                    class="selected-product-info mt-2 p-3 bg-gray-50 rounded-lg border border-gray-200">
                                    <div class="flex items-center justify-between">
                                        <h4 class="text-sm font-medium">已选产品和客户信息</h4>
                                        <a-button type="link" size="small"
                                            @click="resetProductSelection">重新选择</a-button>
                                    </div>
                                    <div class="grid grid-cols-2 gap-2 mt-2">
                                        <div class="text-xs">
                                            <span class="text-gray-500">产品名称:</span>
                                            <span class="ml-1">{{ formData.value.productSelection.product.productName
                                                }}</span>
                                        </div>
                                        <div class="text-xs">
                                            <span class="text-gray-500">保险公司:</span>
                                            <span class="ml-1">{{ formData.value.productSelection.product.companyName
                                                }}</span>
                                        </div>
                                        <div class="text-xs">
                                            <span class="text-gray-500">保障期限:</span>
                                            <span class="ml-1">{{
                                                formData.value.productSelection.product.guaranteePeriod
                                                }}</span>
                                        </div>
                                        <div class="text-xs">
                                            <span class="text-gray-500">客户姓名:</span>
                                            <span class="ml-1">{{ formData.value.productSelection.customerName || '未填写'
                                                }}</span>
                                        </div>
                                        <div class="text-xs">
                                            <span class="text-gray-500">客户年龄:</span>
                                            <span class="ml-1">{{ formData.value.productSelection.startAge || '未填写'
                                                }}岁</span>
                                        </div>
                                        <div class="text-xs">
                                            <span class="text-gray-500">保费金额:</span>
                                            <span class="ml-1">{{
                                                formatNumber(formData.value.productSelection.precisePremium, {
                                                    currency: formData.value.productSelection.currency
                                                }) ||
                                                '未填写' }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 通用计算模式 -->
                    <div v-if="calculationType === 'general'"
                        class="space-y-4 flex flex-wrap items-center justify-between gap-4">

                        <div class="w-full">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">年缴保费</label>
                                    <a-input-number v-model:value="formData.value.annualPremium" :min="0" :precision="2"
                                        class="w-full" placeholder="请输入年缴保费" :formatter="formatAmount"
                                        :parser="parseAmount" />
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">缴费期</label>
                                    <a-input-number v-model:value="formData.value.paymentYears" :min="1" :max="100"
                                        :precision="0" class="w-full" placeholder="请输入缴费期" :formatter="formatAmount"
                                        :parser="parseAmount" />
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">持有期</label>
                                    <a-input-number v-model:value="formData.value.holdingYears" :min="1" :precision="0"
                                        class="w-full" placeholder="请输入持有年数" />
                                </div>
                                <div class="hidden md:block">
                                    <!-- 占位，确保布局对称 -->
                                    <!-- <label class="block text-sm font-medium text-gray-700 mb-1">缴费方式</label>
                                <a-select v-model:value="formData.value.paymentMethod" :options="paymentMethodOptions"
                                    class="w-full" placeholder="请选择缴费方式" /> -->
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">保证现金价值</label>
                                    <a-input-number v-model:value="formData.value.guaranteedValue" :min="0"
                                        :precision="2" class="w-full" placeholder="请输入保证现金价值" :formatter="formatAmount"
                                        :parser="parseAmount" />
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">非保证红利</label>
                                    <a-input-number v-model:value="formData.value.nonGuaranteedBonus" :min="0"
                                        :precision="2" class="w-full" placeholder="请输入非保证红利" :formatter="formatAmount"
                                        :parser="parseAmount" />
                                </div>
                            </div>

                            <!-- 保费优惠区域 -->
                            <div class="mt-4">
                                <DiscountManager :discounts="formData.value.discounts"
                                    :maxPaymentYears="formData.value.paymentYears" @add="addDiscount"
                                    @remove="removeDiscount" @update="updateDiscount" />
                            </div>
                        </div>
                    </div>

                    <!-- 计算按钮 -->
                    <div class="mt-4 flex justify-end ">
                        <!-- <a-button type="primary" @click="handleImportCustomer" size="large" class="mr-4">
                        <template #icon>
                            <Icon icon="material-symbols:upload" />
                        </template>
导入客户
</a-button> -->
                        <a-button type="primary" :loading="calculating" @click="calculate" size="large" class="px-8"
                            v-if="calculationType === 'general'">
                            <template #icon>
                                <Icon icon="material-symbols:calculate" />
                            </template>
                            计算收益
                        </a-button>
                    </div>
                </div>
                <!-- 图表区域  TODO: 展示单利和复利收益折线图-->
                <!-- <div class="mb-6 flex-1  bg-white rounded-lg shadow-md p-6 h-[464px]" v-if="calculationType === 'precise'">
                <h2 class="text-lg font-semibold mb-4 flex items-center">
                    <Icon icon="material-symbols:analytics" class="mr-2 text-blue-500" />
                    产品单复利收益计算工具注意事项
                </h2>
                <p class="text-gray-500 mb-4">
                    1. 相关结果仅供参考，具体收益以保险公司实际计算为准。
                </p>
                <p class="text-gray-500 mb-4">
                    2. 计算结果的案例为虚构内容，仅供参考。
                </p>
            </div> -->
                <div class="mb-6 flex-1  bg-white rounded-lg shadow-md p-6 h-[452px]"
                    v-if="calculationType === 'general'">
                    <h2 class="text-lg font-semibold mb-4 flex items-center">
                        <Icon icon="material-symbols:analytics" class="mr-2 text-blue-500" />
                        通用单复利收益计算工具注意事项
                    </h2>
                    <p class="text-gray-500 mb-4">
                        1. 相关结果仅供参考，具体收益以保险公司实际计算为准。
                    </p>
                    <p class="text-gray-500 mb-4">
                        2. 计算结果的案例为假设案例，仅供参考。
                    </p>
                </div>
            </div>
        </a-spin> <!-- 计算结果 -->
        <div v-if="calculationResult || apiCalculationResult" class="bg-white rounded-lg shadow-md p-6 mb-6"
            ref="resultRef" id="export-pdf">
            <div class="flex items-center justify-between " id="no-pdf">
                <h2 class="text-lg font-semibold mb-4 flex items-center" id="no-pdf">
                    <Icon icon="material-symbols:analytics" class="mr-2 text-blue-500" id="no-pdf" />
                    计算结果
                </h2>
                <a-button type="primary" @click="exportPdf" id="no-pdf"
                    v-if="calculationType === 'precise'">导出PDF</a-button>
            </div>
            <h1 class="text-4xl font-semibold mb-4 text-center pdf-only">
                {{ formData.value.productName }}
            </h1>

            <!-- 结果切换标签 -->

            <a-tabs v-show="apiCalculationResult" default-active-key="api">
                <a-tab-pane key="api">
                    <ApiResultDisplay ref="apiResultDisplayRef" :apiCalculationResult="apiCalculationResult"
                        @tab-change="handleApiTabChange" @exportToExcel="exportToExcel"
                        @apply-stress-test="handleApplyStressTest" @reset-stress-test="handleResetStressTest"
                        :currency="formData.value.currency" :selectedProduct="formData.value.productSelection" />
                </a-tab-pane>
            </a-tabs>

            <!-- 如果没有API结果，只显示普通单复利计算结果 -->
            <LocalResultDisplay v-if="!apiCalculationResult && calculationResult" :calculationResult="calculationResult"
                :formData="formData.value" @exportToExcel="exportToExcel" />
        </div>

        <!-- 产品选择模态框 -->
        <!-- <ProductSelectModal :visible="showProductModal" :product-types="productTypes"
            @update:visible="showProductModal = $event" @select="handleProductSelect" /> -->
    </div>

</template>

<script setup>
import { message } from 'ant-design-vue';
import { Icon } from '@iconify/vue';
import { productAPI } from '@/api';
import { toTraditional } from '@/utils/chineseConverter';
import ProductSelector from './components/interest/components/ProductSelector.vue';

// 引入导出功能
import { exportExcel } from '@/utils/excel';
import { exportElementToPDF } from '@/utils/pdf';
import { formatNumber } from '@/utils/number';
// 引入功能组件
import DiscountManager from './components/interest/components/DiscountManager.vue';
import ApiResultDisplay from './components/interest/components/ApiResultDisplay.vue';
import LocalResultDisplay from './components/interest/components/LocalResultDisplay.vue';
import ProductSelectModal from './components/interest/components/ProductSelectModal.vue';

// 引入业务逻辑
import { useProductSearch } from './components/interest/composables/useProductSearch';
import { useInterestCalculation } from './components/interest/composables/useInterestCalculation';
import { useApiCalculation } from './components/interest/composables/useApiCalculation';
import { useTableExport } from './components/interest/components/api_result/hooks/useTableExport';
// 初始化各个业务逻辑
const { productList } = useProductSearch(true, { isCashValue: 1 });

// 结果区域的引用，用于导出PDF
const resultRef = ref(null);
// ApiResultDisplay组件的引用
const apiResultDisplayRef = ref(null);

const {
    calculating,
    calculationResult,
    validateForm,
    calculateLocal
} = useInterestCalculation();

const {
    apiCalculationResult,
    calculateWithNewApi,
    resetApiCalculation
} = useApiCalculation();

// 计算方式选择
const calculationType = ref('precise'); // 'precise' 或 'general'
const productTypes = ref([]);
const productSelectorRef = ref(null);

// ProductSelector状态
const productSelectorComplete = ref(false);
const currentProductSelectorStep = ref(0);
// 产品选择模态框
const showProductModal = ref(false);
const typeOptions = ref([
    { value: 'standard', label: '累积生息' },
    { value: 'non_interest', label: '现金提取' }
]);
// 表单数据
const formData = reactive({
    value: {
        annualPremium: null, // 通用计算使用
        precisePremium: null, // 精确计算使用
        paymentYears: null,
        discounts: [],
        holdingYears: null,
        guaranteedValue: null,
        nonGuaranteedBonus: null,
        productId: null,
        productName: '',
        company: undefined,
        region: null,
        mainType: null,
        customerName: '',
        startAge: null,
        type: 'standard', // 计算类型
        productSelection: null,
        // 子产品相关信息
        paymentMethods: [], // 支持的缴费方式列表
        selectedPaymentMethod: null, // 用户选择的缴费方式
        prepaymentYears: [], // 支持的缴费年期列表
        selectedPrepaymentYear: null, // 用户选择的缴费年期
        premiumRange: [], // 保费范围 [最小值, 最大值]
        // 压力测试相关
        enableStressTest: true, // 是否启用压力测试
        dividendRate: 100, // 分红实现率，默认100%
    }
});

// 年龄范围
const ageRange = ref({
    min: 0,
    max: 100
});

// 监听productId变化，重置计算结果
watch(() => formData.value.productId, () => {
    calculationResult.value = null;
    resetApiCalculation();
});

// 监听计算方式变化
const handleCalculationTypeChange = () => {
    console.log('切换计算方式:123', productSelectorRef.value);
    productSelectorRef.value?.resetToParentStep();
};
watch(calculationType, (newVal) => {
    console.log('切换计算方式:', newVal);
    // 切换计算方式时重置表单
    if (newVal === 'precise') {
        formData.value.productId = null;
        formData.value.productName = '';
        formData.value.company = undefined;
        formData.value.region = '香港';
        // 重置子产品相关字段
        formData.value.paymentMethods = [];
        formData.value.selectedPaymentMethod = null;
        formData.value.prepaymentYears = [];
        formData.value.selectedPrepaymentYear = null;
        formData.value.premiumRange = [];
        formData.value.precisePremium = null;
        console.log('productSelectorRef.value', productSelectorRef.value);
    } else {
        formData.value.productId = null;
        formData.value.productName = '';
        formData.value.company = undefined;
        formData.value.region = '香港';
        // 重置子产品相关字段
        formData.value.paymentMethods = [];
        formData.value.selectedPaymentMethod = null;
        formData.value.prepaymentYears = [];
        formData.value.selectedPrepaymentYear = null;
        formData.value.premiumRange = [];
        formData.value.precisePremium = null;
    }
    calculationResult.value = null;
    resetApiCalculation();
});

// 格式化金额
const formatAmount = (value) => {
    if (!value) return '';
    return Number(value).toLocaleString();
};
const paymentYearsLabels = {
    "1": "1年缴",
    "2": "2年缴",
    "3": "3年缴",
    "4": "4年缴",
    "5": "5年缴",
    "6": "6年缴",
    "7": "7年缴",
    "8": "8年缴",
    "9": "9年缴",
    "10": "10年缴",
};
const paymentYearsOptions = Object.entries(paymentYearsLabels).map(([key, value]) => ({
    value: key,
    label: value
}));

// 获取产品类型列表
const getProductTypes = async () => {
    try {
        const res = await productAPI.getChinaLifeProductCalculationType();
        if (res && res !== undefined) {
            productTypes.value = res.map(item => {
                return {
                    value: item.code,
                    label: item.name,
                    code: item.code
                }
            });
        }
        return res;
    } catch (error) {

        return null;
    }
};
// 解析金额
const parseAmount = (value) => {
    if (!value) return '';

    // 处理"w"或"W"表示的万元
    if (value.toString().toLowerCase().includes('w')) {
        // 提取数字部分
        const numPart = value.toString().toLowerCase().replace(/[^0-9.]/g, '');
        if (numPart) {
            // 将数字乘以10000（万）
            return parseFloat(numPart) * 10000;
        }
    }

    // 移除货币符号和千位分隔符
    return value.replace(/[^\d.]/g, '');
};

// 新增的handleProductSelectionComplete方法
const handleProductSelectionComplete = (selection) => {

    // 设置产品基本信息和客户信息
    formData.value.productId = selection.productId; // 使用子产品ID
    formData.value.productName = selection.subProduct.productName; // 使用子产品名称
    formData.value.company = selection.product.companyName;
    formData.value.mainType = selection.product.categoryName;
    formData.value.region = selection.product.region;

    // 设置客户信息
    formData.value.customerName = selection.customerName;
    formData.value.startAge = selection.startAge;

    // 设置缴费相关信息
    formData.value.currency = selection.currency;
    formData.value.paymentTerm = selection.paymentTerm;
    formData.value.paymentMethod = selection.paymentMethod;
    formData.value.paymentYears = selection.selectedPrepaymentYear || selection.paymentTerm;
    formData.value.precisePremium = selection.precisePremium;
    formData.value.holdingYears = selection.holdingYears;
    formData.value.type = selection.type || 'standard';
    formData.value.discounts = selection.discounts || [];
    console.log('handleProductSelectionComplete', formData.value.currency);

    // 保存子产品的缴费方式选项
    formData.value.paymentMethods = Array.isArray(selection.subProduct.paymentMethods) ?
        selection.subProduct.paymentMethods : ['ANNUALLY'];
    formData.value.selectedPaymentMethod = selection.selectedPaymentMethod ||
        selection.paymentMethod ||
        (formData.value.paymentMethods.length > 0 ? formData.value.paymentMethods[0] : 'ANNUALLY');

    // 保存子产品的缴费年期选项
    if (selection.subProduct.prepaymentYears && Array.isArray(selection.subProduct.prepaymentYears)) {
        formData.value.prepaymentYears = selection.subProduct.prepaymentYears;
    } else {
        formData.value.prepaymentYears = [parseInt(selection.paymentTerm)];
    }
    formData.value.selectedPrepaymentYear = selection.selectedPrepaymentYear ||
        parseInt(selection.paymentTerm);

    // 设置保费范围
    formData.value.premiumRange = Array.isArray(selection.subProduct.premiumRange) ?
        selection.subProduct.premiumRange.map(val => parseFloat(val)) :
        [1, Number.MAX_SAFE_INTEGER];

    // 重置计算结果
    calculationResult.value = null;
    resetApiCalculation();

};

// 新增的handleAgeRangeChange方法
const handleAgeRangeChange = (range) => {
    if (range) {
        // 处理字符串格式的年龄范围（如"15天"、"80岁"）
        const minAge = typeof range.min === 'string' ? parseAgeRange(range.min) : range.min;
        const maxAge = typeof range.max === 'string' ? parseAgeRange(range.max) : range.max;

        // 更新年龄范围
        ageRange.value = {
            min: minAge,
            max: maxAge
        };

        // 如果当前年龄不在范围内，调整到范围内
        if (formData.value.startAge < minAge) {
            formData.value.startAge = minAge;
        } else if (formData.value.startAge > maxAge) {
            formData.value.startAge = maxAge;
        }

        console.log('年龄范围已更新:', ageRange.value);
    }
};

// 处理缴费年期变更
const handlePrepaymentYearChange = (value) => {
    // 更新paymentYears字段，保持一致性
    formData.value.paymentYears = value;

    // 清除之前的计算结果
    calculationResult.value = null;
    resetApiCalculation();

    // 可能需要更新折扣管理器的最大年限
    // 触发消息提示
    message.success(`已选择${value}年缴费期`);
};

// 添加优惠
const addDiscount = (discount) => {
    formData.value.discounts.push(discount);
};

// 移除优惠
const removeDiscount = (index) => {
    formData.value.discounts.splice(index, 1);
};

// 更新优惠
const updateDiscount = (index, discount) => {
    if (index >= 0 && index < formData.value.discounts.length) {
        formData.value.discounts[index] = { ...discount };
    }
};

// 计算收益
const calculate = async () => {

    calculating.value = true;
    calculationResult.value = null;
    resetApiCalculation();

    try {
        // 表单验证
        if (!validateForm(formData.value)) {
            calculating.value = false;
            return;
        }

        // 子产品字段验证
        if (calculationType.value === 'precise' && !validateSubProductFields()) {
            calculating.value = false;
            return;
        }

        // 判断是否选择了产品，如果选择了则使用新的API计算
        if (calculationType.value === 'precise') {
            if (!formData.value.productId) {
                message.warning('请选择产品');
                return
            }
            // 使用新的动态计算API
            await calculateWithNewApi(formData.value);
            console.log('apiResultDisplayRef.value', apiResultDisplayRef.value);
            // 同步压力测试状态到子组件
            if (apiResultDisplayRef.value && apiResultDisplayRef.value.$refs.apiResultRef) {
                apiResultDisplayRef.value.$refs.apiResultRef.syncStressTestStatus(
                    formData.value.enableStressTest,
                    formData.value.dividendRate
                );
            }

            // 无论API计算是否成功，都执行前端计算作为备用
            // calculationResult.value = calculateLocal(formData.value);
        } else {
            // 仅使用前端计算
            calculationResult.value = calculateLocal(formData.value);
        }
    } catch (error) {
        console.error('计算失败:', error);
        message.error(`计算失败: ${error.message || '请检查输入数据'}`);
    } finally {
        calculating.value = false;

        // 如果有计算结果，等待DOM更新后滚动到结果区域
        if (calculationResult.value || apiCalculationResult.value) {
            nextTick(() => {
                if (resultRef.value) {
                    // 使用平滑滚动效果
                    resultRef.value.scrollIntoView({ behavior: 'smooth', block: 'start' });

                    // 直接传递货币符号到StatisticsCards组件
                    if (apiResultDisplayRef.value && apiResultDisplayRef.value.$refs.apiResultRef) {
                        console.log('直接设置货币符号1:', apiResultDisplayRef.value.$refs.apiResultRef.$refs.productInfoCardRef[0]);
                        apiResultDisplayRef.value.$refs.apiResultRef.$refs.statisticsCardsRef.currency = formData.value.currency;
                        apiResultDisplayRef.value.$refs.apiResultRef.$refs.productInfoCardRef[0].setCurrency(formData.value.currency);
                        console.log('直接设置货币符号:', apiResultDisplayRef.value.$refs.apiResultRef.$refs.productInfoCardRef);
                    }
                }
            });
        }
    }
};

// 验证子产品相关字段
const validateSubProductFields = () => {
    // 验证保费范围
    if (formData.value.premiumRange && formData.value.premiumRange.length === 2) {
        const minPremium = formData.value.premiumRange[0];
        const maxPremium = formData.value.premiumRange[1];

        if (formData.value.precisePremium < minPremium || formData.value.precisePremium > maxPremium) {
            message.error(`保费必须在允许范围内: ${formatAmount(minPremium)}-${formatAmount(maxPremium)}元`);
            return false;
        }
    }

    // 验证缴费年期选择
    // if (formData.value.prepaymentYears && formData.value.prepaymentYears.length > 0) {
    //     if (!formData.value.selectedPrepaymentYear) {
    //         message.error('请选择缴费年期');
    //         return false;
    //     }

    //     if (!formData.value.prepaymentYears.includes(formData.value.selectedPrepaymentYear)) {
    //         message.error('请选择有效的缴费年期');
    //         return false;
    //     }
    // }

    // 验证缴费方式
    if (formData.value.paymentMethods && formData.value.paymentMethods.length > 0) {
        if (!formData.value.selectedPaymentMethod) {
            message.error('请选择缴费方式');
            return false;
        }

        if (!formData.value.paymentMethods.includes(formData.value.selectedPaymentMethod)) {
            message.error('请选择有效的缴费方式');
            return false;
        }
    }

    return true;
};

// 处理API结果tab切换
const handleApiTabChange = () => {
    productSelectorRef.value?.resetToParentStep();
};

// 导出Excel
const exportToExcel = () => {
    try {
        // 初始化表格导出工具
        const { exportTableToExcel } = useTableExport();

        if (calculationType.value === 'precise' && apiCalculationResult.value) {
            // 精确计算模式（API结果）
            // 获取当前选中的tab
            const currentTabIndex = apiCalculationResult.value.irr.length > 1 ? apiTabIndex.value || 0 : 0;
            const currentTab = apiCalculationResult.value.irr[currentTabIndex];

            // 检查是否有多个tab
            if (apiCalculationResult.value.irr && apiCalculationResult.value.irr.length > 1) {
                // 多tab模式，使用多工作表导出
                // 获取API结果中的所有tab页配置
                const productName = apiCalculationResult.value.irr[0]?.productName || '产品';
                const filename = `${productName}-单复利计算结果.xlsx`;

                // 准备多个工作表的数据
                const sheetsData = apiCalculationResult.value.irr.map((tab, index) => {
                    // 使用表格导出工具过滤数据，应用当前的显示列和行步长设置
                    const { filteredColumns } = exportTableToExcel({
                        exportExcelFunc: () => { }, // 不实际导出，仅获取列
                        data: tab.list,
                        columnsVisible: apiResultDisplayRef.value ?
                            apiResultDisplayRef.value.$refs.apiResultRef?.columnsVisible || {} : {},
                        theme: apiResultDisplayRef.value ?
                            apiResultDisplayRef.value.$refs.apiResultRef?.tableTheme || 'default' : 'default',
                        customColor: apiResultDisplayRef.value ?
                            apiResultDisplayRef.value.$refs.apiResultRef?.customThemeColor || null : null,
                        rowStep: apiResultDisplayRef.value ?
                            apiResultDisplayRef.value.$refs.apiResultRef?.rowStepValue || 1 : 1,
                        enableHighlight: apiResultDisplayRef.value ?
                            apiResultDisplayRef.value.$refs.apiResultRef?.enableHighlight || true : true,
                        highlightRules: apiResultDisplayRef.value ?
                            apiResultDisplayRef.value.$refs.apiResultRef?.highlightRules || [] : [],
                        filename: '' // 不需要文件名
                    });

                    // 返回工作表配置
                    const sheetName = tab.name || `方案${index + 1}`;
                    return { sheetName, data: tab.list, columns: filteredColumns };
                });
                // 创建Excel选项
                const { createExcelOptions } = useTableExport();
                const excelOptions = createExcelOptions(
                    apiResultDisplayRef.value ? apiResultDisplayRef.value.$refs.apiResultRef?.tableTheme || 'default' : 'default',
                    apiResultDisplayRef.value ? apiResultDisplayRef.value.$refs.apiResultRef?.customThemeColor || null : null,
                    apiResultDisplayRef.value ? apiResultDisplayRef.value.$refs.apiResultRef?.enableHighlight || true : true,
                    productName,
                    productInfo
                );

                // 获取当前选中的tab的产品信息
                const productData = currentTab || apiCalculationResult.value.irr[0];
                const productInfo = productData.dynamicIRRData || {
                    gender: productData.gender,
                    assumedAge: productData.years || productData.assumedAge,
                    smoking_status: productData.smoking_status,
                    annualPremium: productData.annualPremium,
                    paymentMode: productData.paymentMode,
                    coverageTerm: productData.coverageTerm,
                    premiumPaymentTerm: productData.premiumPaymentTerm,
                    policyCurrency: productData.policyCurrency
                };

                exportExcel(null, [], filename, {
                    multipleSheets: true,
                    sheetsData: sheetsData.map(sheet => ({
                        sheetName: sheet.sheetName,
                        data: sheet.data
                    })),
                    autoWidth: true,
                    headerStyle: excelOptions.headerStyle,
                    cellStyle: excelOptions.cellStyle,
                    highlightCellStyle: excelOptions.highlightCellStyle,
                    enableHighlight: excelOptions.enableHighlight,
                    productName: productName,
                    productInfo: productInfo,
                    theme: currentTheme
                });
            } else {
                console.log('apiResultDisplayRef.value', apiResultDisplayRef.value.$refs.apiResultRef);
                // 单tab模式，使用单工作表导出
                // 获取表格设置和API结果
                const currentTheme = apiResultDisplayRef.value ?
                    apiResultDisplayRef.value.$refs.apiResultRef?.tableTheme || 'default' : 'default';
                const customColor = apiResultDisplayRef.value && currentTheme === 'custom' ?
                    apiResultDisplayRef.value.$refs.apiResultRef?.customThemeColor || null : null;
                const rowStep = apiResultDisplayRef.value ?
                    apiResultDisplayRef.value.$refs.apiResultRef?.rowStepValue || 1 : 1;
                const enableHighlight = apiResultDisplayRef.value ?
                    apiResultDisplayRef.value.$refs.apiResultRef?.enableHighlight || true : true;
                const columnsVisible = apiResultDisplayRef.value ?
                    apiResultDisplayRef.value.$refs.apiResultRef?.columnsVisible || {} : {};
                console.log('sheetsData', apiResultDisplayRef.value.$refs.apiResultRef.enableHighlight);
                // 设置文件名
                const filename = `${currentTab.productName || '产品'}-单复利计算结果.xlsx`;

                // 获取产品信息
                const productInfo = currentTab.dynamicIRRData || {
                    productName: currentTab.productName,
                    gender: currentTab.gender,
                    assumedAge: currentTab.years || currentTab.assumedAge,
                    smoking_status: currentTab.smoking_status,
                    annualPremium: currentTab.annualPremium,
                    paymentMode: currentTab.paymentMode,
                    coverageTerm: currentTab.coverageTerm,
                    premiumPaymentTerm: currentTab.premiumPaymentTerm,
                    policyCurrency: currentTab.policyCurrency
                };

                // 使用表格导出工具导出Excel
                exportTableToExcel({
                    exportExcelFunc: exportExcel,
                    data: currentTab.list,
                    columnsVisible,
                    theme: currentTheme,
                    customColor,
                    rowStep,
                    enableHighlight,
                    filename,
                    productInfo,
                    productName: currentTab.productName || productInfo.productName,
                    highlightRules: apiResultDisplayRef.value ?
                        apiResultDisplayRef.value.$refs.apiResultRef?.highlightRules || [] : []
                });
            }
        } else if (calculationResult.value) {
            // 通用计算模式
            // 设置列
            const columns = [
                { title: '年份', dataIndex: 'year' },
                { title: '现金流（元）', dataIndex: 'cashFlow' },
                { title: '累计保费（元）', dataIndex: 'totalPremium' },
                { title: '预估现金价值（元）', dataIndex: 'cashValue' }
            ];

            // 准备现金流数据
            const cashFlows = calculationResult.value.cashFlows;
            const annualPremium = formData.value.annualPremium || 0;
            const paymentYears = formData.value.paymentYears || 0;
            const holdingYears = formData.value.holdingYears || 0;
            const finalValue = (formData.value.guaranteedValue || 0) + (formData.value.nonGuaranteedBonus || 0);

            // 设置数据
            const data = cashFlows.map((flow, index) => {
                const year = index + 1;
                const totalPremium = year <= paymentYears ? annualPremium * year : annualPremium * paymentYears;
                // 简化模型：线性增长的现金价值
                const cashValue = year === holdingYears ? finalValue : Math.round(finalValue * year / holdingYears);

                return {
                    year: `第${year}年`,
                    cashFlow: flow.toLocaleString(),
                    totalPremium: totalPremium.toLocaleString(),
                    cashValue: cashValue.toLocaleString()
                };
            });

            // 添加汇总信息
            data.push({
                year: '汇总',
                cashFlow: '',
                totalPremium: `总投入: ${(annualPremium * paymentYears).toLocaleString()}元`,
                cashValue: `总收益: ${((formData.value.guaranteedValue || 0) + (formData.value.nonGuaranteedBonus || 0)).toLocaleString()}元`
            });
            data.push({
                year: '收益率',
                cashFlow: '',
                totalPremium: `单利: ${calculationResult.value.simpleInterest}%`,
                cashValue: `复利: ${calculationResult.value.compoundInterest}%`
            });

            // 设置文件名
            const filename = '通用单复利计算结果.xlsx';

            // 创建Excel选项 - 使用默认主题
            const { createExcelOptions } = useTableExport();
            const excelOptions = createExcelOptions('default', null, true);

            // 使用表格导出工具导出Excel
            exportExcel(data, columns, filename, {
                sheetName: '单复利计算结果',
                autoWidth: true,
                headerStyle: excelOptions.headerStyle,
                cellStyle: excelOptions.cellStyle,
                highlightCellStyle: excelOptions.highlightCellStyle,
                enableHighlight: excelOptions.enableHighlight,

            });
        } else {
            message.warning('没有可导出的数据');
            return;
        }

        message.success('导出Excel成功');
    } catch (error) {
        console.error('导出Excel失败:', error);
        message.error('导出Excel失败，请稍后重试');
    }
};

// 导出PDF
const exportToPDF = async () => {
    if (!resultRef.value) {
        message.warning('没有可导出的数据');
        return;
    }

    try {
        // 显示加载提示
        message.loading('正在生成PDF，请稍候...', 0);

        // 导出文件名
        let filename = '';
        if (calculationType.value === 'precise' && apiCalculationResult.value) {
            const currentTab = apiCalculationResult.value.irr[0];
            filename = `${currentTab.productName || '产品'}-单复利计算结果.pdf`;
        } else {
            filename = '通用单复利计算结果.pdf';
        }

        // 如果有图表组件，先处理图表
        if (apiResultDisplayRef.value && calculationType.value === 'precise' && apiCalculationResult.value) {
            try {
                // 调用图表组件的准备方法，将图表转换为图片
                await apiResultDisplayRef.value.prepareForPDFExport();
                console.log('图表已准备好用于PDF导出');
            } catch (error) {
                console.error('准备图表用于PDF导出失败:', error);
                // 继续执行，即使图表处理失败
            }
        }

        // 获取原始DOM元素
        const originalDom = resultRef.value;

        // 创建深度克隆
        const clonedDom = originalDom.cloneNode(true);

        // 设置克隆DOM的样式为不可见但保持结构
        clonedDom.style.position = 'absolute';
        clonedDom.style.left = '-9999px';
        clonedDom.style.top = '-9999px';
        clonedDom.style.opacity = '1';
        clonedDom.style.width = originalDom.offsetWidth + 'px';

        // 查找所有id为no-pdf的元素
        const noPdfElements = clonedDom.querySelectorAll('[id="no-pdf"]');

        // 移除找到的所有元素
        noPdfElements.forEach(element => {
            if (element && element.parentNode) {
                element.parentNode.removeChild(element);
            }
        });

        // 显示所有pdf-only类的元素
        const pdfOnlyElements = clonedDom.querySelectorAll('.pdf-only');
        pdfOnlyElements.forEach(element => {
            if (element) {
                element.style.display = 'block';
            }
        });

        // 将克隆DOM临时附加到body
        document.body.appendChild(clonedDom);

        try {
            console.log('开始导出PDF，使用html2canvas-pro...');

            // 使用处理后的DOM调用exportElementToPDF
            const success = await exportElementToPDF(clonedDom, filename, {
                orientation: 'portrait',
                format: 'a4',
                margin: { top: 15, right: 15, bottom: 15, left: 15 }
            });

            // 显示结果提示
            if (success) {
                message.success('导出PDF成功');
            } else {
                message.error('导出PDF失败，请稍后重试');
            }
        } finally {
            // 无论成功与否，都从文档中移除临时DOM
            if (clonedDom && clonedDom.parentNode) {
                clonedDom.parentNode.removeChild(clonedDom);
            }

            // 关闭加载提示
            message.destroy();

            // 如果有图表组件，恢复图表
            if (apiResultDisplayRef.value && calculationType.value === 'precise' && apiCalculationResult.value) {
                try {
                    // 调用图表组件的恢复方法
                    apiResultDisplayRef.value.restoreChartsAfterExport();
                    console.log('图表已恢复');
                } catch (error) {
                    console.error('恢复图表失败:', error);
                    // 继续执行，即使图表恢复失败
                }
            }
        }
    } catch (error) {
        message.destroy();
        console.error('导出PDF失败:', error);
        message.error(`导出PDF失败: ${error.message || '请稍后重试'}`);

        // 如果有图表组件，确保图表被恢复
        if (apiResultDisplayRef.value && calculationType.value === 'precise' && apiCalculationResult.value) {
            try {
                apiResultDisplayRef.value.restoreChartsAfterExport();
            } catch (e) {
                console.error('恢复图表失败:', e);
            }
        }
    }
};

// 导出PDF的函数（按钮点击事件）
const exportPdf = () => {
    // 调用已有的exportToPDF函数
    exportToPDF();
};

// 监听窗口大小变化（空函数，实际图表处理已移到子组件中）
const handleResize = () => {
    // 空函数，由子组件处理图表大小调整
};

// 组件挂载后执行
onMounted(() => {
    window.addEventListener('resize', handleResize);
    getProductTypes();
});

// 组件卸载前执行
onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
});

// 添加导入客户信息的方法
const handleImportCustomer = () => {
    // TODO: 实现客户信息导入逻辑
    message.info('客户信息导入功能开发中...');
};

// 处理ProductSelector完成状态变化
const handleCompleteStatusChange = (isComplete) => {
    productSelectorComplete.value = isComplete;
};

// 处理ProductSelector步骤变化
const handleStepChange = (step) => {
    currentProductSelectorStep.value = step;
};

// 处理确认并计算
const handleConfirmAndCalculate = () => {
    // 确保数据已准备好
    if (formData.value.productId && formData.value.customerName) {
        // 直接触发计算
        calculate();
    }
};

// 解析年龄范围，处理包含"天"或"岁"的字符串
const parseAgeRange = (ageStr) => {
    if (!ageStr || typeof ageStr !== 'string') return 0;

    // 处理数字类型，直接返回
    if (!isNaN(ageStr)) return Number(ageStr);

    // 移除所有空格
    const trimmedStr = ageStr.trim();

    // 提取数字部分
    const numMatch = trimmedStr.match(/\d+/);
    if (!numMatch) return 0;

    const num = parseInt(numMatch[0]);

    // 检查是否包含"天"，如果是，则转换为年龄（假设365天=1岁）
    if (trimmedStr.includes('天') || trimmedStr.includes('日') ||
        trimmedStr.includes('day') || trimmedStr.includes('days')) {
        return Math.max(0, Math.floor(num / 365));
    }

    // 其他情况（如包含"岁"、"歲"等），直接返回数字部分
    return num;
};

// 获取缴费方式名称
const getPaymentMethodName = (method) => {
    const methods = {
        "ANNUALLY": "年缴",
        "MONTHLY": "月缴",
        "QUARTERLY": "季缴",
        "SEMI_ANNUALLY": "半年缴",
        "SINGLE_PREMIUM": "趸缴",
        "SINGLE_PAYMENT": "整付保费"
    };
    return methods[method] || method;
};

// 重置产品选择
const resetProductSelection = () => {
    // 重置表单数据
    formData.value.productSelection = null;
    formData.value.productId = null;
    formData.value.productName = '';
    formData.value.company = undefined;
    formData.value.mainType = null;
    formData.value.region = null;
    formData.value.currency = null;
    formData.value.paymentTerm = null;
    formData.value.paymentMethod = null;
    formData.value.paymentYears = null;

    // 重置子产品相关字段
    formData.value.paymentMethods = [];
    formData.value.selectedPaymentMethod = null;
    formData.value.prepaymentYears = [];
    formData.value.selectedPrepaymentYear = null;
    formData.value.premiumRange = [];
    formData.value.precisePremium = null;

    // 重置年龄范围
    ageRange.value = {
        min: 0,
        max: 100
    };

    // 重置计算结果
    calculationResult.value = null;
    resetApiCalculation();

    // 重置ProductSelector组件状态
    if (productSelectorRef.value) {
        try {
            productSelectorRef.value.resetToParentStep();
            console.log('ProductSelector组件重置成功');
        } catch (error) {
            console.error('ProductSelector组件重置失败:', error);
        }
    }
};

// 添加压力测试处理函数
const handleApplyStressTest = async (stressTestSettings) => {
    console.log('应用压力测试设置222:', stressTestSettings);

    // 更新表单数据中的压力测试设置
    formData.value.enableStressTest = stressTestSettings.enableStressTest;
    formData.value.dividendRate = stressTestSettings.dividendRate;

    // 检查是否需要重新调用API计算（只有从"应用"按钮触发的才执行API计算）
    if (formData.value.enableStressTest) {
        calculating.value = true;

        // 设置压力测试加载状态
        if (apiResultDisplayRef.value && apiResultDisplayRef.value.$refs.apiResultRef) {
            apiResultDisplayRef.value.$refs.apiResultRef.setStressTestLoading(true);
        }

        try {
            // 关闭设置面板
            if (apiResultDisplayRef.value && apiResultDisplayRef.value.$refs.apiResultRef) {
                apiResultDisplayRef.value.$refs.apiResultRef.editTableModal = false;
            }

            // 调用API计算
            await calculateWithNewApi(formData.value);

            // 同步压力测试状态到子组件
            if (apiResultDisplayRef.value && apiResultDisplayRef.value.$refs.apiResultRef) {
                apiResultDisplayRef.value.$refs.apiResultRef.syncStressTestStatus(
                    formData.value.enableStressTest,
                    formData.value.dividendRate
                );
            }
        } catch (error) {
            console.error('压力测试计算失败:', error);
            message.error('压力测试计算失败，请稍后重试');
        } finally {
            calculating.value = false;

            // 重置压力测试加载状态
            if (apiResultDisplayRef.value && apiResultDisplayRef.value.$refs.apiResultRef) {
                apiResultDisplayRef.value.$refs.apiResultRef.setStressTestLoading(false);
            }
        }
    } else if (stressTestSettings._fromStressTestApplyButton) {
        // 如果是从"应用"按钮触发的，但禁用了压力测试，则重置
        handleResetStressTest();
    }
};

// 重置压力测试
const handleResetStressTest = async (param) => {
    // 检查是否是从重置按钮触发的
    const fromButton = param && param._fromStressTestApplyButton || true;
    // 重置压力测试设置
    formData.value.enableStressTest = false;
    formData.value.dividendRate = 100;

    // 只有当从按钮触发时才重新调用API计算
    if (fromButton) {
        calculating.value = true;

        // 设置压力测试加载状态
        if (apiResultDisplayRef.value && apiResultDisplayRef.value.$refs.apiResultRef) {
            apiResultDisplayRef.value.$refs.apiResultRef.setStressTestLoading(true);
        }

        try {
            // 关闭设置面板
            if (apiResultDisplayRef.value && apiResultDisplayRef.value.$refs.apiResultRef) {
                apiResultDisplayRef.value.$refs.apiResultRef.editTableModal = false;
            }

            await calculateWithNewApi(formData.value);

            // 同步压力测试状态到子组件
            if (apiResultDisplayRef.value && apiResultDisplayRef.value.$refs.apiResultRef) {
                apiResultDisplayRef.value.$refs.apiResultRef.syncStressTestStatus(
                    false,
                    100
                );
            }

            message.success('已重置压力测试');
        } catch (error) {
            console.error('重置压力测试失败:', error);
            message.error('重置压力测试失败，请稍后重试');
        } finally {
            calculating.value = false;

            // 重置压力测试加载状态
            if (apiResultDisplayRef.value && apiResultDisplayRef.value.$refs.apiResultRef) {
                apiResultDisplayRef.value.$refs.apiResultRef.setStressTestLoading(false);
            }
        }
    } else {
        // 只同步状态，不调用API
        if (apiResultDisplayRef.value && apiResultDisplayRef.value.$refs.apiResultRef) {
            apiResultDisplayRef.value.$refs.apiResultRef.syncStressTestStatus(
                false,
                100
            );
        }
    }
};
</script>

<style scoped>
.container {
    max-width: 1200px;
}

@media (max-width: 640px) {
    .container {
        padding: 0.5rem;
    }
}

/* 标题区域样式 */
.title-section {
    background-size: 200% 200%;
    animation: gradientAnimation 5s ease infinite;
}

@keyframes gradientAnimation {
    0% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

.page-title {
    color: #fff;
}

.page-description {
    color: #e0e0e0;
}

/* 统计卡片样式 */
.stat-card {
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* 输入框样式覆盖 */
:deep(.ant-input-number) {
    width: 100%;
}

:deep(.ant-input-number-input) {
    height: 32px;
}

/* 产品选择下拉框样式 */
:deep(.ant-select-item) {
    padding: 8px 12px;
}

:deep(.ant-select-item + .ant-select-item) {
    border-top: 1px solid #f0f0f0;
}

:deep(.ant-select-item-option-content) {
    white-space: normal;
}

/* 按钮样式覆盖 */
:deep(.ant-btn) {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

:deep(.ant-btn .anticon) {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* PDF导出相关样式 */
.pdf-only {
    display: none;
    /* 在正常视图中隐藏 */
}

@media print {
    .pdf-only {
        display: block;
        /* 在打印视图中显示 */
    }
}
</style>