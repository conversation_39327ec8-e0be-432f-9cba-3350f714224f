<template>
    <div class="container mx-auto py-6 px-4">
        <!-- 页面标题 -->
        <div v-if="!showDetail"
        class="title-section mb-6 p-6 rounded-lg shadow-md text-white bg-gradient-to-r from-green-500 via-green-600 to-green-700">
            <div class="flex items-center">
                <Icon icon="material-symbols:image" class="text-4xl mr-3" />
                <h1 class="text-2xl font-bold page-title">海报生成工具</h1>
            </div>
            <p class="mt-2 page-description">
                选择产品生成精美海报，提升营销效果
            </p>
        </div>

        <!-- 搜索区域 -->
        <div v-if="!showDetail" class="search-section bg-white p-6 rounded-lg shadow-md mb-6">
            <div class="flex flex-wrap items-center justify-between gap-4">
                <div class="flex-1 min-w-[280px] flex items-center gap-4">
                    <div class="flex items-center">
                        <a-input-search v-model:value="searchKeyword" placeholder="搜索产品名称或代码" enter-button
                            @search="handleSearch" class="flex-1 max-w-[400px]" />
                    </div>
                    <a-button type="primary" @click="fetchList" :loading="loading.list">
                        <template #icon>
                            <Icon icon="material-symbols:refresh" />
                        </template>
                        刷新列表
                    </a-button>
                </div>
                <div class="flex flex-wrap items-center justify-start gap-4">
                    <div class="stat-card bg-blue-50 px-4 py-2 rounded-lg border-l-4 border-blue-500 flex items-center">
                        <div>
                            <span class="text-gray-700 text-sm font-medium">产品总数：</span>
                            <span class="text-lg font-bold text-blue-600">{{ total }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据统计卡片 -->
        <a-card v-if="!showDetail && false" class="mb-6 rounded-lg" :bordered="false">
            <h1 class="text-2xl font-bold mb-4">推荐产品</h1>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- 产品总数卡片 -->
                <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-blue-500">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-500 text-sm">产品总数</p>
                            <p class="text-2xl font-bold">{{ total }}</p>
                        </div>
                        <div class="bg-blue-100 p-2 rounded-full">
                            <Icon icon="material-symbols:inventory-2" class="text-2xl text-blue-500" />
                        </div>
                    </div>
                    <div class="mt-2 text-xs text-gray-500">
                        <span class="flex items-center">
                            <Icon icon="material-symbols:info-outline" class="mr-1" />
                            当前系统产品总数
                        </span>
                    </div>
                </div>

                <!-- 热门产品卡片 -->
                <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-green-500">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-500 text-sm">热门产品</p>
                            <p class="text-2xl font-bold">{{ list.length > 0 ? list[0].productName : '暂无数据' }}</p>
                        </div>
                        <div class="bg-green-100 p-2 rounded-full">
                            <Icon icon="material-symbols:trending-up" class="text-2xl text-green-500" />
                        </div>
                    </div>
                    <div class="mt-2 text-xs text-gray-500">
                        <span class="flex items-center">
                            <Icon icon="material-symbols:info-outline" class="mr-1" />
                            最受欢迎的海报产品
                        </span>
                    </div>
                </div>

                <!-- 海报模板卡片 -->
                <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-orange-500">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-500 text-sm">海报模板</p>
                            <p class="text-2xl font-bold">6</p>
                        </div>
                        <div class="bg-orange-100 p-2 rounded-full">
                            <Icon icon="material-symbols:image" class="text-2xl text-orange-500" />
                        </div>
                    </div>
                    <div class="mt-2 text-xs text-gray-500">
                        <span class="flex items-center">
                            <Icon icon="material-symbols:info-outline" class="mr-1" />
                            可用的海报模板数量
                        </span>
                    </div>
                </div>

                <!-- 已生成海报卡片 -->
                <div class="stat-card bg-white p-4 rounded-lg shadow-md border-l-4 border-purple-500">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-500 text-sm">已生成海报</p>
                            <p class="text-2xl font-bold">{{ Math.floor(Math.random() * 100) }}</p>
                        </div>
                        <div class="bg-purple-100 p-2 rounded-full">
                            <Icon icon="material-symbols:auto-awesome" class="text-2xl text-purple-500" />
                        </div>
                    </div>
                    <div class="mt-2 text-xs text-gray-500">
                        <span class="flex items-center">
                            <Icon icon="material-symbols:info-outline" class="mr-1" />
                            本月已生成的海报数量
                        </span>
                    </div>
                </div>
            </div>
        </a-card>

        <div class="content-container">
            <!-- 产品列表 -->
            <div v-if="!showDetail" class="product-list-view">
                <product-list :products="list" :total="total" :loading="loading.list" @search="handleSearch"
                    @page-change="handlePageChange" @size-change="handleSizeChange"
                    @select-product="handleSelectProduct" />
            </div>

            <!-- 产品详情 -->
            <div v-else class="product-detail-view">
                <product-detail :product-detail="detail" :loading="loading.detail" @back="showDetail = false"
                    @generate-poster="openPosterTemplates" />
            </div>
        </div>

        <!-- 海报模板选择 -->
        <poster-templates v-model:visible="templatesVisible" :product-data="detail" @cancel="templatesVisible = false"
            @confirm="handleTemplateConfirm" />

        <!-- 海报预览 -->
        <poster-preview v-model:visible="previewVisible" :template-data="selectedTemplate" :product-data="detail"
            @cancel="previewVisible = false" />
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { Icon } from '@iconify/vue';
import { getInsurancePoster, getInsurancePosterDetails } from '@/api/poster';
import { message } from 'ant-design-vue';

// 导入组件
import {
    ProductList,
    ProductDetail,
    PosterTemplates,
    PosterPreview
} from './components/poster/index.js';

// 数据
const list = ref([]);
const detail = ref({});
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(12);
const searchKeyword = ref('');
const showDetail = ref(false);
const templatesVisible = ref(false);
const previewVisible = ref(false);
const selectedTemplate = ref(null);

// 加载状态
const loading = ref({
    list: false,
    detail: false
});

// 获取产品列表
const fetchList = async () => {
    loading.value.list = true;
    try {
        const res = await getInsurancePoster({
            pageNum: currentPage.value,
            pageSize: pageSize.value,
            search: searchKeyword.value
        });

        if (res) {
            list.value = res.records || [];
            total.value = res.total || 0;
        } else {
            message.error('获取产品列表失败');
        }
    } catch (error) {
        console.error('获取产品列表出错:', error);
        message.error('获取产品列表出错');
    } finally {
        loading.value.list = false;
    }
};

// 获取产品详情
const fetchDetail = async (id) => {
    loading.value.detail = true;
    try {
        const res = await getInsurancePosterDetails(id);

        if (res) {
            detail.value = res || {};
            showDetail.value = true;
        } else {
            message.error('获取产品详情失败');
        }
    } catch (error) {
        console.error('获取产品详情出错:', error);
        message.error('获取产品详情出错');
    } finally {
        loading.value.detail = false;
    }
};

// 处理搜索
const handleSearch = (keyword) => {
    searchKeyword.value = keyword;
    currentPage.value = 1;
    fetchList();
};

// 处理页码变化
const handlePageChange = (page) => {
    currentPage.value = page;
    fetchList();
};

// 处理每页条数变化
const handleSizeChange = (size) => {
    pageSize.value = size;
    currentPage.value = 1;
    fetchList();
};

// 处理选择产品
const handleSelectProduct = (product) => {
    fetchDetail(product.id);
};

// 打开海报模板选择
const openPosterTemplates = () => {
    templatesVisible.value = true;
};

// 处理模板确认
const handleTemplateConfirm = (data) => {
    selectedTemplate.value = data.templateData;
    templatesVisible.value = false;
    previewVisible.value = true;
};

// 组件挂载时获取产品列表
onMounted(() => {
    fetchList();
});
</script>

<style scoped>
.container {
    min-height: 100vh;
    background-color: #f0f2f5;
}

.title-section {
    background-size: 200% 200%;
    animation: gradientAnimation 5s ease infinite;
}

@keyframes gradientAnimation {
    0% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

.page-title {
    color: #fff;
}

.page-description {
    color: #e0e0e0;
}

.content-container {
    padding: 0;
}

.product-list-view,
.product-detail-view {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    animation: fadeIn 0.5s;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@media (max-width: 640px) {
    .container {
        padding: 0.5rem;
    }
}

.stat-card {
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.search-section .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}
</style>