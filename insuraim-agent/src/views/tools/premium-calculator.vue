<template>
  <div class="container mx-auto py-6 px-4">
    <!-- 页面标题 -->
    <page-header />

    <!-- 搜索区域 -->
    <!-- <search-bar :initial-value="searchParams.search" :product-count="pagination.total" :product-types="productTypes"
      :selected-type-value="selectedType" @search="handleSearch" @reset="resetSearch" @type-change="handleTypeChange"
      class="mb-2" /> -->

    <!-- 数据概览卡片 -->
    <!-- <stat-cards :product-count="products.length" :avg-premium="averagePremium" :max-premium="maxPremium"
      :min-premium="minPremium" /> -->

    <!-- 计算区域 -->
    <calculation-section :selected-product="selectedProduct" :loading="calculationLoading" :result="calculationResult"
      :error="calculationError" :products-data="products" :loading-products="productsLoading"
      :product-types="productTypes" :selected-type-value="selectedType" @calculate="calculatePremium"
      @product-search="handleSearch" @product-select="selectProductForCalculation" @type-change="handleTypeChange"
      @company-change="handleCompanyChange" @region-change="handleRegionChange" />

    <!-- 产品表格 -->
    <product-table :data-source="products" :loading="productsLoading" :total="pagination.total"
      v-model:pagination="pagination" @change="handleTableChange" @calculate="selectProductForCalculation"
      v-show="false" />
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';
import { productAPI } from '@/api';
import { message } from 'ant-design-vue';
import { toTraditional } from '@/utils/chineseConverter';
// 导入组件
import {
  PageHeader,
  SearchBar,
  StatCards,
  ProductTable,
  CalculationSection
} from './components/premium-calculator';

// 初始化国际化和路由
const { t } = useI18n();
const route = useRoute();

// 产品类型列表
const productTypes = ref([]);
const productsLoading = ref(false);
const selectedType = ref('');

// 产品列表
const products = ref([]);
const searchParams = reactive({
  pageNum: 1,
  pageSize: 100,
  search: '',
  type: '',
  insurer: '',
  region: '',
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 100,
  total: 0,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50', '100'],
  showTotal: (total) => `共 ${total} 条记录`,
});

// 计算相关
const selectedProduct = ref(null);
const calculationLoading = ref(false);
const calculationResult = ref(null);
const calculationError = ref(false);
const calculationFormData = ref({});

// 计算统计数据
const averagePremium = computed(() => {
  // 这里可以根据实际需求返回平均保费，默认为示例值
  return 5000;
});

const maxPremium = computed(() => {
  // 这里可以根据实际需求返回最高保费，默认为示例值
  return 10000;
});

const minPremium = computed(() => {
  // 这里可以根据实际需求返回最低保费，默认为示例值
  return 1000;
});

// 获取产品类型列表
const getProductTypes = async () => {
  try {
    const res = await productAPI.getChinaLifeProductCalculationType();
    if (res && res !== undefined) {
      productTypes.value = res.map(item => {
        return {
          value: item.code,
          label: item.name,
          code: item.code
        }
      });
    }
    return res;
  } catch (error) {
    console.error('获取产品类型失败:', error);
    message.error(t('premiumCalculator.error'));
    return null;
  }
};

// 加载产品列表
const loadProducts = async () => {
  productsLoading.value = true;

  try {
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize
    };

    // 添加搜索关键词
    if (searchParams.search) {
      params.search = searchParams.search;
    }

    // 添加产品类型筛选
    if (selectedType.value !== '') {
      params.type = selectedType.value;
    }

    // 添加公司筛选
    if (searchParams.insurer) {
      params.insurer = searchParams.insurer;
    }

    // 添加地区筛选
    if (searchParams.region) {
      params.region = searchParams.region;
    }

    const res = await productAPI.getChinaLifeProductCalculationProducts(params);

    if (res && res.records) {
      products.value = res.records || [];
      pagination.total = res.total || 0;
    } else {
      products.value = [];
      pagination.total = 0;
    }

    return res;
  } catch (error) {
    console.error('获取产品列表失败:', error);
    message.error(t('premiumCalculator.error'));
    products.value = [];
    pagination.total = 0;
    return null;
  } finally {
    productsLoading.value = false;
  }
};

// 处理搜索
const handleSearch = (value) => {
  searchParams.search = value;
  pagination.current = 1; // 重置到第一页
  loadProducts();
};

// 处理类型变化
const handleTypeChange = (type) => {
  selectedType.value = type;

  pagination.current = 1; // 重置到第一页
  loadProducts();
};

// 处理地区变化
const handleRegionChange = (region) => {
  // 设置搜索参数中的地区
  searchParams.region = region;
  pagination.current = 1; // 重置到第一页
  loadProducts();
};

// 重置搜索
const resetSearch = () => {
  searchParams.search = '';
  searchParams.company = '';
  searchParams.region = '';
  selectedType.value = 'all';
  pagination.current = 1; // 重置到第一页
  loadProducts();
};

// 处理表格变化
const handleTableChange = ({ pagination: newPagination, filters, sorter }) => {
  pagination.current = newPagination.current;
  pagination.pageSize = newPagination.pageSize;

  // 处理排序
  if (sorter && sorter.field) {
    searchParams.sortField = sorter.field;
    searchParams.sortOrder = sorter.order === 'ascend' ? 'asc' : 'desc';
  } else {
    searchParams.sortField = undefined;
    searchParams.sortOrder = undefined;
  }

  loadProducts();
};

// 选择产品进行计算
const selectProductForCalculation = (product) => {
  selectedProduct.value = product;
  calculationFormData.value = { ...calculationFormData.value, id: product.id };
  calculationResult.value = null; // 清空之前的计算结果
  calculationError.value = false;
};

// 计算保费
const calculatePremium = async (formData) => {
  calculationLoading.value = true;
  calculationError.value = false;
  calculationFormData.value = { ...formData, id: selectedProduct.value.id };
  try {
    const res = await productAPI.getChinaLifeProductCalculationFactors(calculationFormData.value);
    if (res !== '') {
      calculationResult.value = res;
    } else {
      calculationError.value = true;
    }
  } catch (error) {
    console.error('计算保费失败:', error);
    calculationError.value = true;
    message.error(t('premiumCalculator.error'));
  } finally {
    calculationLoading.value = false;
  }
};

// 自动搜索并计算
const autoSearchAndCalculate = async (productName, productId) => {
  try {
    // 设置搜索关键词
    searchParams.search = productName;

    // 重置页码
    pagination.current = 1;

    // 增加页面大小以提高找到产品的可能性
    const originalPageSize = pagination.pageSize;
    pagination.pageSize = 20;

    // 执行搜索
    productsLoading.value = true;

    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      search: searchParams.search
    };

    // 添加产品类型筛选
    if (selectedType.value !== 'all') {
      params.type = selectedType.value;
    }

    // 添加地区筛选
    if (searchParams.region) {
      params.region = searchParams.region;
    }

    const res = await productAPI.getChinaLifeProductCalculationProducts(params);

    if (res && res.records) {
      products.value = res.records || [];
      pagination.total = res.total || 0;

      // 查找匹配的产品
      const matchedProduct = products.value.find(p => String(p.id) === String(productId));

      // 如果找到匹配的产品，自动执行计算
      if (matchedProduct) {
        selectProductForCalculation(matchedProduct);
      }
    }

    // 恢复原始页面大小
    pagination.pageSize = originalPageSize;
  } catch (error) {
    console.error('自动搜索产品失败:', error);
    // 不显示错误提示，静默失败
  } finally {
    productsLoading.value = false;
  }
};

// 处理公司变化
const handleCompanyChange = (company) => {
  // 设置搜索参数中的公司
  if (company) {
    searchParams.insurer = toTraditional(company);
  } else {
    searchParams.insurer = '';
  }
  pagination.current = 1; // 重置到第一页
  loadProducts();
};

// 页面加载时初始化数据
onMounted(async () => {
  await getProductTypes();

  // 检查URL参数
  const { productName, productId } = route.query;

  if (productName && productId) {
    // 如果存在产品名称和ID参数，执行自动搜索和计算
    await autoSearchAndCalculate(productName, productId);
  } else {
    // 否则正常加载产品列表
    await loadProducts();
    // 默认选择第一条产品
    if (products.value.length > 0) {
      selectProductForCalculation(products.value[0]);
      handleTypeChange(products.value[0].type);
    }
  }
});
</script>

<style scoped>
@media (max-width: 640px) {
  .container {
    padding: 0.5rem;
  }
}
</style>