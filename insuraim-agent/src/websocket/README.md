# WebSocket 实时消息通知

本模块提供了WebSocket实时消息通知功能，用于替代原有的轮询机制。

## 目录结构

```
src/websocket/
├── index.js                  # 导出所有WebSocket相关功能
├── WebSocketService.js       # WebSocket连接管理
├── WebSocketStore.js         # WebSocket状态存储
├── WebSocketMessageHandler.js # 消息处理
└── useWebSocket.js           # Vue Composition API钩子
```

## 使用方法

### 1. 在组件中使用WebSocket

```vue
<script setup>
import { useWebSocket } from '@/websocket';

// 初始化WebSocket连接
const { connected, unreadCount, resetUnreadCount } = useWebSocket();

// 可以在模板中使用connected和unreadCount
</script>

<template>
  <div>
    <span>WebSocket状态: {{ connected ? '已连接' : '未连接' }}</span>
    <span>未读消息: {{ unreadCount }}</span>
    <button @click="resetUnreadCount">清除未读</button>
  </div>
</template>
```

### 2. 手动控制WebSocket连接

```javascript
import { WebSocketService } from '@/websocket';

// 手动连接
WebSocketService.connect()
  .then(() => {
    console.log('WebSocket连接成功');
  })
  .catch((error) => {
    console.error('WebSocket连接失败:', error);
  });

// 手动断开连接
WebSocketService.disconnect();

// 检查连接状态
const isConnected = WebSocketService.isConnected();
```

### 3. 访问WebSocket状态

```javascript
import { WebSocketStore } from '@/websocket';

// 获取连接状态
const connected = WebSocketStore.connected.value;

// 获取未读消息数量
const unreadCount = WebSocketStore.unreadCount.value;

// 获取最新消息
const latestMessage = WebSocketStore.latestMessage.value;

// 获取最新广播
const latestBroadcast = WebSocketStore.latestBroadcast.value;
```

## 消息格式

### 个人消息

```json
{
  "type": "BIZ_MESSAGE",
  "payload": {
    "id": 123,
    "userId": 456,
    "title": "消息标题",
    "content": "消息内容",
    "messageType": 1,
    "status": 0,
    "isPush": 0,
    "isImportant": 0,
    "createdAt": 1636123456789
  },
  "timestamp": 1636123456789
}
```

### 广播消息

```json
{
  "type": "BROADCAST",
  "payload": {
    "title": "广播标题",
    "content": "广播内容",
    "type": 1,
    "timestamp": 1636123456789
  },
  "timestamp": 1636123456789
}
``` 