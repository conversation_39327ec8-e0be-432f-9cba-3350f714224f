/**
 * WebSocket消息处理器
 * 
 * 负责处理接收到的WebSocket消息
 */

import { notification } from 'ant-design-vue';
import { pushMessage } from '../api/message';

class WebSocketMessageHandler {
  /**
   * 处理个人消息
   * @param {object} message 消息对象
   */
  handlePersonalMessage(message) {
    if (!message || !message.payload) {
      console.error('无效的消息格式:', message);
      return;
    }

    // 显示消息通知
    this.showNotification(message);

    // 更新消息为已推送
    this.updateMessagePushed(message.payload.id);

    // 延迟2秒后刷新页面
    setTimeout(() => {
      // 判断当前url，如果在我的计划页面则刷新
      const currentUrl = window.location.href;
      if (currentUrl.includes('/my-plans')) {
        window.location.reload();
      }
    }, 2000);
  }

  /**
   * 处理广播消息
   * @param {object} message 广播消息对象
   */
  handleBroadcastMessage(message) {
    if (!message || !message.payload) {
      console.error('无效的广播消息格式:', message);
      return;
    }

    // 显示广播通知
    this.showBroadcastNotification(message);
  }

  /**
   * 显示消息通知
   * @param {object} message 消息对象
   */
  showNotification(message) {
    const { payload } = message;

    notification.info({
      message: `${payload.messageType === 1 ? '系统消息' : '业务消息'}`,
      description: `${payload.title}`,
      placement: 'topRight',
      onClick: () => {
        window.location.href = '/message-center';
      }
    });
  }

  /**
   * 显示广播通知
   * @param {object} message 广播消息对象
   */
  showBroadcastNotification(message) {
    const { payload } = message;

    notification.info({
      message: `系统广播`,
      description: `${payload.title}`,
      placement: 'topRight'
    });
  }

  /**
   * 更新消息为已推送
   * @param {number} messageId 消息ID
   */
  async updateMessagePushed(messageId) {
    try {
      await pushMessage(messageId);
    } catch (error) {
      console.error('更新消息推送状态失败:', error);
    }
  }
}

export default WebSocketMessageHandler; 