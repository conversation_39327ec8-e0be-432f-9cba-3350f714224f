/**
 * WebSocket服务
 * 
 * 负责创建和管理WebSocket连接
 */

import SockJS from 'sockjs-client/dist/sockjs';
import { Client } from '@stomp/stompjs';
import { getBaseUrl } from '../utils/request';
import WebSocketMessageHandler from './WebSocketMessageHandler';

/**
 * 判断是否为不需要WebSocket连接的路由
 * @param {string} path 路由路径
 * @returns {boolean} 如果是不需要WebSocket的路由则返回true
 */
const isNoWebSocketRoute = (path) => {
  // 不需要WebSocket连接的路由列表
  const noWebSocketRoutes = [
    '/login',
    '/register',
    '/form/invite',
    '/form/success',
    '/404'
  ];

  // 检查路径是否包含不需要WebSocket的路由
  return noWebSocketRoutes.some(route =>
    path.includes(route) || path.startsWith(route + '/')
  );
};

class WebSocketService {
  constructor() {
    this.stompClient = null;
    this.connected = false;
    this.messageHandler = new WebSocketMessageHandler();
  }

  /**
   * 检查当前路由是否需要WebSocket连接
   * @returns {boolean} 如果当前路由需要WebSocket连接则返回true
   */
  checkCurrentRoute() {
    // 获取当前路径
    const currentPath = window.location.pathname;
    console.log('当前路径:', currentPath);

    // 检查是否为不需要WebSocket的路由
    const isNoWebSocket = isNoWebSocketRoute(currentPath);
    console.log('是否为不需要WebSocket的路由:', isNoWebSocket);

    // 返回是否需要WebSocket连接
    return !isNoWebSocket;
  }

  /**
   * 创建WebSocket连接
   * @returns {Promise} 连接成功的Promise
   */
  connect() {
    return new Promise((resolve, reject) => {
      // 检查当前路由是否需要WebSocket连接
      if (!this.checkCurrentRoute()) {
        console.log('当前页面不需要WebSocket连接');
        // 如果已连接，则断开连接
        if (this.connected) {
          this.disconnect();
        }
        reject(new Error('当前页面不需要WebSocket连接'));
        return;
      }

      if (this.connected && this.stompClient) {
        resolve(this.stompClient);
        return;
      }

      // 获取认证令牌
      const token = localStorage.getItem('token');
      if (!token) {
        reject(new Error('未登录，无法建立WebSocket连接'));
        return;
      }

      // 获取API基础URL
      const baseUrl = getBaseUrl().replace('/api', '');

      // 创建STOMP客户端
      this.stompClient = new Client({
        // 创建WebSocket连接
        webSocketFactory: () => new SockJS(`${baseUrl}/ws`),

        // 连接头信息，包含认证令牌
        connectHeaders: {
          Authorization: `Bearer ${token}`
        },

        // 调试日志
        debug: (process.env.NODE_ENV === 'development') ?
          (str) => console.log('STOMP: ' + str) :
          () => { },

        // 重连延迟（毫秒）
        reconnectDelay: 5000,

        // 心跳间隔（毫秒）
        heartbeatIncoming: 4000,
        heartbeatOutgoing: 4000
      });

      // 连接成功回调
      this.stompClient.onConnect = (frame) => {
        console.log('WebSocket连接成功');
        this.connected = true;

        // 订阅个人消息
        this.stompClient.subscribe('/user/queue/messages', (message) => {
          try {
            const messageData = JSON.parse(message.body);
            console.log('收到个人消息:', messageData);

            // 处理消息
            this.messageHandler.handlePersonalMessage(messageData);
          } catch (error) {
            console.error('解析消息失败:', error);
          }
        });

        // 订阅广播消息
        this.stompClient.subscribe('/topic/broadcast', (message) => {
          try {
            const messageData = JSON.parse(message.body);
            console.log('收到广播消息:', messageData);

            // 处理广播
            this.messageHandler.handleBroadcastMessage(messageData);
          } catch (error) {
            console.error('解析广播消息失败:', error);
          }
        });

        resolve(this.stompClient);
      };

      // 连接错误回调
      this.stompClient.onStompError = (frame) => {
        console.error('WebSocket连接错误: ' + frame.headers['message']);
        console.error('错误详情: ' + frame.body);
        this.connected = false;
        reject(new Error('WebSocket连接错误'));
      };

      // 激活连接
      this.stompClient.activate();
    });
  }

  /**
   * 断开WebSocket连接
   */
  disconnect() {
    if (this.stompClient && this.connected) {
      this.stompClient.deactivate();
      this.stompClient = null;
      this.connected = false;
      console.log('WebSocket连接已断开');
    }
  }

  /**
   * 获取连接状态
   * @returns {boolean} 是否已连接
   */
  isConnected() {
    return this.connected;
  }

  /**
   * 获取STOMP客户端实例
   * @returns {Object} STOMP客户端实例
   */
  getClient() {
    return this.stompClient;
  }
}

// 导出单例
export default new WebSocketService(); 