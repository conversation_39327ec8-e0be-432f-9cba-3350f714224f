/**
 * WebSocket Composition API钩子
 * 
 * 在Vue组件中使用WebSocket功能
 */

import { onMounted, onUnmounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import WebSocketService from './WebSocketService';

/**
 * 判断是否为不需要WebSocket连接的路由
 * @param {string} path 路由路径
 * @returns {boolean} 如果是不需要WebSocket的路由则返回true
 */
const isNoWebSocketRoute = (path) => {
  // 不需要WebSocket连接的路由列表
  const noWebSocketRoutes = [
    '/login',
    '/register',
    '/form/invite',
    '/form/success',
    '/404'
  ];

  // 检查路径是否匹配或包含不需要WebSocket的路由
  return noWebSocketRoutes.some(route =>
    path.includes(route) || path.startsWith(route + '/')
  );
};

/**
 * WebSocket钩子
 * @returns {object} WebSocket相关状态和方法
 */
export function useWebSocket() {
  // 连接状态
  const connected = ref(false);
  // 获取当前路由
  const route = useRoute();

  /**
   * 检查当前路由并决定是否连接WebSocket
   */
  const checkRouteAndConnect = () => {
    console.log('检查当前路由并决定是否连接WebSocket', isNoWebSocketRoute(route.path));
    // 检查是否为不需要WebSocket的路由
    if (isNoWebSocketRoute(route.path)) {
      // 如果已连接，则断开连接
      disconnectWebSocket();
      return;
    }

    // 检查是否已登录
    const token = localStorage.getItem('token');
    if (token && !WebSocketService.isConnected()) {
      connectWebSocket();
    }
  };

  // 在组件挂载时检查路由并决定是否连接WebSocket
  onMounted(() => {
    checkRouteAndConnect();
  });

  // 监听路由变化
  watch(() => route.path, (newPath) => {
    console.log('路由变化:', newPath);
    checkRouteAndConnect();
  });

  // 在组件卸载时断开WebSocket连接
  onUnmounted(() => {
    disconnectWebSocket();
  });

  /**
   * 连接WebSocket
   * @returns {Promise} 连接成功的Promise
   */
  const connectWebSocket = async () => {
    try {
      await WebSocketService.connect();
      connected.value = true;
      return true;
    } catch (error) {
      console.error('WebSocket连接失败:', error);
      connected.value = false;
      return false;
    }
  };

  /**
   * 断开WebSocket连接
   */
  const disconnectWebSocket = () => {
    WebSocketService.disconnect();
    connected.value = false;
  };

  /**
   * 重新连接WebSocket
   * @returns {Promise} 连接成功的Promise
   */
  const reconnectWebSocket = async () => {
    disconnectWebSocket();
    return connectWebSocket();
  };

  return {
    // 状态
    connected,

    // 方法
    connect: connectWebSocket,
    disconnect: disconnectWebSocket,
    reconnect: reconnectWebSocket
  };
}

export default useWebSocket; 