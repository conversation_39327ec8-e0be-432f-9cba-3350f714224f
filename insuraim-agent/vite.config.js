import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'
import tailwindcss from '@tailwindcss/vite'

// https://vitejs.dev/config/
export default defineConfig({
  server: {
    port: 5174,
    open: true,
  },
  plugins: [
    vue(),
    tailwindcss(),
    AutoImport({
      resolvers: [AntDesignVueResolver()],
      imports: ['vue', 'vue-router', 'vue-i18n'],
      // 自动导入自定义组合式API
      dirs: ['src/I18n/composables'],
      // 为true时会自动生成声明文件，生成环境下关闭
      dts: process.env.NODE_ENV !== 'production',
      // 全局引入的API前缀
      vueTemplate: true,
    }),
    Components({
      resolvers: [
        AntDesignVueResolver({
          importStyle: false, // 使用全局导入的样式，不单独导入组件样式
        }),
      ],
      // 自动导入自定义组件
      dirs: ['src/components'],
      // 为true时会自动生成声明文件，生成环境下关闭
      dts: process.env.NODE_ENV !== 'production',
    }),
  ],
  resolve: {
    alias: {
      '@': '/src'
    }
  }
})

