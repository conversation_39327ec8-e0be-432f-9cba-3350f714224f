# FROM alpine
FROM ccr.ccs.tencentyun.com/common_container/playwright:1.50

ENV SPRING_HOME=/spring
ENV DEPLOY_DIR=/spring/server

WORKDIR $SPRING_HOME
COPY . $SPRING_HOME/target

EXPOSE 9100 8250

RUN  mkdir -p $DEPLOY_DIR \
    && mv ./target/distribution/Insurance-api-0.0.1-SNAPSHOT/* ./server \
    && mv ./target/Dockerfile ./ \
    && rm -rf target  \
    && apt update 
    # && apk add openjdk17-jre && apk add bash && apk add fontconfig

ENTRYPOINT ["bash", "-c", "/spring/server/bin/server.sh start-debug"]