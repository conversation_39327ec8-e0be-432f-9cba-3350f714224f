# 保险管理系统 (baoxian)

## 项目概述
保险管理系统是一个基于Spring Boot和Vue的全栈应用，提供保险产品管理、客户管理、保单管理等功能。

## 技术架构

### 后端技术栈 (baoxian)
- 核心框架: Spring Boot 3.4.4 (Java 21)
- 持久层: MyBatis-Plus 3.5.11
- 数据库: MySQL 8.3.0
- 安全认证: Sa-Token 1.41.0
- 文档处理: 
  - POI 5.2.3 (Excel/Word)
  - Docx4j 11.5.0 (Word)
  - iTextPDF 5.5.13.3 (PDF)
- 工具库: 
  - Hutool 5.8.16
  - Lombok

### 前端技术栈 (baoxian-vue)
- 核心框架: Vue 3.5.13 + Vite 6.2.0
- UI组件: Ant Design Vue 4.2.6
- 路由: Vue Router 4.5.0
- 图表: ECharts 5.4.3
- HTTP客户端: Axios 1.8.4
- CSS框架: TailwindCSS 4.0.17
- 图标库: Iconify 4.3.0

## 项目结构
```
baoxian/          # 后端项目
├── src/
│   ├── main/
│   │   ├── java/com/sub/baoxian/  # Java源代码
│   │   └── resources/             # 配置文件
│   └── test/                      # 测试代码
└── pom.xml                        # Maven配置

baoxian-vue/      # 前端项目
├── src/
│   ├── views/     # 页面组件
│   ├── router/    # 路由配置
│   └── components # 公共组件
└── package.json   # 前端依赖
```

## 开发环境准备
1. 后端:
   - JDK 21
   - MySQL 8.3+
   - Maven 3.9+

2. 前端:
   - Node.js 18+
   - npm/yarn

## 启动说明
1. 后端启动:
```bash
cd baoxian
mvn spring-boot:run
```

2. 前端启动:
```bash
cd baoxian-vue
npm install
npm run dev
