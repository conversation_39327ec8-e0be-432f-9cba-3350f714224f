# InsurAim API 接口文档

## 目录

- [后台管理接口](#后台管理接口)
  - [管理员管理](#管理员管理)
  - [分类管理](#分类管理)
  - [菜单管理](#菜单管理)
  - [组织管理](#组织管理)
  - [权限管理](#权限管理)
  - [角色管理](#角色管理)
  - [用户管理](#用户管理)
- [通用接口](#通用接口)
  - [保险公司](#保险公司)
  - [消息管理](#消息管理)
  - [产品管理](#产品管理)
- [前台接口](#前台接口)
  - [日程管理](#日程管理)
  - [客户管理](#客户管理)
  - [考试管理](#考试管理)
  - [文件管理](#文件管理)
  - [文件夹管理](#文件夹管理)
  - [资讯管理](#资讯管理)
  - [知识库管理](#知识库管理)
  - [产品比较](#产品比较)
  - [保险计划书管理](#保险计划书管理)
  - [调查问卷](#调查问卷)
  - [文件上传与分析](#文件上传与分析)
  - [前台用户管理](#前台用户管理)

## 后台管理接口

### 管理员管理

基础路径: `/api/backend/admin`

| 方法   | 路径                       | 说明                 | 参数                                                   | 权限                     |
|------|--------------------------|--------------------|----------------------------------------------------|------------------------|
| POST | /login                   | 管理员登录              | AdminLoginDTO (body)                               | 无                      |
| GET  | /info                    | 获取当前登录管理员信息        | 无                                                  | 需登录                    |
| POST | /logout                  | 管理员登出              | 无                                                  | 无                      |
| GET  | /checkLogin              | 检查是否登录             | 无                                                  | 无                      |
| GET  | /page                    | 分页查询管理员列表          | pageNum, pageSize, keyword, status                 | admin:page             |
| POST | /                        | 新增管理员              | Admin (body)                                       | admin:add              |
| PUT  | /                        | 修改管理员              | Admin (body)                                       | admin:update           |
| DELETE | /{id}                 | 删除管理员              | id (path)                                          | admin:delete           |
| DELETE | /batch                 | 批量删除管理员            | ids (body)                                         | admin:delete           |
| PUT  | /status/{id}             | 修改管理员状态            | id (path), status (body)                           | admin:updateStatus     |
| PUT  | /resetPassword/{id}      | 重置管理员密码            | id (path), newPassword (body)                      | admin:resetPassword    |
| GET  | /{id}                    | 获取管理员详情            | id (path)                                          | admin:query            |

### 分类管理

基础路径: `/api/backend/category`

| 方法   | 路径                    | 说明        | 参数                                     | 权限              |
|------|------------------------|-----------|----------------------------------------|-----------------|
| GET  | /page                  | 分页查询分类列表  | pageNum, pageSize, keyword             | category:page   |
| GET  | /list                  | 获取所有分类列表  | 无                                      | category:list   |
| GET  | /{id}                  | 获取分类详情    | id (path)                              | category:query  |
| POST | /                      | 新增分类      | Categories (body)                      | category:add    |
| PUT  | /                      | 修改分类      | Categories (body)                      | category:update |
| DELETE | /{id}                | 删除分类      | id (path)                              | category:delete |
| DELETE | /batch               | 批量删除分类    | ids (body)                             | category:delete |

### 菜单管理

基础路径: `/api/backend/menu`

| 方法   | 路径                    | 说明        | 参数                                     | 权限              |
|------|------------------------|-----------|----------------------------------------|-----------------|
| GET  | /current               | 获取当前用户菜单  | 无                                      | menu:query      |
| POST | /                      | 创建新菜单     | Menu (body)                            | menu:add        |
| DELETE | /{id}                | 删除菜单      | id (path)                              | menu:delete     |
| PUT  | /                      | 更新菜单      | Menu (body)                            | menu:update     |
| GET  | /{id}                  | 获取菜单详情    | id (path)                              | menu:query      |
| GET  | /list                  | 获取所有菜单列表  | 无                                      | menu:list       |

### 组织管理

基础路径: `/api/backend/organization`

| 方法   | 路径                    | 说明        | 参数                                            | 权限           |
|------|------------------------|-----------|-----------------------------------------------|--------------|
| GET  | /page                  | 分页查询组织列表  | pageNum, pageSize, keyword, type, status      | org:page     |
| GET  | /list/{type}           | 按类型获取组织列表 | type (path)                                   | org:list     |
| GET  | /{id}                  | 获取组织详情    | id (path)                                     | org:query    |
| POST | /                      | 新增组织      | Organization (body)                           | org:add      |
| PUT  | /                      | 修改组织      | Organization (body)                           | org:update   |
| DELETE | /{id}                | 删除组织      | id (path)                                     | org:delete   |
| DELETE | /batch               | 批量删除组织    | ids (body)                                    | org:delete   |

### 权限管理

基础路径: `/api/backend/permission`

| 方法   | 路径                    | 说明         | 参数                                     | 权限                |
|------|------------------------|------------|----------------------------------------|-------------------|
| GET  | /page                  | 分页查询权限列表   | pageNum, pageSize, keyword             | permission:page   |
| GET  | /list                  | 获取所有权限列表   | 无                                      | permission:list   |
| GET  | /{id}                  | 获取权限详情     | id (path)                              | permission:query  |
| POST | /                      | 新增权限       | Permission (body)                      | permission:add    |
| PUT  | /                      | 修改权限       | Permission (body)                      | permission:update |
| DELETE | /{id}                | 删除权限       | id (path)                              | permission:delete |
| DELETE | /batch               | 批量删除权限     | ids (body)                             | permission:delete |
| GET  | /role/{roleId}         | 获取角色的权限列表  | roleId (path)                           | permission:query  |
| POST | /role/{roleId}         | 为角色分配权限    | roleId (path), permissionIds (body)    | permission:assign |

### 角色管理

基础路径: `/api/backend/role`

| 方法   | 路径                    | 说明        | 参数                                     | 权限            |
|------|------------------------|-----------|----------------------------------------|---------------|
| GET  | /page                  | 分页查询角色列表  | pageNum, pageSize, keyword             | role:page     |
| GET  | /list                  | 获取所有角色列表  | 无                                      | role:list     |
| GET  | /{id}                  | 获取角色详情    | id (path)                              | role:query    |
| POST | /                      | 新增角色      | Role (body)                            | role:add      |
| PUT  | /                      | 修改角色      | Role (body)                            | role:update   |
| DELETE | /{id}                | 删除角色      | id (path)                              | role:delete   |
| DELETE | /batch               | 批量删除角色    | ids (body)                             | role:delete   |

### 用户管理

基础路径: `/api/backend/user`

| 方法   | 路径                    | 说明        | 参数                                                | 权限                 |
|------|------------------------|-----------|---------------------------------------------------|----------------------|
| GET  | /page                  | 分页查询用户列表  | pageNum, pageSize, keyword, roleId, username, status | user:page            |
| POST | /                      | 新增用户      | User (body)                                       | user:add             |
| PUT  | /                      | 修改用户      | User (body)                                       | user:update          |
| DELETE | /{id}                | 删除用户      | id (path)                                         | user:delete          |
| DELETE | /batch               | 批量删除用户    | ids (body)                                        | user:delete          |
| PUT  | /resetPassword/{id}    | 重置用户密码    | id (path), newPassword (body)                     | user:resetPassword   |
| PUT  | /status/{id}           | 修改用户状态    | id (path), status (body)                          | user:updateStatus    |

## 通用接口

### 保险公司

基础路径: `/api/common/company`

| 方法   | 路径                   | 说明         | 参数                                        | 权限  |
|------|-----------------------|------------|-------------------------------------------|-----|
| GET  | /list                 | 分页查询保险公司列表 | pageNum, pageSize, name, code, region     | 无   |
| GET  | /{code}               | 获取保险公司详情   | code (path)                               | 无   |
| POST | /                     | 创建保险公司     | Insurers (body)                           | 无   |
| PUT  | /{id}                 | 更新保险公司     | id (path), Insurers (body)                | 无   |
| DELETE | /{id}               | 删除保险公司     | id (path)                                 | 无   |

### 消息管理

基础路径: `/api/common/message`

| 方法   | 路径                    | 说明          | 参数                           | 权限  |
|------|------------------------|-------------|------------------------------|-----|
| GET  | /poll                  | 轮询获取未推送信息   | 无                            | 无   |
| POST | /push                  | 更新消息为已推送    | messageId                    | 无   |
| POST | /read                  | 更新消息为已读状态   | messageId                    | 无   |
| GET  | /all                   | 获取所有信息列表    | 无                            | 无   |
| GET  | /unread                | 获取所有未读消息    | 无                            | 无   |
| GET  | /paged                 | 分页获取消息列表    | page, pageSize               | 无   |

### 产品管理

基础路径: `/api/common/product`

| 方法   | 路径                       | 说明               | 参数                                             | 权限  |
|------|--------------------------|------------------|-------------------------------------------------|-----|
| GET  | /list                    | 分页查询产品列表         | pageNum, pageSize, categoryCode, search, region, comparable | 无   |
| GET  | /{code}                  | 获取产品详情           | code (path)                                      | 无   |
| GET  | /plan-book-list          | 获取可制作计划书产品列表     | 无                                               | 无   |
| GET  | /china-life-product-list | 获取中国人寿产品列表       | 无                                               | 无   |

## 前台接口

### 日程管理

基础路径: `/api/front/calendar`

| 方法   | 路径                    | 说明       | 参数                               | 权限  |
|------|------------------------|----------|----------------------------------|-----|
| GET  | /list                  | 查询日程列表   | 无                                | 无   |
| GET  | /{id}                  | 获取日程详情   | id (path)                        | 无   |
| POST | /create                | 创建日程     | CalendarDTO (body)               | 无   |
| PUT  | /update                | 更新日程     | CalendarDTO (body)               | 无   |
| DELETE | /{id}                | 删除日程     | id (path)                        | 无   |
| PUT  | /{id}/status           | 更新日程状态   | id (path), status                | 无   |

### 客户管理

基础路径: `/api/front/customer`

| 方法   | 路径                    | 说明       | 参数                               | 权限  |
|------|------------------------|----------|----------------------------------|-----|
| GET  | /page                  | 分页获取客户列表 | page, pageSize                    | 无   |
| GET  | /{id}                  | 获取客户详情   | id (path)                        | 无   |

### 考试管理

基础路径: `/api/front/exam`

| 方法   | 路径                    | 说明       | 参数                               | 权限  |
|------|------------------------|----------|----------------------------------|-----|
| GET  | /category              | 获取模拟考试类目 | 无                                | 无   |
| GET  | /question              | 获取模拟考试题目 | categoryId                       | 无   |

### 文件管理

基础路径: `/api/front/file`

| 方法   | 路径                    | 说明       | 参数                                         | 权限  |
|------|------------------------|----------|----------------------------------------------|-----|
| POST | /upload                | 上传文件     | file, folderId, uploaderId                  | 无   |
| GET  | /{id}                  | 获取文件信息   | id (path)                                   | 无   |
| GET  | /list                  | 获取文件夹中的文件列表 | folderId, fileType                           | 无   |
| GET  | /page                  | 分页查询文件    | page, size, folderId, keyword               | 无   |
| PUT  | /                      | 更新文件信息    | File (body)                                 | 无   |
| DELETE | /{id}                | 删除文件      | id (path)                                   | 无   |

### 文件夹管理

基础路径: `/api/front/folder`

| 方法   | 路径                    | 说明         | 参数                                         | 权限  |
|------|------------------------|------------|----------------------------------------------|-----|
| POST | /                      | 创建文件夹      | Folder (body)                               | 无   |
| GET  | /{id}                  | 获取文件夹信息    | id (path)                                   | 无   |
| GET  | /content/{id}          | 查询文件夹内容    | id (path), fileType                         | 无   |
| GET  | /tree                  | 获取文件夹树形结构  | parentId                                    | 无   |
| GET  | /list                  | 获取文件夹列表    | parentId                                    | 无   |
| GET  | /page                  | 分页查询文件夹    | page, size, parentId, keyword               | 无   |
| PUT  | /                      | 更新文件夹信息    | Folder (body)                               | 无   |
| DELETE | /{id}                | 删除文件夹      | id (path)                                   | 无   |

### 资讯管理

基础路径: `/api/front/information`

| 方法   | 路径                    | 说明         | 参数                         | 权限  |
|------|------------------------|------------|----------------------------|-----|
| GET  | /paged                 | 分页获取资讯列表   | page, pageSize             | 无   |

### 知识库管理

基础路径: `/api/front/knowledge-base`

| 方法   | 路径                     | 说明             | 参数                      | 权限  |
|------|-------------------------|----------------|-----------------------|-----|
| GET  | /company-list           | 获取知识库保险公司信息列表  | 无                       | 无   |
| GET  | /company-categories     | 获取知识库专题列表      | companyId               | 无   |
| GET  | /lore-list              | 根据分类ID获取知识库信息列表 | categoryId              | 无   |
| GET  | /lore-detail            | 获取知识库详情        | id                      | 无   |

### 产品比较

基础路径: `/api/front/product-compare`

*注意: 该控制器尚未实现具体接口*

### 保险计划书管理

基础路径: `/api/front/proposal`

| 方法   | 路径                    | 说明             | 参数                          | 权限  |
|------|------------------------|----------------|------------------------------|-----|
| POST | /generate              | 生成保险计划书        | ProposalRequestDTO (body)    | 无   |
| GET  | /page                  | 获取当前用户的计划书分页列表 | pageNum, pageSize            | 无   |
| GET  | /{id}                  | 获取计划书详情        | id (path)                    | 无   |
| GET  | /status/{id}           | 轮询计划书生成状态      | id (path)                    | 无   |

### 调查问卷

基础路径: `/api/front/survey`

*注意: 该控制器接口已注释，尚未实现*

### 文件上传与分析

基础路径: `/api/front/upload`

| 方法   | 路径                    | 说明           | 参数                          | 权限  |
|------|------------------------|--------------|------------------------------|-----|
| POST | /analyze               | 上传并分析保险PDF文件 | file                         | 无   |

### 前台用户管理

基础路径: `/api/front/user`

| 方法   | 路径                    | 说明       | 参数                               | 权限  |
|------|------------------------|----------|----------------------------------|-----|
| POST | /register              | 用户注册     | UserRegisterDTO (body)           | 无   |
| POST | /login                 | 用户登录     | UserLoginDTO (body)              | 无   |
| GET  | /info                  | 获取当前登录用户信息 | 无                                | 无   |
| POST | /logout                | 用户登出     | 无                                | 无   |
| GET  | /checkLogin            | 检查是否登录   | 无                                | 无   | 