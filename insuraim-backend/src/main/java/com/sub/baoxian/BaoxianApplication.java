package com.sub.baoxian;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableAsync
@SpringBootApplication
@MapperScan("com.sub.baoxian.mapper")
@EnableCaching
@EnableScheduling
public class BaoxianApplication {

    public static void main(String[] args) {
        SpringApplication.run(BaoxianApplication.class, args);
    }

}
