package com.sub.baoxian.common.config;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotPermissionException;
import io.swagger.v3.oas.annotations.Hidden;

import com.sub.baoxian.common.exception.BizException;
import com.sub.baoxian.common.response.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.apache.catalina.connector.ClientAbortException;

/**
 * 全局异常处理
 */
@Slf4j
@Hidden
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理Sa-Token未登录异常
     * 
     * @param e 未登录异常
     * @return 返回未登录信息
     */
    @ExceptionHandler(NotLoginException.class)
    public Result<Void> handleNotLoginException(NotLoginException e) {
        log.warn("未登录异常: {}", e.getMessage());

        String message;
        if (e.getType().equals(NotLoginException.NOT_TOKEN)) {
            message = "非法请求";
        } else if (e.getType().equals(NotLoginException.INVALID_TOKEN)) {
            message = "token无效";
        } else if (e.getType().equals(NotLoginException.TOKEN_TIMEOUT)) {
            message = "token已过期";
        } else if (e.getType().equals(NotLoginException.BE_REPLACED)) {
            message = "token已被顶下线";
        } else if (e.getType().equals(NotLoginException.KICK_OUT)) {
            message = "token已被踢下线";
        } else {
            message = "未登录";
        }

        return Result.error(401, message);
    }

    /**
     * 处理权限异常
     * 
     * @param e 权限异常
     * @return 返回错误信息
     */
    @ExceptionHandler(NotPermissionException.class)
    public Result<Void> handleNotPermissionException(NotPermissionException e) {
        log.warn("权限异常: {}", e.getMessage());
        return Result.error(403, e.getMessage());
    }

    /**
     * 处理业务异常
     * 
     * @param e 业务异常
     * @return 返回错误信息
     */
    @ExceptionHandler(BizException.class)
    public Result<Void> handleBizException(BizException e) {
        log.warn("业务异常: {}", e.getMessage(), e);
        return Result.error(e.getCode(), e.getMessage());
    }

    /**
     * 处理客户端中断连接异常
     * 当客户端主动断开连接或连接超时时发生
     * 
     * @param e 客户端中断连接异常
     * @return null，不需要返回任何内容给已断开的客户端
     */
    @ExceptionHandler(ClientAbortException.class)
    public Result<Void> handleClientAbortException(ClientAbortException e) {
        log.warn("客户端断开连接: {}", e.getMessage());
        // 这里不需要做任何操作，因为客户端已经断开了连接
        // 返回null或任何值都不会发送给客户端
        return null;
    }

    /**
     * 处理其他异常
     * 
     * @param e 异常
     * @return 返回错误信息
     */
    @ExceptionHandler(Exception.class)
    public Result<Void> handleException(Exception e) {
        log.error("系统异常", e);
        return Result.error("系统异常，请稍后重试");
    }
}