package com.sub.baoxian.common.config;

import com.baomidou.mybatisplus.autoconfigure.ConfigurationCustomizer;
import com.sub.baoxian.handler.JsonTypeHandler;
import com.sub.baoxian.productInfoService.BO.CalculationDataBO;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;

/*
 * MyBatis-Plus 配置
 */
@Configuration
public class MybatisPlusConfig {

    // @Autowired
    // private DataSource dataSource;

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        return interceptor;
    }
    @Bean
    public ConfigurationCustomizer mybatisConfigurationCustomizer() {
        return configuration -> {

            configuration.getTypeHandlerRegistry().register(CalculationDataBO.class, new JsonTypeHandler<>(CalculationDataBO.class));

            // You can register other type handlers here as well
            // configuration.getTypeHandlerRegistry().register(YourOtherClass.class, new YourOtherTypeHandler<>(YourOtherClass.class));
        };
    }

    // /**
    // * 自定义SqlSessionFactory配置，注册类型处理器
    // */
    // @Bean
    // public SqlSessionFactory sqlSessionFactory() throws Exception {
    // MybatisSqlSessionFactoryBean factoryBean = new
    // MybatisSqlSessionFactoryBean();
    // factoryBean.setDataSource(dataSource);

    // // 创建并配置MybatisConfiguration
    // MybatisConfiguration configuration = new MybatisConfiguration();

    // // 注册类型处理器
    // TypeHandlerRegistry registry = configuration.getTypeHandlerRegistry();
    // registry.register(List.class, JacksonTypeHandler.class);
    // registry.register(HashMap.class, JacksonTypeHandler.class);

    // // 设置配置
    // factoryBean.setConfiguration(configuration);

    // // 添加插件
    // factoryBean.setPlugins(mybatisPlusInterceptor());

    // return factoryBean.getObject();
    // }
}
