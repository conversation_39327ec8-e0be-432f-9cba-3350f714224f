package com.sub.baoxian.common.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaHttpMethod;
import cn.dev33.satoken.router.SaRouter;
import lombok.extern.slf4j.Slf4j;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.sub.baoxian.common.exception.BizException;
import com.sub.baoxian.util.StpKit;

/**
 * Sa-Token配置类
 */
@Configuration
@Slf4j
public class SaTokenConfig implements WebMvcConfigurer {

    /**
     * 注册Sa-Token拦截器
     * 
     * @param registry 拦截器注册表
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册Sa-Token拦截器
        registry.addInterceptor(new SaInterceptor(handle -> {
            // 排除OPTIONS请求
            SaRouter.match(SaHttpMethod.OPTIONS).stop();
            // 后端接口
            SaRouter.match("/api/backend/**") // 拦截所有/api/backend/**接口
                    .notMatch("/api/backend/admin/login") // 排除管理员登录接口
                    .notMatch("/api/backend/admin/checkLogin") // 排除管理员登录检查接口
                    .notMatch("/api/backend/admin/logout") // 排除管理员登出接口
                    .check(r -> {
                        StpKit.ADMIN.checkLogin();
                    });
            // 前端接口
            SaRouter.match("/api/front/**") // 拦截所有/api/front/**接口
                    .notMatch("/api/front/user/login") // 排除登录接口
                    .notMatch("/api/front/user/register") // 排除注册接口
                    .notMatch("/api/front/user/checkLogin") // 排除登录检查接口
                    .notMatch("/api/front/user/logout") // 排除登出接口
                    .check(r -> {
                        StpKit.USER.checkLogin();
                    });

            SaRouter.match("/api/common/**")
                    .check(r -> {
                        if (StpKit.USER.isLogin() == false && StpKit.ADMIN.isLogin() == false) {
                            throw new BizException(401, "请先登录");
                        }
                    });

        })).excludePathPatterns("/api/form/invite/**").addPathPatterns("/**");
    }
}