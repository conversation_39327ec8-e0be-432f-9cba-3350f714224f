package com.sub.baoxian.common.config;

import cn.dev33.satoken.stp.StpInterface;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sub.baoxian.mapper.AdminMapper;
import com.sub.baoxian.mapper.PermissionMapper;
import com.sub.baoxian.mapper.RolePermissionMapper;
import com.sub.baoxian.model.entity.Admin;
import com.sub.baoxian.model.entity.Permission;
import com.sub.baoxian.model.relation.RolePermission;

import java.util.ArrayList;
import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
public class StpInterfaceImpl implements StpInterface {

    private final RolePermissionMapper rolePermissionMapper;
    private final PermissionMapper permissionMapper;
    private final AdminMapper adminMapper;

    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        log.info("开始鉴权...loginId: {}, loginType: {}", loginId, loginType);

        Admin admin = adminMapper.selectById(Long.valueOf(loginId.toString()));
        if (admin == null) {
            return null;
        }

        Long roleId = admin.getRoleId();
        List<String> permissions = new ArrayList<>();
        List<RolePermission> rolePermissions = rolePermissionMapper
                .selectList(new LambdaQueryWrapper<RolePermission>().eq(RolePermission::getRoleId, roleId));
        for (RolePermission rolePermission : rolePermissions) {
            Permission permission = permissionMapper.selectById(rolePermission.getPermissionId());
            permissions.add(permission.getPermKey());
        }
        log.info("用户{}获取到的权限列表: {}", loginId, permissions);
        return permissions;
    }

    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        return null;
    }

}
