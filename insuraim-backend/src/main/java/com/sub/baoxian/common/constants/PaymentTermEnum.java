package com.sub.baoxian.common.constants;

/**
 * 缴费年限枚举类
 */
public enum PaymentTermEnum {

    /**
     * 1年缴
     */
    ONE_YEAR("1"),

    /**
     * 2年缴
     */
    TWO_YEAR("2"),

    /**
     * 3年缴
     */
    THREE_YEAR("3"),

    /**
     * 4年缴
     */
    FOUR_YEAR("4"),

    /**
     * 5年缴
     */
    FIVE_YEAR("5"),

    /**
     * 10年缴
     */
    TEN_YEAR("10"),

    /**
     * 15年缴
     */
    FIFTEEN_YEAR("15"),

    /**
     * 20年缴
     */
    TWENTY_YEAR("20"),

    /**
     * 25年缴
     */
    TWENTY_FIVE_YEAR("25"),

    /**
     * 30年缴
     */
    THIRTY_YEAR("30"),

    /**
     * 至99岁
     */
    UNTIL_99("至99"),

    /**
     * 终身缴费
     */
    LIFE_TERM("終身");

    private final String value;

    PaymentTermEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
    /**
     * 根据 value 获取枚举实例
     *
     * @param value 值
     * @return 枚举实例
     */
    public static PaymentTermEnum fromValue(String value) {
        for (PaymentTermEnum term : values()) {
            if (term.value.equals(value)) {
                return term;
            }
        }
        throw new IllegalArgumentException("Invalid payment term value: " + value);
    }
}