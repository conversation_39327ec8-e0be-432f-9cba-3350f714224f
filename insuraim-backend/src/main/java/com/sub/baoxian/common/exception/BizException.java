package com.sub.baoxian.common.exception;

import lombok.Getter;

/**
 * 自定义业务异常
 */
@Getter
public class BizException extends RuntimeException {
    
    private final Integer code;
    
    /**
     * 创建业务异常
     * @param message 错误信息
     */
    public BizException(String message) {
        super(message);
        this.code = 500;
    }
    
    /**
     * 创建业务异常
     * @param code 错误码
     * @param message 错误信息
     */
    public BizException(Integer code, String message) {
        super(message);
        this.code = code;
    }
    
    /**
     * 创建业务异常
     * @param message 错误信息
     * @param cause 原始异常
     */
    public BizException(String message, Throwable cause) {
        super(message, cause);
        this.code = 500;
    }
    
    /**
     * 创建业务异常
     * @param code 错误码
     * @param message 错误信息
     * @param cause 原始异常
     */
    public BizException(Integer code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }
} 