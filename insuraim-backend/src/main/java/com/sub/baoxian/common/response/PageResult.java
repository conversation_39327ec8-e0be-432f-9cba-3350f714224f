package com.sub.baoxian.common.response;

import lombok.Data;
import java.util.List;

/**
 * 分页查询结果封装类
 * @param <T> 数据类型
 */
@Data
public class PageResult<T> {
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 总页数
     */
    private Long pages;
    
    /**
     * 当前页码
     */
    private Integer currentPage;
    
    /**
     * 每页大小
     */
    private Integer pageSize;
    
    /**
     * 当前页数据
     */
    private List<T> list;
    
    /**
     * 是否有下一页
     */
    private Boolean hasNext;
} 