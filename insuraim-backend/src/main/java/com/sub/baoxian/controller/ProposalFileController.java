package com.sub.baoxian.controller;

import org.springframework.web.bind.annotation.RequestMapping;

import com.sub.baoxian.common.exception.BizException;
import com.sub.baoxian.service.ProposalService;
import com.sub.baoxian.util.StpKit;

import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;

import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@RequestMapping("/api/proposal-file")
@Controller
@Slf4j
@RequiredArgsConstructor
public class ProposalFileController {

    private final ProposalService proposalService;

    /**
     * 获取计划书PDF内容（预览模式）
     * 
     * @param id       计划书ID
     * @param response HTTP响应对象
     */
    @GetMapping("/pdf/preview/{id}")
    public void getProposalPdf(@PathVariable("id") Long id, HttpServletResponse response) throws IOException {
        log.info("获取计划书PDF预览, ID: {}", id);

        // 根据token获取用户ID
        Long userId = StpKit.USER.getLoginIdAsLong();

        // 获取PDF输入流
        InputStream pdfStream = proposalService.getProposalPdfStream(id, userId);

        // 设置响应头（预览模式）
        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition", "inline; filename=" + proposalService.getProposalFileName(id));

        // 写入响应
        IOUtils.copy(pdfStream, response.getOutputStream());
        response.flushBuffer();

        log.info("计划书PDF预览成功, ID: {}", id);
    }

    /**
     * 下载计划书PDF
     * 
     * @param id       计划书ID
     * @param response HTTP响应对象
     */
    @GetMapping("/pdf/download/{id}")
    public void downloadProposalPdf(@PathVariable("id") Long id, HttpServletResponse response) throws IOException {
        log.info("下载计划书PDF, ID: {}", id);

        // 获取当前登录用户ID
        Long userId = StpKit.USER.getLoginIdAsLong();

        // 获取PDF输入流（业务逻辑在Service中处理）
        InputStream pdfStream = proposalService.getProposalPdfStream(id, userId);

        // 设置响应头（下载模式）
        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition",
                "attachment; filename=" + proposalService.getProposalFileName(id));

        // 写入响应
        IOUtils.copy(pdfStream, response.getOutputStream());
        response.flushBuffer();

        log.info("计划书PDF下载成功, ID: {}", id);
    }
}
