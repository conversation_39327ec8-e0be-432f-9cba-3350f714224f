package com.sub.baoxian.controller.backend;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.model.dto.AdminLoginDTO;
import com.sub.baoxian.model.entity.Admin;
import com.sub.baoxian.service.AdminService;
import com.sub.baoxian.util.StpKit;

import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.RequiredArgsConstructor;

/**
 * 管理员控制器
 */
@RestController
@RequestMapping("/api/backend/admin")
@RequiredArgsConstructor
public class AdminController {

    private final AdminService adminService;

    /**
     * 管理员登录
     * 
     * @param adminLoginDTO 管理员登录信息
     * @return 登录结果
     */
    @PostMapping("/login")
    public Result<Map<String, Object>> login(@RequestBody AdminLoginDTO adminLoginDTO) {
        Admin admin = adminService.login(adminLoginDTO);

        // 登录成功，记录登录状态
        StpKit.ADMIN.login(admin.getId());

        Map<String, Object> data = new HashMap<>();
        data.put("admin", admin);
        data.put("token", StpKit.ADMIN.getTokenValue());

        return Result.success(data);
    }

    /**
     * 获取当前登录管理员信息
     * 
     * @return 管理员信息
     */
    @GetMapping("/info")
    public Result<Admin> getAdminInfo() {
        // 检查是否登录
        if (!StpKit.ADMIN.isLogin()) {
            return Result.error(401, "未登录");
        }

        // 获取当前登录管理员ID
        Long adminId = StpKit.ADMIN.getLoginIdAsLong();
        Admin admin = adminService.getAdminById(adminId);

        if (admin == null) {
            return Result.error(500, "管理员不存在");
        }

        return Result.success(admin);
    }

    /**
     * 管理员登出
     * 
     * @return 登出结果
     */
    @PostMapping("/logout")
    public Result<Void> logout() {
        StpKit.ADMIN.logout();
        return Result.success("登出成功");
    }

    /**
     * 检查是否登录
     * 
     * @return 登录状态
     */
    @GetMapping("/checkLogin")
    public Result<Boolean> checkLogin() {
        return Result.success(StpKit.ADMIN.isLogin());
    }

    /**
     * 分页查询管理员列表
     * 
     * @param pageNum  页码
     * @param pageSize 每页记录数
     * @param keyword  关键字
     * @param status   账号状态
     * @return 分页结果
     */
    @SaCheckPermission(value = "admin:page", type = "admin")
    @GetMapping("/page")
    public Result<Page<Admin>> page(
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "keyword", required = false) String keyword,
            @RequestParam(value = "status", required = false) Integer status) {

        return Result.success(adminService.page(pageNum, pageSize, keyword, status));
    }

    /**
     * 新增管理员
     * 
     * @param admin 管理员信息
     * @return 操作结果
     */
    @PostMapping
    @SaCheckPermission(value = "admin:add", type = "admin")
    public Result<Boolean> save(@RequestBody Admin admin) {
        return Result.success(adminService.save(admin));
    }

    /**
     * 修改管理员
     * 
     * @param admin 管理员信息
     * @return 操作结果
     */
    @PutMapping
    @SaCheckPermission(value = "admin:update", type = "admin")
    public Result<Boolean> update(@RequestBody Admin admin) {
        if (admin.getId() == null) {
            return Result.error("管理员ID不能为空");
        }
        return Result.success(adminService.update(admin));
    }

    /**
     * 删除管理员
     * 
     * @param id 管理员ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @SaCheckPermission(value = "admin:delete", type = "admin")
    public Result<Boolean> removeById(@PathVariable Long id) {
        return Result.success(adminService.removeById(id));
    }

    /**
     * 批量删除管理员
     * 
     * @param ids 管理员ID列表
     * @return 操作结果
     */
    @DeleteMapping("/batch")
    @SaCheckPermission(value = "admin:delete", type = "admin")
    public Result<Boolean> removeByIds(@RequestBody List<Long> ids) {
        return Result.success(adminService.removeByIds(ids));
    }

    /**
     * 修改管理员状态
     * 
     * @param id        管理员ID
     * @param statusMap 包含状态的map
     * @return 操作结果
     */
    @PutMapping("/status/{id}")
    @SaCheckPermission(value = "admin:updateStatus", type = "admin")
    public Result<Boolean> updateStatus(@PathVariable Long id, @RequestBody Map<String, Integer> statusMap) {
        Integer status = statusMap.get("status");
        if (status == null) {
            return Result.error("状态不能为空");
        }

        return Result.success(adminService.updateStatus(id, status));
    }

    /**
     * 重置管理员密码
     * 
     * @param id          管理员ID
     * @param passwordMap 包含新密码的map
     * @return 操作结果
     */
    @PutMapping("/resetPassword/{id}")
    @SaCheckPermission(value = "admin:resetPassword", type = "admin")
    public Result<Boolean> resetPassword(@PathVariable Long id, @RequestBody Map<String, String> passwordMap) {
        String newPassword = passwordMap.get("newPassword");
        if (newPassword == null || newPassword.trim().isEmpty()) {
            return Result.error("新密码不能为空");
        }

        return Result.success(adminService.resetPassword(id, newPassword));
    }

    /**
     * 获取管理员详情
     * 
     * @param id 管理员ID
     * @return 管理员详情
     */
    @GetMapping("/{id}")
    @SaCheckPermission(value = "admin:query", type = "admin")
    public Result<Admin> getById(@PathVariable Long id) {
        Admin admin = adminService.getAdminById(id);
        if (admin == null) {
            return Result.error("管理员不存在");
        }
        return Result.success(admin);
    }
}
