package com.sub.baoxian.controller.backend;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.model.entity.ProductCategories;
import com.sub.baoxian.model.vo.CategoriesVO;
import com.sub.baoxian.service.ProductCategoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 产品分类管理控制器
 */
@RestController
@RequestMapping("/api/backend/category")
@RequiredArgsConstructor
public class CategoryController {

    private final ProductCategoryService categoryService;

    /**
     * 分页查询分类列表
     */
    @GetMapping("/page")
    @SaCheckPermission(value = "category:page", type = "admin")
    public Result<Page<CategoriesVO>> page(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
        @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
        @RequestParam(value = "keyword", required = false) String keyword) {
        return Result.success(categoryService.page(pageNum, pageSize, keyword));
    }

    /**
     * 获取所有分类列表
     * 
     * @return 分类列表
     */
    @GetMapping("/list")
    @SaCheckPermission(value = "category:list", type = "admin")
    public Result<List<ProductCategories>> list() {
        return Result.success(categoryService.list());
    }

    /**
     * 获取分类详情
     * 
     * @param id 分类ID
     * @return 分类详情
     */
    @GetMapping("/{id}")
    @SaCheckPermission(value = "category:query", type = "admin")
    public Result<ProductCategories> getById(@PathVariable Long id) {
        return Result.success(categoryService.getById(id));
    }

    /**
     * 新增分类
     * 
     * @param category 分类信息
     * @return 操作结果
     */
    @PostMapping
    @SaCheckPermission(value = "category:add", type = "admin")
    public Result<Boolean> save(@RequestBody ProductCategories category) {
        return Result.success(categoryService.save(category));
    }

    /**
     * 修改分类
     * 
     * @param category 分类信息
     * @return 操作结果
     */
    @PutMapping
    @SaCheckPermission(value = "category:update", type = "admin")
    public Result<Boolean> update(@RequestBody ProductCategories category) {
        if (category.getId() == null) {
            return Result.error("分类ID不能为空");
        }
        return Result.success(categoryService.update(category));
    }

    /**
     * 删除分类
     * 
     * @param id 分类ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @SaCheckPermission(value = "category:delete", type = "admin")
    public Result<Boolean> removeById(@PathVariable Long id) {
        return Result.success(categoryService.removeById(id));
    }
}