package com.sub.baoxian.controller.backend;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.model.dto.*;
import com.sub.baoxian.model.entity.*;
import com.sub.baoxian.model.vo.InsurerDetailVO;
import com.sub.baoxian.service.ComapnyService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 保险公司管理Controller（后台管理）
 */
@Slf4j
@RestController
@RequestMapping("/api/backend/ins-company")
@RequiredArgsConstructor
@Validated
public class InsCompanyManageController {

    private final ComapnyService companyService;

    /**
     * 分页查询保险公司列表
     */
    @GetMapping("/page")
    public Result<IPage<Company>> getCompanyPage(CompanyQueryDTO queryDTO) {
        IPage<Company> pageResult = companyService.getCompanyPageForManage(queryDTO);
        return Result.success(pageResult);
    }

    /**
     * 获取保险公司列表（不分页）
     */
    @GetMapping("/list")
    public Result<List<Company>> getCompanyList(@RequestParam(required = false) String search,
            @RequestParam(required = false) String region) {
        List<Company> list = companyService.getCompanyList(search, region);
        return Result.success(list);
    }

    /**
     * 根据ID查询保险公司详情
     */
    @GetMapping("/{id}")
    public Result<Company> getCompanyById(@PathVariable Long id) {
        Company company = companyService.getInsurerById(id);
        if (company == null) {
            return Result.error("保险公司不存在");
        }
        return Result.success(company);
    }

    /**
     * 根据代码查询保险公司详情（包含关联数据）
     */
    @GetMapping("/detail/{code}")
    public Result<InsurerDetailVO> getCompanyDetail(@PathVariable String code) {
        InsurerDetailVO detail = companyService.getInsurerByCode(code);
        if (detail == null) {
            return Result.error("保险公司不存在");
        }
        return Result.success(detail);
    }

    /**
     * 创建保险公司
     */
    @PostMapping
    public Result<Company> createCompany(@RequestBody @Validated CompanyCreateDTO createDTO) {
        Company company = companyService.createCompany(createDTO);
        return Result.success(company);
    }

    /**
     * 更新保险公司
     */
    @PostMapping("/update/{id}")
    public Result<Company> updateCompany(@PathVariable Long id, @RequestBody @Validated CompanyUpdateDTO updateDTO) {
        updateDTO.setId(id);
        Company company = companyService.updateCompany(updateDTO);
        return Result.success(company);
    }

    /**
     * 删除保险公司
     */
    @PostMapping("/delete/{id}")
    public Result<Void> deleteCompany(@PathVariable Long id) {
        companyService.deleteCompany(id);
        return Result.success();
    }

    /**
     * 批量删除保险公司
     */
    @PostMapping("/batch/delete")
    public Result<Void> batchDeleteCompanies(@RequestBody List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return Result.error("请选择要删除的保险公司");
        }
        for (Long id : ids) {
            companyService.deleteCompany(id);
        }
        return Result.success();
    }

    // ==================== 信用评级管理 ====================

    /**
     * 获取保险公司信用评级列表
     */
    @GetMapping("/{code}/credit-ratings")
    public Result<List<CompanyCreditRatings>> getCreditRatings(@PathVariable String code) {
        List<CompanyCreditRatings> ratings = companyService.getCreditRatingsByCode(code);
        return Result.success(ratings);
    }

    /**
     * 添加信用评级
     */
    @PostMapping("/credit-rating")
    public Result<CompanyCreditRatings> addCreditRating(
            @RequestBody @Validated CompanyCreditRatingCreateDTO createDTO) {
        CompanyCreditRatings rating = companyService.addCreditRating(createDTO);
        return Result.success(rating);
    }

    /**
     * 更新信用评级
     */
    @PostMapping("/credit-rating/update/{id}")
    public Result<CompanyCreditRatings> updateCreditRating(@PathVariable Integer id,
            @RequestBody @Validated CompanyCreditRatingUpdateDTO updateDTO) {
        updateDTO.setId(id);
        CompanyCreditRatings rating = companyService.updateCreditRating(updateDTO);
        return Result.success(rating);
    }

    /**
     * 删除信用评级
     */
    @PostMapping("/credit-rating/delete/{id}")
    public Result<Void> deleteCreditRating(@PathVariable Integer id) {
        companyService.deleteCreditRating(id);
        return Result.success();
    }

    // ==================== 文件管理 ====================

    /**
     * 获取保险公司文件列表
     */
    @GetMapping("/{code}/files")
    public Result<List<CompanyFile>> getFiles(@PathVariable String code) {
        List<CompanyFile> files = companyService.getFilesByCode(code);
        return Result.success(files);
    }

    /**
     * 上传文件
     */
    @PostMapping("/file/upload")
    public Result<CompanyFile> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam("code") String code,
            @RequestParam("author") String author) {
        CompanyFile companyFile = companyService.uploadFile(file, code, author);
        return Result.success(companyFile, "文件上传成功");
    }

    /**
     * 添加文件（通过DTO）
     */
    @PostMapping("/file")
    public Result<CompanyFile> addFile(@RequestBody @Validated CompanyFileCreateDTO createDTO) {
        CompanyFile file = companyService.addFile(createDTO);
        return Result.success(file);
    }

    /**
     * 更新文件
     */
    @PostMapping("/file/update/{id}")
    public Result<CompanyFile> updateFile(@PathVariable Integer id,
            @RequestBody @Validated CompanyFileUpdateDTO updateDTO) {
        updateDTO.setId(id);
        CompanyFile file = companyService.updateFile(updateDTO);
        return Result.success(file);
    }

    /**
     * 获取文件详情
     */
    @GetMapping("/file/{id}")
    public Result<CompanyFile> getFileById(@PathVariable Integer id) {
        CompanyFile file = companyService.getFileById(id);
        if (file == null) {
            return Result.error("文件不存在");
        }
        return Result.success(file);
    }

    /**
     * 删除文件
     */
    @PostMapping("/file/delete/{id}")
    public Result<Void> deleteFile(@PathVariable Integer id) {
        companyService.deleteFile(id);
        return Result.success();
    }

    // ==================== 链接管理 ====================

    /**
     * 获取保险公司链接列表
     */
    @GetMapping("/{code}/links")
    public Result<List<CompanyLink>> getLinks(@PathVariable String code) {
        List<CompanyLink> links = companyService.getLinksByCode(code);
        return Result.success(links);
    }

    /**
     * 添加链接
     */
    @PostMapping("/link")
    public Result<CompanyLink> addLink(@RequestBody @Validated CompanyLinkCreateDTO createDTO) {
        CompanyLink link = companyService.addLink(createDTO);
        return Result.success(link);
    }

    /**
     * 更新链接
     */
    @PostMapping("/link/update/{id}")
    public Result<CompanyLink> updateLink(@PathVariable Integer id,
            @RequestBody @Validated CompanyLinkUpdateDTO updateDTO) {
        updateDTO.setId(id);
        CompanyLink link = companyService.updateLink(updateDTO);
        return Result.success(link);
    }

    /**
     * 删除链接
     */
    @PostMapping("/link/delete/{id}")
    public Result<Void> deleteLink(@PathVariable Integer id) {
        companyService.deleteLink(id);
        return Result.success();
    }

}
