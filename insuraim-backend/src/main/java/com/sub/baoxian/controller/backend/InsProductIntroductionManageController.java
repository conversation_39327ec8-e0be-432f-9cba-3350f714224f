package com.sub.baoxian.controller.backend;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.model.dto.InsProductIntroductionCreateDTO;
import com.sub.baoxian.model.dto.InsProductIntroductionQueryDTO;
import com.sub.baoxian.model.dto.InsProductIntroductionUpdateDTO;
import com.sub.baoxian.model.vo.InsProductIntroductionVO;
import com.sub.baoxian.service.InsProductIntroductionService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 产品介绍管理Controller（后台管理）
 */
@Slf4j
@RestController
@RequestMapping("/api/backend/ins-product-introduction")
@RequiredArgsConstructor
@Validated
@Tag(name = "产品介绍管理", description = "产品介绍的增删改查接口")
public class InsProductIntroductionManageController {

    private final InsProductIntroductionService insProductIntroductionService;

    /**
     * 分页查询产品介绍列表
     */
    @GetMapping("/page")
    @Operation(summary = "分页查询产品介绍列表", description = "支持按产品ID、状态、关键词等条件查询")
    public Result<IPage<InsProductIntroductionVO>> getProductIntroductionPage(InsProductIntroductionQueryDTO queryDTO) {
        IPage<InsProductIntroductionVO> pageResult = insProductIntroductionService.getProductIntroductionPage(queryDTO);
        return Result.success(pageResult);
    }

    /**
     * 根据ID查询产品介绍详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询产品介绍详情")
    public Result<InsProductIntroductionVO> getProductIntroductionById(
            @Parameter(description = "产品介绍ID", required = true) @PathVariable Long id) {
        InsProductIntroductionVO productIntroduction = insProductIntroductionService.getProductIntroductionById(id);
        return Result.success(productIntroduction);
    }

    /**
     * 根据产品ID查询产品介绍
     */
    @GetMapping("/product/{productId}")
    @Operation(summary = "根据产品ID查询产品介绍")
    public Result<InsProductIntroductionVO> getProductIntroductionByProductId(
            @Parameter(description = "产品ID", required = true) @PathVariable Long productId) {
        InsProductIntroductionVO productIntroduction = insProductIntroductionService.getProductIntroductionByProductId(productId);
        return Result.success(productIntroduction);
    }

    /**
     * 创建产品介绍
     */
    @PostMapping
    @Operation(summary = "创建产品介绍")
    public Result<InsProductIntroductionVO> createProductIntroduction(
            @Parameter(description = "产品介绍创建参数", required = true) @Valid @RequestBody InsProductIntroductionCreateDTO createDTO) {
        InsProductIntroductionVO productIntroduction = insProductIntroductionService.createProductIntroduction(createDTO);
        return Result.success(productIntroduction);
    }

    /**
     * 更新产品介绍
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新产品介绍")
    public Result<InsProductIntroductionVO> updateProductIntroduction(
            @Parameter(description = "产品介绍ID", required = true) @PathVariable Long id,
            @Parameter(description = "产品介绍更新参数", required = true) @Valid @RequestBody InsProductIntroductionUpdateDTO updateDTO) {
        updateDTO.setId(id);
        InsProductIntroductionVO productIntroduction = insProductIntroductionService.updateProductIntroduction(updateDTO);
        return Result.success(productIntroduction);
    }

    /**
     * 删除产品介绍
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除产品介绍")
    public Result<Void> deleteProductIntroduction(
            @Parameter(description = "产品介绍ID", required = true) @PathVariable Long id) {
        insProductIntroductionService.deleteProductIntroduction(id);
        return Result.success("删除成功");
    }

    /**
     * 根据产品ID删除产品介绍
     */
    @DeleteMapping("/product/{productId}")
    @Operation(summary = "根据产品ID删除产品介绍")
    public Result<Void> deleteProductIntroductionByProductId(
            @Parameter(description = "产品ID", required = true) @PathVariable Long productId) {
        insProductIntroductionService.deleteProductIntroductionByProductId(productId);
        return Result.success("删除成功");
    }
}
