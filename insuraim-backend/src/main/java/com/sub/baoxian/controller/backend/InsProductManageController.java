package com.sub.baoxian.controller.backend;

import java.util.List;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.model.dto.InsProductCreateDTO;
import com.sub.baoxian.model.dto.InsProductQueryDTO;
import com.sub.baoxian.model.dto.InsProductUpdateDTO;
import com.sub.baoxian.model.entity.InsProduct;
import com.sub.baoxian.model.vo.InsProductVO;
import com.sub.baoxian.service.InsProductService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 产品管理Controller（后台管理）
 */
@Slf4j
@RestController
@RequestMapping("/api/backend/ins-product")
@RequiredArgsConstructor
@Validated
public class InsProductManageController {

    private final InsProductService insProductService;

    /**
     * 分页查询产品列表（包含产品详情和子产品）
     */
    @GetMapping("/page")
    public Result<IPage<InsProductVO>> getProductPage(InsProductQueryDTO queryDTO) {
        IPage<InsProductVO> pageResult = insProductService.getProductPageWithDetails(queryDTO);
        return Result.success(pageResult);
    }

    /**
     * 分页查询产品列表（基础信息，不包含详情）
     */
    @GetMapping("/page-basic")
    public Result<IPage<InsProduct>> getProductPageBasic(InsProductQueryDTO queryDTO) {
        IPage<InsProduct> pageResult = insProductService.getProductPage(queryDTO);
        return Result.success(pageResult);
    }

    /**
     * 获取产品列表（不分页）
     */
    @GetMapping("/list")
    public Result<List<InsProductVO>> getProductList(@RequestParam(required = false) Long categoryId) {
        List<InsProductVO> list = insProductService.getProductList(categoryId);
        return Result.success(list);
    }

    /**
     * 获取产品列表（不分页）
     */
    @GetMapping("/list-basic")
    public Result<List<InsProduct>> getProductListBasic(@RequestParam(required = false) Long categoryId) {
        List<InsProduct> list = insProductService.getProductListBasic(categoryId);
        return Result.success(list);
    }

    /**
     * 根据ID查询产品详情
     */
    @GetMapping("/{id}")
    public Result<InsProductVO> getProductById(@PathVariable Long id) {
        InsProductVO productVO = insProductService.getProductDetailById(id);
        return Result.success(productVO);
    }

    /**
     * 创建产品
     */
    @PostMapping
    public Result<InsProduct> createProduct(@RequestBody InsProductCreateDTO createDTO) {
        InsProduct product = insProductService.createProduct(createDTO);
        return Result.success(product);
    }

    /**
     * 更新产品
     */
    @PutMapping("/{id}")
    public Result<InsProduct> updateProduct(@PathVariable Long id, @RequestBody InsProductUpdateDTO updateDTO) {
        updateDTO.setId(id);
        InsProduct product = insProductService.updateProduct(updateDTO);
        return Result.success(product);
    }

    /**
     * 删除产品
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteProduct(@PathVariable Long id) {
        insProductService.deleteProduct(id);
        return Result.success();
    }

}
