package com.sub.baoxian.controller.backend;

import java.util.List;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.model.dto.InsProductSubCreateDTO;
import com.sub.baoxian.model.dto.InsProductSubQueryDTO;
import com.sub.baoxian.model.dto.InsProductSubUpdateDTO;
import com.sub.baoxian.model.entity.InsProductSub;
import com.sub.baoxian.service.InsProductSubService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 子产品管理Controller（后台管理）
 */
@Slf4j
@RestController
@RequestMapping("/api/backend/ins-product-sub")
@RequiredArgsConstructor
@Validated
public class InsProductSubManageController {

    private final InsProductSubService insProductSubService;

    /**
     * 分页查询子产品列表
     */
    @GetMapping("/page")
    public Result<IPage<InsProductSub>> getSubProductPage(InsProductSubQueryDTO queryDTO) {
        IPage<InsProductSub> pageResult = insProductSubService.getSubProductPage(queryDTO);
        return Result.success(pageResult);
    }

    /**
     * 根据父产品ID获取子产品列表
     */
    @GetMapping("/list")
    public Result<List<InsProductSub>> getSubProductsByParentId(@RequestParam Integer parentProductId) {
        List<InsProductSub> list = insProductSubService.getSubProductsByParentId(parentProductId);
        return Result.success(list);
    }

    /**
     * 根据ID查询子产品详情
     */
    @GetMapping("/{id}")
    public Result<InsProductSub> getSubProductById(@PathVariable Long id) {
        InsProductSub subProduct = insProductSubService.getSubProductById(id);
        if (subProduct == null) {
            return Result.error("子产品不存在");
        }
        return Result.success(subProduct);
    }

    /**
     * 创建子产品
     */
    @PostMapping
    public Result<InsProductSub> createSubProduct(@RequestBody InsProductSubCreateDTO createDTO) {
        InsProductSub subProduct = insProductSubService.createSubProduct(createDTO);
        return Result.success(subProduct);
    }

    /**
     * 更新子产品
     */
    @PutMapping("/{id}")
    public Result<InsProductSub> updateSubProduct(@PathVariable Long id,
            @RequestBody InsProductSubUpdateDTO updateDTO) {
        updateDTO.setId(id);
        InsProductSub subProduct = insProductSubService.updateSubProduct(updateDTO);
        return Result.success(subProduct);
    }

    /**
     * 删除子产品
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteSubProduct(@PathVariable Long id) {
        insProductSubService.deleteSubProduct(id);
        return Result.success();
    }

    /**
     * 根据父产品ID删除所有子产品
     */
    @DeleteMapping("/parent/{parentProductId}")
    public Result<Void> deleteSubProductsByParentId(@PathVariable Integer parentProductId) {
        insProductSubService.deleteSubProductsByParentId(parentProductId);
        return Result.success();
    }
}
