package com.sub.baoxian.controller.backend;

import java.util.List;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.model.dto.MenuCreateDTO;
import com.sub.baoxian.model.dto.MenuUpdateDTO;
import com.sub.baoxian.model.entity.Menu;
import com.sub.baoxian.model.vo.MenuTreeVO;
import com.sub.baoxian.service.MenuService;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

/**
 * 菜单控制器
 */
@RestController("backendMenuController")
@RequestMapping("/api/backend/menu")
@RequiredArgsConstructor
public class MenuController {

    private final MenuService menuService;

    /**
     * 获取所有菜单列表
     * @return 菜单列表
     */
    @GetMapping("/list")
    @SaCheckPermission(value = "menu:list", type = "admin")
    public Result<List<Menu>> list() {
        return Result.success(menuService.list());
    }

    /**
     * 获取菜单树形结构
     * @return 菜单树
     */
    @GetMapping("/tree")
    @SaCheckPermission(value = "menu:list", type = "admin")
    public Result<List<MenuTreeVO>> tree() {
        return Result.success(menuService.getMenuTree());
    }

    /**
     * 根据ID获取菜单详情
     * @param id 菜单ID
     * @return 菜单详情
     */
    @GetMapping("/{id}")
    @SaCheckPermission(value = "menu:query", type = "admin")
    public Result<MenuTreeVO> getById(@PathVariable Long id) {
        return Result.success(menuService.getMenuById(id));
    }

    /**
     * 新增菜单
     * @param createDTO 菜单创建DTO
     * @return 操作结果
     */
    @PostMapping
    @SaCheckPermission(value = "menu:add", type = "admin")
    public Result<Boolean> save(@Valid @RequestBody MenuCreateDTO createDTO) {
        return Result.success(menuService.save(createDTO));
    }

    /**
     * 更新菜单
     * @param updateDTO 菜单更新DTO
     * @return 操作结果
     */
    @PutMapping
    @SaCheckPermission(value = "menu:update", type = "admin")
    public Result<Boolean> update(@Valid @RequestBody MenuUpdateDTO updateDTO) {
        return Result.success(menuService.update(updateDTO));
    }

    /**
     * 删除菜单
     * @param id 菜单ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @SaCheckPermission(value = "menu:delete", type = "admin")
    public Result<Boolean> removeById(@PathVariable Long id) {
        return Result.success(menuService.removeById(id));
    }

    /**
     * 批量删除菜单
     * @param ids 菜单ID列表
     * @return 操作结果
     */
    @DeleteMapping("/batch")
    @SaCheckPermission(value = "menu:delete", type = "admin")
    public Result<Boolean> removeByIds(@RequestBody List<Long> ids) {
        return Result.success(menuService.removeByIds(ids));
    }

    /**
     * 为角色分配菜单
     * @param roleId 角色ID
     * @param menuIds 菜单ID列表
     * @return 操作结果
     */
    @PostMapping("/role/{roleId}")
    @SaCheckPermission(value = "menu:assign", type = "admin")
    public Result<Boolean> assignMenus(@PathVariable Long roleId, @RequestBody List<Long> menuIds) {
        return Result.success(menuService.assignMenus(roleId, menuIds));
    }

    /**
     * 获取角色已分配的菜单ID列表
     * @param roleId 角色ID
     * @return 菜单ID列表
     */
    @GetMapping("/role/{roleId}")
    @SaCheckPermission(value = "menu:query", type = "admin")
    public Result<List<Long>> getRoleMenuIds(@PathVariable Long roleId) {
        return Result.success(menuService.getRoleMenuIds(roleId));
    }
}