package com.sub.baoxian.controller.backend;

import java.util.List;

import org.springframework.web.bind.annotation.*;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.model.entity.Organization;
import com.sub.baoxian.model.vo.OrganizationPageListVO;
import com.sub.baoxian.service.OrganizationService;

import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.RequiredArgsConstructor;

/**
 * 组织管理控制器
 */
@RestController
@RequestMapping("/api/backend/organization")
@RequiredArgsConstructor
public class OrganizationController {

    private final OrganizationService organizationService;

    /**
     * 分页查询组织列表
     */
    @GetMapping("/page")
    @SaCheckPermission(value = "org:page", type = "admin")
    public Result<Page<OrganizationPageListVO>> page(
        @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
        @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
        @RequestParam(value = "keyword", required = false) String keyword,
        @RequestParam(value = "type", required = false) String type,
        @RequestParam(value = "status", required = false) Integer status) {
        return Result.success(organizationService.page(pageNum, pageSize, keyword, type, status));
    }

    /**
     * 根据类型获取组织列表
     * 
     * @param type 组织类型
     * @return 组织列表
     */
    @GetMapping("/list/{type}")
    @SaCheckPermission(value = "org:list", type = "admin")
    public Result<List<Organization>> listByType(@PathVariable String type) {
        return Result.success(organizationService.listByType(type));
    }

    /**
     * 获取组织详情
     * 
     * @param id 组织ID
     * @return 组织详情
     */
    @GetMapping("/{id}")
    @SaCheckPermission(value = "org:query", type = "admin")
    public Result<Organization> getById(@PathVariable Long id) {
        return Result.success(organizationService.getById(id));
    }

    /**
     * 新增组织
     * 
     * @param organization 组织信息
     * @return 操作结果
     */
    @PostMapping
    @SaCheckPermission(value = "org:add", type = "admin")
    public Result<Boolean> save(@RequestBody Organization organization) {
        return Result.success(organizationService.save(organization));
    }

    /**
     * 修改组织
     * 
     * @param organization 组织信息
     * @return 操作结果
     */
    @PutMapping
    @SaCheckPermission(value = "org:update", type = "admin")
    public Result<Boolean> update(@RequestBody Organization organization) {
        return Result.success(organizationService.update(organization));
    }

    /**
     * 删除组织
     * 
     * @param id 组织ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @SaCheckPermission(value = "org:delete", type = "admin")
    public Result<Boolean> removeById(@PathVariable Long id) {
        return Result.success(organizationService.removeById(id));
    }

    /**
     * 批量删除组织
     * 
     * @param ids 组织ID列表
     * @return 操作结果
     */
    @DeleteMapping("/batch")
    @SaCheckPermission(value = "org:delete", type = "admin")
    public Result<Boolean> removeByIds(@RequestBody List<Long> ids) {
        return Result.success(organizationService.removeByIds(ids));
    }
}
