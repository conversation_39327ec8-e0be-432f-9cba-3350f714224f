package com.sub.baoxian.controller.backend;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.model.entity.Permission;
import com.sub.baoxian.service.PermissionService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 权限管理控制器
 */
@RestController
@RequestMapping("/api/backend/permission")
@RequiredArgsConstructor
public class PermissionController {

    private final PermissionService permissionService;

    /**
     * 分页查询权限列表
     * 
     * @param pageNum  页码
     * @param pageSize 每页记录数
     * @param keyword  关键字
     * @return 分页结果
     */
    @GetMapping("/page")
    @SaCheckPermission(value = "permission:page", type = "admin")
    public Result<Page<Permission>> page(
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "keyword", required = false) String keyword) {

        return Result.success(permissionService.page(pageNum, pageSize, keyword));
    }

    /**
     * 获取所有权限列表
     * 
     * @return 权限列表
     */
    @GetMapping("/list")
    @SaCheckPermission(value = "permission:list", type = "admin")
    public Result<List<Permission>> list() {
        return Result.success(permissionService.list());
    }

    /**
     * 获取权限详情
     * 
     * @param id 权限ID
     * @return 权限详情
     */
    @GetMapping("/{id}")
    @SaCheckPermission(value = "permission:query", type = "admin")
    public Result<Permission> getById(@PathVariable Long id) {
        Permission permission = permissionService.getById(id);
        if (permission == null) {
            return Result.error("权限不存在");
        }
        return Result.success(permission);
    }

    /**
     * 新增权限
     * 
     * @param permission 权限信息
     * @return 操作结果
     */
    @PostMapping
    @SaCheckPermission(value = "permission:add", type = "admin")
    public Result<Boolean> save(@RequestBody Permission permission) {
        return Result.success(permissionService.save(permission));
    }

    /**
     * 修改权限
     * 
     * @param permission 权限信息
     * @return 操作结果
     */
    @PutMapping
    @SaCheckPermission(value = "permission:update", type = "admin")
    public Result<Boolean> update(@RequestBody Permission permission) {
        if (permission.getId() == null) {
            return Result.error("权限ID不能为空");
        }
        return Result.success(permissionService.update(permission));
    }

    /**
     * 删除权限
     * 
     * @param id 权限ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @SaCheckPermission(value = "permission:delete", type = "admin")
    public Result<Boolean> removeById(@PathVariable Long id) {
        return Result.success(permissionService.removeById(id));
    }

    /**
     * 批量删除权限
     * 
     * @param ids 权限ID列表
     * @return 操作结果
     */
    @DeleteMapping("/batch")
    @SaCheckPermission(value = "permission:delete", type = "admin")
    public Result<Boolean> removeByIds(@RequestBody List<Long> ids) {
        return Result.success(permissionService.removeByIds(ids));
    }

    /**
     * 获取角色的权限列表
     * 
     * @param roleId 角色ID
     * @return 权限列表
     */
    @GetMapping("/role/{roleId}")
    @SaCheckPermission(value = "permission:query", type = "admin")
    public Result<List<Permission>> getByRoleId(@PathVariable Long roleId) {
        return Result.success(permissionService.getByRoleId(roleId));
    }

    /**
     * 为角色分配权限
     * 
     * @param roleId        角色ID
     * @param permissionIds 权限ID列表
     * @return 操作结果
     */
    @PostMapping("/role/{roleId}")
    @SaCheckPermission(value = "permission:assign", type = "admin")
    public Result<Boolean> assignPermissions(
            @PathVariable Long roleId,
            @RequestBody List<Long> permissionIds) {
        return Result.success(permissionService.assignPermissions(roleId, permissionIds));
    }
}