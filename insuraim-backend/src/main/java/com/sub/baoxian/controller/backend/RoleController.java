package com.sub.baoxian.controller.backend;

import java.util.List;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.model.entity.Role;
import com.sub.baoxian.model.vo.RolePageListVO;
import com.sub.baoxian.service.RoleService;

import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.RequiredArgsConstructor;

/**
 * 角色管理控制器
 */
@RestController
@RequestMapping("/api/backend/role")
@RequiredArgsConstructor
public class RoleController {

    private final RoleService roleService;

    /**
     * 分页查询角色列表
     */
    @GetMapping("/page")
    @SaCheckPermission(value = "role:page",type = "admin")
    public Result<Page<RolePageListVO>> page(
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "keyword", required = false) String keyword) {
        return Result.success(roleService.page(pageNum, pageSize, keyword));
    }

    /**
     * 获取所有角色列表
     * @return 角色列表
     */
    @GetMapping("/list")
    @SaCheckPermission(value = "role:list",type = "admin")
    public Result<List<Role>> list() {
        return Result.success(roleService.list());
    }

    /**
     * 获取角色详情
     * @param id 角色ID
     * @return 角色详情
     */
    @GetMapping("/{id}")
    @SaCheckPermission(value = "role:query",type = "admin")
    public Result<Role> getById(@PathVariable Long id) {
        return Result.success(roleService.getById(id));
    }

    /**
     * 新增角色
     * @param role 角色信息
     * @return 操作结果
     */
    @PostMapping
    @SaCheckPermission(value = "role:add",type = "admin")
    public Result<Boolean> save(@RequestBody Role role) {
        return Result.success(roleService.save(role));
    }

    /**
     * 修改角色
     * @param role 角色信息
     * @return 操作结果
     */
    @PutMapping
    @SaCheckPermission(value = "role:update",type = "admin")
    public Result<Boolean> update(@RequestBody Role role) {
        return Result.success(roleService.update(role));
    }

    /**
     * 删除角色
     * @param id 角色ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @SaCheckPermission(value = "role:delete",type = "admin")
    public Result<Boolean> removeById(@PathVariable Long id) {
        return Result.success(roleService.removeById(id));
    }

    /**
     * 批量删除角色
     * @param ids 角色ID列表
     * @return 操作结果
     */
    @DeleteMapping("/batch")
    @SaCheckPermission(value = "role:delete",type = "admin")
    public Result<Boolean> removeByIds(@RequestBody List<Long> ids) {
        return Result.success(roleService.removeByIds(ids));
    }
}
