package com.sub.baoxian.controller.backend;

import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.model.entity.User;
import com.sub.baoxian.model.vo.OrganizationPageUsersListVO;
import com.sub.baoxian.service.UserService;

import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.RequiredArgsConstructor;

@RestController("backendUserController")
@RequestMapping("/api/backend/user")
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;

    /**
     * 分页查询用户列表
     * 
     * @param pageNum  页码
     * @param pageSize 每页记录数
     * @param keyword  关键字
     * @param roleId   角色ID
     * @param username 用户名
     * @param status   账号状态
     * @return 分页结果
     */
    @GetMapping("/page")
    @SaCheckPermission(value = "user:page", type = "admin")
    public Result<Page<OrganizationPageUsersListVO>> page(
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "keyword", required = false) String keyword,
            @RequestParam(value = "roleId", required = false) Long roleId,
            @RequestParam(value = "username", required = false) String username,
            @RequestParam(value = "status", required = false) Integer status) {

        return Result.success(userService.page(pageNum, pageSize, keyword, roleId, username, status));
    }

    /**
     * 新增用户
     * 
     * @param user 用户信息
     * @return 操作结果
     */
    @PostMapping
    @SaCheckPermission(value = "user:add", type = "admin")
    public Result<Boolean> save(@RequestBody User user) {
        return Result.success(userService.save(user));
    }

    /**
     * 修改用户
     * 
     * @param user 用户信息
     * @return 操作结果
     */
    @PutMapping
    @SaCheckPermission(value = "user:update", type = "admin")
    public Result<Boolean> update(@RequestBody User user) {
        if (user.getId() == null) {
            return Result.error("用户ID不能为空");
        }
        return Result.success(userService.update(user));
    }

    /**
     * 删除用户
     * 
     * @param id 用户ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @SaCheckPermission(value = "user:delete", type = "admin")
    public Result<Boolean> removeById(@PathVariable Long id) {
        return Result.success(userService.removeById(id));
    }

    /**
     * 批量删除用户
     * 
     * @param ids 用户ID列表
     * @return 操作结果
     */
    @DeleteMapping("/batch")
    @SaCheckPermission(value = "user:delete", type = "admin")
    public Result<Boolean> removeByIds(@RequestBody List<Long> ids) {
        return Result.success(userService.removeByIds(ids));
    }

    /**
     * 重置用户密码
     * 
     * @param id          用户ID
     * @param passwordMap 包含新密码的map
     * @return 操作结果
     */
    @PutMapping("/resetPassword/{id}")
    @SaCheckPermission(value = "user:resetPassword", type = "admin")
    public Result<Boolean> resetPassword(@PathVariable Long id, @RequestBody Map<String, String> passwordMap) {
        String newPassword = passwordMap.get("newPassword");
        if (newPassword == null || newPassword.trim().isEmpty()) {
            return Result.error("新密码不能为空");
        }

        User user = new User();
        user.setId(id);
        user.setPassword(newPassword);
        return Result.success(userService.update(user));
    }

    /**
     * 修改用户状态
     * 
     * @param id        用户ID
     * @param statusMap 包含状态的map
     * @return 操作结果
     */
    @PutMapping("/status/{id}")
    @SaCheckPermission(value = "user:updateStatus", type = "admin")
    public Result<Boolean> updateStatus(@PathVariable Long id, @RequestBody Map<String, Integer> statusMap) {
        Integer status = statusMap.get("status");
        if (status == null) {
            return Result.error("状态不能为空");
        }

        User user = new User();
        user.setId(id);
        user.setStatus(status);
        return Result.success(userService.update(user));
    }
}
