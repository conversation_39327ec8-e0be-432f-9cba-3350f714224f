package com.sub.baoxian.controller.common;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.model.entity.Company;
import com.sub.baoxian.model.vo.InsurerDetailVO;
import com.sub.baoxian.service.ComapnyService;

import lombok.RequiredArgsConstructor;

/**
 * 保险公司控制器
 */
@RestController
@RequestMapping("/api/common/company")
@RequiredArgsConstructor
public class CompanyController {

    private final ComapnyService companyService;

    /**
     * 分页查询保险公司列表
     *
     * @param pageNum  页码(默认1)
     * @param pageSize 每页数量(默认10)
     * @param search     公司名称(可选，用于模糊查询)
     * @param region   地区(可选)
     * @return 分页结果
     */
    @GetMapping("/page")
    public Result<IPage<Company>> getCompanyPage(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String region) {

        // 创建分页对象
        Page<Company> page = new Page<>(pageNum, pageSize);

        IPage<Company> pageResult = companyService.getCompanyPage(page, search, region);

        return Result.success(pageResult);
    }

    @GetMapping("/list")
    public Result<List<Company>> getCompanyList(@RequestParam(required = false) String search,
            @RequestParam(required = false) String region) {
        return Result.success(companyService.getCompanyList(search, region));
    }

    /**
     * 获取保险公司详情
     * 
     * @param code 保险公司code
     * @return 保险公司详情
     */
    @GetMapping("/{code}")
    public Result<InsurerDetailVO> getCompanyDetail(@PathVariable String code) {
        return Result.success(companyService.getInsurerByCode(code));
    }
}
