package com.sub.baoxian.controller.common;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.model.dto.InsProductQueryDTO;
import com.sub.baoxian.model.entity.ProposalProductConfig;
import com.sub.baoxian.model.vo.InsProductVO;
import com.sub.baoxian.service.InsProductService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * ins_product产品控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/common/ins-product")
@RequiredArgsConstructor
public class InsProductController {

    private final InsProductService insProductService;

    /**
     * 分页查询产品列表
     *
     * @param queryDTO 查询条件DTO
     * @return 分页结果
     */
    @GetMapping("/page")
    public Result<IPage<InsProductVO>> getProductPage(InsProductQueryDTO queryDTO) {
        IPage<InsProductVO> pageResult = insProductService.getProductPageWithDetails(queryDTO);
        return Result.success(pageResult);
    }

    /**
     * 获取产品列表
     * 
     * @param categoryId 分类ID(可选)
     * @return 产品列表
     */
    @GetMapping("/list")
    public Result<List<InsProductVO>> getProductList(@RequestParam(required = false) Long categoryId) {
        List<InsProductVO> list = insProductService.getProductList(categoryId);
        return Result.success(list);
    }

    /**
     * 根据ID查询产品详情
     *
     * @param id 产品ID
     * @return 产品详情
     */
    @GetMapping("/{id}")
    public Result<InsProductVO> getProductById(@PathVariable Long id) {
        InsProductVO productVO = insProductService.getProductDetailById(id);
        return Result.success(productVO);
    }

    /**
     * 获取可收益演算的产品列表
     */
    @GetMapping("/benefit-calculate-list")
    public Result<List<ProposalProductConfig>> getBenefitCalculateList() {
        return Result.success(insProductService.getBenefitCalculateList());
    }

    /**
     * 获取可生成计划书产品配置列表
     */
    @GetMapping("/plan-book-list")
    public Result<List<ProposalProductConfig>> getPlanBookList() {
        return Result.success(insProductService.getPlanBookList());
    }

    /**
     * 用户收藏产品
     */
    @PostMapping("/favorite/{productId}")
    public Result<Void> favoriteProduct(@PathVariable Long productId) {
        boolean success = insProductService.favoriteProduct(productId);
        if (success) {
            return Result.success("收藏成功");
        } else {
            return Result.error("收藏失败");
        }
    }

    /**
     * 用户取消收藏产品
     */
    @PostMapping("/unfavorite/{productId}")
    public Result<Void> unfavoriteProduct(@PathVariable Long productId) {
        boolean success = insProductService.unfavoriteProduct(productId);
        if (success) {
            return Result.success("取消收藏成功");
        } else {
            return Result.error("取消收藏失败");
        }
    }

    /**
     * 获取用户收藏的产品列表
     */
    @GetMapping("/favorites")
    public Result<List<InsProductVO>> getUserFavoriteProducts() {
        List<InsProductVO> favoriteProducts = insProductService.getUserFavoriteProducts();
        return Result.success(favoriteProducts);
    }

    /**
     * 检查用户是否收藏了指定产品
     */
    @GetMapping("/favorite/check/{productId}")
    public Result<Boolean> isProductFavorited(@PathVariable Long productId) {
        boolean isFavorited = insProductService.isProductFavorited(productId);
        return Result.success(isFavorited);
    }
}
