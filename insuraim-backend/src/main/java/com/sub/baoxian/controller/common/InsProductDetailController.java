package com.sub.baoxian.controller.common;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.model.dto.InsProductDetailContentDTO;
import com.sub.baoxian.model.dto.InsProductDetailTitleDTO;
import com.sub.baoxian.model.dto.ProductDetailQueryDTO;
import com.sub.baoxian.model.vo.InsProductDetailContentVO;
import com.sub.baoxian.model.vo.InsProductDetailTitleVO;
import com.sub.baoxian.model.vo.ProductDetailStructuredVO;
import com.sub.baoxian.model.vo.ProductDetailTreeVO;
import com.sub.baoxian.service.InsProductDetailService;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 产品详情控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/common/product-detail")
@RequiredArgsConstructor
public class InsProductDetailController {

    private final InsProductDetailService productDetailService;

    // ==================== 标题相关接口 ====================

    /**
     * 创建标题
     */
    @PostMapping("/titles")
    public Result<Integer> createTitle(@Valid @RequestBody InsProductDetailTitleDTO dto) {
        Integer id = productDetailService.createTitle(dto);
        return Result.success(id, "标题创建成功");
    }

    /**
     * 更新标题
     */
    @PostMapping("/titles/update")
    public Result<Boolean> updateTitle(@Valid @RequestBody InsProductDetailTitleDTO dto) {
        if (dto.getId() == null) {
            return Result.error(400, "更新标题时ID不能为空");
        }
        boolean success = productDetailService.updateTitle(dto);
        return success ? Result.success(true, "标题更新成功") : Result.error(500, "标题更新失败");
    }

    /**
     * 删除标题
     */
    @PostMapping("/titles/{id}/delete")
    public Result<Boolean> deleteTitle(@PathVariable Integer id) {
        boolean success = productDetailService.deleteTitle(id);
        return success ? Result.success(true, "标题删除成功") : Result.error(500, "标题删除失败");
    }

    /**
     * 根据ID查询标题
     */
    @GetMapping("/titles/{id}")
    public Result<InsProductDetailTitleVO> getTitleById(@PathVariable Integer id) {
        InsProductDetailTitleVO title = productDetailService.getTitleById(id);
        return title != null ? Result.success(title) : Result.error(404, "标题不存在");
    }

    /**
     * 根据分类ID查询标题树形结构
     */
    @GetMapping("/titles/tree")
    public Result<List<InsProductDetailTitleVO>> getTitleTree(@RequestParam Integer categoryId) {
        List<InsProductDetailTitleVO> titleTree = productDetailService.getTitleTreeByCategoryId(categoryId);
        return Result.success(titleTree);
    }

    /**
     * 分页查询标题
     */
    @GetMapping("/titles")
    public Result<IPage<InsProductDetailTitleVO>> getTitlePage(ProductDetailQueryDTO queryDTO) {
        IPage<InsProductDetailTitleVO> page = productDetailService.getTitlePage(queryDTO);
        return Result.success(page);
    }

    // ==================== 内容相关接口 ====================

    /**
     * 保存内容（创建或更新）
     */
    @PostMapping("/contents")
    public Result<Integer> saveContent(@Valid @RequestBody InsProductDetailContentDTO dto) {
        Integer id = productDetailService.saveContent(dto);
        return Result.success(id, "内容保存成功");
    }

    /**
     * 删除内容
     */
    @PostMapping("/contents/{id}/delete")
    public Result<Boolean> deleteContent(@PathVariable Integer id) {
        boolean success = productDetailService.deleteContent(id);
        return success ? Result.success(true, "内容删除成功") : Result.error(500, "内容删除失败");
    }

    /**
     * 根据产品ID和标题ID删除内容
     */
    @PostMapping("/contents/delete-by-product-title")
    public Result<Boolean> deleteContentByProductIdAndTitleId(@RequestParam Integer productId,
            @RequestParam Integer titleId) {
        boolean success = productDetailService.deleteContentByProductIdAndTitleId(productId, titleId);
        return success ? Result.success(true, "内容删除成功") : Result.error(500, "内容删除失败");
    }

    /**
     * 根据产品ID查询内容列表
     */
    @GetMapping("/contents/by-product")
    public Result<List<InsProductDetailContentVO>> getContentsByProductId(@RequestParam Integer productId) {
        List<InsProductDetailContentVO> contents = productDetailService.getContentsByProductId(productId);
        return Result.success(contents);
    }

    /**
     * 根据标题ID查询内容列表
     */
    @GetMapping("/contents/by-title")
    public Result<List<InsProductDetailContentVO>> getContentsByTitleId(@RequestParam Integer titleId) {
        List<InsProductDetailContentVO> contents = productDetailService.getContentsByTitleId(titleId);
        return Result.success(contents);
    }

    /**
     * 分页查询内容
     */
    @GetMapping("/contents")
    public Result<IPage<InsProductDetailContentVO>> getContentPage(ProductDetailQueryDTO queryDTO) {
        IPage<InsProductDetailContentVO> page = productDetailService.getContentPage(queryDTO);
        return Result.success(page);
    }

    // ==================== 综合查询接口 ====================

    /**
     * 根据分类ID和产品ID查询完整的产品详情树形结构
     */
    @GetMapping("/tree")
    public Result<List<ProductDetailStructuredVO>> getProductDetailTree(@RequestParam Integer categoryId,
            @RequestParam Integer productId) {
        List<ProductDetailStructuredVO> tree = productDetailService.getProductDetailStructured(categoryId,
            productId);  
        return Result.success(tree);
    }

    /**
    * 根据分类ID和产品ID查询原始的产品详情树形结构（保留原有接口）
    */
    @GetMapping("/tree/original")
    public Result<List<ProductDetailTreeVO>>getProductDetailTreeOriginal(@RequestParam Integer categoryId,
            @RequestParam Integer productId) {
            try {
               List<ProductDetailTreeVO> tree =  productDetailService.getProductDetailTree(categoryId, productId);
               return Result.success(tree);
            } catch (Exception e) {
                 log.error("查询产品详情树失败", e);
                 return Result.error(500, "查询产品详情树失败：" + e.getMessage());
            }
    }
}
