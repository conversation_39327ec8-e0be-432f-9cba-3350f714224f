package com.sub.baoxian.controller.common;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.model.entity.Products;
import com.sub.baoxian.service.LifeBeeProductService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 产品控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/common/product")
@RequiredArgsConstructor
public class LifeBeeProductController {

    private final LifeBeeProductService productService;

    /**
     * 分页查询产品列表
     * 
     * @param pageNum      页码(默认1)
     * @param pageSize     每页数量(默认10)
     * @param categoryCode 产品类别代码(可选)
     * @param name         产品名称(可选，用于模糊查询)
     * @param region       地区(可选)
     * @return 分页结果
     */
    @GetMapping("/page")
    public Result<IPage<Products>> getProductPage(
            @RequestParam(defaultValue = "1") long pageNum,
            @RequestParam(defaultValue = "10") long pageSize,
            @RequestParam(required = false) String categoryCode,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String region,
            @RequestParam(required = false) String companyCode,
            @RequestParam(required = false) String companyName,
            @RequestParam(required = false) boolean comparable) {

        Page<Products> page = new Page<>(pageNum, pageSize);
        IPage<Products> pageResult = productService.getProductPage(page, region, categoryCode, search, companyCode,
                companyName, comparable);
        return Result.success(pageResult);
    }

    /**
     * 获取产品详情
     * 
     * @param code 产品代码
     * @return 产品详情
     */
    @GetMapping("/{code}")
    public Result<Products> getProductDetail(@PathVariable String code) {
        return Result.success(productService.getProductByCode(code));
    }
}
