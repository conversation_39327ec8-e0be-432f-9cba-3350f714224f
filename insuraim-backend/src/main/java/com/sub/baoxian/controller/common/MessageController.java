package com.sub.baoxian.controller.common;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestParam;

import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.common.response.PageResult;
import com.sub.baoxian.model.entity.Message;
import com.sub.baoxian.service.MessageService;

@RestController
@RequestMapping("/api/common/message")
@RequiredArgsConstructor
public class MessageController {

    private final MessageService messageService;

    /**
     * 轮询接口- 获取未推送信息
     * 
     * @return 未推送信息
     */
    @GetMapping("/poll")
    public Result<List<Message>> pollMessage() {
        return Result.success(messageService.getUserUnpushMessage());
    }

    /**
     * 更新消息为已推送
     */
    @PostMapping("/push")
    public Result<Void> pushMessage(@RequestParam Long messageId) {
        messageService.pushMessage(messageId);
        return Result.success();
    }

    /**
     * 更新消息为已读状态
     */
    @PostMapping("/read")
    public Result<Void> readMessage(@RequestParam Long messageId) {
        messageService.readMessage(messageId);
        return Result.success();
    }

    /**
     * 获取所有信息列表
     */
    @GetMapping("/all")
    public Result<List<Message>> getAllMessages() {
        return Result.success(messageService.getAllMessages());
    }

    /**
     * 获取所有未读消息
     */
    @GetMapping("/unread")
    public Result<List<Message>> getAllUnreadMessages() {
        return Result.success(messageService.getAllUnreadMessages());
    }
    
    /**
     * 分页获取消息列表
     * 
     * @param page 页码，从1开始
     * @param pageSize 每页数量
     * @return 分页消息列表
     */
    @GetMapping("/paged")
    public Result<PageResult<Message>> getPagedMessages(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        return Result.success(messageService.getPagedMessages(page, pageSize));
    }
}
