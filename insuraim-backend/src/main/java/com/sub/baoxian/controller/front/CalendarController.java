package com.sub.baoxian.controller.front;

import org.springframework.web.bind.annotation.*;

import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.model.dto.CalendarDTO;
import com.sub.baoxian.service.CalendarService;

import lombok.RequiredArgsConstructor;

import java.util.List;

/**
 * 日程管理控制器
 */
@RestController
@RequestMapping("api/front/calendar")
@RequiredArgsConstructor
public class CalendarController {

    private final CalendarService calendarService;

    /**
     * 查询日程列表
     * 
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @param type 日程类型
     * @param status 完成状态
     * @return 日程列表
     */
    @GetMapping("/list")
    public Result<List<CalendarDTO>> list() {
        List<CalendarDTO> calendarList = calendarService.queryCalendars();
        return Result.success(calendarList);
    }
    
    /**
     * 获取日程详情
     * 
     * @param id 日程ID
     * @return 日程详情
     */
    @GetMapping("/{id}")
    public Result<CalendarDTO> getById(@PathVariable Long id) {
        CalendarDTO calendarDTO = calendarService.getCalendarById(id);
        if (calendarDTO == null) {
            return Result.error("日程不存在或无权限查看");
        }
        return Result.success(calendarDTO);
    }
    
    /**
     * 创建日程
     * 
     * @param calendarDTO 日程信息
     * @return 创建结果
     */
    @PostMapping("/create")
    public Result<Long> create(@RequestBody CalendarDTO calendarDTO) {
        Long id = calendarService.createCalendar(calendarDTO);
        return Result.success(id);
    }
    
    /**
     * 更新日程
     * 
     * @param calendarDTO 日程信息
     * @return 更新结果
     */
    @PutMapping("/update")
    public Result<Boolean> update(@RequestBody CalendarDTO calendarDTO) {
        boolean success = calendarService.updateCalendar(calendarDTO);
        if (!success) {
            return Result.error("更新失败，日程不存在或无权限修改");
        }
        return Result.success("更新成功");
    }
    
    /**
     * 删除日程
     * 
     * @param id 日程ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> delete(@PathVariable Long id) {
        boolean success = calendarService.deleteCalendar(id);
        if (!success) {
            return Result.error("删除失败，日程不存在或无权限删除");
        }
        return Result.success("删除成功");
    }
    
    /**
     * 更新日程状态
     * 
     * @param id 日程ID
     * @param status 状态值
     * @return 更新结果
     */
    @PutMapping("/{id}/status")
    public Result<Boolean> updateStatus(@PathVariable Long id, @RequestParam Integer status) {
        boolean success = calendarService.updateCalendarStatus(id, status);
        if (!success) {
            return Result.error("更新状态失败，日程不存在或无权限修改");
        }
        return Result.success("状态更新成功");
    }
}
