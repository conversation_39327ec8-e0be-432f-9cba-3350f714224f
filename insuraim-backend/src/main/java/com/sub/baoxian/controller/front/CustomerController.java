package com.sub.baoxian.controller.front;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.model.entity.Customer;
import com.sub.baoxian.service.CustomerService;

import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/api/front/customer")
@RequiredArgsConstructor
public class CustomerController {

    private final CustomerService customerService;

    /**
     * 分页获取客户列表
     */
    @GetMapping("/page")
    public Result<IPage<Customer>> getCustomerPage(@RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        return Result.success(customerService.getCustomerPageList(page, pageSize));
    }

    /**
     * 获取客户详情
     * 
     * @param id 客户ID
     */
    @GetMapping("/{id}")
    public Result<Customer> getCustomerDetail(@PathVariable Long id) {
        return Result.success(customerService.getCustomerById(id));
    }

}
