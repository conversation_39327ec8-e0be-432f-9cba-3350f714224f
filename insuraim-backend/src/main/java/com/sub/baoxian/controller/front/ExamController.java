package com.sub.baoxian.controller.front;

import java.util.List;

import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.model.entity.ExamCategory;
import com.sub.baoxian.model.vo.ExamQuestionVO;

import org.springframework.web.bind.annotation.*;

import com.sub.baoxian.service.ExamService;

import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/api/front/exam")
@RequiredArgsConstructor
public class ExamController {

    private final ExamService examService;

    /**
     * 获取模拟考试类目
     * 
     * @return
     */
    @GetMapping("/category")
    public Result<List<ExamCategory>> getExamCategoryList() {
        return Result.success(examService.getExamCategoryList());
    }

    /**
     * 获取模拟考试题目（使用LEFT JOIN优化查询，避免N+1问题）
     * 
     * @param categoryId
     * @return
     */
    @GetMapping("/question")
    public Result<List<ExamQuestionVO>> getExamQuestionList(@RequestParam Integer categoryId) {
        return Result.success(examService.getExamQuestionVOList(categoryId));
    }

}
