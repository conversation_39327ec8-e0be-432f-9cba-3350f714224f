package com.sub.baoxian.controller.front;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.model.dto.FileQueryDTO;
import com.sub.baoxian.model.entity.File;
import com.sub.baoxian.service.FileService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

import java.util.List;

/**
 * 文件控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/front/file")
public class FileController {

    private final FileService fileService;

    /**
     * 上传文件
     * 
     * @param file       文件
     * @param folderId   文件夹ID
     * @param uploaderId 上传者ID
     * @param category   分类
     * @return 文件信息
     */
    @PostMapping("/upload")
    public Result<File> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam("folderId") Long folderId,
            @RequestParam("uploaderId") Long uploaderId) throws IOException {
        File uploadedFile = fileService.uploadFile(file, folderId, uploaderId);
        return Result.success(uploadedFile, "文件上传成功");
    }

    /**
     * 获取文件信息
     * 
     * @param id 文件ID
     * @return 文件信息
     */
    @GetMapping("/{id}")
    public Result<File> getFile(@PathVariable Long id) {
        File file = fileService.getFileById(id);
        if (file == null) {
            return Result.error("文件不存在");
        }
        return Result.success(file);
    }

    /**
     * 获取文件夹中的文件列表
     * 
     * @param folderId 文件夹ID
     * @param fileType 文件类型
     * @return 文件列表
     */
    @GetMapping("/list")
    public Result<List<File>> listFiles(
            @RequestParam("folderId") Long folderId,
            @RequestParam(value = "fileType", required = false) String fileType) {
        List<File> files = fileService.listFiles(folderId, fileType);
        return Result.success(files);
    }

    /**
     * 分页查询文件
     * 
     * @param queryDTO 查询参数
     * @return 分页结果
     */
    @GetMapping("/page")
    public Result<Page<File>> pageFiles(FileQueryDTO queryDTO) {
        Page<File> page = fileService.pageFiles(
                queryDTO.getPage(),
                queryDTO.getSize(),
                queryDTO.getFolderId(),
                queryDTO.getKeyword());
        return Result.success(page);
    }

    /**
     * 更新文件信息
     * 
     * @param file 文件信息
     * @return 是否成功
     */
    @PutMapping
    public Result<Boolean> updateFile(@RequestBody File file) {
        boolean result = fileService.updateFile(file);
        return result ? Result.success(true, "更新成功") : Result.error("更新失败");
    }

    /**
     * 删除文件
     * 
     * @param id 文件ID
     * @return 是否成功
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> deleteFile(@PathVariable Long id) {
        boolean result = fileService.deleteFile(id);
        return result ? Result.success(true, "删除成功") : Result.error("删除失败");
    }

}