package com.sub.baoxian.controller.front;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.model.dto.FolderQueryDTO;
import com.sub.baoxian.model.entity.File;
import com.sub.baoxian.model.entity.Folder;
import com.sub.baoxian.model.vo.FolderContentVO;
import com.sub.baoxian.service.FileService;
import com.sub.baoxian.service.FolderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文件夹控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/front/folder")
public class FolderController {

    private final FolderService folderService;
    private final FileService fileService;

    /**
     * 创建文件夹
     * @param folder 文件夹信息
     * @return 创建的文件夹
     */
    @PostMapping
    public Result<Folder> createFolder(@RequestBody Folder folder) {
        folder.setCreateAt(LocalDateTime.now());
        folder.setUpdateAt(LocalDateTime.now());
        folder.setIsDeleted(false);

        Folder createdFolder = folderService.createFolder(folder);
        return Result.success(createdFolder, "文件夹创建成功");
    }

    /**
     * 获取文件夹信息
     * @param id 文件夹ID
     * @return 文件夹信息
     */
    @GetMapping("/{id}")
    public Result<Folder> getFolder(@PathVariable Long id) {
        Folder folder = folderService.getFolderById(id);
        if (folder == null) {
            return Result.error("文件夹不存在");
        }
        return Result.success(folder);
    }

    /**
     * 查询文件夹内容，包括子文件夹和文件
     * @param id 文件夹ID，如果为0则查询根目录
     * @param fileType 文件类型筛选(可选)
     * @return 文件夹内容
     */
    @GetMapping("/content/{id}")
    public Result<FolderContentVO> getFolderContent(
            @PathVariable Long id,
            @RequestParam(value = "fileType", required = false) String fileType) {

        // 获取当前文件夹信息
        Folder currentFolder = folderService.getFolderById(id);

        // 获取子文件夹列表
        List<Folder> subFolders = folderService.listFolders(id);

        // 获取文件列表
        List<File> files = fileService.listFiles(id, fileType);

        FolderContentVO contentVO = FolderContentVO.builder()
                .folderId(id)
                .currentFolder(currentFolder)
                .folders(subFolders)
                .files(files)
                .totalFolders((long) subFolders.size())
                .totalFiles((long) files.size())
                .build();

        return Result.success(contentVO);
    }

    /**
     * 获取文件夹树形结构
     * @param parentId 父级ID(可选)
     * @return 文件夹树
     */
    @GetMapping("/tree")
    public Result<List<Folder>> getFolderTree(
            @RequestParam(value = "parentId", required = false) Long parentId) {
        List<Folder> tree = folderService.getFolderTree(parentId);
        return Result.success(tree);
    }

    /**
     * 获取文件夹列表
     * @param parentId 父文件夹ID
     * @return 文件夹列表
     */
    @GetMapping("/list")
    public Result<List<Folder>> listFolders(
            @RequestParam(value = "parentId", required = true) Long parentId) {
        List<Folder> folders = folderService.listFolders(parentId);
        return Result.success(folders);
    }

    /**
     * 分页查询文件夹
     * @param queryDTO 查询参数
     * @return 分页结果
     */
    @GetMapping("/page")
    public Result<Page<Folder>> pageFolders(FolderQueryDTO queryDTO) {
        Page<Folder> page = folderService.pageFolders(
                queryDTO.getPage(),
                queryDTO.getSize(),
                queryDTO.getParentId(),
                queryDTO.getKeyword()
        );
        return Result.success(page);
    }

    /**
     * 更新文件夹信息
     * @param folder 文件夹信息
     * @return 是否成功
     */
    @PutMapping
    public Result<Boolean> updateFolder(@RequestBody Folder folder) {
        folder.setUpdateAt(LocalDateTime.now());
        boolean result = folderService.updateFolder(folder);
        return result ? Result.success(true, "更新成功") : Result.error("更新失败");
    }

    /**
     * 删除文件夹
     * @param id 文件夹ID
     * @return 是否成功
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> deleteFolder(@PathVariable Long id) {
        boolean result = folderService.deleteFolder(id);
        return result ? Result.success(true, "删除成功") : Result.error("删除失败");
    }
}