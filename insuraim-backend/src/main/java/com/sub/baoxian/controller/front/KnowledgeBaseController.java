package com.sub.baoxian.controller.front;

import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.model.entity.KnowledgeBase;
import com.sub.baoxian.model.entity.KnowledgeBaseCompany;
import com.sub.baoxian.model.vo.CategoryTreeVO;
import com.sub.baoxian.model.vo.getKnowledgeBaseByIdVO;
import com.sub.baoxian.service.KnowledgeBaseService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 知识库控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/front/knowledge-base")
public class KnowledgeBaseController {

    private final KnowledgeBaseService knowledgeBaseService;

    /**
     * 获取知识库保险公司信息列表
     */
    @GetMapping("/company-list")
    public Result<List<KnowledgeBaseCompany>> getCompanyList() {
        return Result.success(knowledgeBaseService.getCompanyList());
    }

    /**
     * 获取知识库专题列表（根据保险公司ID）
     */
    @GetMapping("/company-categories")
    public Result<Map<String, Object>> getCategoryListByCompanyId(@RequestParam Long companyId) {
        return Result.success(knowledgeBaseService.getCategoryListByCompanyId(companyId));
    }

    /**
     * 根据分类ID获取知识库信息列表
     */
    @GetMapping("/lore-list")
    public Result<List<KnowledgeBase>> getLoreList(@RequestParam Long categoryId) {
        return Result.success(knowledgeBaseService.getLoreListByCategoryId(categoryId));
    }

    /**
     * 获取知识库详情
     */
    @GetMapping("/lore-detail")
    public Result<getKnowledgeBaseByIdVO> getKnowledgeBaseById(@RequestParam Long id) {
        return Result.success(knowledgeBaseService.getKnowledgeBaseById(id));
    }

    /**
     * 获取完整分类树
     */
    @GetMapping("/category-tree")
    public Result<List<CategoryTreeVO>> getCategoryTree() {
        return Result.success(knowledgeBaseService.getCategoryTree());
    }

    /**
     * 根据公司ID获取分类树
     */
    @GetMapping("/category-tree/company/{companyId}")
    public Result<List<CategoryTreeVO>> getCategoryTreeByCompanyId(@PathVariable Long companyId) {
        return Result.success(knowledgeBaseService.getCategoryTreeByCompanyId(companyId));
    }

    /**
     * 根据分类ID获取其子分类树
     */
    @GetMapping("/category-tree/{categoryId}")
    public Result<CategoryTreeVO> getCategoryTreeById(@PathVariable Long categoryId) {
        return Result.success(knowledgeBaseService.getCategoryTreeById(categoryId));
    }

}