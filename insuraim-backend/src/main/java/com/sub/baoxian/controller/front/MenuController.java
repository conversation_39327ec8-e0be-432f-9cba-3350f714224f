package com.sub.baoxian.controller.front;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.model.vo.MenuTreeVO;
import com.sub.baoxian.service.MenuService;
import com.sub.baoxian.util.StpKit;

import lombok.RequiredArgsConstructor;

@RestController("frontMenuController")
@RequestMapping("/api/front/menu")
@RequiredArgsConstructor
public class MenuController {

    private final MenuService menuService;

    /**
     * 获取当前用户菜单列表
     */
    @GetMapping("/user/list")
    public Result<List<MenuTreeVO>> listUserMenu() {
        return Result.success(menuService.getCurrentUserMenuList(StpKit.USER.getLoginIdAsLong()));
    }

    /**
     * 获取所有菜单列表
     */
    @GetMapping("/all/list")
    public Result<List<MenuTreeVO>> listAll() {
        return Result.success(menuService.getMenuTree());
    }

}
