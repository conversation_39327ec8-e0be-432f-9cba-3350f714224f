package com.sub.baoxian.controller.front;

import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.service.PersonalCloudService;
import lombok.RequiredArgsConstructor;

import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * 个人云盘控制器
 */
@RestController
@RequestMapping("/api/front/personal-cloud")
@RequiredArgsConstructor
public class PersonalCloudDocsController {

    private final PersonalCloudService personalCloudService;

    /**
     * 获取用户云盘基本信息
     * @param userId 用户ID
     * @return 云盘信息
     */
    @GetMapping("/info")
    public Result<Map<String, Object>> getPersonalCloudInfo() {
        return Result.success(personalCloudService.getPersonalCloudInfo());
    }

    /**
     * 获取文件夹内容（文件夹和文件）
     * @param folderId 文件夹ID，null或0表示根目录
     * @param fileType 文件类型筛选，可选
     * @return 文件夹内容
     */
    @GetMapping("/folder-content")
    public Result<Map<String, Object>> getFolderContent(
            @RequestParam(required = false) Long folderId) {
        return Result.success(personalCloudService.getFolderContent(folderId));
    }

    /**
     * 创建文件夹
     * @param folderData 文件夹数据
     * @return 创建结果
     */
    @PostMapping("/folder")
    public Result<Map<String, Object>> createFolder(@RequestBody Map<String, Object> folderData) {
        personalCloudService.createFolder(folderData);
        return Result.success();
    }

    /**
     * 删除文件夹
     * @param folderId 文件夹ID
     * @return 删除结果
     */
    @DeleteMapping("/folder/{folderId}")
    public Result<Boolean> deleteFolder(@PathVariable Long folderId) {
        personalCloudService.deleteFolder(folderId);
        return Result.success();
    }

    /**
     * 上传文件
     * @param file 文件
     * @param folderId 文件夹ID
     * @param userId 用户ID
     * @return 上传结果
     */
    @PostMapping("/file/upload")
    public Result<Map<String, Object>> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam(required = false, defaultValue = "0") Long folderId) {
        personalCloudService.uploadFile(file, folderId);
        return Result.success();
    }

    /**
     * 删除文件
     * @param fileId 文件ID
     * @return 删除结果
     */
    @DeleteMapping("/file/{fileId}")
    public Result<Boolean> deleteFile(@PathVariable Long fileId) {
        personalCloudService.deleteFile(fileId);
        return Result.success();
    }

    /**
     * 获取文件下载URL
     * @param fileId 文件ID
     * @return 文件下载信息
     */
    @GetMapping("/file/download/{fileId}")
    public Result<Map<String, Object>> getFileDownloadUrl(@PathVariable Long fileId) {
        return Result.success(personalCloudService.getFileDownloadUrl(fileId));
    }
}
