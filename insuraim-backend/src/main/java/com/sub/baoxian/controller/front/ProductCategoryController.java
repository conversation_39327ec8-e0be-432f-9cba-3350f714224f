package com.sub.baoxian.controller.front;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.model.entity.ProductCategories;
import com.sub.baoxian.service.ProductCategoryService;

import lombok.RequiredArgsConstructor;

@RequestMapping("/api/front/product-category")
@RestController
@RequiredArgsConstructor
public class ProductCategoryController {

    private final ProductCategoryService productCategoryService;

    @GetMapping("/list")
    public Result<List<ProductCategories>> list() {
        return Result.success(productCategoryService.list());
    }

}
