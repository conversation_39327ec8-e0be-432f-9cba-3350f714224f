package com.sub.baoxian.controller.front;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.core.playwright.dto.ProposalRequestDTO;
import com.sub.baoxian.model.entity.ProposalRecord;
import com.sub.baoxian.policymanagement.model.dto.ProposalCalculateRequestDTO;
import com.sub.baoxian.policymanagement.model.vo.ProposalCalculateVO;
import com.sub.baoxian.service.ProposalService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 保险计划书控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/front/proposal")
@RequiredArgsConstructor
public class ProposalController {

    private final ProposalService proposalService;

    /**
     * 生成保险计划书（异步方式）
     * 
     * @param request 请求参数
     * @return 计划书ID
     */
    @PostMapping("/generate")
    public Result<Long> generateProposal(@RequestBody ProposalRequestDTO request) {
        Long proposalId = proposalService.generateProposal(request);
        return Result.success(proposalId, "计划书生成任务已提交，请稍后查询结果");
    }

    /**
     * 获取当前用户的计划书分页列表
     * 
     * @param pageNum  页码
     * @param pageSize 每页条数
     * @return 计划书分页列表
     */
    @GetMapping("/page")
    public Result<Page<ProposalRecord>> pageOfMine(
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "customerName", required = false) String customerName,
            @RequestParam(value = "status", required = false) String status) {

        Page<ProposalRecord> page = proposalService.pageOfMine(pageNum, pageSize, customerName, status);
        return Result.success(page);
    }

    /**
     * 获取计划书详情
     * 
     * @param id 计划书ID
     * @return 计划书详情
     */
    @GetMapping("/{id}")
    public Result<ProposalRecord> getById(@PathVariable("id") Long id) {
        ProposalRecord proposal = proposalService.getById(id);
        return Result.success(proposal);
    }

    /**
     * 轮询计划书生成状态
     * 
     * @param id 计划书ID
     * @return 计划书生成状态
     */
    @GetMapping("/status/{id}")
    public Result<String> getStatus(@PathVariable("id") Long id) {
        String status = proposalService.getStatus(id);
        return Result.success(status);
    }

    /**
     * 生成计划书演算
     *
     * @param request 请求参数
     * @return 计算结果
     */
    @PostMapping("/calculate/excel")
    public Result<ProposalCalculateVO> calculateForExcel(@RequestBody ProposalCalculateRequestDTO request) {
        ProposalCalculateVO result = proposalService.calculateForExcel(request);
        return Result.success(result);
    }

}
