package com.sub.baoxian.controller.front;

import java.util.HashMap;
import java.util.Map;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.model.dto.UserLoginDTO;
import com.sub.baoxian.model.dto.UserRegisterDTO;
import com.sub.baoxian.model.entity.User;
import com.sub.baoxian.model.vo.UserLoginVO;
import com.sub.baoxian.service.UserService;
import com.sub.baoxian.util.StpKit;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;

@RestController("frontUserController")
@RequestMapping("/api/front/user")
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;

    /**
     * 用户注册
     * 
     * @param userRegisterDTO 用户注册信息
     * @return 注册结果
     */
    @PostMapping("/register")
    public Result<Void> register(@RequestBody UserRegisterDTO userRegisterDTO) {
        if (userRegisterDTO.getUsername() == null || userRegisterDTO.getUsername().trim().isEmpty()) {
            return Result.error("用户名不能为空");
        }
        if (userRegisterDTO.getPassword() == null || userRegisterDTO.getPassword().trim().isEmpty()) {
            return Result.error("密码不能为空");
        }
        if (userRegisterDTO.getEmail() == null || userRegisterDTO.getEmail().trim().isEmpty()) {
            return Result.error("邮箱不能为空");
        }

        boolean success = userService.register(userRegisterDTO.getUsername(), userRegisterDTO.getPassword(),
                userRegisterDTO.getEmail());
        return success ? Result.success("注册成功") : Result.error("注册失败");
    }

    /**
     * 用户登录
     * 
     * @param userLoginDTO 用户登录信息
     * @return 登录结果
     */
    @PostMapping("/login")
    public Result<Map<String, Object>> login(@RequestBody UserLoginDTO userLoginDTO, HttpServletRequest request) {
        if (userLoginDTO.getUsername() == null) {
            return Result.error("用户名不能为空");
        }
        if (userLoginDTO.getPassword() == null || userLoginDTO.getPassword().trim().isEmpty()) {
            return Result.error("密码不能为空");
        }
        if (userLoginDTO.getEmail() == null) {
            return Result.error("邮箱不能为空");
        }
        if (userLoginDTO.getOrgCode() == null) {
            return Result.error("组织代码不能为空");
        }

        Map<String, Object> data = new HashMap<>();
        UserLoginVO userLoginVO = userService.login(userLoginDTO, request);
        data.put("user", userLoginVO);
        data.put("token", StpKit.USER.getTokenValue());
        return Result.success(data);
    }

    /**
     * 获取当前登录用户信息
     * 
     * @return 用户信息
     */
    @GetMapping("/info")
    public Result<User> getUserInfo() {

        // 获取当前登录用户ID
        Integer userId = StpKit.USER.getLoginIdAsInt();
        User user = userService.getUserById(userId);

        if (user == null) {
            return Result.error(401, "用户不存在");
        }

        // 清除敏感信息
        user.setPassword(null);

        return Result.success(user);
    }

    /**
     * 用户登出
     * 
     * @return 登出结果
     */
    @PostMapping("/logout")
    public Result<Void> logout() {
        StpKit.USER.logout();
        return Result.success("登出成功");
    }

    /**
     * 检查是否登录
     * 
     * @return 登录状态
     */
    @GetMapping("/checkLogin")
    public Result<Boolean> checkLogin() {
        return Result.success(StpKit.USER.isLogin());
    }

}