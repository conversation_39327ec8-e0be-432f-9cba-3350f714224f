package com.sub.baoxian.controller.product;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.productInfoService.ProductInfoProviderSecond;
import com.sub.baoxian.productInfoService.ProductInfoProviderService;
import com.sub.baoxian.productInfoService.Ao.InsurancePostAo;
import com.sub.baoxian.productInfoService.Ao.ProductCalculateAo;
import com.sub.baoxian.productInfoService.Ao.ProductIntroAO;
import com.sub.baoxian.productInfoService.Ao.RealizationRateAo;
import com.sub.baoxian.productInfoService.entity.InsuranceFeePo;
import com.sub.baoxian.productInfoService.entity.InsurancePostPo;
import com.sub.baoxian.productInfoService.entity.ProductAttributeAO;
import com.sub.baoxian.productInfoService.entity.ProductIntroPO;
import com.sub.baoxian.productInfoService.entity.RealizationRatePo;
import com.sub.baoxian.productInfoService.entity.RealizedRateStarPo;
import com.sub.baoxian.productInfoService.utils.InsuranceCost;
import com.sub.baoxian.productInfoService.utils.PageResult;

import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/api/front/china-life-product-list")
@RequiredArgsConstructor
public class ProductFrontController {
    @Autowired
    private ProductInfoProviderService productService;
    @Autowired
    private ProductInfoProviderSecond productServiceSecond;
    @Autowired
    private InsuranceCost insuranceCost;

    /**
     * 根据产品ID获取产品详细信息
     * @param id 产品ID
     * @return 包含产品详细信息的JSON对象
     * @throws JsonProcessingException 当JSON处理出错时抛出异常
     */
    @RequestMapping(value = "getProductById", method = RequestMethod.GET)
    public Result<ObjectNode> getProductById(@RequestParam Integer id) throws JsonProcessingException {
        return Result.success(productService.getInfoOfProducts(id));
    }

    /**
     * 分页获取产品基本信息列表
     * @param pageNum 页码，默认值为1
     * @param pageSize 每页大小，默认值为10
     * @param search 搜索关键词（可选）
     * @param mainType 产品主类型（可选）
     * @param region 地区（可选）
     * @param insurer 保险公司（可选）
     * @return 分页后的产品基本信息列表
     * @throws JsonProcessingException 当JSON处理出错时抛出异常
     */
    @RequestMapping(value = "getBasicInfo", method = RequestMethod.GET)
    public Result<PageResult<ProductIntroPO>> getInfoOfProducts(@RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String mainType,
            @RequestParam(required = false) String region,
            @RequestParam(required = false) String insurer) throws JsonProcessingException {
        // search = ZhConverterUtil.toTraditional(search);
        // mainType = ZhConverterUtil.toTraditional(mainType);
        return Result
                .success(productService.getBasicInfoOfProducts(pageNum, pageSize, search, mainType, region, insurer));
    }

    /**
     * 获取产品介绍信息
     * @param productId 产品ID
     * @return 产品介绍信息对象
     * @throws JsonProcessingException 当JSON处理出错时抛出异常
     */
    @RequestMapping(value = "getIntroInfo", method = RequestMethod.GET)
    public Result<ProductIntroAO> getIntroInfoOfProducts(@RequestParam Integer productId)
            throws JsonProcessingException {
        return Result.success(productService.getIntroInfoOfProducts(productId));
    }

    /**
     * 获取所有产品类型列表
     * @return 产品类型字符串列表
     * @throws JsonProcessingException 当JSON处理出错时抛出异常
     */
    @RequestMapping(value = "productCategoryList", method = RequestMethod.GET)
    public Result<List<String>> getSearchResult() throws JsonProcessingException {
        return Result.success(productService.productCategoryList());
    }

    /**
     * 获取产品代码信息
     * @param productId 产品ID
     * @return 产品属性列表
     * @throws JsonProcessingException 当JSON处理出错时抛出异常
     */
    @RequestMapping(value = "getProductCode", method = RequestMethod.GET)
    public Result<List<ProductAttributeAO>> getProductCode(@RequestParam Integer productId)
            throws JsonProcessingException {
        return Result.success(productService.getSingleAttributeOfProduct(productId));
    }

    /**
     * 获取产品宣传册下载信息
     * @param productId 产品ID
     * @return 产品宣传册属性列表
     * @throws JsonProcessingException 当JSON处理出错时抛出异常
     */
    @RequestMapping(value = "getDownloadBrochure", method = RequestMethod.GET)
    public Result<List<ProductAttributeAO>> getDownloadBrochure(@RequestParam Integer productId)
            throws JsonProcessingException {
        return Result.success(productService.getDownloadOfProduct(productId));
    }

    /**
     * 获取产品的IRR和分红率信息
     * @param productId 产品ID
     * @return 包含IRR和分红率的产品属性对象
     * @throws JsonProcessingException 当JSON处理出错时抛出异常
     */
    @RequestMapping(value = "getIRRAndDividend", method = RequestMethod.GET)
    public Result<ProductAttributeAO> getIRRAndDividend(@RequestParam Integer productId)
            throws JsonProcessingException {

        return Result.success(productService.getIRRAndDividendOfProduct(productId));
    }

    /**
     * 分页获取可进行保费试算的产品列表
     * @param pageNum 页码，默认值为1
     * @param pageSize 每页大小，默认值为10
     * @param search 搜索关键词（可选）
     * @param type 产品类型（可选）
     * @param insurer 保险公司（可选）
     * @param region 地区（可选）
     * @return 分页后的保费试算产品列表
     * @throws JsonProcessingException 当JSON处理出错时抛出异常
     */
    @RequestMapping(value = "displayCalculationProducts", method = RequestMethod.GET)
    public Result<PageResult<InsuranceFeePo>> displayCalculationProducts(@RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String insurer,
            @RequestParam(required = false) String region) throws JsonProcessingException {
//        search = ZhConverterUtil.toTraditional(search);
        return Result.success(insuranceCost.getCalculationProducts(pageNum, pageSize, search, type, insurer, region));
    }

    /**
     * 获取保费计算结果
     * @param id 产品ID
     * @param age 投保年龄
     * @param insuredFee 保额，默认值为1
     * @param paymentMode 缴费方式，默认值为2
     * @return 计算后的保费金额
     * @throws JsonProcessingException 当JSON处理出错时抛出异常
     */
    @RequestMapping(value = "getCalculationFactors", method = RequestMethod.GET)
    public Result<Float> getCalculationFactors(@RequestParam Integer id, @RequestParam Integer age,
            @RequestParam(defaultValue = "1") Integer insuredFee, @RequestParam(defaultValue = "2") Integer paymentMode)
            throws JsonProcessingException {
        return Result.success(insuranceCost.getInsuranceCost(id, age, insuredFee, paymentMode));
    }

    /**
     * 获取可进行保费试算的产品类型列表
     * @return 产品类型字符串列表
     * @throws JsonProcessingException 当JSON处理出错时抛出异常
     */
    @RequestMapping(value = "displayCalculationType", method = RequestMethod.GET)
    public Result<List<String>> displayCalculationType() throws JsonProcessingException {
        return Result.success(insuranceCost.getCalculationType());
    }

    //可以计算单复利产品的展示的接口
//    @RequestMapping(value = "displaySingleAndDoubleInterestProducts", method = RequestMethod.GET)
//    public Result<PageResult<InsurancePostPo>> displaySingleAndDoubleInterestProducts(
//
//    })

    /**
     * 获取特定年份的单复利计算结果
     * @param productId 产品ID
     * @param year 计算年份
     * @param age 投保年龄
     * @return 包含单复利计算结果的对象
     */
    @RequestMapping(value = "getSingleAndDoubleInterest", method = RequestMethod.GET)
    public Result<ProductCalculateAo> displaySpecificYearIrr(@RequestParam Integer productId,
            @RequestParam Integer year, @RequestParam Integer age) {
        return Result.success(productService.getSpecificYearIrr(productId, year,age));
    }

    /**
     * 分页获取产品实现率名称列表
     * @param pageNum 页码，默认值为1
     * @param pageSize 每页大小，默认值为10
     * @return 分页后的产品实现率名称列表
     * @throws JsonProcessingException 当JSON处理出错时抛出异常
     */
    @RequestMapping(value = "getProductRealizationName", method = RequestMethod.GET)
    public Result<PageResult<RealizationRatePo>> getProductRealizationName(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize)
            throws JsonProcessingException {
        return Result.success(productServiceSecond.getRealizationRateProductName(pageNum, pageSize));
    }

    /**
     * 获取产品实现率星级列表
     * @return 产品实现率星级列表
     */
    @RequestMapping(value = "getProductRealizationStar", method = RequestMethod.GET)
    public Result<List<RealizedRateStarPo>> getProductRealizationStar() {
        return Result.success(productServiceSecond.getRealizationRateProductStar());
    }

    /**
     * 分页获取产品实现率详细信息
     * @param pageNum 页码，默认值为1
     * @param pageSize 每页大小，默认值为10
     * @param search 搜索关键词（可选）
     * @param insurer 保险公司（可选）
     * @param region 地区（可选）
     * @return 分页后的产品实现率详细信息列表
     * @throws JsonProcessingException 当JSON处理出错时抛出异常
     */
    @RequestMapping(value = "getProductRealizationDetails", method = RequestMethod.GET)
    public Result<IPage<RealizationRateAo>> getProductRealizationDetails(@RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String insurer,
            @RequestParam(required = false) String region)
            throws JsonProcessingException {
        // search = ZhTwConverterUtil.toTraditional(search);
        return Result.success(
                productServiceSecond.getProductRealizationDetailsRe(pageNum, pageSize, search, insurer, region));
    }

    /**
     * 分页获取保险海报列表
     * @param pageNum 页码，默认值为1
     * @param pageSize 每页大小，默认值为10
     * @param search 搜索关键词（可选）
     * @return 分页后的保险海报列表
     * @throws JsonProcessingException 当JSON处理出错时抛出异常
     */
    @RequestMapping(value = "getInsurancePoster", method = RequestMethod.GET)
    public Result<PageResult<InsurancePostPo>> getInsurancePoster(@RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String search) throws JsonProcessingException {
        return Result.success(productServiceSecond.getBasicInfoOfPostInsurance(pageNum, pageSize, search));
    }

    /**
     * 获取保险海报详细信息
     * @param id 海报ID
     * @return 保险海报详细信息对象
     * @throws JsonProcessingException 当JSON处理出错时抛出异常
     */
    @RequestMapping(value = "getInsurancePosterDetails", method = RequestMethod.GET)
    public Result<InsurancePostAo> getInsurancePosterDetails(@RequestParam Integer id) throws JsonProcessingException {
        return Result.success(productServiceSecond.getPostGeneratorInfo(id));
    }





}
