package com.sub.baoxian.controller.product;

import java.math.BigDecimal;
import java.util.List;

import com.sub.baoxian.productInfoService.DTO.ProfitCalculationRequestDTO;
import com.sub.baoxian.productInfoService.SavingDynamicCalService;
import com.sub.baoxian.productInfoService.entity.FileLinkPo;
import com.sub.baoxian.productInfoService.entity.ProductCalDisplayPo;
import com.sub.baoxian.productInfoService.vo.CalculationDataVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.productInfoService.ProductInfoProviderSecond;
import com.sub.baoxian.productInfoService.ProductInfoProviderService;
import com.sub.baoxian.productInfoService.Ao.ProductCalculateAo;
import com.sub.baoxian.productInfoService.DTO.ProductDisplayDTO;
import com.sub.baoxian.productInfoService.entity.ProductIntroPO;
import com.sub.baoxian.productInfoService.utils.PageResult;

@RestController
@RequestMapping(value ="/api/front/interest")
public class ProductInterestCal {
    @Autowired
    ProductInfoProviderService productInfoProviderService;
    @Autowired
    private ProductInfoProviderSecond productServiceSecond;
    @Autowired
    private SavingDynamicCalService savingDynamicCalService;

    @RequestMapping(value = "IRRCurrency",method = RequestMethod.GET)
    public Result<List<String>> IRRProductCurrency() {
        return Result.success(productInfoProviderService.productCurrencyList());
    }

    //展示可以计算IRR的产品
    @RequestMapping(value = "productCalDisplay",method = RequestMethod.POST)
    public Result<PageResult<ProductIntroPO>> productIrrDisplay(@RequestBody ProductDisplayDTO productDisplayDTO) throws JsonProcessingException {
        return Result.success(productInfoProviderService.getBasicInfoOfIRRProducts(productDisplayDTO));
    }

    @RequestMapping(value = "productCalDisplayV2",method = RequestMethod.POST)
    public Result<PageResult<ProductCalDisplayPo>> productIrrDisplayV2(@RequestBody ProductDisplayDTO productDisplayDTO) throws JsonProcessingException {
            return Result.success(productInfoProviderService.ProductsOfCashValue(productDisplayDTO));
    }
    
    //动态计算IRR的接口
    @RequestMapping(value = "calculateProfitDynamic", method = RequestMethod.GET)
    public Result<ProductCalculateAo> calculateProfitDynamic(@RequestParam Integer productId,
                                                             @RequestParam BigDecimal premium, @RequestParam Integer holdYear, @RequestParam Integer startAge) {
        return Result.success(productInfoProviderService.getProfitCalculate(productId, premium, holdYear, startAge));
    }

    @RequestMapping(value = "calculateProfitV2", method = RequestMethod.POST)
    public Result<CalculationDataVO> calculateProfit(@RequestBody ProfitCalculationRequestDTO profitCalculationRequestDTO) {
        return Result.success(savingDynamicCalService.classifyAgeGetProduct(profitCalculationRequestDTO));
    }
    @RequestMapping(value = "productPlanLink", method = RequestMethod.GET)
    public Result<List<FileLinkPo>> productPlanLink(@RequestParam(required = false) String productName,
                                                    @RequestParam(required = false) String age,
                                                    @RequestParam(required = false) String companyName,
                                                    @RequestParam(required = false) Integer mode) {
        if(mode !=null && mode ==1) {
            return Result.success(savingDynamicCalService.getCompany());
        }
        return Result.success(savingDynamicCalService.getFileLink(productName, age,companyName));

    }


}
