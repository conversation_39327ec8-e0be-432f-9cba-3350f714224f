package com.sub.baoxian.core.langchain.assistant;

import dev.langchain4j.service.SystemMessage;
import dev.langchain4j.service.TokenStream;
import dev.langchain4j.service.UserMessage;

public interface EmbeddingAssistant {

    /**
     * 根据用户提供的上下文（通过ContentRetriever隐式处理）和查询进行流式聊天。
     * AiServices 会将此方法链接到配置的 StreamingChatModel 和 ContentRetriever。
     *
     * @param query 用户的提问。
     * @return TokenStream 包含AI响应的流式对象，可以使用流式API处理响应和事件。
     */
    @SystemMessage("你是一个专业的保险顾问，请根据客户的需求，给出最合适的回答")
    TokenStream chat(@UserMessage String query);
}
