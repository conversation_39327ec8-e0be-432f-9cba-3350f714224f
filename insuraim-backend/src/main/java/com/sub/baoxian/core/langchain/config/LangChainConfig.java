package com.sub.baoxian.core.langchain.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import dev.langchain4j.model.chat.StreamingChatModel;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.openai.OpenAiChatModel;
import dev.langchain4j.model.openai.OpenAiEmbeddingModel;
import dev.langchain4j.model.openai.OpenAiStreamingChatModel;

@Configuration
public class LangChainConfig {

    @Bean
    StreamingChatModel streamingChatModel() {
        return OpenAiStreamingChatModel.builder()
                .apiKey("sk-urdlcxuoyhgtxqruemzdogsahymqtncbnkljnwljbrkuzifv")
                .baseUrl("https://api.siliconflow.cn/v1")
                .modelName("deepseek-ai/DeepSeek-V3")
                .build();
    }

    @Bean
    ChatModel chatModel() {
        return OpenAiChatModel.builder()
                .apiKey("sk-urdlcxuoyhgtxqruemzdogsahymqtncbnkljnwljbrkuzifv")
                .baseUrl("https://api.siliconflow.cn/v1")
                .modelName("deepseek-ai/DeepSeek-V3")
                .build();
    }

    @Bean
    EmbeddingModel embeddingModel() {
        return OpenAiEmbeddingModel.builder()
                .apiKey("sk-JrYrJGaCMxBDTmOCWCdWQo9ncvAIbuxZEZIZIQoB8O9rIjCq")
                .baseUrl("https://api.damoxing.site/v1")
                .modelName("text-embedding-3-small")
                .build();
    }
}