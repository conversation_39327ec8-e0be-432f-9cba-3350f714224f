package com.sub.baoxian.core.langchain.controller;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.core.langchain.service.ChatService;

import dev.langchain4j.service.TokenStream;
import jakarta.servlet.http.HttpServletResponse;

@RestController
public class ChatController {

    @Autowired
    private ChatService chatService;

    private final ExecutorService sseExecutor = Executors.newCachedThreadPool();

    @GetMapping(value = "/api/chat/completion", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter analyzePdfStream(@RequestParam("sessionId") String sessionId,
            @RequestParam("query") String query, HttpServletResponse response) {
        response.setHeader("X-Accel-Buffering", "no");
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("Connection", "keep-alive");
        SseEmitter emitter = new SseEmitter(Long.MAX_VALUE);

        sseExecutor.execute(() -> {
            try {
                TokenStream tokenStream = chatService.processQuery(sessionId, query);

                tokenStream.onPartialResponse((String token) -> {
                    try {
                        if (token != null && !token.isEmpty()) {
                            emitter.send(SseEmitter.event().data(token));
                            System.out.print(token);
                        }
                    } catch (IOException e) {
                        System.err.println("Error sending SSE event: " + e.getMessage());
                    }
                })
                        .onCompleteResponse((res) -> {
                            try {
                                emitter.send(SseEmitter.event().name("COMPLETE").data("Stream finished"));
                                emitter.complete();
                            } catch (IOException e) {
                                System.err.println("Error sending completion event: " + e.getMessage());
                                emitter.completeWithError(e);
                            }
                        })
                        .onError((error) -> {
                            try {
                                emitter.send(SseEmitter.event().name("ERROR").data(error.getMessage()));
                                emitter.completeWithError(error);
                            } catch (IOException e) {
                                System.err.println("Error sending error event: " + e.getMessage());
                                emitter.completeWithError(error);
                            }
                        })
                        .start(); // 开始流式处理
            } catch (Exception e) {
                emitter.completeWithError(e);
            }
        });

        emitter.onTimeout(() -> System.out.println("SSE emitter timed out for query: " + query));
        emitter.onError((throwable) -> System.err
                .println("SSE emitter error for query: " + query + ", error: " + throwable.getMessage()));
        emitter.onCompletion(() -> System.out.println("SSE emitter completed for query: " + query));

        return emitter;
    }

    /**
     * 创建sessionID
     */
    @PostMapping("/api/chat/session/create")
    public Result<Map<String, String>> createSession(@RequestParam String modal) {
        String sessionId = chatService.createSession(modal);
        Map<String, String> result = new HashMap<>();
        result.put("sessionId", sessionId);
        return Result.success(result);
    }

    /**
     * 处理文件嵌入
     */
    @PostMapping("/api/chat/assistant/create")
    public Result<Boolean> createSessionWithPdf(@RequestBody Map<String, String> requestBody) {
        String sessionId = requestBody.get("sessionId");
        Long proposalId = Long.parseLong(requestBody.get("proposalId"));
        Boolean result = chatService.createSessionWithPdf(sessionId, proposalId);
        return Result.success(result);
    }

    /**
     * 清除会话
     */
    @PostMapping("/api/chat/session/clear")
    public Result<Boolean> clearSession(@RequestBody String sessionId) {
        Boolean result = chatService.removeSession(sessionId);
        return Result.success(result);
    }

}
