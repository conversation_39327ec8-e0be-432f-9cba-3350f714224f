package com.sub.baoxian.core.langchain.model;

import java.io.InputStream;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.sub.baoxian.core.langchain.assistant.EmbeddingAssistant;
import com.sub.baoxian.core.langchain.assistant.NormalAssistant;

import lombok.Data;

/**
 * 会话模型类，用于管理用户会话和对应的PDF文档处理结果
 */ 
@Data
public class Session implements Serializable {
    private static final long serialVersionUID = 1L;

    private String sessionId;
    private EmbeddingAssistant embeddingAssistant;
    private NormalAssistant normalAssistant;
    private LocalDateTime lastAccessTime;
    private InputStream pdfInputStream;
    private String modal;

    public Session(String sessionId) {
        this.sessionId = sessionId;
        this.lastAccessTime = LocalDateTime.now();
    }
}