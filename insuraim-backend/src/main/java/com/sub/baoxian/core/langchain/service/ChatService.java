package com.sub.baoxian.core.langchain.service;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.sub.baoxian.common.exception.BizException;
import com.sub.baoxian.core.langchain.assistant.EmbeddingAssistant;
import com.sub.baoxian.core.langchain.assistant.NormalAssistant;
import com.sub.baoxian.core.langchain.model.Session;
import com.sub.baoxian.model.entity.ProposalRecord;
import com.sub.baoxian.service.ProposalService;

import dev.langchain4j.data.document.Document;
import dev.langchain4j.data.document.parser.apache.pdfbox.ApachePdfBoxDocumentParser;
import dev.langchain4j.data.document.splitter.DocumentByParagraphSplitter;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.memory.chat.MessageWindowChatMemory;
import dev.langchain4j.model.chat.StreamingChatModel;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.rag.content.retriever.EmbeddingStoreContentRetriever;
import dev.langchain4j.service.AiServices;
import dev.langchain4j.service.TokenStream;
import dev.langchain4j.store.embedding.EmbeddingStoreIngestor;
import dev.langchain4j.store.embedding.inmemory.InMemoryEmbeddingStore;

@Service
public class ChatService {

    private static final Logger logger = LoggerFactory.getLogger(ChatService.class);

    @Autowired
    private EmbeddingModel embeddingModel;

    @Autowired
    private SessionManager sessionManager;

    @Autowired
    private StreamingChatModel streamingChatModel;

    @Autowired
    private ProposalService proposalService;

    /**
     * 处理PDF并创建嵌入助手
     * 
     * @param inputStream PDF文件输入流
     * @return AssistantEmbedding实例
     */
    private EmbeddingAssistant processPdf(InputStream inputStream) {
        Document document = new ApachePdfBoxDocumentParser().parse(inputStream);
        InMemoryEmbeddingStore<TextSegment> embeddingStore = new InMemoryEmbeddingStore<>();

        DocumentByParagraphSplitter splitter = new DocumentByParagraphSplitter(1024, 0);

        List<TextSegment> segments = splitter.split(document);
        EmbeddingStoreIngestor ingestor = EmbeddingStoreIngestor.builder()
                .embeddingModel(embeddingModel)
                .embeddingStore(embeddingStore)
                .build();

        List<Document> documents = new ArrayList<>();
        for (TextSegment segment : segments) {
            Document chunkDoc = Document.from(segment.text(), segment.metadata());
            documents.add(chunkDoc);
        }
        ingestor.ingest(documents);

        EmbeddingStoreContentRetriever retriever = EmbeddingStoreContentRetriever.builder()
                .embeddingStore(embeddingStore)
                .embeddingModel(embeddingModel)
                .build();

        EmbeddingAssistant embeddingAssistant = AiServices.builder(EmbeddingAssistant.class)
                .streamingChatModel(streamingChatModel)
                .contentRetriever(retriever)
                .chatMemory(MessageWindowChatMemory.withMaxMessages(10))
                .build();

        return embeddingAssistant;
    }

    /**
     * 创建嵌入对话助手
     * 
     * @param sessionId      会话ID
     * @param pdfInputStream PDF文件的输入流
     * @return 嵌入对话助手
     */
    public Boolean createEmbeddingAssistant(String sessionId, InputStream pdfInputStream) {
        Session session = sessionManager.getSession(sessionId);
        if (session == null) {
            throw new IllegalArgumentException("无效的会话ID: " + sessionId);
        }
        EmbeddingAssistant assistant = processPdf(pdfInputStream);
        session.setEmbeddingAssistant(assistant);
        session.setPdfInputStream(pdfInputStream);
        session.setModal("embedding");
        return true;
    }

    /**
     * 创建普通助手
     * @param sessionId
     * @param proposalId
     * @return
     */
    public Boolean createNormalAssistant(String sessionId) {
        Session session = sessionManager.getSession(sessionId);
        if (session == null) {
            throw new IllegalArgumentException("无效的会话ID: " + sessionId);
        }
        session.setNormalAssistant(AiServices.builder(NormalAssistant.class)
                .streamingChatModel(streamingChatModel)
                .chatMemory(MessageWindowChatMemory.withMaxMessages(10))
                .build());
        session.setModal("normal");
        return true;
    }
    /**
     * 处理pdf
     * 
     * @param sessionId      会话ID
     * @param pdfInputStream PDF文件的输入流
     * @return 是否处理成功
     */
    public Boolean createSessionWithPdf(String sessionId, Long proposalId) {
        ProposalRecord proposal = proposalService.getById(proposalId);
        if (proposal == null) {
            throw new IllegalArgumentException("无效的计划书ID: " + proposalId);
        }
        InputStream pdfInputStream = proposalService.getProposalPdfStream(proposalId, proposal.getUserId());
        return createEmbeddingAssistant(sessionId, pdfInputStream);
    }

    /**
     * 创建seesionID
     * 
     * @param pdfInputStream
     * @return
     * @throws IOException
     */
    public String createSession(String modal) {
        String sessionId = sessionManager.createSession();
        if (modal.equals("embedding")) {
            createEmbeddingAssistant(sessionId, null);
        } else if (modal.equals("normal")) {
            createNormalAssistant(sessionId);
        } else {
            throw new BizException("无效的模型: " + modal);
        }
        return sessionId;
    }

    /**
     * 处理用户查询，使用现有会话进行流式响应
     * 
     * @param sessionId 会话ID
     * @param userQuery 用户的查询字符串
     * @return TokenStream 包含AI响应的流式对象
     */
    public TokenStream processQuery(String sessionId, String userQuery) {
        Session session = sessionManager.getSession(sessionId);
        if (session == null) {
            throw new IllegalArgumentException("无效的会话ID: " + sessionId);
        }
        logger.info("处理用户查询: sessionId={}, query={}", sessionId, userQuery);
        if (session.getModal().equals("embedding")) {
            return session.getEmbeddingAssistant().chat(userQuery);
        } else if (session.getModal().equals("normal")) {
            return session.getNormalAssistant().chat(userQuery);
        } else {
            throw new BizException("无效的模型: " + session.getModal());
        }
    }

    /**
     * 检查会话是否存在
     * 
     * @param sessionId 会话ID
     * @return 如果会话存在返回true，否则返回false
     */
    public boolean isSessionValid(String sessionId) {
        return sessionManager.sessionExists(sessionId);
    }

    /**
     * 删除会话
     * 
     * @param sessionId 会话ID
     * @return 如果删除成功返回true，否则返回false
     */
    public boolean removeSession(String sessionId) {
        return sessionManager.removeSession(sessionId);
    }

}
