package com.sub.baoxian.core.langchain.service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.sub.baoxian.core.langchain.model.Session;

/**
 * 会话管理服务，负责管理用户会话和相关的AssistantEmbedding实例
 */
@Service
public class SessionManager {

    private static final Logger logger = LoggerFactory.getLogger(SessionManager.class);

    // 使用ConcurrentHashMap保证线程安全
    private final Map<String, Session> sessions = new ConcurrentHashMap<>();

    /**
     * 创建新会话
     * 
     * @param assistant    已处理的嵌入助手实例
     * @param documentHash PDF文档的哈希值
     * @return 会话ID
     */
    public String createSession() {
        String sessionId = UUID.randomUUID().toString();
        Session session = new Session(sessionId);
        sessions.put(sessionId, session);
        logger.info("创建新会话: sessionId={}", sessionId);
        return sessionId;
    }

    /**
     * 获取会话
     * 
     * @param sessionId 会话ID
     * @return Session对象，如果不存在则返回null
     */
    public Session getSession(String sessionId) {
        Session session = sessions.get(sessionId);
        if (session != null) {
            // 更新最后访问时间
            session.setLastAccessTime(LocalDateTime.now());
            logger.debug("访问会话: sessionId={}", sessionId);
        } else {
            logger.warn("会话不存在: sessionId={}", sessionId);
        }
        return session;
    }

    /**
     * 检查会话是否存在
     * 
     * @param sessionId 会话ID
     * @return 如果会话存在返回true，否则返回false
     */
    public boolean sessionExists(String sessionId) {
        return sessions.containsKey(sessionId);
    }

    /**
     * 删除会话
     * 
     * @param sessionId 会话ID
     * @return 如果删除成功返回true，否则返回false
     */
    public boolean removeSession(String sessionId) {
        Session removedSession = sessions.remove(sessionId);
        if (removedSession != null) {
            logger.info("删除会话: sessionId={}", sessionId);
            return true;
        }
        return false;
    }

    /**
     * 清理过期会话（可以设置定时任务调用此方法）
     * 
     * @param expirationMinutes 过期时间（分钟）
     * @return 清理的会话数量
     */
    public int cleanupExpiredSessions(int expirationMinutes) {
        LocalDateTime expirationTime = LocalDateTime.now().minusMinutes(expirationMinutes);
        int removedCount = 0;

        sessions.entrySet().removeIf(entry -> {
            if (entry.getValue().getLastAccessTime().isBefore(expirationTime)) {
                logger.info("清理过期会话: sessionId={}, lastAccessTime={}",
                        entry.getKey(), entry.getValue().getLastAccessTime());
                return true;
            }
            return false;
        });

        if (removedCount > 0) {
            logger.info("清理了 {} 个过期会话", removedCount);
        }

        return removedCount;
    }

    /**
     * 获取当前活跃会话数量
     * 
     * @return 活跃会话数量
     */
    public int getActiveSessionCount() {
        return sessions.size();
    }
}