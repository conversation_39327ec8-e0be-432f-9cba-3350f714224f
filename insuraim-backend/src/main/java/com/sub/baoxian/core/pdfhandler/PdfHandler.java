package com.sub.baoxian.core.pdfhandler;

import java.io.InputStream;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.sub.baoxian.core.pdfhandler.factory.PdfStrategyFactory;
import com.sub.baoxian.core.pdfhandler.inter.PdfProcessStrategy;

/**
 * PDF处理类
 */
@Component
public class PdfHandler {

    @Autowired
    private PdfStrategyFactory strategyFactory;

    /**
     * 处理PDF文档
     * 
     * @param inputStream  PDF输入流
     * @param strategyType 策略类型
     * @return 处理后的PDF输入流
     */
    public InputStream processPdf(InputStream inputStream, String strategyType) {
        PdfProcessStrategy strategy = strategyFactory.getStrategy(strategyType);
        return strategy.processPdf(inputStream);
    }

}
