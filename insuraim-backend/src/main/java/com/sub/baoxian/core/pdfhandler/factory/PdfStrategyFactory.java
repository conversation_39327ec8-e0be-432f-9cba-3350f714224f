package com.sub.baoxian.core.pdfhandler.factory;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.sub.baoxian.core.pdfhandler.inter.PdfProcessStrategy;
import com.sub.baoxian.core.pdfhandler.strategy.DefaultPdfStrategy;
import com.sub.baoxian.core.pdfhandler.strategy.MEDICALPdfStrategy;
import com.sub.baoxian.core.pdfhandler.strategy.SpecialPdfStrategy;

/**
 * PDF处理策略工厂
 * 根据不同条件选择合适的PDF处理策略
 */
@Component
public class PdfStrategyFactory {

    private final Map<String, PdfProcessStrategy> strategyMap = new HashMap<>();

    public PdfStrategyFactory(DefaultPdfStrategy defaultStrategy, MEDICALPdfStrategy medicalStrategy,
            SpecialPdfStrategy specialStrategy) {
        // 注册各种策略
        strategyMap.put("DEFAULT", defaultStrategy);
        strategyMap.put("MEDICAL", medicalStrategy);
        strategyMap.put("SPECIAL", specialStrategy);
    }

    /**
     * 根据策略类型获取PDF处理策略
     * 
     * @param strategyType 策略类型
     * @return PDF处理策略
     */
    public PdfProcessStrategy getStrategy(String strategyType) {
        return strategyMap.getOrDefault(strategyType, strategyMap.get("DEFAULT"));
    }
}