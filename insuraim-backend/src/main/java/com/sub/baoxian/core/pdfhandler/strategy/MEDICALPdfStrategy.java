package com.sub.baoxian.core.pdfhandler.strategy;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;

import org.springframework.stereotype.Component;

import com.sub.baoxian.common.exception.BizException;
import com.sub.baoxian.core.pdfhandler.inter.PdfProcessStrategy;
import com.sub.baoxian.core.pdfhandler.util.PdfWatermarkUtil;

@Component
public class MEDICALPdfStrategy implements PdfProcessStrategy {

    @Override
    public InputStream processPdf(InputStream inputStream) {
        try {
            PDDocument document = PDDocument.load(inputStream);
            for (PDPage page : document.getPages()) {
                // 获取页面尺寸
                PDRectangle pageSize = page.getMediaBox();

                float pageHeight = pageSize.getHeight();
                // 在左上角画一个矩形
                try (PDPageContentStream contentStream = new PDPageContentStream(
                        document, page, PDPageContentStream.AppendMode.APPEND, true)) {
                    contentStream.setNonStrokingColor(0, 0, 0, 0); // 白色 (CMYK)
                    contentStream.addRect(0, pageHeight - 65, 250, 100);
                    contentStream.fill();
                }

                // 添加水印
                PdfWatermarkUtil.addChineseWatermark(document, page);
            }

            // 在第一页下面
            PDPage page2 = document.getPage(0);
            PDPageContentStream contentStream = new PDPageContentStream(document, page2,
                    PDPageContentStream.AppendMode.APPEND, true);
            contentStream.setNonStrokingColor(0, 0, 0, 0); // 白色 (CMYK)
            contentStream.addRect(30, 30, 550, 200);
            contentStream.fill();
            contentStream.close();

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            document.save(outputStream);
            document.close();
            return new ByteArrayInputStream(outputStream.toByteArray());
        } catch (IOException e) {
            throw new BizException("处理PDF失败", e);
        }
    }

}
