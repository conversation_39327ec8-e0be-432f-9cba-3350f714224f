package com.sub.baoxian.core.pdfhandler.strategy;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;

import org.springframework.stereotype.Component;

import com.sub.baoxian.core.pdfhandler.inter.PdfProcessStrategy;
import com.sub.baoxian.core.pdfhandler.util.PdfWatermarkUtil;

@Component
public class SpecialPdfStrategy implements PdfProcessStrategy {

    @Override
    public InputStream processPdf(InputStream inputStream) {
        try {
            PDDocument document = PDDocument.load(inputStream);
            document.removePage(0);
            for (PDPage page : document.getPages()) {
                // 获取页面尺寸
                PDRectangle pageSize = page.getMediaBox();
                float pageWidth = pageSize.getWidth();
                float pageHeight = pageSize.getHeight();

                // 在Logo位置画白色矩形（假设Logo在右上角，范围250x50）
                try (PDPageContentStream contentStream = new PDPageContentStream(
                        document, page, PDPageContentStream.AppendMode.APPEND, true)) {
                    contentStream.setNonStrokingColor(0, 0, 0, 0); // 白色 (CMYK)
                    contentStream.addRect(pageWidth - 250, pageHeight - 50, 250, 50); // (x, y, width, height)
                    contentStream.fill();

                    // 在左下角画一个矩形
                    contentStream.addRect(0, 0, 250, 60);
                    contentStream.fill();
                }

                // 添加水印
                PdfWatermarkUtil.addChineseWatermark(document, page);
            }
            // 保存修改后的PDF
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            document.save(outputStream);
            document.close();
            return new ByteArrayInputStream(outputStream.toByteArray());
        } catch (IOException e) {
            throw new RuntimeException("Failed to process PDF", e);
        }
    }

}