package com.sub.baoxian.core.pdfhandler.util;

import java.io.IOException;
import java.io.InputStream;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.font.PDFont;
import org.apache.pdfbox.pdmodel.font.PDType0Font;
import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.apache.pdfbox.pdmodel.graphics.state.PDExtendedGraphicsState;
import org.apache.pdfbox.util.Matrix;

/**
 * PDF水印工具类
 * 提供为PDF页面添加水印的功能
 */
public class PdfWatermarkUtil {

    /**
     * 默认水印文本
     */
    private static final String DEFAULT_WATERMARK_TEXT = "TEST DATA";

    /**
     * 默认字体大小
     */
    private static final float DEFAULT_FONT_SIZE = 70f;

    /**
     * 默认透明度
     */
    private static final float DEFAULT_ALPHA = 0.3f;

    /**
     * 默认旋转角度（弧度）
     */
    private static final double DEFAULT_ROTATION = Math.toRadians(45);

    /**
     * 为PDF页面添加默认水印
     *
     * @param document PDF文档
     * @param page     PDF页面
     * @throws IOException IO异常
     */
    public static void addWatermark(PDDocument document, PDPage page) throws IOException {
        addWatermark(document, page, DEFAULT_WATERMARK_TEXT);
    }

    /**
     * 为PDF页面添加中文水印
     *
     * @param document PDF文档
     * @param page     PDF页面
     * @throws IOException IO异常
     */
    public static void addChineseWatermark(PDDocument document, PDPage page) throws IOException {
        addChineseWatermark(document, page, "演示数据");
    }

    /**
     * 为PDF页面添加自定义文本水印
     *
     * @param document      PDF文档
     * @param page          PDF页面
     * @param watermarkText 水印文本
     * @throws IOException IO异常
     */
    public static void addWatermark(PDDocument document, PDPage page, String watermarkText) throws IOException {
        addWatermark(document, page, watermarkText, DEFAULT_FONT_SIZE, DEFAULT_ALPHA, DEFAULT_ROTATION);
    }

    /**
     * 为PDF页面添加完全自定义的水印
     *
     * @param document      PDF文档
     * @param page          PDF页面
     * @param watermarkText 水印文本
     * @param fontSize      字体大小
     * @param alpha         透明度 (0.0-1.0)
     * @param rotation      旋转角度（弧度）
     * @throws IOException IO异常
     */
    public static void addWatermark(PDDocument document, PDPage page, String watermarkText,
            float fontSize, float alpha, double rotation) throws IOException {
        if (watermarkText == null || watermarkText.trim().isEmpty()) {
            return; // 如果水印文本为空，则不添加水印
        }

        PDRectangle pageSize = page.getMediaBox();
        float pageWidth = pageSize.getWidth();
        float pageHeight = pageSize.getHeight();

        try (PDPageContentStream contentStream = new PDPageContentStream(
                document, page, PDPageContentStream.AppendMode.APPEND, true)) {

            // 设置透明度
            PDExtendedGraphicsState graphicsState = new PDExtendedGraphicsState();
            graphicsState.setNonStrokingAlphaConstant(alpha);
            contentStream.setGraphicsStateParameters(graphicsState);

            // 设置字体和颜色
            contentStream.setFont(PDType1Font.HELVETICA_BOLD, fontSize);
            contentStream.setNonStrokingColor(0.7f, 0.7f, 0.7f); // 灰色

            // 计算水印文本的位置（页面中心）
            float textWidth = PDType1Font.HELVETICA_BOLD.getStringWidth(watermarkText) / 1000 * fontSize;
            float x = (pageWidth - textWidth) / 2;
            float y = pageHeight / 2;

            // 开始文本
            contentStream.beginText();

            // 创建变换矩阵
            Matrix matrix = new Matrix();
            matrix.translate(x, y);
            matrix.rotate(rotation);

            // 设置文本位置和旋转角度
            contentStream.setTextMatrix(matrix);

            // 显示文本
            contentStream.showText(watermarkText);
            contentStream.endText();
        }
    }

    /**
     * 为PDF页面添加多个水印（平铺效果）
     *
     * @param document      PDF文档
     * @param page          PDF页面
     * @param watermarkText 水印文本
     * @param fontSize      字体大小
     * @param alpha         透明度 (0.0-1.0)
     * @param rotation      旋转角度（弧度）
     * @param spacing       水印间距
     * @throws IOException IO异常
     */
    public static void addTiledWatermark(PDDocument document, PDPage page, String watermarkText,
            float fontSize, float alpha, double rotation, float spacing) throws IOException {
        if (watermarkText == null || watermarkText.trim().isEmpty()) {
            return;
        }

        PDRectangle pageSize = page.getMediaBox();
        float pageWidth = pageSize.getWidth();
        float pageHeight = pageSize.getHeight();

        try (PDPageContentStream contentStream = new PDPageContentStream(
                document, page, PDPageContentStream.AppendMode.APPEND, true)) {

            // 设置透明度
            PDExtendedGraphicsState graphicsState = new PDExtendedGraphicsState();
            graphicsState.setNonStrokingAlphaConstant(alpha);
            contentStream.setGraphicsStateParameters(graphicsState);

            // 设置字体和颜色
            contentStream.setFont(PDType1Font.HELVETICA_BOLD, fontSize);
            contentStream.setNonStrokingColor(0.7f, 0.7f, 0.7f);

            // 计算文本尺寸
            float textWidth = PDType1Font.HELVETICA_BOLD.getStringWidth(watermarkText) / 1000 * fontSize;

            // 计算平铺的行列数
            int cols = (int) Math.ceil(pageWidth / (textWidth + spacing));
            int rows = (int) Math.ceil(pageHeight / (fontSize + spacing));

            // 开始文本
            contentStream.beginText();

            // 平铺水印
            for (int row = 0; row < rows; row++) {
                for (int col = 0; col < cols; col++) {
                    float x = col * (textWidth + spacing);
                    float y = row * (fontSize + spacing);

                    // 创建变换矩阵
                    Matrix matrix = new Matrix();
                    matrix.translate(x, y);
                    matrix.rotate(rotation);

                    // 设置文本位置和旋转角度
                    contentStream.setTextMatrix(matrix);

                    // 显示文本
                    contentStream.showText(watermarkText);
                }
            }

            contentStream.endText();
        }
    }

    /**
     * 为PDF页面添加中文水印
     *
     * @param document      PDF文档
     * @param page          PDF页面
     * @param watermarkText 水印文本
     * @throws IOException IO异常
     */
    public static void addChineseWatermark(PDDocument document, PDPage page, String watermarkText) throws IOException {
        if (watermarkText == null || watermarkText.trim().isEmpty()) {
            return; // 如果水印文本为空，则不添加水印
        }

        PDRectangle pageSize = page.getMediaBox();
        float pageWidth = pageSize.getWidth();
        float pageHeight = pageSize.getHeight();

        try (PDPageContentStream contentStream = new PDPageContentStream(
                document, page, PDPageContentStream.AppendMode.APPEND, true)) {

            // 设置透明度
            PDExtendedGraphicsState graphicsState = new PDExtendedGraphicsState();
            graphicsState.setNonStrokingAlphaConstant(DEFAULT_ALPHA);
            contentStream.setGraphicsStateParameters(graphicsState);

            PDFont font;
            float textWidth;

            try {
                // 尝试加载支持中文的字体
                font = loadChineseFont(document);
                if (font != null) {
                    // 使用中文字体
                    contentStream.setFont(font, DEFAULT_FONT_SIZE);
                    textWidth = font.getStringWidth(watermarkText) / 1000 * DEFAULT_FONT_SIZE;
                } else {
                    // 降级到英文字体和文本
                    font = PDType1Font.HELVETICA_BOLD;
                    watermarkText = "TEST DATA";
                    contentStream.setFont(font, DEFAULT_FONT_SIZE);
                    textWidth = font.getStringWidth(watermarkText) / 1000 * DEFAULT_FONT_SIZE;
                }
            } catch (Exception e) {
                // 如果加载中文字体失败，使用英文字体
                font = PDType1Font.HELVETICA_BOLD;
                watermarkText = "TEST DATA";
                contentStream.setFont(font, DEFAULT_FONT_SIZE);
                textWidth = font.getStringWidth(watermarkText) / 1000 * DEFAULT_FONT_SIZE;
            }

            // 设置颜色
            contentStream.setNonStrokingColor(0.7f, 0.7f, 0.7f); // 灰色

            // 计算水印文本的位置（页面中心）
            float x = (pageWidth - textWidth) / 2;
            float y = pageHeight / 2;

            // 开始文本
            contentStream.beginText();

            // 创建变换矩阵
            Matrix matrix = new Matrix();
            matrix.translate(x, y);
            matrix.rotate(DEFAULT_ROTATION);

            // 设置文本位置和旋转角度
            contentStream.setTextMatrix(matrix);

            // 显示文本
            contentStream.showText(watermarkText);
            contentStream.endText();
        }
    }

    /**
     * 加载支持中文的字体
     *
     * @param document PDF文档
     * @return 支持中文的字体，如果加载失败返回null
     */
    private static PDFont loadChineseFont(PDDocument document) {
        try {
            // 尝试从系统加载常见的中文字体
            String[] fontPaths = {
                    // Windows 系统字体
                    "C:/Windows/Fonts/simsun.ttc",
                    "C:/Windows/Fonts/simhei.ttf",
                    "C:/Windows/Fonts/msyh.ttc",
                    // macOS 系统字体
                    "/System/Library/Fonts/PingFang.ttc",
                    "/System/Library/Fonts/Hiragino Sans GB.ttc",
                    "/Library/Fonts/Arial Unicode MS.ttf",
                    // Linux 系统字体
                    "/usr/share/fonts/truetype/droid/DroidSansFallbackFull.ttf",
                    "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
                    "/usr/share/fonts/truetype/arphic/uming.ttc"
            };

            for (String fontPath : fontPaths) {
                try {
                    java.io.File fontFile = new java.io.File(fontPath);
                    if (fontFile.exists()) {
                        return PDType0Font.load(document, fontFile);
                    }
                } catch (Exception e) {
                    // 继续尝试下一个字体
                    continue;
                }
            }

            // 如果系统字体都不可用，尝试使用内置的字体资源
            try {
                InputStream fontStream = PdfWatermarkUtil.class.getClassLoader()
                        .getResourceAsStream("fonts/NotoSansSC.ttf");
                if (fontStream != null) {
                    return PDType0Font.load(document, fontStream);
                }
            } catch (Exception e) {
                // 忽略，继续下面的处理
            }

            return null; // 无法加载中文字体
        } catch (Exception e) {
            return null;
        }
    }
}
