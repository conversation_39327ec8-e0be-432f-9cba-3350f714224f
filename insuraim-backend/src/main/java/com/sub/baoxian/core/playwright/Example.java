package com.sub.baoxian.core.playwright;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

import cn.hutool.http.HttpUtil;

import com.microsoft.playwright.Browser;
import com.microsoft.playwright.BrowserContext;
import com.microsoft.playwright.BrowserType;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Mouse;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.Playwright;
import com.microsoft.playwright.Response;
import com.microsoft.playwright.options.BoundingBox;
import com.microsoft.playwright.options.LoadState;
import com.microsoft.playwright.options.WaitForSelectorState;
import com.sub.baoxian.common.exception.BizException;
import com.sub.baoxian.util.OssUtil;

public class Example {
    // 网站相关常量
    private static final String BASE_URL = "https://onepartner.chinalife.com.hk/";
    private static final String USERNAME = "ifaone";
    private static final String PASSWORD = "$Clio2628";
    private static final String urlTeamp = "https://onepartner.chinalife.com.hk/epos/proposal/?onePartnerToken=%s&type=op-front-web&isAdmin=false";

    public static void main(String[] args) {
        Playwright playwright = Playwright.create();
        Browser browser = null;
        BrowserContext context = null;
        Page page = null;
        String token = "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJCS0lMjk5IiwiYWRtaW5Vc2VySWQiOm51bGwsInByZUNvZGUiOiJudWxsIiwicmVhbFVzZXJUeXBlIjoiMyIsIm5vZGVObyI6Ijk4MjAiLCJzdGFmZm5vIjoiQktJUzI5OSIsImhxRmxhZyI6IjAiLCJjZXJ0aWZpY2F0ZWlkIjoiTklMIiwiaXNzIjoiaWZhb25lIiwiY2hhbm5lbCI6IkJLSVMyIiwic2VydmljZU5hbWUiOiJvcC1zZXJ2aWNlLXdlYiIsInBhcmVudElkIjoiQktJUzI5OSIsImJyYW5jaENvZGUiOiI4MTAxMDAiLCJhdWQiOiJhZ2VudCIsIm9yaWdpblR5cGUiOiIzIiwiYWRtaW5DaGFubmVsIjpudWxsLCJ1c2VyVHlwZSI6IjMiLCJpYXQiOjE3NDQ4NzQyNDk5NDEsImp0aSI6ImlmYW9uZSIsIm9mZmljZU5vIjoiQlIyIn0.F_-atWXHUHSiThlHIdcn5Tp0dR_fysrMUVTjTNx60Pw";

        try {
            browser = playwright.chromium().launch(new BrowserType.LaunchOptions().setHeadless(false));

            // 创建上下文和页面
            context = browser.newContext();
            page = context.newPage();

            // 获取token
            if (checkToken(page, token)) {
                // 打开带token的特定页面
                openProposalPage(context, token);
            } else {
                login(page);
                page.waitForLoadState(LoadState.DOMCONTENTLOADED);
                token = getToken(page);
                refreshToken(page);
                openProposalPage(context, token);
            }
            Map<String, Object> info = new HashMap<>();
            // 货币类型
            info.put("currency", "USD");
            info.put("age", "30");
            info.put("smoke", false);
            info.put("type", "儲蓄");
            info.put("amount", "10000");
            fillInfo(page, info, context);

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 确保资源正确关闭
            if (context != null) {
                context.close();
            }
            if (browser != null) {
                browser.close();
            }
            if (playwright != null) {
                playwright.close();
            }
        }
    }

    /**
     * 刷新token
     * 
     * @param page
     */
    private static void refreshToken(Page page) {
        // TODO: 刷新token
        
    }

    /**
     * 获取token
     * 
     * @param page 页面实例
     * @return token
     */
    private static String getToken(Page page) {
        // 从sessionStorage中获取userInfo
        String userInfoJson = (String) page.evaluate("() => sessionStorage.getItem('userInfo')");
        // 使用正则表达式提取token字段
        if (userInfoJson != null && !userInfoJson.isEmpty()) {
            String tokenRegex = "\"token\"\\s*:\\s*\"([^\"]+)\"";
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(tokenRegex);
            java.util.regex.Matcher matcher = pattern.matcher(userInfoJson);
            if (matcher.find()) {
                return matcher.group(1);
            }
        }
        return null;
    }

    /**
     * 登录操作
     * 
     * @param page 页面实例
     */
    private static void login(Page page) {
        System.out.println("开始登录...");
        page.navigate(BASE_URL);

        // 获取输入框,填入内容
        page.locator("//*[@id=\"login\"]/div[1]/div[2]/div[6]/div[1]/div[1]/input").fill(USERNAME);
        page.locator("//*[@id=\"login\"]/div[1]/div[2]/div[6]/div[2]/div[1]/input").fill(PASSWORD);

        // 滑动验证
        slideVerification(page);

        // 等待验证通过
        page.waitForSelector("#dragText:has-text('驗證通過')",
                new Page.WaitForSelectorOptions().setTimeout(10000));

        // 等待一段时间确保验证完成
        page.waitForTimeout(2000);

        Response loginResponse = page.waitForResponse(
            response -> response.url().contains("/op-service-authentication/login_new"),
                () -> {
                    // 点击登录按钮
                    page.locator("//*[@id=\"login\"]/div[1]/div[2]/div[6]/button").click();
                });
        if(loginResponse.status() != 200){
            throw new BizException("登录失败");
        }
        System.out.println("登录成功");
    }

    /**
     * 填入相关信息
     * 
     * @param page 页面实例
     */
    public static void fillInfo(Page page, Map<String, Object> info, BrowserContext context) {
        // 默认男/非吸烟
        page.locator("label.el-radio:has-text('女')").first().click();
        page.locator("input[type='text'][role='spinbutton']").first().fill(info.get("age").toString());
        page.locator(".el-select-group.basePlanList span").click();
        page.waitForTimeout(2000);
        page.locator(".el-select-group.basePlanList span").click();
        page.locator("ul.el-select-pop li.select_item:has-text('裕饒傳承儲蓄保險計劃II')").first().click();
        page.locator(".el-dialog__footer .button").nth(2).click();
        Locator amountInput = page.locator(".step1_slide2 .slide2_left .currencyNum .el-input__inner");
        amountInput.click();
        amountInput.press("Control+A");
        amountInput.press("Backspace");
        page.keyboard().type(info.get("amount").toString());
        page.locator("body").click();
        page.locator(".home_footer .nextStep .button").click();
        page.waitForLoadState(LoadState.LOAD);
        page.locator(".home_footer .home_footer_bottom .nextStep .button").nth(1).click();
        page.waitForSelector(".step3_label", new Page.WaitForSelectorOptions().setTimeout(10000));
        Locator proposalPDF = page.locator("//*[@id=\"step3\"]/div[1]/p[2]/span");
        Page newPage = context.waitForPage(() -> {
            proposalPDF.click();
        });
        newPage.waitForLoadState(LoadState.NETWORKIDLE);
        String newPageUrl = newPage.url();
        
        System.out.println("新标签页URL: " + newPageUrl);
        
        // 下载PDF并上传到OSS
        String ossFileUrl = downloadAndUploadPdfToOss(newPageUrl);
        System.out.println("OSS文件URL: " + ossFileUrl);
    }

    /**
     * 下载PDF并上传到OSS
     * 
     * @param pdfUrl PDF文件URL
     * @return OSS中的文件URL
     */
    private static String downloadAndUploadPdfToOss(String pdfUrl) {
        try {
            System.out.println("开始下载PDF: " + pdfUrl);
            
            // 使用Hutool的HttpUtil下载PDF内容
            byte[] pdfContent = HttpUtil.downloadBytes(pdfUrl);
            
            if (pdfContent == null || pdfContent.length == 0) {
                throw new BizException("PDF下载失败，内容为空");
            }
            
            System.out.println("PDF下载成功，文件大小: " + pdfContent.length + " 字节");
            
            // 生成文件名
            String fileName = "proposal_" + System.currentTimeMillis() + ".pdf";
            
            // 上传到OSS
            String ossUrl = OssUtil.uploadBytes(pdfContent, "insuriam/files", fileName);
            
            System.out.println("PDF上传到OSS成功");
            
            return ossUrl;
        } catch (Exception e) {
            System.err.println("下载并上传PDF失败: " + e.getMessage());
            e.printStackTrace();
            throw new BizException("下载并上传PDF失败: " + e.getMessage(), e);
        }
    }

    /**
     * 滑动验证
     * 
     * @param page 页面实例
     */
    public static void slideVerification(Page page) {
        Locator slider = page.locator("#dragHandler");
        slider.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE));
        BoundingBox sliderBox = slider.boundingBox();
        int startX = (int) sliderBox.x;
        int startY = (int) (sliderBox.y + sliderBox.height / 2);
        int targetX = startX + 270;
        slider.hover();
        page.mouse().down();
        Random random = new Random();
        int moved = 0;
        while (moved < 270) {
            int step = 10 + random.nextInt(10);
            if (moved + step > 270) {
                step = 270 - moved;
            }

            int currentY = startY + random.nextInt(5) - 2;

            moved += step;
            page.mouse().move(
                    startX + moved,
                    currentY,
                    new Mouse.MoveOptions().setSteps(1));
        }
        page.mouse().move(targetX, startY);
        page.mouse().move(targetX + 3, startY);
        page.waitForTimeout(100);
        page.mouse().move(targetX, startY);
        page.waitForTimeout(100);
        page.mouse().up();
    }

    /**
     * 打开带token的特定页面
     * 
     * @param context 浏览器上下文
     * @param token   用户token
     */
    private static void openProposalPage(BrowserContext context, String token) {
        try {
            System.out.println("开始打开计划书页面...");

            // 拼接完整URL
            String baseProposalUrl = "https://onepartner.chinalife.com.hk/epos/proposal/?onePartnerToken=";
            String additionalParams = "&type=op-front-web&isAdmin=false";
            String fullUrl = baseProposalUrl + token + additionalParams;

            // 在当前会话中打开新页面
            Page proposalPage = context.pages().get(0);
            proposalPage.navigate(fullUrl);

            // 等待页面加载完成
            proposalPage.waitForLoadState(LoadState.NETWORKIDLE);

        } catch (Exception e) {
            System.err.println("打开计划书页面失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 判断token是否失效
     * 
     * @param page 页面实例
     */
    private static boolean checkToken(Page page, String token) {
        System.out.println("检查token是否失效");
        page.navigate(String.format(urlTeamp, token));
        page.waitForLoadState(LoadState.NETWORKIDLE);
        Locator homeBody = page.locator(".el-dialog__wrapper.infoDialog");
        if (homeBody.isVisible()) {
            System.out.println("token有效");
            return true;
        } else {
            System.out.println("token失效");
            return false;
        }
    }
}
