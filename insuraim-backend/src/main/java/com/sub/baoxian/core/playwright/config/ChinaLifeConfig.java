package com.sub.baoxian.core.playwright.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

/**
 * 中国人寿网站相关配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "chinalife")
public class ChinaLifeConfig {
    
    /**
     * 网站基础URL
     */
    private String baseUrl = "https://onepartner.chinalife.com.hk/";
    
    /**
     * 计划书URL模板
     */
    private String proposalUrlTemplate = "https://onepartner.chinalife.com.hk/epos/proposal/?onePartnerToken=%s&type=op-front-web&isAdmin=false";
    
    /**
     * 登录用户名
     */
    private String username = "ifaone";
    
    /**
     * 登录密码
     */
    private String password = "$Clio2628";
    
    /**
     * Token缓存键前缀
     */
    private String tokenCachePrefix = "chinalife:token";
    
    /**
     * Token过期时间（秒）
     */
    private long tokenExpireSeconds = 7200;
    
    /**
     * OSS文件存储路径
     */
    private String ossFilePath = "Insuriam/files";
} 