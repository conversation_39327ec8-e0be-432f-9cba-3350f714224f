package com.sub.baoxian.core.playwright.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 保险计划书请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProposalRequestDTO {

    /**
     * 被保人姓名
     */
    private String customerName;

    /**
     * 被保人年龄
     */
    private Integer age;

    /**
     * 被保人性别（female:女, male:男）
     */
    private String gender;

    /**
     * 是否吸烟
     */
    private Boolean isSmoking;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 保费类型 0-保额 1-保费
     */
    private Integer amountType;

    /**
     * 金额
     */
    private String amount;

    /**
     * 货币类型（HKD, USD, RMB等）
     */
    private String currency;

    /**
     * 保险产品名称
     */
    private String productName;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 语言
     */
    private String language;

    /**
     * 地区
     */
    private String region;

    /**
     * 产品类型
     */
    private String productType;

    /**
     * 预缴年份
     */
    private String prepaymentYear;

    /**
     * 缴费方式
     */
    private String paymentMethod;

    /**
     * 是否启用自定金额提取
     */
    private boolean enableCustomWithdrawal;

    /**
     * 是否启用锁定终期红利
     */
    private boolean enableLockEndBonus;

    /**
     * 提取起始年龄
     */
    private Integer withdrawalStartAge;

    /**
     * 提取结束年龄
     */
    private Integer withdrawalEndAge;

    /**
     * 初始提取金额
     */
    private Integer initialWithdrawalAmount;

    /**
     * 提取增长率
     */
    private Integer withdrawalGrowthRate;

    /**
     * 行使权益时最小年龄
     */
    private Integer lockBonusStartAge;

    /**
     * 行使权益时最大年龄
     */
    private Integer lockBonusEndAge;

    /**
     * 锁定终期红利百分比
     */
    private String lockBonusPercentage;

}