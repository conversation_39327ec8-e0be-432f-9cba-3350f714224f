package com.sub.baoxian.core.playwright.service;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import org.springframework.stereotype.Service;

import com.microsoft.playwright.Browser;
import com.microsoft.playwright.BrowserContext;
import com.microsoft.playwright.BrowserType;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Mouse;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.Playwright;
import com.microsoft.playwright.Response;
import com.microsoft.playwright.TimeoutError;
import com.microsoft.playwright.options.BoundingBox;
import com.microsoft.playwright.options.LoadState;
import com.microsoft.playwright.options.WaitForSelectorState;
import com.sub.baoxian.common.exception.BizException;
import com.sub.baoxian.core.pdfhandler.PdfHandler;
import com.sub.baoxian.core.playwright.config.ChinaLifeConfig;
import com.sub.baoxian.core.playwright.dto.ProposalRequestDTO;
import com.sub.baoxian.util.OssUtil;
import com.sub.baoxian.util.RedisUtil;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Playwright自动化服务
 * 用于通过浏览器自动化生成保险计划书
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PlaywrightService {

    private final ChinaLifeConfig chinaLifeConfig;
    private final PdfHandler pdfHandler;

    private final List<String> SPECIAL_PRODUCT_LIST = Arrays.asList("G014", "G093", "G094");
    private final List<String> SPECIAL_PRODUCT_LIST_FIRST = Arrays.asList("C395", "C396", "C397", "C398");

    /**
     * 生成保险计划书PDF
     * 
     * @param request 请求参数
     * @return OSS中的PDF文件URL
     */
    public String generateProposalPdf(ProposalRequestDTO request) {
        log.info("开始生成保险计划书PDF, 参数: {}", request);

        Playwright playwright = null;
        Browser browser = null;
        BrowserContext context = null;
        Page page = null;
        String pdfUrl = null;

        try {
            playwright = Playwright.create();
            browser = playwright.chromium().launch(new BrowserType.LaunchOptions()
                    .setHeadless(true)
                    .setArgs(Arrays.asList(
                            "--no-sandbox",
                            "--disable-setuid-sandbox",
                            "--disable-dev-shm-usage",
                            "--disable-gpu",
                            "--disable-web-security",
                            "--single-process")));
            context = browser.newContext();
            page = context.newPage();

            // 获取或刷新token
            String token = getOrRefreshToken(page);
            if (StrUtil.isBlank(token)) {
                throw new BizException("无法获取有效token");
            }

            // 打开计划书页面
            openProposalPage(page, token);

            pdfUrl = getPdfUrlWithNormalProduct(page, request);

            if (pdfUrl == null) {
                throw new BizException("计划书生成失败，请重新生成");
            }
            return pdfUrl;
        } catch (Exception e) {
            log.error("计划书生成失败", e);
            if (e instanceof TimeoutError) {
                throw new BizException("计划书生成失败:" + "网络超时，请重新生成");
            } else if (e instanceof BizException) {
                throw new BizException("计划书生成失败:" + e.getMessage());
            }
        } finally {
            // 确保资源正确关闭
            log.info("关闭浏览器资源");
            if (context != null) {
                context.close();
            }
            if (browser != null) {
                browser.close();
            }
            if (playwright != null) {
                playwright.close();
            }
        }
        return null;
    }

    /**
     * 获取或刷新token
     */
    private String getOrRefreshToken(Page page) {
        String token = (String) RedisUtil.get(chinaLifeConfig.getTokenCachePrefix());
        if (StrUtil.isNotBlank(token) && isTokenValid(token)) {
            log.info("从缓存中获取到有效token");
            return token;
        } else {
            login(page);
            page.waitForLoadState(LoadState.DOMCONTENTLOADED);
            token = getToken(page);
            RedisUtil.set(chinaLifeConfig.getTokenCachePrefix(), token, chinaLifeConfig.getTokenExpireSeconds());
            return token;
        }
    }

    /**
     * 获取token
     */
    private String getToken(Page page) {
        log.info("从页面获取token");

        String userInfoJson = (String) page.evaluate("() => sessionStorage.getItem('userInfo')");
        if (StrUtil.isBlank(userInfoJson)) {
            log.error("无法获取userInfo");
            return null;
        }

        String tokenRegex = "\"token\"\\s*:\\s*\"([^\"]+)\"";
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(tokenRegex);
        java.util.regex.Matcher matcher = pattern.matcher(userInfoJson);
        if (matcher.find()) {
            String token = matcher.group(1);
            log.info("成功获取token");
            RedisUtil.set(chinaLifeConfig.getTokenCachePrefix(), token, chinaLifeConfig.getTokenExpireSeconds());
            return token;
        }

        log.error("从userInfo中提取token失败");
        return null;
    }

    /**
     * 登录操作
     */
    private void login(Page page) {
        log.info("开始登录中国人寿系统");
        page.navigate(chinaLifeConfig.getBaseUrl());
        page.waitForLoadState(LoadState.DOMCONTENTLOADED);

        // 填写登录信息
        page.locator("//*[@id=\"login\"]/div[1]/div[2]/div[6]/div[1]/div[1]/input").fill(chinaLifeConfig.getUsername());
        page.locator("//*[@id=\"login\"]/div[1]/div[2]/div[6]/div[2]/div[1]/input").fill(chinaLifeConfig.getPassword());

        // 滑动验证
        slideVerification(page);

        // 等待验证通过
        page.waitForSelector("#dragText:has-text('驗證通過')",
                new Page.WaitForSelectorOptions().setTimeout(10000));

        // 等待一段时间确保验证完成
        page.waitForTimeout(2000);

        Response loginResponse = page.waitForResponse(
                response -> response.url().contains("/op-service-authentication/login_new"),
                () -> {
                    // 点击登录按钮
                    page.locator("//*[@id=\"login\"]/div[1]/div[2]/div[6]/button").click();
                });

        if (loginResponse.status() != 200) {
            throw new BizException("登录失败，响应状态码: " + loginResponse.status());
        }

        log.info("登录成功");
    }

    /**
     * 滑动验证
     */
    private void slideVerification(Page page) {
        log.info("执行滑动验证");

        Locator slider = page.locator("#dragHandler");
        slider.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE));

        BoundingBox sliderBox = slider.boundingBox();
        int startX = (int) sliderBox.x;
        int startY = (int) (sliderBox.y + sliderBox.height / 2);
        int targetX = startX + 270;

        slider.hover();
        page.mouse().down();

        Random random = new Random();
        int moved = 0;

        while (moved < 270) {
            int step = 10 + random.nextInt(10);
            if (moved + step > 270) {
                step = 270 - moved;
            }

            int currentY = startY + random.nextInt(5) - 2;

            moved += step;
            page.mouse().move(
                    startX + moved,
                    currentY,
                    new Mouse.MoveOptions().setSteps(1));
        }

        page.mouse().move(targetX, startY);
        page.mouse().move(targetX + 3, startY);
        page.waitForTimeout(100);
        page.mouse().move(targetX, startY);
        page.waitForTimeout(100);
        page.mouse().up();

        log.info("滑动验证完成");
    }

    /**
     * 打开计划书页面
     */
    private void openProposalPage(Page page, String token) {
        log.info("开始打开计划书页面");

        try {
            // 拼接完整URL
            String fullUrl = String.format(chinaLifeConfig.getProposalUrlTemplate(), token);

            // 在当前会话中打开页面
            page.navigate(fullUrl);

            // 等待页面加载完成
            page.waitForLoadState(LoadState.DOMCONTENTLOADED);

            log.info("计划书页面打开成功");
        } catch (Exception e) {
            log.error("打开计划书页面失败", e);
            throw new BizException("打开计划书页面失败: " + e.getMessage(), e);
        }
    }

    /**
     * 填写信息并获取PDF
     */
    private String getPdfUrlWithNormalProduct(Page page, ProposalRequestDTO request) {
        log.info("开始填写保险信息DTO: {}", request);
        // 选择性别
        if (request.getGender().equals("female")) {
            page.locator("label.el-radio:has-text('女')").first().click();
            log.info("选择性别: 女");
        } else {
            page.locator("label.el-radio:has-text('男')").first().click();
            log.info("选择性别: 男");
        }

        // 填写吸烟情况
        log.info("填写吸烟情况: {}", request.getIsSmoking());
        Locator smokeSelector = page.locator(
                "body > div.el-dialog__wrapper.infoDialog > div > div.el-dialog__body > div.dialog_content > div.dialog_slide1 > div.dialog_slide1_item.color_b5b5b5.smokeContainer > div");
        if (request.getIsSmoking()) {
            smokeSelector.locator("label").nth(0).click();
        } else {
            smokeSelector.locator("label").nth(1).click();
        }
        // 填写年龄
        page.locator("input[type='text'][role='spinbutton']").first().fill(request.getAge().toString());
        log.info("填写年龄: {}", request.getAge());

        page.locator(".el-select-group.basePlanList span").click();
        page.waitForTimeout(2000);
        page.locator(".el-select-group.basePlanList span").click();

        // 选择产品
        String productSelector = "ul.el-select-pop li.select_item:has-text('" + request.getProductCode() + "')";
        page.locator(productSelector).click();
        log.info("选择产品: {}", request.getProductCode());

        // 处理特殊产品
        if (SPECIAL_PRODUCT_LIST.contains(request.getProductCode())) {
            page.locator(
                    "body > div.el-dialog__wrapper.infoDialog > div > div.el-dialog__body > div.dialog_content > div.dialog_slide2 > div:nth-child(3) > div > div.el-input.el-input--suffix > input")
                    .click();

            Locator currencyList = page.locator(
                    "body > div.el-select-dropdown.el-popper > div.el-scrollbar > div.el-select-dropdown__wrap.el-scrollbar__wrap > ul");

            String currency = request.getCurrency().equals("HKD") ? "*港元" : "*美元";
            currencyList.locator("li:text-is('" + currency + "')").click();
            log.info("选择货币: {}", currency);
        }

        // 确认选择
        page.locator(".el-dialog__footer .button").nth(2).click();

        if (!request.getProductType().equals("MEDICAL")) {
            // 选择地区
            try {
                if (StrUtil.isNotBlank(request.getRegion())) {
                    page.locator("//*[@id=\"step1\"]/div[1]/div[1]/div/div[1]/input").click();
                    page.locator(".el-select-dropdown .el-scrollbar__view li").first().click();
                    log.info("选择地区: {}", request.getRegion());
                }
            } catch (Exception e) {
                throw new BizException("地区选择失败");
            }

            // 填写金额 0-保额 1-保费
            if (request.getAmountType() == 0) {
                Locator amountInput = page.locator(
                        "#step1 > div.step1_slide2.step_slide > div > div.slide2_left > div:nth-child(1) > div > div > input");
                amountInput.click();
                amountInput.fill(request.getAmount());
                log.info("填写金额: {}", request.getAmount());
            } else {
                Locator amountInput = page.locator(
                        "#step1 > div.step1_slide2.step_slide > div > div.slide2_right > div:nth-child(1) > div > div > input");
                amountInput.click();
                amountInput.fill(request.getAmount());
                log.info("填写保额: {}", request.getAmount());
            }
            page.locator("body").click();
            page.waitForTimeout(3000);
        }

        // 选择缴费方式
        if (request.getPaymentMethod() != null) {
            selectPaymentMethod(page, request);
        }

        // 选择预缴年份
        if (request.getPaymentMethod().equals("ANNUALLY")) {
            if (request.getPrepaymentYear() != null) {
                selectPrepaymentYear(page, request);
            }

        }

        // 点击下一步
        page.locator(".home_footer .nextStep .button").click();

        // 选择语言
        if (request.getLanguage().equals("zh-TW")) {
            page.locator("//*[@id=\"step2\"]/div[1]/div[1]/div[1]/div/label[1]").click();
        } else if (request.getLanguage().equals("en-US")) {
            page.locator("//*[@id=\"step2\"]/div[1]/div[1]/div[1]/div/label[2]").click();
        }

        log.info("选择语言: {}", request.getLanguage());
        page.locator("body").click();

        // 自定义现金提取
        customCashWithdrawal(page, request);

        // 锁定终期红利
        lockFinalDividend(page, request);

        // 填写客户名字
        Locator customerNameInput = page.locator(".step2 .custInfo .el-input__inner").first();
        customerNameInput.click();
        customerNameInput.press("Control+A");
        customerNameInput.press("Backspace");
        page.keyboard().type(request.getCustomerName());
        log.info("填写客户名字: {}", request.getCustomerName());

        // 监听API请求，获取PDF URL
        log.info("等待计划书生成API响应");
        String proposalFileId = null;
        if (!request.isEnableCustomWithdrawal()) {
            Response reqProposalResponse = page.waitForResponse(
                    response -> response.url().contains("/epos-web-proposal/pdf/reqProposal"),
                    () -> {
                        page.locator(".home_footer .home_footer_bottom .nextStep .button").nth(1).click();
                    });

            // 等待计划书生成界面完成
            page.waitForSelector(".step3_label", new Page.WaitForSelectorOptions().setTimeout(10000));

            // 解析第二个API响应，获取必要信息
            String responseBody = reqProposalResponse.text();
            log.info("获取到reqProposal响应: {}", responseBody);

            JSONObject jsonResponse = JSONUtil.parseObj(responseBody);

            // 从嵌套JSON对象中提取proposalFileId
            JSONObject dataObj = jsonResponse.getJSONObject("data");
            if (dataObj == null) {
                throw new BizException("API响应中缺少data字段");
            }

            proposalFileId = dataObj.getStr("proposalFileId");
            if (StrUtil.isBlank(proposalFileId)) {
                throw new BizException("无法从API响应中获取proposalFileId");
            }

            log.info("获取到proposalFileId: {}", proposalFileId);
        } else {
            // 设置响应监听，等待点击下一步后的第一个API请求完成
            Response validateWithDrawalResponse = page.waitForResponse(
                    response -> response.url().contains("/epos-web-proposal/calc/validateWithDrawal"),
                    () -> {
                        // 点击下一步，触发API请求
                        page.locator(".home_footer .home_footer_bottom .nextStep .button").nth(1).click();
                    });

            // 解析第一个响应并检查状态
            String validateWithDrawalResponseBody = validateWithDrawalResponse.text();
            JSONObject validateWithDrawalResponseJson = JSONUtil.parseObj(validateWithDrawalResponseBody);
            log.info("validateWithDrawal响应: {}", validateWithDrawalResponseBody);

            // 检查响应状态码
            if (!validateWithDrawalResponseJson.getObj("code").equals("200")) {
                throw new BizException("自定金额提取过大，请重新设置");
            }

            // 第一个响应成功，继续等待第二个响应
            log.info("validateWithDrawal请求成功，等待reqProposal响应");
            Response reqProposalResponse = page.waitForResponse(
                    response -> response.url().contains("/epos-web-proposal/pdf/reqProposal"),
                    () -> {
                    });

            // 等待计划书生成界面完成
            page.waitForSelector(".step3_label", new Page.WaitForSelectorOptions().setTimeout(10000));

            // 解析第二个API响应，获取必要信息
            String responseBody = reqProposalResponse.text();
            log.info("获取到reqProposal响应: {}", responseBody);

            JSONObject jsonResponse = JSONUtil.parseObj(responseBody);

            // 从嵌套JSON对象中提取proposalFileId
            JSONObject dataObj = jsonResponse.getJSONObject("data");
            if (dataObj == null) {
                throw new BizException("API响应中缺少data字段");
            }

            proposalFileId = dataObj.getStr("proposalFileId");
            if (StrUtil.isBlank(proposalFileId)) {
                throw new BizException("无法从API响应中获取proposalFileId");
            }
        }

        log.info("获取到proposalFileId: {}", proposalFileId);

        // 构建PDF URL
        String pdfBaseUrl = "https://onepartner.chinalife.com.hk/epos-web-portal/file/getFile/";
        String newPageUrl = pdfBaseUrl + proposalFileId;

        log.info("构建的PDF URL: {}", newPageUrl);

        // 下载PDF并上传到OSS
        String ossFileUrl = downloadAndUploadPdfToOss(newPageUrl, request.getProductType(), request.getProductCode());
        log.info("PDF已上传到OSS: {}", ossFileUrl);

        return ossFileUrl;
    }

    /**
     * 下载PDF并上传到OSS
     */
    private String downloadAndUploadPdfToOss(String pdfUrl, String productType, String productCode) {
        try {
            log.info("开始下载PDF: {}", pdfUrl);

            byte[] pdfContent = HttpUtil.downloadBytes(pdfUrl);

            if (pdfContent == null || pdfContent.length == 0) {
                throw new BizException("PDF下载失败，内容为空");
            }

            // 使用PdfHandler类处理PDF
            InputStream inputStream = new ByteArrayInputStream(pdfContent);
            InputStream fileInputStream = null;
            if (productType.equals("MEDICAL") || SPECIAL_PRODUCT_LIST.contains(productCode)) {
                fileInputStream = pdfHandler.processPdf(inputStream, "MEDICAL");
            } else if (SPECIAL_PRODUCT_LIST_FIRST.contains(productCode)) {
                fileInputStream = pdfHandler.processPdf(inputStream, "SPECIAL");
            } else {
                fileInputStream = pdfHandler.processPdf(inputStream, "DEFAULT");
            }

            // 生成文件名
            String fileName = "proposal_" + System.currentTimeMillis() + ".pdf";
            // 上传到OSS
            String ossUrl = OssUtil.uploadInputStream(fileInputStream, "Insuriam/proposal/", fileName);

            log.info("PDF上传到OSS成功: {}", ossUrl);

            return ossUrl;
        } catch (Exception e) {
            log.error("下载并上传PDF失败", e);
            throw new BizException("下载并上传PDF失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查token是否有效
     */
    private boolean isTokenValid(String token) {
        if (StrUtil.isBlank(token)) {
            return false;
        }

        Playwright playwright = null;
        Browser browser = null;
        BrowserContext context = null;
        Page page = null;

        try {
            playwright = Playwright.create();
            browser = playwright.chromium().launch(new BrowserType.LaunchOptions()
                    .setHeadless(true)
                    .setArgs(Arrays.asList(
                            "--no-sandbox",
                            "--disable-setuid-sandbox",
                            "--disable-dev-shm-usage",
                            "--disable-gpu",
                            "--disable-web-security",
                            "--single-process")));
            context = browser.newContext();
            page = context.newPage();

            log.info("检查token是否有效");

            // 使用token访问页面
            page.navigate(String.format(chinaLifeConfig.getProposalUrlTemplate(), token));
            page.waitForLoadState(LoadState.DOMCONTENTLOADED);

            // 检查是否成功加载
            Locator homeBody = page.locator(".el-dialog__wrapper.infoDialog");
            boolean isValid = homeBody.isVisible();

            log.info("Token有效性检查结果: {}", isValid ? "有效" : "无效");

            // 关闭浏览器
            context.close();
            browser.close();
            playwright.close();

            return isValid;
        } catch (Exception e) {
            log.error("检查token有效性失败", e);
            return false;
        }
    }

    /**
     * 自定义现金提取
     */
    private void customCashWithdrawal(Page page, ProposalRequestDTO request) {

        if (request.getWithdrawalStartAge() != null && request.getWithdrawalEndAge() != null
                && request.getInitialWithdrawalAmount() != null && request.getWithdrawalGrowthRate() != null) {
            log.info("开始自定义现金提取");
            page.locator("#showDefCashExtract > div.el-switch > span").click();

            Locator endAgeInput = page.locator("#defCashExtractMaxAge0");
            endAgeInput.fill(request.getWithdrawalEndAge().toString());

            Locator startAgeInput = page.locator("#defCashExtractMinAge0");
            startAgeInput.fill(request.getWithdrawalStartAge().toString());
            Locator initialWithdrawalAmountInput = page.locator("#defCashExtractDrawMoney0");
            initialWithdrawalAmountInput.fill(request.getInitialWithdrawalAmount().toString());

            Locator withdrawalGrowthRateInput = page.locator("#defCashExtractRiseRate0");
            withdrawalGrowthRateInput.fill(request.getWithdrawalGrowthRate().toString());

            page.locator("body").click();
            page.waitForTimeout(5000);
            log.info("自定义现金提取完成");
        }

    }

    /**
     * 锁定终期红利
     * 
     * @param page
     * @param request
     */
    private void lockFinalDividend(Page page, ProposalRequestDTO request) {

        if (request.getLockBonusStartAge() != null && request.getLockBonusEndAge() != null
                && request.getLockBonusPercentage() != null) {
            log.info("开始锁定终期红利");
            // #showLockInCheck > div.el-switch > span
            // #showDefCashExtract > div.el-switch > span
            page.locator("#showLockInCheck > div.el-switch.is-checked > span").click();
            // #showLockInCheck > div.receive.custom > div > div:nth-child(1) >
            // div.last-form > input:nth-child(2)
            Locator exerciseRightMinAgeInput = page.locator(
                    "#showLockInCheck > div.receive.custom > div > div:nth-child(1) > div.last-form > input:nth-child(2)");
            exerciseRightMinAgeInput.fill(request.getLockBonusStartAge().toString());

            Locator exerciseRightMaxAgeInput = page.locator(
                    "#showLockInCheck > div.receive.custom > div > div:nth-child(1) > div.last-form > input:nth-child(6)");
            exerciseRightMaxAgeInput.fill(request.getLockBonusEndAge().toString());

            Locator lockBonusPercentageList = page
                    .locator("#showLockInCheck > div.receive.custom > div > div.pro-left-type.dividend > div");
            lockBonusPercentageList.locator("li:has-text('" + request.getLockBonusPercentage() + "')").click();

            page.waitForTimeout(5000);
            log.info("锁定终期红利完成");
        }

    }

    /**
     * 选择预缴方式
     */
    private void selectPaymentMethod(Page page, ProposalRequestDTO request) {
        log.info("开始选择预缴方式");
        Map<String, String> paymentMethodMap = new HashMap<>();
        paymentMethodMap.put("MONTHLY", "月繳");
        paymentMethodMap.put("QUARTERLY", "季繳");
        paymentMethodMap.put("SEMI_ANNUALLY", "半年繳");
        paymentMethodMap.put("ANNUALLY", "年繳");

        // 特殊处理医疗产品
        if (request.getProductType().equals("MEDICAL")) {
            Locator prepaymentYearListLocator = page
                    .locator("#step1 > div.step1_slide2.step_slide > div > div.slide_noselect_right > div");

            String paymentMethod = paymentMethodMap.get(request.getPaymentMethod());
            if (paymentMethod != null) {
                prepaymentYearListLocator.locator("span:text-is('" + paymentMethod + "')").click();
            }
        } else {
            Locator prepaymentYearListLocator = page.locator(request.getPrepaymentYear() != null
                    ? "#step1 > div.step1_slide2.step_slide > ul > li:nth-child(1) > div"
                    : "#step1 > div.step1_slide2.step_slide > ul > li > div");

            String paymentMethod = paymentMethodMap.get(request.getPaymentMethod());
            if (paymentMethod != null) {
                prepaymentYearListLocator.locator("span:text-is('" + paymentMethod + "')").click();
            }
        }

        // #step1 > div.step1_slide2.step_slide > ul > li > div
        // //*[@id="step1"]/div[3]/ul/li/div
        // #step1 > div.step1_slide2.step_slide > ul > li:nth-child(1) > div
        // //*[@id="step1"]/div[3]/ul/li[1]/div

        page.waitForTimeout(5000);
        log.info("选择缴费方式完成: {}", request.getPaymentMethod());

    }

    /**
     * 选择预缴年份
     */
    private void selectPrepaymentYear(Page page, ProposalRequestDTO request) {
        log.info("开始选择预缴年份");
        Locator prepaymentYearListLocator = page
                .locator("#step1 > div.step1_slide2.step_slide > ul > li.t-c > div > div > input");
        Locator prepaymentYearul = page.locator(
                "body > div.el-select-dropdown.el-popper > div.el-scrollbar > div.el-select-dropdown__wrap.el-scrollbar__wrap > ul");
        String prepaymentYear = null;
        prepaymentYearListLocator.click();
        if (request.getPrepaymentYear().equals("1")) {
            prepaymentYear = "不適用";
        } else {
            prepaymentYear = request.getPrepaymentYear() + "年";
        }
        prepaymentYearul.locator("li span:text-is('" + prepaymentYear + "')").click();
        page.waitForTimeout(5000);
        log.info("选择预缴年份完成: {}", request.getPrepaymentYear());

    }

}
