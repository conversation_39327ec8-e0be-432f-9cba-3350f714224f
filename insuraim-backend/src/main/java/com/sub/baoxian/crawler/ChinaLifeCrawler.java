package com.sub.baoxian.crawler;

import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.sub.baoxian.util.CrawlerUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 中国人寿知识库爬虫
 */
@Component
@Slf4j
public class ChinaLifeCrawler {

    private static ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 仅爬取中国人寿知识库数据
     * 
     * @return 爬取的知识库数据列表
     * @throws IOException
     */
    public List<Map<String, String>> crawlChapter1() throws IOException {
        String id = "deef8502-f44c-41d0-acc9-aa57f07c5129";
        String token = "be1eec5c-44c7-4460-88ba-ff504db1ab5f";
        String url = "https://km.chinalife.com.hk/api/service-community/lore-cheats/get-lore-topic";
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("id", id);
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Token", token);
        headers.put("User-Agent",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
        String result = CrawlerUtil.get(url, queryParams, headers);
        JsonNode list = objectMapper.readTree(result).get("obj").get("chapterList");
        List<Map<String, String>> loreList = new ArrayList<>();
        for (JsonNode jsonNode : list) {
            JsonNode chapterList = jsonNode.get("chapterList");
            for (JsonNode chapterLore : chapterList) {
                JsonNode chapterLoreList = chapterLore.get("chapterLoreList");
                for (JsonNode lorelist : chapterLoreList) {
                    String content = lorelist.get("content").asText();
                    Map<String, String> lore = new HashMap<>();
                    String loreId = lorelist.get("loreId").asText();
                    lore.put("loreId", loreId);
                    lore.put("title", content);

                    Map<String, Object> queryParams1 = new HashMap<>();
                    queryParams1.put("loreId", loreId);
                    String result1 = CrawlerUtil.get("https://km.chinalife.com.hk/api/service-lore/lore/get-lore",
                            queryParams1, headers);
                    JsonNode data = objectMapper.readTree(result1).get("obj");
                    StringBuilder content1 = new StringBuilder();
                    JsonNode children = data.get("paragraphHead").get("children");
                    if (children == null) {
                        content1 = new StringBuilder("");
                    } else {
                        for (JsonNode child : children) {
                            String contentHead = child.get("head").asText();
                            content1.append("<h3>" + contentHead + "</h3>");
                            JsonNode paragraphContentList = child.get("paragraphContentList");
                            for (JsonNode paragraphContent : paragraphContentList) {
                                String contentBody = paragraphContent.get("loreContent").get("content").asText();
                                content1.append(contentBody);
                            }
                        }
                    }
                    lore.put("content", content1.toString());
                    loreList.add(lore);
                }
            }
        }
        return loreList;
    }

    /**
     * 爬取第二章
     * 
     * @return 爬取的知识库数据列表
     * @throws IOException
     */
    public List<Map<String, String>> crawlChapter2() throws IOException {
        String id = "47792eb7-1b96-47d1-b8ab-69f453f854e1";
        String token = "2bf7b0e1-544e-4f4c-ae4c-eed1111c4684";
        String url = "https://km.chinalife.com.hk/api/service-community/lore-cheats/get-lore-topic";
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("id", id);
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Token", token);
        headers.put("User-Agent",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
        String result = CrawlerUtil.get(url, queryParams, headers);
        JsonNode list = objectMapper.readTree(result).get("obj").get("chapterList");
        List<Map<String, String>> loreList = new ArrayList<>();
        for (JsonNode chapterList : list) {
            JsonNode chapterLoreList = chapterList.get("chapterLoreList");
            for (JsonNode lorelist : chapterLoreList) {
                String content = lorelist.get("content").asText();
                Map<String, String> lore = new HashMap<>();
                String loreId = lorelist.get("loreId").asText();
                lore.put("loreId", loreId);
                lore.put("title", content);

                Map<String, Object> queryParams1 = new HashMap<>();
                queryParams1.put("loreId", loreId);
                String result1 = CrawlerUtil.get("https://km.chinalife.com.hk/api/service-lore/lore/get-lore",
                        queryParams1, headers);
                JsonNode data = objectMapper.readTree(result1).get("obj");
                StringBuilder content1 = new StringBuilder();
                JsonNode children = data.get("paragraphHead").get("children");
                if (children == null) {
                    content1 = new StringBuilder("");
                } else {
                    for (JsonNode child : children) {
                        String contentHead = child.get("head").asText();
                        content1.append("<h3>" + contentHead + "</h3>");
                        JsonNode paragraphContentList = child.get("paragraphContentList");
                        for (JsonNode paragraphContent : paragraphContentList) {
                            String contentBody = paragraphContent.get("loreContent").get("content").asText();
                            content1.append(contentBody);
                        }
                    }
                }
                lore.put("content", content1.toString());
                loreList.add(lore);
            }

        }
        return loreList;
    }

    /**
     * 爬取‘續期保費、保費徵費繳費方式及入賬資料指引’
     */
    public List<Map<String, String>> crawlChapter7() throws IOException {
        String id = "5025c0f2-0b8d-4510-acc6-d589a97ea96a";
        String token = "2354a100-9a21-4b40-9d69-0fd1326f6508";
        String url = "https://km.chinalife.com.hk/api/service-community/lore-cheats/get-lore-topic";
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("id", id);
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Token", token);
        headers.put("User-Agent",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
        String result = CrawlerUtil.get(url, queryParams, headers);
        JsonNode list = objectMapper.readTree(result).get("obj").get("chapterList");
        List<Map<String, String>> loreList = new ArrayList<>();
        for (JsonNode chapterList : list) {
            JsonNode chapterLoreList = chapterList.get("chapterLoreList");
            for (JsonNode lorelist : chapterLoreList) {
                String content = lorelist.get("content").asText();
                Map<String, String> lore = new HashMap<>();
                String loreId = lorelist.get("loreId").asText();
                lore.put("loreId", loreId);
                lore.put("title", content);

                Map<String, Object> queryParams1 = new HashMap<>();
                queryParams1.put("loreId", loreId);
                String result1 = CrawlerUtil.get("https://km.chinalife.com.hk/api/service-lore/lore/get-lore",
                        queryParams1, headers);
                JsonNode data = objectMapper.readTree(result1).get("obj");
                StringBuilder content1 = new StringBuilder();
                JsonNode children = data.get("paragraphHead").get("children");
                if (children == null) {
                    content1 = new StringBuilder("");
                } else {
                    for (JsonNode child : children) {
                        String contentHead = child.get("head").asText();
                        content1.append("<h3>" + contentHead + "</h3>");
                        JsonNode paragraphContentList = child.get("paragraphContentList");
                        for (JsonNode paragraphContent : paragraphContentList) {
                            String contentBody = paragraphContent.get("loreContent").get("content").asText();
                            content1.append(contentBody);
                        }
                    }
                }
                lore.put("content", content1.toString());
                loreList.add(lore);
            }

        }
        return loreList;
    }

    /**
     * 爬取‘保單服務指引’ 章节5
     */
    public List<Map<String, String>> crawlChapter5() throws IOException {
        String id = "1eb6fa4a-3924-4e14-a0e4-0c6d6881c671";
        String token = "3feb2ceb-8415-4cc7-a9d2-baf5dbe04aa9";
        String url = "https://km.chinalife.com.hk/api/service-community/lore-cheats/get-lore-topic";
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("id", id);
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Token", token);
        headers.put("User-Agent",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
        String result = CrawlerUtil.get(url, queryParams, headers);
        JsonNode list = objectMapper.readTree(result).get("obj").get("chapterList");
        List<Map<String, String>> loreList = new ArrayList<>();
        for (JsonNode chapterList : list) {
            JsonNode chapterLoreList = chapterList.get("chapterLoreList");
            for (JsonNode lorelist : chapterLoreList) {
                String content = lorelist.get("content").asText();
                Map<String, String> lore = new HashMap<>();
                String loreId = lorelist.get("loreId").asText();
                lore.put("loreId", loreId);
                lore.put("title", content);

                Map<String, Object> queryParams1 = new HashMap<>();
                queryParams1.put("loreId", loreId);
                String result1 = CrawlerUtil.get("https://km.chinalife.com.hk/api/service-lore/lore/get-lore",
                        queryParams1, headers);
                JsonNode data = objectMapper.readTree(result1).get("obj");
                StringBuilder content1 = new StringBuilder();
                JsonNode paragraphHead = data.get("paragraphHead");
                if (paragraphHead == null) {
                    JsonNode loreContent = data.get("loreContent");
                    if (loreContent == null) {
                        content1 = new StringBuilder("");
                    } else {
                        content1.append(loreContent.get("content").asText());
                    }
                } else {
                    JsonNode children = paragraphHead.get("children");
                    if (children == null) {
                        content1 = new StringBuilder("");
                    } else {
                        for (JsonNode child : children) {
                            String contentHead = child.get("head").asText();
                            content1.append("<h3>" + contentHead + "</h3>");
                            JsonNode paragraphContentList = child.get("paragraphContentList");
                            for (JsonNode paragraphContent : paragraphContentList) {
                                String contentBody = paragraphContent.get("loreContent").get("content").asText();
                                content1.append(contentBody);
                            }
                        }
                    }
                    lore.put("content", content1.toString());
                    loreList.add(lore);
                }
            }
        }
        return loreList;
    }

    /**
     * 测试方法 - 使用方法一：JDBC方式直接插入数据
     */
    private static void saveToDbViaJdbc(List<Map<String, String>> loreList, Long categoryId, Long authorId) {
        // 数据库连接参数
        String url = "**************************************************************************************************************************";
        String username = "zeyu";
        String password = "Am123456.";

        String sql = "INSERT INTO knowledge_base (title, content, category, status, created_at, updated_at, " +
                "author_id, summary, views, tags, category_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        int successCount = 0;

        try (Connection conn = DriverManager.getConnection(url, username, password);
                PreparedStatement pstmt = conn.prepareStatement(sql)) {

            // 关闭自动提交
            conn.setAutoCommit(false);
            long currentTime = System.currentTimeMillis();

            for (Map<String, String> lore : loreList) {
                String title = lore.get("title");
                String content = lore.get("content");
                String summary = content.length() > 100 ? content.substring(0, 100) + "..." : content;

                pstmt.setString(1, title);
                pstmt.setString(2, content);
                pstmt.setString(3, "中国人寿");
                pstmt.setInt(4, 1); // 状态：1-正常
                pstmt.setLong(5, currentTime);
                pstmt.setLong(6, currentTime);
                pstmt.setLong(7, authorId);
                pstmt.setString(8, summary);
                pstmt.setInt(9, 0); // 初始浏览量为0
                pstmt.setString(10, "中国人寿,保险知识");
                pstmt.setLong(11, categoryId);

                pstmt.addBatch();
            }

            int[] results = pstmt.executeBatch();
            conn.commit();

            for (int i : results) {
                if (i > 0) {
                    successCount++;
                }
            }

            System.out.println("JDBC方式成功插入数据：" + successCount + " 条");

        } catch (SQLException e) {
            System.err.println("数据库插入失败：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 主方法 - 脱离Spring容器环境直接运行并插入数据库
     * 
     * 支持两种数据库插入方式:
     * 1. JDBC直接插入（默认）
     * 
     * @throws IOException
     */
    public static void main(String[] args) throws IOException {
        // 配置参数
        Long categoryId = 5L; // 知识分类ID，默认1
        Long authorId = 1L; // 作者ID，默认1b

        // // 爬取数据
        ChinaLifeCrawler crawler = new ChinaLifeCrawler();
        List<Map<String, String>> loreList = crawler.crawlChapter5();
        System.out.println("爬取中国人寿知识库数据 " + loreList.size() + " 条");
        saveToDbViaJdbc(loreList, categoryId, authorId);
    }

    /**
     * 从数据库中获取content字段，移除a标签及其内容，并更新回数据库
     * 
     * @param categoryId 知识分类ID (可选过滤条件)
     * @return 处理的记录数量
     */
    public static int removeATagsFromContent(Long categoryId) {
        // 数据库连接参数
        String url = "**************************************************************************************************************************";
        String username = "zeyu";
        String password = "Am123456.";

        int updatedCount = 0;

        try (Connection conn = DriverManager.getConnection(url, username, password)) {
            // 查询SQL，根据是否提供categoryId确定是否添加过滤条件
            String selectSql = categoryId != null
                    ? "SELECT id, content FROM knowledge_base WHERE category_id = ?"
                    : "SELECT id, content FROM knowledge_base";

            // 更新SQL
            String updateSql = "UPDATE knowledge_base SET content = ? WHERE id = ?";

            // 先查询数据
            try (PreparedStatement selectStmt = conn.prepareStatement(selectSql)) {
                // 设置查询参数（如果有）
                if (categoryId != null) {
                    selectStmt.setLong(1, categoryId);
                }

                ResultSet rs = selectStmt.executeQuery();

                // 准备更新语句
                try (PreparedStatement updateStmt = conn.prepareStatement(updateSql)) {
                    while (rs.next()) {
                        long id = rs.getLong("id");
                        String content = rs.getString("content");

                        if (content != null && !content.isEmpty()) {
                            // 使用正则表达式移除所有a标签及其内容
                            // 模式: <a[^>]*>.*?</a> 匹配开始标签、任意属性、标签内容和结束标签
                            String cleanedContent = content.replaceAll("<a[^>]*>.*?</a>", "");

                            // 设置更新参数
                            updateStmt.setString(1, cleanedContent);
                            updateStmt.setLong(2, id);

                            // 执行更新
                            int result = updateStmt.executeUpdate();
                            if (result > 0) {
                                updatedCount++;
                            }
                        }
                    }
                }
            }

            System.out.println("成功移除a标签：处理 " + updatedCount + " 条记录");

        } catch (SQLException e) {
            System.err.println("处理数据时出错：" + e.getMessage());
            e.printStackTrace();
        }

        return updatedCount;
    }
}
