package com.sub.baoxian.crawler;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.URL;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sub.baoxian.util.CrawlerUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ChinaLifeKnowledgeBaseCrawler {

    private final String token = "1dc2e47f-6acb-4d4a-8fbc-4421d27e5f6a";
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 根据 fileId 获取file链接
     *
     * @throws IOException
     */
    public String getFileUrl(String fileId) throws IOException {
        String baseUrl = "https://km.chinalife.com.hk/api/service-file/file/get-by-id";
        Map<String, Object> params = new HashMap<>();
        params.put("id", fileId);
        Map<String, String> headers = new HashMap<>();
        headers.put("Token", token);
        String result = CrawlerUtil.get(baseUrl, params, headers);
        JsonNode jsonNode = objectMapper.readTree(result);
        String path = jsonNode.get("obj").get("path").asText();
        return "https://km.chinalife.com.hk/api/service-file" + path;
    }

    /**
     * 获取分页列表
     *
     * @throws IOException
     */
    public JsonNode getPageList(String categoryId, Integer pageNumber, Integer pageSize) throws IOException {
        String baseUrl = "https://km.chinalife.com.hk/api/service-lore/lore/page-lore";
        Map<String, Object> jsonBody = new HashMap<>();
        jsonBody.put("categoryId", categoryId);
        jsonBody.put("pageNumber", pageNumber);
        jsonBody.put("pageSize", pageSize);
        jsonBody.put("sort", "8");
        jsonBody.put("title", "");
        jsonBody.put("fieldId", "");
        jsonBody.put("listStyle", 0);
        Map<String, String> headers = new HashMap<>();
        headers.put("Token", token);
        String result = CrawlerUtil.postJsonWithHeaders(baseUrl, jsonBody, headers);
        JsonNode jsonNode = objectMapper.readTree(result).get("obj").get("content");
        return jsonNode;
    }

    /**
     * 获取分页总页数
     *
     * @param categoryId
     * @return
     * @throws IOException
     */
    public int getPageNumber(String categoryId) throws IOException {
        String baseUrl = "https://km.chinalife.com.hk/api/service-lore/lore/page-lore";
        Map<String, Object> jsonBody = new HashMap<>();
        jsonBody.put("categoryId", categoryId);
        jsonBody.put("pageNumber", 1);
        jsonBody.put("pageSize", 10);
        Map<String, String> headers = new HashMap<>();
        headers.put("Token", token);
        String result = CrawlerUtil.postJsonWithHeaders(baseUrl, jsonBody, headers);
        JsonNode jsonNode = objectMapper.readTree(result).get("obj");
        return jsonNode.get("totalPages").asInt();
    }

    /**
     * 获取文章内容
     *
     * @param loreId
     * @return
     * @throws IOException
     */
    public JsonNode getArticle(String loreId) throws IOException {
        String baseUrl = "https://km.chinalife.com.hk/api/service-lore/lore/get-lore";
        Map<String, Object> params = new HashMap<>();
        params.put("loreId", loreId);
        Map<String, String> headers = new HashMap<>();
        headers.put("Token", token);
        String result = CrawlerUtil.get(baseUrl, params, headers);
        JsonNode jsonNode = objectMapper.readTree(result).get("obj");
        return jsonNode;
    }

    /**
     * 获取文章内容（content）
     *
     * @param article 文章JsonNode对象
     * @return 处理后的内容字符串
     */
    public String getArticleContent(JsonNode article) throws IOException {
        String content = "";
        if (article != null) {
            // 第一种情况：检查loreContent及其content字段
            if (article.get("loreContent") != null) {
                JsonNode contentNode = article.get("loreContent").get("content");
                if (contentNode != null && !contentNode.isNull() && !contentNode.asText().isEmpty()) {
                    content = contentNode.asText();
                }
            }
            // 第二种情况：检查paragraphHead字段
            if (article.get("paragraphHead") != null && !article.get("paragraphHead").isEmpty()) {
                StringBuilder sb = new StringBuilder();
                JsonNode child = article.get("paragraphHead").get("children");
                if (child != null && !child.isEmpty()) {
                    for (JsonNode childNode : child) {
                        String head = "<h3>" + childNode.get("head").asText() + "</h3>";
                        sb.append(head).append("\n");
                        JsonNode contentNode = childNode.get("paragraphContentList");
                        if (contentNode != null && !contentNode.isEmpty()) {
                            for (JsonNode contentNodeItem : contentNode) {
                                sb.append(contentNodeItem.get("loreContent").get("content").asText()).append("\n");
                            }
                        }
                    }
                }
                content = sb.toString().trim();
            }
            // 第三种情况：检查loreChannelValueList字段
            if (article.get("loreChannelValueList") != null && !article.get("loreChannelValueList").isEmpty()) {
                StringBuilder sb = new StringBuilder();
                // 创建HTML表格，添加美化样式
                sb.append(
                        "<table style='width:100%; border-collapse: collapse; border: 2px solid #ddd; font-family: Arial, sans-serif; margin: 15px 0;'>\n");

                for (JsonNode channelValue : article.get("loreChannelValueList")) {
                    // 安全检查
                    if (channelValue != null) {
                        // 获取fieldName作为表头
                        JsonNode fieldNameNode = channelValue.get("fieldName");
                        String fieldName = (fieldNameNode != null && !fieldNameNode.isNull()) ? fieldNameNode.asText()
                                : "";

                        // 获取value作为内容
                        JsonNode valueNode = channelValue.get("value");
                        String value = (valueNode != null && !valueNode.isNull()) ? valueNode.asText() : "";

                        // 只有当fieldName不为空时才添加到表格中
                        if (!fieldName.isEmpty()) {
                            sb.append("<tr>\n");
                            sb.append(
                                    "  <td style='background-color:#f0f7ff; font-weight:bold; padding: 12px; border: 1px solid #bbb; border-bottom: 2px solid #999;'>")
                                    .append(fieldName)
                                    .append("</td>\n");
                            sb.append("</tr>\n");
                            sb.append("<tr>\n");
                            if (!value.isEmpty()) {
                                // 处理可能包含HTML标签的内容
                                if (value.contains("<p>")) {
                                    sb.append("  <td style='padding: 12px; border: 1px solid #ddd;'>").append(value)
                                            .append("</td>\n");
                                } else {
                                    sb.append("  <td style='padding: 12px; border: 1px solid #ddd;'>").append(value)
                                            .append("</td>\n");
                                }
                            } else {
                                sb.append("  <td style='padding: 12px; border: 1px solid #ddd; color: #999;'>-</td>\n"); // 如果value为空，显示"-"
                            }
                            sb.append("</tr>\n");
                        }
                    }
                }
                sb.append("</table>");
                content = sb.toString();
            }
        }
        return content;
    }

    /**
     * 主流程
     *
     * @throws IOException
     */
    public static void main(String[] args) throws IOException {
        // try {
        // importCategoryTreeFromFile("categoryTree.json");
        // } catch (IOException e) {
        // e.printStackTrace();
        // }
        mainCrawler("89b261fc-c627-47ca-9610-f81e7c941883", 173L);
    }

    /**
     * 原始爬虫主方法
     * 
     * @param args 命令行参数
     * @throws IOException 异常
     */
    private static void mainCrawler(String categoryId, Long sqlCategoryId) throws IOException {
        ChinaLifeKnowledgeBaseCrawler crawler = new ChinaLifeKnowledgeBaseCrawler();
        int pageNumber = crawler.getPageNumber(categoryId);
        try {
            int articleCount = 0;
            int i;
            List<Map<String, Object>> articleList = new ArrayList<>();
            for (i = 1; i <= pageNumber; i++) {
                JsonNode pageList = crawler.getPageList(categoryId, i, 10);
                for (JsonNode map : pageList) {
                    Map<String, Object> article = new HashMap<>();
                    String title = map.get("title").asText();
                    System.out.println("标题: " + title);
                    String categoryName = map.get("mainCategory").get("categoryName").asText();
                    String loreId = map.get("id").asText();
                    // 操作文章
                    JsonNode articleContent = crawler.getArticle(loreId);
                    String content = crawler.getArticleContent(articleContent);
                    JsonNode attachmentList = articleContent.get("loreAttachmentList");
                    List<Map<String, Object>> loreAttachmentList = new ArrayList<>();
                    if (attachmentList != null && !attachmentList.isEmpty()) {
                        for (JsonNode attachment : attachmentList) {
                            Map<String, Object> attachmentMap = new HashMap<>();
                            String fileId = attachment.get("fileId").asText();
                            String fileUrl = crawler.getFileUrl(fileId);
                            String fileName = attachment.get("name").asText();
                            System.out.println("文件名: " + fileName);
                            fileName = fileName.replace(" ", "_");
                            String fileType = attachment.get("fileType").asText();
                            attachmentMap.put("fileUrl", fileUrl);
                            attachmentMap.put("fileName", fileName);
                            attachmentMap.put("fileType", fileType);
                            loreAttachmentList.add(attachmentMap);
                        }
                    }

                    article.put("title", title);
                    article.put("categoryName", categoryName);
                    article.put("content", content);
                    article.put("loreAttachmentList", loreAttachmentList);
                    articleList.add(article);
                }
                
            }
            log.info("执行插入...文章数{}", articleList.size());
            for (Map<String, Object> article : articleList) {
                crawler.insertArticle(article, sqlCategoryId);
                articleCount++;
            }
            log.info("成功爬取并插入 {} 篇文章", articleCount);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 从文件导入分类树
     * 
     * @param filePath 文件路径，如果为null则使用默认的JSON字符串
     * @throws IOException 异常
     */
    public static void importCategoryTreeFromFile(String filePath) throws IOException {
        ChinaLifeKnowledgeBaseCrawler crawler = new ChinaLifeKnowledgeBaseCrawler();

        if (filePath != null) {
            System.out.println("从文件导入分类树: " + filePath);
            // 从文件读取JSON
            File file = new File(filePath);
            if (!file.exists()) {
                throw new FileNotFoundException("找不到文件: " + filePath);
            }

            String jsonContent = new String(java.nio.file.Files.readAllBytes(file.toPath()), "UTF-8");
            crawler.insertCategoryTree(jsonContent);
        } else {
            System.out.println("使用默认JSON导入分类树");
            crawler.createCategoryTree();
        }
    }

    /**
     * 将文章插入数据库中
     */
    public void insertArticle(Map<String, Object> article, Long sqlCategoryId) {
        // 数据库连接参数
        String url = "**************************************************************************************************************************";
        String username = "zeyu";
        String password = "Am123456.";

        // 获取附件列表
        List<Map<String, Object>> loreAttachmentList = (List<Map<String, Object>>) article.get("loreAttachmentList");

        // SQL语句
        String articleSql = "INSERT INTO knowledge_base (title, content, category, status, created_at, updated_at, " +
                "author_id, summary, views, tags, category_id, type) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        String attachmentSql = "INSERT INTO knowledge_attachments (knowledge_id, file_name, file_path, file_type, " +
                "file_size, upload_time, description) VALUES (?, ?, ?, ?, ?, ?, ?)";

        Connection conn = null;
        PreparedStatement articleStmt = null;
        PreparedStatement attachmentStmt = null;
        ResultSet generatedKeys = null;

        try {
            // 建立数据库连接
            conn = DriverManager.getConnection(url, username, password);

            // 关闭自动提交，开启事务
            conn.setAutoCommit(false);

            // 准备文章插入语句，设置返回生成的主键
            articleStmt = conn.prepareStatement(articleSql, PreparedStatement.RETURN_GENERATED_KEYS);

            // 获取当前时间戳
            long currentTime = System.currentTimeMillis();

            // 从文章Map中获取数据
            String title = (String) article.get("title");
            String content = (String) article.get("content");
            String category = (String) article.get("categoryName");

            // 生成摘要（取内容前100个字符）
            String summary = content != null ? (content.length() > 100 ? content.substring(0, 100) + "..." : content)
                    : "";

            // 设置文章表的参数
            articleStmt.setString(1, title);
            articleStmt.setString(2, content);
            articleStmt.setString(3, category);
            articleStmt.setInt(4, 1); // 状态：1-正常
            articleStmt.setLong(5, currentTime); // 创建时间
            articleStmt.setLong(6, currentTime); // 更新时间
            articleStmt.setLong(7, 1L); // 作者ID，默认为1
            articleStmt.setString(8, summary); // 摘要
            articleStmt.setInt(9, 0); // 初始浏览量为0
            articleStmt.setString(10, "中国人寿,保险知识"); // 标签
            articleStmt.setLong(11, sqlCategoryId); // 分类ID
            articleStmt.setString(12, content.isEmpty() ? "file" : "article"); // 类型

            // 执行文章插入
            int articleResult = articleStmt.executeUpdate();

            // 获取生成的文章ID
            Long knowledgeId = null;
            if (articleResult > 0) {
                generatedKeys = articleStmt.getGeneratedKeys();
                if (generatedKeys.next()) {
                    knowledgeId = generatedKeys.getLong(1);

                    // 检查是否有附件信息
                    if (loreAttachmentList != null && !loreAttachmentList.isEmpty()) {
                        // 处理每个附件
                        for (Map<String, Object> attachment : loreAttachmentList) {
                            String fileUrl = (String) attachment.get("fileUrl");
                            String fileName = (String) attachment.get("fileName");
                            String fileType = (String) attachment.get("fileType");

                            // 准备附件插入语句
                            attachmentStmt = conn.prepareStatement(attachmentSql);

                            // 设置附件表的参数
                            attachmentStmt.setInt(1, knowledgeId.intValue()); // 知识库ID
                            attachmentStmt.setString(2, fileName); // 文件名
                            attachmentStmt.setString(3, fileUrl); // 文件路径（OSS的URL）
                            attachmentStmt.setString(4, fileType); // 文件类型
                            attachmentStmt.setLong(5, 0L); // 文件大小，默认为0
                            attachmentStmt.setLong(6, currentTime); // 上传时间
                            attachmentStmt.setString(7, "中国人寿知识库附件"); // 描述

                            // 执行附件插入
                            int attachmentResult = attachmentStmt.executeUpdate();

                            if (attachmentResult > 0) {
                                System.out.println("成功插入附件：" + fileName);
                            }

                            // 关闭当前的PreparedStatement，以便下一次循环创建新的
                            attachmentStmt.close();
                            attachmentStmt = null;
                        }
                    }

                    // 提交事务
                    conn.commit();
                    System.out.println("成功插入文章：" + title);
                }
            }

        } catch (SQLException e) {
            // 发生异常时回滚事务
            try {
                if (conn != null) {
                    conn.rollback();
                }
            } catch (SQLException ex) {
                ex.printStackTrace();
            }
            System.err.println("数据库插入失败：" + e.getMessage());
            e.printStackTrace();
        } finally {
            // 关闭资源
            try {
                if (generatedKeys != null)
                    generatedKeys.close();
                if (attachmentStmt != null)
                    attachmentStmt.close();
                if (articleStmt != null)
                    articleStmt.close();
                if (conn != null)
                    conn.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 将文件上传至oss
     *
     * @param fileUrl  文件远程URL
     * @param fileName 文件名
     * @param fileType 文件类型
     * @return OSS文件URL
     */
    public String uploadFileToOss(String fileUrl, String fileName, String fileType) {
        // OSS配置参数
        String endpoint = "https://oss-ap-southeast-1.aliyuncs.com";
        String accessKeyId = "LTAI5tLiHh9ar6U7Ejodt3fU";
        String accessKeySecret = "******************************";
        String bucketName = "suboga";
        String objectName = "Insuriam/files/" + fileName + "_" + System.currentTimeMillis() + "." + fileType;

        // 创建OSSClient实例
        OSS ossClient = null;

        try {
            // 从远程URL获取输入流
            URL url = URI.create(fileUrl).toURL();
            try (InputStream inputStream = url.openStream()) {
                // 创建OSSClient实例
                ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

                // 上传文件流
                ossClient.putObject(bucketName, objectName, inputStream);

                // 生成文件访问URL
                String ossUrl = "https://resouce.insuraim.com/" + objectName;

                System.out.println("文件已上传至OSS: " + ossUrl);
                return ossUrl;
            }
        } catch (Exception e) {
            System.err.println("上传文件到OSS失败: " + e.getMessage());
            e.printStackTrace();
            return fileUrl; // 如果上传失败，返回原始URL
        } finally {
            // 关闭OSSClient
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    /**
     * 创建分类树
     */
    public void createCategoryTree() {
        // 这里可以放置示例的分类树数据用于测试
        // 实际使用时，可以传入从外部获取的JSON数据
        // 例如：String jsonStr = "..."; // 外部传入的JSON字符串
        // JsonNode categoryTree = objectMapper.readTree(jsonStr);
        // insertCategoryTree(categoryTree);

        try {
            System.out.println("开始处理分类树...");
            // 将字符串转换为JsonNode对象
            JsonNode categoryTree = objectMapper.readTree(getRawCategoryTreeJson());
            // 插入分类树
            insertCategoryTree(categoryTree);
            System.out.println("分类树处理完成");
        } catch (Exception e) {
            System.err.println("处理分类树时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 获取原始分类树JSON字符串
     * 实际使用时可以从外部文件或参数获取
     */
    private String getRawCategoryTreeJson() {
        // 这里返回实际的JSON字符串，实际使用时可能从文件或参数中获取
        return "{}"; // 替换为实际的JSON
    }

    /**
     * 将分类树插入数据库
     * 
     * @param categoryTree 分类树JSON数据
     */
    public void insertCategoryTree(JsonNode categoryTree) {
        // 数据库连接参数
        String url = "**************************************************************************************************************************";
        String username = "zeyu";
        String password = "Am123456.";

        // SQL语句
        String categorySql = "INSERT INTO knowledge_category (category_name, parent_id, description, created_at, updated_at, company_id) "
                +
                "VALUES (?, ?, ?, ?, ?, ?)";

        Connection conn = null;
        PreparedStatement categoryStmt = null;

        try {
            // 建立数据库连接
            conn = DriverManager.getConnection(url, username, password);

            // 关闭自动提交，开启事务
            conn.setAutoCommit(false);

            // 准备分类插入语句
            categoryStmt = conn.prepareStatement(categorySql, PreparedStatement.RETURN_GENERATED_KEYS);

            // 递归插入分类树，父ID初始为0（根分类）
            Long parentId = 0L;
            Long companyId = 1L; // 中国人寿公司ID，根据实际情况调整

            // 插入主分类及其子分类
            insertCategoryTreeNode(conn, categoryStmt, categoryTree, parentId, companyId);

            // 提交事务
            conn.commit();
            System.out.println("分类树成功插入数据库");

        } catch (SQLException e) {
            // 发生异常时回滚事务
            try {
                if (conn != null) {
                    conn.rollback();
                }
            } catch (SQLException ex) {
                ex.printStackTrace();
            }
            System.err.println("数据库插入失败：" + e.getMessage());
            e.printStackTrace();
        } finally {
            // 关闭资源
            try {
                if (categoryStmt != null)
                    categoryStmt.close();
                if (conn != null)
                    conn.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 递归插入分类树节点
     * 
     * @param conn      数据库连接
     * @param stmt      PreparedStatement对象
     * @param node      当前节点
     * @param parentId  父节点ID
     * @param companyId 公司ID
     * @return 插入节点的ID
     * @throws SQLException 数据库操作异常
     */
    private Long insertCategoryTreeNode(Connection conn, PreparedStatement stmt,
            JsonNode node, Long parentId, Long companyId) throws SQLException {

        if (node == null) {
            return null;
        }

        // 获取当前时间戳
        long currentTime = System.currentTimeMillis();

        // 获取分类信息
        String name = node.get("name").asText();
        String description = generateDescription(node);

        // 设置参数
        stmt.setString(1, name); // 分类名称
        stmt.setLong(2, parentId); // 父级ID
        stmt.setString(3, description); // 描述
        stmt.setLong(4, currentTime); // 创建时间
        stmt.setLong(5, currentTime); // 更新时间
        stmt.setLong(6, companyId); // 公司ID

        // 执行插入
        int result = stmt.executeUpdate();
        Long categoryId = null;

        // 获取生成的分类ID
        if (result > 0) {
            ResultSet generatedKeys = stmt.getGeneratedKeys();
            if (generatedKeys.next()) {
                categoryId = generatedKeys.getLong(1);
                int level = node.has("level") ? node.get("level").asInt() : 0;
                System.out.println("成功插入分类：" + name + "，ID：" + categoryId + "，层级：" + level);

                // 处理子分类
                if (node.has("children") && !node.get("children").isNull() && node.get("children").size() > 0) {
                    JsonNode children = node.get("children");
                    for (JsonNode child : children) {
                        // 递归插入子分类
                        insertCategoryTreeNode(conn, stmt, child, categoryId, companyId);
                    }
                }
            }
            generatedKeys.close();
        }

        return categoryId;
    }

    /**
     * 生成分类描述
     * 
     * @param node 分类节点
     * @return 描述信息
     */
    private String generateDescription(JsonNode node) {
        StringBuilder description = new StringBuilder();

        // 添加关键信息到描述中
        if (node.has("id") && !node.get("id").isNull()) {
            description.append("外部ID: ").append(node.get("id").asText()).append("; ");
        }

        if (node.has("hierarchyCode") && !node.get("hierarchyCode").isNull()) {
            description.append("层级编码: ").append(node.get("hierarchyCode").asText()).append("; ");
        }

        if (node.has("position") && !node.get("position").isNull()) {
            description.append("位置: ").append(node.get("position").asInt()).append("; ");
        }

        if (node.has("level") && !node.get("level").isNull()) {
            description.append("层级: ").append(node.get("level").asInt()).append("; ");
        }

        if (node.has("nameCn") && !node.get("nameCn").isNull() && !node.get("nameCn").asText().isEmpty()) {
            description.append("中文名: ").append(node.get("nameCn").asText()).append("; ");
        }

        if (node.has("nameEn") && !node.get("nameEn").isNull() && !node.get("nameEn").asText().isEmpty()) {
            description.append("英文名: ").append(node.get("nameEn").asText()).append("; ");
        }

        if (node.has("status") && !node.get("status").isNull()) {
            description.append("状态: ").append(node.get("status").asInt()).append("; ");
        }

        if (node.has("type") && !node.get("type").isNull()) {
            description.append("类型: ").append(node.get("type").asInt()).append("; ");
        }

        if (node.has("logo") && !node.get("logo").isNull() && !node.get("logo").asText().equals("null")) {
            description.append("Logo: ").append(node.get("logo").asText()).append("; ");
        }

        if (description.length() > 0) {
            description.append("来源: 中国人寿知识库");
        } else {
            description.append("中国人寿知识库分类");
        }

        return description.toString();
    }

    /**
     * 将分类树插入数据库
     * 
     * @param categoryTreeJson 分类树JSON字符串
     * @throws IOException JSON解析异常
     */
    public void insertCategoryTree(String categoryTreeJson) throws IOException {
        JsonNode categoryTree = objectMapper.readTree(categoryTreeJson);
        insertCategoryTree(categoryTree);
    }

}