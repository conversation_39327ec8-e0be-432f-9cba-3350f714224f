package com.sub.baoxian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sub.baoxian.model.entity.ExamAnswer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 模拟考试答案Mapper接口
 */
@Mapper
public interface ExamAnswerMapper extends BaseMapper<ExamAnswer> {
    

    /**
     * 根据题目ID获取所有答案选项
     * 
     * @param questionId 题目ID
     * @return 所有答案选项列表
     */
    @Select("SELECT * FROM exam_answers WHERE question_id = #{questionId} ORDER BY id ASC")
    List<ExamAnswer> getAllAnswersByQuestionId(@Param("questionId") Long questionId);
} 