package com.sub.baoxian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sub.baoxian.model.entity.ExamQuestion;
import com.sub.baoxian.model.dto.QuestionAnswerDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;


import java.util.List;

/**
 * 模拟考试题目Mapper接口
 */
@Mapper
public interface ExamQuestionMapper extends BaseMapper<ExamQuestion> {
    
    
    /**
     * 使用LEFT JOIN联表查询题目及其所有答案选项，避免N+1查询问题
     * 
     * @param categoryId 类目ID
     * @return 扁平化的题目和答案组合列表
     */
    @Select("SELECT q.id, q.exam_id, q.difficulty, q.major_chapter, q.chapter, q.question, " +
            "q.feedback_correct, q.feedback_incorrect, q.status, q.created_at, q.updated_at, " +
            "a.id AS answer_id, a.question_id, a.option_text, a.is_correct, " +
            "a.created_at AS answer_created_at, a.updated_at AS answer_updated_at " +
            "FROM exam_questions q " +
            "LEFT JOIN exam_answers a ON q.id = a.question_id " +
            "WHERE q.exam_id = #{categoryId} " +
            "ORDER BY q.id, a.id")
    List<QuestionAnswerDTO> getQuestionsAndAnswersByCategoryId(@Param("categoryId") Integer categoryId);
} 