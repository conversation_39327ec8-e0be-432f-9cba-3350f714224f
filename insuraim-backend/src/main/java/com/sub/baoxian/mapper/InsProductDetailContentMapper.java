package com.sub.baoxian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.model.entity.InsProductDetailContent;
import com.sub.baoxian.model.vo.InsProductDetailContentVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 产品内容Mapper接口
 */
@Mapper
public interface InsProductDetailContentMapper extends BaseMapper<InsProductDetailContent> {

    /**
     * 根据产品ID查询内容列表
     * 
     * @param productId 产品ID
     * @return 内容列表
     */
    List<InsProductDetailContentVO> selectByProductId(@Param("productId") Integer productId);

    /**
     * 根据标题ID查询内容列表
     * 
     * @param titleId 标题ID
     * @return 内容列表
     */
    List<InsProductDetailContentVO> selectByTitleId(@Param("titleId") Integer titleId);

    /**
     * 根据产品ID和标题ID查询内容
     * 
     * @param productId 产品ID
     * @param titleId 标题ID
     * @return 内容对象
     */
    InsProductDetailContentVO selectByProductIdAndTitleId(@Param("productId") Integer productId, 
                                                          @Param("titleId") Integer titleId);

    /**
     * 分页查询内容列表
     * 
     * @param page 分页对象
     * @param productId 产品ID
     * @param titleId 标题ID
     * @param contentValue 内容值（模糊查询）
     * @return 分页结果
     */
    IPage<InsProductDetailContentVO> selectContentPage(Page<?> page,
                                                       @Param("productId") Integer productId,
                                                       @Param("titleId") Integer titleId,
                                                       @Param("contentValue") String contentValue);

    /**
     * 批量查询多个产品的内容
     * 
     * @param productIds 产品ID列表
     * @param titleIds 标题ID列表
     * @return 内容列表
     */
    List<InsProductDetailContentVO> selectByProductIdsAndTitleIds(@Param("productIds") List<Integer> productIds,
                                                                  @Param("titleIds") List<Integer> titleIds);
}
