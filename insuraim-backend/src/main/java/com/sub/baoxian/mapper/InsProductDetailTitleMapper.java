package com.sub.baoxian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.model.entity.InsProductDetailTitle;
import com.sub.baoxian.model.vo.InsProductDetailTitleVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 产品分类标题Mapper接口
 */
@Mapper
public interface InsProductDetailTitleMapper extends BaseMapper<InsProductDetailTitle> {

    /**
     * 根据分类ID查询标题列表
     * 
     * @param categoryId 分类ID
     * @return 标题列表
     */
    List<InsProductDetailTitleVO> selectByCategoryId(@Param("categoryId") Integer categoryId);

    /**
     * 根据父标题ID查询子标题列表
     * 
     * @param parentId 父标题ID
     * @return 子标题列表
     */
    List<InsProductDetailTitleVO> selectByParentId(@Param("parentId") Integer parentId);

    /**
     * 分页查询标题列表
     * 
     * @param page 分页对象
     * @param categoryId 分类ID
     * @param parentId 父标题ID
     * @param titleName 标题名称（模糊查询）
     * @return 分页结果
     */
    IPage<InsProductDetailTitleVO> selectTitlePage(Page<?> page, 
                                                   @Param("categoryId") Integer categoryId,
                                                   @Param("parentId") Integer parentId,
                                                   @Param("titleName") String titleName);

    /**
     * 获取同级标题的最大排序值
     * 
     * @param categoryId 分类ID
     * @param parentId 父标题ID
     * @return 最大排序值
     */
    Integer getMaxRank(@Param("categoryId") Integer categoryId, @Param("parentId") Integer parentId);
}
