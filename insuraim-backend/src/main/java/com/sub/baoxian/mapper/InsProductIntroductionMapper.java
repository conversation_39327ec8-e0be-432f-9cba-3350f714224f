package com.sub.baoxian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sub.baoxian.model.entity.InsProductIntroduction;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 产品介绍表Mapper接口
 */
@Mapper
public interface InsProductIntroductionMapper extends BaseMapper<InsProductIntroduction> {

    /**
     * 根据产品ID查询产品介绍信息
     *
     * @param productId 产品ID
     * @return 产品介绍信息
     */
    InsProductIntroduction selectByProductId(@Param("productId") Long productId);

    /**
     * 根据产品ID删除产品介绍信息
     *
     * @param productId 产品ID
     * @return 删除的记录数
     */
    int deleteByProductId(@Param("productId") Long productId);
}
