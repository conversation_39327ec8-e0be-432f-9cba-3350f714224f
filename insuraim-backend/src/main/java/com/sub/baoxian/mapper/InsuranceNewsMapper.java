package com.sub.baoxian.mapper;



import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.sub.baoxian.model.entity.InsuranceNews;
import com.sub.baoxian.model.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InsuranceNewsMapper extends BaseMapper<InsuranceNews> {



    InsuranceNews getInsuranceNews(@Param("groupHash") String groupHash);

    List<InsuranceNews> getInsuranceNewsList(InsuranceNews insuranceNews);

    int batchInsuranceNews(List<InsuranceNews> list);

    int updateInsuranceNews(InsuranceNews insuranceNews);
}
