package com.sub.baoxian.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.sub.baoxian.model.bo.getKnowledgeBaseByIdBO;
import com.sub.baoxian.model.entity.KnowledgeBase;
import com.sub.baoxian.model.entity.KnowledgeBaseCategory;
import com.sub.baoxian.model.entity.KnowledgeBaseCompany;

@Mapper
public interface KnowledgeBaseMapper {

    /**
     * 根据分类ID获取知识库信息列表
     * 
     * @param categoryId
     * @return
     */
    List<KnowledgeBase> getLoreListByCategoryId(Long categoryId);

    /**
     * 根据ID获取知识库详情
     * 
     * @param id
     * @return
     */
    getKnowledgeBaseByIdBO getKnowledgeBaseById(Long id);

    /**
     * 更新知识库浏览量
     * 
     * @param id
     */
    void updateKnowledgeBaseViewCount(Long id);

    /**
     * 获取保险公司列表
     * 
     * @return
     */
    List<KnowledgeBaseCompany> getCompanyList();

    /**
     * 根据保险公司ID获取知识库专题列表
     * 
     * @param companyId
     * @return
     */
    List<KnowledgeBaseCategory> getCategoryListByCompanyId(Long companyId);

    /**
     * 根据分类ID获取分类信息
     * 
     * @param categoryId
     * @return
     */
    KnowledgeBaseCategory getCategoryInfo(Long categoryId);

    /**
     * 根据保险公司ID获取保险公司信息
     * 
     * @param companyId
     * @return
     */
    KnowledgeBaseCompany getCompanyInfo(Long companyId);

    /**
     * 获取所有分类信息
     * 
     * @return 分类列表
     */
    List<KnowledgeBaseCategory> getAllCategories();

    /**
     * 根据父级ID获取所有子分类
     * 
     * @param parentId 父级ID
     * @return 子分类列表
     */
    List<KnowledgeBaseCategory> getChildrenByParentId(Long parentId);
}
