package com.sub.baoxian.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.sub.baoxian.model.entity.PersonalCloud;
import com.sub.baoxian.model.entity.PCFolder;
import com.sub.baoxian.model.entity.PCFile;

import java.util.List;

@Mapper
public interface PersonalCloudMapper {
    
    /**
     * 通过用户ID获取云盘基本信息
     * @param userId 用户ID
     * @return 云盘信息
     */
    PersonalCloud getCloudInfoByUserId(@Param("userId") Long userId);
    
    /**
     * 插入个人云盘记录
     * @param personalCloud 个人云盘实体
     * @return 影响行数
     */
    int insertPersonalCloud(PersonalCloud personalCloud);
    
    /**
     * 更新个人云盘信息
     * @param personalCloud 个人云盘实体
     * @return 影响行数
     */
    int updatePersonalCloud(PersonalCloud personalCloud);
    
    /**
     * 更新已使用空间
     * @param userId 用户ID
     * @param usedStorage 已使用空间
     * @return 影响行数
     */
    int updateUsedStorage(@Param("userId") Long userId, @Param("usedStorage") Long usedStorage, @Param("updatedAt") Long updatedAt);
    
    /* 文件夹操作 */
    
    /**
     * 获取文件夹列表
     * @param userId 用户ID
     * @param parentId 父文件夹ID，0表示根目录
     * @return 文件夹列表
     */
    List<PCFolder> getFoldersByParentId(@Param("userId") Long userId, @Param("parentId") Long parentId);
    
    /**
     * 获取文件夹详情
     * @param folderId 文件夹ID
     * @return 文件夹信息
     */
    PCFolder getFolderById(@Param("folderId") Long folderId);
    
    /**
     * 创建文件夹
     * @param folder 文件夹实体
     * @return 影响行数
     */
    int insertFolder(PCFolder folder);
    
    /**
     * 更新文件夹
     * @param folder 文件夹实体
     * @return 影响行数
     */
    int updateFolder(PCFolder folder);
    
    /**
     * 删除文件夹
     * @param folderId 文件夹ID
     * @return 影响行数
     */
    int deleteFolder(@Param("folderId") Long folderId);
    
    /**
     * 获取子文件夹数量
     * @param folderId 文件夹ID
     * @return 数量
     */
    int countSubFolders(@Param("folderId") Long folderId);
    
    /* 文件操作 */
    
    /**
     * 获取文件夹内文件列表
     * @param userId 用户ID
     * @param folderId 文件夹ID
     * @param fileType 文件类型(可选)
     * @return 文件列表
     */
    List<PCFile> getFilesByFolderId(@Param("userId") Long userId, @Param("folderId") Long folderId);
    
    /**
     * 获取文件详情
     * @param fileId 文件ID
     * @return 文件信息
     */
    PCFile getFileById(@Param("fileId") Long fileId);
    
    /**
     * 插入文件记录
     * @param file 文件实体
     * @return 影响行数
     */
    int insertFile(PCFile file);
    
    /**
     * 更新文件信息
     * @param file 文件实体
     * @return 影响行数
     */
    int updateFile(PCFile file);
    
    /**
     * 删除文件
     * @param fileId 文件ID
     * @return 影响行数
     */
    int deleteFile(@Param("fileId") Long fileId);
    
    /**
     * 获取文件夹内文件总大小
     * @param folderId 文件夹ID
     * @return 文件总大小(字节)
     */
    Long getFilesTotalSizeByFolderId(@Param("folderId") Long folderId);
    
    /**
     * 更新文件下载次数
     * @param fileId 文件ID
     * @return 影响行数
     */
    int incrementDownloadCount(@Param("fileId") Long fileId);
}
