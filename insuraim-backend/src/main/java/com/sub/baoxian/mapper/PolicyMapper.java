package com.sub.baoxian.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sub.baoxian.policymanagement.constant.PolicyRecordStatusEnum;
import com.sub.baoxian.policymanagement.constant.PolicyStatusEnum;
import com.sub.baoxian.policymanagement.model.entity.BeneficiaryInfo;
import com.sub.baoxian.policymanagement.model.entity.InsuredInfo;
import com.sub.baoxian.policymanagement.model.entity.Policy;
import com.sub.baoxian.policymanagement.model.entity.PolicyOrder;
import com.sub.baoxian.policymanagement.model.entity.PolicyStatusAttachment;
import com.sub.baoxian.policymanagement.model.entity.PolicyStatusHistory;
import com.sub.baoxian.policymanagement.model.entity.PolicyholderInfo;

@Mapper
public interface PolicyMapper extends BaseMapper<Policy> {

        /**
         * 创建保单订单
         *
         * @param policyOrder 保单订单信息
         * @return 创建成功的记录数
         */
        int createPolicyOrder(PolicyOrder policyOrder);

        /**
         * 创建保单
         *
         * @param policy 保单信息
         */
        void createPolicy(Policy policy);

        /**
         * 创建投保人信息
         *
         * @param policyholderInfo 投保人信息
         */
        void createPolicyholderInfo(PolicyholderInfo policyholderInfo);

        /**
         * 创建被保人信息
         *
         * @param insuredInfo 被保人信息
         */
        void createInsuredInfo(InsuredInfo insuredInfo);

        /**
         * 创建受益人信息
         *
         * @param beneficiaryInfo 受益人信息
         */
        void createBeneficiaryInfo(BeneficiaryInfo beneficiaryInfo);

        /**
         * 创建保单状态历史记录
         *
         * @param policyStatusHistory 保单状态历史信息
         */
        void createPolicyStatusHistory(PolicyStatusHistory policyStatusHistory);

        /**
         * 创建保单状态附件
         *
         * @param policyStatusAttachment 保单状态附件信息
         */
        void createPolicyStatusAttachment(PolicyStatusAttachment policyStatusAttachment);

        /**
         * 根据保单ID获取历史状态列表
         *
         * @param policyId 保单ID
         * @return 保单状态历史列表
         */
        List<PolicyStatusHistory> getHistoryStatus(Long policyId);

        // 根据订单ID查询保单信息
        Policy getPolicyByOrderId(@Param("orderId") Long orderId);

        // 根据保单ID查询投保人信息
        PolicyholderInfo getPolicyholderInfoByPolicyId(@Param("policyId") Long policyId);

        // 根据保单ID查询被保人信息
        InsuredInfo getInsuredInfoByPolicyId(@Param("policyId") Long policyId);

        // 根据保单ID查询受益人列表
        List<BeneficiaryInfo> getBeneficiaryInfosByPolicyId(@Param("policyId") Long policyId);

        // 根据保单ID查询状态历史列表
        List<PolicyStatusHistory> getPolicyStatusHistoriesByPolicyId(@Param("policyId") Long policyId);

        // 根据状态历史ID查询状态附件列表
        List<PolicyStatusAttachment> getPolicyStatusAttachmentsByHistoryId(
                        @Param("statusHistoryId") Long statusHistoryId);

        // 根据保单ID查询所有状态附件列表
        List<PolicyStatusAttachment> getPolicyStatusAttachmentsByPolicyId(@Param("policyId") Long policyId);

        // 根据状态历史ID查询状态历史记录
        PolicyStatusHistory getPolicyStatusHistoryByHistoryId(@Param("statusHistoryId") Long statusHistoryId);

        /**
         * 更新保单状态
         *
         * @param policyId  保单ID
         * @param status    新状态
         * @param updatedAt 更新时间
         * @return 更新成功的记录数
         */
        int updatePolicyStatus(@Param("policyId") Long policyId, @Param("status") PolicyStatusEnum status,
                        @Param("updatedAt") Long updatedAt, @Param("effectiveDate") Long effectiveDate);

        /**
         * 更新保单状态历史记录的记录状态
         *
         * @param policyId     保单ID
         * @param statusCode   状态码
         * @param recordStatus 记录状态
         * @return 更新成功的记录数
         */
        int updatePolicyStatusHistoryRecordStatus(@Param("policyId") Long policyId,
                        @Param("statusCode") PolicyStatusEnum statusCode,
                        @Param("recordStatus") PolicyRecordStatusEnum recordStatus,
                        @Param("updatedAt") Long updatedAt);

        /**
         * 根据保单ID和状态码查询保单状态历史记录
         *
         * @param policyId   保单ID
         * @param statusCode 状态码
         * @return 保单状态历史记录
         */
        PolicyStatusHistory getPolicyStatusHistoryByStatusCode(@Param("policyId") Long policyId,
                        @Param("statusCode") PolicyStatusEnum statusCode);

        /**
         * 根据保单ID查询保单订单信息
         *
         * @param policyId 保单ID
         * @return 保单订单信息
         */
        PolicyOrder getPolicyOrderById(@Param("policyId") Long policyId);

        /**
         * 更新保单持有人信息
         *
         * @param policyholderInfo 保单持有人信息
         * @return 更新成功的记录数
         */
        int updatePolicyholderInfo(PolicyholderInfo policyholderInfo);

        /**
         * 更新受保人信息
         *
         * @param insuredInfo 受保人信息
         * @return 更新成功的记录数
         */
        int updateInsuredInfo(InsuredInfo insuredInfo);

        /**
         * 更新受益人信息
         *
         * @param beneficiaryInfo 受益人信息
         * @return 更新成功的记录数
         */
        int updateBeneficiaryInfo(BeneficiaryInfo beneficiaryInfo);

        /**
         * 根据状态历史ID删除所有相关附件
         *
         * @param statusHistoryId 状态历史ID
         * @return 删除的记录数
         */
        int deletePolicyStatusAttachment(@Param("statusHistoryId") Long statusHistoryId);

        /**
         * 根据状态历史ID删除状态历史记录
         *
         * @param statusHistoryId 状态历史ID
         * @return 删除的记录数
         */
        int deletePolicyStatusHistory(@Param("statusHistoryId") Long statusHistoryId);

        /**
         * 根据订单号统计记录数
         *
         * @param orderNo 订单号
         * @return 记录数
         */
        int countByOrderNo(@Param("orderNo") String orderNo);

        /**
         * 根据保单号统计记录数
         *
         * @param policyNo 保单号
         * @return 记录数
         */
        int countByPolicyNo(@Param("policyNo") String policyNo);

        /**
         * 更新保单号
         *
         * @param policyId  保单ID
         * @param policyNo  保单号
         * @param updatedAt 更新时间
         * @return 更新成功的记录数
         */
        int updatePolicyNo(@Param("policyId") Long policyId, @Param("policyNo") String policyNo,
                        @Param("updatedAt") Long updatedAt);

        /**
         * 根据受益人ID查询受益人信息
         *
         * @param beneficiaryId 受益人ID
         * @return 受益人信息
         */
        BeneficiaryInfo getBeneficiaryInfoById(@Param("beneficiaryId") Long beneficiaryId);

        /**
         * 删除受益人信息
         *
         * @param beneficiaryId 受益人ID
         * @return 删除的记录数
         */
        int deleteBeneficiaryInfo(@Param("beneficiaryId") Long beneficiaryId);

        /**
         * 根据保单ID删除所有受益人信息
         *
         * @param policyId 保单ID
         * @return 删除的记录数
         */
        int deleteBeneficiaryInfoByPolicyId(@Param("policyId") Long policyId);

        /**
         * 根据保单ID删除被保人信息
         *
         * @param policyId 保单ID
         * @return 删除的记录数
         */
        int deleteInsuredInfoByPolicyId(@Param("policyId") Long policyId);

        /**
         * 根据保单ID删除保单状态历史记录
         *
         * @param policyId 保单ID
         * @return 删除的记录数
         */
        int deletePolicyStatusHistoryByPolicyId(@Param("policyId") Long policyId);

        /**
         * 根据保单ID删除保单状态附件
         *
         * @param policyId 保单ID
         * @return 删除的记录数
         */
        int deletePolicyStatusAttachmentByPolicyId(@Param("policyId") Long policyId);

}
