package com.sub.baoxian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.policymanagement.constant.OrderStatusEnum;
import com.sub.baoxian.policymanagement.constant.PolicyStatusEnum;
import com.sub.baoxian.policymanagement.model.entity.PolicyOrder;
import com.sub.baoxian.policymanagement.model.vo.GetPageListVO;

import org.apache.ibatis.annotations.Param;

public interface PolicyOrderMapper extends BaseMapper<PolicyOrder> {

    /**
     * 根据订单ID查询订单详情
     *
     * @param orderId 订单ID
     * @return 订单详情
     */
    PolicyOrder getDetailByOrderId(@Param("orderId") Long orderId);

    /**
     * 分页查询订单列表
     *
     * @param page             分页对象
     * @param userId           用户ID
     * @param orderNo          订单号
     * @param policyNo         保单号
     * @param policyholderName 投保人姓名
     * @param insuredName      被保险人姓名
     * @param policyStatus     保单状态
     * @param status           订单状态
     * @param createdAt        创建时间
     * @param updatedAt        更新时间
     * @return 分页结果
     */
    Page<GetPageListVO> pageList(
            @Param("page") Page<GetPageListVO> page,
            @Param("userId") Long userId,
            @Param("search") String search,
            @Param("policyStatus") PolicyStatusEnum policyStatus,
            @Param("status") OrderStatusEnum status,
            @Param("createdAt") String createdAt,
            @Param("updatedAt") String updatedAt);
}
