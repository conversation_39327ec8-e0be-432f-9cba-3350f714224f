package com.sub.baoxian.mapper;

import com.sub.baoxian.productInfoService.BO.CalculationDataBO;
import com.sub.baoxian.productInfoService.entity.FileLinkPo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProductCalMapper {
    CalculationDataBO profitCalculator(@Param("productId") Integer productId, @Param("age") Integer age, @Param("type") String type);
    List<FileLinkPo> selectLink(@Param("productName") String productName, @Param("age") String age,@Param("company") String company);
    List<FileLinkPo> selectCompany();

    int insertProductLink(@Param("productName") String productName,@Param("age") String age,@Param("productLink") String productLink,@Param("company") String  company);

}

