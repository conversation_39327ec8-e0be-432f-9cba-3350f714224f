package com.sub.baoxian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.model.entity.ProductPo;
import com.sub.baoxian.productInfoService.BO.CalculationDataBO;
import com.sub.baoxian.productInfoService.DTO.ProductDisplayDTO;
import com.sub.baoxian.productInfoService.entity.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;

@Mapper
public interface ProductDao extends BaseMapper<ProductIntroPO> {
    ProductPo getProductById(@Param("id") Integer id);
    //展示所有产品或者搜索产品
    IPage<ProductIntroPO> getProductDisplay(Page<?> page,@Param("search") String search,
                                            @Param("mainType") String mainType,
                                            @Param("region") String  region,
                                            @Param("insurer") String insurer);
    //展示可以计算IRR的产品
    IPage<ProductIntroPO> getProductDisplayIRR(Page<?> page, ProductDisplayDTO productDisplayDTO);

    IPage<ProductCalDisplayPo> getProductDisplayIRR2(Page<?> page,ProductDisplayDTO productDisplayDTO);
    //展示IRR里的可选的货币
    List<String> getIRRProductCurrency();

    List<Integer> lifeProductIds();

    ProductIntroPO getProductIntro(@Param("productId") Integer productId);

    List<String> productCategoryList();

    List<ProductAttributeAO> getInsuranceCode(@Param("productId") Integer productId);
    List<ProductAttributeAO> getInsuranceDownload(@Param("productId") Integer productId);

    ProductAttributePo getProductIRRAndDividendDynamic(@Param("productId") Integer productId,@Param("age") Integer age);
    ProductAttributePo getProductIRRAndDividend(@Param("productId") Integer productId);
    CalculationDataBO getProductIRRAndDividend2(@Param("productId") Integer productId, @Param("age") Integer age);

    IPage<InsuranceFeePo> ProductListOfInsuranceFee(Page<?> page,@Param("search") String search,
                                                    @Param("type") String type,
                                                    @Param("insurer") String insurer,
                                                    @Param("region") String region);
    InsuranceFeePo getCalculationFactors(@Param("id") Integer id);
    List<String> displayCalculationType();

    IPage<RealizationRatePo> getRealizationProductsName(Page<?> page);
    List<String> getRealizationProductsName1();

    //辅助test测试插入不涉及业务逻辑
    int insertss(InsurancePostPo insurancePostPo);
    int update111(InsurancePostPo insurancePostPo);

    List<ProductAttributePo> getProductIRRAndDividend111111();
    //----
    IPage<RealizationRatePo> getProductRealizationRateByName(Page<?> page, @Param("search") String search);
    List<RealizationRatePo> getProductRealizationRateByNameDistinct(@Param("search") String search);

    List<RealizationRatePo> getDetailsOfDividendProduct(@Param("search") String search,@Param("insurer") String insurer, @Param("region") String region);
    List<RealizationRatePo> getDividendProductStar();
    InsurancePostPo getPostInfoOfInsurance(@Param("id") Integer id);
    IPage<InsurancePostPo> displayAvailablePost(Page<?> page, @Param("search") String search);

    String getDynamicIRR(@Param("productId") Integer productId);
}
