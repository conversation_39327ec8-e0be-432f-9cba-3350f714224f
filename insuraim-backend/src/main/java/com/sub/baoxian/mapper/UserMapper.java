package com.sub.baoxian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sub.baoxian.model.entity.User;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import java.util.List;
import java.util.Map;

/**
 * 用户Mapper接口
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {
    
    /**
     * 批量查询各个组织的用户数量
     * @return 包含orgId和userCount的Map列表
     */
    @Select("SELECT org_id AS orgId, COUNT(*) AS userCount FROM organization_user GROUP BY org_id")
    List<Map<String, Object>> getOrgUserCounts();
} 