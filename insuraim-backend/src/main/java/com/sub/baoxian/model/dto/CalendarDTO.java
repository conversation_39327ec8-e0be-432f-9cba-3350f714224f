package com.sub.baoxian.model.dto;

import lombok.Data;

@Data
public class CalendarDTO {
    /**
     * 日程ID
     */
    private Long id;
    
    /**
     * 日程标题
     */
    private String title;
    
    /**
     * 日程内容
     */
    private String content;
    
    
    /**
     * 日程时间
     */
    private Long calendarTime;

    /**
     * 提醒时间(时间戳)
     */
    private Long remindTime;
    
    /**
     * 重复规则：daily-每天，weekly-每周，monthly-每月，yearly-每年，none-不重复
     */
    private String repeatRule;
    
    /**
     * 完成状态：0-进行中，1-已完成，2-已取消
     */
    private Integer status;
    
    /**
     * 备注
     */
    private String remark;
} 