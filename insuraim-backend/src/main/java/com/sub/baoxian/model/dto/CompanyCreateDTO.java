package com.sub.baoxian.model.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;

/**
 * 保险公司创建DTO
 */
@Data
@Schema(description = "保险公司创建DTO")
public class CompanyCreateDTO {

    /**
     * 保险公司代码
     */
    @Schema(description = "保险公司代码", required = true)
    private String code;

    /**
     * 保险公司名称
     */
    @Schema(description = "保险公司名称", required = true)
    private String name;

    /**
     * 公司LOGO URL
     */
    @Schema(description = "公司LOGO URL")
    private String logoUrl;

    /**
     * 公司Banner图片URL
     */
    @Schema(description = "公司Banner图片URL")
    private String bannerUrl;

    /**
     * 公司详细描述
     */
    @Schema(description = "公司详细描述")
    private String description;

    /**
     * 公司官网URL
     */
    @Schema(description = "公司官网URL")
    private String websiteUrl;

    /**
     * 行业排名
     */
    @Schema(description = "行业排名")
    private Integer companyRank;

    /**
     * 所属地区
     */
    @Schema(description = "所属地区")
    private String region;

    /**
     * 当前征费率
     */
    @Schema(description = "当前征费率")
    private BigDecimal levyRate;

    /**
     * 汇率参考货币
     */
    @Schema(description = "汇率参考货币")
    private String exchangeCurrency;

    /**
     * 参考汇率值
     */
    @Schema(description = "参考汇率值")
    private BigDecimal exchangeRate;

    /**
     * 汇率来源URL
     */
    @Schema(description = "汇率来源URL")
    private String exchangeRateUrl;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;
}
