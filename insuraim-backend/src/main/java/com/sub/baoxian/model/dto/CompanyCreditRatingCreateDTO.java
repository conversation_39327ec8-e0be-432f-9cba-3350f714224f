package com.sub.baoxian.model.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 保险公司信用评级创建DTO
 */
@Data
@Schema(description = "保险公司信用评级创建DTO")
public class CompanyCreditRatingCreateDTO {

    /**
     * 保险公司唯一标识码
     */
    @Schema(description = "保险公司唯一标识码", required = true)
    private String code;

    /**
     * 评级项目
     */
    @Schema(description = "评级项目", required = true)
    private String project;

    /**
     * 评级结果
     */
    @Schema(description = "评级结果", required = true)
    private String rating;

    /**
     * 评级机构
     */
    @Schema(description = "评级机构", required = true)
    private String ratingAgency;

    /**
     * 评级时间戳(毫秒)
     */
    @Schema(description = "评级时间戳(毫秒)", required = true)
    private Long time;
}
