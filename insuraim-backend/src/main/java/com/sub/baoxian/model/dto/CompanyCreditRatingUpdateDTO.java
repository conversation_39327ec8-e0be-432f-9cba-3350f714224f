package com.sub.baoxian.model.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 保险公司信用评级更新DTO
 */
@Data
@Schema(description = "保险公司信用评级更新DTO")
public class CompanyCreditRatingUpdateDTO {

    /**
     * 评级ID
     */
    @Schema(description = "评级ID", required = true)
    private Integer id;

    /**
     * 保险公司唯一标识码
     */
    @Schema(description = "保险公司唯一标识码")
    private String code;

    /**
     * 评级项目
     */
    @Schema(description = "评级项目")
    private String project;

    /**
     * 评级结果
     */
    @Schema(description = "评级结果")
    private String rating;

    /**
     * 评级机构
     */
    @Schema(description = "评级机构")
    private String ratingAgency;

    /**
     * 评级时间戳(毫秒)
     */
    @Schema(description = "评级时间戳(毫秒)")
    private Long time;
}
