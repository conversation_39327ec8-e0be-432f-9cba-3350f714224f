package com.sub.baoxian.model.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 保险公司文件创建DTO
 */
@Data
@Schema(description = "保险公司文件创建DTO")
public class CompanyFileCreateDTO {

    /**
     * 保险公司唯一标识码
     */
    @Schema(description = "保险公司唯一标识码", required = true)
    private String code;

    /**
     * 上传者/作者
     */
    @Schema(description = "上传者/作者", required = true)
    private String author;

    /**
     * 原始文件名
     */
    @Schema(description = "原始文件名", required = true)
    private String fileName;

    /**
     * 文件存储路径(URL)
     */
    @Schema(description = "文件存储路径(URL)", required = true)
    private String filePath;

    /**
     * 上传时间戳(毫秒)
     */
    @Schema(description = "上传时间戳(毫秒)")
    private Long uploadTime;
}
