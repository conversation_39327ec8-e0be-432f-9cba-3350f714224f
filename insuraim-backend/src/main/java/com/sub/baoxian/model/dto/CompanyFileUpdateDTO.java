package com.sub.baoxian.model.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 保险公司文件更新DTO
 */
@Data
@Schema(description = "保险公司文件更新DTO")
public class CompanyFileUpdateDTO {

    /**
     * 文件ID
     */
    @Schema(description = "文件ID", required = true)
    private Integer id;

    /**
     * 保险公司唯一标识码
     */
    @Schema(description = "保险公司唯一标识码")
    private String code;

    /**
     * 上传者/作者
     */
    @Schema(description = "上传者/作者")
    private String author;

    /**
     * 原始文件名
     */
    @Schema(description = "原始文件名")
    private String fileName;

    /**
     * 文件存储路径(URL)
     */
    @Schema(description = "文件存储路径(URL)")
    private String filePath;

    /**
     * 上传时间戳(毫秒)
     */
    @Schema(description = "上传时间戳(毫秒)")
    private Long uploadTime;
}
