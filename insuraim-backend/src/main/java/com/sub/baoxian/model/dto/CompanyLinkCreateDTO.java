package com.sub.baoxian.model.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 保险公司链接创建DTO
 */
@Data
@Schema(description = "保险公司链接创建DTO")
public class CompanyLinkCreateDTO {

    /**
     * 关联保险公司编码
     */
    @Schema(description = "关联保险公司编码", required = true)
    private String code;

    /**
     * 链接描述
     */
    @Schema(description = "链接描述", required = true)
    private String description;

    /**
     * 链接地址
     */
    @Schema(description = "链接地址", required = true)
    private String linkUrl;
}
