package com.sub.baoxian.model.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 保险公司链接更新DTO
 */
@Data
@Schema(description = "保险公司链接更新DTO")
public class CompanyLinkUpdateDTO {

    /**
     * 链接ID
     */
    @Schema(description = "链接ID", required = true)
    private Integer id;

    /**
     * 关联保险公司编码
     */
    @Schema(description = "关联保险公司编码")
    private String code;

    /**
     * 链接描述
     */
    @Schema(description = "链接描述")
    private String description;

    /**
     * 链接地址
     */
    @Schema(description = "链接地址")
    private String linkUrl;
}
