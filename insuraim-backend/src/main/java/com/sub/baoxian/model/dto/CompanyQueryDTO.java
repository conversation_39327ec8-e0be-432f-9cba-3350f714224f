package com.sub.baoxian.model.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 保险公司查询DTO
 */
@Data
@Schema(description = "保险公司查询DTO")
public class CompanyQueryDTO {

    /**
     * 页码，默认1
     */
    @Schema(description = "页码", defaultValue = "1")
    private Long pageNum = 1L;

    /**
     * 每页大小，默认10
     */
    @Schema(description = "每页大小", defaultValue = "10")
    private Long pageSize = 10L;

    /**
     * 保险公司代码
     */
    @Schema(description = "保险公司代码")
    private String code;

    /**
     * 保险公司名称（模糊查询）
     */
    @Schema(description = "保险公司名称（模糊查询）")
    private String name;

    /**
     * 所属地区
     */
    @Schema(description = "所属地区")
    private String region;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;

    /**
     * 关键词搜索（公司名称或代码）
     */
    @Schema(description = "关键词搜索（公司名称或代码）")
    private String keyword;
}
