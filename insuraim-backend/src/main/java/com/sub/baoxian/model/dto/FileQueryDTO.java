package com.sub.baoxian.model.dto;

import lombok.Data;

/**
 * 文件查询参数DTO
 */
@Data
public class FileQueryDTO {
    /**
     * 文件夹ID
     */
    private Long folderId;

    /**
     * 文件类型
     */
    private String fileType;



    /**
     * 关键词（文件名）
     */
    private String keyword;

    /**
     * 页码
     */
    private Integer page = 1;

    /**
     * 每页大小
     */
    private Integer size = 10;
}