package com.sub.baoxian.model.dto;

import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 产品内容DTO
 */
@Data
public class InsProductDetailContentDTO {

    /**
     * 主键ID（更新时需要）
     */
    private Integer id;

    /**
     * 关联产品ID
     */
    @NotNull(message = "产品ID不能为空")
    private Integer productId;

    /**
     * 关联分类标题ID
     */
    @NotNull(message = "标题ID不能为空")
    private Integer titleId;

    /**
     * 内容值
     */
    private String contentValue;
}
