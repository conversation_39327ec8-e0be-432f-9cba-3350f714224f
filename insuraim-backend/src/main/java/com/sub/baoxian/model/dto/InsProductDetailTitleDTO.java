package com.sub.baoxian.model.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 产品分类标题DTO
 */
@Data
public class InsProductDetailTitleDTO {

    /**
     * 主键ID（更新时需要）
     */
    private Integer id;

    /**
     * 关联产品分类ID
     */
    @NotNull(message = "分类ID不能为空")
    private Integer categoryId;

    /**
     * 父标题ID，顶级标题为NULL
     */
    private Integer parentId;

    /**
     * 标题名称
     */
    @NotBlank(message = "标题名称不能为空")
    private String titleName;

    /**
     * 同级排序
     */
    @NotNull(message = "排序不能为空")
    private Integer rankValue;
}
