package com.sub.baoxian.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 产品介绍创建DTO
 */
@Data
@Schema(description = "产品介绍创建DTO")
public class InsProductIntroductionCreateDTO {

    /**
     * 产品ID（关联ins_product表）
     */
    @NotNull(message = "产品ID不能为空")
    @Schema(description = "产品ID", required = true)
    private Long productId;

    /**
     * 产品摘要/简介
     */
    @Schema(description = "产品摘要/简介")
    private String productSummary;

    /**
     * 产品banner图URL
     */
    @Schema(description = "产品banner图URL")
    private String bannerImageUrl;

    /**
     * 产品特点列表
     */
    @Valid
    @Schema(description = "产品特点列表")
    private List<ProductFeatureItemDTO> productFeatures;

    /**
     * 产品概况（HTML格式的富文本）
     */
    @Schema(description = "产品概况（HTML格式的富文本）")
    private String productOverview;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用（1-启用，0-禁用）", defaultValue = "1")
    private Integer status = 1;

    /**
     * 产品特点项DTO
     */
    @Data
    @Schema(description = "产品特点项DTO")
    public static class ProductFeatureItemDTO {
        
        /**
         * 特点名称
         */
        @Schema(description = "特点名称", required = true)
        private String name;
        
        /**
         * 特点值/描述
         */
        @Schema(description = "特点值/描述", required = true)
        private String value;
        
        /**
         * 图标URL（可选）
         */
        @Schema(description = "图标URL")
        private String iconUrl;
        
        /**
         * 排序值
         */
        @Schema(description = "排序值")
        private Integer sortOrder;
    }
}
