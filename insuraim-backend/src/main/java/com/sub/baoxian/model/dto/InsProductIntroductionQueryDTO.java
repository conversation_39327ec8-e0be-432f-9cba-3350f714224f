package com.sub.baoxian.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 产品介绍查询DTO
 */
@Data
@Schema(description = "产品介绍查询DTO")
public class InsProductIntroductionQueryDTO {

    /**
     * 页码，默认1
     */
    @Schema(description = "页码", defaultValue = "1")
    private Long pageNum = 1L;

    /**
     * 每页大小，默认10
     */
    @Schema(description = "每页大小", defaultValue = "10")
    private Long pageSize = 10L;

    /**
     * 产品ID
     */
    @Schema(description = "产品ID")
    private Long productId;

    /**
     * 产品名称（模糊查询）
     */
    @Schema(description = "产品名称（模糊查询）")
    private String productName;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用（1-启用，0-禁用）")
    private Integer status;

    /**
     * 关键词搜索（产品摘要）
     */
    @Schema(description = "关键词搜索（产品摘要）")
    private String keyword;
}
