package com.sub.baoxian.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * ins_product查询条件DTO
 */
@Data
@Schema(description = "产品查询条件DTO")
public class InsProductQueryDTO {

    /**
     * 页码，默认1
     */
    @Schema(description = "页码", defaultValue = "1")
    private Long pageNum = 1L;

    /**
     * 每页大小，默认10
     */
    @Schema(description = "每页大小", defaultValue = "10")
    private Long pageSize = 10L;

    /**
     * 产品名称（模糊查询）
     */
    @Schema(description = "产品名称（模糊查询）")
    private String productName;

    /**
     * 保险公司代码
     */
    @Schema(description = "保险公司代码")
    private String companyCode;

    /**
     * 保险公司名称（模糊查询）
     */
    @Schema(description = "保险公司名称（模糊查询）")
    private String companyName;

    /**
     * 产品类别代码
     */
    @Schema(description = "产品类别代码")
    private String categoryCode;

    /**
     * 产品类别名称
     */
    @Schema(description = "产品类别名称")
    private String categoryName;

    /**
     * 销售地区
     */
    @Schema(description = "销售地区")
    private String region;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型")
    private String productType;

    /**
     * 保障年限
     */
    @Schema(description = "保障年限")
    private String guaranteePeriod;

    /**
     * 启用状态
     */
    @Schema(description = "启用状态")
    private Integer status;

    /**
     * 是否允许预缴
     */
    @Schema(description = "是否允许预缴")
    private Integer isPrepayment;

    @Schema(description = "是否有现金价值")
    private Integer isCashValue;

    /**
     * 子产品是否有现金价值
     */
    @Schema(description = "子产品是否有现金价值")
    private Integer subProductHasCashValue;

    /**
     * 关键词搜索（产品名称或公司名称）
     */
    @Schema(description = "关键词搜索（产品名称或公司名称）")
    private String keyword;
}
