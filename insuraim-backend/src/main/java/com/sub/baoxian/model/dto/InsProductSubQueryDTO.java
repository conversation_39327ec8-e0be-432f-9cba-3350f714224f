package com.sub.baoxian.model.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 子产品查询DTO
 */
@Data
@Schema(description = "子产品查询条件DTO")
public class InsProductSubQueryDTO {

    /**
     * 页码，默认1
     */
    @Schema(description = "页码", defaultValue = "1")
    private Long pageNum = 1L;

    /**
     * 每页大小，默认10
     */
    @Schema(description = "每页大小", defaultValue = "10")
    private Long pageSize = 10L;

    /**
     * 父产品ID
     */
    @Schema(description = "父产品ID")
    private Integer parentProductId;

    /**
     * 产品名称（模糊查询）
     */
    @Schema(description = "产品名称（模糊查询）")
    private String productName;

    /**
     * 分类ID
     */
    @Schema(description = "分类ID")
    private Long categoryId;

    /**
     * 缴费期限
     */
    @Schema(description = "缴费期限")
    private String paymentTerm;

    /**
     * 币种
     */
    @Schema(description = "币种")
    private String currency;

    /**
     * 是否有现金价值
     */
    @Schema(description = "是否有现金价值")
    private Integer hasCashValue;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;

    /**
     * 关键词搜索（产品名称）
     */
    @Schema(description = "关键词搜索（产品名称）")
    private String keyword;
}
