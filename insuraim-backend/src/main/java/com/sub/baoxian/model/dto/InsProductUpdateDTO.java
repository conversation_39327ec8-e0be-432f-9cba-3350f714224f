package com.sub.baoxian.model.dto;

import java.util.List;
import java.util.Map;

import lombok.Data;

/**
 * 产品更新DTO
 */
@Data
public class InsProductUpdateDTO {

    /**
     * 产品ID
     */
    private Long id;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 保险公司代码
     */
    private String companyCode;

    /**
     * 保险公司名称
     */
    private String companyName;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 产品类别代码
     */
    private String categoryCode;

    /**
     * 产品类别名称
     */
    private String categoryName;

    /**
     * 产品描述
     */
    private String description;

    /**
     * 产品类型
     */
    private String productType;

    /**
     * 产品logo URL
     */
    private String logoUrl;

    /**
     * 销售地区
     */
    private String region;

    /**
     * 保障年限
     */
    private String guaranteePeriod;

    /**
     * 产品tags
     */
    private List<String> tags;

    /**
     * 缴费期限
     */
    private List<String> paymentTerm;

    /**
     * 年龄范围
     */
    private Map<String, Object> ageRange;

    /**
     * 支持币种
     */
    private List<String> currencies;

    /**
     * 缴费方式
     */
    private List<String> paymentMethods;

    /**
     * 是否支持生成计划书
     */
    private Integer isProposal;

    /**
     * 是否允许预缴
     */
    private Integer isPrepayment;

    /**
     * 产品小册子
     */
    private String productBrochure;

    /**
     * 启用状态
     */
    private Integer status;
}
