package com.sub.baoxian.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 菜单创建DTO
 */
@Data
@Schema(description = "菜单创建DTO")
public class MenuCreateDTO {

    /**
     * 菜单名称
     */
    @NotBlank(message = "菜单名称不能为空")
    @Schema(description = "菜单名称", required = true)
    private String name;

    /**
     * 父菜单ID
     */
    @Schema(description = "父菜单ID，顶级菜单为0")
    private Long parentId = 0L;

    /**
     * 显示顺序
     */
    @Schema(description = "显示顺序")
    private Integer orderRank = 0;

    /**
     * 路由地址
     */
    @Schema(description = "路由地址")
    private String path;

    /**
     * 组件路径
     */
    @Schema(description = "组件路径")
    private String component;

    /**
     * 路由名称
     */
    @Schema(description = "路由名称")
    private String routeName;

    /**
     * 菜单状态（0停用 1启用）
     */
    @NotNull(message = "菜单状态不能为空")
    @Schema(description = "菜单状态（0停用 1启用）", required = true)
    private Integer status = 1;

    /**
     * 菜单图标
     */
    @Schema(description = "菜单图标")
    private String icon;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 菜单类型（0:目录,1:菜单,2:按钮）
     */
    @NotNull(message = "菜单类型不能为空")
    @Schema(description = "菜单类型（0:目录,1:菜单,2:按钮）", required = true)
    private Integer menuType;
}
