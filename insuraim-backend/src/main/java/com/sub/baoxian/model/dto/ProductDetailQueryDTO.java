package com.sub.baoxian.model.dto;

import lombok.Data;

/**
 * 产品详情查询DTO
 */
@Data
public class ProductDetailQueryDTO {

    /**
     * 分类ID
     */
    private Integer categoryId;

    /**
     * 产品ID
     */
    private Integer productId;

    /**
     * 标题ID
     */
    private Integer titleId;

    /**
     * 父标题ID
     */
    private Integer parentId;

    /**
     * 标题名称（模糊查询）
     */
    private String titleName;

    /**
     * 内容值（模糊查询）
     */
    private String contentValue;

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    private Integer pageSize = 10;
}
