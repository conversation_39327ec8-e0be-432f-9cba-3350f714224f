package com.sub.baoxian.model.dto;

import lombok.Data;

/**
 * 题目答案DTO，用于接收JOIN查询的结果
 */
@Data
public class QuestionAnswerDTO {
    // 题目字段
    private Long id;
    private Long examId;
    private String difficulty;
    private String majorChapter;
    private String chapter;
    private String question;
    private String feedbackCorrect;
    private String feedbackIncorrect;
    private Integer status;
    private Long createdAt;
    private Long updatedAt;
    
    // 答案字段
    private Long answerId;
    private Long questionId;
    private String optionText;
    private Integer isCorrect;
    private Long answerCreatedAt;
    private Long answerUpdatedAt;
} 