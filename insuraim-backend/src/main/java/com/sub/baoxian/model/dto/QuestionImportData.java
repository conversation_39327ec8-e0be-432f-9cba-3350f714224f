package com.sub.baoxian.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 题目导入数据模型类，用于解析JSON
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QuestionImportData {
    /**
     * 难度等级
     */
    private String difficulty;

    /**
     * 章节，如1.1.3
     */
    private String chapter;

    /**
     * 题目内容
     */
    private String question;

    /**
     * 答案选项列表
     */
    private List<AnswerOption> answer;

    /**
     * 答对反馈
     */
    private String feedbackCorrect;

    /**
     * 答错反馈
     */
    private String feedbackIncorrect;

    /**
     * 大章节，如1
     */
    private String majorChapter;

    /**
     * 答案选项
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AnswerOption {
        /**
         * 是否为正确答案
         */
        private boolean correct;

        /**
         * 选项文本
         */
        private String text;
    }
} 