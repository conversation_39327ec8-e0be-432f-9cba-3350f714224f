package com.sub.baoxian.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("calendar")
public class Calendar {

    /**
     * 日程ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 日程标题
     */
    private String title;

    /**
     * 日程内容
     */
    private String content;

    /**
     * 日程时间
     */
    private Long calendarTime;

    /**
     * 提醒时间(时间戳)
     */
    private Long remindTime;

    /**
     * 重复规则：daily-每天，weekly-每周，monthly-每月，yearly-每年，none-不重复
     */
    private String repeatRule;

    /**
     * 完成状态：0-进行中, 1-已完成, 2-已取消
     */
    private Integer status;

    /**
     * 是否已发送提醒
     */
    private Integer remindSent;

    /**
     * 创建时间(时间戳)
     */
    private Long createTime;

    /**
     * 更新时间(时间戳)
     */
    private Long updateTime;

    /**
     * 备注
     */
    private String remark;
}