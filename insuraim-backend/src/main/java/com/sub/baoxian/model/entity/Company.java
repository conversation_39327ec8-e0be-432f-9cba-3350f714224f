package com.sub.baoxian.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 保险公司实体类
 */
@Data
@TableName("ins_company")
public class Company {

    /**
     * 自增主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 保险公司代码
     */
    private String code;

    /**
     * 保险公司名称
     */
    private String name;

    /**
     * 公司LOGO URL
     */
    private String logoUrl;

    /**
     * 公司Banner图片URL
     */
    private String bannerUrl;

    /**
     * 公司详细描述
     */
    private String description;

    /**
     * 公司官网URL
     */
    private String websiteUrl;

    /**
     * 行业排名
     */
    private Integer companyRank;

    /**
     * 所属地区
     */
    private String region;

    /**
     * 当前征费率
     */
    private BigDecimal levyRate;

    /**
     * 汇率参考货币
     */
    private String exchangeCurrency;

    /**
     * 参考汇率值
     */
    private BigDecimal exchangeRate;

    /**
     * 汇率更新时间戳
     */
    private LocalDateTime exchangeRateTime;

    /**
     * 汇率来源URL
     */
    private String exchangeRateUrl;

    /**
     * 文件检查时间
     */
    private LocalDateTime documentInspectionTime;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 记录创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 记录最后更新时间
     */
    private LocalDateTime updatedAt;
}