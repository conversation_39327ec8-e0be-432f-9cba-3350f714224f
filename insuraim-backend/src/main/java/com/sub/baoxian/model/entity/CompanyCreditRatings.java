package com.sub.baoxian.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 信用评级记录实体类
 */
@Data
@TableName("ins_company_credit_ratings")
public class CompanyCreditRatings {

    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 保险公司唯一标识码
     */
    private String code;

    /**
     * 评级项目
     */
    private String project;

    /**
     * 评级结果
     */
    private String rating;

    /**
     * 评级机构
     */
    private String ratingAgency;

    /**
     * 评级时间戳(毫秒)
     */
    private Long time;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}