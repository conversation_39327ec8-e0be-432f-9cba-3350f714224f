package com.sub.baoxian.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 保险公司文件实体类
 */
@Data
@TableName("ins_company_file")
public class CompanyFile {

    /**
     * 文件唯一ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 保险公司唯一标识码
     */
    private String code;

    /**
     * 上传者/作者
     */
    private String author;

    /**
     * 原始文件名
     */
    private String fileName;

    /**
     * 文件存储路径(URL)
     */
    private String filePath;

    /**
     * 上传时间戳(毫秒)
     */
    private Long uploadTime;

    /**
     * 记录创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 记录更新时间
     */
    private LocalDateTime updatedAt;

}