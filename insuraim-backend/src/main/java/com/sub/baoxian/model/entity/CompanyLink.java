package com.sub.baoxian.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 保险公司相关链接实体类
 */
@Data
@TableName("ins_company_link")
public class CompanyLink {

    /**
     * 链接唯一ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 关联保险公司编码
     */
    private String code;

    /**
     * 链接描述
     */
    private String description;

    /**
     * 链接地址
     */
    private String linkUrl;

    /**
     * 记录创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 记录更新时间
     */
    private LocalDateTime updatedAt;

}