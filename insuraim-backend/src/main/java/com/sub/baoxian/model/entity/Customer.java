package com.sub.baoxian.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;

import java.sql.Date;

import com.baomidou.mybatisplus.annotation.IdType;

import lombok.Data;

@Data
@TableName("customer")
public class Customer {

    /**
     * 客户id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 客户姓名
     */
    private String name;

    /**
     * 客户手机号
     */
    private String phone;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 生日
     */
    private Date birthday;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 客户地址
     */
    private String address;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

}
