package com.sub.baoxian.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 保险行业模拟考试分类实体类
 */
@Data
@TableName("exam_category")
public class ExamCategory {

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    
    /**
     * 考试名称（如：保险代理人资格模拟考试）
     */
    private String examName;
    
    /**
     * 考试类型（IIQE等）
     */
    private String examType;
    
    /**
     * 考试时长（分钟）
     */
    private Integer duration;
    
    /**
     * 总题量
     */
    private Integer questionCount;
    
    /**
     * 是否启用,0-未启用，1-启用
     */
    private Integer status;
    
    /**
     * 创建时间戳(毫秒)
     */
    private Long createdAt;
    
    /**
     * 更新时间戳(毫秒)
     */
    private Long updatedAt;
} 