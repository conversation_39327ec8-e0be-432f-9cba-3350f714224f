package com.sub.baoxian.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 模拟考试题库实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("exam_questions")
public class ExamQuestion implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 题目ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 所属类目ID
     */
    private Long examId;

    /**
     * 难度等级
     */
    private String difficulty;

    /**
     * 大章节(如1)
     */
    private String majorChapter;

    /**
     * 详细章节(如1.1.3)
     */
    private String chapter;

    /**
     * 题干内容
     */
    private String question;


    /**
     * 答对反馈
     */
    private String feedbackCorrect;

    /**
     * 答错反馈
     */
    private String feedbackIncorrect;

    /**
     * 是否启用
     */
    private Integer status;

    /**
     * 创建时间戳(毫秒)
     */
    private Long createdAt;

    /**
     * 更新时间戳(毫秒)
     */
    private Long updatedAt;

    /**
     * 难度等级枚举
     */
    public enum Difficulty {
        Easy, Medium, Hard, Expert
    }
} 