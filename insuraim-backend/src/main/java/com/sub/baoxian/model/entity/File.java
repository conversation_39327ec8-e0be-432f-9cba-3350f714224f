package com.sub.baoxian.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 文件实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("ins_file")
public class File implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 文件名
     */
    private String name;
    
    /**
     * 所属文件夹ID
     */
    private Long folderId;
    
    /**
     * 文件类型(pdf,doc,xls,ppt等)
     */
    private String fileType;
    
    /**
     * 文件大小(字节)
     */
    private Long size;
    
    
    /**
     * 访问地址
     */
    private String ossUrl;
    
    /**
     * 创建时间
     */
    private LocalDateTime createAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateAt;
    
    /**
     * 上传者ID
     */
    private Long uploaderId;
    
    /**
     * 是否删除
     */
    @TableLogic
    private Boolean isDeleted;
} 