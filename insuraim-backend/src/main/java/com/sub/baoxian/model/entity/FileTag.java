package com.sub.baoxian.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 文件标签实体类
 */
@Data
@TableName("file_tag")
public class FileTag {
    
    /**
     * 标签ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 文件唯一ID
     */
    private Integer fileId;
    
    /**
     * 标签名称
     */
    private String name;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 删除标记(0-未删除,1-已删除)
     */
    @TableLogic
    private Integer isDeleted;
    
    /**
     * 删除时间
     */
    private LocalDateTime deletedAt;
} 