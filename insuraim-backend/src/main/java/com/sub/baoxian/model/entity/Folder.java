package com.sub.baoxian.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 文件夹实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("ins_folder")
public class Folder implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 文件夹名称
     */
    private String name;
    
    /**
     * 父文件夹ID
     */
    private Long parentId;
    
    
    /**
     * 创建时间
     */
    private LocalDateTime createAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateAt;
    
    /**
     * 创建者ID
     */
    private Long creatorId;
    
    /**
     * 是否删除
     */
    @TableLogic
    private Boolean isDeleted;
} 