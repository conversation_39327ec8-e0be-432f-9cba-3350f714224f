package com.sub.baoxian.model.entity;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;

import lombok.Data;

/**
 * ins_product表对应的实体类
 */
@Data
@TableName(value = "ins_product", autoResultMap = true)
public class InsProduct {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 保险公司代码
     */
    private String companyCode;

    /**
     * 保险公司名称
     */
    private String companyName;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 产品类别代码
     */
    private String categoryCode;

    /**
     * 产品类别名称
     */
    private String categoryName;


    /**
     * 产品描述
     */
    private String description;

    /**
     * 产品logo URL
     */
    private String logoUrl;

    /**
     * 销售地区
     */
    private String region;

    /**
     * 保障年限
     */
    private String guaranteePeriod;

    /**
     * 是否有现金价值
     */
    private Integer hasCashValue;

    /**
     * 产品类型
     */
    private String productType;

    /**
     * 产品tags
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> tags;

    /**
     * 缴费期限
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> paymentTerm;

    /**
     * 年龄范围
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> ageRange;

    /**
     * 支持币种
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> currencies;

    /**
     * 缴费方式
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> paymentMethods;

    /**
     * 1是允许预缴，0是不适用
     */
    private Integer isPrepayment;

    /**
     * 是否支持生成计划书
     */
    private Integer isProposal;
    
    /**
     * 产品小册子
     */
    private String productBrochure;

    /**
     * 启用状态
     */
    private Integer status;

    /**
     * 创建时间(Unix时间戳)
     */
    private Long createdAt;

    /**
     * 更新时间(Unix时间戳)
     */
    private Long updatedAt;
}
