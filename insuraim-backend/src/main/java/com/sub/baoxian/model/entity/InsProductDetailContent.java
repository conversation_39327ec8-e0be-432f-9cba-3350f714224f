package com.sub.baoxian.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 产品内容表实体类
 */
@Data
@TableName("ins_product_detail_contents")
public class InsProductDetailContent {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 关联产品ID
     */
    private Integer productId;

    /**
     * 关联分类标题ID
     */
    private Integer titleId;

    /**
     * 内容值
     */
    private String contentValue;

    /**
     * 创建时间戳(毫秒)
     */
    private Long createdAt;

    /**
     * 更新时间戳(毫秒)
     */
    private Long updatedAt;
}
