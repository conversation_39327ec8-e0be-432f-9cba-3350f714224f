package com.sub.baoxian.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 产品分类标题表实体类
 */
@Data
@TableName("ins_product_detail_titles")
public class InsProductDetailTitle {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 关联产品分类ID
     */
    private Integer categoryId;

    /**
     * 父标题ID，顶级标题为NULL
     */
    private Integer parentId;

    /**
     * 标题名称
     */
    private String titleName;

    /**
     * 同级排序
     */
    private Integer rankValue;

    /**
     * 创建时间戳(毫秒)
     */
    private Long createdAt;

    /**
     * 更新时间戳(毫秒)
     */
    private Long updatedAt;
}
