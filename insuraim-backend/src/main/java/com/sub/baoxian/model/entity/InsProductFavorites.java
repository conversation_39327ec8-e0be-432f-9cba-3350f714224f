package com.sub.baoxian.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 产品收藏表实体类
 */
@Data
@TableName("ins_product_favorites")
public class InsProductFavorites {

    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 收藏时间
     */
    private Long createdAt;

    /**
     * 更新时间
     */
    private Long updatedAt;
}
