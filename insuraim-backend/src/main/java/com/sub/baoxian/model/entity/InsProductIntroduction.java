package com.sub.baoxian.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;

import lombok.Data;

import java.util.List;

/**
 * 产品介绍表实体类
 * 专门用于存储产品介绍页面的展示信息
 */
@Data
@TableName(value = "ins_product_introduction", autoResultMap = true)
public class InsProductIntroduction {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 产品ID（关联ins_product表）
     */
    private Long productId;

    /**
     * 产品摘要/简介
     */
    private String productSummary;

    /**
     * 产品banner图URL
     */
    private String bannerImageUrl;

    /**
     * 产品特点列表（JSON格式存储）
     * 格式：[{"name":"保障期限","value":"终身","iconUrl":"xxx","sortOrder":1}]
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<ProductFeatureItem> productFeatures;

    /**
     * 产品概况（HTML格式的富文本）
     */
    private String productOverview;

    /**
     * 是否启用
     */
    private Integer status;

    /**
     * 创建时间(Unix时间戳)
     */
    private Long createdAt;

    /**
     * 更新时间(Unix时间戳)
     */
    private Long updatedAt;

    /**
     * 产品特点项内部类
     */
    @Data
    public static class ProductFeatureItem {
        
        /**
         * 特点名称
         */
        private String name;
        
        /**
         * 特点值/描述
         */
        private String value;
        
        /**
         * 图标URL（可选）
         */
        private String iconUrl;
        
        /**
         * 排序值
         */
        private Integer sortOrder;
    }
}
