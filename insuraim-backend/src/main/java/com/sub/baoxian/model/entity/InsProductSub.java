package com.sub.baoxian.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * ins_product_sub表对应的实体类
 */
@Data
@TableName(value = "ins_product_sub", autoResultMap = true)
public class InsProductSub {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 父产品ID
     */
    private Integer parentProductId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 缴费期限
     */
    private String paymentTerm;

    /**
     * 币种
     */
    private String currency;

    /**
     * 年龄范围
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> ageRange;

    /**
     * 保额范围
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> sumInsuredRange;

    /**
     * 保费范围
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> premiumRange;

    /**
     * 预缴年数
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Integer> prepaymentYears;

    /**
     * 缴费方式
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> paymentMethods;

    /**
     * 是否有现金价值
     */
    private Integer hasCashValue;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建时间(Unix时间戳)
     */
    private Long createdAt;

    /**
     * 更新时间(Unix时间戳)
     */
    private Long updatedAt;
}
