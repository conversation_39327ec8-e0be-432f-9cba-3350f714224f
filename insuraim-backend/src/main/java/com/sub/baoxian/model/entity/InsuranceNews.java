package com.sub.baoxian.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("insurance_news")
public class InsuranceNews implements Serializable {
    /**
     * id
     */
    private Long id;
    /**
     * 标题
     */
    private String title;
    /**
     * 内容
     */
    private String content;
    /**
     * 图片
     */
    private String img;
    /**
     * 链接
     */
    private String link;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 发布时间
     */
    private Long pubDate;
    /**
     * 标签
     */
    private String tags;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 热度
     */
    private Integer popular;
    /**
     * 去重hash
     */
    private String groupHash;
    /**
     * 数据来源
     */
    private String source;

    /**
     * 新闻
     */
    private Integer type;
}
