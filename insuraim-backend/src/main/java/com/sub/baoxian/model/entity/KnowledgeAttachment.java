package com.sub.baoxian.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 知识库附件实体类
 */
@Data
@TableName("knowledge_attachments")
public class KnowledgeAttachment {
    
    /**
     * 附件ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 关联知识库ID
     */
    private Integer knowledgeId;
    
    /**
     * 文件名称
     */
    private String fileName;
    
    /**
     * 文件路径
     */
    private String filePath;
    
    /**
     * 文件类型
     */
    private String fileType;
    
    /**
     * 文件大小(字节)
     */
    private Long fileSize;
    
    /**
     * 上传时间戳(毫秒)
     */
    private Long uploadTime;
    
    /**
     * 文件描述
     */
    private String description;
} 