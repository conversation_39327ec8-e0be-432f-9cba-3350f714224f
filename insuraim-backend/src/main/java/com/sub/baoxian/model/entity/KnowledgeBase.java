package com.sub.baoxian.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 知识库信息实体类
 */
@Data
@TableName("knowledge_base")
public class KnowledgeBase {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 知识标题
     */
    private String title;
    
    /**
     * 知识内容
     */
    private String content;
    
    /**
     * 分类名称
     */
    private String category;
    
    /**
     * 状态：1-正常，0-禁用
     */
    private Integer status;
    
    /**
     * 创建时间戳（Unix timestamp）
     */
    private Long createdAt;
    
    /**
     * 最后更新时间戳
     */
    private Long updatedAt;
    
    /**
     * 作者ID
     */
    private Long authorId;
    
    /**
     * 摘要
     */
    private String summary;
    
    /**
     * 浏览量
     */
    private Integer views;
    
    /**
     * 标签，多个标签用逗号分隔
     */
    private String tags;
    
    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 类型："article"-文章，"file"-文件
     */
    private String type;
} 