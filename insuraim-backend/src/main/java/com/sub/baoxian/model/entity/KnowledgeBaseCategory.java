package com.sub.baoxian.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 知识库分类信息实体类
 */
@Data
@TableName("knowledge_category")
public class KnowledgeBaseCategory {
    
    /**
     * 分类ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 分类中文名称
     */
    private String categoryName;

    /**
     * 保险公司ID
     */
    private Long companyId;
    
    /**
     * 父级ID
     */
    private Long parentId;
        
    /**
     * 分类描述
     */
    private String description;
    
    /**
     * 创建时间戳（Unix timestamp）
     */
    private Long createdAt;
    
    /**
     * 最后更新时间戳
     */
    private Long updatedAt;
}
