package com.sub.baoxian.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

@Data
@TableName("knowledge_company")
public class KnowledgeBaseCompany {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String companyName;

    private String companyCode;

    private String logoUrl;

}
