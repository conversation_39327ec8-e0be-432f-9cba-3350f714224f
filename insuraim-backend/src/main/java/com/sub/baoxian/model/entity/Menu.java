package com.sub.baoxian.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 菜单实体类
 */
@Data
@TableName("sys_menu")
public class Menu {
    /**
     * 菜单ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 菜单名称
     */
    private String name;
    
    /**
     * 父菜单ID
     */
    private Long parentId;
    
    /**
     * 显示顺序
     */
    private Integer orderRank;
    
    /**
     * 路由地址
     */
    private String path;

    /**
     * 菜单类型（0:目录,1:菜单,2:按钮,3:页面）
     */
    private Integer menuType;
    
    /**
     * 组件路径
     */
    private String component;
    
    /**
     * 路由名称
     */
    private String routeName;
    
    /**
     * 菜单状态（0停用 1启用）
     */
    private Integer status;
    
    /**
     * 菜单图标
     */
    private String icon;
    
    /**
     * 创建时间
     */
    private Long createTime;
    
    /**
     * 更新时间
     */
    private Long updateTime;
    
    /**
     * 备注
     */
    private String remark;
}
