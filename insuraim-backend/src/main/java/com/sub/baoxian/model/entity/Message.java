package com.sub.baoxian.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sub.baoxian.common.constants.MessageType;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
@TableName("messages")
public class Message {

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 接收消息的用户ID
     */
    private Long userId;

    /**
     * 消息标题
     */
    private String title;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 消息类型(SYSTEM_MESSAGE=系统消息,BUSINESS_MESSAGE=业务信息,PROPOSAL_MESSAGE=计划书信息,NEWS_MESSAGE=资讯信息)
     */
    private MessageType messageType;

    /**
     * 阅读状态(0=未读,1=已读)
     */
    private Integer status;

    /**
     * 是否推送(0=未推送,1=已推送)
     */
    private Integer isPush;

    /**
     * 是否重要消息
     */
    private Integer isImportant;

    /**
     * 创建时间
     */
    private Long createdAt;

    /**
     * 阅读时间
     */
    private Long readAt;

    /**
     * 额外数据(JSON格式)
     */
    private String extraData;

}
