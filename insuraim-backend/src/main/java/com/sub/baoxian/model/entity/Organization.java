package com.sub.baoxian.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 组织实体类
 */
@Data
@TableName("organization")
public class Organization {
    /**
     * 组织ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 组织编码
     */
    private String code;
    
    /**
     * 组织名称
     */
    private String name;
    
    /**
     * 组织描述
     */
    private String description;
    
    
    
    /**
     * 同级排序号
     */
    private Integer sortOrder;
    
    /**
     * 组织类型：INSURANCE_COMPANY-保险公司，INSURANCE_AGENT-保险代理人，
     * INSURANCE_BROKER-保险经纪人，INSURANCE_AGENCY-保险中介机构，
     * SALES_TEAM-保险销售团队，OTHER-其他
     */
    private String type;
    
    /**
     * 状态：1-启用，0-禁用
     */
    private Integer status;
    
    /**
     * 地址
     */
    private String address;
    
    /**
     * 联系电话
     */
    private String phone;
    
    /**
     * 联系邮箱
     */
    private String email;
    
    /**
     * 负责人
     */
    private String leader;



    /**
     *  最大用户数
     */
    private Integer maxUser;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 是否删除，0-未删除，1-已删除
     */
    @TableLogic
    private Integer isDeleted;
    
    /**
     * 删除时间
     */
    private LocalDateTime deletedAt;
} 