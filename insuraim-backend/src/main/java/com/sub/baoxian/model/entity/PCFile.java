package com.sub.baoxian.model.entity;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Builder;

/**
 * 个人云盘文件表实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PCFile {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 文件名
     */
    private String name;
    
    /**
     * 所属文件夹ID
     */
    private Long folderId;
    
    /**
     * 所属用户ID
     */
    private Long userId;
    
    /**
     * 文件类型，如pdf,doc,xls等
     */
    private String fileType;
    
    /**
     * 文件大小(字节)
     */
    private Long size;
    
    
    /**
     * Oss URL
     */
    private String ossUrl;

    /**
     * Oss bucketName
     */
    private String ossBucketName;
    
    /**
     * 文件MD5值，用于去重
     */
    private String md5;
    
    /**
     * 创建时间戳
     */
    private Long createdAt;
    
    /**
     * 更新时间戳
     */
    private Long updatedAt;
    
    /**
     * 上传者ID
     */
    private Long creatorId;
    
    /**
     * 下载次数
     */
    private Integer downloadCount;
    
    /**
     * 是否收藏，1-已收藏，0-未收藏
     */
    private Boolean isFavorite;
    
    /**
     * 文件标签，多个标签用逗号分隔
     */
    private String tag;
} 