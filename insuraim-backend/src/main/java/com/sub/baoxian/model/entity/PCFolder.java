package com.sub.baoxian.model.entity;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Builder;

/**
 * 个人云盘文件夹表实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PCFolder {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 文件夹名称
     */
    private String name;
    
    /**
     * 父文件夹ID，0表示根目录
     */
    private Long parentId;
    
    /**
     * 所属用户ID
     */
    private Long userId;
    
    /**
     * 完整路径，格式如：/根目录/子目录
     */
    private String path;
    
    /**
     * 文件夹描述
     */
    private String description;
    
    /**
     * 创建时间戳
     */
    private Long createdAt;
    
    /**
     * 更新时间戳
     */
    private Long updatedAt;
    
    /**
     * 创建者ID
     */
    private Long creatorId;
} 