package com.sub.baoxian.model.entity;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Builder;

/**
 * 个人云盘主表实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PersonalCloud {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 总存储空间(字节)，默认5GB
     */
    private Long totalStorage;
    
    /**
     * 已使用存储空间(字节)
     */
    private Long usedStorage;
    
    /**
     * 是否启用云盘，1-启用，0-禁用
     */
    private Boolean isActive;
    
    /**
     * 创建时间戳
     */
    private Long createdAt;
    
    /**
     * 更新时间戳
     */
    private Long updatedAt;
    
    /**
     * 最后访问时间戳
     */
    private Long lastAccessedAt;
    
    /**
     * 用户偏好设置，JSON格式
     */
    private String settings;
} 