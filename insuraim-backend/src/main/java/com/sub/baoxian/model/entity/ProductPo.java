package com.sub.baoxian.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("product_details")
public class ProductPo {

    private Long id;
    private String name;
    private String mainType;
    private String subType;
    private Long commonId;
    private String policyCurrency;
    private String policyCurrencyDetails;
    private String premiumPaymentPeriod;
    private String policyTerm;
    private String minimumAge;
    private String maximumAge;
    private String minimumSumInsured;
    private String maximumSumInsured;
    private String sellDate;
    private String policyDate;
    private String annualPrepaymentInterestRate;
    private String underwritingMethodOrUnderwritingProcess;
    private Integer medicalUnderwriting;
    private Integer financialUnderwriting;
    private Integer occupationalUnderwriting;
    private Integer geographicalUnderwriting;
    private Integer noMedicalExamLimit;
    private Integer additionalLoadingApplicable;
    private Integer specialDocumentationRequired;
    private Integer criticalIllnessCoverage1;
    private Integer hospitalizationBenefitNonPremiumPlan;
    private Integer hospitalizationBenefitPremiumPlan;
    private Integer accidentalBenefit;
    private Integer premiumWaiverBenefit;
    private Integer longTermSickLeaveBenefit;
    private Integer outpatientBenefit;
    private Integer femaleSpecificBenefit;
    private Integer babyRewardBenefit;
    private Integer disabilityBenefit;
    private Integer increaseSumInsured;
    private Integer decreaseSumInsured;
    private Integer addBaseCoverage;
    private Integer reduceBaseCoverage;
    private Integer addRider;
    private Integer removeRider;
    private Integer policyLoan;
    private Integer renewalPaymentMethod;
    private Integer prepaidPremium;
    private Integer policyReinstatement;
    private Integer automaticPremiumLoan;
    private Integer changePlan;
    private Integer reducedPaidUpOption;
    private Integer changeInSmokingStatus;
    private String others;

    private String productFeatures; // 產品特點
    private String targetAudience; // 銷售對象
    private String premiumRate; // 保費費率
    private String nonGuaranteedDividend; // 紅利(非保證)
    private String terminalDividend; // 終期紅利
    private String terminalDividendNonGuarantee; // 終期紅利（非保證）
    private String terminalDividendManagementBenefit; // 終期紅利管理權益
    private String guaranteedMonthlyAnnuityIncome; // 保證每月年金入息
    private String nonGuaranteedMonthlyAnnuityIncome; // 非保證每月年金入息
    private String highCoverageDiscount; // 大額折扣
    private String specialDiseaseCoverage; // 特別疾病保障
    private String premiumHoliday; // 保費假期
    private String policySplitRights; // 保單分拆權益
    private String currencyConversionRights; // 貨幣轉換權益
    private String additionalCriticalIllnessCoverageForChildren; // 兒童額外特別疾病保障
    private String earlyStageIllnessCoverage; // 初期階段疾病保障
    private String criticalIllnessCoverage; // 嚴重病症保障
    private String waiverOfPremiumOnCriticalIllnessBenefit; // 特別疾病豁免繳付保費保障
    private String additionalCriticalIllnessCoverage; // 額外嚴重病症保障
    private String multipleCriticalIllnessCoverage; // 多重嚴重病症保障
    private String familySharedCoverage; // 家庭共享保障
    private String allRoundProtectionPlan; // 全安心保障
    private String level1; // 保障級別一
    private String level2; // 保障級別二
    private String level3; // 保障級別三
    private String allLevels; // 全级别保障
    private String doubleIndemnityBenefit; // 雙倍賠償保障
    private String compassionateDeathBenefit; // 恩恤身故賠償
    private String additionalAllRoundProtectionPlan; // 額外全安心保障
    private String medicalExpensesCoverage; // 治療費用保障
    private String rehabilitationSupportServices; // 康復輔助服務
    private String additionalHospitalCashBenefit; // 額外住院現金保障
    private String hospitalSurgeryReimbursement; // 賠償住院及手術開支
    private String noLifetimeLimitAndUnknownPreexistingCovered; // 不設終身保障限額並承保投保前未知的已有病症
    private String additionalDeathBenefit; // 額外身故賠償
    private String accidentalBurnBenefit; // 意外燒傷保障
    private String flexibleDeathAndAccidentalPayoutMethod; // 靈活身故賠償及意外身故保障賠付方式
    private String accidentalDeathBenefit; // 意外身故保障
    private String cancerCoverage; // 癌症保障
    private String accidentalTotalAndPermanentDisability; // 意外完全及永久傷殘保障
    private String accidentalDeathAndDismembermentBenefit; // 意外死亡及斷肢保障
    private String accidentalTpdWaiverOfPremium; // 完全及永久意外傷殘豁免保費保障
    private String suddenDeathCoverage; // 猝死身故保障
    private String changeOfInsuredPerson; // 轉換受保人
    private String substituteInsuredPerson; // 後補受保人
    private String parentalWaiverBenefit; // 父母身故豁免繳付保費保障
    private String spousalWaiverBenefit; // 配偶身故豁免繳付保費保障
    private String extendedCoverageForChildren; // 關懷子女延伸保障
    private String policyTermination; // 保單失效、終止
    private String otherBenefits; // 其他利益
    private String specialRemarks; // 特別備註
    private String policyReverseMortgageLoan; // 保單逆按貸款
    private String rateTable; // 費率表

}
