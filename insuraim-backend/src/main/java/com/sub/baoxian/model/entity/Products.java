package com.sub.baoxian.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 保险产品实体类
 */
@Data
@TableName(value = "products", autoResultMap = true)
public class Products {
    
    /**
     * 自增主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 产品代码（如"AIA:OYS"）
     */
    private String productCode;
    
    /**
     * 产品名称（如"爱伴航"）
     */
    private String productName;
    
    /**
     * 保险公司代码（如"AIA"）
     */
    private String companyCode;
    
    /**
     * 保险公司名称（如"友邦保險"）
     */
    private String companyName;
    
    /**
     * 产品类别代码（如"CRITICAL_ILLNESS"）
     */
    private String categoryCode;
    
    /**
     * 产品类别名称（如"危疾"）
     */
    private String categoryName;
    
    /**
     * 产品描述
     */
    private String description;
    
    /**
     * 产品logo URL
     */
    private String logoUrl;
    
    /**
     * 销售地区（如"香港"）
     */
    private String region;
    
    /**
     * 销售状态（如"SELLING"）
     */
    private String sellStatus;
    
    /**
     * 产品类型（如"BASIC"）
     */
    private String productType;
    
    /**
     * 是否热门产品
     */
    private Boolean isHot;
    
    /**
     * 是否可比较
     */
    private Boolean isComparable;
    
    /**
     * 是否定制产品
     */
    private Boolean isCustom;
    
    /**
     * 是否为更新版本
     */
    private Boolean isUpdated;
    
    /**
     * 是否可报价
     */
    private Boolean isQuotable;
    
    /**
     * 是否可退保
     */
    private Boolean isWithdrawalable;
    
    /**
     * 是否支持保费融资
     */
    private Boolean premiumFinancingSupported;
    
    /**
     * 是否支持提议频率
     */
    private Boolean proposalFrequencySupported;
    
    /**
     * 是否支持预付保费
     */
    private Boolean proposalPrepaymentSupported;
    
    /**
     * 提议金额类型（如"BOTH"）
     */
    private String proposalAmountType;
    
    /**
     * 提议金额提示
     */
    private String proposalAmountTip;
    
    /**
     * 减额提示
     */
    private String reduceAmountTips;
    
    /**
     * banner图片URL
     */
    private String bannerUrl;
    
    /**
     * 年龄范围{min:0,max:128}
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Integer> ageRange;
    
    /**
     * 支持币种["USD"]
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> currencies;
    
    /**
     * 缴费期限["10","18","25","30"]
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> paymentTerm;
    
    /**
     * 缴费频率["ANNUALLY","SEMI_ANNUALLY","QUARTERLY","MONTHLY"]
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> paymentFrequencies;
    
    /**
     * 提议语言["HK","CN","EN"]
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> proposalLanguages;
    
    /**
     * 产品亮点["涵盖58种危疾..."]
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> productHighlights;
    
    /**
     * 产品标签
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> productTags;
    
    /**
     * 产品特性{maxWithdrawal:false,...}
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> productFeatures;
    
    /**
     * 产品详情[{key:"CIBasicInformation",name:"基本信息",...}]
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Map<String, Object>> productDetails;
    
    /**
     * 产品文件[{fileName:"愛伴航.pdf",...}]
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Map<String, Object>> productFiles;
    
    /**
     * 文件标签
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> fileTags;
    
    /**
     * 自定义文件[{fileName:"爱伴航基本定义.pdf",...}]
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Map<String, Object>> customFiles;
    
    /**
     * 自定义文件标签
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> customFileTags;
    
    /**
     * 产品信息[{id:2443,title:"AIA:(更新)2025第一季度客戶推廣優優惠",...}]
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Map<String, Object>> productInformation;
    
    /**
     * 医疗选项
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> medicalOptions;
    
    /**
     * 动态字段
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Map<String, Object>> dynamicFields;
    
    /**
     * 报价字段要求[{field:"Age",required:true},...}]
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Map<String, Object>> quotationFieldRequirements;
    
    /**
     * 提议附加险["AIA:CEO5R","AIA:CEOP5R",...]
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> proposalRiders;
    
    /**
     * 自定义缴费期限
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Object customPaymentTerm;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 删除标记(0-未删除,1-已删除)
     */
    @TableLogic
    private Integer isDeleted;
    
    /**
     * 删除时间
     */
    private LocalDateTime deletedAt;

    /**
     * 是否可制作计划书
     */
    private Integer isProposal;
} 