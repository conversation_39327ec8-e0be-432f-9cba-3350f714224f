package com.sub.baoxian.model.entity;

import java.time.LocalDateTime;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/*
 * 计划书生成配置表
 */
@Data
@TableName(value = "ins_proposal_product_config", autoResultMap = true)
public class ProposalProductConfig {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String productName;

    private String productCode;

    /**
     * 产品类型：储蓄-SAVINGS
     * 人寿-LIFE
     * 危疾-CRITICAL_ILLNESS
     * 医疗-MEDICAL
     */
    private String productType;

    /**
     * 供款年限
     */
    private String paymentYears;

    /**
     * 支持货币类型
     * 人民币-CNY
     * 港币-HKD
     * 美元-USD
     * 澳元-AUD
     * 欧元-EUR
     * 英镑-GBP
     * 加拿大元-CAD
     * 新加坡元-SGD
     * 瑞士法郎-CHF
     * 日元-JPY
     * 新西兰元-NZD
     * 韩元-KRW
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> supportCurrency;

    /**
     * 保额区间
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> amountRange;

    /**
     * 保费区间
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> premiumRange;

    /**
     * 地区
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> region;

    /**
     * 是否支持提取金额
     */
    private Boolean supportWithdrawal;

    /**
     * 预缴年份
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> prepaymentYears;

    /**
     * 缴费方式 ["ANNUALLY", "SINGLE_PAYMENT", "MONTHLY", "QUARTERLY", "SEMI_ANNUALLY"]
     * ANNUALLY: 年缴
     * SINGLE_PAYMENT: 趸缴
     * MONTHLY: 月缴
     * QUARTERLY: 季缴
     * SEMI_ANNUALLY: 半年缴
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> paymentMethods;

    /**
     * 是否可以鎖定終期紅利
     */
    private Boolean canLockendPeriodBonus;

    /**
     * 自定现金年龄偏差
     */
    private Integer customWithdrawalAgeOffset;

    /**
     * 保障期限
     */
    private String guaranteePeriod;

    /**
     * 公司logo图片url
     */
    private String companyLogoUrl;

    /**
     * 基准数据（演算表）
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private ProposalCalculateDataBO baseData;

    /**
     * 基准数据（提取红利）
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private ProposalWithdrawalDataBO withdrawalData;

    /**
     * 状态：0-下架 1-上架
     */
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateAt;

    @Data
    public static class ProposalCalculateDataBO {
        private Integer baseAmount;
        private List<ProposalCalculateData> data;

        @Data
        public static class ProposalCalculateData {
            // 保单年度
            private Integer policyYear;
            // 已缴保费
            private Integer totalPremiumPaid;
            // 退保保证金额
            private Integer surrenderGuaranteed;
            // 终期红利金额
            private Integer terminalBonus;
            // 总金额
            private Integer totalAmount;
        }
    }

    @Data
    public static class ProposalWithdrawalDataBO {
        private Integer baseAmount;
        private List<ProposalWithdrawalData> data;

        @Data
        public static class ProposalWithdrawalData {
            // 保单年度
            private Integer policyYear;
            // 已缴保费
            private Integer totalPremiumPaid;
            // 已提取红利总额
            private Integer totalWithdrawnBonus;
            // 退保保证金额
            private Integer surrenderGuaranteed;
            // 终期红利金额
            private Integer terminalBonus;
            // 总金额
            private Integer totalAmount;
        }
    }

}
