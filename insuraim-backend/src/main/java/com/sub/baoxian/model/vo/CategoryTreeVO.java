package com.sub.baoxian.model.vo;

import java.util.List;

import lombok.Data;

/**
 * 分类树节点VO
 */
@Data
public class CategoryTreeVO {
    
    /**
     * 分类ID
     */
    private Long id;
    
    /**
     * 分类名称
     */
    private String name;
    
    /**
     * 分类描述
     */
    private String description;
    
    /**
     * 父级ID
     */
    private Long parentId;
    
    /**
     * 创建时间
     */
    private Long createdAt;
    
    /**
     * 更新时间
     */
    private Long updatedAt;
    
    /**
     * 公司ID
     */
    private Long companyId;
    
    /**
     * 子分类列表
     */
    private List<CategoryTreeVO> children;
} 