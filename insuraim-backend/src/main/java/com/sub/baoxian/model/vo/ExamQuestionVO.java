package com.sub.baoxian.model.vo;

import java.util.List;

import com.sub.baoxian.model.entity.ExamAnswer;

import lombok.Data;

@Data
public class ExamQuestionVO {

    /**
     * 题目ID
     */
    private Long id;

    /**
     * 所属类目ID
     */
    private Long examId;

    /**
     * 难度等级
     */
    private String difficulty;

    /**
     * 大章节(如1)
     */
    private String majorChapter;

    /**
     * 详细章节(如1.1.3)
     */
    private String chapter;

    /**
     * 题干内容
     */
    private String question;

    /**
     * 答案
     */
    private List<ExamAnswer> answer;

    /**
     * 答对反馈
     */
    private String feedbackCorrect;

    /**
     * 答错反馈
     */
    private String feedbackIncorrect;

    /**
     * 是否启用
     */
    private Integer status;
}
