package com.sub.baoxian.model.vo;

import com.sub.baoxian.model.entity.File;
import com.sub.baoxian.model.entity.Folder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 文件夹内容VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FolderContentVO {
    /**
     * 当前文件夹ID
     */
    private Long folderId;
    
    /**
     * 当前文件夹信息
     */
    private Folder currentFolder;
    
    /**
     * 子文件夹列表
     */
    private List<Folder> folders;
    
    /**
     * 文件列表
     */
    private List<File> files;
    
    /**
     * 总文件夹数
     */
    private Long totalFolders;
    
    /**
     * 总文件数
     */
    private Long totalFiles;
} 