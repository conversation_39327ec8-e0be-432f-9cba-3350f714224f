package com.sub.baoxian.model.vo;

import lombok.Data;

/**
 * 产品内容VO
 */
@Data
public class InsProductDetailContentVO {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 关联产品ID
     */
    private Integer productId;

    /**
     * 关联分类标题ID
     */
    private Integer titleId;

    /**
     * 标题名称
     */
    private String titleName;

    /**
     * 内容值
     */
    private String contentValue;

    /**
     * 创建时间戳(毫秒)
     */
    private Long createdAt;

    /**
     * 更新时间戳(毫秒)
     */
    private Long updatedAt;
}
