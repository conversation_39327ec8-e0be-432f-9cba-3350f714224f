package com.sub.baoxian.model.vo;

import lombok.Data;

import java.util.List;

/**
 * 产品分类标题VO
 */
@Data
public class InsProductDetailTitleVO {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 关联产品分类ID
     */
    private Integer categoryId;

    /**
     * 父标题ID，顶级标题为NULL
     */
    private Integer parentId;

    /**
     * 标题名称
     */
    private String titleName;

    /**
     * 同级排序
     */
    private Integer rankValue;

    /**
     * 创建时间戳(毫秒)
     */
    private Long createdAt;

    /**
     * 更新时间戳(毫秒)
     */
    private Long updatedAt;

    /**
     * 子标题列表（用于树形结构）
     */
    private List<InsProductDetailTitleVO> children;
}
