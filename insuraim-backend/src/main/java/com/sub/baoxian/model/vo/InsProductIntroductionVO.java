package com.sub.baoxian.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 产品介绍VO
 */
@Data
@Schema(description = "产品介绍VO")
public class InsProductIntroductionVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 产品ID
     */
    @Schema(description = "产品ID")
    private Long productId;

    /**
     * 产品名称
     */
    @Schema(description = "产品名称")
    private String productName;

    /**
     * 产品摘要/简介
     */
    @Schema(description = "产品摘要/简介")
    private String productSummary;

    /**
     * 产品banner图URL
     */
    @Schema(description = "产品banner图URL")
    private String bannerImageUrl;

    /**
     * 产品特点列表
     */
    @Schema(description = "产品特点列表")
    private List<ProductFeatureItemVO> productFeatures;

    /**
     * 产品概况（HTML格式的富文本）
     */
    @Schema(description = "产品概况（HTML格式的富文本）")
    private String productOverview;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用（1-启用，0-禁用）")
    private Integer status;

    /**
     * 创建时间(Unix时间戳)
     */
    @Schema(description = "创建时间(Unix时间戳)")
    private Long createdAt;

    /**
     * 更新时间(Unix时间戳)
     */
    @Schema(description = "更新时间(Unix时间戳)")
    private Long updatedAt;

    /**
     * 产品特点项VO
     */
    @Data
    @Schema(description = "产品特点项VO")
    public static class ProductFeatureItemVO {
        
        /**
         * 特点名称
         */
        @Schema(description = "特点名称")
        private String name;
        
        /**
         * 特点值/描述
         */
        @Schema(description = "特点值/描述")
        private String value;
        
        /**
         * 图标URL（可选）
         */
        @Schema(description = "图标URL")
        private String iconUrl;
        
        /**
         * 排序值
         */
        @Schema(description = "排序值")
        private Integer sortOrder;
    }
}
