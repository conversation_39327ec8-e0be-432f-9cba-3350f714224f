package com.sub.baoxian.model.vo;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 子产品信息VO类
 */
@Data
public class InsProductSubVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 父产品ID
     */
    private Integer parentProductId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 缴费期限
     */
    private String paymentTerm;

    /**
     * 币种
     */
    private String currency;

    /**
     * 年龄范围
     */
    private Map<String, Object> ageRange;

    /**
     * 保额范围
     */
    private List<String> sumInsuredRange;

    /**
     * 保费范围
     */
    private List<String> premiumRange;

    /**
     * 预缴年数
     */
    private List<Integer> prepaymentYears;

    /**
     * 缴费方式
     */
    private List<String> paymentMethods;

    /**
     * 是否有现金价值
     */
    private Integer hasCashValue;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建时间(Unix时间戳)
     */
    private Long createdAt;

    /**
     * 更新时间(Unix时间戳)
     */
    private Long updatedAt;
}
