package com.sub.baoxian.model.vo;

import java.util.List;
import java.util.Map;

import lombok.Data;

/**
 * 产品信息VO类（包含产品详情结构）
 */
@Data
public class InsProductVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 保险公司代码
     */
    private String companyCode;

    /**
     * 保险公司名称
     */
    private String companyName;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 产品类别代码
     */
    private String categoryCode;

    /**
     * 产品类别名称
     */
    private String categoryName;

    /**
     * 产品描述
     */
    private String description;

    /**
     * 产品logo URL
     */
    private String logoUrl;

    /**
     * 销售地区
     */
    private String region;

    /**
     * 保障年限
     */
    private String guaranteePeriod;

    /**
     * 产品类型
     */
    private String productType;

    /**
     * 产品tags
     */
    private List<String> tags;

    /**
     * 缴费期限
     */
    private List<String> paymentTerm;

    /**
     * 年龄范围
     */
    private Map<String, Object> ageRange;

    /**
     * 支持币种
     */
    private List<String> currencies;

    /**
     * 缴费方式
     */
    private List<String> paymentMethods;

    /**
     * 1是允许预缴，0是不适用
     */
    private Integer isPrepayment;

    /**
     * 是否支持生成计划书
     */
    private Integer isProposal;

    /**
     * 产品小册子
     */
    private String productBrochure;

    /**
     * 是否有现金价值
     */
    private Integer isCashValue;

    /**
     * 启用状态
     */
    private Integer status;

    /**
     * 创建时间(Unix时间戳)
     */
    private Long createdAt;

    /**
     * 更新时间(Unix时间戳)
     */
    private Long updatedAt;

    /**
     * 产品详情结构列表
     */
    private List<ProductDetailStructuredVO> productDetails;

    /**
     * 子产品列表
     */
    private List<InsProductSubVO> subProducts;

    /**
     * 产品介绍
     */
    private InsProductIntroductionVO productIntroduction;
}
