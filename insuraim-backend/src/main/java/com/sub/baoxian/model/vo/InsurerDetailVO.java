package com.sub.baoxian.model.vo;

import com.sub.baoxian.model.entity.ProductCategories;
import com.sub.baoxian.model.entity.CompanyCreditRatings;
import com.sub.baoxian.model.entity.CompanyFile;
import com.sub.baoxian.model.entity.CompanyLink;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 保险公司详情VO
 */
@Data
public class InsurerDetailVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 保险公司代码
     */
    private String code;

    /**
     * 保险公司名称
     */
    private String name;

    /**
     * 保险公司描述
     */
    private String desc;

    /**
     * 所属地区
     */
    private String region;

    /**
     * 公司LOGO URL
     */
    private String logoUrl;

    /**
     * 公司官网
     */
    private String website;

    /**
     * Banner图片URL
     */
    private String banner;

    /**
     * 行业排名
     */
    private Integer companyRank;

    /**
     * 征费率
     */
    private BigDecimal levyRate;

    /**
     * 汇率
     */
    private BigDecimal exchangeRate;

    /**
     * 汇率币种
     */
    private String exchangeCurrency;

    /**
     * 汇率更新时间
     */
    private Long exchangeRateTime;

    /**
     * 汇率查询URL
     */
    private String exchangeRateUrl;

    /**
     * 文件检查时间
     */
    private Long documentInspectionTime;

    /**
     * 分类列表
     */
    private List<ProductCategories> categories;

    /**
     * 信用评级列表
     */
    private List<CompanyCreditRatings> creditRatings;

    // /**
    // * 产品列表
    // */
    // private List<Products> products;

    /**
     * 文件列表
     */
    private List<CompanyFile> files;

    /**
     * 自定义文件列表
     */
    private List<CompanyFile> customFiles;

    /**
     * 链接列表
     */
    private List<CompanyLink> links;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 标签列表
     */
    private List<String> tags;
}
