package com.sub.baoxian.model.vo;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

@Data
public class OrganizationPageUsersListVO {

    private Long id;

    private String roleName;

    private String orgName;

    private String username;

    private String name;

    private String avatar;

    private String phone;

    private String email;

    private Integer status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastLoginTime;

    private String lastLoginIp;
    
    
}
