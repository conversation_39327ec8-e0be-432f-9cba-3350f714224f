package com.sub.baoxian.model.vo;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.List;

import lombok.AllArgsConstructor;

/**
 * 产品比较列表视图对象，合并了产品详情和通用信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductCompareListVO {

    /**
     * 产品详情ID
     */
    private Integer id;

    /**
     * 保险代码
     */
    private List<String> code;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 通用信息ID
     */
    private Integer commonId;

    /**
     * 货币单位
     */
    private List<String> policyCurrency;

    /**
     * 缴费年限
     */
    private List<String> premiumPaymentPeriod;

    /**
     * 最低投保年齡
     */
    private List<String> minimumAge;

    /**
     * 最高投保年齡
     */
    private List<String> maximumAge;

    /**
     * 最低基本金額限制
     */
    private List<String> minimumSumInsured;

    /**
     * 最高基本金額限制
     */
    private List<String> maximumSumInsured;

    /**
     * 保單销售日期
     */
    private String sellDate;

    /**
     * 保單日期
     */
    private String policyDate;

    /**
     * 每年預繳優惠利率
     */
    private String annualPrepaymentInterestRate;

    /**
     * 核保方式
     */
    private String underwritingMethodOrUnderwritingProcess;

    /**
     * 醫療核保
     */
    private Boolean medicalUnderwriting;

    /**
     * 財務核保
     */
    private Boolean financialUnderwriting;

    /**
     * 職業核保
     */
    private Boolean occupationalUnderwriting;

    /**
     * 地區核保
     */
    private Boolean geographicalUnderwriting;

    /**
     * 免體檢限額
     */
    private Boolean noMedicalExamLimit;

    /**
     * 附加額外加費
     */
    private Boolean additionalLoadingApplicable;

    /**
     * 特別單證
     */
    private Boolean specialDocumentationRequired;

    /**
     * 嚴重疾病賠償 (详情)
     */
    private Boolean criticalIllnessCoverageDetail;

    /**
     * 住院賠償適用於非尊尚醫療保障計劃
     */
    private Boolean hospitalizationBenefitNonPremiumPlan;

    /**
     * 住院賠償適用於尊尚醫療保障計劃
     */
    private Boolean hospitalizationBenefitPremiumPlan;

    /**
     * 意外賠償
     */
    private Boolean accidentalBenefit;

    /**
     * 免繳/供款者免繳保費賠償
     */
    private Boolean premiumWaiverBenefit;

    /**
     * 長期病假賠償
     */
    private Boolean longTermSickLeaveBenefit;

    /**
     * 個人門診賠償
     */
    private Boolean outpatientBenefit;

    /**
     * 女性賠償
     */
    private Boolean femaleSpecificBenefit;

    /**
     * 分享喜悅獎勵
     */
    private Boolean babyRewardBenefit;

    /**
     * 傷殘賠償
     */
    private Boolean disabilityBenefit;

    /**
     * 增加保額
     */
    private Boolean increaseSumInsured;

    /**
     * 減少保額
     */
    private Boolean decreaseSumInsured;

    /**
     * 增加基本金額
     */
    private Boolean addBaseCoverage;

    /**
     * 減低基本金額
     */
    private Boolean reduceBaseCoverage;

    /**
     * 增加附加保障
     */
    private Boolean addRider;

    /**
     * 刪減附加保障
     */
    private Boolean removeRider;

    /**
     * 保單貸款
     */
    private Boolean policyLoan;

    /**
     * 續期保費繳付方法
     */
    private Boolean renewalPaymentMethod;

    /**
     * 預繳保費
     */
    private Boolean prepaidPremium;

    /**
     * 保單復效
     */
    private Boolean policyReinstatement;

    /**
     * 自動保費貸款
     */
    private Boolean automaticPremiumLoan;

    /**
     * 更改計劃
     */
    private Boolean changePlan;

    /**
     * 減額清繳保險
     */
    private Boolean reducedPaidUpOption;

    /**
     * 其他字段 (详情)
     */
    private HashMap<String, String> othersDetail;

    // 以下是ProductCommonCompare中的字段

    /**
     * 保费费率
     */
    private String premiumRate;

    /**
     * 期末红利
     */
    private String terminalDividend;

    /**
     * 期末红利管理收益
     */
    private String trminalDividendManagementBenefit;

    /**
     * 高保额折扣
     */
    private String highCoverageDiscount;

    /**
     * 特定疾病保障
     */
    private String specialDiseaseCoverage;

    /**
     * 危疾保费豁免
     */
    private String waiverOfPremiumOnCriticalIllness;

    /**
     * 儿童额外危疾保障
     */
    private String additionalCriticalIllnessCoverageForChildren;

    /**
     * 早期疾病保障
     */
    private String earlyStageIllnessCoverage;

    /**
     * 危疾保障 (通用)
     */
    private String criticalIllnessCoverage;

    /**
     * 危疾保费豁免利益
     */
    private String waiverOfPremiumOnCriticalIllnessBenefit;

    /**
     * 额外危疾保障
     */
    private String additionalCriticalIllnessCoverage;

    /**
     * 多重危疾保障
     */
    private String multipleCriticalIllnessCoverage;

    /**
     * .家庭共享保额
     */
    private String familySharedCoverage;

    /**
     * 全面保障计划
     */
    private String allRoundProtectionPlan;

    /**
     * 额外全面保障计划
     */
    private String additionalAllRoundProtectionPlan;

    /**
     * 额外身故赔偿
     */
    private String additionalDeathBenefit;

    /**
     * 意外身故赔偿
     */
    private String accidentalDeathBenefit;

    /**
     * 突发身故保障
     */
    private String suddenDeathCoverage;

    /**
     * 父母保费豁免利益
     */
    private String parentalWaiverBenefit;

    /**
     * 配偶保费豁免利益
     */
    private String spousalWaiverBenefit;

    /**
     * 儿童延伸保障
     */
    private String extendedCoverageForChildren;

    /**
     * 期末红利（非保证）
     */
    private String terminalDividendNonGuarantee;

    /**
     * 保单终止
     */
    private String policyTermination;

    /**
     * 其他利益
     */
    private String otherBenefits;

    /**
     * 特别备注
     */
    private String specialRemarks;

    /**
     * 保单逆按揭贷款
     */
    private String policyReverseMortgageLoan;

    /**
     * 吸烟状况变更
     */
    private String changeInSmokingStatus;

    /**
     * 费率表
     */
    private String rateTable;

    /**
     * 产品说明书
     */
    private String productFactsheet;

    /**
     * 利益条款
     */
    private String benefitProvisions;

    /**
     * 额外保费
     */
    private String additionalPremium;

    /**
     * 所需文件
     */
    private String supportingDocuments;

    /**
     * 保费豁免
     */
    private String waiverOfPremium;

    /**
     * 计划相关
     */
    private String planRelated;

    /**
     * 培训资料
     */
    private String training;
}
