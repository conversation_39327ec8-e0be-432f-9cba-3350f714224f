package com.sub.baoxian.model.vo;

import lombok.Data;

import java.util.List;

/**
 * 产品详情结构化VO
 */
@Data
public class ProductDetailStructuredVO {

    /**
     * 标题名称
     */
    private String name;

    /**
     * 排序
     */
    private Integer rankValue;

    /**
     * 属性列表
     */
    private List<AttributeInfo> attributes;

    /**
     * 属性信息
     */
    @Data
    public static class AttributeInfo {
        /**
         * 属性名称
         */
        private String name;

        /**
         * 排序
         */
        private Integer rankValue;

        /**
         * 属性值
         */
        private String value;

        /**
         * 属性唯一标识
         */
        private String attribute;
    }
}
