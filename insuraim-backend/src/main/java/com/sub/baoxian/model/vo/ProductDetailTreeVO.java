package com.sub.baoxian.model.vo;

import lombok.Data;

import java.util.List;

/**
 * 产品详情树形结构VO
 */
@Data
public class ProductDetailTreeVO {

    /**
     * 标题ID
     */
    private Integer titleId;

    /**
     * 标题名称
     */
    private String titleName;

    /**
     * 父标题ID
     */
    private Integer parentId;

    /**
     * 同级排序
     */
    private Integer rankValue;

    /**
     * 产品内容列表
     */
    private List<InsProductDetailContentVO> contents;

    /**
     * 子标题列表
     */
    private List<ProductDetailTreeVO> children;
}
