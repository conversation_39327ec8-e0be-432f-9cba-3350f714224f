package com.sub.baoxian.model.vo;

import java.util.List;

import com.sub.baoxian.model.entity.KnowledgeAttachment;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class getKnowledgeBaseByIdVO {

    /**
     * 知识库ID
     */
    private Long kbId;
    
    /**
     * 知识标题
     */
    private String title;
    
    /**
     * 知识内容
     */
    private String content;
    
    /**
     * 分类名称
     */
    private String category;
    
    /**
     * 状态：1-正常，0-禁用
     */
    private Integer status;
    
    /**
     * 创建时间戳（Unix timestamp）
     */
    private Long createdAt;
    
    /**
     * 最后更新时间戳
     */
    private Long updatedAt;
    
    /**
     * 作者ID
     */
    private Long authorId;
    
    /**
     * 摘要
     */
    private String summary;
    
    /**
     * 浏览量
     */
    private Integer views;
    
    /**
     * 标签，多个标签用逗号分隔
     */
    private String tags;
    
    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 类型："article"-文章，"file"-文件
     */
    private String type;

    /**
     * 附件列表
     */
    private List<KnowledgeAttachment> attachments;
}
