package com.sub.baoxian.news.common;

import com.alibaba.fastjson2.JSONObject;
import com.sub.baoxian.news.model.HttpResultBo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.HttpsURLConnection;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.HttpURLConnection;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class HttpUtils { private static final Logger log = LoggerFactory.getLogger(HttpUtils.class);
    private static final int DEFAULT_TIMEOUT = 50000;
    private static final String DEFAULT_CHARSET = "UTF-8";
    private static final int MAX_REDIRECTS = 1;
    private static final int MAX_RETRIES = 3;
    private static final long RETRY_DELAY_MS = 1000;

    /**
     * HTTP请求配置
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class HttpConfig {
        /**
         * 请求地址
         */
        private String url;
        /**
         * 请求方法
         */
        @Builder.Default
        private String method = "GET";
        /**
         * 请求头
         */
        @Builder.Default
        private Map<String, String> headers = new HashMap<>();
        /**
         * 请求体
         */
        private JSONObject body;
        /**
         * 字符集
         */
        @Builder.Default
        private String charset = DEFAULT_CHARSET;
        /**
         * 超时
         */
        @Builder.Default
        private int timeout = DEFAULT_TIMEOUT;
        /**
         * 最大重试次数
         */
        @Builder.Default
        private int maxRetries = MAX_RETRIES;
        /**
         * 是否跟随重定向
         */
        @Builder.Default
        private boolean followRedirects = true;
    }

    /**
     * 设置请求头
     */
    private static void setHeaders(HttpURLConnection connection, Map<String, String> headerMap) {
        if (headerMap != null && !headerMap.isEmpty()) {
            for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                connection.setRequestProperty(entry.getKey(), entry.getValue());
            }
        }
    }

    /**
     * 处理响应
     */
    private static void handleResponse(HttpURLConnection connection, StringBuilder result, String charset) throws IOException {
        try (BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream(), charset))) {
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
        }
    }

    /**
     * 执行HTTP请求
     */
    private static HttpResultBo executeRequest(HttpConfig config) {


        try {
            return doExecuteRequest(config);
        } catch (Exception e) {
            log.error("请求失败， URL: {}, 错误: {}", config.url, e.getMessage());
            return new HttpResultBo(0, "请求失败: " + e.getMessage(), "", config.url, new HashMap<>());
        }

    }

    /**
     * 执行HTTP请求的具体实现
     */
    private static HttpResultBo doExecuteRequest(HttpConfig config) throws IOException {
        StringBuilder result = new StringBuilder();
        Integer status = 0;
        String message = null;
        Map<String, List<String>> headers = new HashMap<>();


        URL url = new URL(config.url);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod(config.method);
        connection.setConnectTimeout(config.timeout);
        connection.setReadTimeout(config.timeout);
        connection.setInstanceFollowRedirects(config.followRedirects);
        setHeaders(connection, config.headers);

        if (config.body != null) {
            connection.setDoOutput(true);
            try (PrintWriter out = new PrintWriter(connection.getOutputStream())) {
                out.print(config.body.toString());
                out.flush();
            }
        }

        int responseCode = connection.getResponseCode();
        if (responseCode == HttpURLConnection.HTTP_OK || responseCode == HttpURLConnection.HTTP_CREATED) {
            status = responseCode;
            handleResponse(connection, result, config.charset);
            headers = connection.getHeaderFields();
        } else if (config.followRedirects &&
                (responseCode == HttpURLConnection.HTTP_MOVED_PERM ||
                        responseCode == HttpURLConnection.HTTP_MOVED_TEMP)) {
            String location = connection.getHeaderField("Location");
            log.info("重定向到: {}", location);
            HttpConfig redirectConfig = HttpConfig.builder()
                    .url(location)
                    .method(config.method)
                    .headers(config.headers)
                    .body(config.body)
                    .charset(config.charset)
                    .timeout(config.timeout)
                    .maxRetries(config.maxRetries)
                    .followRedirects(false) // 防止重定向循环
                    .build();
            return executeRequest(redirectConfig);
        } else {
            handleResponse(connection, result, config.charset);
            message = "HTTP请求失败，状态码: " + responseCode;
            log.warn("{} - URL: {}", message, config.url);
        }

        return new HttpResultBo(status, message, result.toString(), config.url, headers);
    }

    /**
     * 向指定 URL 发送GET方法的请求
     */
    public static HttpResultBo sendGet(String url) {
        return sendGet(url, null);
    }

    /**
     * 向指定 URL 发送GET方法的请求
     */
    public static HttpResultBo sendGet(String url, Map<String, String> headerMap) {
        return sendGet(url, headerMap, DEFAULT_CHARSET, DEFAULT_TIMEOUT);
    }

    /**
     * 向指定 URL 发送GET方法的请求
     */
    public static HttpResultBo sendGet(String url, Map<String, String> headerMap, String charset, int timeout) {
        HttpConfig config = HttpConfig.builder()
                .url(url)
                .method("GET")
                .headers(headerMap)
                .charset(charset)
                .timeout(timeout)
                .build();
        return executeRequest(config);
    }

    /**
     * 向指定 URL 发送PUT方法的请求
     */
    public static HttpResultBo sendPut(String url) {
        return sendPut(url, null, null);
    }

    /**
     * 向指定 URL 发送PUT方法的请求
     */
    public static HttpResultBo sendPut(String url, JSONObject jsonParam, Map<String, String> headerMap) {
        return sendPut(url, jsonParam, headerMap, DEFAULT_CHARSET, DEFAULT_TIMEOUT);
    }

    /**
     * 向指定 URL 发送PUT方法的请求
     */
    public static HttpResultBo sendPut(String url, JSONObject jsonParam, Map<String, String> headerMap, String charset, int timeout) {
        HttpConfig config = HttpConfig.builder()
                .url(url)
                .method("PUT")
                .headers(headerMap)
                .body(jsonParam)
                .charset(charset)
                .timeout(timeout)
                .build();
        return executeRequest(config);
    }

    /**
     * 向指定 URL 发送POST方法的请求
     */
    public static HttpResultBo sendPost(String url) {
        return sendPost(url, null, null);
    }

    /**
     * 向指定 URL 发送POST方法的请求
     */
    public static HttpResultBo sendPost(String url, JSONObject jsonParam, Map<String, String> headerMap) {
        return sendPost(url, jsonParam, headerMap, DEFAULT_CHARSET, DEFAULT_TIMEOUT);
    }

    /**
     * 向指定 URL 发送POST方法的请求
     */
    public static HttpResultBo sendPost(String url, JSONObject jsonParam, Map<String, String> headerMap, String charset, int timeout) {
        HttpConfig config = HttpConfig.builder()
                .url(url)
                .method("POST")
                .headers(headerMap)
                .body(jsonParam)
                .charset(charset)
                .timeout(timeout)
                .build();
        return executeRequest(config);
    }

    /**
     * 向指定 URL 发送DELETE方法的请求
     */
    public static HttpResultBo sendDelete(String url, Map<String, String> headerMap) {
        return sendDelete(url, headerMap, DEFAULT_CHARSET, DEFAULT_TIMEOUT);
    }

    /**
     * 向指定 URL 发送DELETE方法的请求
     */
    public static HttpResultBo sendDelete(String url, Map<String, String> headerMap, String charset, int timeout) {
        HttpConfig config = HttpConfig.builder()
                .url(url)
                .method("DELETE")
                .headers(headerMap)
                .charset(charset)
                .timeout(timeout)
                .build();
        return executeRequest(config);
    }


    /**
     * 链接测速
     */
    public static long openUrlSpeed(String urlStr) {
        try {
            long startTime;
            URL url = new URL(urlStr);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            connection.setRequestMethod("HEAD"); // 使用HEAD方法，只获取响应头，不获取响应体
            connection.setConnectTimeout(30000); // 设置连接超时（毫秒）
            connection.setReadTimeout(30000);     // 设置读取超时（毫秒）
            startTime = System.currentTimeMillis();
            connection.connect();
            ;
            return System.currentTimeMillis() - startTime;
        } catch (Exception e) {
            return -1L;
        }
    }

}
