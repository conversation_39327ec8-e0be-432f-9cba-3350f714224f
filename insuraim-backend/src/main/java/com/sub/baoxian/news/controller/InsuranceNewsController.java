package com.sub.baoxian.news.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.model.entity.InsuranceNews;
import com.sub.baoxian.news.service.NewsContent;
import com.sub.baoxian.news.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/common/news")
@RequiredArgsConstructor
public class InsuranceNewsController {

    private final NewsContent newsContent;

    @GetMapping("/list")
    public Result<Page<InsuranceNews>> page( @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                             @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                             @RequestParam(value = "tags",required = false) String tags,
                                             @RequestParam(value = "title", required = false) String title) {
        InsuranceNews insuranceNews = new InsuranceNews();
        insuranceNews.setTags(tags);
        insuranceNews.setTitle(title);
        if (StringUtils.isEmpty(tags) || !tags.equals("产品优惠")) {
            return Result.success(newsContent.getNewsList(insuranceNews, pageNum, pageSize));
        } else {
            return Result.success(newsContent.getProductDiscountList(insuranceNews, pageNum, pageSize));
        }
    }
}
