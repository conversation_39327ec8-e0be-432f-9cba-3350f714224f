package com.sub.baoxian.news.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChannelRss implements Serializable {
    private String title;
    private String language;
    private String icon;
    private String charset;
    private String link;
    private String originLink;
    private String description;
    private String generator;
    private String webMaster;
    private Integer ttl;
    private RssItem[] item;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RssItem implements Serializable {
        private String title;

        private String description;

        private String link;

        private String guid;

        private String pubDate;

        private String author;

        private String img;
    }
}
