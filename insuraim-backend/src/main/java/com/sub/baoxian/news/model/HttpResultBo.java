package com.sub.baoxian.news.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class HttpResultBo implements Serializable {

    /**
     * 状态码
     */
    private Integer code;
    /**
     * 返回信息
     */
    private String message;
    /**
     * 返回数据
     */

    private String data;
    /**
     * 请求的链接
     */
    private String url;
    /**
     * 请求的响应头
     */
    private Map<String, List<String>> headers;
}
