package com.sub.baoxian.news.service;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.mapper.FileMapper;
import com.sub.baoxian.model.entity.File;
import com.sub.baoxian.model.entity.InsuranceNews;
import com.sub.baoxian.mapper.InsuranceNewsMapper;
import com.sub.baoxian.news.utils.StringUtils;
import com.sub.baoxian.news.utils.TimeUtils;
import com.sub.baoxian.service.FileService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.util.*;

@Service
@RequiredArgsConstructor
public class NewsContent {
    private Logger log = LoggerFactory.getLogger(this.getClass());


    private final InsuranceNewsMapper insuranceNewsMapper;

    private final Map<String, NewsStrategy> newsStrategyMap;

    private final FileMapper fileMapper;


    /**
     * 执行全部策略模式进行爬取数据
     */
    public void insuranceNewsDb() {
        List<InsuranceNews> newsList = new ArrayList<>();
        Set<String> groupSet = new HashSet<>();
        for (Map.Entry<String, NewsStrategy> entry : newsStrategyMap.entrySet()) {
            log.info("开始执行策略: {}", entry.getKey());
            NewsStrategy newsStrategy = entry.getValue();
            List<InsuranceNews> insuranceNewsList = newsStrategy.getLinkNewsList();
            if (insuranceNewsList != null && insuranceNewsList.size() > 0) {
                for (InsuranceNews insuranceNews : insuranceNewsList) {
                    if (groupSet.add(insuranceNews.getGroupHash())) {
                        InsuranceNews insuranceNews1 = insuranceNewsMapper.getInsuranceNews(insuranceNews.getGroupHash());
                        if (insuranceNews1 == null) {
                            newsList.add(insuranceNews);
                        }
                    }
                }
            }
        }
        if (newsList != null && newsList.size() > 0) {
            insuranceNewsMapper.batchInsuranceNews(newsList);
        }
    }

    /**
     * 执行指定策略模式进行爬取数据
     * @return
     */
    public void insuranceNewsDb(String strategyName) {
        NewsStrategy newsStrategy = newsStrategyMap.get(strategyName);
        Set<String> groupSet = new HashSet<>();
        List<InsuranceNews> newsList = new ArrayList<>();
        if (newsStrategy != null) {
            List<InsuranceNews> insuranceNewsList = newsStrategy.getLinkNewsList();
            if (insuranceNewsList != null && insuranceNewsList.size() > 0) {
                for (InsuranceNews insuranceNews : insuranceNewsList) {
                    if (groupSet.add(insuranceNews.getGroupHash())) {
                        InsuranceNews insuranceNews1 = insuranceNewsMapper.getInsuranceNews(insuranceNews.getGroupHash());
                        if (insuranceNews1 == null) {
                            newsList.add(insuranceNews);
                        }
                    }
                }
            }
        }
        if (newsList != null && newsList.size() > 0) {
            insuranceNewsMapper.batchInsuranceNews(newsList);
        }
    }




    public Page<InsuranceNews> getNewsList(InsuranceNews insuranceNews, Integer pageNum, Integer pageSize) {
        // 构建查询条件
        LambdaQueryWrapper<InsuranceNews> wrapper = new LambdaQueryWrapper<>();

        if (StringUtils.isNotEmpty(insuranceNews.getTags())) {
            wrapper.like(InsuranceNews::getTags, insuranceNews.getTags());
        }
        if (StringUtils.isNotEmpty(insuranceNews.getTitle())) {
            wrapper.like(InsuranceNews::getTitle, insuranceNews.getTitle());
        }

        wrapper.orderByDesc(InsuranceNews::getPubDate);
        Page<InsuranceNews> page = new Page<>(pageNum, pageSize);
        Page<InsuranceNews> result = insuranceNewsMapper.selectPage(page, wrapper);
        return result;
    }

    /**
     * 产品优惠
     */
    public Page<InsuranceNews> getProductDiscountList(InsuranceNews insuranceNews, Integer pageNum, Integer pageSize) {
        // 构建查询条件
        LambdaQueryWrapper<File> wrapper = new LambdaQueryWrapper<>();
        Set<Integer> groupSet = new HashSet<>();
        groupSet.add(29);
        groupSet.add(30);
        groupSet.add(31);
        groupSet.add(34);
        groupSet.add(36);
        wrapper.in(File::getFolderId,  groupSet);
        Page<File> page = new Page<>(pageNum, pageSize);
        Page<File> result = fileMapper.selectPage(page, wrapper);
        List<InsuranceNews> insuranceNewsList = new ArrayList<>();
        for (File file : result.getRecords()) {
            InsuranceNews news = InsuranceNews.builder()
                    .content(file.getOssUrl())
                    .title(file.getName())
                    .pubDate(TimeUtils.getTimeStamp(file.getCreateAt().toString(), "yyyy-MM-dd HH:mm:ss"))
                    .build();
            insuranceNewsList.add(news);
        }
        Page<InsuranceNews> newPage = new Page<>();
        newPage.setRecords(insuranceNewsList);
        newPage.setTotal(result.getTotal());
        newPage.setCurrent(result.getCurrent());
        newPage.setSize(result.getSize());
        return newPage;

    }


}
