package com.sub.baoxian.news.service.strategy;

import com.sub.baoxian.model.entity.InsuranceNews;
import com.sub.baoxian.news.service.NewsStrategy;
import com.sub.baoxian.news.utils.AuthUtils;
import com.sub.baoxian.news.utils.StringUtils;
import com.sub.baoxian.news.utils.TimeUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;

import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component("chinaLife")
public class ChinaLifeStrategy implements NewsStrategy {
    private Logger log = LoggerFactory.getLogger(this.getClass());

    private static final String CHINA_LIFE_URL = "https://www.chinalife.com.hk";

    public List<InsuranceNews> getLinkNewsList() {
        List<InsuranceNews> insuranceNewsList = new ArrayList<>();
        try {
            Document document = Jsoup.connect(CHINA_LIFE_URL + "/zh-hk/about-us/news-center").get();
            Elements elements = document.select(".views-row");
            for (Element element : elements) {
                String href = CHINA_LIFE_URL + element.selectFirst("a").attr("href");
                try {
                    Document document1 = Jsoup.connect(href).get();
                    Element contentElement = document1.selectFirst("#content");
                    String content = contentElement.html();
                    if (StringUtils.isNotEmpty(content)) {
                        String img = CHINA_LIFE_URL + contentElement.selectFirst("img").attr("src");
                        content = content.replaceAll("img src=\"", "img src=\"https://www.chinalife.com.hk");
                        String title = element.selectFirst(".views-field-title").text();
                        Long pubDate = TimeUtils.getTimeStamp(element.selectFirst(".views-field-created").text(), "yyyy-MM-dd");

                        InsuranceNews insuranceNews = InsuranceNews.builder()
                                .title(title)
                                .content(content)
                                .img(img)
                                .link(href)
                                .createTime(TimeUtils.getCurrentTime())
                                .pubDate(pubDate)
                                .tags(StringUtils.randomArray(new String[]{"行业新闻", "产品动态"}) + ", 中国人寿 | 海外")
                                .status(1)
                                .popular(0)
                                .groupHash(AuthUtils.md5Hex(href))
                                .source(CHINA_LIFE_URL)
                                .build();
                        insuranceNewsList.add(insuranceNews);
                    }
                } catch (Exception e) {
                    log.error("获取中国寿险新 消息内容失败, 本次URL: " + href);
                }
            }
        } catch (Exception e) {
            log.error("获取中国寿险新闻失败");
        }
        return insuranceNewsList;
    }
}
