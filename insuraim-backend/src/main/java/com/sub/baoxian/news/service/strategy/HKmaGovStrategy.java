package com.sub.baoxian.news.service.strategy;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.sub.baoxian.model.entity.InsuranceNews;
import com.sub.baoxian.news.common.HttpUtils;
import com.sub.baoxian.news.service.NewsStrategy;
import com.sub.baoxian.news.utils.AuthUtils;
import com.sub.baoxian.news.utils.StringUtils;
import com.sub.baoxian.news.utils.TimeUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Component("hkmaGov")
public class HKmaGovStrategy implements NewsStrategy {

    private Logger log = LoggerFactory.getLogger(HKmaGovStrategy.class);

    private static final String URL = "https://api.hkma.gov.hk/public/press-releases?offset=0&lang=tc";

    public List<InsuranceNews> getLinkNewsList() {
        List<InsuranceNews> list = new ArrayList<>();
        String result = HttpUtils.sendGet(URL).getData();
        if (StringUtils.isNotEmpty(result)) {
            JSONArray jsonArray = JSONObject.parseObject(result).getJSONObject("result").getJSONArray("records");
            for (Object o : jsonArray) {
                JSONObject jsonObject = (JSONObject) o;
                String title = jsonObject.getString("title");
                String link = jsonObject.getString("link");
                String pubDate = jsonObject.getString("date");
                try {
                    Document document = Jsoup.connect(link).get();
                    String content = document.selectFirst(".content-with-right-content")
                            .selectFirst(".content-wrapper").html();

                    if (StringUtils.isNotEmpty(content)) {
                        InsuranceNews news = InsuranceNews.builder()
                                .title(title)
                                .createTime(TimeUtils.getTimeStamp(pubDate, "yyyy-MM-dd"))
                                .content(content)
                                .link(link)
                                .img("")
                                .tags(StringUtils.randomArray(new String[]{"行业新闻", "产品动态", "市场分析"}) + ",香港金融管理局")
                                .status(1)
                                .popular(0)
                                .groupHash(AuthUtils.md5Hex(link))
                                .source("https://www.hkma.gov.hk")
                                .build();
                        list.add(news);
                    }
                } catch (Exception e) {

                }
            }
        }
        return list;
    }

}
