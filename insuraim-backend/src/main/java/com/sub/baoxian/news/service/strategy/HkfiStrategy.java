package com.sub.baoxian.news.service.strategy;

import com.alibaba.fastjson2.JSONObject;
import com.sub.baoxian.model.entity.InsuranceNews;
import com.sub.baoxian.news.service.NewsStrategy;
import com.sub.baoxian.news.utils.AuthUtils;
import com.sub.baoxian.news.utils.StringUtils;
import com.sub.baoxian.news.utils.TimeUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;

@Service("hkfi")
public class HkfiStrategy implements NewsStrategy {

    private static final String URL = "https://www.hkfi.org.hk/zh/";

    @Override
    public List<InsuranceNews> getLinkNewsList() {
        Long currentTime = TimeUtils.getCurrentTime();
        Elements elements = null;
        try {
            Document document = Jsoup.connect(URL + "media-release").get();
            elements = document.select(".framer-1a4hopb-container");

        } catch (Exception e) {
            throw new RuntimeException("获取链接内容失败" + e);
        }
        List<InsuranceNews> insuranceNewsList = new java.util.ArrayList<>();
        for (Element element : elements) {
            String title = element.selectFirst(".framer-6plui4").text();
            Long pubDate = TimeUtils.getTimeStamp(element.selectFirst(".framer-styles-preset-awk5e5").text(), "dd/MM/yyyy");
            String href = URL + (element.selectFirst(".framer-HhrrV").attr("href").substring(2));
            try {
                Document jsoup1 = Jsoup.connect(href).get();
                String content = jsoup1.selectFirst(".framer-69v6xo-container").html();
                if (StringUtils.isNotEmpty((content))) {
                    InsuranceNews news = InsuranceNews.builder()
                            .title(title)
                            .createTime(currentTime)
                            .content(content)
                            .link(href)
                            .pubDate(pubDate)
                            .tags(StringUtils.randomArray(new String[]{"行业新闻", "市场分析", "政策法规"}) + ",香港保險業聯會")
                            .status(1)
                            .popular(0)
                            .groupHash(AuthUtils.md5Hex(href))
                            .source("https://www.hkfi.org.hk")
                            .build();
                    insuranceNewsList.add(news);
                }
            } catch (IOException e) {
            }
        }
        return insuranceNewsList;
    }
}
