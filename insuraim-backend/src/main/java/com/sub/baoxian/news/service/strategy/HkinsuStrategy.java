package com.sub.baoxian.news.service.strategy;

import com.sub.baoxian.model.entity.InsuranceNews;
import com.sub.baoxian.news.service.NewsStrategy;
import com.sub.baoxian.news.utils.AuthUtils;
import com.sub.baoxian.news.utils.StringUtils;
import com.sub.baoxian.news.utils.TimeUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;

/**
 * 香港保险 新闻咨询
 */
@Component("hkbaoxian")
public class HkinsuStrategy implements NewsStrategy {


    private static final String BASE_URL = "http://www.hongkonginsuranceno1.com";

    @Override
    public List<InsuranceNews> getLinkNewsList() {
        Document document = null;
        try {
            document = Jsoup.connect(BASE_URL + "/Strategy/List/1").get();
            Elements elements = document.select(".information-box");
            List<InsuranceNews> insuranceNewsList = new java.util.ArrayList<>();
            for (Element element : elements) {
                String title = element.selectFirst(".title").text();
                String img = BASE_URL + element.selectFirst("img").attr("src");
                Long time = TimeUtils.getTimeStamp(element.selectFirst(".time").text().replaceAll("时间：", ""), "yyyy-MM-dd");
                String href = BASE_URL + element.selectFirst("a").attr("href");
                try {
                    Document document1 = Jsoup.connect(href).get();
                    String content = document1.selectFirst(".rich").html();
                    if (StringUtils.isNotEmpty((content))) {
                        InsuranceNews news = InsuranceNews.builder()
                                .title(title)
                                .createTime(time)
                                .content(content)
                                .link(href)
                                .img(img)
                                .tags(StringUtils.randomArray(new String[]{"市场分析", "产品动态"}) + ",香港保险")
                                .status(1)
                                .popular(0)
                                .groupHash(AuthUtils.md5Hex(href))
                                .source("hongkonginsuranceno1")
                                .build();
                        insuranceNewsList.add(news);
                    }
                } catch (Exception e) {
                }
            }
            return insuranceNewsList;
        } catch (Exception e) {

        }
        return null;
    }

}
