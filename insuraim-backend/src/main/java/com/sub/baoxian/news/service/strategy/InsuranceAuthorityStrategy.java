package com.sub.baoxian.news.service.strategy;


import com.alibaba.fastjson2.JSONObject;
import com.google.gson.JsonObject;
import com.sub.baoxian.model.entity.InsuranceNews;
import com.sub.baoxian.news.common.HttpUtils;
import com.sub.baoxian.news.model.ChannelRss;
import com.sub.baoxian.news.service.NewsStrategy;
import com.sub.baoxian.news.utils.AuthUtils;
import com.sub.baoxian.news.utils.RssHandler;
import com.sub.baoxian.news.utils.StringUtils;
import com.sub.baoxian.news.utils.TimeUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.xml.parsers.SAXParser;
import javax.xml.parsers.SAXParserFactory;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.util.*;

@Service("authority")
public class InsuranceAuthorityStrategy implements NewsStrategy {
    private Logger log = LoggerFactory.getLogger(this.getClass());
    private static final String BASE_URL = "http://www.ia.org.hk/tc/rss/rss_news_tc.xml";

    @Override
    public List<InsuranceNews> getLinkNewsList() {
        return getNewsRssList();
    }

    private List<InsuranceNews> getNewsRssList() {
        String getResult = "";
        Long currentTime = TimeUtils.getCurrentTime();
        try {
            Map<String, String> headerMap = new HashMap<>();
            headerMap.put("Accept", "application/xml, text/xml");
            getResult = HttpUtils.sendGet(BASE_URL, headerMap, "UTF-8", 10000).getData();
        } catch (Exception e) {
            log.error("获取源数据失败" + e);
            throw new RuntimeException();
        }
        try {
            InputStream inputStream = new ByteArrayInputStream(getResult.getBytes("UTF-8"));
            // 创建 SAX 解析器工厂和解析器
            SAXParserFactory factory = SAXParserFactory.newInstance();
            SAXParser parser = factory.newSAXParser();
            RssHandler handler = new RssHandler();
            parser.parse(inputStream, handler);
            // 获取解析结果
            ChannelRss channel = handler.getChannel();
            List<InsuranceNews> newsList = new ArrayList<>();
            for (ChannelRss.RssItem item : channel.getItem()) {
                String content = getLinkContent(item.getLink());
                if (StringUtils.isNotEmpty(content)) {
                    InsuranceNews news = InsuranceNews.builder()
                            .title(item.getTitle())
                            .content(content)
                            .createTime(currentTime)
                            .link(item.getLink())
                            .img(item.getImg())
                            .pubDate(TimeUtils.getTimeStamp(item.getPubDate(), "yyyy-MM-dd"))
                            .tags(StringUtils.randomArray(new String[]{"行业新闻", "政策法规", "市场分析", "保险知识"}) + ",保險業監管局")
                            .status(1)
                            .popular(0)
                            .groupHash(AuthUtils.md5Hex(item.getLink()))
                            .source("http://www.ia.org.hk")
                            .build();
                    newsList.add(news);
                }
            }
            return newsList;
        } catch (Exception e) {
            log.error("解析源数据失败" + e);
            throw new RuntimeException();
        }
    }

    /**
     * 获取链接中的内容
     * @param link
     * @return
     */
    private  String getLinkContent(String link) {
        try {
            Document document = Jsoup.connect("https://www.ia.org.hk/tc/infocenter/press_releases/20250502.html").get();
            return document.selectFirst(".cont").html();
        } catch (IOException e) {
            log.error("获取链接内容失败" + e);
            return null;
        }
    }
}
