package com.sub.baoxian.news.service.strategy;

import com.sub.baoxian.model.entity.InsuranceNews;
import com.sub.baoxian.news.service.NewsStrategy;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;

@Service
public class MsigStrategy implements NewsStrategy {

    private static final String MSIG_URL = "https://www.msig.com.hk/zh-hant/corporate-news";

    public List<InsuranceNews> getLinkNewsList() {

        return null;
    }


    public static void main(String[] args) throws IOException {
        Document document = Jsoup.connect(MSIG_URL).get();
        Elements elements = document.select(".views-row");
        for (Element e : elements) {
            System.out.println(e.selectFirst(".title").text());
            System.out.println(e.selectFirst(".field-content").text());
            System.out.println(e.select("a").attr("href"));
        }
    }
}
