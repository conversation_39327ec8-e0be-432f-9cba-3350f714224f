package com.sub.baoxian.news.service.strategy;

import com.sub.baoxian.model.entity.InsuranceNews;
import com.sub.baoxian.news.service.NewsStrategy;
import com.sub.baoxian.news.utils.AuthUtils;
import com.sub.baoxian.news.utils.StringUtils;
import com.sub.baoxian.news.utils.TimeUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 平安 保险新闻策略模式
 */
@Component("pingAan")
public class PingAnStrategy implements NewsStrategy {

    private static final String  PINGAN_URL = "https://www.pingan.com.hk";
    @Override
    public List<InsuranceNews> getLinkNewsList() {
        Long currentTime = TimeUtils.getCurrentTime();
        Document document = null;
        List<InsuranceNews> insuranceNewsList = new ArrayList<>();
        try {
            document = Jsoup.connect(PINGAN_URL + "/blogs").get();
            Elements elements = document.select(".recommend-card");
            for (Element element : elements) {
                String href = PINGAN_URL + element.selectFirst("a").attr("href");
                String img = PINGAN_URL + element.selectFirst("img").attr("src");
                String title = element.selectFirst(".recommend-content").text();
                Long pubDate = TimeUtils.getTimeStamp(element.selectFirst(".fa-clock").text().trim(), "yyyy-MM-dd");
                Document document1 = null;
                try {
                    document1 = Jsoup.connect(href).get();
                    String content = document1.selectFirst(".content-area").html().
                            replaceAll("srcset=\"", "srcset=" + PINGAN_URL);
                    content = content.replaceAll("src=\"", "src=" + PINGAN_URL);
                    if (StringUtils.isNotEmpty(content)) {
                        InsuranceNews insuranceNews = InsuranceNews.builder()
                                .title(title)
                                .content(content)
                                .link(href)
                                .img(img)
                                .createTime(currentTime)
                                .tags(StringUtils.randomArray(new String[]{"行业新闻", "产品动态", "市场分析"}) + ",平安保險")
                                .pubDate(pubDate)
                                .source(PINGAN_URL)
                                .status(1)
                                .popular(0)
                                .groupHash(AuthUtils.md5Hex(href))
                                .build();
                        insuranceNewsList.add(insuranceNews);
                    }
                } catch (Exception e) {
                }
            }
        } catch (Exception e) {
        }
        return insuranceNewsList;
    }
}
