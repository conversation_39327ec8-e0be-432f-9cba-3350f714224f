package com.sub.baoxian.news.service.strategy;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.sub.baoxian.model.entity.InsuranceNews;
import com.sub.baoxian.news.common.HttpUtils;
import com.sub.baoxian.news.service.NewsStrategy;
import com.sub.baoxian.news.utils.AuthUtils;
import com.sub.baoxian.news.utils.StringUtils;
import com.sub.baoxian.news.utils.TimeUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component("prudential")
public class PrudentialStrategy implements NewsStrategy {
    private static final String PRUDENTIAL_URL = "https://www.prudential.com.hk";
    private static final String PRUDENTIAL_API =  PRUDENTIAL_URL +
            "/system/modules/com.prudential.v2.global/action/search-result.jsp?url=/tc/all-news/index.html&keyword=&catas=&start=44&numbers=12&path=Ly5jb250ZW50L3BydS1zZWFyY2gtcmVzdWx0cy9wcnUtc2VhcmNoLXJlc3VsdHMtMDAwMDcueG1s&lang=zh_HK&_=1747469041294";

    @Override
    public List<InsuranceNews> getLinkNewsList() {
        List<InsuranceNews> insuranceNewsList = new ArrayList<>();
        String result = HttpUtils.sendGet(PRUDENTIAL_API).getData();
        if (StringUtils.isNotEmpty(result)) {
            JSONArray array = JSONObject.parseObject(result).getJSONArray("data");
            for ( Object o : array) {
                JSONObject jsonObject = (JSONObject) o;
                try {
                    String link = PRUDENTIAL_URL + jsonObject.getString("link");
                    Document document = Jsoup.connect(link).get();
                    Elements elements = document.selectFirst("#site-mainHK").selectFirst(".section-rich-text").
                            selectFirst(".container").select(".row").select(".editorial-body-copy");
                    String content = elements.html();
                    if (StringUtils.isNotEmpty(content)) {
                        InsuranceNews insuranceNews = InsuranceNews.builder()
                                .title(jsonObject.getString("title"))
                                .content(content)
                                .link(link)
                                .pubDate(TimeUtils.getTimeStamp(jsonObject.getString("date"), "yyyy年 MM月 dd日"))
                                .createTime(TimeUtils.getCurrentTime())
                                .source(PRUDENTIAL_URL)
                                .status(1)
                                .tags(StringUtils.randomArray(new String[]{"行业新闻", "市场分析"}) + ",保誠保險")
                                .popular(0)
                                .groupHash(AuthUtils.md5Hex(link))
                                .build();
                        if (StringUtils.isNotEmpty(jsonObject.getString("img"))) {
                            insuranceNews.setImg(PRUDENTIAL_URL + jsonObject.getString("img"));
                        }
                        insuranceNewsList.add(insuranceNews);
                    }
                } catch (Exception e) {

                }
            }
        }
        return insuranceNewsList;
    }
}
