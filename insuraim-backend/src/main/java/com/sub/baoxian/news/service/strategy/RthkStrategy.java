package com.sub.baoxian.news.service.strategy;


import com.sub.baoxian.model.entity.InsuranceNews;
import com.sub.baoxian.news.common.HttpUtils;
import com.sub.baoxian.news.service.NewsStrategy;
import com.sub.baoxian.news.utils.AuthUtils;
import com.sub.baoxian.news.utils.StringUtils;
import com.sub.baoxian.news.utils.TimeUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Component("rthk")
public class RthkStrategy implements NewsStrategy {

    private final static  String RTHK_URL = "https://news.rthk.hk/rthk/webpageCache/services/loadModNewsShowSp2List.php?lang=zh-TW&cat=5&newsCount=60&dayShiftMode=1&archive_date=";

    public  List<InsuranceNews> getLinkNewsList() {
        List<InsuranceNews> insuranceNewsList = new ArrayList<>();
        String result = HttpUtils.sendGet(RTHK_URL).getData();

        if (StringUtils.isNotEmpty(result)) {
            Document document = Jsoup.parse(result);
            Elements elements = document.select(".ns2-page");
            for (Element element : elements) {
                String title = element.selectFirst("a").text();
                String href = element.selectFirst("a").attr("href");
                try {
                    Document document1 = Jsoup.connect(href).get();
                    String content = document1.selectFirst(".itemBody").text();
                    Long pubDate = TimeUtils.getTimeStamp(
                            document1.selectFirst(".createddate").text().replaceAll("HKT", ""), "yyyy-MM-dd HH:mm");
                    if (StringUtils.isNotEmpty(content)) {
                        InsuranceNews insuranceNews = InsuranceNews.builder()
                                .title(title)
                                .link(href)
                                .content(content)
                                .createTime(TimeUtils.getCurrentTime())
                                .pubDate(pubDate)
                                .tags("insuraim 快讯")
                                .status(1)
                                .popular(0)
                                .type(2)
                                .groupHash(AuthUtils.md5Hex(href))
                                .source("https://news.rthk.hk")
                                .build();
                        insuranceNewsList.add(insuranceNews);
                    }
                } catch (IOException e) {

                }
            }
        }
        return insuranceNewsList;
    }
}
