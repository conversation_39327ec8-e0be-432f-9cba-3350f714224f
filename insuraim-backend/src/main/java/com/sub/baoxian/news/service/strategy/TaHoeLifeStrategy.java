package com.sub.baoxian.news.service.strategy;

import com.sub.baoxian.model.entity.InsuranceNews;
import com.sub.baoxian.news.service.NewsStrategy;
import com.sub.baoxian.news.utils.AuthUtils;
import com.sub.baoxian.news.utils.StringUtils;
import com.sub.baoxian.news.utils.TimeUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


@Component("taHoeLife")
public class TaHoeLifeStrategy implements NewsStrategy {
    private Logger log = LoggerFactory.getLogger(this.getClass());

    private static final String TA_HO_URL = "https://www.tahoelife.com.hk";


    public List<InsuranceNews> getLinkNewsList() {
        List<InsuranceNews> insuranceNewsList = new ArrayList<>();
        try {
            Document document = Jsoup.connect(TA_HO_URL).get();
            Elements elements = document.selectFirst(".engagement__A").select(".mar-t20");
            for (Element element : elements) {
                String title = element.selectFirst(".side-text").text();
                Element imgElement = element.selectFirst("img");
                String img = imgElement == null ? "" : TA_HO_URL + imgElement.attr("data-src");
                String href = TA_HO_URL + element.selectFirst("a").attr("href");
                try {
                    Document document1 = Jsoup.connect(href).get();
                    String content = document1.selectFirst("#apollo-page").html();
                    if (StringUtils.isNotEmpty(content)) {
                        content = content.replaceAll("src=\"", "src=\"" + TA_HO_URL);
                        InsuranceNews insuranceNews = InsuranceNews.builder()
                                .title(title)
                                .img(img)
                                .link(href)
                                .content(content)
                                .createTime(TimeUtils.getCurrentTime())
                                .pubDate(TimeUtils.getCurrentTime())
                                .tags(StringUtils.randomArray(new String[]{"行业新闻", "产品动态", "市场分析"}) + ",泰禾人壽")
                                .status(1)
                                .popular(0)
                                .groupHash(AuthUtils.md5Hex(href))
                                .source(TA_HO_URL)
                                .build();
                        insuranceNewsList.add(insuranceNews);
                    }

                } catch (Exception e) {
                    log.error("获取新闻失败", e);
                }

            }
        } catch (Exception e) {
            log.error("获取新闻失败", e);
        }
        return insuranceNewsList;
    }
}
