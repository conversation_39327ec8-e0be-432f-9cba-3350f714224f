package com.sub.baoxian.news.service.strategy;

import com.sub.baoxian.model.entity.InsuranceNews;
import com.sub.baoxian.news.service.NewsStrategy;
import com.sub.baoxian.news.utils.AuthUtils;
import com.sub.baoxian.news.utils.StringUtils;
import com.sub.baoxian.news.utils.TimeUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.List;

/**
 * 易通保险
 */
@Component("yiLife")
public class YiLiFeStrategy implements NewsStrategy {
    private Logger log = LoggerFactory.getLogger(this.getClass());

    private static final String YI_LIFE_URL = "https://www.yflife.com";

    public List<InsuranceNews> getLinkNewsList() {
        List<InsuranceNews> insuranceNewsList = new ArrayList<>();

        try {
            Document document = Jsoup.connect(YI_LIFE_URL + "/tc/Hong-Kong/About/Newsroom").get();
            Elements elements = document.selectFirst("#content").selectFirst(".view-content")
                    .selectFirst(".row").select(".col-12");
            for (Element element : elements) {
                String href = YI_LIFE_URL + element.selectFirst("a").attr("href");
                try {
                    Document document1 = Jsoup.connect(href).get();

                    Elements fieldItemsList = document1.selectFirst("#content")
                            .selectFirst(".content")
                            .select(".field-items");
                    StringBuilder contentBuilder = new StringBuilder();
                    for (int i = 0; i < fieldItemsList.size() - 1; i++) {
                        contentBuilder.append(fieldItemsList.get(i).html());
                    }

                    String content = contentBuilder.toString();

                    if (StringUtils.isNotEmpty(content)) {
                        String title = element.selectFirst(".title").text();
                        String img = element.selectFirst("img").attr("src");
                        Long pubDate = TimeUtils.getTimeStamp(element.selectFirst(".date-display-single").text(), "dd/MM/yyyy");
                        content = content.replaceAll("<img src=\"", "<img src=\"" + YI_LIFE_URL);
                        content = content.replaceAll("<img alt src=\"", "<img src=\"" + YI_LIFE_URL);
                        InsuranceNews insuranceNews = InsuranceNews.builder()
                                .title(title)
                                .img(img)
                                .link(href)
                                .pubDate(pubDate)
                                .content(content)
                                .createTime(TimeUtils.getCurrentTime())
                                .tags(StringUtils.randomArray(new String[]{"产品消息", "行业新闻", "产品动态", "市场分析"}) + ",易通寿险")
                                .status(1)
                                .popular(0)
                                .groupHash(AuthUtils.md5Hex(href))
                                .source(YI_LIFE_URL)
                                .build();
                        insuranceNewsList.add(insuranceNews);
                    }

                } catch (Exception e) {
                    log.error("获取易通寿险新闻内容失败, 具体URL: " + href);
                }
            }
        } catch (Exception e) {
            log.error("获取易通寿险新闻失败");
        }
        return insuranceNewsList;
    }
}
