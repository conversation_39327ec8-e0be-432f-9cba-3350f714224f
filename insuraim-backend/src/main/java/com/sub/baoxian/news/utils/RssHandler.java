package com.sub.baoxian.news.utils;


import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.sub.baoxian.news.model.ChannelRss;
import org.jsoup.Jsoup;
import org.xml.sax.Attributes;
import org.xml.sax.SAXException;
import org.xml.sax.helpers.DefaultHandler;

import java.util.ArrayList;
import java.util.List;


/**
 * xml 文件 处理, 专门用于处理rss 的xml, 流式处理法
 */
public class RssHandler extends DefaultHandler {
    private static final int MAX_ITEMS = 40;
    private int itemCount = 0;
    private StringBuilder currentText = new StringBuilder();
    private List<ChannelRss.RssItem> items = new ArrayList<>();
    private ChannelRss.RssItem item = new ChannelRss.RssItem();
    private ChannelRss channel = new ChannelRss();
    private String linkHref;
    private boolean inImage = false; // 标志变量，表示是否在处理 <image> 元素
    private String itemImg;
    private boolean imgAddedToDescription = false; // 标志变量，表示图片是否已经添加到 description 中
    private String imgNull = "<img src=\"https://www.bbc.com/bbcx/grey-placeholder.png\" class=\"sc-a34861b-0 cOpVbP hide-when-no-script\">";

    @Override
    public void startElement(String uri, String localName, String qName, Attributes attributes) {
        currentText.setLength(0);
        if (itemCount <= MAX_ITEMS) {
            if ("item".equals(qName) || "entry".equals(qName)) {
                if (StringUtils.isNotEmpty(item.getTitle())) {
                    items.add(item);
                }
                item = new ChannelRss.RssItem();
                itemImg = new String();
                imgAddedToDescription = false; // 重置标志变量
                itemCount++;
            }
            switch (qName) {
                case "media:thumbnail":
                case "media:content":
                case "enclosure":
                    itemImg = attributes.getValue("url");
                    break;
                case "link":
                    linkHref = attributes.getValue("href");
                    break;
                case "image":
                    inImage = true; // 设置标志变量
                    break;
            }
        }

    }


    @Override
    public void endElement(String uri, String localName, String qName) {
        // 处理 <channel> 和 <item> 元素的结束
        if (itemCount < MAX_ITEMS) {
            String getXmlStr = currentText.toString().trim();
            if (itemCount == 0) {
                switch (qName) {
                    case "title":
                    case "copyright":
                        channel.setTitle(getXmlStr);
                        break;
                    case "language":
                        channel.setLanguage(getXmlStr);
                        break;
                    case "link":
                        channel.setLink(getXmlStr);
                        break;
                    case "icon":
                        channel.setIcon(getXmlStr);
                        break;
                    case "image":
                        inImage = false; // 重置标志变量
                        break;
                    case "url":
                        if (inImage) {
                            channel.setIcon(getXmlStr);
                        }
                        break;
                }
            } else {
                if (StringUtils.isNotEmpty(getXmlStr) || qName.equals("link") || StringUtils.isNotEmpty(itemImg)) {
                    switch (qName) {
                        case "title":
                            item.setTitle(Jsoup.parse(getXmlStr).text());
                            break;
                        case "link":
                            if (StringUtils.isNotEmpty(linkHref)) {
                                item.setLink(linkHref);
                            } else {
                                item.setLink(getXmlStr);
                            }
                            break;
                        case "content:encoded":
                        case "content":
                        case "description":
                        case "summary":
                            getXmlStr = getXmlStr.replace(imgNull, "");
                            item.setDescription(getXmlStr);
                            if (StringUtils.isNotEmpty(itemImg) && !imgAddedToDescription) {
                                item.setDescription("<img src=\"" + itemImg + "\">" + item.getDescription());
                                imgAddedToDescription = true; // 设置标志变量，表示图片已经添加到 description 中
                            }
                            break;
                        case "pubDate":
                        case "published":
                        case "dc:date":
                            item.setPubDate(getXmlStr);
                            break;
                        case "media:thumbnail":
                        case "media:content":
                        case "enclosure":
                        case "image":
                            item.setImg(itemImg);
                            if (StringUtils.isNotEmpty(item.getDescription()) && !imgAddedToDescription) {
                                item.setDescription("<img src=\"" + itemImg + "\">" + item.getDescription());
                                imgAddedToDescription = true; // 设置标志变量，表示图片已经添加到 description 中
                            }
                            break;
                    }
                }
            }
        }
    }

    @Override
    public void characters(char ch[], int start, int length) throws SAXException {
        currentText.append(ch, start, length);
    }

    @Override
    public void endDocument() throws SAXException {
        // 在这里处理流已经全部读取完后的逻辑
        if (StringUtils.isNotEmpty(item.getTitle())) {
            items.add(item);
        }
    }


    public ChannelRss getChannel() {
        channel.setTtl(items.size());
        channel.setWebMaster("<EMAIL> (SubFeeds)");
        channel.setGenerator("SubFeeds");
        channel.setItem(items.toArray(new ChannelRss.RssItem[0]));
        return channel;
    }
}
