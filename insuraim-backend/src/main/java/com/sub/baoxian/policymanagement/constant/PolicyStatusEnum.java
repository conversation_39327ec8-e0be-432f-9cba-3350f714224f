package com.sub.baoxian.policymanagement.constant;

/**
 * 保单状态枚举类
 */
public enum PolicyStatusEnum {

    /**
     * 预约状态
     */
    APPOINTMENT,

    /**
     * 签单预约确认
     */
    SIGN_APPOINTMENT,

    /**
     * 签单
     */
    SIGN,

    /**
     * 保费缴纳指引
     */
    PAYMENT_GUIDE,

    /**
     * 保单交费记录
     */
    PAYMENT_RECORD,

    /**
     * 保单生效/保单回访及签收指引
     */
    EFFECTIVE,

    /**
     * 保单合同寄送
     */
    CONTRACT_SENT,

    /**
     * 保单签收
     */
    SIGNATURE,

    /**
     * 保单冷静期到期
     */
    QUIET_PERIOD_EXPIRED;

    /**
     * 验证状态转换是否合法
     * 保单状态必须按照枚举定义的顺序进行转换
     *
     * @param currentStatus 当前状态
     * @param targetStatus  目标状态
     * @return 是否可以转换
     */
    public static boolean isValidTransition(PolicyStatusEnum currentStatus, PolicyStatusEnum targetStatus) {
        if (currentStatus == null || targetStatus == null) {
            return false;
        }

        // 获取当前状态和目标状态在枚举中的位置
        int currentIndex = currentStatus.ordinal();
        int targetIndex = targetStatus.ordinal();

        // 只能转换到下一个状态
        return targetIndex == currentIndex + 1;
    }

    /**
     * 获取下一个可转换的状态
     *
     * @param currentStatus 当前状态
     * @return 下一个状态，如果已是最后一个状态则返回null
     */
    public static PolicyStatusEnum getNextStatus(PolicyStatusEnum currentStatus) {
        if (currentStatus == null) {
            return null;
        }

        PolicyStatusEnum[] values = PolicyStatusEnum.values();
        int currentIndex = currentStatus.ordinal();

        // 如果已是最后一个状态，返回null
        if (currentIndex >= values.length - 1) {
            return null;
        }

        return values[currentIndex + 1];
    }

    /**
     * 获取上一个状态
     */
    public static PolicyStatusEnum getPreviousStatus(PolicyStatusEnum currentStatus) {
        if (currentStatus == null) {
            return null;
        }

        PolicyStatusEnum[] values = PolicyStatusEnum.values();
        int currentIndex = currentStatus.ordinal();

        // 如果已是第一个状态，返回null
        if (currentIndex <= 0) {
            return null;
        }

        return values[currentIndex - 1];
    }
}
