package com.sub.baoxian.policymanagement.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.policymanagement.constant.PolicyStatusEnum;
import com.sub.baoxian.policymanagement.model.dto.FormInviteDTO;
import com.sub.baoxian.policymanagement.model.vo.FormInviteLinkVO;
import com.sub.baoxian.policymanagement.service.FormInviteService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/api/form/invite")
@RequiredArgsConstructor
@Tag(name = "表单邀请", description = "表单邀请相关接口")
public class FormInviteController {

    private final FormInviteService formInviteService;

    /**
     * 创建表单邀请链接
     */
    @PostMapping("/create")
    @Operation(summary = "创建表单邀请链接", description = "创建表单邀请链接")
    public Result<FormInviteLinkVO> createInviteLink(@RequestParam Long policyId) {
        return Result.success(formInviteService.createInviteLink(policyId));
    }

    /**
     * 验证邀请码有效性
     */
    @GetMapping("/validate/{inviteCode}")
    @Operation(summary = "验证邀请码有效性", description = "验证邀请码有效性")
    public Result<Boolean> validateInviteCode(@PathVariable String inviteCode) {
        formInviteService.getPolicyIdByInviteCode(inviteCode);
        return Result.success(true);
    }

    /**
     * 根据保单ID获取表单邀请码
     */
    @GetMapping("/get/{policyId}")
    @Operation(summary = "根据保单ID获取表单邀请码", description = "获取表单邀请码")
    public Result<FormInviteLinkVO> getInviteLink(@PathVariable Long policyId) {
        return Result.success(formInviteService.getInviteLink(policyId));
    }

    /**
     * 提交表单数据
     */
    @PostMapping("/submit/{inviteCode}")
    @Operation(summary = "提交表单数据", description = "提交表单数据")
    public Result<Void> submitFormData(@PathVariable String inviteCode, @RequestBody FormInviteDTO formData) {
        formInviteService.submitFormData(inviteCode, formData);
        return Result.success();
    }

    /**
     * 提交附件
     */
    @PostMapping("/submit-attachment/{inviteCode}")
    @Operation(summary = "提交附件", description = "提交附件")
    public Result<Void> submitAttachment(@PathVariable String inviteCode, @RequestParam MultipartFile[] files,
            @RequestParam PolicyStatusEnum status) {
        for (MultipartFile file : files) {
            if (file != null && !file.isEmpty()) {
                formInviteService.submitAttachment(inviteCode, file, status);
            }
        }
        return Result.success();
    }

}