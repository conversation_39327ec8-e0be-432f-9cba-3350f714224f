package com.sub.baoxian.policymanagement.controller;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.policymanagement.model.dto.CreateAppointDTO;
import com.sub.baoxian.policymanagement.model.vo.CreateAppointVO;
import com.sub.baoxian.policymanagement.service.PolicyAppointService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@RequestMapping("/api/appoint")
@RestController
@RequiredArgsConstructor
@Tag(name = "预约管理", description = "保单相关操作接口")
public class PolicyAppointController {

    private final PolicyAppointService appointService;

    /*
     * 创建预约
     */
    @PostMapping("/create")
    @Operation(summary = "创建预约", description = "创建预约")
    public Result<CreateAppointVO> createAppoint(@RequestBody CreateAppointDTO createAppointDTO) {
        CreateAppointVO createAppointVO = appointService.createAppoint(createAppointDTO);
        return Result.success(createAppointVO);
    }

}
