package com.sub.baoxian.policymanagement.controller;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.policymanagement.constant.PolicyStatusEnum;
import com.sub.baoxian.policymanagement.model.dto.AddBeneficiaryInfoDTO;
import com.sub.baoxian.policymanagement.model.dto.UpdateBeneficiaryInfoDTO;
import com.sub.baoxian.policymanagement.model.dto.UpdateInsuredInfoDTO;
import com.sub.baoxian.policymanagement.model.dto.UpdatePolicyBasicInfoDTO;
import com.sub.baoxian.policymanagement.model.dto.UpdatePolicyStatusDTO;
import com.sub.baoxian.policymanagement.model.dto.UpdatePolicyholderInfoDTO;
import com.sub.baoxian.policymanagement.model.dto.withdrawStatusChangeDTO;
import com.sub.baoxian.policymanagement.model.bo.PolicyStatusHistoryBO;
import com.sub.baoxian.policymanagement.service.PolicyService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Tag(name = "保单管理", description = "保单相关操作接口")
@RestController
@RequestMapping("/api/policy")
@RequiredArgsConstructor
public class PolicyController {

    private final PolicyService policyService;

    @Operation(summary = "获取保单状态历史", description = "根据保单ID获取保单的状态变更历史记录，包含每个状态的附件信息")
    @Parameters({
            @Parameter(name = "policyId", description = "保单ID", required = true, example = "1")
    })
    @GetMapping("/history-status")
    public Result<List<PolicyStatusHistoryBO>> getHistoryStatus(@RequestParam Long policyId) {
        return Result.success(policyService.getHistoryStatus(policyId));
    }

    @Operation(summary = "修改保单状态", description = "更新保单的状态，并记录状态变更历史")
    @PostMapping("/update-status")
    public Result<Void> updatePolicyStatus(@RequestBody UpdatePolicyStatusDTO updatePolicyStatusDTO) {
        policyService.updatePolicyStatus(updatePolicyStatusDTO);
        return Result.success();
    }

    @Operation(summary = "上传保单附件", description = "为指定保单状态上传相关附件文件")
    @Parameters({
            @Parameter(name = "file", description = "要上传的文件", required = true),
            @Parameter(name = "policyId", description = "保单ID", required = true, example = "1"),
            @Parameter(name = "status", description = "保单状态", required = true)
    })
    @PostMapping("/upload-attachment")
    public Result<Void> uploadAttachment(@RequestParam MultipartFile file,
            @RequestParam Long policyId, @RequestParam PolicyStatusEnum status) {
        policyService.uploadAttachment(file, policyId, status);
        return Result.success();
    }

    @Operation(summary = "撤回保单状态变更", description = "撤回最近一次的保单状态变更操作")
    @PostMapping("/withdraw-status-change")
    public Result<Void> withdrawStatusChange(@RequestBody withdrawStatusChangeDTO withdrawStatusChangeDTO) {
        policyService.withdrawStatusChange(withdrawStatusChangeDTO.getPolicyId());
        return Result.success();
    }

    /**
     * 修改保单基本信息
     */
    @Operation(summary = "修改保单基本信息", description = "修改保单的基本信息，包括投保人、被保人、受益人等")
    @PostMapping("/update-basic-info")
    public Result<Void> updateBasicInfo(@RequestBody UpdatePolicyBasicInfoDTO updatePolicyBasicInfoDTO) {
        policyService.updatePolicyBasicInfo(updatePolicyBasicInfoDTO);
        return Result.success();
    }

    /**
     * 修改保单投保人信息
     */
    @Operation(summary = "修改保单投保人信息", description = "修改保单的投保人信息，包括投保人、被保人、受益人等")
    @PostMapping("/update-policyholder-info")
    public Result<Void> updatePolicyholderInfo(@RequestBody UpdatePolicyholderInfoDTO updatePolicyholderInfoDTO) {
        policyService.updatePolicyholderInfo(updatePolicyholderInfoDTO);
        return Result.success();
    }

    /**
     * 修改受保人信息
     */
    @Operation(summary = "修改受保人信息", description = "修改保单的受保人信息，包括受保人、被保人、受益人等")
    @PostMapping("/update-insured-info")
    public Result<Void> updateInsuredInfo(@RequestBody UpdateInsuredInfoDTO updateInsuredInfoDTO) {
        policyService.updateInsuredInfo(updateInsuredInfoDTO);
        return Result.success();
    }

    /**
     * 添加受益人信息
     */
    @Operation(summary = "添加受益人信息", description = "为指定保单添加新的受益人信息")
    @PostMapping("/add-beneficiary-info")
    public Result<Void> addBeneficiaryInfo(@RequestBody AddBeneficiaryInfoDTO addBeneficiaryInfoDTO) {
        policyService.addBeneficiaryInfo(addBeneficiaryInfoDTO);
        return Result.success();
    }

    /**
     * 修改受益人信息
     */
    @Operation(summary = "修改受益人信息", description = "修改保单的受益人信息，包括受益人、被保人、受益人等")
    @PostMapping("/update-beneficiary-info")
    public Result<Void> updateBeneficiaryInfo(@RequestBody UpdateBeneficiaryInfoDTO updateBeneficiaryInfoDTO) {
        policyService.updateBeneficiaryInfo(updateBeneficiaryInfoDTO);
        return Result.success();
    }

    /**
     * 删除受益人信息
     */
    @Operation(summary = "删除受益人信息", description = "删除保单的受益人信息，支持直接删除数据库记录")
    @Parameters({
            @Parameter(name = "beneficiaryId", description = "受益人ID", required = true, example = "1")
    })
    @PostMapping("/delete-beneficiary-info")
    public Result<Void> deleteBeneficiaryInfo(@RequestParam Long beneficiaryId) {
        policyService.deleteBeneficiaryInfo(beneficiaryId);
        return Result.success();
    }

}
