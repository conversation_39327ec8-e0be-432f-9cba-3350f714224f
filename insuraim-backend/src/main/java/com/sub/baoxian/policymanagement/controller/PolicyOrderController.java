package com.sub.baoxian.policymanagement.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestParam;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.common.response.Result;
import com.sub.baoxian.policymanagement.constant.OrderStatusEnum;
import com.sub.baoxian.policymanagement.constant.PolicyStatusEnum;
import com.sub.baoxian.policymanagement.model.vo.GetPageListVO;
import com.sub.baoxian.policymanagement.model.vo.getDetailByOrderIdVO;
import com.sub.baoxian.policymanagement.service.PolicyOrderService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@RequestMapping("/api/policy-order")
@RestController
@RequiredArgsConstructor
@Tag(name = "保单订单管理", description = "保单相关操作接口")
public class PolicyOrderController {

    private final PolicyOrderService policyOrderService;

    /**
     * 分页获取订单列表
     */
    @GetMapping("/page")
    @Operation(summary = "分页获取订单列表", description = "分页获取订单列表")
    public Result<Page<GetPageListVO>> getPolicyOrdersPage(
            @RequestParam(required = false) String search,
            @RequestParam(required = false) PolicyStatusEnum policyStatus,
            @RequestParam(required = false) OrderStatusEnum status,
            @RequestParam(required = false) String createdAt,
            @RequestParam(required = false) String updatedAt,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        return Result.success(
                policyOrderService.getPolicyOrdersPage(search, policyStatus,
                        status, createdAt, updatedAt, page, pageSize));
    }

    /**
     * 根据订单ID查询所有保单信息
     */
    @GetMapping("/detail")
    @Operation(summary = "根据订单ID查询所有保单信息", description = "根据订单ID查询所有保单信息")
    public Result<getDetailByOrderIdVO> getPolicyOrderById(@RequestParam Long orderId) {
        return Result.success(policyOrderService.getDetailByOrderId(orderId));
    }

    /**
     * 删除订单
     */
    @PostMapping("/delete")
    @Operation(summary = "删除订单", description = "删除订单")
    public Result<Void> deletePolicyOrder(@RequestParam Long policyId) {
        policyOrderService.deletePolicyOrder(policyId);
        return Result.success();
    }

}
