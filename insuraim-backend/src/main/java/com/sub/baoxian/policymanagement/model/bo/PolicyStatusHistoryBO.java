package com.sub.baoxian.policymanagement.model.bo;

import com.sub.baoxian.policymanagement.constant.PolicyRecordStatusEnum;
import com.sub.baoxian.policymanagement.constant.PolicyStatusEnum;
import com.sub.baoxian.policymanagement.model.entity.PolicyStatusAttachment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 保单状态历史业务对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PolicyStatusHistoryBO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 保单ID
     */
    private Long policyId;

    /**
     * 状态码
     */
    private PolicyStatusEnum statusCode;

    /**
     * 保单记录状态（已完成/待完成/pending）
     */
    private PolicyRecordStatusEnum recordStatus;

    /**
     * 变更时间(时间戳)
     */
    private Long updatedAt;

    /**
     * 操作人
     */
    private Long updateBy;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间(时间戳)
     */
    private Long createdAt;

    /**
     * 附件列表
     */
    private List<PolicyStatusAttachment> attachments;
}
