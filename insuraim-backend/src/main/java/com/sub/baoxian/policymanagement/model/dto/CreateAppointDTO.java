package com.sub.baoxian.policymanagement.model.dto;

import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

@Data
public class CreateAppointDTO {
    private OverviewDTO overview;
    private PolicyholderDTO policyholder;
    private InsuredDTO insured;
    private List<BeneficiaryDTO> beneficiary;
    private ProposalDTO proposal;

    @Data
    public static class OverviewDTO {
        private String customerName; // 客户姓名
        private String phone; // 顾问联络电话
        private String email; // 顾问邮箱
        private String team; // 团队/主管
        private String referrer; // 介绍人
        private String company; // 保险公司
        private String region; // 地区
        private Long appointmentTime; // 预约时间，时间戳格式
        private String product; // 产品名称
        private String paymentTerm; // 缴费期限
        private String paymentMethod; // 支付方式
        private String remark; // 备注
        private String currencyType; // 保单货币
        private BigDecimal insuredAmount; // 保额
        private BigDecimal annualPremium; // 年供保费
        private String firstYearPaymentMethod; // 首年保费缴付方式
        private String renewalPaymentMethod; // 续期保单缴付方式
    }

    @Data
    public static class PolicyholderDTO {
        private String nameCn; // 中文姓名
        private String nameEn; // 拼音名
        private Long birthDate; // 出生日期，时间戳格式
        private Integer gender; // 性别，1-男，0-女
        private String idCardNo; // 身份证号码
        private String travelPermitNo; // 通行证号码
        private String nationality; // 国籍
        private String birthPlace; // 出生地
        private String maritalStatus; // 婚姻状况
        private String educationLevel; // 教育程度
        private BigDecimal height; // 身高，单位厘米
        private BigDecimal weight; // 体重，单位公斤
        private String mobile; // 手机号码
        private String homePhone; // 住宅电话
        private String email; // 邮箱
        private String idCardAddress; // 身份证地址/邮编
        private String residentialAddress; // 居住地址
        private String mailingAddress; // 通讯地址
        private Integer isSmoker; // 是否吸烟，1-是，0-否
        private String companyNameCn; // 公司中文名称
        private String companyNameEn; // 公司英文名称
        private String companyAddress; // 公司地址
        private String companyIndustry; // 公司行业
        private String position; // 职位
        private BigDecimal annualIncome; // 年薪
    }

    @Data
    public static class InsuredDTO {
        private String relationshipWithPolicyholder; // 与投保人关系
        private String nameCn; // 中文姓名
        private String nameEn; // 拼音名
        private Long birthDate; // 出生日期，时间戳格式
        private Integer gender; // 性别，1-男，0-女
        private String idCardNo; // 身份证号码
        private String travelPermitNo; // 通行证号码
        private String mobile; // 手机号码
        private String homePhone; // 住宅电话
        private String email; // 邮箱
        private String nationality; // 国籍
        private String maritalStatus; // 婚姻状况
        private String educationLevel; // 教育程度
        private BigDecimal height; // 身高，单位厘米
        private BigDecimal weight; // 体重，单位公斤
        private String idCardAddress; // 身份证地址/邮编
        private Integer isSmoker; // 是否吸烟，1-是，0-否
    }

    @Data
    public static class BeneficiaryDTO {
        private String name; // 受益人姓名
        private Integer gender; // 性别，1-男，0-女
        private String relationship; // 与受保人关系
        private String idCardNo; // 身份证号码
        private BigDecimal benefitPercentage; // 受益比例(%)
        private Integer isTrustee; // 是否为信托人，1-是，0-否
    }

    @Data
    public static class ProposalDTO {
        private Long selectedProposalId; // 已选择的计划书ID
    }
}
