package com.sub.baoxian.policymanagement.model.dto;

import java.math.BigDecimal;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "更新受益人信息请求DTO")
public class UpdateBeneficiaryInfoDTO {

    @Schema(description = "受益人ID", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "保单ID", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long policyId;

    @Schema(description = "受益人姓名", example = "王五")
    private String name;

    @Schema(description = "性别", example = "1", allowableValues = { "0", "1" })
    private Integer gender;

    @Schema(description = "与受保人关系", example = "配偶")
    private String relationship;

    @Schema(description = "身份证号码", example = "******************")
    private String idCardNo;

    @Schema(description = "受益比例", example = "100.00")
    private BigDecimal benefitPercentage;

    @Schema(description = "是否为信托人", example = "0", allowableValues = { "0", "1" })
    private Integer isTrustee;
}
