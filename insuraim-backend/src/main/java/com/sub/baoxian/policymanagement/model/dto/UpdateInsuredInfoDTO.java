package com.sub.baoxian.policymanagement.model.dto;

import java.math.BigDecimal;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "更新受保人信息请求DTO")
public class UpdateInsuredInfoDTO {

    @Schema(description = "保单ID", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long policyId;

    @Schema(description = "与投保人关系", example = "本人")
    private String relationshipWithPolicyholder;

    @Schema(description = "中文姓名", example = "李四")
    private String nameCn;

    @Schema(description = "英文姓名/拼音", example = "Li Si")
    private String nameEn;

    @Schema(description = "出生日期", example = "725846400000")
    private Long birthDate;

    @Schema(description = "性别", example = "0", allowableValues = { "0", "1" })
    private Integer gender;

    @Schema(description = "身份证号码", example = "******************")
    private String idCardNo;

    @Schema(description = "通行证号码", example = "H87654321")
    private String travelPermitNo;

    @Schema(description = "手机号码", example = "13900139000")
    private String mobile;

    @Schema(description = "住宅电话", example = "0755-87654321")
    private String homePhone;

    @Schema(description = "邮箱地址", example = "<EMAIL>")
    private String email;

    @Schema(description = "国籍", example = "中国")
    private String nationality;

    @Schema(description = "婚姻状况", example = "未婚")
    private String maritalStatus;

    @Schema(description = "教育程度", example = "硕士")
    private String educationLevel;

    @Schema(description = "身高", example = "168.0")
    private BigDecimal height;

    @Schema(description = "体重", example = "55.5")
    private BigDecimal weight;

    @Schema(description = "身份证地址", example = "广东省深圳市宝安区新安街道")
    private String idCardAddress;

    @Schema(description = "是否吸烟", example = "0", allowableValues = { "0", "1" })
    private Integer isSmoker;
}
