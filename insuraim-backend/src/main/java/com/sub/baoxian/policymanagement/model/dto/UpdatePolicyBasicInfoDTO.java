package com.sub.baoxian.policymanagement.model.dto;

import java.math.BigDecimal;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "更新保单基本信息请求DTO")
public class UpdatePolicyBasicInfoDTO {

    @Schema(description = "保单ID", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long policyId;

    @Schema(description = "客户姓名", example = "张三")
    private String customerName;

    @Schema(description = "顾问联络电话", example = "13800138000")
    private String phone;

    @Schema(description = "顾问邮箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "团队/主管", example = "销售一组")
    private String team;

    @Schema(description = "介绍人", example = "李四")
    private String referrer;

    @Schema(description = "保险公司", example = "中国平安")
    private String company;

    @Schema(description = "地区", example = "深圳")
    private String region;

    @Schema(description = "预约时间", example = "1703664000000")
    private Long appointmentTime;

    @Schema(description = "产品名称", example = "平安福终身寿险")
    private String product;

    @Schema(description = "缴费期限", example = "20年")
    private String paymentTerm;

    @Schema(description = "供款方式", example = "年缴")
    private String paymentMethod;

    @Schema(description = "备注", example = "客户特殊要求")
    private String remark;

    @Schema(description = "保单货币", example = "CNY")
    private String currencyType;

    @Schema(description = "保额", example = "500000.00")
    private BigDecimal insuredAmount;

    @Schema(description = "年供保费", example = "12000.00")
    private BigDecimal annualPremium;

    @Schema(description = "供款年期", example = "20")
    private Integer paymentYears;

    @Schema(description = "首年保费缴付方式", example = "银行转账")
    private String firstPremiumPaymentMethod;

    @Schema(description = "续期保单缴付方式", example = "自动扣款")
    private String renewalPaymentMethod;
}
