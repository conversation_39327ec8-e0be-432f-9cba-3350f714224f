package com.sub.baoxian.policymanagement.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "更新保单状态请求DTO")
public class UpdatePolicyStatusDTO {

    @Schema(description = "保单ID", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long policyId;

    @Schema(description = "备注信息", example = "状态更新备注")
    private String remark;

}
