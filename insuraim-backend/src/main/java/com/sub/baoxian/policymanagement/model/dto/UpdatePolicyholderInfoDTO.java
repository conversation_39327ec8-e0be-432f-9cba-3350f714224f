package com.sub.baoxian.policymanagement.model.dto;

import java.math.BigDecimal;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "更新保单持有人信息请求DTO")
public class UpdatePolicyholderInfoDTO {

    @Schema(description = "保单ID", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long policyId;

    @Schema(description = "中文姓名", example = "张三")
    private String nameCn;

    @Schema(description = "英文姓名/拼音", example = "Zhang San")
    private String nameEn;

    @Schema(description = "出生日期", example = "694224000000")
    private Long birthDate;

    @Schema(description = "性别", example = "1", allowableValues = { "0", "1" })
    private Integer gender;

    @Schema(description = "身份证号码", example = "******************")
    private String idCardNo;

    @Schema(description = "通行证号码", example = "H12345678")
    private String travelPermitNo;

    @Schema(description = "国籍", example = "中国")
    private String nationality;

    @Schema(description = "出生地", example = "广东省深圳市")
    private String birthPlace;

    @Schema(description = "婚姻状况", example = "已婚")
    private String maritalStatus;

    @Schema(description = "教育程度", example = "本科")
    private String educationLevel;

    @Schema(description = "身高", example = "175.5")
    private BigDecimal height;

    @Schema(description = "体重", example = "70.0")
    private BigDecimal weight;

    @Schema(description = "手机号码", example = "13800138000")
    private String mobile;

    @Schema(description = "住宅电话", example = "0755-12345678")
    private String homePhone;

    @Schema(description = "邮箱地址", example = "<EMAIL>")
    private String email;

    @Schema(description = "身份证地址", example = "广东省深圳市南山区科技园")
    private String idCardAddress;

    @Schema(description = "居住地址", example = "广东省深圳市福田区中心区")
    private String residentialAddress;

    @Schema(description = "通讯地址", example = "广东省深圳市罗湖区东门")
    private String mailingAddress;

    @Schema(description = "是否吸烟", example = "0", allowableValues = { "0", "1" })
    private Integer isSmoker;

    @Schema(description = "公司中文名称", example = "深圳科技有限公司")
    private String companyNameCn;

    @Schema(description = "公司英文名称", example = "Shenzhen Technology Co., Ltd.")
    private String companyNameEn;

    @Schema(description = "公司地址", example = "深圳市南山区科技园南区")
    private String companyAddress;

    @Schema(description = "公司行业", example = "信息技术")
    private String companyIndustry;

    @Schema(description = "职位", example = "软件工程师")
    private String position;

    @Schema(description = "年收入", example = "300000.00")
    private BigDecimal annualIncome;
}
