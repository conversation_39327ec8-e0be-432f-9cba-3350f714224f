package com.sub.baoxian.policymanagement.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 受益人/信托人信息表实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("ins_policy_beneficiary_info")
public class BeneficiaryInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 关联保单ID
     */
    private Long policyId;

    /**
     * 受益人姓名
     */
    private String name;

    /**
     * 性别(0-女,1-男)
     */
    private Integer gender;

    /**
     * 与受保人关系
     */
    private String relationship;

    /**
     * 身份证号码
     */
    private String idCardNo;

    /**
     * 受益比例(%)
     */
    private BigDecimal benefitPercentage;

    /**
     * 是否为信托人(0-否,1-是)
     */
    private Integer isTrustee;

    /**
     * 创建时间(时间戳)
     */
    private Long createdAt;

    /**
     * 更新时间(时间戳)
     */
    private Long updatedAt;
}
