package com.sub.baoxian.policymanagement.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 受保人信息表实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("ins_policy_insured_info")
public class InsuredInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 关联保单ID
     */
    private Long policyId;

    /**
     * 中文姓名
     */
    private String nameCn;

    /**
     * 中文（护照）拼音
     */
    private String nameEn;

    /**
     * 出生日期(时间戳)
     */
    private Long birthDate;

    /**
     * 性别(0-女,1-男)
     */
    private Integer gender;

    /**
     * 身份证号码
     */
    private String idCardNo;

    /**
     * 通行证号码
     */
    private String travelPermitNo;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 住宅电话
     */
    private String homePhone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 国籍
     */
    private String nationality;

    /**
     * 婚姻状况
     */
    private String maritalStatus;

    /**
     * 教育程度
     */
    private String educationLevel;

    /**
     * 身高(厘米)
     */
    private BigDecimal height;

    /**
     * 体重(公斤)
     */
    private BigDecimal weight;

    /**
     * 身份证地址
     */
    private String idCardAddress;

    /**
     * 是否吸烟(0-否,1-是)
     */
    private Integer isSmoker;

    /**
     * 创建时间(时间戳)
     */
    private Long createdAt;

    /**
     * 更新时间(时间戳)
     */
    private Long updatedAt;
}
