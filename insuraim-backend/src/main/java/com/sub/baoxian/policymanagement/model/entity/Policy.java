package com.sub.baoxian.policymanagement.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.sub.baoxian.policymanagement.constant.PolicyStatusEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 保单表实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("ins_policy")
public class Policy implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 关联订单ID
     */
    private Long orderId;

    /**
     * 保单号
     */
    private String policyNo;

    /**
     * 保单状态
     */
    private PolicyStatusEnum policyStatus;

    /**
     * 保险公司
     */
    private String company;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 货币类型
     */
    private String currency;

    /**
     * 保额
     */
    private BigDecimal coverageAmount;

    /**
     * 年供保费
     */
    private BigDecimal annualPremium;

    /**
     * 供款方式
     */
    private String paymentMethod;

    /**
     * 供款年份
     */
    private String paymentTerm;

    /**
     * 首年保费缴费日期(时间戳)
     */
    private Long firstPremiumDueDate;

    /**
     * 首年缴费金额
     */
    private BigDecimal firstPremiumAmount;

    /**
     * 首年缴费方式
     */
    private String firstPremiumPaymentMethod;

    /**
     * 生效日期(时间戳)
     */
    private Long effectiveDate;

    /**
     * 冷静期开始时间(时间戳)
     */
    private Long coolingPeriodStart;

    /**
     * 冷静期结束时间(时间戳)
     */
    private Long coolingPeriodEnd;

    /**
     * 下次续保日期(时间戳)
     */
    private Long nextRenewalDate;

    /**
     * 下次续保金额
     */
    private BigDecimal nextRenewalAmount;

    /**
     * 续期保单缴付方式
     */
    private String renewalPaymentMethod;

    /**
     * 创建时间(时间戳)
     */
    private Long createdAt;

    /**
     * 更新时间(时间戳)
     */
    private Long updatedAt;
}
