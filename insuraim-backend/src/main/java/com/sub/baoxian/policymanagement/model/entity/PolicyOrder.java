package com.sub.baoxian.policymanagement.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.sub.baoxian.policymanagement.constant.OrderStatusEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 保单订单表实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("ins_policy_order")
public class PolicyOrder implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 系统订单号(录入年月日+00XX)
     */
    private String orderNo;

    /**
     * 客户姓名
     */
    private String customerName;

    /**
     * 投保人中文名
     */
    private String policyholderNameCn;

    /**
     * 投保人英文名(大写)
     */
    private String policyholderNameEn;

    /**
     * 被保险人中文名
     */
    private String insuredNameCn;

    /**
     * 被保险人英文名(大写)
     */
    private String insuredNameEn;

    /**
     * 地区
     */
    private String region;

    /**
     * 保险公司
     */
    private String company;

    /**
     * 签约产品全称
     */
    private String product;

    /**
     * 顾问联络电话
     */
    private String phone;

    /**
     * 顾问邮箱
     */
    private String email;

    /**
     * 团队/主管
     */
    private String team;

    /**
     * 介绍人
     */
    private String referrer;

    /**
     * 签约时间(时间戳)
     */
    private Long signTime;

    /**
     * 预约时间
     */
    private Long appointmentTime;

    /**
     * 缴费期限
     */
    private String paymentTerm;

    /**
     * 货币类型
     */
    private String currency;

    /**
     * 年缴保费
     */
    private BigDecimal annualPremium;

    /**
     * 下次续保日期(时间戳)
     */
    private Long nextRenewalDate;

    /**
     * 订单状态
     */
    private OrderStatusEnum orderStatus;

    /**
     * 状态更新时间(时间戳)
     */
    private Long statusUpdatedAt;

    /**
     * 关联用户ID
     */
    private Long userId;

    /**
     * 订单录入人员
     */
    private Long createdId;

    /**
     * 创建时间(时间戳)
     */
    private Long createdAt;

    /**
     * 更新时间(时间戳)
     */
    private Long updatedAt;

    /**
     * 备注
     */
    private String remark;
}
