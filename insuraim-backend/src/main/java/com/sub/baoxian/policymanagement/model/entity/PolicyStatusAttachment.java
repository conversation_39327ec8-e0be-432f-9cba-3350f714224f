package com.sub.baoxian.policymanagement.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 保单状态附件表实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("ins_policy_status_attachment")
public class PolicyStatusAttachment implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 状态历史ID
     */
    private Long statusHistoryId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * OSS地址
     */
    private String ossUrl;

    /**
     * 文件类型(jpg/pdf/doc等)
     */
    private String fileType;

    /**
     * 文件大小(字节)
     */
    private Long fileSize;

    /**
     * 元数据 (JSON格式)
     */
    private String metadata;

    /**
     * 描述
     */
    private String description;

    /**
     * 创建时间(时间戳)
     */
    private Long createdAt;

    /**
     * 更新时间(时间戳)
     */
    private Long updatedAt;
}
