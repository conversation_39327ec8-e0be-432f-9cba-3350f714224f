package com.sub.baoxian.policymanagement.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "表单邀请链接VO")
public class FormInviteLinkVO {
    
    @Schema(description = "邀请码", example = "a1b2c3d4")
    private String inviteCode;
    
    @Schema(description = "邀请链接", example = "/form/invite/a1b2c3d4")
    private String inviteLink;
    
    @Schema(description = "过期时间", example = "1628841600000")
    private Long expireTime;
}