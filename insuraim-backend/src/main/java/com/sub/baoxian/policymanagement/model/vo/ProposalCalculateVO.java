package com.sub.baoxian.policymanagement.model.vo;

import java.util.List;

import lombok.Data;

@Data
public class ProposalCalculateVO {

    private String productName;
    private Integer customerAge;
    private String customerGender;
    private Integer premiumAmount;
    private Integer paymentYear;
    private String currency;
    private List<ProposalCalculateResultData> data;

    @Data
    public static class ProposalCalculateResultData {
        private Integer policyYear;
        private Integer age;
        private Long totalPremiumPaid;
        private Long surrenderGuaranteed;
        private Long terminalBonus;
        private Long totalAmount;
    }

}
