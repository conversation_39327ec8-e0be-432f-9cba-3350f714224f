package com.sub.baoxian.policymanagement.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.sub.baoxian.policymanagement.model.bo.PolicyStatusHistoryBO;
import com.sub.baoxian.policymanagement.model.entity.*;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class getDetailByOrderIdVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private PolicyOrder policyOrder;
    private Policy policyInfo;
    private PolicyholderInfo policyholderInfo;
    private InsuredInfo insuredInfo;
    private List<BeneficiaryInfo> beneficiaryInfos;
    private List<PolicyStatusHistoryBO> policyStatusHistories;
    private List<PolicyStatusAttachment> policyStatusAttachments;

}
