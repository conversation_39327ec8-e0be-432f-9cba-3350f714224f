package com.sub.baoxian.policymanagement.service;

import java.util.Set;

import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.sub.baoxian.common.exception.BizException;
import com.sub.baoxian.policymanagement.constant.PolicyStatusEnum;
import com.sub.baoxian.policymanagement.model.dto.FormInviteDTO;
import com.sub.baoxian.policymanagement.model.vo.FormInviteLinkVO;
import com.sub.baoxian.util.RedisUtil;

import cn.hutool.core.util.IdUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class FormInviteService {

    private final PolicyService policyService;

    private static final String REDIS_KEY_PREFIX = "form:invite:";
    private static final int DEFAULT_EXPIRE_HOURS = 24;

    /**
     * 创建表单邀请链接
     */
    public FormInviteLinkVO createInviteLink(Long policyId) {
        // 检查是否已存在该保单的邀请链接
        FormInviteLinkVO existingLink = getInviteLink(policyId);
        if (existingLink != null) {
            return existingLink;
        }

        // 生成唯一邀请码
        String inviteCode = IdUtil.fastSimpleUUID();

        // 存储到Redis，只存储保单ID
        String redisKey = REDIS_KEY_PREFIX + inviteCode;
        RedisUtil.set(redisKey, policyId, DEFAULT_EXPIRE_HOURS * 3600); // 转换为秒

        // 计算过期时间
        long expireTime = System.currentTimeMillis() + DEFAULT_EXPIRE_HOURS * 3600 * 1000;

        // 构建返回结果
        FormInviteLinkVO result = new FormInviteLinkVO();
        result.setInviteCode(inviteCode);
        result.setInviteLink("/form/invite/" + inviteCode);
        result.setExpireTime(expireTime);

        return result;
    }

    /**
     * 根据保单ID获取邀请链接信息
     */
    public FormInviteLinkVO getInviteLink(Long policyId) {
        // 获取所有邀请码相关的key
        Set<String> keys = RedisUtil.keys(REDIS_KEY_PREFIX + "*");

        if (keys == null || keys.isEmpty()) {
            return null;
        }

        // 遍历所有key，查找匹配的保单ID
        for (String key : keys) {
            Object storedPolicyIdObj = RedisUtil.get(key);

            if (storedPolicyIdObj != null) {
                Long storedPolicyId = null;

                // 处理可能的类型转换
                if (storedPolicyIdObj instanceof Integer) {
                    storedPolicyId = ((Integer) storedPolicyIdObj).longValue();
                } else if (storedPolicyIdObj instanceof Long) {
                    storedPolicyId = (Long) storedPolicyIdObj;
                } else if (storedPolicyIdObj instanceof String) {
                    try {
                        storedPolicyId = Long.parseLong((String) storedPolicyIdObj);
                    } catch (NumberFormatException e) {
                        continue;
                    }
                }

                // 如果找到匹配的保单ID
                if (policyId.equals(storedPolicyId)) {
                    // 提取邀请码（去掉前缀）
                    String inviteCode = key.substring(REDIS_KEY_PREFIX.length());

                    // 获取过期时间
                    long expireSeconds = RedisUtil.getExpire(key);
                    long expireTime = expireSeconds > 0 ? System.currentTimeMillis() + expireSeconds * 1000 : 0;

                    // 构建返回结果
                    FormInviteLinkVO result = new FormInviteLinkVO();
                    result.setInviteCode(inviteCode);
                    result.setInviteLink("/form/invite/" + inviteCode);
                    result.setExpireTime(expireTime);

                    return result;
                }
            }
        }

        return null;
    }

    /**
     * 获取邀请关联的保单ID
     */
    public Long getPolicyIdByInviteCode(String inviteCode) {
        String redisKey = REDIS_KEY_PREFIX + inviteCode;
        Object policyIdObj = RedisUtil.get(redisKey);

        if (policyIdObj == null) {
            throw new BizException("邀请链接不存在或已过期");
        }

        // 处理可能的类型转换
        if (policyIdObj instanceof Integer) {
            return ((Integer) policyIdObj).longValue();
        } else if (policyIdObj instanceof Long) {
            return (Long) policyIdObj;
        } else if (policyIdObj instanceof String) {
            return Long.parseLong((String) policyIdObj);
        }

        throw new BizException("保单ID格式错误");
    }

    /**
     * 提交表单数据
     */
    public void submitFormData(String inviteCode, FormInviteDTO formData) {
        // 获取邀请关联的保单ID
        Long policyId = getPolicyIdByInviteCode(inviteCode);

        // 创建或更新投保人信息
        if (formData.getPolicyholderInfo() != null) {
            formData.getPolicyholderInfo().setPolicyId(policyId);
            policyService.updatePolicyholderInfo(formData.getPolicyholderInfo(), true); // 传递isFormInvite=true
        }

        // 创建或更新被保险人信息
        if (formData.getInsuredInfo() != null) {
            formData.getInsuredInfo().setPolicyId(policyId);
            policyService.updateInsuredInfo(formData.getInsuredInfo(), formData.getPolicyholderInfo(), true); // 传递isFormInvite=true
        }

        // 创建或更新受益人信息
        if (formData.getBeneficiaryInfo() != null && !formData.getBeneficiaryInfo().isEmpty()) {
            for (var beneficiary : formData.getBeneficiaryInfo()) {
                beneficiary.setPolicyId(policyId);
                policyService.updateBeneficiaryInfo(beneficiary, true);
            }
        }

        // 删除Redis中的临时数据
        String redisKey = REDIS_KEY_PREFIX + inviteCode;
        RedisUtil.del(redisKey);
    }

    /**
     * 提交附件
     */
    public void submitAttachment(String inviteCode, MultipartFile file, PolicyStatusEnum status) {
        // 获取邀请关联的保单ID
        Long policyId = getPolicyIdByInviteCode(inviteCode);
        // 上传附件
        policyService.uploadAttachment(file, policyId, status, true);
    }
}