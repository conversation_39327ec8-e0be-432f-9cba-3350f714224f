package com.sub.baoxian.policymanagement.service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.sub.baoxian.common.exception.BizException;
import com.sub.baoxian.mapper.PolicyMapper;
import com.sub.baoxian.model.dto.CalendarDTO;
import com.sub.baoxian.model.entity.ProposalRecord;
import com.sub.baoxian.policymanagement.constant.OrderStatusEnum;
import com.sub.baoxian.policymanagement.constant.PolicyRecordStatusEnum;
import com.sub.baoxian.policymanagement.constant.PolicyStatusEnum;
import com.sub.baoxian.policymanagement.model.dto.CreateAppointDTO;
import com.sub.baoxian.policymanagement.model.dto.CreateAppointDTO.BeneficiaryDTO;
import com.sub.baoxian.policymanagement.model.dto.CreateAppointDTO.InsuredDTO;
import com.sub.baoxian.policymanagement.model.dto.CreateAppointDTO.OverviewDTO;
import com.sub.baoxian.policymanagement.model.dto.CreateAppointDTO.PolicyholderDTO;
import com.sub.baoxian.policymanagement.model.dto.CreateAppointDTO.ProposalDTO;
import com.sub.baoxian.policymanagement.model.entity.BeneficiaryInfo;
import com.sub.baoxian.policymanagement.model.entity.InsuredInfo;
import com.sub.baoxian.policymanagement.model.entity.Policy;
import com.sub.baoxian.policymanagement.model.entity.PolicyOrder;
import com.sub.baoxian.policymanagement.model.entity.PolicyStatusAttachment;
import com.sub.baoxian.policymanagement.model.entity.PolicyStatusHistory;
import com.sub.baoxian.policymanagement.model.entity.PolicyholderInfo;
import com.sub.baoxian.policymanagement.model.vo.CreateAppointVO;
import com.sub.baoxian.service.CalendarService;
import com.sub.baoxian.service.ProposalService;
import com.sub.baoxian.util.StpKit;

import cn.hutool.core.util.IdUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class PolicyAppointService {

        private final PolicyMapper policyMapper;
        private final ProposalService proposalService;
        private final CalendarService calendarService;

        @Transactional(rollbackFor = Exception.class)
        public CreateAppointVO createAppoint(CreateAppointDTO createAppointDTO) {

                try {
                        OverviewDTO overview = createAppointDTO.getOverview();
                        PolicyholderDTO policyholder = createAppointDTO.getPolicyholder();
                        InsuredDTO insured = createAppointDTO.getInsured();
                        List<BeneficiaryDTO> beneficiary = createAppointDTO.getBeneficiary();
                        ProposalDTO proposal = createAppointDTO.getProposal();
                        Long userId = StpKit.USER.getLoginIdAsLong();
                        Long currentTime = System.currentTimeMillis();
                        PolicyOrder policyOrder = PolicyOrder.builder()
                                        .userId(userId)
                                        .customerName(overview.getCustomerName())
                                        .policyholderNameCn(policyholder.getNameCn())
                                        .policyholderNameEn(policyholder.getNameEn())
                                        .insuredNameCn(insured.getNameCn())
                                        .insuredNameEn(insured.getNameEn())
                                        .region(overview.getRegion())
                                        .company(overview.getCompany())
                                        .product(overview.getProduct())
                                        .phone(overview.getPhone())
                                        .email(overview.getEmail())
                                        .team(overview.getTeam())
                                        .referrer(overview.getReferrer())
                                        .appointmentTime(overview.getAppointmentTime())
                                        .paymentTerm(overview.getPaymentTerm())
                                        .orderNo(this.createOrderNo())
                                        .createdAt(currentTime)
                                        .updatedAt(currentTime)
                                        .createdId(userId)
                                        .statusUpdatedAt(currentTime)
                                        .orderStatus(OrderStatusEnum.APPOINTMENT)
                                        .remark(overview.getRemark())
                                        .currency(overview.getCurrencyType())
                                        .annualPremium(overview.getAnnualPremium())
                                        .build();
                        policyMapper.createPolicyOrder(policyOrder);

                        Policy policy = Policy.builder()
                                        .orderId(policyOrder.getId())
                                        .policyStatus(PolicyStatusEnum.APPOINTMENT)
                                        .company(overview.getCompany())
                                        .productName(overview.getProduct())
                                        .currency(overview.getCurrencyType())
                                        .coverageAmount(overview.getInsuredAmount())
                                        .annualPremium(overview.getAnnualPremium())
                                        .paymentMethod(overview.getPaymentMethod())
                                        .paymentTerm(overview.getPaymentTerm())
                                        .firstPremiumPaymentMethod(overview.getFirstYearPaymentMethod())
                                        .renewalPaymentMethod(overview.getRenewalPaymentMethod())
                                        .createdAt(currentTime)
                                        .updatedAt(currentTime)
                                        .build();

                        policyMapper.createPolicy(policy);

                        if (policyholder != null && policyholder.getNameCn() != null) {
                                PolicyholderInfo policyholderInfo = PolicyholderInfo.builder()
                                                .policyId(policy.getId())
                                                .nameCn(policyholder.getNameCn())
                                                .nameEn(policyholder.getNameEn())
                                                .birthDate(policyholder.getBirthDate())
                                                .gender(policyholder.getGender())
                                                .idCardNo(policyholder.getIdCardNo())
                                                .travelPermitNo(policyholder.getTravelPermitNo())
                                                .nationality(policyholder.getNationality())
                                                .mobile(policyholder.getMobile())
                                                .homePhone(policyholder.getHomePhone())
                                                .email(policyholder.getEmail())
                                                .birthPlace(policyholder.getBirthPlace())
                                                .maritalStatus(policyholder.getMaritalStatus())
                                                .educationLevel(policyholder.getEducationLevel())
                                                .height(policyholder.getHeight())
                                                .weight(policyholder.getWeight())
                                                .idCardAddress(policyholder.getIdCardAddress())
                                                .mailingAddress(policyholder.getMailingAddress())
                                                .residentialAddress(policyholder.getResidentialAddress())
                                                .isSmoker(policyholder.getIsSmoker())
                                                .companyNameCn(policyholder.getCompanyNameCn())
                                                .companyNameEn(policyholder.getCompanyNameEn())
                                                .companyAddress(policyholder.getCompanyAddress())
                                                .companyIndustry(policyholder.getCompanyIndustry())
                                                .position(policyholder.getPosition())
                                                .annualIncome(policyholder.getAnnualIncome())
                                                .createdAt(currentTime)
                                                .updatedAt(currentTime)
                                                .build();

                                policyMapper.createPolicyholderInfo(policyholderInfo);
                        }

                        if (insured != null && policyholder != null
                                        && insured.getRelationshipWithPolicyholder() != null) {
                                InsuredInfo insuredInfo = null;
                                if (insured.getRelationshipWithPolicyholder().equals("本人")) {
                                        insuredInfo = InsuredInfo.builder()
                                                        .policyId(policy.getId())
                                                        .nameCn(policyholder.getNameCn())
                                                        .nameEn(policyholder.getNameEn())
                                                        .birthDate(policyholder.getBirthDate())
                                                        .gender(policyholder.getGender())
                                                        .idCardNo(policyholder.getIdCardNo())
                                                        .travelPermitNo(policyholder.getTravelPermitNo())
                                                        .mobile(policyholder.getMobile())
                                                        .homePhone(policyholder.getHomePhone())
                                                        .email(policyholder.getEmail())
                                                        .nationality(policyholder.getNationality())
                                                        .maritalStatus(policyholder.getMaritalStatus())
                                                        .educationLevel(policyholder.getEducationLevel())
                                                        .height(policyholder.getHeight())
                                                        .weight(policyholder.getWeight())
                                                        .idCardAddress(policyholder.getIdCardAddress())
                                                        .isSmoker(policyholder.getIsSmoker())
                                                        .createdAt(currentTime)
                                                        .updatedAt(currentTime)
                                                        .build();
                                } else {
                                        insuredInfo = InsuredInfo.builder()
                                                        .policyId(policy.getId())
                                                        .nameCn(insured.getNameCn())
                                                        .nameEn(insured.getNameEn())
                                                        .birthDate(insured.getBirthDate())
                                                        .gender(insured.getGender())
                                                        .idCardNo(insured.getIdCardNo())
                                                        .travelPermitNo(insured.getTravelPermitNo())
                                                        .mobile(insured.getMobile())
                                                        .email(insured.getEmail())
                                                        .nationality(insured.getNationality())
                                                        .maritalStatus(insured.getMaritalStatus())
                                                        .educationLevel(insured.getEducationLevel())
                                                        .height(insured.getHeight())
                                                        .weight(insured.getWeight())
                                                        .idCardAddress(insured.getIdCardAddress())
                                                        .isSmoker(insured.getIsSmoker())
                                                        .createdAt(currentTime)
                                                        .updatedAt(currentTime)
                                                        .build();
                                }

                                policyMapper.createInsuredInfo(insuredInfo);
                        }

                        if (beneficiary != null && !beneficiary.isEmpty()) {
                                for (CreateAppointDTO.BeneficiaryDTO beneficiaryDTO : beneficiary) {
                                        BeneficiaryInfo beneficiaryInfo = BeneficiaryInfo.builder()
                                                        .policyId(policy.getId())
                                                        .name(beneficiaryDTO.getName())
                                                        .gender(beneficiaryDTO.getGender())
                                                        .relationship(beneficiaryDTO.getRelationship())
                                                        .idCardNo(beneficiaryDTO.getIdCardNo())
                                                        .benefitPercentage(beneficiaryDTO.getBenefitPercentage())
                                                        .isTrustee(beneficiaryDTO.getIsTrustee())
                                                        .createdAt(currentTime)
                                                        .updatedAt(currentTime)
                                                        .build();
                                        policyMapper.createBeneficiaryInfo(beneficiaryInfo);
                                }
                        }

                        PolicyStatusHistory policyStatusHistory = PolicyStatusHistory.builder()
                                        .policyId(policy.getId())
                                        .statusCode(PolicyStatusEnum.APPOINTMENT)
                                        .recordStatus(PolicyRecordStatusEnum.PENDING_COMPLETION)
                                        .createdAt(currentTime)
                                        .updatedAt(currentTime)
                                        .build();

                        policyMapper.createPolicyStatusHistory(policyStatusHistory);
                        if (proposal.getSelectedProposalId() != null) {
                                PolicyStatusAttachment policyStatusAttachment = this
                                                .proposalConvertToPolicyStatusAttachment(
                                                                proposal,
                                                                policyStatusHistory);
                                policyMapper.createPolicyStatusAttachment(policyStatusAttachment);
                        }

                        this.createSchedule(createAppointDTO);
                        return CreateAppointVO.builder()
                                        .policyId(policy.getId())
                                        .orderId(policyOrder.getId())
                                        .build();

                } catch (Exception e) {
                        log.error("创建预约失败", e);
                        throw new BizException("创建预约失败");
                }
        }

        /**
         * 创建日程
         * 
         * @return
         */
        public void createSchedule(CreateAppointDTO createAppointDTO) {
                OverviewDTO overview = createAppointDTO.getOverview();

                // 创建日程DTO
                CalendarDTO calendarDTO = new CalendarDTO();
                calendarDTO.setTitle("客户预约: " + overview.getCustomerName());

                // 构建日程内容
                StringBuilder content = new StringBuilder();
                content.append("客户: ").append(overview.getCustomerName()).append("\n");
                content.append("产品: ").append(overview.getProduct()).append("\n");
                content.append("保险公司: ").append(overview.getCompany()).append("\n");
                if (overview.getPhone() != null) {
                        content.append("联系电话: ").append(overview.getPhone()).append("\n");
                }
                if (overview.getRemark() != null && !overview.getRemark().isEmpty()) {
                        content.append("备注: ").append(overview.getRemark());
                }

                calendarDTO.setContent(content.toString());
                calendarDTO.setCalendarTime(overview.getAppointmentTime());

                // 设置提醒时间（预约时间前1小时）
                if (overview.getAppointmentTime() != null) {
                        calendarDTO.setRemindTime(overview.getAppointmentTime() - 3600000); // 提前1小时
                }

                calendarDTO.setRepeatRule("none"); // 不重复
                calendarDTO.setStatus(0); // 进行中状态

                // 调用日程服务创建日程
                calendarService.createCalendar(calendarDTO);

                log.info("已为预约创建日程, 客户: {}, 预约时间: {}", overview.getCustomerName(),
                                new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm")
                                                .format(new java.util.Date(overview.getAppointmentTime())));
        }

        /**
         * 生成唯一的订单号
         * 格式：yyyyMMdd + 8位随机字符
         * 如果生成重复则重试，最多重试5次
         */
        private String createOrderNo() {
                String datePrefix = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                int maxRetries = 5;

                for (int i = 0; i < maxRetries; i++) {
                        String orderNo = datePrefix + IdUtil.fastSimpleUUID().substring(0, 4).toUpperCase();

                        // 检查数据库中是否已存在该订单号
                        if (!isOrderNoExists(orderNo)) {
                                return orderNo;
                        }

                        log.warn("订单号重复，正在重试生成: {}, 重试次数: {}", orderNo, i + 1);
                }

                // 如果重试5次都失败，使用时间戳确保唯一性
                return datePrefix + System.currentTimeMillis() % 100000000L;
        }

        /**
         * 生成唯一的保单号
         * 格式：P + yyyyMMdd + 8位随机字符
         * 如果生成重复则重试，最多重试5次
         */
        public String createPolicyNo() {
                String datePrefix = "P" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                int maxRetries = 5;

                for (int i = 0; i < maxRetries; i++) {
                        String policyNo = datePrefix + IdUtil.fastSimpleUUID().substring(0, 4).toUpperCase();

                        // 检查数据库中是否已存在该保单号
                        if (!isPolicyNoExists(policyNo)) {
                                return policyNo;
                        }

                        log.warn("保单号重复，正在重试生成: {}, 重试次数: {}", policyNo, i + 1);
                }

                // 如果重试5次都失败，使用时间戳确保唯一性
                return datePrefix + System.currentTimeMillis() % 100000000L;
        }

        /**
         * 检查订单号是否已存在
         */
        private boolean isOrderNoExists(String orderNo) {
                try {
                        return policyMapper.countByOrderNo(orderNo) > 0;
                } catch (Exception e) {
                        log.error("检查订单号是否存在时发生错误: {}", orderNo, e);
                        return true; // 发生错误时保守处理，认为已存在
                }
        }

        /**
         * 检查保单号是否已存在
         */
        private boolean isPolicyNoExists(String policyNo) {
                try {
                        return policyMapper.countByPolicyNo(policyNo) > 0;
                } catch (Exception e) {
                        log.error("检查保单号是否存在时发生错误: {}", policyNo, e);
                        return true; // 发生错误时保守处理，认为已存在
                }
        }

        /**
         * 更新保单号
         *
         * @param policyId 保单ID
         * @param policyNo 保单号（如果为null则自动生成）
         * @return 更新成功返回true
         */
        public boolean updatePolicyNo(Long policyId, String policyNo) {
                try {
                        if (policyNo == null || policyNo.trim().isEmpty()) {
                                policyNo = createPolicyNo();
                        }

                        int result = policyMapper.updatePolicyNo(policyId, policyNo, System.currentTimeMillis());
                        return result > 0;
                } catch (Exception e) {
                        log.error("更新保单号失败, policyId: {}, policyNo: {}", policyId, policyNo, e);
                        return false;
                }
        }

        /**
         * 将计划书转换为保单状态附件
         * 
         * @param proposalDTO         计划书DTO
         * @param policyStatusHistory 保单状态历史
         * @return 保单状态附件
         */
        private PolicyStatusAttachment proposalConvertToPolicyStatusAttachment(CreateAppointDTO.ProposalDTO proposalDTO,
                        PolicyStatusHistory policyStatusHistory) {
                ProposalRecord proposal = proposalService.getById(proposalDTO.getSelectedProposalId());
                return PolicyStatusAttachment.builder()
                                .statusHistoryId(policyStatusHistory.getId())
                                .fileName(proposalService.getProposalFileName(proposalDTO.getSelectedProposalId()))
                                .ossUrl(proposal.getOssFileUrl())
                                .fileType(proposal.getOssFileUrl()
                                                .substring(proposal.getOssFileUrl().lastIndexOf(".") + 1))
                                .createdAt(System.currentTimeMillis())
                                .updatedAt(System.currentTimeMillis())
                                .build();
        }
}
