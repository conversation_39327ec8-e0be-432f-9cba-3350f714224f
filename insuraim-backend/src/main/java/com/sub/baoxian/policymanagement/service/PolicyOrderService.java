package com.sub.baoxian.policymanagement.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.common.exception.BizException;
import com.sub.baoxian.mapper.PolicyMapper;
import com.sub.baoxian.mapper.PolicyOrderMapper;
import com.sub.baoxian.policymanagement.constant.OrderStatusEnum;
import com.sub.baoxian.policymanagement.constant.PolicyStatusEnum;
import com.sub.baoxian.policymanagement.model.entity.BeneficiaryInfo;
import com.sub.baoxian.policymanagement.model.entity.InsuredInfo;
import com.sub.baoxian.policymanagement.model.entity.Policy;
import com.sub.baoxian.policymanagement.model.entity.PolicyOrder;
import com.sub.baoxian.policymanagement.model.entity.PolicyStatusAttachment;
import com.sub.baoxian.policymanagement.model.bo.PolicyStatusHistoryBO;
import com.sub.baoxian.policymanagement.model.entity.PolicyStatusHistory;
import com.sub.baoxian.policymanagement.model.entity.PolicyholderInfo;
import com.sub.baoxian.policymanagement.model.vo.GetPageListVO;
import com.sub.baoxian.policymanagement.model.vo.getDetailByOrderIdVO;
import com.sub.baoxian.util.StpKit;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class PolicyOrderService {

    private final PolicyOrderMapper policyOrderMapper;
    private final PolicyMapper policyMapper;
    private final PolicyService policyService;

    public Page<GetPageListVO> getPolicyOrdersPage(String search, PolicyStatusEnum policyStatus, OrderStatusEnum status,
            String createdAt, String updatedAt, Integer page, Integer pageSize) {

        Page<GetPageListVO> pageParam = new Page<>(page, pageSize);
        return policyOrderMapper.pageList(pageParam, StpKit.USER.getLoginIdAsLong(), search, policyStatus, status,
                createdAt, updatedAt);
    }

    @Cacheable(value = "policyOrder", key = "#orderId")
    public getDetailByOrderIdVO getDetailByOrderId(Long orderId) {
        PolicyOrder policyOrder = policyOrderMapper.selectById(orderId);
        if (policyOrder == null) {
            throw new BizException("订单不存在");
        }

        Policy policy = policyMapper.getPolicyByOrderId(orderId);
        if (policy == null) {
            throw new BizException("保单不存在");
        }

        PolicyholderInfo policyholderInfo = policyMapper.getPolicyholderInfoByPolicyId(policy.getId());

        InsuredInfo insuredInfo = policyMapper.getInsuredInfoByPolicyId(policy.getId());

        List<BeneficiaryInfo> beneficiaryInfos = policyMapper.getBeneficiaryInfosByPolicyId(policy.getId());
        if (beneficiaryInfos == null) {
            beneficiaryInfos = new ArrayList<>();
        }

        List<PolicyStatusHistory> policyStatusHistories = policyMapper
                .getPolicyStatusHistoriesByPolicyId(policy.getId());
        if (policyStatusHistories == null) {
            policyStatusHistories = new ArrayList<>();
        }

        List<PolicyStatusHistoryBO> policyStatusHistoryBOs = new ArrayList<>();

        // 为每个历史记录设置附件列表，转换为BO对象
        for (PolicyStatusHistory policyStatusHistory : policyStatusHistories) {
            List<PolicyStatusAttachment> policyStatusAttachments = policyMapper
                    .getPolicyStatusAttachmentsByHistoryId(policyStatusHistory.getId());
            if (policyStatusAttachments == null) {
                policyStatusAttachments = new ArrayList<>();
            }
            // 转换为BO对象
            PolicyStatusHistoryBO bo = PolicyStatusHistoryBO.builder().id(policyStatusHistory.getId())
                    .policyId(policyStatusHistory.getPolicyId()).statusCode(policyStatusHistory.getStatusCode())
                    .recordStatus(policyStatusHistory.getRecordStatus())
                    .updatedAt(policyStatusHistory.getUpdatedAt()).updateBy(policyStatusHistory.getUpdateBy())
                    .remark(policyStatusHistory.getRemark()).createdAt(policyStatusHistory.getCreatedAt())
                    .attachments(policyStatusAttachments).build();

            policyStatusHistoryBOs.add(bo);
        }

        List<PolicyStatusAttachment> policyStatusAttachments = policyMapper
                .getPolicyStatusAttachmentsByPolicyId(policy.getId());
        if (policyStatusAttachments == null) {
            policyStatusAttachments = new ArrayList<>();
        }

        return getDetailByOrderIdVO.builder().policyOrder(policyOrder).policyInfo(policy)
                .policyholderInfo(policyholderInfo).insuredInfo(insuredInfo).beneficiaryInfos(beneficiaryInfos)
                .policyStatusHistories(policyStatusHistoryBOs).policyStatusAttachments(policyStatusAttachments)
                .build();
    }

    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "policyOrder", allEntries = true)
    public void deletePolicyOrder(Long policyId) {
        Map<String, Object> res = policyService.checkPolicyBelongToUser(policyId, StpKit.USER.getLoginIdAsLong());
        Policy policy = (Policy) res.get("policy");
        PolicyOrder policyOrder = (PolicyOrder) res.get("policyOrder");
        if (policyOrder == null) {
            throw new BizException("订单不存在");
        }

        policyOrderMapper.deleteById(policyOrder.getId());
        policyMapper.deleteById(policy.getId());

        // 删除受益人受保人信息
        policyMapper.deleteBeneficiaryInfoByPolicyId(policy.getId());
        policyMapper.deleteInsuredInfoByPolicyId(policy.getId());

        // 删除保单状态历史
        policyMapper.deletePolicyStatusHistoryByPolicyId(policy.getId());

        // 删除保单状态附件
        policyMapper.deletePolicyStatusAttachmentByPolicyId(policy.getId());

        log.info("订单删除成功，订单ID：{}", policyOrder.getId());
    }
}
