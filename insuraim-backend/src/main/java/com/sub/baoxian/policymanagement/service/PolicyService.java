package com.sub.baoxian.policymanagement.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sub.baoxian.common.exception.BizException;
import com.sub.baoxian.mapper.PolicyMapper;
import com.sub.baoxian.mapper.PolicyOrderMapper;
import com.sub.baoxian.policymanagement.constant.OrderStatusEnum;
import com.sub.baoxian.policymanagement.constant.PolicyRecordStatusEnum;
import com.sub.baoxian.policymanagement.constant.PolicyStatusEnum;
import com.sub.baoxian.policymanagement.model.dto.AddBeneficiaryInfoDTO;
import com.sub.baoxian.policymanagement.model.dto.UpdateBeneficiaryInfoDTO;
import com.sub.baoxian.policymanagement.model.dto.UpdateInsuredInfoDTO;
import com.sub.baoxian.policymanagement.model.dto.UpdatePolicyBasicInfoDTO;
import com.sub.baoxian.policymanagement.model.dto.UpdatePolicyStatusDTO;
import com.sub.baoxian.policymanagement.model.dto.UpdatePolicyholderInfoDTO;
import com.sub.baoxian.policymanagement.model.entity.BeneficiaryInfo;
import com.sub.baoxian.policymanagement.model.entity.InsuredInfo;
import com.sub.baoxian.policymanagement.model.entity.Policy;
import com.sub.baoxian.policymanagement.model.entity.PolicyOrder;
import com.sub.baoxian.policymanagement.model.bo.PolicyStatusHistoryBO;
import com.sub.baoxian.policymanagement.model.entity.PolicyStatusAttachment;
import com.sub.baoxian.policymanagement.model.entity.PolicyStatusHistory;
import com.sub.baoxian.policymanagement.model.entity.PolicyholderInfo;
import com.sub.baoxian.util.OssUtil;
import com.sub.baoxian.util.StpKit;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class PolicyService {

    private final PolicyMapper policyMapper;
    private final PolicyOrderMapper policyOrderMapper;

    public List<PolicyStatusHistoryBO> getHistoryStatus(Long policyId) {
        List<PolicyStatusHistory> policyStatusHistories = policyMapper.getHistoryStatus(policyId);
        if (policyStatusHistories == null) {
            return new ArrayList<>();
        }

        List<PolicyStatusHistoryBO> result = new ArrayList<>();

        // 为每个历史记录设置附件列表
        for (PolicyStatusHistory policyStatusHistory : policyStatusHistories) {
            List<PolicyStatusAttachment> policyStatusAttachments = policyMapper
                    .getPolicyStatusAttachmentsByHistoryId(policyStatusHistory.getId());
            if (policyStatusAttachments == null) {
                policyStatusAttachments = new ArrayList<>();
            }

            // 转换为BO对象
            PolicyStatusHistoryBO bo = PolicyStatusHistoryBO.builder()
                    .id(policyStatusHistory.getId())
                    .policyId(policyStatusHistory.getPolicyId())
                    .statusCode(policyStatusHistory.getStatusCode())
                    .recordStatus(policyStatusHistory.getRecordStatus())
                    .updatedAt(policyStatusHistory.getUpdatedAt())
                    .updateBy(policyStatusHistory.getUpdateBy())
                    .remark(policyStatusHistory.getRemark())
                    .createdAt(policyStatusHistory.getCreatedAt())
                    .attachments(policyStatusAttachments)
                    .build();

            result.add(bo);
        }

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = { "policyOrder" }, allEntries = true)
    public void updatePolicyStatus(UpdatePolicyStatusDTO updatePolicyStatusDTO) {
        Long userId = StpKit.USER.getLoginIdAsLong();
        Map<String, Object> res = checkPolicyBelongToUser(updatePolicyStatusDTO.getPolicyId(), userId);
        Policy policy = (Policy) res.get("policy");

        // 获取当前状态
        Long currentTime = System.currentTimeMillis();
        PolicyStatusEnum currentStatus = policy.getPolicyStatus();

        // 获取下一个状态
        PolicyStatusEnum nextStatus = PolicyStatusEnum.getNextStatus(currentStatus);
        if (nextStatus == null) {
            policyMapper.updatePolicyStatusHistoryRecordStatus(
                    updatePolicyStatusDTO.getPolicyId(),
                    currentStatus,
                    PolicyRecordStatusEnum.COMPLETED,
                    currentTime);
            return;
        }

        Long effectiveDate = null;
        if (nextStatus.equals(PolicyStatusEnum.QUIET_PERIOD_EXPIRED)) {
            effectiveDate = currentTime;
        }

        int updateResult = policyMapper.updatePolicyStatus(
                updatePolicyStatusDTO.getPolicyId(), nextStatus, currentTime, effectiveDate);

        if (updateResult <= 0) {
            throw new BizException("更新保单状态失败");
        }

        OrderStatusEnum orderStatus = null;
        switch (nextStatus) {
            case SIGN:
                orderStatus = OrderStatusEnum.WAIT_PAYMENT;
                break;
            case EFFECTIVE:
                orderStatus = OrderStatusEnum.QUIET_PERIOD;
                break;
            case QUIET_PERIOD_EXPIRED:
                orderStatus = OrderStatusEnum.IN_EFFECT;
                break;
            default:
                break;
        }

        if (orderStatus != null) {
            LambdaUpdateWrapper<PolicyOrder> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(PolicyOrder::getId, policy.getOrderId());
            updateWrapper.set(PolicyOrder::getOrderStatus, orderStatus);
            updateWrapper.set(PolicyOrder::getStatusUpdatedAt, currentTime);
            policyOrderMapper.update(null, updateWrapper);
        }

        // 将当前状态的历史记录更新为已完成
        policyMapper.updatePolicyStatusHistoryRecordStatus(
                updatePolicyStatusDTO.getPolicyId(),
                currentStatus,
                PolicyRecordStatusEnum.COMPLETED,
                currentTime);

        // 创建新的状态历史记录
        PolicyStatusHistory statusHistory = PolicyStatusHistory.builder()
                .policyId(updatePolicyStatusDTO.getPolicyId())
                .statusCode(nextStatus)
                .recordStatus(PolicyRecordStatusEnum.PENDING_COMPLETION)
                .updatedAt(currentTime)
                .createdAt(currentTime)
                .remark(updatePolicyStatusDTO.getRemark())
                .build();

        policyMapper.createPolicyStatusHistory(statusHistory);

        log.info(
                "保单状态更新成功，保单ID：{}，状态：{} -> {}",
                updatePolicyStatusDTO.getPolicyId(),
                currentStatus,
                nextStatus);
    }

    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "policyOrder", allEntries = true)
    public void uploadAttachment(MultipartFile file, Long policyId, PolicyStatusEnum status, Boolean isFormInvite) {
        if (!isFormInvite) {
            Long userId = StpKit.USER.getLoginIdAsLong();
            checkPolicyBelongToUser(policyId, userId);
        }
        PolicyOrder policyOrder = policyMapper.getPolicyOrderById(policyId);

        // 获取原始文件名
        String originalFileName = file.getOriginalFilename();

        // 根据文件名分割获取fileName和fileType
        String fileName = "";
        String fileType = "";

        if (originalFileName != null && !originalFileName.isEmpty()) {
            int lastDotIndex = originalFileName.lastIndexOf('.');
            if (lastDotIndex > 0 && lastDotIndex < originalFileName.length() - 1) {
                // 有扩展名的情况：123.pdf -> fileName=123, fileType=pdf
                fileName = originalFileName.substring(0, lastDotIndex) + "_" + policyOrder.getCustomerName() + "_"
                        + policyOrder.getOrderNo();
                fileType = originalFileName.substring(lastDotIndex + 1);
            } else {
                // 没有扩展名的情况：123 -> fileName=123, fileType=""
                fileName = originalFileName + "_" + policyOrder.getCustomerName() + "_" + policyOrder.getOrderNo();
                fileType = "";
            }
        }
        Long fileSize = file.getSize();

        // 保存文件
        String filePath = OssUtil.uploadFile(file, "Insuriam/policyattachment/", fileName + "." + fileType);

        // 根据StatusCode查询历史记录
        PolicyStatusHistory policyStatusHistory = policyMapper.getPolicyStatusHistoryByStatusCode(policyId, status);
        if (policyStatusHistory == null) {
            throw new BizException("状态历史记录不存在");
        }

        // 保存文件信息
        PolicyStatusAttachment policyStatusAttachment = PolicyStatusAttachment.builder()
                .statusHistoryId(policyStatusHistory.getId())
                .fileName(fileName)
                .fileType(fileType != null && !fileType.isEmpty() ? fileType : "unknown")
                .fileSize(fileSize)
                .ossUrl(filePath)
                .createdAt(System.currentTimeMillis())
                .updatedAt(System.currentTimeMillis())
                .build();

        policyMapper.createPolicyStatusAttachment(policyStatusAttachment);
    }

    @CacheEvict(value = "policyOrder", allEntries = true)
    public void uploadAttachment(MultipartFile file, Long policyId, PolicyStatusEnum status) {
        uploadAttachment(file, policyId, status, false);
    }

    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "policyOrder", allEntries = true)
    public void withdrawStatusChange(Long policyId) {
        Long userId = StpKit.USER.getLoginIdAsLong();
        Map<String, Object> res = checkPolicyBelongToUser(policyId, userId);
        Policy policy = (Policy) res.get("policy");

        // 查询当前状态
        PolicyStatusEnum currentStatus = policy.getPolicyStatus();
        PolicyStatusEnum previousStatus = PolicyStatusEnum.getPreviousStatus(currentStatus);

        if (currentStatus.equals(PolicyStatusEnum.APPOINTMENT)) {
            throw new BizException("预约状态无法撤回");
        }

        if (previousStatus == null) {
            throw new BizException("当前状态无法撤回");
        }

        // 查询当前状态的历史记录
        PolicyStatusHistory policyStatusHistory = policyMapper.getPolicyStatusHistoryByStatusCode(policyId,
                currentStatus);
        if (policyStatusHistory == null) {
            throw new BizException("当前状态的历史记录不存在");
        }

        // 获取当前时间戳
        Long currentTime = System.currentTimeMillis();

        // 获取并删除OSS中的附件文件
        List<PolicyStatusAttachment> attachments = policyMapper
                .getPolicyStatusAttachmentsByHistoryId(policyStatusHistory.getId());
        if (attachments != null && !attachments.isEmpty()) {
            for (PolicyStatusAttachment attachment : attachments) {
                if (attachment.getOssUrl() != null && !attachment.getOssUrl().isEmpty()) {
                    try {
                        OssUtil.deleteFile(OssUtil.extractObjectNameFromUrl(attachment.getOssUrl()));
                    } catch (Exception e) {
                        log.warn("删除OSS文件失败: {}", e.getMessage());
                    }
                }
            }
        }

        // 删除数据库中的附件记录
        policyMapper.deletePolicyStatusAttachment(policyStatusHistory.getId());

        // 删除状态历史记录
        policyMapper.deletePolicyStatusHistory(policyStatusHistory.getId());

        // 更新保单状态
        policyMapper.updatePolicyStatus(policyId, previousStatus, currentTime, null);

        // 根据前一个状态更新订单状态
        OrderStatusEnum orderStatus = null;
        switch (previousStatus) {
            case SIGNATURE:
                orderStatus = OrderStatusEnum.QUIET_PERIOD;
                break;
            case PAYMENT_RECORD:
                orderStatus = OrderStatusEnum.WAIT_PAYMENT;
                break;
            case SIGN_APPOINTMENT:
                orderStatus = OrderStatusEnum.APPOINTMENT;
            default:
                break;
        }

        // 只有在需要更新订单状态时才执行更新
        if (orderStatus != null) {
            LambdaUpdateWrapper<PolicyOrder> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(PolicyOrder::getId, policy.getOrderId());
            updateWrapper.set(PolicyOrder::getOrderStatus, orderStatus);
            updateWrapper.set(PolicyOrder::getStatusUpdatedAt, currentTime);
            policyOrderMapper.update(null, updateWrapper);
        }

        // 将前一个状态的历史记录更新为待完成
        policyMapper.updatePolicyStatusHistoryRecordStatus(
                policyId, previousStatus, PolicyRecordStatusEnum.PENDING_COMPLETION, currentTime);

        log.info("保单状态撤回成功，保单ID：{}，状态：{} -> {}", policyId, currentStatus, previousStatus);
    }

    public Map<String, Object> checkPolicyBelongToUser(Long policyId, Long userId) {
        Policy policy = policyMapper.selectById(policyId);
        PolicyOrder policyOrder = policyMapper.getPolicyOrderById(policyId);
        if (policy == null) {
            throw new BizException("保单不存在");
        }
        if (!policyOrder.getUserId().equals(userId)) {
            throw new BizException("保单不属于当前用户");
        }
        Map<String, Object> result = new HashMap<>();
        result.put("policy", policy);
        result.put("policyOrder", policyOrder);
        return result;
    }

    @CacheEvict(value = "policyOrder", allEntries = true)
    public void updatePolicyBasicInfo(UpdatePolicyBasicInfoDTO updatePolicyBasicInfoDTO) {
        Long userId = StpKit.USER.getLoginIdAsLong();
        Map<String, Object> res = checkPolicyBelongToUser(updatePolicyBasicInfoDTO.getPolicyId(), userId);
        Policy policy = (Policy) res.get("policy");
        PolicyOrder policyOrder = (PolicyOrder) res.get("policyOrder");
        Long currentTime = System.currentTimeMillis();

        policyOrder.setCustomerName(updatePolicyBasicInfoDTO.getCustomerName());
        policyOrder.setRegion(updatePolicyBasicInfoDTO.getRegion());
        policyOrder.setCompany(updatePolicyBasicInfoDTO.getCompany());
        policyOrder.setProduct(updatePolicyBasicInfoDTO.getProduct());
        policyOrder.setPhone(updatePolicyBasicInfoDTO.getPhone());
        policyOrder.setEmail(updatePolicyBasicInfoDTO.getEmail());
        policyOrder.setTeam(updatePolicyBasicInfoDTO.getTeam());
        policyOrder.setAppointmentTime(updatePolicyBasicInfoDTO.getAppointmentTime());
        policyOrder.setReferrer(updatePolicyBasicInfoDTO.getReferrer());
        policyOrder.setPaymentTerm(updatePolicyBasicInfoDTO.getPaymentTerm());
        policyOrder.setCurrency(updatePolicyBasicInfoDTO.getCurrencyType());
        policyOrder.setAnnualPremium(updatePolicyBasicInfoDTO.getAnnualPremium());
        policyOrder.setRemark(updatePolicyBasicInfoDTO.getRemark());
        policyOrder.setStatusUpdatedAt(currentTime);
        policyOrderMapper.updateById(policyOrder);

        policy.setCompany(updatePolicyBasicInfoDTO.getCompany());
        policy.setProductName(updatePolicyBasicInfoDTO.getProduct());
        policy.setCurrency(updatePolicyBasicInfoDTO.getCurrencyType());
        policy.setCoverageAmount(updatePolicyBasicInfoDTO.getInsuredAmount());
        policy.setAnnualPremium(updatePolicyBasicInfoDTO.getAnnualPremium());
        policy.setPaymentMethod(updatePolicyBasicInfoDTO.getPaymentMethod());
        policy.setPaymentTerm(updatePolicyBasicInfoDTO.getPaymentTerm());
        policy.setFirstPremiumPaymentMethod(updatePolicyBasicInfoDTO.getFirstPremiumPaymentMethod());
        policy.setRenewalPaymentMethod(updatePolicyBasicInfoDTO.getRenewalPaymentMethod());
        policy.setUpdatedAt(currentTime);
        policyMapper.updateById(policy);
    }

    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "policyOrder", allEntries = true)
    public void updatePolicyholderInfo(
            UpdatePolicyholderInfoDTO updatePolicyholderInfoDTO, Boolean isFormInvite) {
        PolicyOrder policyOrder = null;
        if (isFormInvite) {
            policyOrder = policyMapper.getPolicyOrderById(updatePolicyholderInfoDTO.getPolicyId());
        } else {
            Long userId = StpKit.USER.getLoginIdAsLong();
            Map<String, Object> res = checkPolicyBelongToUser(updatePolicyholderInfoDTO.getPolicyId(), userId);
            policyOrder = (PolicyOrder) res.get("policyOrder");
        }

        Long currentTime = System.currentTimeMillis();

        // 更新PolicyOrder表中的投保人姓名
        policyOrder.setPolicyholderNameCn(updatePolicyholderInfoDTO.getNameCn());
        policyOrder.setPolicyholderNameEn(updatePolicyholderInfoDTO.getNameEn());
        policyOrder.setStatusUpdatedAt(currentTime);
        policyOrderMapper.updateById(policyOrder);

        // 查询现有的保单持有人信息
        PolicyholderInfo existingPolicyholderInfo = policyMapper
                .getPolicyholderInfoByPolicyId(updatePolicyholderInfoDTO.getPolicyId());
        boolean shouldCreate = false;

        if (isFormInvite != null && isFormInvite) {
            // 邀请表单提交：一定是创建新的保单持有人信息
            shouldCreate = true;
        } else {
            // 普通更新：保单持有人信息必须存在
            if (existingPolicyholderInfo == null) {
                if (isFormInvite == null || !isFormInvite) {
                    throw new BizException("请等待客户填写完资料");
                } else {
                    throw new BizException("保单持有人信息不存在");
                }
            }
        }

        // 创建或更新保单持有人信息
        if (shouldCreate) {
            // 创建新的保单持有人信息
            PolicyholderInfo policyholderInfo = PolicyholderInfo.builder()
                    .policyId(updatePolicyholderInfoDTO.getPolicyId())
                    .nameCn(updatePolicyholderInfoDTO.getNameCn())
                    .nameEn(updatePolicyholderInfoDTO.getNameEn())
                    .birthDate(updatePolicyholderInfoDTO.getBirthDate())
                    .gender(updatePolicyholderInfoDTO.getGender())
                    .idCardNo(updatePolicyholderInfoDTO.getIdCardNo())
                    .travelPermitNo(updatePolicyholderInfoDTO.getTravelPermitNo())
                    .nationality(updatePolicyholderInfoDTO.getNationality())
                    .mobile(updatePolicyholderInfoDTO.getMobile())
                    .email(updatePolicyholderInfoDTO.getEmail())
                    .birthPlace(updatePolicyholderInfoDTO.getBirthPlace())
                    .maritalStatus(updatePolicyholderInfoDTO.getMaritalStatus())
                    .educationLevel(updatePolicyholderInfoDTO.getEducationLevel())
                    .height(updatePolicyholderInfoDTO.getHeight())
                    .weight(updatePolicyholderInfoDTO.getWeight())
                    .idCardAddress(updatePolicyholderInfoDTO.getIdCardAddress())
                    .residentialAddress(updatePolicyholderInfoDTO.getResidentialAddress())
                    .mailingAddress(updatePolicyholderInfoDTO.getMailingAddress())
                    .isSmoker(updatePolicyholderInfoDTO.getIsSmoker())
                    .companyNameCn(updatePolicyholderInfoDTO.getCompanyNameCn())
                    .companyNameEn(updatePolicyholderInfoDTO.getCompanyNameEn())
                    .companyAddress(updatePolicyholderInfoDTO.getCompanyAddress())
                    .companyIndustry(updatePolicyholderInfoDTO.getCompanyIndustry())
                    .position(updatePolicyholderInfoDTO.getPosition())
                    .annualIncome(updatePolicyholderInfoDTO.getAnnualIncome())
                    .createdAt(currentTime)
                    .updatedAt(currentTime)
                    .build();

            policyMapper.createPolicyholderInfo(policyholderInfo);
            log.info("保单持有人信息创建成功，保单ID：{}", updatePolicyholderInfoDTO.getPolicyId());
        } else {
            // 更新现有的保单持有人信息
            PolicyholderInfo policyholderInfo = PolicyholderInfo.builder()
                    .policyId(updatePolicyholderInfoDTO.getPolicyId())
                    .nameCn(updatePolicyholderInfoDTO.getNameCn())
                    .nameEn(updatePolicyholderInfoDTO.getNameEn())
                    .birthDate(updatePolicyholderInfoDTO.getBirthDate())
                    .gender(updatePolicyholderInfoDTO.getGender())
                    .idCardNo(updatePolicyholderInfoDTO.getIdCardNo())
                    .travelPermitNo(updatePolicyholderInfoDTO.getTravelPermitNo())
                    .nationality(updatePolicyholderInfoDTO.getNationality())
                    .mobile(updatePolicyholderInfoDTO.getMobile())
                    .email(updatePolicyholderInfoDTO.getEmail())
                    .birthPlace(updatePolicyholderInfoDTO.getBirthPlace())
                    .maritalStatus(updatePolicyholderInfoDTO.getMaritalStatus())
                    .educationLevel(updatePolicyholderInfoDTO.getEducationLevel())
                    .height(updatePolicyholderInfoDTO.getHeight())
                    .weight(updatePolicyholderInfoDTO.getWeight())
                    .idCardAddress(updatePolicyholderInfoDTO.getIdCardAddress())
                    .residentialAddress(updatePolicyholderInfoDTO.getResidentialAddress())
                    .mailingAddress(updatePolicyholderInfoDTO.getMailingAddress())
                    .isSmoker(updatePolicyholderInfoDTO.getIsSmoker())
                    .companyNameCn(updatePolicyholderInfoDTO.getCompanyNameCn())
                    .companyNameEn(updatePolicyholderInfoDTO.getCompanyNameEn())
                    .companyAddress(updatePolicyholderInfoDTO.getCompanyAddress())
                    .companyIndustry(updatePolicyholderInfoDTO.getCompanyIndustry())
                    .position(updatePolicyholderInfoDTO.getPosition())
                    .annualIncome(updatePolicyholderInfoDTO.getAnnualIncome())
                    .updatedAt(currentTime)
                    .build();

            int updateResult = policyMapper.updatePolicyholderInfo(policyholderInfo);

            if (updateResult <= 0) {
                throw new BizException("更新保单持有人信息失败");
            }

            log.info("保单持有人信息更新成功，保单ID：{}", updatePolicyholderInfoDTO.getPolicyId());
        }
    }

    /**
     * 更新保单持有人信息（重载方法，保持向后兼容性）
     */
    public void updatePolicyholderInfo(UpdatePolicyholderInfoDTO updatePolicyholderInfoDTO) {
        updatePolicyholderInfo(updatePolicyholderInfoDTO, false);
    }

    /**
     * 更新受保人信息
     *
     * @param updateInsuredInfoDTO
     * @param isFormInvite
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "policyOrder", allEntries = true)
    public void updateInsuredInfo(UpdateInsuredInfoDTO updateInsuredInfoDTO,
            UpdatePolicyholderInfoDTO updatePolicyholderInfoDTO, Boolean isFormInvite) {
        PolicyOrder policyOrder = null;
        if (isFormInvite) {
            policyOrder = policyMapper.getPolicyOrderById(updateInsuredInfoDTO.getPolicyId());
        } else {
            Long userId = StpKit.USER.getLoginIdAsLong();
            Map<String, Object> res = checkPolicyBelongToUser(updateInsuredInfoDTO.getPolicyId(), userId);
            policyOrder = (PolicyOrder) res.get("policyOrder");
        }

        Long currentTime = System.currentTimeMillis();

        // 更新PolicyOrder表中的受保人姓名
        policyOrder.setInsuredNameCn(updateInsuredInfoDTO.getNameCn());
        policyOrder.setInsuredNameEn(updateInsuredInfoDTO.getNameEn());
        if (isFormInvite != null && isFormInvite) {
            if (updateInsuredInfoDTO.getRelationshipWithPolicyholder().equals("本人")) {
                policyOrder.setInsuredNameCn(updatePolicyholderInfoDTO.getNameCn());
                policyOrder.setInsuredNameEn(updatePolicyholderInfoDTO.getNameEn());
            } else {
                policyOrder.setInsuredNameCn(updateInsuredInfoDTO.getNameCn());
                policyOrder.setInsuredNameEn(updateInsuredInfoDTO.getNameEn());
            }
        }
        policyOrder.setStatusUpdatedAt(currentTime);
        policyOrderMapper.updateById(policyOrder);

        // 查询现有的受保人信息
        InsuredInfo existingInsuredInfo = policyMapper.getInsuredInfoByPolicyId(updateInsuredInfoDTO.getPolicyId());
        boolean shouldCreate = false;

        if (isFormInvite != null && isFormInvite) {
            // 邀请表单提交：一定是创建新的受保人信息
            shouldCreate = true;
        } else {
            // 普通更新：受保人信息必须存在
            if (existingInsuredInfo == null) {
                if (isFormInvite == null || !isFormInvite) {
                    throw new BizException("请等待客户填写完资料");
                } else {
                    throw new BizException("受保人信息不存在");
                }
            }
        }

        // 创建或更新受保人信息
        if (shouldCreate) {
            // 创建新的受保人信息
            InsuredInfo insuredInfo = null;
            if (updateInsuredInfoDTO.getRelationshipWithPolicyholder().equals("本人")) {
                insuredInfo = InsuredInfo.builder()
                        .policyId(updatePolicyholderInfoDTO.getPolicyId())
                        .nameCn(updatePolicyholderInfoDTO.getNameCn())
                        .nameEn(updatePolicyholderInfoDTO.getNameEn())
                        .birthDate(updatePolicyholderInfoDTO.getBirthDate())
                        .gender(updatePolicyholderInfoDTO.getGender())
                        .idCardNo(updatePolicyholderInfoDTO.getIdCardNo())
                        .travelPermitNo(updatePolicyholderInfoDTO.getTravelPermitNo())
                        .mobile(updatePolicyholderInfoDTO.getMobile())
                        .email(updatePolicyholderInfoDTO.getEmail())
                        .nationality(updatePolicyholderInfoDTO.getNationality())
                        .maritalStatus(updatePolicyholderInfoDTO.getMaritalStatus())
                        .educationLevel(updatePolicyholderInfoDTO.getEducationLevel())
                        .height(updatePolicyholderInfoDTO.getHeight())
                        .weight(updatePolicyholderInfoDTO.getWeight())
                        .idCardAddress(updatePolicyholderInfoDTO.getIdCardAddress())
                        .isSmoker(updatePolicyholderInfoDTO.getIsSmoker())
                        .createdAt(currentTime)
                        .updatedAt(currentTime)
                        .build();
            } else {
                insuredInfo = InsuredInfo.builder()
                        .policyId(updateInsuredInfoDTO.getPolicyId())
                        .nameCn(updateInsuredInfoDTO.getNameCn())
                        .nameEn(updateInsuredInfoDTO.getNameEn())
                        .birthDate(updateInsuredInfoDTO.getBirthDate())
                        .gender(updateInsuredInfoDTO.getGender())
                        .idCardNo(updateInsuredInfoDTO.getIdCardNo())
                        .travelPermitNo(updateInsuredInfoDTO.getTravelPermitNo())
                        .mobile(updateInsuredInfoDTO.getMobile())
                        .email(updateInsuredInfoDTO.getEmail())
                        .nationality(updateInsuredInfoDTO.getNationality())
                        .maritalStatus(updateInsuredInfoDTO.getMaritalStatus())
                        .educationLevel(updateInsuredInfoDTO.getEducationLevel())
                        .height(updateInsuredInfoDTO.getHeight())
                        .weight(updateInsuredInfoDTO.getWeight())
                        .idCardAddress(updateInsuredInfoDTO.getIdCardAddress())
                        .isSmoker(updateInsuredInfoDTO.getIsSmoker())
                        .createdAt(currentTime)
                        .updatedAt(currentTime)
                        .build();
            }
            policyMapper.createInsuredInfo(insuredInfo);
            log.info("受保人信息创建成功，保单ID：{}", updateInsuredInfoDTO.getPolicyId());
        } else {
            // 更新现有的受保人信息
            InsuredInfo insuredInfo = InsuredInfo.builder()
                    .policyId(updateInsuredInfoDTO.getPolicyId())
                    .nameCn(updateInsuredInfoDTO.getNameCn())
                    .nameEn(updateInsuredInfoDTO.getNameEn())
                    .birthDate(updateInsuredInfoDTO.getBirthDate())
                    .gender(updateInsuredInfoDTO.getGender())
                    .idCardNo(updateInsuredInfoDTO.getIdCardNo())
                    .travelPermitNo(updateInsuredInfoDTO.getTravelPermitNo())
                    .mobile(updateInsuredInfoDTO.getMobile())
                    .email(updateInsuredInfoDTO.getEmail())
                    .nationality(updateInsuredInfoDTO.getNationality())
                    .maritalStatus(updateInsuredInfoDTO.getMaritalStatus())
                    .educationLevel(updateInsuredInfoDTO.getEducationLevel())
                    .height(updateInsuredInfoDTO.getHeight())
                    .weight(updateInsuredInfoDTO.getWeight())
                    .idCardAddress(updateInsuredInfoDTO.getIdCardAddress())
                    .isSmoker(updateInsuredInfoDTO.getIsSmoker())
                    .updatedAt(currentTime)
                    .build();

            int updateResult = policyMapper.updateInsuredInfo(insuredInfo);

            if (updateResult <= 0) {
                throw new BizException("更新受保人信息失败");
            }

            log.info("受保人信息更新成功，保单ID：{}", updateInsuredInfoDTO.getPolicyId());
        }
    }

    /**
     * 更新受保人信息（重载方法，保持向后兼容性）
     */
    public void updateInsuredInfo(UpdateInsuredInfoDTO updateInsuredInfoDTO) {
        updateInsuredInfo(updateInsuredInfoDTO, null, false);
    }

    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "policyOrder", allEntries = true)
    public void updateBeneficiaryInfo(
            UpdateBeneficiaryInfoDTO updateBeneficiaryInfoDTO, Boolean isFormInvite) {
        if (!isFormInvite) {
            checkPolicyBelongToUser(updateBeneficiaryInfoDTO.getPolicyId(), StpKit.USER.getLoginIdAsLong());
        }

        Long currentTime = System.currentTimeMillis();

        // 查询现有的受益人信息
        List<BeneficiaryInfo> existingBeneficiaryInfos = policyMapper
                .getBeneficiaryInfosByPolicyId(updateBeneficiaryInfoDTO.getPolicyId());
        boolean shouldCreate = false;
        BeneficiaryInfo targetBeneficiary = null;

        if (isFormInvite != null && isFormInvite) {
            // 邀请表单提交：一定是创建新的受益人信息
            shouldCreate = true;
        } else {
            // 普通更新：受益人信息必须存在
            if (existingBeneficiaryInfos == null || existingBeneficiaryInfos.isEmpty()) {
                throw new BizException("受益人信息不存在");
            }

            // 根据ID查找要更新的受益人
            for (BeneficiaryInfo beneficiary : existingBeneficiaryInfos) {
                if (beneficiary.getId().equals(updateBeneficiaryInfoDTO.getId())) {
                    targetBeneficiary = beneficiary;
                    break;
                }
            }

            if (targetBeneficiary == null) {
                throw new BizException("指定的受益人信息不存在");
            }
        }

        // 创建或更新受益人信息
        if (shouldCreate) {
            // 创建新的受益人信息
            BeneficiaryInfo beneficiaryInfo = BeneficiaryInfo.builder()
                    .policyId(updateBeneficiaryInfoDTO.getPolicyId())
                    .name(updateBeneficiaryInfoDTO.getName())
                    .gender(updateBeneficiaryInfoDTO.getGender())
                    .relationship(updateBeneficiaryInfoDTO.getRelationship())
                    .idCardNo(updateBeneficiaryInfoDTO.getIdCardNo())
                    .benefitPercentage(updateBeneficiaryInfoDTO.getBenefitPercentage())
                    .isTrustee(updateBeneficiaryInfoDTO.getIsTrustee())
                    .createdAt(currentTime)
                    .updatedAt(currentTime)
                    .build();

            policyMapper.createBeneficiaryInfo(beneficiaryInfo);
            log.info("受益人信息创建成功，保单ID：{}", updateBeneficiaryInfoDTO.getPolicyId());
        } else {
            // 更新现有的受益人信息
            BeneficiaryInfo beneficiaryInfo = BeneficiaryInfo.builder()
                    .id(updateBeneficiaryInfoDTO.getId())
                    .policyId(updateBeneficiaryInfoDTO.getPolicyId())
                    .name(updateBeneficiaryInfoDTO.getName())
                    .gender(updateBeneficiaryInfoDTO.getGender())
                    .relationship(updateBeneficiaryInfoDTO.getRelationship())
                    .idCardNo(updateBeneficiaryInfoDTO.getIdCardNo())
                    .benefitPercentage(updateBeneficiaryInfoDTO.getBenefitPercentage())
                    .isTrustee(updateBeneficiaryInfoDTO.getIsTrustee())
                    .updatedAt(currentTime)
                    .build();

            int updateResult = policyMapper.updateBeneficiaryInfo(beneficiaryInfo);

            if (updateResult <= 0) {
                throw new BizException("更新受益人信息失败");
            }

            log.info(
                    "受益人信息更新成功，保单ID：{}，受益人ID：{}",
                    updateBeneficiaryInfoDTO.getPolicyId(),
                    updateBeneficiaryInfoDTO.getId());
        }
    }

    /**
     * 更新受益人信息（重载方法，保持向后兼容性）
     */
    public void updateBeneficiaryInfo(UpdateBeneficiaryInfoDTO updateBeneficiaryInfoDTO) {
        updateBeneficiaryInfo(updateBeneficiaryInfoDTO, false);
    }

    /**
     * 添加受益人信息
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "policyOrder", allEntries = true)
    public void addBeneficiaryInfo(AddBeneficiaryInfoDTO addBeneficiaryInfoDTO) {
        Long userId = StpKit.USER.getLoginIdAsLong();
        checkPolicyBelongToUser(addBeneficiaryInfoDTO.getPolicyId(), userId);
        Long currentTime = System.currentTimeMillis();

        // 验证必填字段
        if (addBeneficiaryInfoDTO.getName() == null || addBeneficiaryInfoDTO.getName().trim().isEmpty()) {
            throw new BizException("受益人姓名不能为空");
        }

        // 创建新的受益人信息
        BeneficiaryInfo beneficiaryInfo = BeneficiaryInfo.builder()
                .policyId(addBeneficiaryInfoDTO.getPolicyId())
                .name(addBeneficiaryInfoDTO.getName())
                .gender(addBeneficiaryInfoDTO.getGender())
                .relationship(addBeneficiaryInfoDTO.getRelationship())
                .idCardNo(addBeneficiaryInfoDTO.getIdCardNo())
                .benefitPercentage(addBeneficiaryInfoDTO.getBenefitPercentage())
                .isTrustee(addBeneficiaryInfoDTO.getIsTrustee())
                .createdAt(currentTime)
                .updatedAt(currentTime)
                .build();

        policyMapper.createBeneficiaryInfo(beneficiaryInfo);
        log.info("受益人信息添加成功，保单ID：{}，受益人姓名：{}",
                addBeneficiaryInfoDTO.getPolicyId(), addBeneficiaryInfoDTO.getName());
    }

    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "policyOrder", allEntries = true)
    public void deleteBeneficiaryInfo(Long beneficiaryId) {
        Long userId = StpKit.USER.getLoginIdAsLong();

        // 根据受益人ID查询受益人信息
        BeneficiaryInfo beneficiaryInfo = policyMapper.getBeneficiaryInfoById(beneficiaryId);
        if (beneficiaryInfo == null) {
            throw new BizException("受益人信息不存在");
        }
        // 检查保单是否属于当前用户
        checkPolicyBelongToUser(beneficiaryInfo.getPolicyId(), userId);

        // 执行删除操作
        int deleteResult = policyMapper.deleteBeneficiaryInfo(beneficiaryId);

        if (deleteResult <= 0) {
            throw new BizException("删除受益人信息失败");
        }

        log.info("受益人信息删除成功，受益人ID：{}", beneficiaryId);
    }
}
