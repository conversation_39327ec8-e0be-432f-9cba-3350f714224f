package com.sub.baoxian.productInfoService.Ao;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class InsurancePostAo {
    private Integer id;
    private String productCode;
    private String productName;
    private String introduction;
    private JSONArray productFeature;
    private String productBasic;
    private JSONObject interestRate;
}
