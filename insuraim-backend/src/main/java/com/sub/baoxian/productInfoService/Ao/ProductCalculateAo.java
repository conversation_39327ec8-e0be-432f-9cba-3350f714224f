package com.sub.baoxian.productInfoService.Ao;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductCalculateAo {
    private Integer productId;
    private String code;
    private String name;
    private String download;
    private JSONArray IRR;
    private JSONArray dividendRealizationRate;
    private Integer MaxYear;
    private JSONObject dynamicIRR;
    private JSONObject dynamicIRRWithoutInterest;
}
