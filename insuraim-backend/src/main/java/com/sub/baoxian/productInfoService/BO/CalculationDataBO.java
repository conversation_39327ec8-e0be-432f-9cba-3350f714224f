package com.sub.baoxian.productInfoService.BO;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CalculationDataBO {

        //产品名称
        @JsonProperty("productName")
        private String productName;
        //假设年龄
        @JsonProperty("assumedAge")
        @JsonAlias({"years", "age"})
        private Integer age;
        //性别
        @JsonProperty("gender")
        private String gender;
        //吸烟状态
        @JsonProperty("smoking_status")
        private String smokingStatus;
        //保单货币
        @JsonProperty("policyCurrency")
        private String policyCurrency;
        //缴费方式
        @JsonProperty("paymentMode")
        private String paymentMode;
        //缴费年期
        @JsonProperty("premiumPaymentTerm")
        private String premiumPaymentTerm;
        //保障期限
        @JsonProperty("coverageTerm")
        private String coverageTerm;
        //保额
        @JsonProperty("coverageAmount")
        private Double coverageAmount;
        //年保费
        @JsonProperty("annualPremium")
        private Double annualPremium;
        //计划链接
        @JsonProperty("planLink")
        private String planLink;
        //演算数据
        @JsonProperty("list")
        private List<CalculationDataItemVO> calculationDataItemVOList;

        @Data
        public static class CalculationDataItemVO {
            //年龄
            @JsonProperty("age")
            private Integer age;
            //保单年度
            @JsonProperty("year")
            private Integer year;
            //总保费
            @JsonProperty("totalPremum")
            private Double totalPremium;
            // 净现金流
            @JsonProperty("netflow")
            private Double netCashFlow;
            // 保证退保金额
            @JsonProperty("insuredAmount")
            @JsonAlias("insured_amount, insuredAmount")
            private Double insuredAmount;
            // 保证周年红利及利息
            @JsonProperty("cashValueOfReversionaryBonusNg")
            private Double cashValueOfReversionaryBonusNg;
            // 年金收入
            @JsonProperty("totalMonthlyAnnuityAndInterest")
            private Double totalMonthlyAnnuityAndInterest;
            //非保证终年红利
            @JsonProperty("totalWithdrawableCashAndInterest")
            private Double totalWithdrawableCashAndInterest;

            @JsonProperty("terminalBonusNg")
            private Double TerminalBonusNg;
            // 退保总额
            @JsonProperty("surrender")
            private Double surrender;
            // 身故保证金额
            @JsonProperty("deathBenefitG")
            @JsonAlias({"deathBenefitG", "death_benefit_g"})
            private Double deathBenefitG;
            // 身故终期红利
            @JsonProperty("deathBenefitTerminalBonusNg")
            @JsonAlias({"deathBenefitTerminalBonus_ng", "death_benefit_terminal_bonus_ng"})
            private Double deathBenefitTerminalBonusNg;
            // 身故退保金额
            @JsonProperty("deathBenefitTotal")
            @JsonAlias({"deathBenefitTotal", "death_benefit_total"})
            private Double deathBenefitTotal;
            // IRR
            @JsonProperty("IRR")
            private Double IRR;
            // 单利
            @JsonProperty("singleton")
            private Double singleInterest;
        }
}
