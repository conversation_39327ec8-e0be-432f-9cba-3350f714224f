package com.sub.baoxian.productInfoService.DTO;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProfitCalculationRequestDTO {
    @NotNull
    private Integer productId;
    private List<DiscountDTO> discounts;
    private Double premium;
    private Integer startAge;
    private String type;
    private Double yearShareRate;
    private Double realizationRate;
    private Integer paymentTerm;
    private String currency;
}
