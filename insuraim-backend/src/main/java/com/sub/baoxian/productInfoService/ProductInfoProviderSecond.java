package com.sub.baoxian.productInfoService;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.mapper.ProductDao;
import com.sub.baoxian.productInfoService.Ao.InsurancePostAo;
import com.sub.baoxian.productInfoService.Ao.RealizationRateAo;
import com.sub.baoxian.productInfoService.entity.InsurancePostPo;
import com.sub.baoxian.productInfoService.entity.ProductIntroPO;
import com.sub.baoxian.productInfoService.entity.RealizationRatePo;
import com.sub.baoxian.productInfoService.entity.RealizedRateStarPo;
import com.sub.baoxian.productInfoService.utils.PageResult;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class ProductInfoProviderSecond {
    @Autowired
    private ProductDao productDao;

    public InsurancePostAo getPostGeneratorInfo(Integer id) {
        InsurancePostPo insurancePostPo = productDao.getPostInfoOfInsurance(id);
        InsurancePostAo ao = new InsurancePostAo();
//        BeanUtil.copyProperties(ao,  insurancePostPo);
        ao.setId(insurancePostPo.getId());
        ao.setProductCode(insurancePostPo.getProductCode());
        ao.setProductName(insurancePostPo.getProductName());
        ao.setProductBasic(insurancePostPo.getProductBasic());
        ao.setIntroduction(insurancePostPo.getIntroduction());
        ao.setProductFeature(Optional.ofNullable(insurancePostPo.getProductFeature())
                .filter(s -> !s.isEmpty())
                .map(JSONArray::parseArray)
                .orElse(null));

        ao.setInterestRate(Optional.ofNullable(insurancePostPo.getInterestRate())
                .filter(s -> !s.isEmpty())
                .map(jsonStr -> {
                    JSONObject fullJson = JSONObject.parseObject(jsonStr);
                    JSONArray fullList = fullJson.getJSONArray("list");

                    // 获取 premiumPaymentTerm 的数值
                    String termStr = fullJson.getString("premiumPaymentTerm");
                    int term = 0;
                    try {
                        term = Integer.parseInt(termStr.replaceAll("\\D+", ""));
                    } catch (NumberFormatException ignored) {
                        // 如果解析失败，默认按“其他”处理
                        term = -1;
                    }
                    JSONArray filteredList = getObjects(term, fullList);

                    // 构造新的 JSON 返回
                    JSONObject result = new JSONObject();
                    result.put("productName", fullJson.getString("productName"));
                    result.put("years",  fullJson.getString("years"));
                    result.put("gender",  fullJson.getString("gender"));
                    result.put("smoking_status",  fullJson.getString("smoking_status"));
                    result.put("policyCurrency",  fullJson.getString("policyCurrency"));
                    result.put("paymentMode",  fullJson.getString("paymentMode"));
                    result.put("coverageTerm",  fullJson.getString("coverageTerm"));
                    result.put("premiumPaymentTerm", termStr); // 原样保留缴费期限
                    result.put("annualPremium",  fullJson.getString("annualPremium"));
                    result.put("name",  fullJson.getString("name"));
                    result.put("list", filteredList);
                    return result;
                })
                .orElse(null));

        return ao;
    }
    @NotNull
    private static JSONArray getObjects(int term, JSONArray fullList) {
        JSONArray filteredList = new JSONArray();
        int loop=0;
        for (Object obj : fullList) {
            JSONObject item = (JSONObject) obj;
            int year = item.getIntValue("year");
            int totalPremum = item.getIntValue("totalPremum");
            int surrender = item.getIntValue("surrender");

            // 判断是否符合展示条件（term 控制）
            boolean isMatchedYear = false;
            if (term != -1) {
                if (term < 15) {
                    isMatchedYear = year <= 15|| year % 10 == 0; // 展示前15年所有数据
                } else {
                    isMatchedYear = year <= term|| year % 10 == 0; // 展示到 term 年的所有数据
                }
            } else {
                isMatchedYear = year <= 15; // 默认展示前15年
            }

            if (isMatchedYear) {
                // 计算 multiple
                double ratio = (double) surrender / totalPremum;
                int multiple = (int) Math.floor(ratio);
                if(multiple!=loop) {
                    item.put("multiple", multiple);
                    loop = multiple;
                } else {
                    item.put("multiple", 0);
                }
                // 不管 multiple 是否大于 0，都添加这条记录
                filteredList.add(item);
            }
        }

        return filteredList;
    }

    public PageResult<InsurancePostPo> getBasicInfoOfPostInsurance(Integer pageNum, Integer pageSize, String search) {
        Page<ProductIntroPO> page = new Page<>(pageNum, pageSize);
        IPage<InsurancePostPo> resultPage = productDao.displayAvailablePost(page, search);

        return new PageResult<>(resultPage);
    }

    public PageResult<RealizationRatePo> getRealizationRateProductName(Integer pageNum, Integer pageSize) {
        Page<RealizationRatePo> page = new Page<>(pageNum, pageSize);
        IPage<RealizationRatePo> resultPage = productDao.getRealizationProductsName(page);


        return new PageResult<>(resultPage);
    }

    public List<RealizedRateStarPo> getRealizationRateProductStar() {
        // 从数据库获取所有记录
        List<RealizationRatePo> allPoRecords = productDao.getDividendProductStar();
        List<RealizedRateStarPo> resultList = new ArrayList<>();
        // 按產品名稱分組
        Map<String, List<RealizationRatePo>> productRatesMap = new HashMap<>();
        for (RealizationRatePo data : allPoRecords) {
            productRatesMap
                    .computeIfAbsent(data.getProductName(), k -> new ArrayList<>())
                    .add(data);
        }

        RealizedRateStarPo topProduct2022 = null;
        RealizedRateStarPo topProduct2023 = null;
        double maxRatio2022 = 1;
        double maxRatio2023 = 1;
        int idCounter = 1;

        for (Map.Entry<String, List<RealizationRatePo>> entry : productRatesMap.entrySet()) {
            String productName = entry.getKey();
            List<RealizationRatePo> dataList = entry.getValue();

//            long over100Count2022 = dataList.stream()
//                    .filter(d -> {
//                        try {
//                            int rate = Integer.parseInt(d.getDividendRealizationRate2022());
//                            return rate >= 100;
//                        } catch (Exception e) {
//                            return false;
//                        }
//                    })
//                    .count();

            long over100Count2023 = dataList.stream()
                    .filter(d -> {
                        try {
                            int rate = Integer.parseInt(d.getDividendRealizationRate2023());
                            return rate >= 100;
                        } catch (Exception e) {
                            return false;
                        }
                    })
                    .count();

            long totalCount = dataList.size();

            if (totalCount == 0) continue;

//            double ratio2022 = (double) over100Count2022 / totalCount;
            double ratio2023 = (double) over100Count2023 / totalCount;

//            if (ratio2022 == maxRatio2022) {
//                maxRatio2022 = ratio2022;
////                String rateString = over100Count2022 + "/" + totalCount;
//                RealizedRateStarPo po = new RealizedRateStarPo();
//                po.setId(idCounter++);
//                po.setProductName(productName);
//                po.setTopStar2022("100%");
////                topProduct2022 = po;
//                resultList.add(po);
//            }
            if (ratio2023 == maxRatio2023) {

                maxRatio2023 = ratio2023;
                String rateString = over100Count2023 + "/" + totalCount;
                RealizedRateStarPo po = new RealizedRateStarPo();
                po.setId(idCounter++);
                po.setProductName(productName);
                po.setTopStar2023("100%");
//                topProduct2023= po;
                resultList.add(po);
            }
        }



        return resultList;
    }

    public IPage<RealizationRateAo> getProductRealizationDetailsRe(Integer pageNum, Integer pageSize, String search,String insurer, String region) {
        // 1. 查询出所有符合条件的原始数据，不再进行数据库物理分页
        List<RealizationRatePo> allPoRecords = productDao.getDetailsOfDividendProduct(search,insurer,region);

        // 2. 按产品名进行分组 (这部分逻辑和您原来的一样)
        Map<String, List<RealizationRatePo>> grouped = allPoRecords.stream()
                .collect(Collectors.groupingBy(RealizationRatePo::getProductName));

        // 3. 将分组后的数据转换为 AO 列表 (这部分逻辑也和原来一样)
        List<RealizationRateAo> aoList = new ArrayList<>();
        for (Map.Entry<String, List<RealizationRatePo>> entry : grouped.entrySet()) {
            String name = entry.getKey();
            List<RealizationRatePo> poList = entry.getValue();

            RealizationRateAo ao = new RealizationRateAo();
            ao.setProductName(name);
            List<JSONObject> infoList = poList.stream().map(po -> {
                JSONObject info = new JSONObject();
                // ... (您原来的转换逻辑不变)
                info.put("year", po.getIssueYear());
                info.put("type", po.getDividendType());
                info.put("currency", po.getCurrency());
                info.put("rate2022", po.getDividendRealizationRate2022() == null ? "" : po.getDividendRealizationRate2022());
                info.put("rate2023", po.getDividendRealizationRate2023() == null ? "" : po.getDividendRealizationRate2023());
                return info;
            }).collect(Collectors.toList());

            JSONObject data = new JSONObject();
            data.put("info", infoList);
            ao.setData(data);
            aoList.add(ao);
        }

        // 4. 对已经分组完成的 aoList 进行手动内存分页
        Page<RealizationRateAo> resultPage = new Page<>(pageNum, pageSize);
        // 设置总记录数为分组后的产品总数
        resultPage.setTotal(aoList.size());

        // 计算当前页的起始索引
        int start = (int) resultPage.offset(); // pageNum-1 * pageSize
        // 计算当前页的结束索引 (不能超过列表总大小)
        int end = Math.min((start + pageSize), aoList.size());

        // 如果起始索引大于等于列表大小，说明请求的页码超出了范围，返回空列表
        if (start >= aoList.size()) {
            resultPage.setRecords(Collections.emptyList());
        } else {
            // 使用 subList 截取当前页的数据
            resultPage.setRecords(aoList.subList(start, end));
        }

        return resultPage;
    }



    public IPage<RealizationRateAo> getProductRealizationDetails(Integer pageNum, Integer pageSize,String search) {
        Page<ProductIntroPO> page = new Page<>(pageNum, pageSize);
        IPage<RealizationRatePo> resultPage = productDao.getProductRealizationRateByName(page, search);



        // 按产品名分组
        Map<String, List<RealizationRatePo>> grouped = resultPage.getRecords().stream()
                .collect(Collectors.groupingBy(RealizationRatePo::getProductName));

        List<RealizationRateAo> aoList = new ArrayList<>();

        for (Map.Entry<String, List<RealizationRatePo>> entry : grouped.entrySet()) {
            String name = entry.getKey();
            List<RealizationRatePo> poList = entry.getValue();

            RealizationRateAo ao = new RealizationRateAo();
            ao.setProductName(name);

            List<JSONObject> infoList = poList.stream().map(po -> {
                JSONObject info = new JSONObject();
                String year = po.getIssueYear();
                String type = po.getDividendType(); // 红利类型
                String currency = po.getCurrency();
                String rate2022 = po.getDividendRealizationRate2022() == null ? "" : po.getDividendRealizationRate2022();
                String rate2023 = po.getDividendRealizationRate2023() == null ? "" : po.getDividendRealizationRate2023();
                info.put("year", year);
                info.put("type", type);
                info.put("currency", currency);
                info.put("rate2022", rate2022);
                info.put("rate2023", rate2023);
                return info;

            }).collect(Collectors.toList());

            JSONObject data = new JSONObject();
            data.put("info", infoList);
            ao.setData(data);
            aoList.add(ao);
        }

        Page<RealizationRateAo> aoPage = new Page<>(pageNum, pageSize);
        aoPage.setTotal(resultPage.getTotal());
        aoPage.setRecords(aoList);
        return aoPage;
    }


}
