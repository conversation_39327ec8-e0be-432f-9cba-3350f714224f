package com.sub.baoxian.productInfoService;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

import com.sub.baoxian.productInfoService.entity.ProductCalDisplayPo;
import com.sub.baoxian.productInfoService.utils.FinancialCalculator;
import com.sub.baoxian.productInfoService.utils.FinancialCalculatorV2;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.sub.baoxian.mapper.ProductDao;
import com.sub.baoxian.model.entity.ProductPo;
import com.sub.baoxian.productInfoService.Ao.ProductCalculateAo;
import com.sub.baoxian.productInfoService.Ao.ProductIntroAO;
import com.sub.baoxian.productInfoService.DTO.ProductDisplayDTO;
import com.sub.baoxian.productInfoService.entity.ProductAttributeAO;
import com.sub.baoxian.productInfoService.entity.ProductAttributePo;
import com.sub.baoxian.productInfoService.entity.ProductIntroPO;
import com.sub.baoxian.productInfoService.utils.NumberUtils;
import com.sub.baoxian.productInfoService.utils.PageResult;
import com.sub.baoxian.service.product.ProductTransformer;

@Service
public class ProductInfoProviderService {

    @Autowired
    ProductDao productDao;
    @Autowired
    ProductTransformer productTransformer;
    @Autowired
    FinancialCalculator financialCalculator;
    @Autowired
    NumberUtils numberUtils;


    public ObjectNode getInfoOfProducts(Integer id) throws JsonProcessingException {
        ProductPo product  =productDao.getProductById(id);

        return productTransformer.getStringToJson(product);
    }
    //展示所有产品基本信息
    public PageResult<ProductIntroPO> getBasicInfoOfProducts(Integer pageNum, Integer pageSize, String search, String mainType, String region,String insurer) throws JsonProcessingException {

        Page<ProductIntroPO> page = new Page<>(pageNum, pageSize);
        IPage<ProductIntroPO> resultPage =productDao.getProductDisplay(page, search,mainType,region,insurer);
        return new PageResult<>(resultPage);
    }

    public ProductIntroAO getIntroInfoOfProducts(Integer productId) throws JsonProcessingException {
        ProductIntroPO poList = productDao.getProductIntro(productId);
        ProductIntroAO a = new ProductIntroAO(); // 每次新建对象
        a.setId(poList.getId());
        a.setProductId(poList.getProductId());
        a.setName(poList.getName());
        a.setProductIntro(JSONObject.parseObject(poList.getProductIntro())); // 转换为 JSONObject
        a.setDownload(poList.getDownload());
        a.setImageAddress(poList.getImageAddress());
        a.setNote(poList.getNote());
        return a;
    }

    public List<String> productCategoryList() throws JsonProcessingException {
        return productDao.productCategoryList();
    }
    public List<ProductAttributeAO> getSingleAttributeOfProduct(Integer productId) throws JsonProcessingException {

        return productDao.getInsuranceCode(productId);
    }
    public List<ProductAttributeAO> getDownloadOfProduct(Integer productId) throws JsonProcessingException {
        return productDao.getInsuranceDownload(productId);
    }
    public ProductAttributeAO getIRRAndDividendOfProduct(Integer productId) throws JsonProcessingException {
        ProductAttributeAO pa = new ProductAttributeAO();

        ProductAttributePo info =productDao.getProductIRRAndDividend(productId);
        BeanUtils.copyProperties(pa, info);
        JSONObject intro= JSON.parseObject(info.getProductIntro());
        JSONArray IrrArray = JSON.parseArray(info.getIRR());
        JSONArray dividendArray = JSON.parseArray(info.getDividendRealizationRate());
        pa.setProductIntro(intro);
        pa.setIRR(IrrArray);
        pa.setDividendRealizationRate(dividendArray);

        return pa;
    }


    private static JSONArray getJsonObjectDividendRealizationRate(JSONArray array, JSONObject data) {
        JSONArray dataArray = new JSONArray();

        for (int i=0; i<array.size(); i++) {
            JSONObject dataObject = new JSONObject();
           String year = array.getJSONObject(i).getString("year");
           String percentage = array.getJSONObject(i).getString("percentage");
            dataObject.put("year", year);
            dataObject.put("percentage", percentage);
            dataArray.add(dataObject);
        }
        return dataArray;
    }

    private static JSONObject getJsonObjectIRRCal(JSONObject item, Integer loop, Integer StartYear) {

        JSONObject data = new JSONObject();
        String productName = item.getString("productName");
        data.put("productName",  productName);
        String years = item.getString("years");
        data.put("years",  years);
        String gender = item.getString("gender");
        data.put("gender",  gender);
        String smoking_status = item.getString("smoking_status");
        data.put("smoking_status",  smoking_status);
        String policyCurrency = item.getString("policyCurrency");
        data.put("policyCurrency",  policyCurrency);
        String paymentMode = item.getString("paymentMode");
        data.put("paymentMode",  paymentMode);
        String premiumPaymentTerm = item.getString("premiumPaymentTerm");
        data.put("premiumPaymentTerm",  premiumPaymentTerm);
        String coverageTerm = item.getString("coverageTerm");
        data.put("coverageTerm",  coverageTerm);
        String annualPremium = item.getString("annualPremium");
        data.put("annualPremium",  annualPremium);
        String name = item.getString("name");
        data.put("name",  name);

        JSONArray list = item.getJSONArray("list");

        JSONArray dataArray = new JSONArray();
        int multipleCount =0;
        if(loop <=0 ) {
            loop =1;
        } else if(loop > list.size()) {
            loop = list.size();
        }
        for (int j = 0; j < loop; j++) {

            JSONObject itemObject = list.getJSONObject(j);
            JSONObject dataList = getJsonObject(itemObject,StartYear);
            int tem = (int)dataList.get("multiple");
            if(multipleCount !=tem) {
                dataList.put("multiple",tem);
                multipleCount = tem;
            } else {
                dataList.put("multiple",0);
            }
            dataArray.add(dataList); // 把每个 dataList 加入数组中
        }
        data.put("list", dataArray); // 最后一次性放进 JSON 对象里


        return data;
    }
    public JSONObject getProcessLifeIRR(JSONObject data, BigDecimal premium,  Integer loop, Integer startAge) {
        JSONObject result = new JSONObject();

        String productName = data.getString("productName");
        result.put("productName",  productName);
//        String age = data.getString("assumedAge");
        result.put("assumedAge",  startAge);
        String gender = data.getString("gender");
        result.put("gender",  gender);
        String smoking_status = data.getString("smoking_status");
        result.put("smoking_status",  smoking_status);
        String policyCurrency = data.getString("policyCurrency");
        result.put("policyCurrency",  policyCurrency);
        String paymentMode = data.getString("paymentMode");
        result.put("paymentMode",  paymentMode);
        String premiumPaymentTerm = data.getString("premiumPaymentTerm");
        result.put("premiumPaymentTerm",  premiumPaymentTerm);


        String coverageTerm = data.getString("coverageTerm");
        result.put("coverageTerm",  coverageTerm);
        String annualPremium = data.getString("annualPremium");
        result.put("annualPremium",  premium);
        String name = data.getString("name");
        result.put("name",  name);

        int PaymentTermInt =0;
        if(!Objects.equals(premiumPaymentTerm, "整付保費")) {
            PaymentTermInt = NumberUtils.parseToInt(premiumPaymentTerm);
            System.out.println(annualPremium);
        } else {
            PaymentTermInt =1;
        }

        BigDecimal annualPremiumDecimal = NumberUtils.parseToBigDecimal(annualPremium);
        BigDecimal factor = premium.divide(annualPremiumDecimal, 6, RoundingMode.HALF_UP);

        System.out.println("Factor:"+factor);
        JSONArray list = data.getJSONArray("list");

        JSONArray dataArray = new JSONArray();

        if(loop <=0 ) {
            loop =1;
        } else if(loop > list.size()) {
            loop = list.size();
        }
        BigDecimal totalPremium = new BigDecimal(0);
        List<Double> netOutflows = new ArrayList<>();
        List<Integer>  years = new ArrayList<>();

        // 👇 把这两个变量放在这里！
        int lastRecordedMultiple = 0;

        for (int j = 0; j < loop; j++) {

            JSONObject itemObject = list.getJSONObject(j);
            if( PaymentTermInt!=0) {
                //放入新的保费作为净流出
                itemObject.put("netflow",-premium.doubleValue());

                totalPremium  = totalPremium.add(premium);
                itemObject.put("totalPremum",totalPremium);

                netOutflows.add(-premium.doubleValue());
//                System.out.println(netOutflows);
                PaymentTermInt--;
            } else {
                itemObject.put("netflow",0);
                itemObject.put("totalPremum",totalPremium);
                netOutflows.add(0.0);
            }

            //计算新的退保价值
            String surrenderStr = itemObject.getString("surrender");
            BigDecimal surrenderValue = new BigDecimal(surrenderStr);
            BigDecimal newSurrender = surrenderValue.multiply(factor).setScale(2, RoundingMode.HALF_UP);
//            System.out.println("NewSurrender:"+newSurrender);
            itemObject.put("surrender",newSurrender);
            //计算IRR
            netOutflows.add(newSurrender.doubleValue());
            Double IRR= FinancialCalculatorV2.calculateIRR(netOutflows);
            System.out.println("IRR:"+IRR);
            itemObject.put("IRR",IRR);

//            System.out.println("------");
//            System.out.println(netOutflows);
            netOutflows.remove(netOutflows.size() - 1);
//            System.out.println(netOutflows);
//            System.out.println("------");
            //计算单利

            Integer year = itemObject.getInteger("year");
            years.add(year);
//            System.out.println("-------");
            System.out.println(totalPremium);
            System.out.println(surrenderValue);
            Double singleton = FinancialCalculatorV2.calculateSimpleInterestRate(newSurrender.doubleValue(), netOutflows, years, year);
            itemObject.put("singleton",singleton);
            itemObject.put("age",  year+startAge);

            // 计算 multiple
            BigDecimal multiple = newSurrender.divide(totalPremium, 2, RoundingMode.HALF_UP);
            int floorValue = multiple.intValue(); // 向下取整，例如：1.9 -> 1, 2.0 -> 2

            int putValue = 0;

            if (floorValue > lastRecordedMultiple) {
                putValue = floorValue;
                lastRecordedMultiple = floorValue;
            }

            itemObject.put("multiple", putValue);
            dataArray.add(itemObject); // 把每个 dataList 加入数组中
        }
        result.put("list", dataArray); // 最后一次性放进 JSON 对象里

        return result;
    }
    @NotNull
    private static JSONObject getJsonObject(JSONObject item, Integer startAge) {
        JSONObject dataList = new JSONObject();

        String year = item.getString("year");
        dataList.put("year", year);
        Integer age  =  startAge+ Integer.parseInt(year);
        dataList.put("age", age);
        String totalPremum = item.getString("totalPremum");
        dataList.put("totalPremum", totalPremum);
        String netflow = item.getString("netflow");
        dataList.put("netflow", netflow);
        String surrender = item.getString("surrender");
        dataList.put("surrender", surrender);
        String singleton = item.getString("singleton");
        dataList.put("singleton", singleton);
        String IRR = item.getString("IRR");
        dataList.put("IRR", IRR);

        float currentFee = item.getFloat("totalPremum");
        currentFee = Math.max(item.getFloat("totalPremum"), currentFee);
        float cashValue = item.getFloat("surrender");

        dataList.put("multiple",(int)(cashValue/currentFee));
        return dataList;
    }
    //展示到特殊年份的IRR非储蓄类
    public ProductCalculateAo getSpecificYearIrr(Integer productId, Integer holdYear, Integer startAge) {
        ProductCalculateAo pa = new ProductCalculateAo();

        ProductAttributePo info =productDao.getProductIRRAndDividendDynamic(productId,startAge);
        if(info !=null) {
            BeanUtils.copyProperties(info,pa);
            JSONObject IrrArray = JSON.parseObject(info.getIRR());

            if(IrrArray.isEmpty()) {
                pa.setDynamicIRR(new JSONObject());
            }
            if(info.getIRRWithoutInterest() !=null) {
                JSONObject IrrWithoutInterest = JSON.parseObject(info.getIRRWithoutInterest());
                JSONObject rrArrayWithoutInterest = getJsonObjectIRRCal(IrrWithoutInterest ,holdYear,startAge);
                pa.setDynamicIRRWithoutInterest(rrArrayWithoutInterest);
            }
            JSONObject rrArray = getJsonObjectIRRCal(IrrArray ,holdYear,startAge);
            pa.setDynamicIRR(rrArray);
            return pa;
        }
        return pa;
    }
    //动态计算储蓄类的IRR
    public ProductCalculateAo getLifeProduct(Integer productId, BigDecimal premium,Integer holdYear, Integer startAge) {

        ProductCalculateAo pa = new ProductCalculateAo();
        //动态计算不需要年龄
        JSONObject jo =JSONObject.parseObject(productDao.getDynamicIRR(productId));
        if(jo != null) {
            JSONObject result =  getProcessLifeIRR(jo,premium, holdYear,startAge);
            pa.setDynamicIRR(result);
            return pa;
        }
        return pa;
    }
    //收益演算的接口
    public ProductCalculateAo getProfitCalculate(Integer productId, BigDecimal premium, Integer holdYear,Integer startAge) {
        List<Integer> lifeProductIds = productDao.lifeProductIds();
        ProductCalculateAo pa = new ProductCalculateAo();
        if(lifeProductIds.contains(productId)) {
            pa =getLifeProduct(productId,premium,holdYear,startAge);
        } else {
            List<Integer> targetAges = Arrays.asList(20, 25, 40);
            int nearestAge = targetAges.stream()
                    .min(Comparator.comparingInt(age -> Math.abs(startAge - age)))
                    .orElse(20); // 默认值可选

            pa = getSpecificYearIrr(productId,holdYear,nearestAge);
        }
        return pa;
    }

    //IRR产品展示
    public PageResult<ProductIntroPO> getBasicInfoOfIRRProducts(ProductDisplayDTO productDisplayDTO) throws JsonProcessingException {
        Page<ProductIntroPO> page = new Page<>(productDisplayDTO.getPageNum(), productDisplayDTO.getPageSize());
        IPage<ProductIntroPO> resultPage =productDao.getProductDisplayIRR(page, productDisplayDTO);
        return new PageResult<>(resultPage);
    }

    public PageResult<ProductCalDisplayPo> ProductsOfCashValue(ProductDisplayDTO productDisplayDTO) throws JsonProcessingException {
        Page<ProductCalDisplayPo> page = new Page<>(productDisplayDTO.getPageNum(), productDisplayDTO.getPageSize());
        IPage<ProductCalDisplayPo> resultPage =productDao.getProductDisplayIRR2(page, productDisplayDTO);
        return new PageResult<>(resultPage);
    }


    public List<String> productCurrencyList() {
        return productDao.getIRRProductCurrency();
    }
}
