package com.sub.baoxian.productInfoService;

import com.sub.baoxian.mapper.ProductCalMapper;
import com.sub.baoxian.mapper.ProductDao;
import com.sub.baoxian.productInfoService.BO.CalculationDataBO;
import com.sub.baoxian.productInfoService.DTO.DiscountDTO;
import com.sub.baoxian.productInfoService.DTO.ProfitCalculationRequestDTO;
import com.sub.baoxian.productInfoService.entity.FileLinkPo;
import com.sub.baoxian.productInfoService.utils.FinancialCalculationsV3;
import com.sub.baoxian.productInfoService.utils.FinancialCalculator;
import com.sub.baoxian.productInfoService.utils.NumberUtils;
import com.sub.baoxian.productInfoService.vo.CalculationDataVO;
import cn.hutool.core.bean.BeanUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SavingDynamicCalService {
    @Autowired
    ProductCalMapper calMapper;
    @Autowired
    ProductDao productDao;
    @Autowired
    NumberUtils numberUtils;
    List<String> keywordsToRemove = List.of(
            "退保收益", "紅利累積生息", "全數提取紅利", "積存生息", "現金提取"
    );
    public CalculationDataVO getDynamicSaving(List<DiscountDTO> list, Double premium, Integer age, CalculationDataBO lit) {

        CalculationDataVO vo =  new CalculationDataVO();
//
        Double factorData = premium/lit.getAnnualPremium();
        //基础信息设置
        vo.setProductName(lit.getProductName());
        vo.setAge(age);
        vo.setGender(lit.getGender());
        vo.setSmokingStatus(lit.getSmokingStatus());
        vo.setPolicyCurrency(lit.getPolicyCurrency());
        vo.setPaymentMode(lit.getPaymentMode());
        vo.setPremiumPaymentTerm(lit.getPremiumPaymentTerm());
        vo.setCoverageTerm(lit.getCoverageTerm());
        if(lit.getCoverageAmount() != null) {
            vo.setCoverageAmount(lit.getCoverageAmount()*factorData);
        }
        vo.setAnnualPremium(premium);

        List<Double> netflow = new ArrayList<>();
        List<Double> insuredNet = new ArrayList<>();
        List<Integer> years = new ArrayList<>();
        List<CalculationDataVO.CalculationBaseDataItemBO> result = new ArrayList<>();

        int paymentTerm = NumberUtils.parseToInt(lit.getPremiumPaymentTerm());
        double totalPremium = 0.0;
        for(int i =0; i < lit.getCalculationDataItemVOList().size(); i++) {
            CalculationDataBO.CalculationDataItemVO item = lit.getCalculationDataItemVOList().get(i);
            CalculationDataVO.CalculationBaseDataItemBO itemVO = new CalculationDataVO.CalculationBaseDataItemBO();
            double originalPremium = premium;
            double newPremium = originalPremium; // 每次都从原始保费开始

            for (DiscountDTO discount : list) {
                if (i == discount.getYear()-1) {
                    newPremium = originalPremium * (1 - discount.getRate());
                    System.out.println("----------");
                    System.out.println("Applying discount for year " + i + ": " + newPremium);
                }
            }
            itemVO.setYear(item.getYear());
            itemVO.setAge(age+item.getYear());
            years.add(item.getYear());


            
//            设置展示现金流
            if(i<paymentTerm) {
                totalPremium += newPremium;
                itemVO.setNetCashFlow(-newPremium);
            } else {
                itemVO.setNetCashFlow(0.0);
            }
            itemVO.setTotalPremium(totalPremium);
            
            //设置终期红利
            if(item.getTerminalBonusNg()!=null) {

                itemVO.setTerminalBonusNg(item.getTerminalBonusNg()*factorData);
            }
            //设置累计提现金额和利息
            if(item.getTotalMonthlyAnnuityAndInterest() !=null) {
                itemVO.setTotalMonthlyAnnuityAndInterest(item.getTotalMonthlyAnnuityAndInterest()*factorData);
            }

            //设置新的复归红利
            Double cashValue = item.getCashValueOfReversionaryBonusNg();
            if (cashValue != null && cashValue != 0.0) {
                itemVO.setCashValueOfReversionaryBonusNg(cashValue * factorData);
            }

            //设置新的保证金额
            if(item.getInsuredAmount()!=null && item.getInsuredAmount() != 0.0) {
                itemVO.setInsuredAmount(item.getInsuredAmount()*factorData);
            } else {
                itemVO.setInsuredAmount(0.0);
            }

            //设置新的退保金额
            if(item.getSurrender()!=null && item.getSurrender() != 0.0) {
                itemVO.setSurrender(item.getSurrender()*factorData);
            }else {
                itemVO.setSurrender(0.0);
            }

            //加入总现金流
            netflow.add(itemVO.getNetCashFlow());
            insuredNet.add(itemVO.getNetCashFlow());

            //计算IRR - 创建当前年份的现金流副本
            List<Double> currentNetflow = new ArrayList<>(netflow);
            List<Double> currentInsuredNet = new ArrayList<>(insuredNet);
            
            currentNetflow.add(itemVO.getSurrender());
            currentInsuredNet.add(itemVO.getInsuredAmount());

            // 转换为BigDecimal用于FinancialCalculationsV3
            List<BigDecimal> netflowBd = currentNetflow.stream()
                    .map(BigDecimal::valueOf)
                    .collect(Collectors.toList());
            List<BigDecimal> insuredNetBd = currentInsuredNet.stream()
                    .map(BigDecimal::valueOf)
                    .collect(Collectors.toList());

            String irrStr = FinancialCalculationsV3.calculateIRR(netflowBd);
            itemVO.setIRR(irrStr);
//            System.out.println("Year " + i + " size"+currentNetflow.size()+", NetFlow: " + currentNetflow +"  IRR:" +irrStr);
            
            String insuredIrrStr = FinancialCalculationsV3.calculateIRR(insuredNetBd);
            itemVO.setInsuredIRR(insuredIrrStr);

            //计算单利
            // 转换为BigDecimal用于FinancialCalculationsV3
            List<BigDecimal> netflowBdSingle = netflow.stream()
                    .map(BigDecimal::valueOf)
                    .collect(Collectors.toList());
            List<BigDecimal> insuredNetBdSingle = insuredNet.stream()
                    .map(BigDecimal::valueOf)
                    .collect(Collectors.toList());
            
            String singleInterestStr = FinancialCalculationsV3.calculateSimpleInterest(
                    itemVO.getYear(), 
                    years, 
                    netflowBdSingle, 
                    BigDecimal.valueOf(itemVO.getSurrender())
            );
            itemVO.setSingleInterest(parseInterestRate(singleInterestStr));
            
            String insuredSingleInterestStr = FinancialCalculationsV3.calculateSimpleInterest(
                    itemVO.getYear(), 
                    years, 
                    insuredNetBdSingle, 
                    BigDecimal.valueOf(itemVO.getInsuredAmount())
            );
            itemVO.setInsuredSingleInterest(parseInterestRate(insuredSingleInterestStr));
            
            result.add(itemVO);
        }
        vo.setCalculationBaseDataItemBOList(result);
        return vo;
    }

    public CalculationDataVO.CalculationBaseDataItemBO calculateAndSetFinancialMetrics(
            CalculationDataVO.CalculationBaseDataItemBO itemVO,
            List<Double> baseNetPremiums,         // Original 'netflow'
            List<Integer> years                // Original 'years'

    ) {
        if (itemVO == null) {
            throw new IllegalArgumentException("ItemVO cannot be null.");
        }
        if (baseNetPremiums == null) {
            baseNetPremiums = Collections.emptyList();
        }

        // --- IRR Calculation (Standard) ---
        // Create a copy and add final surrender amount for IRR
        List<Double> currentNetflowForIRR = new ArrayList<>(baseNetPremiums);
        currentNetflowForIRR.add(itemVO.getSurrender());
        List<BigDecimal> netflowBdForIRR = toBigDecimalList(currentNetflowForIRR);

        String irrStr = FinancialCalculationsV3.calculateIRR(netflowBdForIRR);
//        System.out.println(currentNetflowForIRR);
//        System.out.println("IRR: " + irrStr);
        itemVO.setIRR(irrStr);

        // --- Simple Interest Calculation (Standard) ---
        // Uses the base premium list directly; surrender amount is a separate parameter.
        List<BigDecimal> netflowBdForSimpleInterest = toBigDecimalList(baseNetPremiums);

        String singleInterestStr = FinancialCalculationsV3.calculateSimpleInterest(
                itemVO.getYear(),
                years,
                netflowBdForSimpleInterest,
                BigDecimal.valueOf(itemVO.getSurrender())
        );
        itemVO.setSingleInterest(parseInterestRate(singleInterestStr));
//        System.out.println("Single Interest: " + singleInterestStr);
        return itemVO;
    }

    private List<BigDecimal> toBigDecimalList(List<Double> doubleList) {
        if (doubleList == null) {
            return Collections.emptyList();
        }
        return doubleList.stream()
                .map(d -> d == null ? BigDecimal.ZERO : BigDecimal.valueOf(d))
                .collect(Collectors.toList());
    }


    public CalculationDataVO getStaticProductData(CalculationDataBO lit) {
        CalculationDataVO vo =  new CalculationDataVO();
        vo.setProductName(lit.getProductName());
        vo.setAge(lit.getAge());
        vo.setGender(lit.getGender());
        vo.setSmokingStatus(lit.getSmokingStatus());
        vo.setPolicyCurrency(lit.getPolicyCurrency());
        vo.setPaymentMode(lit.getPaymentMode());
        vo.setPremiumPaymentTerm(lit.getPremiumPaymentTerm());
        vo.setCoverageTerm(lit.getCoverageTerm());
        vo.setCoverageAmount(lit.getCoverageAmount());
        vo.setAnnualPremium(lit.getAnnualPremium());
        List<CalculationDataVO.CalculationBaseDataItemBO> result = new ArrayList<>();
        for(CalculationDataBO.CalculationDataItemVO item : lit.getCalculationDataItemVOList()) {
            CalculationDataVO.CalculationBaseDataItemBO itemVO = new CalculationDataVO.CalculationBaseDataItemBO();
            BeanUtil.copyProperties(item, itemVO);
            itemVO.setIRR(String.valueOf(item.getIRR()));
            result.add(itemVO);
        }
        vo.setCalculationBaseDataItemBOList(result);
        return vo;
    }
    public CalculationDataVO classifyAgeGetProduct(ProfitCalculationRequestDTO profitCalculationRequestDTO){
        CalculationDataVO vo = new CalculationDataVO();
        List<Integer> lifeProductIds = productDao.lifeProductIds();
        Integer productId = profitCalculationRequestDTO.getProductId();
        String type = profitCalculationRequestDTO.getType();
        Integer startAge = profitCalculationRequestDTO.getStartAge();
        List<DiscountDTO> list = profitCalculationRequestDTO.getDiscounts();
        Double premium = profitCalculationRequestDTO.getPremium();
        if(lifeProductIds.contains(productId)) {
            CalculationDataBO lit =calMapper.profitCalculator(productId,20,type);
            if(premium==null) {
                premium = lit.getAnnualPremium();
            }
            if(lit !=null) {
                vo = getDynamicSaving(list,premium,startAge,lit);
            }
        } else {
            if (startAge == null) {
                // 可选择抛出异常或设定默认值
                throw new IllegalArgumentException("startAge 不能为空");
                // 或者设置默认值，比如：
                // startAge = 30; // 默认年龄
            }
            List<Integer> targetAges = Arrays.asList(20, 40);
            int nearestAge = targetAges.stream()
                    .min(Comparator.comparingInt(age -> Math.abs(startAge - age)))
                    .orElse(20); // 默认值可选\
            CalculationDataBO lit =calMapper.profitCalculator(productId,nearestAge,type);
            if(premium ==null) {
                premium = lit.getAnnualPremium();
            }
            if(lit!=null) {
                vo = getStaticProductData(lit);
            }
        }


        String cleanedName = vo.getProductName();
        for (String keyword : keywordsToRemove) {
            cleanedName = cleanedName.replace(keyword, "");
        }
        cleanedName = cleanedName.trim();
        vo.setProductName(cleanedName);
        if(profitCalculationRequestDTO.getRealizationRate() !=null && profitCalculationRequestDTO.getRealizationRate() !=1) {
            vo = dynamicCalRealizationRate(vo,profitCalculationRequestDTO.getRealizationRate());
        }
        markSurrenderMultiples(vo.getCalculationBaseDataItemBOList());
        return vo;
    }

    public CalculationDataVO dynamicCalRealizationRate(CalculationDataVO vo, double realizationRate) {
        List<CalculationDataVO.CalculationBaseDataItemBO> List = vo.getCalculationBaseDataItemBOList();
        List<CalculationDataVO.CalculationBaseDataItemBO> result = new ArrayList<>();
        List<Integer> years = new ArrayList<>();
        List<Double> netflow = new ArrayList<>();
        int loop =1 ;
        for(CalculationDataVO.CalculationBaseDataItemBO item : List) {
            double terminalBonus = item.getTerminalBonusNg() != null ? item.getTerminalBonusNg() : 0.0;
            double reversionaryBonus = item.getCashValueOfReversionaryBonusNg() != null ? item.getCashValueOfReversionaryBonusNg() : 0.0;
            double monthlyAnnuity = item.getTotalMonthlyAnnuityAndInterest() != null ? item.getTotalMonthlyAnnuityAndInterest() : 0.0;
            double newPremium = (terminalBonus + reversionaryBonus + monthlyAnnuity) * realizationRate;
            item.setCashValueOfReversionaryBonusNg(
                (item.getCashValueOfReversionaryBonusNg() != null ? item.getCashValueOfReversionaryBonusNg() : 0.0) * realizationRate
            );
            item.setTotalMonthlyAnnuityAndInterest(
                (item.getTotalWithdrawableCashAndInterest() != null ? item.getTotalWithdrawableCashAndInterest() : 0.0) * realizationRate
            );
            item.setTerminalBonusNg(
                (item.getTerminalBonusNg() != null ? item.getTerminalBonusNg() : 0.0) * realizationRate
            );
            item.setSurrender(newPremium);

            Integer year = item.getYear();
            years.add(year);

            netflow.add(item.getNetCashFlow());
//            System.out.println(loop);
            CalculationDataVO.CalculationBaseDataItemBO itemVO =calculateAndSetFinancialMetrics(item, netflow, years);
            result.add(itemVO);
            loop++;
        }
        vo.setCalculationBaseDataItemBOList(result);
        return vo;
    }

    /**
     * 解析利率字符串为Double值
     * @param rateStr 利率字符串，可能包含"N/A", "Infinity", "0.00"等
     * @return Double类型的利率值，如果无法解析则返回null
     */
    private Double parseInterestRate(String rateStr) {
        if (rateStr == null || rateStr.equals("N/A")) {
            return null;
        }
        if (rateStr.equals("Infinity")) {
            return Double.POSITIVE_INFINITY;
        }
        try {
            double value = Double.parseDouble(rateStr);
            // 保留4位小数
            return Math.round(value * 10000.0) / 10000.0;
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 标记每个年度退保金额首次超过保费n倍的multiple字段
     */
    private void markSurrenderMultiples(List<CalculationDataVO.CalculationBaseDataItemBO> items) {
        if (items == null) return;
        java.util.Set<Integer> markedMultiples = new java.util.HashSet<>();
        for (CalculationDataVO.CalculationBaseDataItemBO item : items) {
            double surrender = item.getSurrender() != null ? item.getSurrender() : 0.0;
            double totalPremium = item.getTotalPremium() != null ? item.getTotalPremium() : 0.0;
            int multiple = 0;
            if (totalPremium > 0) {
                int currentMultiple = (int) Math.floor(surrender / totalPremium);
                if (currentMultiple > 0 && !markedMultiples.contains(currentMultiple)) {
                    multiple = currentMultiple;
                    markedMultiples.add(currentMultiple);
                }
            }
            item.setMultiple(multiple);
        }
    }

    public List<FileLinkPo> getFileLink(String productName, String age, String company) {

        return calMapper.selectLink(productName, age,company);
    }
    public List<FileLinkPo> getCompany() {
        return calMapper.selectCompany();
    }
}
