package com.sub.baoxian.productInfoService.entity;

import lombok.Data;

@Data
public class InsuranceFeePo {
    private Integer id;
    private Integer productId;
    private String productName;
    private String c0;
    private String c1;
    private String c2;
    private String c3;
    private String c4;
    private String fixedAmount;
    private String fixedPremium;
    private String semiAnnualFactor;
    private String quarterlyFactor;
    private String monthlyFactor;
    private String type;
    private Integer maxAge;
    private Integer minAge;
    private Integer insuredRelated;
    private String region;
}
