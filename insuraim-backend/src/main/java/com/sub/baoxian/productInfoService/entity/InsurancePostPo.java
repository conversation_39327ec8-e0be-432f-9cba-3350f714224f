package com.sub.baoxian.productInfoService.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class InsurancePostPo {
    private Integer id;
    private String productCode;
    private String productName;
    private String introduction;
    private String productFeature;
    private String productBasic;
    private String interestRate;
}
