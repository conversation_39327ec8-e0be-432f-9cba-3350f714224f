package com.sub.baoxian.productInfoService.entity;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.google.gson.JsonObject;
import io.swagger.v3.core.util.Json;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProductAttributeAO implements Serializable {
    private Integer productId;
    private JSONObject productIntro;
    private String code;
    private String name;
    private String download;
    private JSONArray IRR;
    private JSONArray dividendRealizationRate;

}
