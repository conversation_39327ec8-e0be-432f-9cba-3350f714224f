package com.sub.baoxian.productInfoService.entity;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProductAttributePo {
    private Integer id;
    private Integer productId;
    private String productIntro;
    private List<String> code;
    private String name;
    private String download;

    private String IRR;
    private String IRRWithoutInterest;
    private String dividendRealizationRate;

}
