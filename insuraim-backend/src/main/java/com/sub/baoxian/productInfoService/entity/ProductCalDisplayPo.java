package com.sub.baoxian.productInfoService.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sub.baoxian.productInfoService.utils.ListSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
public class ProductCalDisplayPo {
    private Integer productId;
    private String productName;
//    @TableField(typeHandler = JacksonTypeHandler.class)
    @JsonSerialize(using = ListSerializer .class)
    private String currencies;
    private String companyName;
    private String categoryName;
//    @TableField(typeHandler = JacksonTypeHandler.class)
    @JsonSerialize(using = ListSerializer .class)
    private String paymentTerm;
    private String guaranteePeriod;

}
