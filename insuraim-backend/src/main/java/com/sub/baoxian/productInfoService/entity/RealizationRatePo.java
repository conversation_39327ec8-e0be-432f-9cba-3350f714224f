package com.sub.baoxian.productInfoService.entity;

import com.google.gson.JsonObject;
import io.swagger.v3.core.util.Json;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RealizationRatePo {
    private String productName;
    private String currency;
    private String dividendType;
    private String issueYear;
    private String dividendRealizationRate2022;
    private String dividendRealizationRate2023;
    private JsonObject data;
}
