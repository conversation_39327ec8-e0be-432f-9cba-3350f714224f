package com.sub.baoxian.productInfoService.utils;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

public class FinancialCalculationsV3 {

    // Define a MathContext for consistent precision and rounding.
    // DECIMAL128 provides 34 digits of precision, which is very high.
    private static final MathContext MC_PRECISION = new MathContext(34, RoundingMode.HALF_UP);
    private static final MathContext MC_FOR_DIVISION = new MathContext(50, RoundingMode.HALF_UP); // Higher for intermediate division

    // Helper class for discounts (equivalent to JS object literal)
    public static class Discount {
        public int year;
        public BigDecimal rate; // Percentage, e.g., 5.0 for 5%

        public Discount(int year, BigDecimal rate) {
            this.year = year;
            this.rate = rate;
        }

        public Discount(int year, String rateStr) {
            this.year = year;
            if (rateStr != null) {
                try {
                    this.rate = new BigDecimal(rateStr);
                } catch (NumberFormatException e) {
                    this.rate = null; // Or handle error appropriately
                }
            } else {
                this.rate = null;
            }
        }
    }

    // Helper function to calculate Net Present Value (NPV)
    public static BigDecimal NPV(BigDecimal rate, List<BigDecimal> cashFlows) {
        if (rate == null || cashFlows == null) {
            throw new IllegalArgumentException("Rate and cashFlows cannot be null.");
        }
        BigDecimal npv = BigDecimal.ZERO;
        // 1 + rate
        BigDecimal onePlusRate = BigDecimal.ONE.add(rate, MC_PRECISION);

        // Check if 1 + rate is zero or negative, which can cause issues with pow.
        // IRR typically means rate > -100% (-1.0), so 1+rate should be > 0.
        if (onePlusRate.compareTo(BigDecimal.ZERO) <= 0) {
            // This scenario usually indicates an issue with the rate or cash flows.
            // For NPV calculation, if rate = -1, and first cashflow is not 0, it's effectively infinite.
            // However, within IRR context, we search for rates > -1.
            // If it still happens, it implies no solution or a problematic rate guess.
            // Returning a very large or small number might guide the IRR search.
            // Or throw an exception for invalid rate.
            // For now, let's assume valid rate ranges are handled by IRR.
            // If a problematic rate is passed directly, behavior depends on cash flows.
        }


        for (int i = 0; i < cashFlows.size(); i++) {
            if (cashFlows.get(i) == null) {
                throw new IllegalArgumentException("Cash flow at index " + i + " cannot be null.");
            }
            // (1 + rate)^(i + 1)
            BigDecimal denominator;
            try {
                denominator = onePlusRate.pow(i + 1, MC_PRECISION);
            } catch (ArithmeticException e) {
                // This can happen if onePlusRate is zero and i+1 is negative,
                // or if onePlusRate is negative and i+1 is not an integer (not an issue here)
                // For IRR search, a rate of -1 (onePlusRate = 0) is problematic.
                // We might return a large number to steer the search away or throw.
                // For simplicity in translation, if denominator becomes non-positive or problematic:
                if (onePlusRate.compareTo(BigDecimal.ZERO) == 0 && cashFlows.get(i).compareTo(BigDecimal.ZERO) != 0) {
                    return cashFlows.get(i).signum() > 0 ? new BigDecimal("1E50") : new BigDecimal("-1E50"); // Effectively infinity
                }
                if (onePlusRate.compareTo(BigDecimal.ZERO) < 0) {
                    // NPV is not well-defined for rates < -100% for non-integer periods
                    // or if it makes the base of power negative for fractional exponents (not our case here).
                    // This condition should ideally be caught by IRR search bounds.
                    return new BigDecimal("NaN"); // Or throw, signal invalid rate
                }
                // If denominator is zero due to underflow with high powers (unlikely with BigDecimal default)
                // or if it's still zero for other reasons:
                return new BigDecimal("NaN"); // Indicate error
            }

            if (denominator.compareTo(BigDecimal.ZERO) == 0) {
                // Avoid division by zero. This typically happens if rate = -100%
                // If cashflow is non-zero, NPV is infinite.
                if (cashFlows.get(i).compareTo(BigDecimal.ZERO) != 0) {
                    // Return a very large number with the sign of the cash flow
                    // to indicate "infinite" NPV for the IRR algorithm's guidance
                    return cashFlows.get(i).signum() > 0 ? new BigDecimal("1E50") : new BigDecimal("-1E50");
                }
                // If cashflow is also zero, this term is zero.
                // npv remains unchanged, continue to next cash flow.
            } else {
                npv = npv.add(cashFlows.get(i).divide(denominator, MC_FOR_DIVISION), MC_PRECISION);
            }
        }
        return npv;
    }

    // Calculate IRR using an iterative method (Newton-Raphson and Bisection)
    public static String calculateIRR(List<BigDecimal> cashFlows, BigDecimal guess, int maxIterations, BigDecimal tolerance) {
        if (cashFlows == null || cashFlows.isEmpty()) {
            return "N/A";
        }
        if (cashFlows.stream().anyMatch(Objects::isNull)) {
            throw new IllegalArgumentException("Cash flows cannot contain null values.");
        }

        boolean hasPositive = cashFlows.stream().anyMatch(cf -> cf.compareTo(BigDecimal.ZERO) > 0);
        boolean hasNegative = cashFlows.stream().anyMatch(cf -> cf.compareTo(BigDecimal.ZERO) < 0);

        if (!hasPositive || !hasNegative) {
            if (cashFlows.stream().allMatch(cf -> cf.compareTo(BigDecimal.ZERO) == 0)) return "0.00";
            if (!hasNegative && hasPositive) return "Infinity"; // All positive or zero (with at least one positive)
            if (hasNegative && !hasPositive) return "N/A";    // All negative or zero (with at least one negative)
        }

        BigDecimal rate = guess;
        BigDecimal lowRate = new BigDecimal("-0.9999999"); // Close to -100%
        BigDecimal highRate = new BigDecimal("5.0");   // 500% as an upper practical bound

        // Ensure tolerance is positive
        if (tolerance.compareTo(BigDecimal.ZERO) <= 0) {
            tolerance = new BigDecimal("1e-7"); // Default if invalid
        }
        BigDecimal scaledTolerance = tolerance.multiply(new BigDecimal("10"), MC_PRECISION);
        BigDecimal veryScaledTolerance = tolerance.multiply(new BigDecimal("100"), MC_PRECISION);


        for (int i = 0; i < maxIterations; i++) {
            BigDecimal npvValue = NPV(rate, cashFlows);
            if (npvValue.toString().equals("NaN")) { // Check if NPV calculation failed
                // This can happen if rate hits -1. Try bisection.
                if (npvValue.signum() > 0) lowRate = rate; // This part is tricky if npvValue is truly NaN
                else highRate = rate; // This logic assumes NPV could determine a sign before becoming NaN
                // Or, more robustly, if rate is the issue (-1), adjust search.
                rate = lowRate.add(highRate).divide(BigDecimal.valueOf(2), MC_FOR_DIVISION);
                if (highRate.subtract(lowRate).abs().compareTo(scaledTolerance) < 0) {
                    // Converged by bisection due to problematic rate
                    return rate.multiply(BigDecimal.valueOf(100), MC_PRECISION).setScale(3, RoundingMode.HALF_UP).toPlainString();
                }
                continue;
            }


            if (npvValue.abs().compareTo(tolerance) < 0) {
                return rate.multiply(BigDecimal.valueOf(100), MC_PRECISION).setScale(3, RoundingMode.HALF_UP).toPlainString();
            }

            BigDecimal derivative = BigDecimal.ZERO;
            BigDecimal onePlusRate = BigDecimal.ONE.add(rate, MC_PRECISION);

            if (onePlusRate.compareTo(BigDecimal.ZERO) <= 0) {
                // Rate is -100% or less, derivative calculation will fail or be meaningless.
                // Switch to bisection.
                // Heuristic: if NPV is positive, true IRR is likely higher, so increase lowRate.
                // This part might need more robust handling for extreme rates.
                if (npvValue.compareTo(BigDecimal.ZERO) > 0) {
                    lowRate = rate.add(new BigDecimal("0.0001")); // Nudge rate if it's problematic
                } else {
                    highRate = rate.subtract(new BigDecimal("0.0001"));
                }
                rate = lowRate.add(highRate).divide(BigDecimal.valueOf(2), MC_FOR_DIVISION);
                if (highRate.subtract(lowRate).abs().compareTo(scaledTolerance) < 0) {
                    return rate.multiply(BigDecimal.valueOf(100), MC_PRECISION).setScale(3, RoundingMode.HALF_UP).toPlainString();
                }
                continue;
            }


            for (int j = 0; j < cashFlows.size(); j++) {
                BigDecimal termNumerator = BigDecimal.valueOf(-(j + 1)).multiply(cashFlows.get(j), MC_PRECISION);
                BigDecimal termDenominator = onePlusRate.pow(j + 2, MC_PRECISION);

                if (termDenominator.compareTo(BigDecimal.ZERO) == 0) {
                    // This indicates rate is -1. Derivative is infinite.
                    // This case should be handled by onePlusRate check above, but as a safeguard:
                    derivative = BigDecimal.ZERO; // Mark as problematic
                    break;
                }
                derivative = derivative.add(termNumerator.divide(termDenominator, MC_FOR_DIVISION), MC_PRECISION);
            }

            // If derivative is zero (or very close to it), Newton-Raphson fails. Switch to bisection.
            if (derivative.abs().compareTo(new BigDecimal("1e-12")) < 0) { // Using a slightly larger epsilon for derivative check
                if (npvValue.compareTo(BigDecimal.ZERO) > 0) {
                    lowRate = rate;
                } else {
                    highRate = rate;
                }
                rate = lowRate.add(highRate).divide(BigDecimal.valueOf(2), MC_FOR_DIVISION);

                if (highRate.subtract(lowRate).abs().compareTo(scaledTolerance) < 0) {
                    // If range is too small, consider it converged or no solution in range
                    return rate.multiply(BigDecimal.valueOf(100), MC_PRECISION).setScale(3, RoundingMode.HALF_UP).toPlainString();
                }
                continue; // Continue with bisection step
            }

            BigDecimal newRate;
            try {
                newRate = rate.subtract(npvValue.divide(derivative, MC_FOR_DIVISION), MC_PRECISION);
            } catch (ArithmeticException e) { // Catch division by zero if derivative was effectively zero
                if (npvValue.compareTo(BigDecimal.ZERO) > 0) lowRate = rate; else highRate = rate;
                rate = lowRate.add(highRate).divide(BigDecimal.valueOf(2), MC_FOR_DIVISION);
                if (highRate.subtract(lowRate).abs().compareTo(scaledTolerance) < 0) {
                    return rate.multiply(BigDecimal.valueOf(100), MC_PRECISION).setScale(3, RoundingMode.HALF_UP).toPlainString();
                }
                continue;
            }


            // If Newton-Raphson step is too large, or goes out of bounds, use bisection.
            if (newRate.compareTo(lowRate) <= 0 || newRate.compareTo(highRate) >= 0) {
                if (npvValue.compareTo(BigDecimal.ZERO) > 0) {
                    lowRate = rate;
                } else {
                    highRate = rate;
                }
                rate = lowRate.add(highRate).divide(BigDecimal.valueOf(2), MC_FOR_DIVISION);
                if (highRate.subtract(lowRate).abs().compareTo(scaledTolerance) < 0) {
                    return rate.multiply(BigDecimal.valueOf(100), MC_PRECISION).setScale(3, RoundingMode.HALF_UP).toPlainString();
                }
            } else {
                rate = newRate;
            }
            // Ensure rate doesn't go below -1 (or very close to it)
            if (rate.compareTo(new BigDecimal("-0.99999999")) < 0) {
                rate = new BigDecimal("-0.99999999");
            }
        }

        // After max iterations, check if the current rate is acceptable
        BigDecimal lastAttemptNpv = NPV(rate, cashFlows);
        if (lastAttemptNpv.toString().equals("NaN") || lastAttemptNpv.abs().compareTo(veryScaledTolerance) < 0) {
            return rate.multiply(BigDecimal.valueOf(100), MC_PRECISION).setScale(3, RoundingMode.HALF_UP).toPlainString();
        }

        return "N/A"; // Failed to converge
    }

    // Overload for default guess, maxIterations, tolerance
    public static String calculateIRR(List<BigDecimal> cashFlows) {
        return calculateIRR(cashFlows, new BigDecimal("0.05"), 1000, new BigDecimal("1e-10"));
    }


    // Helper function to get actual annual premiums after discounts
    public static List<BigDecimal> getActualPremiumsPerYear(BigDecimal annualPremium, int paymentYears, List<Discount> discounts) {
        if (annualPremium == null) {
            throw new IllegalArgumentException("Annual premium cannot be null.");
        }
        if (discounts == null) {
            discounts = new ArrayList<>(); // Treat null as empty list
        }

        List<BigDecimal> actualPremiums = new ArrayList<>();

        if (annualPremium.compareTo(BigDecimal.ZERO) <= 0 || paymentYears <= 0) {
            for (int i = 0; i < paymentYears; i++) {
                actualPremiums.add(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
            }
            return actualPremiums;
        }

        for (int i = 1; i <= paymentYears; i++) {
            BigDecimal currentYearPremium = annualPremium;
            final int currentYear = i; // for use in lambda

            Optional<Discount> discountOpt = discounts.stream()
                    .filter(d -> d.year == currentYear && d.rate != null)
                    .findFirst();

            if (discountOpt.isPresent()) {
                BigDecimal discountRate = discountOpt.get().rate;
                BigDecimal discountAmount = annualPremium.multiply(discountRate.divide(BigDecimal.valueOf(100), MC_FOR_DIVISION), MC_PRECISION);
                currentYearPremium = currentYearPremium.subtract(discountAmount, MC_PRECISION);
            }
            actualPremiums.add(currentYearPremium.max(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP));
        }
        return actualPremiums;
    }

    // Calculate simple interest
    public static String calculateSimpleInterest(int holdingYear, List<Integer> paymentYearsList, List<BigDecimal> netOutflowsList, BigDecimal totalReturnAtEnd) {
        if (paymentYearsList == null || netOutflowsList == null || totalReturnAtEnd == null) {
            throw new IllegalArgumentException("Input lists and totalReturnAtEnd cannot be null.");
        }
        if (paymentYearsList.size() != netOutflowsList.size() && !netOutflowsList.isEmpty()) {
            // Allow empty paymentYearsList if netOutflowsList is also empty (e.g. lump sum investment)
            // but if netOutflowsList is not empty, their sizes must match.
            if (!netOutflowsList.isEmpty()) {
                throw new IllegalArgumentException("paymentYearsList and netOutflowsList must have the same size or netOutflowsList be empty.");
            }
        }
        if (netOutflowsList.stream().anyMatch(Objects::isNull)) {
            throw new IllegalArgumentException("Net outflows cannot contain null values.");
        }


        BigDecimal sumOfNetOutflows = netOutflowsList.stream()
                .reduce(BigDecimal.ZERO, (a, b) -> a.add(b, MC_PRECISION));
        BigDecimal totalNetCashFlow = sumOfNetOutflows.add(totalReturnAtEnd, MC_PRECISION);

        BigDecimal timeWeightedSumOfPremiums = BigDecimal.ZERO;
        for (int j = 0; j < paymentYearsList.size(); j++) {
            // Ensure j is within bounds of netOutflowsList.
            // The original JS code has: if (j < netOutflowsList.length)
            // This implies paymentYearsList could be longer than netOutflowsList,
            // but usually they correspond. If netOutflowsList is shorter, some payment years are ignored.
            if (j < netOutflowsList.size()) {
                BigDecimal paymentYear = BigDecimal.valueOf(paymentYearsList.get(j));
                BigDecimal netOutflow = netOutflowsList.get(j);

                // timeFactor = holdingYear + 1 - paymentYearsList[j];
                BigDecimal timeFactor = BigDecimal.valueOf(holdingYear + 1).subtract(paymentYear, MC_PRECISION);
                timeWeightedSumOfPremiums = timeWeightedSumOfPremiums.add(timeFactor.multiply(netOutflow, MC_PRECISION), MC_PRECISION);
            }
        }

        if (timeWeightedSumOfPremiums.compareTo(BigDecimal.ZERO) != 0) {
            try {
                // singleInterestRate = totalNetCashFlow / Math.abs(timeWeightedSumOfPremiums);
                BigDecimal singleInterestRate = totalNetCashFlow.divide(timeWeightedSumOfPremiums.abs(), MC_FOR_DIVISION);
                return singleInterestRate.multiply(BigDecimal.valueOf(100), MC_PRECISION)
                        .setScale(2, RoundingMode.HALF_UP)
                        .toPlainString();
            } catch (ArithmeticException e) {
                // This catch is mostly for division by zero, but we already check
                // timeWeightedSumOfPremiums != 0. Could be other issues with extreme values.
                System.err.println("Error calculating simple interest: " + e.getMessage());
                return "N/A";
            }
        } else {
            // Denominator is zero
            boolean allOutflowsZero = netOutflowsList.stream().allMatch(p -> p.compareTo(BigDecimal.ZERO) == 0);
            if (totalNetCashFlow.compareTo(BigDecimal.ZERO) > 0 && allOutflowsZero) {
                return "Infinity";
            }
            if (totalNetCashFlow.compareTo(BigDecimal.ZERO) == 0) {
                return "0.00";
            }
            return "N/A";
        }
    }


//    public static void main(String[] args) {
//        // Example Usage for NPV
//        List<BigDecimal> cashFlowsNpv = List.of(new BigDecimal("-100"), new BigDecimal("30"), new BigDecimal("30"), new BigDecimal("30"), new BigDecimal("30"));
//        BigDecimal rateNpv = new BigDecimal("0.1"); // 10%
//        BigDecimal npvResult = NPV(rateNpv, cashFlowsNpv);
//        System.out.println("NPV: " + npvResult.setScale(2, RoundingMode.HALF_UP).toPlainString()); // Expected: -5.35
//
//        // Example Usage for IRR
//        List<BigDecimal> cashFlowsIrr = List.of(new BigDecimal("-100"), new BigDecimal("10"), new BigDecimal("10"), new BigDecimal("110"));
//        // Expected IRR for -100, 10, 10, 110 is 10.00%
//        System.out.println("IRR 1: " + calculateIRR(cashFlowsIrr, new BigDecimal("0.1"), 1000, new BigDecimal("1e-9")));
//
//        List<BigDecimal> cashFlowsIrr2 = List.of(new BigDecimal("-1000"), new BigDecimal("200"), new BigDecimal("300"), new BigDecimal("400"), new BigDecimal("500"));
//        System.out.println("IRR 2: " + calculateIRR(cashFlowsIrr2)); // Expected around 14.99%
//
//        List<BigDecimal> cashFlowsIrr3 = List.of(new BigDecimal("-100"), new BigDecimal("120")); // Simple 20%
//        System.out.println("IRR 3: " + calculateIRR(cashFlowsIrr3)); // Expected 20.00%
//
//        List<BigDecimal> cashFlowsIrr4 = List.of(new BigDecimal("-100"), new BigDecimal("-10"), new BigDecimal("130"));
//        System.out.println("IRR 4 (complex): " + calculateIRR(cashFlowsIrr4)); // Expected around 9.16%
//
//        List<BigDecimal> allZeros = List.of(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
//        System.out.println("IRR all zeros: " + calculateIRR(allZeros)); // Expected 0.00
//
//        List<BigDecimal> allPositive = List.of(new BigDecimal("10"), new BigDecimal("20"), new BigDecimal("30"));
//        System.out.println("IRR all positive: " + calculateIRR(allPositive)); // Expected Infinity
//
//        List<BigDecimal> allNegative = List.of(new BigDecimal("-10"), new BigDecimal("-20"), new BigDecimal("-30"));
//        System.out.println("IRR all negative: " + calculateIRR(allNegative)); // Expected N/A
//
//        List<BigDecimal> singleNegative = List.of(new BigDecimal("-100"));
//        System.out.println("IRR single negative: " + calculateIRR(singleNegative)); // Expected N/A
//
//        List<BigDecimal> singlePositive = List.of(new BigDecimal("100"));
//        System.out.println("IRR single positive: " + calculateIRR(singlePositive)); // Expected Infinity
//
//        // Example for getActualPremiumsPerYear
//        BigDecimal annualPremium = new BigDecimal("1000.00");
//        int paymentYears = 5;
//        List<Discount> discounts = List.of(
//                new Discount(1, "10"), // 10% discount in year 1
//                new Discount(3, "5.5")  // 5.5% discount in year 3
//        );
//        List<BigDecimal> actualPremiums = getActualPremiumsPerYear(annualPremium, paymentYears, discounts);
//        System.out.println("Actual Premiums: ");
//        actualPremiums.forEach(p -> System.out.println("  " + p.toPlainString()));
//        // Expected: 900.00, 1000.00, 945.00, 1000.00, 1000.00
//
//        // Example for calculateSimpleInterest
//        int holdingYear = 5; // Held for 5 years
//        List<Integer> paymentYearsSimple = List.of(1, 2, 3); // Premiums paid in year 1, 2, 3
//        List<BigDecimal> netOutflowsSimple = List.of(
//                new BigDecimal("-100"), new BigDecimal("-100"), new BigDecimal("-100") // Premiums
//        );
//        BigDecimal totalReturnAtEndSimple = new BigDecimal("350"); // Total return at end of holding period
//        String simpleInterest = calculateSimpleInterest(holdingYear, paymentYearsSimple, netOutflowsSimple, totalReturnAtEndSimple);
//        System.out.println("Simple Interest: " + simpleInterest + "%"); // Example: ( (350 - 300) / ( (5+1-1)*100 + (5+1-2)*100 + (5+1-3)*100 ) ) * 100 = (50 / (500+400+300)) * 100 = (50/1200)*100 = 4.17%
//
//        List<Integer> paymentYearsSimple2 = List.of(0); // Lump sum at year 0
//        List<BigDecimal> netOutflowsSimple2 = List.of(new BigDecimal("-1000"));
//        BigDecimal totalReturnAtEndSimple2 = new BigDecimal("1200"); // Return after 2 years
//        String simpleInterest2 = calculateSimpleInterest(2, paymentYearsSimple2, netOutflowsSimple2, totalReturnAtEndSimple2);
//        // ((1200-1000) / ((2+1-0)*1000)) * 100 = (200 / 3000)*100 = 6.67% -- check formula logic for "holdingYear+1-paymentYear[j]" when paymentYear[j] is 0.
//        // JS logic: (2+1-0) * 1000 = 3000.  (200 / 3000) * 100 = 6.67
//        System.out.println("Simple Interest 2 (lump sum): " + simpleInterest2 + "%");
//
//        // Test case from original comments: timeWeightedSumOfPremiums == 0, totalNetCashFlow > 0, netOutflowsList.every(p => p === 0)
//        List<Integer> paymentYearsZeroOutflow = List.of(1,2,3);
//        List<BigDecimal> netOutflowsZero = List.of(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
//        BigDecimal positiveReturn = new BigDecimal("100");
//        System.out.println("Simple Interest (zero outflow, positive return): " + calculateSimpleInterest(3, paymentYearsZeroOutflow, netOutflowsZero, positiveReturn)); // Expected: Infinity
//
//    }
}