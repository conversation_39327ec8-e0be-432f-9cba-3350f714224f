package com.sub.baoxian.productInfoService.utils;

import java.util.List;

import org.apache.commons.math3.analysis.UnivariateFunction;
import org.apache.commons.math3.analysis.solvers.BracketingNthOrderBrentSolver;
import org.springframework.stereotype.Service;

@Service
public class FinancialCalculator {

    /**
     * 计算 IRR（内部收益率）
     * @param cashFlows 现金流数组，包括初始投资和后续回报
     * @return IRR 百分比（保留两位小数），若无法计算返回 Double.NaN
     */
    public static double calculateIRR(List<Double> cashFlows) {
        try {
            UnivariateFunction npv = new UnivariateFunction() {
                @Override
                public double value(double rate) {
                    double value = 0.0;
                    for (int t = 0; t < cashFlows.size(); t++) {
                        value += cashFlows.get(t) / Math.pow(1 + rate, t);
                    }
                    return value;
                }
            };

            BracketingNthOrderBrentSolver solver = new BracketingNthOrderBrentSolver(1e-10, 1e-14, 5);
            double irr = solver.solve(Integer.MAX_VALUE, npv, -1.0 + 1e-10, 10.0);
            return round(irr * 100, 2); // 返回百分比
        } catch (Exception e) {
            e.printStackTrace(); // THIS IS VERY IMPORTANT
            return Double.NaN;
        }
    }
    /**
     * 计算单利（Simple Interest Rate）
     * @param signletonVal 现金价值/退保金
     * @param netOutflows 历史净现金流列表（负值表示投入）
     * @param years 年份列表（与 netOutflows 对应）
     * @param currentYear 当前年份
     * @return 单利百分比（保留两位小数），若无法计算返回 Double.NaN
     */
    public static double calculateSimpleInterestRate(Double signletonVal, List<Double> netOutflows, List<Integer> years, int currentYear) {
        if (years.size() != netOutflows.size()) {
            throw new IllegalArgumentException("netOutflows 和 years 列表长度不一致");
        }

        double totalCashValue = signletonVal + netOutflows.stream().mapToInt(d -> (int) Math.round(d)).sum();
        double timeWeightedSum = 0.0;

        for (int i = 0; i < years.size(); i++) {
            int year = years.get(i);
            int timeDiff = currentYear +1- year;
            timeWeightedSum += timeDiff * netOutflows.get(i);
        }

        if (timeWeightedSum == 0) {
            return Double.NaN;
        }

        double singleInterestRate = (totalCashValue) / Math.abs(timeWeightedSum);
        return round(singleInterestRate * 100, 2); // 返回百分比
    }

    /**
     * 辅助函数：四舍五入到指定小数位
     */
    private static double round(double value, int places) {
        if (places < 0) throw new IllegalArgumentException();

        long factor = (long) Math.pow(10, places);
        value = value * factor;
        long tmp = Math.round(value);
        return (double) tmp / factor;
    }
}