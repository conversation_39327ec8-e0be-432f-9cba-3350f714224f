package com.sub.baoxian.productInfoService.utils;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.math3.analysis.UnivariateFunction;
import org.apache.commons.math3.analysis.solvers.BracketingNthOrderBrentSolver;
import org.apache.commons.math3.exception.NoBracketingException; // For specific catch
import org.apache.commons.math3.exception.TooManyEvaluationsException; // For specific catch
import org.springframework.stereotype.Service;

@Service
public class FinancialCalculatorV2 {

    private static final MathContext MC = MathContext.DECIMAL128;
    private static final MathContext MC_IRR_POW = new MathContext(50, RoundingMode.HALF_UP);

    private static double round(double value, int places) {
        if (Double.isNaN(value) || Double.isInfinite(value)) {
            return value;
        }
        if (places < 0) {
            throw new IllegalArgumentException("Number of decimal places cannot be negative.");
        }
        BigDecimal bd = BigDecimal.valueOf(value);
        bd = bd.setScale(places, RoundingMode.HALF_UP);
        return bd.doubleValue();
    }

    public static double calculateIRR(List<Double> cashFlows) {
        if (cashFlows == null || cashFlows.isEmpty()) {
            // System.err.println("IRR calculation error: Cashflows list is null or empty.");
            return Double.NaN;
        }

        boolean hasPositive = cashFlows.stream().anyMatch(cf -> cf > 0);
        boolean hasNegative = cashFlows.stream().anyMatch(cf -> cf < 0);
        if (!(hasPositive && hasNegative)) {
            // System.err.println("IRR calculation info: No sign change in cashflows. CashFlows: " + cashFlows);
            return Double.NaN;
        }

        List<BigDecimal> cashFlowsBd = cashFlows.stream()
                .map(BigDecimal::valueOf)
                .collect(Collectors.toList());

        try {
            UnivariateFunction npvFunction = new UnivariateFunction() {
                @Override
                public double value(double rate) {
                    if (Double.isNaN(rate) || Double.isInfinite(rate)) {
                        return rate;
                    }

                    BigDecimal rateBd = BigDecimal.valueOf(rate);
                    BigDecimal npvBd = BigDecimal.ZERO;
                    BigDecimal oneBd = BigDecimal.ONE;
                    BigDecimal onePlusRateBd = oneBd.add(rateBd);

                    for (int t = 0; t < cashFlowsBd.size(); t++) {
                        BigDecimal cfBd = cashFlowsBd.get(t);
                        if (cfBd.compareTo(BigDecimal.ZERO) == 0) {
                            continue;
                        }

                        if (t == 0) {
                            npvBd = npvBd.add(cfBd);
                        } else {
                            if (onePlusRateBd.compareTo(BigDecimal.ZERO) == 0) {
                                if (cfBd.compareTo(BigDecimal.ZERO) != 0) {
                                    return cfBd.signum() > 0 ? Double.POSITIVE_INFINITY : Double.NEGATIVE_INFINITY;
                                }
                            } else {
                                BigDecimal denominatorBd;
                                try {
                                    denominatorBd = onePlusRateBd.pow(t, MC_IRR_POW);
                                } catch (ArithmeticException e) {
                                    // System.err.println("ArithmeticException in BigDecimal.pow for rate=" + rate + ", t=" + t);
                                    // This can happen if 1+rate is negative and t is non-integer (not our case)
                                    // or if the result is too large/small for internal BigDecimal representation with scale
                                    // For IRR, rate > -1, so 1+rate > 0.
                                    // If 1+rate is extremely small, pow(t) might go beyond BigDecimal's internal limits before scaling
                                    // or result in a number that becomes NaN when converted back to double.
                                    // A very large exponent 't' with a base slightly different from 1 can also be an issue.
                                    // This might indicate an issue with the rate itself or extreme cash flow values.
                                    return Double.NaN; // Or a specific infinity if determinable
                                }

                                if (denominatorBd.compareTo(BigDecimal.ZERO) == 0) {
                                    // This implies (1+rate)^t underflowed to 0 even for BigDecimal,
                                    // or onePlusRateBd was 0 (already handled).
                                    // With MC_IRR_POW (prec 50), this should be extremely rare unless (1+rate) is truly zero.
                                    if (cfBd.compareTo(BigDecimal.ZERO) != 0) {
                                        return cfBd.signum() > 0 ? Double.POSITIVE_INFINITY : Double.NEGATIVE_INFINITY;
                                    }
                                } else {
                                    try {
                                        BigDecimal term = cfBd.divide(denominatorBd, MC);
                                        npvBd = npvBd.add(term);
                                    } catch (ArithmeticException e) {
                                        // System.err.println("ArithmeticException in BigDecimal.divide for cf=" + cfBd + ", denom=" + denominatorBd);
                                        // If npvBd is already huge, its double representation might be infinite
                                        if (npvBd.abs().compareTo(BigDecimal.valueOf(Double.MAX_VALUE / 2)) > 0) {
                                            return npvBd.signum() > 0 ? Double.POSITIVE_INFINITY : Double.NEGATIVE_INFINITY;
                                        }
                                        // Otherwise, the division itself failed in a way that implies non-finite result
                                        return Double.NaN;
                                    }
                                }
                            }
                        }
                    }
                    if (npvBd.compareTo(BigDecimal.valueOf(Double.MAX_VALUE)) > 0) return Double.POSITIVE_INFINITY;
                    if (npvBd.compareTo(BigDecimal.valueOf(-Double.MAX_VALUE)) < 0) return Double.NEGATIVE_INFINITY;
                    // For very small non-zero numbers that might become 0.0 or subnormal as double:
                    if (npvBd.abs().compareTo(BigDecimal.ZERO) > 0 && npvBd.abs().compareTo(BigDecimal.valueOf(Double.MIN_NORMAL/2)) < 0) {
                        // This value is extremely small. npvBd.doubleValue() will handle it (might become 0.0 or subnormal)
                    }
                    return npvBd.doubleValue();
                }
            };

            BracketingNthOrderBrentSolver solver = new BracketingNthOrderBrentSolver(1e-10, 1e-14, 5);
            double lowerBound = -1.0 + 1e-8;
            double upperBound = 10.0;

            double valAtMin = npvFunction.value(lowerBound);
            double valAtMax = npvFunction.value(upperBound);

            if (Double.isNaN(valAtMin) || Double.isNaN(valAtMax)) {
                // System.err.println("Cannot solve IRR (pre-check): NPV at a bound is NaN. MinVal: " + valAtMin + ", MaxVal: " + valAtMax + ". CFs: " + cashFlows);
                return Double.NaN;
            }

            boolean signsOpposite;
            if (Double.isInfinite(valAtMin) && Double.isInfinite(valAtMax)) {
                signsOpposite = (valAtMin > 0 && valAtMax < 0) || (valAtMin < 0 && valAtMax > 0);
            } else if (Double.isInfinite(valAtMin)) { // valAtMin is Inf, valAtMax is Finite
                signsOpposite = (valAtMin > 0 && valAtMax < 0) || (valAtMin < 0 && valAtMax > 0);
            } else if (Double.isInfinite(valAtMax)) { // valAtMin is Finite, valAtMax is Inf
                signsOpposite = (valAtMin > 0 && valAtMax < 0) || (valAtMin < 0 && valAtMax > 0);
            } else { // Both finite
                signsOpposite = (valAtMin * valAtMax < 0);
            }

            if (!signsOpposite) {
                // If a bound value is extremely close to zero, it might be the root.
                // Check this only if the other bound is not causing the non-bracketing.
                if (Math.abs(valAtMin) < 1e-9 && (Double.isFinite(valAtMin) || (Double.isInfinite(valAtMax) && ((valAtMin > 0 && valAtMax <0) || (valAtMin <0 && valAtMax >0 ))))) {
                    // System.err.println("IRR (pre-check): valAtMin is effectively zero. CFs: " + cashFlows);
                    return round(lowerBound * 100, 2);
                }
                if (Math.abs(valAtMax) < 1e-9 && (Double.isFinite(valAtMax) || (Double.isInfinite(valAtMin) && ((valAtMax > 0 && valAtMin <0) || (valAtMax <0 && valAtMin >0 ))))) {
                    // System.err.println("IRR (pre-check): valAtMax is effectively zero. CFs: " + cashFlows);
                    return round(upperBound * 100, 2);
                }

                // System.err.println("NoBracketingException (manual pre-check for IRR). CFs: " + cashFlows +
                //        " Bounds: [" + lowerBound + ", " + upperBound + "] Values: [" + valAtMin + ", " + valAtMax + "]");
                return Double.NaN;
            }

            double irr = solver.solve(1000, npvFunction, lowerBound, upperBound);
            return round(irr * 100, 2);

        } catch (NoBracketingException e) {
            // System.err.println("NoBracketingException from solver for cashflows: " + cashFlows.toString() + " - " + e.getMessage());
            return Double.NaN;
        } catch (TooManyEvaluationsException e) {
            // System.err.println("TooManyEvaluationsException from solver for cashflows: " + cashFlows.toString() + " - " + e.getMessage());
            return Double.NaN;
        }
        catch (Exception e) {
            // System.err.println("Generic exception during IRR calculation for cashflows: " + cashFlows.toString() + " - " + e.getMessage());
            // e.printStackTrace(); // Uncomment for detailed stack trace during debugging
            return Double.NaN;
        }
    }

    // calculateSimpleInterestRate and main method remain the same as previous correct version
    public static double calculateSimpleInterestRate(
            BigDecimal signletonValBd,
            List<BigDecimal> netOutflowsBd,
            List<Integer> years,
            int currentYear) {

        if (years.size() != netOutflowsBd.size()) {
            throw new IllegalArgumentException("netOutflowsBd and years 列表长度不一致");
        }
        if (signletonValBd == null || netOutflowsBd.stream().anyMatch(java.util.Objects::isNull)) {
            throw new IllegalArgumentException("Input BigDecimals cannot be null.");
        }


        BigDecimal totalNetOutflowsBd = netOutflowsBd.stream()
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalCashValueBd = signletonValBd.add(totalNetOutflowsBd);


        BigDecimal timeWeightedSumBd = BigDecimal.ZERO;
        for (int i = 0; i < years.size(); i++) {
            int year = years.get(i);
            BigDecimal timeDiffBd = BigDecimal.valueOf(currentYear + 1 - year);
            BigDecimal netOutflowBd_i = netOutflowsBd.get(i);
            timeWeightedSumBd = timeWeightedSumBd.add(timeDiffBd.multiply(netOutflowBd_i, MC));
        }

        if (timeWeightedSumBd.compareTo(BigDecimal.ZERO) == 0) {
            return Double.NaN;
        }
        BigDecimal singleInterestRateBd;
        try {
            singleInterestRateBd = totalCashValueBd.divide(timeWeightedSumBd.abs(), MC);
        } catch (ArithmeticException e) {
            return Double.NaN;
        }

        double result = singleInterestRateBd.multiply(BigDecimal.valueOf(100), MC).doubleValue();
        return round(result, 2);
    }
    public static double calculateSimpleInterestRate(
            Double signletonVal,
            List<Double> netOutflows,
            List<Integer> years,
            int currentYear) {
        if (signletonVal == null || netOutflows == null || netOutflows.stream().anyMatch(java.util.Objects::isNull)) {
            throw new IllegalArgumentException("Input Double values cannot be null.");
        }

        BigDecimal signletonValBd = BigDecimal.valueOf(signletonVal);
        List<BigDecimal> netOutflowsBd = netOutflows.stream()
                .map(BigDecimal::valueOf)
                .collect(Collectors.toList());
        return calculateSimpleInterestRate(signletonValBd, netOutflowsBd, years, currentYear);
    }
//    public static void main(String[] args) {
//        List<Double> flows1 = List.of(-100.0, 20.0, 30.0, 40.0, 50.0);
//        System.out.println("IRR for " + flows1 + ": " + calculateIRR(flows1) + "%");
//
//        List<Double> problematicFlow = List.of(
//                -32000.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
//                0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
//                0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 528924.9
//        );
//        System.out.println("IRR for problematic flow: " + calculateIRR(problematicFlow) + "%");
//
//        List<Double> flowsOnlyPositive = List.of(100.0, 20.0, 30.0);
//        System.out.println("IRR for " + flowsOnlyPositive + ": " + calculateIRR(flowsOnlyPositive) + "%");
//
//        List<Double> flowsOnlyNegative = List.of(-100.0, -20.0, -30.0);
//        System.out.println("IRR for " + flowsOnlyNegative + ": " + calculateIRR(flowsOnlyNegative) + "%");
//
//        BigDecimal sv = new BigDecimal("1500");
//        List<BigDecimal> no = List.of(new BigDecimal("-1000"), new BigDecimal("-200"));
//        List<Integer> yrs = List.of(1, 2);
//        int cy = 3;
//        System.out.println("Simple Interest (BigDecimal inputs): " +
//                calculateSimpleInterestRate(sv, no, yrs, cy) + "%");
//
//        System.out.println("Simple Interest (Double inputs): " +
//                calculateSimpleInterestRate(1500.0, List.of(-1000.0, -200.0), yrs, cy) + "%");
//
//        List<Double> workingFlowV2 = List.of(
//                -32000.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
//                0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 316435.4
//        );
//        System.out.println("IRR for working flow V2: " + calculateIRR(workingFlowV2) + "%");
//    }
}