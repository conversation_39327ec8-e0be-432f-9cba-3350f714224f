package com.sub.baoxian.productInfoService.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.mapper.ProductDao;
import com.sub.baoxian.productInfoService.entity.InsuranceFeePo;
import com.sub.baoxian.productInfoService.entity.ProductIntroPO;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class InsuranceCost {
    @Autowired
    private ProductDao productDao;

    public float getInsuranceCost(Integer id ,Integer age, Integer insuredFee, Integer paymentMode){
        InsuranceFeePo insuranceFee = productDao.getCalculationFactors(id);
        if(age>insuranceFee.getMaxAge() || age<insuranceFee.getMinAge()) {
            return 0;
        }
        float result = 0;
        float increaseFactor = getIncreaseFactor(age, insuranceFee);

        float timeFactor = getTimeFactor(paymentMode, insuranceFee);
        int multiplyFactor = insuredFee/1000;
        if(insuranceFee.getFixedAmount()!=null && !insuranceFee.getFixedAmount().isEmpty()) {
            float fixedFee = Float.parseFloat(insuranceFee.getFixedAmount());

            if (insuranceFee.getType().equals("CRITICAL_ILLNESS")) {
                result = fixedFee + multiplyFactor * increaseFactor;
            }
        } else {
            switch (insuranceFee.getType()) {
                case "SAVINGS" -> {
                    if(insuranceFee.getFixedPremium() !=null) {
                        result  = insuredFee;
                    } else {
                        result =  increaseFactor*multiplyFactor;
                    }
                }
                case "LIFE" ->result = insuredFee * increaseFactor;
                case "MEDICAL" -> result  =increaseFactor;
            }
        }


        return result*timeFactor;
    }

    private static float getIncreaseFactor(Integer age, InsuranceFeePo insuranceFee) {
        float c0= Float.parseFloat(insuranceFee.getC0().trim().replace(" ", ""));
        float c1 = Float.parseFloat(insuranceFee.getC1().trim().replace(" ", ""));
        float c2 = Float.parseFloat(insuranceFee.getC2().trim().replace(" ", ""));
        float c3 = Float.parseFloat(insuranceFee.getC3().trim().replace(" ", ""));
        float c4 = Float.parseFloat(insuranceFee.getC4().trim().replace(" ", ""));
        return c0 + c1 * age + c2 * age * age + c3* age * age * age + c4 * age * age * age * age;
    }

    private static float getTimeFactor(Integer paymentMode, InsuranceFeePo insuranceFee) {
        float timeFactor = 1;

        if(insuranceFee.getSemiAnnualFactor()!=null && !insuranceFee.getSemiAnnualFactor().isEmpty()) {
            if(paymentMode ==2) {
                timeFactor = Float.parseFloat(insuranceFee.getSemiAnnualFactor());
            } else if(paymentMode ==3) {
                timeFactor = Float.parseFloat(insuranceFee.getQuarterlyFactor());
            } else if(paymentMode ==4) {
                timeFactor = Float.parseFloat(insuranceFee.getMonthlyFactor());
            }
        }
        return timeFactor;
    }
    public PageResult<InsuranceFeePo> getCalculationProducts (Integer pageNum, Integer pageSize,String search,String type ,String insurer, String region) {
        Page<ProductIntroPO> page = new Page<>(pageNum, pageSize);
        IPage<InsuranceFeePo> resultPage = productDao.ProductListOfInsuranceFee(page,search, type,insurer,region);
        return new PageResult<>(resultPage);
    }

    public List<String> getCalculationType() {
        return productDao.displayCalculationType();
    }



}
