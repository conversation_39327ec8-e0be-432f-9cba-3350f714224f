package com.sub.baoxian.productInfoService.utils;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.sub.baoxian.news.utils.StringUtils;

import java.io.IOException;
import java.util.Arrays;

public class ListSerializer extends JsonSerializer<String> {

    @Override
    public void serialize(String s, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (StringUtils.isEmpty(s)) {
            jsonGenerator.writeObject(s);
            return;
        }

        // 去掉 JSON 数组和引号，提取成字符串数组
        s = s.replace("[", "").replace("]", "").replace("\"", "").trim();
        String[] items = s.split(",");

        // 创建新的数组，去除每个元素前后的空格
        String[] trimmedItems = Arrays.stream(items)
                .map(String::trim)
                .toArray(String[]::new);

        jsonGenerator.writeObject(trimmedItems);
    }
}