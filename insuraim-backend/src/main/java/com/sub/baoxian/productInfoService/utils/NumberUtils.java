package com.sub.baoxian.productInfoService.utils;

import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;

@Service
public class NumberUtils {

    public static BigDecimal parseToBigDecimal(String input) {
        if (input == null || input.trim().isEmpty()) {
            return BigDecimal.ZERO;
        }

        try {
            String cleaned = input.replaceAll("[^\\d.]", "");
            if (cleaned.isEmpty()) return BigDecimal.ZERO;
            return new BigDecimal(cleaned);
        } catch (NumberFormatException e) {
            return BigDecimal.ZERO;
        }
    }

    public static int parseToInt(String input) {
        if (input == null || input.trim().isEmpty()) {
            return 0;
        }

        String cleaned = input.replaceAll("[^0-9]", "");
        if (cleaned.isEmpty()) return 1;

        try {
            return Integer.parseInt(cleaned);
        } catch (NumberFormatException e) {
            return 0;
        }
    }
}