package com.sub.baoxian.productInfoService.vo;

import java.util.List;

import lombok.Data;

@Data
public class CalculationDataVO {
    //产品名称
    private String productName;
    //年龄
    private Integer age;
    //性别
    private String gender;
    //吸烟状态
    private String smokingStatus;
    //保单货币
    private String policyCurrency;
    //缴费方式
    private String paymentMode;
    //缴费年期
    private String premiumPaymentTerm;
    //保障期限
    private String coverageTerm;
    //保额
    private Double coverageAmount;
    //年保费
    private Double annualPremium;

    //演算数据
    private List<CalculationBaseDataItemBO> calculationBaseDataItemBOList;

    @Data
    public static class CalculationBaseDataItemBO {
        private Integer age;
        // 保单年度
        private Integer year;
        // 总保费
        private Double totalPremium;
        private Double netCashFlow;
        // 退保保证金额
        private Double insuredAmount;
        // 保证周年红利及利息
        private Double cashValueOfReversionaryBonusNg;

        private Double totalMonthlyAnnuityAndInterest;

        private Double totalWithdrawableCashAndInterest;

        private Double TerminalBonusNg;
        // 退保总额
        private Double surrender;
        // 身故保证金额
        private Double deathBenefitG;
        // 身故周年红利及利息
        private Double deathBenefitAnnualBonusAndInterest;
        // 身故终期红利
        private Double deathBenefitTerminalBonusNg;
        // 身故总额
        private Double deathBenefitTotal;
        private String IRR;
        private String insuredIRR;
        private Double singleInterest;
        private Double insuredSingleInterest;
        private Integer multiple;
    }
}
