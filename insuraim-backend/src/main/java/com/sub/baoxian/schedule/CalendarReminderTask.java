package com.sub.baoxian.schedule;

import com.sub.baoxian.common.constants.MessageType;
import com.sub.baoxian.model.entity.Calendar;
import com.sub.baoxian.service.CalendarService;
import com.sub.baoxian.service.MessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 日程提醒定时任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CalendarReminderTask {

    private final CalendarService calendarService;
    private final MessageService messageService;

    /**
     * 每5分钟检查一次即将到期的日程
     */
    @Scheduled(cron = "0 */5 * * * ?")
    public void checkCalendarReminders() {
        log.info("开始检查日程提醒...");

        // 获取当前时间
        long currentTime = System.currentTimeMillis();

        // 1. 检查并自动完成超时的日程
        this.checkAndCompleteOverdueCalendars(currentTime);

        // 2. 获取未来5分钟内需要提醒的日程
        // 查找状态为进行中(0)且提醒时间在当前时间到未来5分钟之间的日程
        // 由于每5分钟执行一次，查询时间范围设为当前时间到未来10分钟，避免遗漏
        List<Calendar> calendarsToRemind = this.findCalendarsToRemind(
                currentTime, currentTime + 10 * 60 * 1000);

        if (!calendarsToRemind.isEmpty()) {
            log.info("找到 {} 个需要提醒的日程", calendarsToRemind.size());

            // 为每个日程发送提醒
            for (Calendar calendar : calendarsToRemind) {
                try {
                    // 发送WebSocket消息提醒
                    this.sendCalendarReminder(calendar);

                    // 更新日程的提醒状态，避免重复提醒
                    this.updateReminderSent(calendar.getId());
                } catch (Exception e) {
                    log.error("发送日程提醒失败, 日程ID: {}", calendar.getId(), e);
                }
            }
        }
    }

    /**
     * 检查并自动完成超时的日程
     */
    private void checkAndCompleteOverdueCalendars(long currentTime) {
        try {
            // 查找所有超时的进行中日程
            List<Calendar> overdueCalendars = calendarService.findOverdueCalendars(currentTime);

            if (!overdueCalendars.isEmpty()) {
                log.info("找到 {} 个超时的日程，将自动标记为已完成", overdueCalendars.size());

                for (Calendar calendar : overdueCalendars) {
                    try {
                        // 将状态更新为已完成(1)
                        boolean success = calendarService.updateCalendarStatusBySystem(calendar.getId(), 1);
                        if (success) {
                            log.info("日程已自动完成, ID: {}, 标题: {}", calendar.getId(), calendar.getTitle());
                        } else {
                            log.warn("自动完成日程失败, ID: {}", calendar.getId());
                        }
                    } catch (Exception e) {
                        log.error("自动完成日程失败, 日程ID: {}", calendar.getId(), e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("检查超时日程失败", e);
        }
    }

    /**
     * 发送日程提醒
     */
    private void sendCalendarReminder(Calendar calendar) {
        String title = "日程提醒";
        String content = calendar.getTitle() + " - 即将开始";

        // 通过WebSocket发送消息给用户
        messageService.sendMessageToUser(
                calendar.getUserId(),
                title,
                content,
                MessageType.SYSTEM_MESSAGE);

        log.info("已发送日程提醒, 用户ID: {}, 日程: {}", calendar.getUserId(), calendar.getTitle());
    }

    private List<Calendar> findCalendarsToRemind(long currentTime, long futureTime) {
        return calendarService.findCalendarsToRemind(currentTime, futureTime);
    }

    private void updateReminderSent(Long calendarId) {
        calendarService.updateReminderSent(calendarId);
    }

}