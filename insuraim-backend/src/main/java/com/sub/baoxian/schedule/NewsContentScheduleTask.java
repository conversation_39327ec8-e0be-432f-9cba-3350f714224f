package com.sub.baoxian.schedule;

import com.sub.baoxian.news.service.NewsContent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 新闻内容定时爬取任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class NewsContentScheduleTask {

    private final NewsContent newsContent;

    /**
     * 每6小时执行一次新闻爬取任务
     * cron表达式：秒 分 时 日 月 周
     */
    @Scheduled(cron = "0 0 */6 * * ?")
    public void fetchNewsTask() {
        log.info("开始执行新闻爬取定时任务...");
        try {
            newsContent.insuranceNewsDb();
            log.info("新闻爬取定时任务执行完成");
        } catch (Exception e) {
            log.error("新闻爬取定时任务执行失败", e);
        }
    }


    @Scheduled(cron = "0 0/15 * * * ?")
    public void fetchNewsTelegramTask() {
        log.info("开始执行新闻爬取定时任务...");
        try {
            newsContent.insuranceNewsDb("rthk");
            log.info("新闻爬取定时任务执行完成");
        } catch (Exception e) {
            log.error("新闻爬取定时任务执行失败", e);
        }
    }



}
