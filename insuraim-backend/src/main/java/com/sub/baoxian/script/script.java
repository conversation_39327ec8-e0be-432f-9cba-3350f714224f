package com.sub.baoxian.script;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * 脚本工具类 - 使用纯JDBC实现
 */
public class script {

    // 数据库连接信息
    private static final String DB_URL = "jdbc:mysql://************:12306/insurance?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai";
    private static final String DB_USER = "zeyu";
    private static final String DB_PASSWORD = "Am123456.";
    
    // URL前缀替换配置
    private static final String OLD_PREFIX = "https://suboga.oss-ap-southeast-1.aliyuncs.com/";
    private static final String NEW_PREFIX = "https://resouce.insuraim.com/";
    
    /**
     * 主方法，支持独立运行
     */
    public static void main(String[] args) {
        try {
            int updatedCount = updateOssUrlPrefix();
            System.out.println("成功更新记录数: " + updatedCount);
        } catch (Exception e) {
            System.err.println("执行失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 更新OSS URL前缀
     * 将URL前缀从https://suboga.oss-ap-southeast-1.aliyuncs.com/替换为https://resouce.insuraim.com/
     * @return 更新的记录数
     * @throws SQLException 如果数据库操作失败
     */
    public static int updateOssUrlPrefix() throws SQLException {
        // 加载JDBC驱动
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
        } catch (ClassNotFoundException e) {
            System.err.println("未找到MySQL JDBC驱动: " + e.getMessage());
            throw new SQLException("未找到MySQL JDBC驱动", e);
        }
        
        Connection conn = null;
        PreparedStatement queryStmt = null;
        PreparedStatement updateStmt = null;
        ResultSet rs = null;
        int updateCount = 0;
        
        try {
            // 创建数据库连接
            System.out.println("正在连接数据库...");
            conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
            conn.setAutoCommit(false); // 开启事务
            
            // 查询包含旧前缀的记录
            String querySQL = "SELECT id, oss_url FROM ins_file WHERE oss_url LIKE ?";
            queryStmt = conn.prepareStatement(querySQL);
            queryStmt.setString(1, OLD_PREFIX + "%");
            rs = queryStmt.executeQuery();
            
            System.out.println("正在查询包含旧前缀的记录...");
            
            // 更新记录
            String updateSQL = "UPDATE ins_file SET oss_url = ? WHERE id = ?";
            updateStmt = conn.prepareStatement(updateSQL);
            
            // 逐条处理结果
            while (rs.next()) {
                long id = rs.getLong("id");
                String ossUrl = rs.getString("oss_url");
                
                // 替换URL前缀
                String newOssUrl = ossUrl.replace(OLD_PREFIX, NEW_PREFIX);
                
                // 执行更新
                updateStmt.setString(1, newOssUrl);
                updateStmt.setLong(2, id);
                updateStmt.addBatch();
                
                System.out.println("处理记录 ID: " + id + ", 旧URL: " + ossUrl + ", 新URL: " + newOssUrl);
            }
            
            // 执行批量更新
            int[] updateResults = updateStmt.executeBatch();
            for (int result : updateResults) {
                if (result > 0) {
                    updateCount += result;
                }
            }
            
            // 提交事务
            conn.commit();
            System.out.println("事务已提交，成功更新记录数: " + updateCount);
            
            return updateCount;
            
        } catch (SQLException e) {
            // 发生异常，回滚事务
            if (conn != null) {
                try {
                    conn.rollback();
                    System.err.println("事务已回滚");
                } catch (SQLException ex) {
                    System.err.println("回滚事务失败: " + ex.getMessage());
                }
            }
            System.err.println("数据库操作失败: " + e.getMessage());
            throw e;
        } finally {
            // 关闭资源
            closeResource(rs);
            closeResource(queryStmt);
            closeResource(updateStmt);
            closeResource(conn);
        }
    }
    
    /**
     * 关闭数据库资源
     * @param resource 需要关闭的资源
     */
    private static void closeResource(AutoCloseable resource) {
        if (resource != null) {
            try {
                resource.close();
            } catch (Exception e) {
                System.err.println("关闭资源失败: " + e.getMessage());
            }
        }
    }
}
