package com.sub.baoxian.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.common.exception.BizException;
import com.sub.baoxian.mapper.AdminMapper;
import com.sub.baoxian.model.dto.AdminLoginDTO;
import com.sub.baoxian.model.entity.Admin;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import cn.hutool.crypto.digest.BCrypt;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 管理员服务
 */
@Service
@RequiredArgsConstructor
public class AdminService {
    
    private final AdminMapper adminMapper;
    
    /**
     * 管理员登录
     * @param adminLoginDTO 管理员登录信息
     * @return 登录成功返回管理员信息，否则返回null
     */
    public Admin login(AdminLoginDTO adminLoginDTO) {
        if (!StringUtils.hasText(adminLoginDTO.getUsername()) && !StringUtils.hasText(adminLoginDTO.getEmail())) {
            throw new BizException(400, "用户名或邮箱不能为空");
        }
        if (!StringUtils.hasText(adminLoginDTO.getPassword())) {
            throw new BizException(400, "密码不能为空");
        }
        
        // 查询管理员
        LambdaQueryWrapper<Admin> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(adminLoginDTO.getUsername())) {
            queryWrapper.eq(Admin::getUsername, adminLoginDTO.getUsername());
        } else {
            queryWrapper.eq(Admin::getEmail, adminLoginDTO.getEmail());
        }
        
        Admin admin = adminMapper.selectOne(queryWrapper);
        
        if (admin == null) {
            throw new BizException(400, "管理员不存在");
        }
        
        // 检查账号状态
        if (admin.getStatus() != 1) {
            throw new BizException(400, "账号已被禁用");
        }
        
        // 验证密码
        boolean passwordValid = BCrypt.checkpw(adminLoginDTO.getPassword(), admin.getPassword());
        if (!passwordValid) {
            throw new BizException(400, "用户名或密码错误");
        }
        
        // 更新最后登录时间
        admin.setLastLoginAt(LocalDateTime.now());
        adminMapper.updateById(admin);
        
        // 清除密码
        admin.setPassword(null);
        return admin;
    }
    
    /**
     * 根据ID获取管理员信息
     * @param adminId 管理员ID
     * @return 管理员信息
     */
    public Admin getAdminById(Long adminId) {
        Admin admin = adminMapper.selectById(adminId);
        if (admin != null) {
            admin.setPassword(null);
        }
        return admin;
    }
    
    /**
     * 分页查询管理员列表
     * @param pageNum 页码
     * @param pageSize 每页记录数
     * @param keyword 关键字（用户名、邮箱）
     * @param status 状态
     * @return 分页结果
     */
    public Page<Admin> page(Integer pageNum, Integer pageSize, String keyword, Integer status) {
        // 构建查询条件
        LambdaQueryWrapper<Admin> wrapper = new LambdaQueryWrapper<>();
        
        // 关键字查询（匹配用户名、邮箱、手机号）
        if (StringUtils.hasText(keyword)) {
            wrapper.like(Admin::getUsername, keyword)
                   .or()
                   .like(Admin::getEmail, keyword)
                   .or()
                   .like(Admin::getMobile, keyword);
        }
        
        // 状态查询
        if (status != null) {
            wrapper.eq(Admin::getStatus, status);
        }
        
        // 排序 - 按创建时间降序
        wrapper.orderByDesc(Admin::getCreatedAt);
        
        // 分页查询
        Page<Admin> page = new Page<>(pageNum, pageSize);
        Page<Admin> result = adminMapper.selectPage(page, wrapper);
        
        // 清除敏感信息
        result.getRecords().forEach(admin -> admin.setPassword(null));
        
        return result;
    }
    
    /**
     * 新增管理员
     * @param admin 管理员信息
     * @return 操作结果
     */
    public boolean save(Admin admin) {
        // 检查用户名是否已存在
        if (StringUtils.hasText(admin.getUsername())) {
            LambdaQueryWrapper<Admin> usernameWrapper = new LambdaQueryWrapper<>();
            usernameWrapper.eq(Admin::getUsername, admin.getUsername());
            Long count = adminMapper.selectCount(usernameWrapper);
            if (count > 0) {
                throw new BizException(400, "用户名已存在");
            }
        } else {
            throw new BizException(400, "用户名不能为空");
        }
        
        // 检查邮箱是否已存在
        if (StringUtils.hasText(admin.getEmail())) {
            LambdaQueryWrapper<Admin> emailWrapper = new LambdaQueryWrapper<>();
            emailWrapper.eq(Admin::getEmail, admin.getEmail());
            Long count = adminMapper.selectCount(emailWrapper);
            if (count > 0) {
                throw new BizException(400, "邮箱已存在");
            }
        } else {
            throw new BizException(400, "邮箱不能为空");
        }
        
        // 检查密码
        if (!StringUtils.hasText(admin.getPassword())) {
            throw new BizException(400, "密码不能为空");
        }
        
        // 密码加密
        admin.setPassword(BCrypt.hashpw(admin.getPassword()));
        
        // 设置默认值
        if (admin.getStatus() == null) {
            admin.setStatus(1); // 默认启用
        }
        
        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        admin.setCreatedAt(now);
        admin.setUpdatedAt(now);
        
        // 保存管理员信息
        return adminMapper.insert(admin) > 0;
    }
    
    /**
     * 更新管理员信息
     * @param admin 管理员信息
     * @return 操作结果
     */
    public boolean update(Admin admin) {
        if (admin.getId() == null) {
            throw new BizException(400, "管理员ID不能为空");
        }
        
        // 查询原管理员信息
        Admin existingAdmin = adminMapper.selectById(admin.getId());
        if (existingAdmin == null) {
            throw new BizException(400, "管理员不存在");
        }
        
        // 检查用户名是否已被其他管理员使用
        if (StringUtils.hasText(admin.getUsername()) && !admin.getUsername().equals(existingAdmin.getUsername())) {
            LambdaQueryWrapper<Admin> usernameWrapper = new LambdaQueryWrapper<>();
            usernameWrapper.eq(Admin::getUsername, admin.getUsername())
                          .ne(Admin::getId, admin.getId());
            Long count = adminMapper.selectCount(usernameWrapper);
            if (count > 0) {
                throw new BizException(400, "用户名已存在");
            }
        }
        
        // 检查邮箱是否已被其他管理员使用
        if (StringUtils.hasText(admin.getEmail()) && !admin.getEmail().equals(existingAdmin.getEmail())) {
            LambdaQueryWrapper<Admin> emailWrapper = new LambdaQueryWrapper<>();
            emailWrapper.eq(Admin::getEmail, admin.getEmail())
                       .ne(Admin::getId, admin.getId());
            Long count = adminMapper.selectCount(emailWrapper);
            if (count > 0) {
                throw new BizException(400, "邮箱已存在");
            }
        }
        
        // 如果提供了密码，则进行加密
        if (StringUtils.hasText(admin.getPassword())) {
            admin.setPassword(BCrypt.hashpw(admin.getPassword()));
        } else {
            // 不更新密码
            admin.setPassword(null);
        }
        
        // 设置更新时间
        admin.setUpdatedAt(LocalDateTime.now());
        
        // 更新管理员信息
        return adminMapper.updateById(admin) > 0;
    }
    
    /**
     * 删除管理员
     * @param id 管理员ID
     * @return 操作结果
     */
    public boolean removeById(Long id) {
        if (id == null) {
            throw new BizException(400, "管理员ID不能为空");
        }
        
        // 查询管理员是否存在
        Admin admin = adminMapper.selectById(id);
        if (admin == null) {
            throw new BizException(400, "管理员不存在");
        }
        
        return adminMapper.deleteById(id) > 0;
    }
    
    /**
     * 批量删除管理员
     * @param ids 管理员ID列表
     * @return 操作结果
     */
    public boolean removeByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new BizException(400, "管理员ID列表不能为空");
        }
        
        return adminMapper.deleteBatchIds(ids) > 0;
    }
    
    /**
     * 修改管理员状态
     * @param id 管理员ID
     * @param status 状态(0禁用,1启用)
     * @return 操作结果
     */
    public boolean updateStatus(Long id, Integer status) {
        if (id == null) {
            throw new BizException(400, "管理员ID不能为空");
        }
        
        if (status == null || (status != 0 && status != 1)) {
            throw new BizException(400, "状态值无效");
        }
        
        // 查询管理员是否存在
        Admin admin = adminMapper.selectById(id);
        if (admin == null) {
            throw new BizException(400, "管理员不存在");
        }
        
        // 更新状态
        LambdaUpdateWrapper<Admin> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Admin::getId, id)
                    .set(Admin::getStatus, status)
                    .set(Admin::getUpdatedAt, LocalDateTime.now());
        
        return adminMapper.update(null, updateWrapper) > 0;
    }
    
    /**
     * 重置管理员密码
     * @param id 管理员ID
     * @param newPassword 新密码
     * @return 操作结果
     */
    public boolean resetPassword(Long id, String newPassword) {
        if (id == null) {
            throw new BizException(400, "管理员ID不能为空");
        }
        
        if (!StringUtils.hasText(newPassword)) {
            throw new BizException(400, "新密码不能为空");
        }
        
        // 查询管理员是否存在
        Admin admin = adminMapper.selectById(id);
        if (admin == null) {
            throw new BizException(400, "管理员不存在");
        }
        
        // 密码加密
        String hashedPassword = BCrypt.hashpw(newPassword);
        
        // 更新密码
        LambdaUpdateWrapper<Admin> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Admin::getId, id)
                    .set(Admin::getPassword, hashedPassword)
                    .set(Admin::getUpdatedAt, LocalDateTime.now());
        
        return adminMapper.update(null, updateWrapper) > 0;
    }
} 