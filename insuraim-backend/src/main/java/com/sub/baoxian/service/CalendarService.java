package com.sub.baoxian.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sub.baoxian.mapper.CalendarMapper;
import com.sub.baoxian.model.dto.CalendarDTO;
import com.sub.baoxian.model.entity.Calendar;
import com.sub.baoxian.util.StpKit;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 日程服务类
 */
@Service
@RequiredArgsConstructor
public class CalendarService {

    private final CalendarMapper calendarMapper;

    /**
     * 创建日程
     *
     * @param calendarDTO 日程信息
     * @return 创建的日程ID
     */
    public Long createCalendar(CalendarDTO calendarDTO) {
        Calendar calendar = new Calendar();
        BeanUtils.copyProperties(calendarDTO, calendar);

        calendar.setUserId(StpKit.USER.getLoginIdAsLong());
        // 设置时间
        long currentTime = System.currentTimeMillis();
        calendar.setCreateTime(currentTime);
        calendar.setUpdateTime(currentTime);

        // 设置默认状态
        if (calendar.getStatus() == null) {
            calendar.setStatus(0); // 默认进行中
        }

        calendarMapper.insert(calendar);
        return calendar.getId();
    }

    /**
     * 更新日程
     *
     * @param calendarDTO 日程信息
     * @return 是否更新成功
     */
    public boolean updateCalendar(CalendarDTO calendarDTO) {
        if (calendarDTO.getId() == null) {
            return false;
        }

        // 验证日程是否存在且属于当前用户
        Calendar existCalendar = calendarMapper.selectById(calendarDTO.getId());
        if (existCalendar == null || !existCalendar.getUserId().equals(StpKit.USER.getLoginIdAsLong())) {
            return false;
        }

        Calendar calendar = new Calendar();
        BeanUtils.copyProperties(calendarDTO, calendar);

        // 更新时间
        calendar.setUpdateTime(System.currentTimeMillis());

        int result = calendarMapper.updateById(calendar);
        return result > 0;
    }

    /**
     * 删除日程
     *
     * @param id 日程ID
     * @return 是否删除成功
     */
    public boolean deleteCalendar(Long id) {
        // 验证日程是否存在且属于当前用户
        Calendar existCalendar = calendarMapper.selectById(id);
        if (existCalendar == null || !existCalendar.getUserId().equals(StpKit.USER.getLoginIdAsLong())) {
            return false;
        }

        int result = calendarMapper.deleteById(id);
        return result > 0;
    }

    /**
     * 获取日程详情
     *
     * @param id 日程ID
     * @return 日程信息
     */
    public CalendarDTO getCalendarById(Long id) {
        Calendar calendar = calendarMapper.selectById(id);

        // 验证日程是否存在且属于当前用户
        if (calendar == null || !calendar.getUserId().equals(StpKit.USER.getLoginIdAsLong())) {
            return null;
        }

        CalendarDTO calendarDTO = new CalendarDTO();
        BeanUtils.copyProperties(calendar, calendarDTO);
        return calendarDTO;
    }

    /**
     * 查询日程列表
     *
     * @return 日程列表
     */
    public List<CalendarDTO> queryCalendars() {
        LambdaQueryWrapper<Calendar> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(Calendar::getUserId, StpKit.USER.getLoginIdAsLong());
        List<Calendar> calendarList = calendarMapper.selectList(wrapper);

        // 转换为DTO
        return calendarList.stream().map(calendar -> {
            CalendarDTO calendarDTO = new CalendarDTO();
            BeanUtils.copyProperties(calendar, calendarDTO);
            return calendarDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 更新日程状态
     *
     * @param id     日程ID
     * @param status 状态值
     * @return 是否更新成功
     */
    public boolean updateCalendarStatus(Long id, Integer status) {
        // 验证日程是否存在且属于当前用户
        Calendar existCalendar = calendarMapper.selectById(id);
        if (existCalendar == null || !existCalendar.getUserId().equals(StpKit.USER.getLoginIdAsLong())) {
            return false;
        }

        LambdaUpdateWrapper<Calendar> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Calendar::getId, id);
        updateWrapper.set(Calendar::getStatus, status);
        updateWrapper.set(Calendar::getUpdateTime, System.currentTimeMillis());

        int result = calendarMapper.update(null, updateWrapper);
        return result > 0;
    }

    /**
     * 查询所有需要提醒的日程
     *
     * @param currentTime 当前时间
     * @param futureTime  未来时间
     * @return 需要提醒的日程列表
     */
    public List<Calendar> findCalendarsToRemind(long currentTime, long futureTime) {
        LambdaQueryWrapper<Calendar> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(Calendar::getRemindTime, currentTime);
        wrapper.le(Calendar::getRemindTime, futureTime);
        wrapper.eq(Calendar::getRemindSent, 0).or().isNull(Calendar::getRemindSent);
        return calendarMapper.selectList(wrapper);
    }

    public void updateReminderSent(Long calendarId) {
        LambdaUpdateWrapper<Calendar> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Calendar::getId, calendarId);
        updateWrapper.set(Calendar::getRemindSent, 1);
        updateWrapper.set(Calendar::getUpdateTime, System.currentTimeMillis());
        calendarMapper.update(null, updateWrapper);
    }

    /**
     * 查找所有超时的进行中日程
     *
     * @param currentTime 当前时间
     * @return 超时的日程列表
     */
    public List<Calendar> findOverdueCalendars(long currentTime) {
        LambdaQueryWrapper<Calendar> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Calendar::getStatus, 0); // 状态为进行中
        wrapper.lt(Calendar::getCalendarTime, currentTime); // 日程时间小于当前时间
        wrapper.isNotNull(Calendar::getCalendarTime); // 日程时间不为空
        return calendarMapper.selectList(wrapper);
    }

    /**
     * 系统自动更新日程状态（不验证用户权限）
     *
     * @param id     日程ID
     * @param status 状态值
     * @return 是否更新成功
     */
    public boolean updateCalendarStatusBySystem(Long id, Integer status) {
        LambdaUpdateWrapper<Calendar> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Calendar::getId, id);
        updateWrapper.set(Calendar::getStatus, status);
        updateWrapper.set(Calendar::getUpdateTime, System.currentTimeMillis());

        int result = calendarMapper.update(null, updateWrapper);
        return result > 0;
    }
}
