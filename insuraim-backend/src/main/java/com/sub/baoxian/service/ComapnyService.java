package com.sub.baoxian.service;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.mapper.CompanyCreditRatingsMapper;
import com.sub.baoxian.mapper.CompanyFileMapper;
import com.sub.baoxian.mapper.CompanyLinkMapper;
import com.sub.baoxian.mapper.CompanyMapper;
import com.sub.baoxian.model.dto.CompanyCreateDTO;
import com.sub.baoxian.model.dto.CompanyCreditRatingCreateDTO;
import com.sub.baoxian.model.dto.CompanyCreditRatingUpdateDTO;
import com.sub.baoxian.model.dto.CompanyFileCreateDTO;
import com.sub.baoxian.model.dto.CompanyFileUpdateDTO;
import com.sub.baoxian.model.dto.CompanyLinkCreateDTO;
import com.sub.baoxian.model.dto.CompanyLinkUpdateDTO;
import com.sub.baoxian.model.dto.CompanyQueryDTO;
import com.sub.baoxian.model.dto.CompanyUpdateDTO;
import com.sub.baoxian.model.entity.Company;
import com.sub.baoxian.model.entity.CompanyCreditRatings;
import com.sub.baoxian.model.entity.CompanyFile;
import com.sub.baoxian.model.entity.CompanyLink;
import com.sub.baoxian.model.vo.InsurerDetailVO;
import com.sub.baoxian.util.OssUtil;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;

/**
 * 保险公司服务类
 */
@Service
@RequiredArgsConstructor
public class ComapnyService {

    private final CompanyMapper companyMapper;
    private final CompanyCreditRatingsMapper companyCreditRatingsMapper;
    private final CompanyFileMapper companyFileMapper;
    private final CompanyLinkMapper companyLinkMapper;

    /**
     * 分页查询保险公司列表
     *
     * @param page   分页参数
     * @param name   公司名称(可选，用于模糊查询)
     * @param region 地区(可选)
     * @return 分页结果
     */
    public IPage<Company> getCompanyPage(Page<Company> page, String search, String region) {
        LambdaQueryWrapper<Company> queryWrapper = new LambdaQueryWrapper<>();

        // 添加查询条件
        if (StrUtil.isNotBlank(search)) {
            queryWrapper.and(wrapper -> wrapper
                    .like(Company::getName, search)
                    .or()
                    .like(Company::getCode, search)
                    .or()
                    .like(Company::getRegion, search));
        }

        if (StrUtil.isNotBlank(region)) {
            queryWrapper.eq(Company::getRegion, region);
        }

        // 添加排序条件：按行业排名正序排列
        queryWrapper.orderByAsc(Company::getCompanyRank);

        // 执行分页查询
        return companyMapper.selectPage(page, queryWrapper);
    }


    /**
     * 获取保险公司列表
     * 
     * @param search
     * @param region
     * @return
     */
    public List<Company> getCompanyList(String search, String region) {
        LambdaQueryWrapper<Company> queryWrapper = new LambdaQueryWrapper<>();

        // 添加查询条件
        if (StrUtil.isNotBlank(search)) {
            queryWrapper.and(wrapper -> wrapper
                    .like(Company::getName, search)
                    .or()
                    .like(Company::getCode, search)
                    .or()
                    .like(Company::getRegion, search));
        }

        if (StrUtil.isNotBlank(region)) {
            queryWrapper.eq(Company::getRegion, region);
        }

        queryWrapper.orderByAsc(Company::getCompanyRank);

        // 执行查询
        return companyMapper.selectList(queryWrapper);
    }

    /**
     * 根据ID获取保险公司详情
     *
     * @param id 保险公司ID
     * @return 保险公司详情
     */
    public Company getInsurerById(Long id) {
        return companyMapper.selectById(id);
    }

    /**
     * 根据code获取保险公司详情
     *
     * @param code 保险公司code
     * @return 保险公司详情VO
     */
    public InsurerDetailVO getInsurerByCode(String code) {
        // 查询保险公司基本信息
        Company company = companyMapper.selectOne(
                new LambdaQueryWrapper<Company>()
                        .eq(Company::getCode, code));

        if (company == null) {
            return null;
        }

        // 创建VO对象并复制基本属性
        InsurerDetailVO vo = new InsurerDetailVO();
        BeanUtils.copyProperties(company, vo);

        // 获取信用评级列表
        List<CompanyCreditRatings> creditRatings = companyCreditRatingsMapper.selectList(
                new LambdaQueryWrapper<CompanyCreditRatings>()
                        .eq(CompanyCreditRatings::getCode, code));
        vo.setCreditRatings(creditRatings);

        // // 获取产品列表
        // List<Products> products = productsMapper.selectList(
        // new LambdaQueryWrapper<Products>()
        // .eq(Products::getCompanyCode, code)
        // );
        // vo.setProducts(products);

        // 获取文件列表
        List<CompanyFile> files = companyFileMapper.selectList(
                new LambdaQueryWrapper<CompanyFile>()
                        .eq(CompanyFile::getCode, code));
        vo.setFiles(files);

        // // 获取自定义文件列表
        // List<InsurerFile> customFiles = insurerFileMapper.selectList(
        // new LambdaQueryWrapper<InsurerFile>()
        // .eq(InsurerFile::getCode, code)
        // .eq(InsurerFile::getIsDeleted, 1)
        // );
        // vo.setCustomFiles(customFiles);

        // 获取链接列表
        List<CompanyLink> links = companyLinkMapper.selectList(
                new LambdaQueryWrapper<CompanyLink>()
                        .eq(CompanyLink::getCode, code));
        vo.setLinks(links);

        return vo;
    }

    /**
     * 分页查询保险公司列表（管理端）
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    public IPage<Company> getCompanyPageForManage(CompanyQueryDTO queryDTO) {
        Page<Company> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        LambdaQueryWrapper<Company> queryWrapper = new LambdaQueryWrapper<>();

        // 添加查询条件
        if (StrUtil.isNotBlank(queryDTO.getCode())) {
            queryWrapper.eq(Company::getCode, queryDTO.getCode());
        }
        if (StrUtil.isNotBlank(queryDTO.getName())) {
            queryWrapper.like(Company::getName, queryDTO.getName());
        }
        if (StrUtil.isNotBlank(queryDTO.getRegion())) {
            queryWrapper.eq(Company::getRegion, queryDTO.getRegion());
        }
        if (queryDTO.getStatus() != null) {
            queryWrapper.eq(Company::getStatus, queryDTO.getStatus());
        }
        if (StrUtil.isNotBlank(queryDTO.getKeyword())) {
            queryWrapper.and(wrapper -> wrapper
                    .like(Company::getName, queryDTO.getKeyword())
                    .or()
                    .like(Company::getCode, queryDTO.getKeyword()));
        }

        // 添加排序条件：按行业排名正序排列
        queryWrapper.orderByAsc(Company::getCompanyRank);

        return companyMapper.selectPage(page, queryWrapper);
    }

    /**
     * 创建保险公司
     *
     * @param createDTO 创建参数
     * @return 创建的保险公司
     */
    @Transactional
    public Company createCompany(CompanyCreateDTO createDTO) {
        // 检查代码是否已存在
        Company existingCompany = companyMapper.selectOne(
                new LambdaQueryWrapper<Company>()
                        .eq(Company::getCode, createDTO.getCode()));
        if (existingCompany != null) {
            throw new RuntimeException("保险公司代码已存在: " + createDTO.getCode());
        }

        Company company = new Company();
        BeanUtil.copyProperties(createDTO, company);

        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        company.setCreatedAt(now);
        company.setUpdatedAt(now);

        companyMapper.insert(company);
        return company;
    }

    /**
     * 更新保险公司
     *
     * @param updateDTO 更新参数
     * @return 更新后的保险公司
     */
    @Transactional
    public Company updateCompany(CompanyUpdateDTO updateDTO) {
        Company existingCompany = companyMapper.selectById(updateDTO.getId());
        if (existingCompany == null) {
            throw new RuntimeException("保险公司不存在，ID: " + updateDTO.getId());
        }

        // 如果更新代码，检查新代码是否已被其他公司使用
        if (StrUtil.isNotBlank(updateDTO.getCode()) && !updateDTO.getCode().equals(existingCompany.getCode())) {
            Company codeCheck = companyMapper.selectOne(
                    new LambdaQueryWrapper<Company>()
                            .eq(Company::getCode, updateDTO.getCode())
                            .ne(Company::getId, updateDTO.getId()));
            if (codeCheck != null) {
                throw new RuntimeException("保险公司代码已存在: " + updateDTO.getCode());
            }
        }

        // 复制非空字段
        BeanUtil.copyProperties(updateDTO, existingCompany, "id", "createdAt");

        // 更新时间
        existingCompany.setUpdatedAt(LocalDateTime.now());

        companyMapper.updateById(existingCompany);
        return existingCompany;
    }

    /**
     * 删除保险公司
     *
     * @param id 保险公司ID
     */
    @Transactional
    public void deleteCompany(Long id) {
        Company company = companyMapper.selectById(id);
        if (company == null) {
            throw new RuntimeException("保险公司不存在，ID: " + id);
        }

        // 删除保险公司
        companyMapper.deleteById(id);

        // 删除关联的信用评级
        companyCreditRatingsMapper.delete(
                new LambdaQueryWrapper<CompanyCreditRatings>()
                        .eq(CompanyCreditRatings::getCode, company.getCode()));

        // 删除关联的文件
        companyFileMapper.delete(
                new LambdaQueryWrapper<CompanyFile>()
                        .eq(CompanyFile::getCode, company.getCode()));

        // 删除关联的链接
        companyLinkMapper.delete(
                new LambdaQueryWrapper<CompanyLink>()
                        .eq(CompanyLink::getCode, company.getCode()));
    }

    // ==================== 信用评级管理 ====================

    /**
     * 添加信用评级
     */
    public CompanyCreditRatings addCreditRating(CompanyCreditRatingCreateDTO createDTO) {
        CompanyCreditRatings rating = new CompanyCreditRatings();
        BeanUtil.copyProperties(createDTO, rating);

        LocalDateTime now = LocalDateTime.now();
        rating.setCreateTime(now);
        rating.setUpdateTime(now);

        companyCreditRatingsMapper.insert(rating);
        return rating;
    }

    /**
     * 更新信用评级
     */
    public CompanyCreditRatings updateCreditRating(CompanyCreditRatingUpdateDTO updateDTO) {
        CompanyCreditRatings existingRating = companyCreditRatingsMapper.selectById(updateDTO.getId());
        if (existingRating == null) {
            throw new RuntimeException("信用评级不存在，ID: " + updateDTO.getId());
        }

        // 复制非空字段
        BeanUtil.copyProperties(updateDTO, existingRating, "id", "createTime");

        // 更新时间
        existingRating.setUpdateTime(LocalDateTime.now());

        companyCreditRatingsMapper.updateById(existingRating);
        return existingRating;
    }

    /**
     * 删除信用评级
     */
    public void deleteCreditRating(Integer id) {
        companyCreditRatingsMapper.deleteById(id);
    }

    /**
     * 根据公司代码获取信用评级列表
     */
    public List<CompanyCreditRatings> getCreditRatingsByCode(String code) {
        return companyCreditRatingsMapper.selectList(
                new LambdaQueryWrapper<CompanyCreditRatings>()
                        .eq(CompanyCreditRatings::getCode, code)
                        .orderByDesc(CompanyCreditRatings::getTime));
    }

    // ==================== 文件管理 ====================

    /**
     * 上传文件
     */
    public CompanyFile uploadFile(MultipartFile file, String code, String author) {
        try {
            // 验证文件
            if (file.isEmpty()) {
                throw new RuntimeException("上传的文件不能为空");
            }

            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null) {
                throw new RuntimeException("文件名不能为空");
            }

            // 验证保险公司是否存在
            Company company = companyMapper.selectOne(
                    new LambdaQueryWrapper<Company>()
                            .eq(Company::getCode, code));
            if (company == null) {
                throw new RuntimeException("保险公司不存在: " + code);
            }

            // 上传文件到OSS
            String fileName = originalFilename.substring(0, originalFilename.lastIndexOf("."));
            String fileExtension = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
            String uniqueFileName = fileName + "_" + System.currentTimeMillis() + "." + fileExtension;

            String ossUrl = OssUtil.uploadFile(file, "Insuriam/company-files", uniqueFileName);

            // 创建文件记录
            CompanyFile companyFile = new CompanyFile();
            companyFile.setCode(code);
            companyFile.setAuthor(author);
            companyFile.setFileName(originalFilename);
            companyFile.setFilePath(ossUrl);
            companyFile.setUploadTime(System.currentTimeMillis());

            LocalDateTime now = LocalDateTime.now();
            companyFile.setCreatedAt(now);
            companyFile.setUpdatedAt(now);

            companyFileMapper.insert(companyFile);
            return companyFile;
        } catch (Exception e) {
            throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
        }
    }

    /**
     * 添加文件
     */
    public CompanyFile addFile(CompanyFileCreateDTO createDTO) {
        CompanyFile file = new CompanyFile();
        BeanUtil.copyProperties(createDTO, file);

        LocalDateTime now = LocalDateTime.now();
        file.setCreatedAt(now);
        file.setUpdatedAt(now);

        if (createDTO.getUploadTime() == null) {
            file.setUploadTime(System.currentTimeMillis());
        }

        companyFileMapper.insert(file);
        return file;
    }

    /**
     * 更新文件
     */
    public CompanyFile updateFile(CompanyFileUpdateDTO updateDTO) {
        CompanyFile existingFile = companyFileMapper.selectById(updateDTO.getId());
        if (existingFile == null) {
            throw new RuntimeException("文件不存在，ID: " + updateDTO.getId());
        }

        // 复制非空字段
        BeanUtil.copyProperties(updateDTO, existingFile, "id", "createdAt");

        // 更新时间
        existingFile.setUpdatedAt(LocalDateTime.now());

        companyFileMapper.updateById(existingFile);
        return existingFile;
    }

    /**
     * 根据ID获取文件
     */
    public CompanyFile getFileById(Integer id) {
        return companyFileMapper.selectById(id);
    }

    /**
     * 删除文件
     */
    public void deleteFile(Integer id) {
        companyFileMapper.deleteById(id);
    }

    /**
     * 根据公司代码获取文件列表
     */
    public List<CompanyFile> getFilesByCode(String code) {
        return companyFileMapper.selectList(
                new LambdaQueryWrapper<CompanyFile>()
                        .eq(CompanyFile::getCode, code)
                        .orderByDesc(CompanyFile::getUploadTime));
    }

    // ==================== 链接管理 ====================

    /**
     * 添加链接
     */
    public CompanyLink addLink(CompanyLinkCreateDTO createDTO) {
        CompanyLink link = new CompanyLink();
        BeanUtil.copyProperties(createDTO, link);

        LocalDateTime now = LocalDateTime.now();
        link.setCreatedAt(now);
        link.setUpdatedAt(now);

        companyLinkMapper.insert(link);
        return link;
    }

    /**
     * 更新链接
     */
    public CompanyLink updateLink(CompanyLinkUpdateDTO updateDTO) {
        CompanyLink existingLink = companyLinkMapper.selectById(updateDTO.getId());
        if (existingLink == null) {
            throw new RuntimeException("链接不存在，ID: " + updateDTO.getId());
        }

        // 复制非空字段
        BeanUtil.copyProperties(updateDTO, existingLink, "id", "createdAt");

        // 更新时间
        existingLink.setUpdatedAt(LocalDateTime.now());

        companyLinkMapper.updateById(existingLink);
        return existingLink;
    }

    /**
     * 删除链接
     */
    public void deleteLink(Integer id) {
        companyLinkMapper.deleteById(id);
    }

    /**
     * 根据公司代码获取链接列表
     */
    public List<CompanyLink> getLinksByCode(String code) {
        return companyLinkMapper.selectList(
                new LambdaQueryWrapper<CompanyLink>()
                        .eq(CompanyLink::getCode, code)
                        .orderByDesc(CompanyLink::getCreatedAt));
    }

}
