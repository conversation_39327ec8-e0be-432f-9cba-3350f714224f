package com.sub.baoxian.service;


import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.mapper.CustomerMapper;
import com.sub.baoxian.model.entity.Customer;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class CustomerService {

    private final CustomerMapper customerMapper;

    /**
     * 分页获取客户列表
     * 
     * @param page     页码
     * @param pageSize 每页条数
     * @return 客户列表
     */
    public IPage<Customer> getCustomerPageList(Integer page, Integer pageSize) {
        Page<Customer> customerPage = new Page<>(page, pageSize);
        IPage<Customer> customerPageList = customerMapper.selectPage(customerPage, null);
        return customerPageList;
    }

    /**
     * 根据ID获取客户详情
     * 
     * @param id 客户ID
     * @return 客户详情
     */
    public Customer getCustomerById(Long id) {
        return customerMapper.selectById(id);
    }

}