package com.sub.baoxian.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sub.baoxian.common.exception.BizException;
import com.sub.baoxian.mapper.ExamCategoryMapper;
import com.sub.baoxian.mapper.ExamQuestionMapper;
import com.sub.baoxian.model.dto.QuestionAnswerDTO;
import com.sub.baoxian.model.entity.ExamAnswer;
import com.sub.baoxian.model.entity.ExamCategory;
import com.sub.baoxian.model.vo.ExamQuestionVO;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class ExamService {

    private final ExamCategoryMapper examCategoryMapper;
    private final ExamQuestionMapper examQuestionMapper;

    /**
     * 获取模拟考试分类列表
     */
    public List<ExamCategory> getExamCategoryList() {
        try {
            return examCategoryMapper.selectList(new LambdaQueryWrapper<ExamCategory>().eq(ExamCategory::getStatus, 1));
        } catch (Exception e) {
            throw new BizException("获取模拟考试分类列表失败", e);
        }
    }

    
    /**
     * 获取模拟考试题目列表(使用LEFT JOIN优化查询)
     * 
     * @param categoryId 类目ID
     * @return 包含所有答案选项的题目VO列表
     */
    public List<ExamQuestionVO> getExamQuestionVOList(Integer categoryId) {
        try {
            // 执行JOIN查询，获取扁平化结果
            List<QuestionAnswerDTO> dtoList = examQuestionMapper.getQuestionsAndAnswersByCategoryId(categoryId);
            
            // 使用Map进行分组
            Map<Long, ExamQuestionVO> questionMap = new HashMap<>();
            
            // 处理每一行数据
            for (QuestionAnswerDTO dto : dtoList) {
                Long questionId = dto.getId();
                
                // 如果Map中还没有这个题目，就创建并添加
                if (!questionMap.containsKey(questionId)) {
                    ExamQuestionVO questionVO = new ExamQuestionVO();
                    questionVO.setId(dto.getId());
                    questionVO.setExamId(dto.getExamId());
                    questionVO.setDifficulty(dto.getDifficulty());
                    questionVO.setMajorChapter(dto.getMajorChapter());
                    questionVO.setChapter(dto.getChapter());
                    questionVO.setQuestion(dto.getQuestion());
                    questionVO.setFeedbackCorrect(dto.getFeedbackCorrect());
                    questionVO.setFeedbackIncorrect(dto.getFeedbackIncorrect());
                    questionVO.setStatus(dto.getStatus());
                    questionVO.setAnswer(new ArrayList<>());
                    
                    questionMap.put(questionId, questionVO);
                }
                
                // 如果答案ID不为空，创建答案对象并关联到题目
                if (dto.getAnswerId() != null) {
                    ExamAnswer answer = new ExamAnswer();
                    answer.setId(dto.getAnswerId());
                    answer.setQuestionId(dto.getQuestionId());
                    answer.setOptionText(dto.getOptionText());
                    answer.setIsCorrect(dto.getIsCorrect());
                    answer.setCreatedAt(dto.getAnswerCreatedAt());
                    answer.setUpdatedAt(dto.getAnswerUpdatedAt());
                    
                    questionMap.get(questionId).getAnswer().add(answer);
                }
            }
            
            return new ArrayList<>(questionMap.values());
        } catch (Exception e) {
            throw new BizException("获取模拟考试题目列表失败", e);
        }
    }
}
