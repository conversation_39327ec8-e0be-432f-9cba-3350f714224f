package com.sub.baoxian.service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.mapper.FileMapper;
import com.sub.baoxian.model.entity.File;
import com.sub.baoxian.util.OssUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 文件服务类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FileService {
    private final FileMapper fileMapper;

    /**
     * 上传文件
     * 
     * @param multipartFile 上传的文件对象
     * @param folderId      文件夹ID
     * @param uploaderId    上传者ID
     * @param category      文件分类
     * @return 文件信息
     */
    public File uploadFile(MultipartFile multipartFile, Long folderId, Long uploaderId) throws IOException {
        String originalFilename = multipartFile.getOriginalFilename();
        // 获取文件名字
        String fileName = originalFilename.substring(0, originalFilename.lastIndexOf("."));
        String fileType = getFileType(originalFilename);
        long fileSize = multipartFile.getSize();

        // 使用OSS工具类上传文件
        String ossUrl = OssUtil.uploadFileWithCustomName(multipartFile,
                fileName + "_" + System.currentTimeMillis() + "." + fileType);

        // 保存文件信息到数据库
        File file = File.builder()
                .name(originalFilename)
                .folderId(folderId)
                .fileType(fileType)
                .size(fileSize)
                .ossUrl(ossUrl)
                .createAt(LocalDateTime.now())
                .updateAt(LocalDateTime.now())
                .uploaderId(uploaderId)
                .isDeleted(false)
                .build();

        fileMapper.insert(file);
        return file;
    }

    /**
     * 根据ID获取文件信息
     * 
     * @param id 文件ID
     * @return 文件信息
     */
    public File getFileById(Long id) {
        return fileMapper.selectById(id);
    }

    /**
     * 获取文件夹中的文件列表
     * 
     * @param folderId 文件夹ID
     * @param fileType 文件类型（可选）
     * @return 文件列表
     */
    public List<File> listFiles(Long folderId, String fileType) {
        LambdaQueryWrapper<File> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(File::getFolderId, folderId)
                .eq(StringUtils.hasText(fileType), File::getFileType, fileType)
                .orderByDesc(File::getCreateAt);
        return fileMapper.selectList(queryWrapper);
    }

    /**
     * 分页查询文件
     * 
     * @param page     页码
     * @param size     每页大小
     * @param folderId 文件夹ID
     * @param keyword  关键字（文件名）
     * @return 分页结果
     */
    public Page<File> pageFiles(int page, int size, Long folderId, String keyword) {
        Page<File> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<File> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(folderId != null, File::getFolderId, folderId)
                .like(StringUtils.hasText(keyword), File::getName, keyword)
                .orderByDesc(File::getCreateAt);
        return fileMapper.selectPage(pageParam, queryWrapper);
    }

    /**
     * 更新文件信息
     * 
     * @param file 文件信息
     * @return 是否更新成功
     */
    public boolean updateFile(File file) {
        file.setUpdateAt(LocalDateTime.now());
        return fileMapper.updateById(file) > 0;
    }

    /**
     * 删除文件
     * 
     * @param id 文件ID
     * @return 是否删除成功
     */
    public boolean deleteFile(Long id) {
        File file = fileMapper.selectById(id);
        if (file != null && file.getOssUrl() != null) {
            try {
                // 从完整URL中提取对象名
                String objectName = extractObjectNameFromUrl(file.getOssUrl());
                // 从OSS删除文件
                OssUtil.deleteFile(objectName);
            } catch (Exception e) {
                log.error("删除OSS文件失败: {}", e.getMessage());
                // 如果删除OSS文件失败，记录错误但继续删除数据库记录
            }
        }
        return fileMapper.deleteById(id) > 0;
    }

    /**
     * 从OSS获取文件内容
     * 
     * @param ossUrl OSS访问地址
     * @return 文件字节数组
     */
    public byte[] getFileContent(String ossUrl) {
        // 从OSS URL中提取对象名
        String objectName = extractObjectNameFromUrl(ossUrl);
        // 使用OSS工具类下载文件内容
        return OssUtil.downloadToBytes(objectName);
    }

    /**
     * 从完整的OSS URL中提取对象名
     * 
     * @param ossUrl OSS完整URL
     * @return 对象名（包括路径）
     */
    private String extractObjectNameFromUrl(String ossUrl) {
        // 简单规则：通常OSS URL格式为https://bucket.endpoint/objectName
        // 我们尝试提取最后一部分作为objectName

        if (ossUrl == null || ossUrl.isEmpty()) {
            return "";
        }

        // 移除查询参数
        if (ossUrl.contains("?")) {
            ossUrl = ossUrl.substring(0, ossUrl.indexOf("?"));
        }

        // 如果URL包含协议，如http://或https://，移除该部分
        if (ossUrl.contains("://")) {
            ossUrl = ossUrl.substring(ossUrl.indexOf("://") + 3);
        }

        // 移除域名部分 (第一个斜杠之前的部分)
        if (ossUrl.contains("/")) {
            ossUrl = ossUrl.substring(ossUrl.indexOf("/") + 1);
        }

        return ossUrl;
    }

    /**
     * 获取文件类型
     * 
     * @param filename 文件名
     * @return 文件类型（扩展名）
     */
    private String getFileType(String filename) {
        if (filename == null || !filename.contains(".")) {
            return "unknown";
        }
        return filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
    }
}