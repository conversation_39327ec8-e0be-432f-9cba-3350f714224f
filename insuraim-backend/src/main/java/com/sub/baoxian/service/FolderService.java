package com.sub.baoxian.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.model.entity.Folder;
import com.sub.baoxian.mapper.FolderMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文件夹服务类
 */
@Service
@RequiredArgsConstructor
public class FolderService {
    private final FolderMapper folderMapper;

    /**
     * 创建文件夹
     * @param folder 文件夹信息
     * @return 创建的文件夹
     */
    public Folder createFolder(Folder folder) {
        folder.setCreateAt(LocalDateTime.now());
        folder.setUpdateAt(LocalDateTime.now());
        folder.setIsDeleted(false);
        folderMapper.insert(folder);
        return folder;
    }

    /**
     * 根据ID获取文件夹
     * @param id 文件夹ID
     * @return 文件夹信息
     */
    public Folder getFolderById(Long id) {
        return folderMapper.selectById(id);
    }

    /**
     * 获取文件夹列表
     * @param parentId 父文件夹ID，如果为0则获取根文件夹
     * @return 文件夹列表
     */
    public List<Folder> listFolders(Long parentId) {
        LambdaQueryWrapper<Folder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(parentId != null, Folder::getParentId, parentId)
                    .orderByAsc(Folder::getName);
        return folderMapper.selectList(queryWrapper);
    }
    
    /**
     * 更新文件夹
     * @param folder 文件夹信息
     * @return 是否更新成功
     */
    public boolean updateFolder(Folder folder) {
        folder.setUpdateAt(LocalDateTime.now());
        return folderMapper.updateById(folder) > 0;
    }

    /**
     * 删除文件夹
     * @param id 文件夹ID
     * @return 是否删除成功
     */
    public boolean deleteFolder(Long id) {
        return folderMapper.deleteById(id) > 0;
    }

    /**
     * 获取文件夹树形结构
     * @param parentId 父级ID，如果为null则获取完整树
     * @return 文件夹列表
     */
    public List<Folder> getFolderTree(Long parentId) {
        LambdaQueryWrapper<Folder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(parentId != null, Folder::getParentId, parentId)
                    .orderByAsc(Folder::getName);
        return folderMapper.selectList(queryWrapper);
    }

    /**
     * 分页查询文件夹
     * @param page 页码
     * @param size 每页大小
     * @param parentId 父文件夹ID
     * @param keyword 关键字（文件夹名）
     * @return 分页结果
     */
    public Page<Folder> pageFolders(int page, int size, Long parentId, String keyword) {
        Page<Folder> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<Folder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(parentId != null, Folder::getParentId, parentId)
                    .like(StringUtils.hasText(keyword), Folder::getName, keyword)
                    .orderByAsc(Folder::getName);
        return folderMapper.selectPage(pageParam, queryWrapper);
    }
}