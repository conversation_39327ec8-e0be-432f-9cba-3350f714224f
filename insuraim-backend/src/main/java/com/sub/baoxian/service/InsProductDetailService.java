package com.sub.baoxian.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.common.exception.BizException;
import com.sub.baoxian.mapper.InsProductDetailContentMapper;
import com.sub.baoxian.mapper.InsProductDetailTitleMapper;
import com.sub.baoxian.model.dto.InsProductDetailContentDTO;
import com.sub.baoxian.model.dto.InsProductDetailTitleDTO;
import com.sub.baoxian.model.dto.ProductDetailQueryDTO;
import com.sub.baoxian.model.entity.InsProductDetailContent;
import com.sub.baoxian.model.entity.InsProductDetailTitle;
import com.sub.baoxian.model.vo.InsProductDetailContentVO;
import com.sub.baoxian.model.vo.InsProductDetailTitleVO;
import com.sub.baoxian.model.vo.ProductDetailStructuredVO;
import com.sub.baoxian.model.vo.ProductDetailTreeVO;

import cn.hutool.core.bean.BeanUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 产品详情服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InsProductDetailService {

    private final InsProductDetailTitleMapper titleMapper;
    private final InsProductDetailContentMapper contentMapper;

    // ==================== 标题相关操作 ====================

    /**
     * 创建标题
     */
    @Transactional
    public Integer createTitle(InsProductDetailTitleDTO dto) {
        InsProductDetailTitle title = BeanUtil.copyProperties(dto, InsProductDetailTitle.class);

        // 如果没有指定排序，自动设置为最大值+1
        if (title.getRankValue() == null) {
            Integer maxRank = titleMapper.getMaxRank(title.getCategoryId(), title.getParentId());
            title.setRankValue(maxRank == null ? 1 : maxRank + 1);
        }

        long currentTime = System.currentTimeMillis();
        title.setCreatedAt(currentTime);
        title.setUpdatedAt(currentTime);

        titleMapper.insert(title);
        return title.getId();
    }

    /**
     * 更新标题
     */
    @Transactional
    public boolean updateTitle(InsProductDetailTitleDTO dto) {
        if (dto.getId() == null) {
            throw new IllegalArgumentException("更新标题时ID不能为空");
        }

        InsProductDetailTitle title = BeanUtil.copyProperties(dto, InsProductDetailTitle.class);
        title.setUpdatedAt(System.currentTimeMillis());

        return titleMapper.updateById(title) > 0;
    }

    /**
     * 删除标题（递归删除所有子标题和关联内容）
     */
    @Transactional
    public boolean deleteTitle(Integer id) {
        return deleteTitleRecursively(id);
    }

    /**
     * 递归删除标题及其所有子标题和关联内容
     */
    private boolean deleteTitleRecursively(Integer id) {
        // 1. 先查找所有子标题
        LambdaQueryWrapper<InsProductDetailTitle> titleWrapper = new LambdaQueryWrapper<>();
        titleWrapper.eq(InsProductDetailTitle::getParentId, id);
        List<InsProductDetailTitle> childTitles = titleMapper.selectList(titleWrapper);

        // 2. 递归删除所有子标题
        for (InsProductDetailTitle childTitle : childTitles) {
            deleteTitleRecursively(childTitle.getId());
        }

        // 3. 删除当前标题关联的所有内容
        LambdaQueryWrapper<InsProductDetailContent> contentWrapper = new LambdaQueryWrapper<>();
        contentWrapper.eq(InsProductDetailContent::getTitleId, id);
        contentMapper.delete(contentWrapper);

        // 4. 删除当前标题
        return titleMapper.deleteById(id) > 0;
    }

    /**
     * 根据ID查询标题
     */
    public InsProductDetailTitleVO getTitleById(Integer id) {
        InsProductDetailTitle title = titleMapper.selectById(id);
        return title == null ? null : BeanUtil.copyProperties(title, InsProductDetailTitleVO.class);
    }

    /**
     * 根据分类ID查询标题树形结构
     */
    public List<InsProductDetailTitleVO> getTitleTreeByCategoryId(Integer categoryId) {
        List<InsProductDetailTitleVO> allTitles = titleMapper.selectByCategoryId(categoryId);
        return buildTitleTree(allTitles, null);
    }

    /**
     * 分页查询标题
     */
    public IPage<InsProductDetailTitleVO> getTitlePage(ProductDetailQueryDTO queryDTO) {
        Page<InsProductDetailTitleVO> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        return titleMapper.selectTitlePage(page, queryDTO.getCategoryId(),
                queryDTO.getParentId(), queryDTO.getTitleName());
    }

    // ==================== 内容相关操作 ====================

    /**
     * 创建或更新内容
     */
    @Transactional
    public Integer saveContent(InsProductDetailContentDTO dto) {
        // 校验：一级标题不可以添加内容
        InsProductDetailTitle title = titleMapper.selectById(dto.getTitleId());
        if (title == null) {
            throw new BizException(400, "标题不存在");
        }
        if (title.getParentId() == null) {
            throw new BizException(400, "一级标题不可以添加内容，请选择子标题");
        }

        // 先查询是否已存在
        InsProductDetailContentVO existing = contentMapper.selectByProductIdAndTitleId(
                dto.getProductId(), dto.getTitleId());

        long currentTime = System.currentTimeMillis();

        if (existing != null) {
            // 更新现有内容
            InsProductDetailContent content = new InsProductDetailContent();
            content.setId(existing.getId());
            content.setContentValue(dto.getContentValue());
            content.setUpdatedAt(currentTime);
            contentMapper.updateById(content);
            return existing.getId();
        } else {
            // 创建新内容
            InsProductDetailContent content = BeanUtil.copyProperties(dto, InsProductDetailContent.class);
            content.setCreatedAt(currentTime);
            content.setUpdatedAt(currentTime);
            contentMapper.insert(content);
            return content.getId();
        }
    }

    /**
     * 删除内容
     */
    @Transactional
    public boolean deleteContent(Integer id) {
        return contentMapper.deleteById(id) > 0;
    }

    /**
     * 根据产品ID和标题ID删除内容
     */
    @Transactional
    public boolean deleteContentByProductIdAndTitleId(Integer productId, Integer titleId) {
        LambdaQueryWrapper<InsProductDetailContent> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InsProductDetailContent::getProductId, productId)
                .eq(InsProductDetailContent::getTitleId, titleId);
        return contentMapper.delete(wrapper) > 0;
    }

    /**
     * 根据产品ID查询内容列表
     */
    public List<InsProductDetailContentVO> getContentsByProductId(Integer productId) {
        return contentMapper.selectByProductId(productId);
    }

    /**
     * 根据标题ID查询内容列表
     */
    public List<InsProductDetailContentVO> getContentsByTitleId(Integer titleId) {
        return contentMapper.selectByTitleId(titleId);
    }

    /**
     * 分页查询内容
     */
    public IPage<InsProductDetailContentVO> getContentPage(ProductDetailQueryDTO queryDTO) {
        Page<InsProductDetailContentVO> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        return contentMapper.selectContentPage(page, queryDTO.getProductId(),
                queryDTO.getTitleId(), queryDTO.getContentValue());
    }

    // ==================== 综合查询 ====================

    /**
     * 根据分类ID和产品ID查询完整的产品详情树形结构
     */
    public List<ProductDetailTreeVO> getProductDetailTree(Integer categoryId, Integer productId) {
        // 获取标题树形结构
        List<InsProductDetailTitleVO> titleTree = getTitleTreeByCategoryId(categoryId);

        // 获取产品的所有内容
        List<InsProductDetailContentVO> contents = contentMapper.selectByProductId(productId);
        Map<Integer, List<InsProductDetailContentVO>> contentMap = contents.stream()
                .collect(Collectors.groupingBy(InsProductDetailContentVO::getTitleId));

        // 构建产品详情树
        return buildProductDetailTree(titleTree, contentMap);
    }

    /**
     * 根据分类ID和产品ID查询结构化的产品详情
     */
    public List<ProductDetailStructuredVO> getProductDetailStructured(Integer categoryId, Integer productId) {
        // 获取顶级标题（parent_id为null的标题）
        List<InsProductDetailTitleVO> topLevelTitles = titleMapper.selectByCategoryId(categoryId)
                .stream()
                .filter(title -> title.getParentId() == null)
                .sorted((a, b) -> Integer.compare(a.getRankValue(), b.getRankValue()))
                .collect(Collectors.toList());

        // 获取产品的所有内容
        List<InsProductDetailContentVO> contents = contentMapper.selectByProductId(productId);
        Map<Integer, List<InsProductDetailContentVO>> contentMap = contents.stream()
                .collect(Collectors.groupingBy(InsProductDetailContentVO::getTitleId));

        // 获取所有子标题
        List<InsProductDetailTitleVO> allTitles = titleMapper.selectByCategoryId(categoryId);
        Map<Integer, List<InsProductDetailTitleVO>> childTitleMap = allTitles.stream()
                .filter(title -> title.getParentId() != null)
                .collect(Collectors.groupingBy(InsProductDetailTitleVO::getParentId));

        // 构建结构化数据
        return buildStructuredData(topLevelTitles, childTitleMap, contentMap);
    }

    // ==================== 私有方法 ====================

    /**
     * 构建标题树形结构
     */
    private List<InsProductDetailTitleVO> buildTitleTree(List<InsProductDetailTitleVO> allTitles, Integer parentId) {
        return allTitles.stream()
                .filter(title -> {
                    if (parentId == null) {
                        return title.getParentId() == null;
                    } else {
                        return parentId.equals(title.getParentId());
                    }
                })
                .sorted((a, b) -> Integer.compare(a.getRankValue(), b.getRankValue()))
                .peek(title -> {
                    List<InsProductDetailTitleVO> children = buildTitleTree(allTitles, title.getId());
                    title.setChildren(children);
                })
                .collect(Collectors.toList());
    }

    /**
     * 构建产品详情树形结构
     */
    private List<ProductDetailTreeVO> buildProductDetailTree(List<InsProductDetailTitleVO> titleTree,
            Map<Integer, List<InsProductDetailContentVO>> contentMap) {
        List<ProductDetailTreeVO> result = new ArrayList<>();

        for (InsProductDetailTitleVO title : titleTree) {
            ProductDetailTreeVO treeNode = new ProductDetailTreeVO();
            treeNode.setTitleId(title.getId());
            treeNode.setTitleName(title.getTitleName());
            treeNode.setParentId(title.getParentId());
            treeNode.setRankValue(title.getRankValue());

            // 设置内容
            List<InsProductDetailContentVO> titleContents = contentMap.get(title.getId());
            treeNode.setContents(titleContents != null ? titleContents : new ArrayList<>());

            // 递归处理子标题
            if (title.getChildren() != null && !title.getChildren().isEmpty()) {
                treeNode.setChildren(buildProductDetailTree(title.getChildren(), contentMap));
            }

            result.add(treeNode);
        }

        return result;
    }

    /**
     * 构建结构化数据
     */
    private List<ProductDetailStructuredVO> buildStructuredData(List<InsProductDetailTitleVO> topLevelTitles,
            Map<Integer, List<InsProductDetailTitleVO>> childTitleMap,
            Map<Integer, List<InsProductDetailContentVO>> contentMap) {
        List<ProductDetailStructuredVO> result = new ArrayList<>();

        for (InsProductDetailTitleVO topTitle : topLevelTitles) {
            ProductDetailStructuredVO structuredVO = new ProductDetailStructuredVO();

            structuredVO.setName(topTitle.getTitleName());
            structuredVO.setRankValue(topTitle.getRankValue());

            // 构建attributes列表
            List<ProductDetailStructuredVO.AttributeInfo> attributes = new ArrayList<>();

            // 获取该顶级标题下的子标题
            List<InsProductDetailTitleVO> childTitles = childTitleMap.get(topTitle.getId());
            if (childTitles != null) {
                childTitles.sort((a, b) -> Integer.compare(a.getRankValue(), b.getRankValue()));

                for (InsProductDetailTitleVO childTitle : childTitles) {
                    // 获取该子标题对应的内容
                    List<InsProductDetailContentVO> titleContents = contentMap.get(childTitle.getId());

                    ProductDetailStructuredVO.AttributeInfo attribute = new ProductDetailStructuredVO.AttributeInfo();
                    attribute.setName(childTitle.getTitleName());
                    attribute.setRankValue(childTitle.getRankValue());
                    attribute.setAttribute(generateAttributeKey(topTitle.getTitleName(), childTitle.getTitleName()));

                    if (titleContents != null && !titleContents.isEmpty()) {
                        // 如果有多个内容，可以合并或取第一个，这里取第一个
                        InsProductDetailContentVO content = titleContents.get(0);
                        attribute.setValue(content.getContentValue());
                    } else {
                        // 如果没有内容，设置默认值
                        attribute.setValue("-");
                    }

                    attributes.add(attribute);
                }
            }

            structuredVO.setAttributes(attributes);
            result.add(structuredVO);
        }

        return result;
    }

    /**
     * 生成属性key
     */
    private String generateAttributeKey(String parentTitle, String childTitle) {
        // 生成类似 "ANBasicInformation_AgeCalculationRules" 的格式
        String parentKey = parentTitle.replaceAll("[\\s\\u4e00-\\u9fa5]", "");
        String childKey = childTitle.replaceAll("[\\s\\u4e00-\\u9fa5]", "");
        return parentKey + "_" + childKey;
    }
}
