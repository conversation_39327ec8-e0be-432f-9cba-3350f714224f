package com.sub.baoxian.service;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.common.exception.BizException;
import com.sub.baoxian.mapper.InsProductIntroductionMapper;
import com.sub.baoxian.mapper.InsProductMapper;
import com.sub.baoxian.model.dto.InsProductIntroductionCreateDTO;
import com.sub.baoxian.model.dto.InsProductIntroductionQueryDTO;
import com.sub.baoxian.model.dto.InsProductIntroductionUpdateDTO;
import com.sub.baoxian.model.entity.InsProduct;
import com.sub.baoxian.model.entity.InsProductIntroduction;
import com.sub.baoxian.model.vo.InsProductIntroductionVO;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 产品介绍服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InsProductIntroductionService {

    private final InsProductIntroductionMapper insProductIntroductionMapper;
    private final InsProductMapper insProductMapper;

    // ==================== 私有工具方法 ====================

    /**
     * 参数校验 - 产品介绍ID
     */
    private void validateIntroductionId(Long id) {
        if (id == null) {
            throw new BizException("产品介绍ID不能为空");
        }
    }

    /**
     * 参数校验 - 产品ID
     */
    private void validateProductId(Long productId) {
        if (productId == null) {
            throw new BizException("产品ID不能为空");
        }
    }

    /**
     * 检查产品是否存在
     */
    private InsProduct validateAndGetProduct(Long productId) {
        InsProduct product = insProductMapper.selectById(productId);
        if (product == null) {
            throw new BizException("产品不存在");
        }
        return product;
    }

    /**
     * 检查产品介绍是否存在
     */
    private InsProductIntroduction validateAndGetIntroduction(Long id) {
        InsProductIntroduction introduction = insProductIntroductionMapper.selectById(id);
        if (introduction == null) {
            throw new BizException("产品介绍不存在");
        }
        return introduction;
    }

    /**
     * 根据产品ID查询产品介绍实体
     */
    private InsProductIntroduction getIntroductionEntityByProductId(Long productId) {
        LambdaQueryWrapper<InsProductIntroduction> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InsProductIntroduction::getProductId, productId);
        return insProductIntroductionMapper.selectOne(queryWrapper);
    }

    /**
     * 检查产品介绍是否已存在
     */
    private void checkIntroductionNotExists(Long productId) {
        InsProductIntroduction existing = getIntroductionEntityByProductId(productId);
        if (existing != null) {
            throw new BizException("该产品的介绍已存在，请使用更新功能");
        }
    }

    /**
     * 转换产品特点 - DTO转Entity
     */
    private List<InsProductIntroduction.ProductFeatureItem> convertFeaturesToEntity(
            List<? extends Object> featureDTOs) {
        if (featureDTOs == null) {
            return null;
        }
        return featureDTOs.stream()
                .map(featureDTO -> {
                    InsProductIntroduction.ProductFeatureItem feature = new InsProductIntroduction.ProductFeatureItem();
                    BeanUtil.copyProperties(featureDTO, feature);
                    return feature;
                }).collect(Collectors.toList());
    }

    /**
     * 转换产品特点 - Entity转VO
     */
    private List<InsProductIntroductionVO.ProductFeatureItemVO> convertFeaturesToVO(
            List<InsProductIntroduction.ProductFeatureItem> features) {
        if (features == null) {
            return null;
        }
        return features.stream()
                .map(feature -> {
                    InsProductIntroductionVO.ProductFeatureItemVO featureVO = new InsProductIntroductionVO.ProductFeatureItemVO();
                    BeanUtil.copyProperties(feature, featureVO);
                    return featureVO;
                }).collect(Collectors.toList());
    }

    /**
     * 获取产品名称（安全获取，失败时返回空字符串）
     */
    private String getProductNameSafely(Long productId) {
        try {
            InsProduct product = insProductMapper.selectById(productId);
            return product != null ? product.getProductName() : "";
        } catch (Exception e) {
            log.warn("获取产品名称失败，产品ID: {}", productId, e);
            return "";
        }
    }

    /**
     * 将Entity转换为VO
     */
    private InsProductIntroductionVO convertToVO(InsProductIntroduction entity) {
        InsProductIntroductionVO vo = new InsProductIntroductionVO();
        BeanUtil.copyProperties(entity, vo);

        // 转换产品特点
        vo.setProductFeatures(convertFeaturesToVO(entity.getProductFeatures()));

        // 获取产品名称
        vo.setProductName(getProductNameSafely(entity.getProductId()));

        return vo;
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<InsProductIntroduction> buildQueryWrapper(InsProductIntroductionQueryDTO queryDTO) {
        LambdaQueryWrapper<InsProductIntroduction> queryWrapper = new LambdaQueryWrapper<>();

        // 产品ID精确查询
        if (queryDTO.getProductId() != null) {
            queryWrapper.eq(InsProductIntroduction::getProductId, queryDTO.getProductId());
        }

        // 启用状态查询
        if (queryDTO.getStatus() != null) {
            queryWrapper.eq(InsProductIntroduction::getStatus, queryDTO.getStatus());
        }

        // 关键词搜索（产品摘要）
        if (StrUtil.isNotBlank(queryDTO.getKeyword())) {
            queryWrapper.like(InsProductIntroduction::getProductSummary, queryDTO.getKeyword());
        }

        return queryWrapper;
    }

    // ==================== 公共业务方法 ====================

    /**
     * 分页查询产品介绍列表
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    public IPage<InsProductIntroductionVO> getProductIntroductionPage(InsProductIntroductionQueryDTO queryDTO) {
        Page<InsProductIntroduction> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        LambdaQueryWrapper<InsProductIntroduction> queryWrapper = buildQueryWrapper(queryDTO);

        IPage<InsProductIntroduction> pageResult = insProductIntroductionMapper.selectPage(page, queryWrapper);

        // 转换为VO列表
        List<InsProductIntroductionVO> voList = pageResult.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        // 构建返回的分页对象
        Page<InsProductIntroductionVO> resultPage = new Page<>(pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal());
        resultPage.setRecords(voList);

        return resultPage;
    }

    /**
     * 根据ID查询产品介绍详情
     *
     * @param id 产品介绍ID
     * @return 产品介绍详情
     */
    public InsProductIntroductionVO getProductIntroductionById(Long id) {
        validateIntroductionId(id);
        InsProductIntroduction entity = validateAndGetIntroduction(id);
        return convertToVO(entity);
    }

    /**
     * 根据产品ID查询产品介绍
     *
     * @param productId 产品ID
     * @return 产品介绍信息，如果不存在返回null
     */
    public InsProductIntroductionVO getProductIntroductionByProductId(Long productId) {
        validateProductId(productId);

        InsProductIntroduction entity = getIntroductionEntityByProductId(productId);
        if (entity == null) {
            return null;
        }

        return convertToVO(entity);
    }

    /**
     * 创建产品介绍
     *
     * @param createDTO 创建参数
     * @return 创建的产品介绍
     */
    public InsProductIntroductionVO createProductIntroduction(InsProductIntroductionCreateDTO createDTO) {
        // 参数校验
        validateProductId(createDTO.getProductId());

        // 检查产品是否存在
        validateAndGetProduct(createDTO.getProductId());

        // 检查是否已存在该产品的介绍
        checkIntroductionNotExists(createDTO.getProductId());

        // 创建实体
        InsProductIntroduction entity = buildEntityFromCreateDTO(createDTO);

        // 保存到数据库
        insProductIntroductionMapper.insert(entity);

        log.info("成功创建产品介绍，产品ID: {}, 介绍ID: {}", createDTO.getProductId(), entity.getId());

        // 返回创建的数据
        return getProductIntroductionById(entity.getId());
    }

    /**
     * 从创建DTO构建实体对象
     */
    private InsProductIntroduction buildEntityFromCreateDTO(InsProductIntroductionCreateDTO createDTO) {
        InsProductIntroduction entity = new InsProductIntroduction();
        BeanUtil.copyProperties(createDTO, entity);

        // 转换产品特点
        entity.setProductFeatures(convertFeaturesToEntity(createDTO.getProductFeatures()));

        // 设置创建时间和更新时间
        long currentTime = System.currentTimeMillis();
        entity.setCreatedAt(currentTime);
        entity.setUpdatedAt(currentTime);

        return entity;
    }

    /**
     * 更新产品介绍
     *
     * @param updateDTO 更新参数
     * @return 更新后的产品介绍
     */
    public InsProductIntroductionVO updateProductIntroduction(InsProductIntroductionUpdateDTO updateDTO) {
        // 参数校验
        validateIntroductionId(updateDTO.getId());

        // 检查记录是否存在
        InsProductIntroduction existing = validateAndGetIntroduction(updateDTO.getId());

        // 更新实体
        updateEntityFromUpdateDTO(updateDTO, existing);

        // 保存到数据库
        insProductIntroductionMapper.updateById(existing);

        log.info("成功更新产品介绍，介绍ID: {}", updateDTO.getId());

        // 返回更新后的数据
        return getProductIntroductionById(existing.getId());
    }

    /**
     * 从更新DTO更新实体对象
     */
    private void updateEntityFromUpdateDTO(InsProductIntroductionUpdateDTO updateDTO, InsProductIntroduction existing) {
        // 复制非空字段
        BeanUtil.copyProperties(updateDTO, existing, "id", "productId", "createdAt");

        // 转换产品特点
        if (updateDTO.getProductFeatures() != null) {
            existing.setProductFeatures(convertFeaturesToEntity(updateDTO.getProductFeatures()));
        }

        // 更新时间
        existing.setUpdatedAt(System.currentTimeMillis());
    }

    /**
     * 删除产品介绍
     *
     * @param id 产品介绍ID
     */
    public void deleteProductIntroduction(Long id) {
        validateIntroductionId(id);
        validateAndGetIntroduction(id); // 确保记录存在

        insProductIntroductionMapper.deleteById(id);

        log.info("成功删除产品介绍，介绍ID: {}", id);
    }

    /**
     * 根据产品ID删除产品介绍
     *
     * @param productId 产品ID
     */
    public void deleteProductIntroductionByProductId(Long productId) {
        validateProductId(productId);

        LambdaQueryWrapper<InsProductIntroduction> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InsProductIntroduction::getProductId, productId);

        int deletedCount = insProductIntroductionMapper.delete(queryWrapper);

        log.info("成功删除产品介绍，产品ID: {}, 删除数量: {}", productId, deletedCount);
    }
}
