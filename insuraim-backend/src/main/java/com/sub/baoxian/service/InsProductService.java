package com.sub.baoxian.service;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.common.exception.BizException;
import com.sub.baoxian.mapper.InsProductFavoritesMapper;
import com.sub.baoxian.mapper.InsProductIntroductionMapper;
import com.sub.baoxian.mapper.InsProductMapper;
import com.sub.baoxian.mapper.InsProductSubMapper;
import com.sub.baoxian.mapper.ProposalProductConfigMapper;
import com.sub.baoxian.model.dto.InsProductCreateDTO;
import com.sub.baoxian.model.dto.InsProductQueryDTO;
import com.sub.baoxian.model.dto.InsProductUpdateDTO;
import com.sub.baoxian.model.entity.InsProduct;
import com.sub.baoxian.model.entity.InsProductFavorites;
import com.sub.baoxian.model.entity.InsProductIntroduction;
import com.sub.baoxian.model.entity.InsProductSub;
import com.sub.baoxian.model.entity.ProposalProductConfig;
import com.sub.baoxian.model.vo.InsProductIntroductionVO;
import com.sub.baoxian.model.vo.InsProductSubVO;
import com.sub.baoxian.model.vo.InsProductVO;
import com.sub.baoxian.model.vo.ProductDetailStructuredVO;
import com.sub.baoxian.util.StpKit;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * ins_product产品服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InsProductService {

    private final InsProductMapper insProductMapper;
    private final InsProductSubMapper insProductSubMapper;
    private final InsProductDetailService insProductDetailService;
    private final ProposalProductConfigMapper proposalProductConfigMapper;
    private final InsProductFavoritesMapper insProductFavoritesMapper;
    private final InsProductIntroductionMapper insProductIntroductionMapper;

    /**
     * 分页查询产品列表
     * 
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    public IPage<InsProduct> getProductPage(InsProductQueryDTO queryDTO) {
        Page<InsProduct> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());

        LambdaQueryWrapper<InsProduct> queryWrapper = new LambdaQueryWrapper<>();

        // 产品名称模糊查询
        if (StrUtil.isNotBlank(queryDTO.getProductName())) {
            queryWrapper.like(InsProduct::getProductName, queryDTO.getProductName());
        }

        // 保险公司代码精确查询
        if (StrUtil.isNotBlank(queryDTO.getCompanyCode())) {
            queryWrapper.eq(InsProduct::getCompanyCode, queryDTO.getCompanyCode());
        }

        // 保险公司名称模糊查询
        if (StrUtil.isNotBlank(queryDTO.getCompanyName())) {
            queryWrapper.like(InsProduct::getCompanyName, queryDTO.getCompanyName());
        }

        // 产品类别代码精确查询
        if (StrUtil.isNotBlank(queryDTO.getCategoryCode())) {
            queryWrapper.eq(InsProduct::getCategoryCode, queryDTO.getCategoryCode());
        }

        // 产品类别名称精确查询
        if (StrUtil.isNotBlank(queryDTO.getCategoryName())) {
            queryWrapper.eq(InsProduct::getCategoryName, queryDTO.getCategoryName());
        }

        // 销售地区精确查询
        if (StrUtil.isNotBlank(queryDTO.getRegion())) {
            queryWrapper.eq(InsProduct::getRegion, queryDTO.getRegion());
        }

        // 产品类型模糊查询
        if (StrUtil.isNotBlank(queryDTO.getProductType())) {
            queryWrapper.like(InsProduct::getProductType, queryDTO.getProductType());
        }

        // 保障年限模糊查询
        if (StrUtil.isNotBlank(queryDTO.getGuaranteePeriod())) {
            queryWrapper.like(InsProduct::getGuaranteePeriod, queryDTO.getGuaranteePeriod());
        }

        // 启用状态查询
        if (queryDTO.getStatus() != null) {
            queryWrapper.eq(InsProduct::getStatus, queryDTO.getStatus());
        }
        // 是否有现金价值查询
        if (queryDTO.getIsCashValue() != null) {
            queryWrapper.eq(InsProduct::getHasCashValue, queryDTO.getIsCashValue());
        }
        // 是否允许预缴查询
        if (queryDTO.getIsPrepayment() != null) {
            queryWrapper.eq(InsProduct::getIsPrepayment, queryDTO.getIsPrepayment());
        }

        // 关键词搜索（产品名称或公司名称）
        if (StrUtil.isNotBlank(queryDTO.getKeyword())) {
            queryWrapper.and(wrapper -> wrapper.like(InsProduct::getProductName, queryDTO.getKeyword()).or()
                .like(InsProduct::getCompanyName, queryDTO.getKeyword()));
        }

        // 按ID升序排列
        queryWrapper.orderByAsc(InsProduct::getId);

        return insProductMapper.selectPage(page, queryWrapper);
    }

    /**
     * 获取产品列表
     *
     * @param categoryId 分类ID
     * @return 产品列表
     */
    public List<InsProductVO> getProductList(Long categoryId) {
        LambdaQueryWrapper<InsProduct> queryWrapper = new LambdaQueryWrapper<>();
        if (categoryId != null) {
            queryWrapper.eq(InsProduct::getCategoryId, categoryId);
        }
        List<InsProduct> products = insProductMapper.selectList(queryWrapper);

        return products.stream()
            .map(product -> getProductDetailById(product.getId()))
            .filter(productVO -> productVO != null) 
            .collect(Collectors.toList());
    }

    /**
     * 获取产品列表
     *
     * @param categoryId 分类ID
     * @return 产品列表
     */
    public List<InsProduct> getProductListBasic(Long categoryId) {
        LambdaQueryWrapper<InsProduct> queryWrapper = new LambdaQueryWrapper<>();
        if (categoryId != null) {
            queryWrapper.eq(InsProduct::getCategoryId, categoryId);
        }
        List<InsProduct> products = insProductMapper.selectList(queryWrapper);

        return products;
    }

    /**
     * 根据ID查询产品详情
     *
     * @param id 产品ID
     * @return 产品详情
     */
    public InsProductVO getProductDetailById(Long id) {
        return getProductDetailById(id, null);
    }

    public InsProductVO getProductDetailById(Long id, Integer subProductHasCashValue) {
        // 参数校验
        if (id == null) {
            throw new BizException("产品ID不能为空");
        }

        // 查询基础产品信息
        InsProduct product = insProductMapper.selectById(id);
        if (product == null) {
            log.warn("产品不存在，ID: {}", id);
            return null;
        }

        // 转换为InsProductVO
        InsProductVO productVO = new InsProductVO();
        BeanUtil.copyProperties(product, productVO);

        // 获取产品详情结构
        if (product.getCategoryId() != null) {
            try {
                List<ProductDetailStructuredVO> productDetails = insProductDetailService
                    .getProductDetailStructured(product.getCategoryId().intValue(), product.getId().intValue());
                productVO.setProductDetails(productDetails);
            } catch (Exception e) {
                log.warn("获取产品详情失败，产品ID: {}, 分类ID: {}", product.getId(), product.getCategoryId(), e);
                productVO.setProductDetails(List.of());
            }
        } else {
            productVO.setProductDetails(List.of());
        }

        productVO.setProductIntroduction(getProductIntroductionPage(id));
        productVO.setSubProducts(getProductSubListById(id, subProductHasCashValue));

        log.info("成功查询产品详情，产品ID: {}, 产品名称: {}", id, product.getProductName());
        return productVO;
    }

    public List<InsProductSubVO> getProductSubListById(Long productId) {
        return getProductSubListById(productId, null);
    }

    public List<InsProductSubVO> getProductSubListById(Long productId, Integer hasCashValue) {
        try {
            LambdaQueryWrapper<InsProductSub> subQueryWrapper = new LambdaQueryWrapper<>();
            subQueryWrapper.eq(InsProductSub::getParentProductId, productId.intValue());
            
            // 添加是否有现金价值的筛选条件
            if (hasCashValue != null) {
                subQueryWrapper.eq(InsProductSub::getHasCashValue, hasCashValue);
            }
            
            subQueryWrapper.orderByDesc(InsProductSub::getCreatedAt);
            List<InsProductSub> subProducts = insProductSubMapper.selectList(subQueryWrapper);

            List<InsProductSubVO> subProductVOList = subProducts.stream().map(subProduct -> {
                InsProductSubVO subProductVO = new InsProductSubVO();
                BeanUtil.copyProperties(subProduct, subProductVO);
                return subProductVO;
            }).collect(Collectors.toList());

            return subProductVOList;
        } catch (Exception e) {
            log.warn("获取子产品列表失败，产品ID: {}", productId, e);
            return List.of();
        }
    }


    

    /**
     * 分页查询产品列表（包含产品详情结构）
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    public IPage<InsProductVO> getProductPageWithDetails(InsProductQueryDTO queryDTO) {
        // 先查询基础产品信息
        IPage<InsProduct> productPage = getProductPage(queryDTO);

        // 转换为InsProductVO并填充产品详情和子产品
        List<InsProductVO> productVOList = productPage.getRecords().stream().map(product -> {
            InsProductVO productVO = new InsProductVO();
            BeanUtil.copyProperties(product, productVO);

            // 获取产品详情结构
            if (product.getCategoryId() != null) {
                try {
                    List<ProductDetailStructuredVO> productDetails = insProductDetailService
                        .getProductDetailStructured(product.getCategoryId().intValue(), product.getId().intValue());
                    productVO.setProductDetails(productDetails);
                } catch (Exception e) {
                    log.warn("获取产品详情失败，产品ID: {}, 分类ID: {}", product.getId(), product.getCategoryId(), e);
                    productVO.setProductDetails(List.of());
                }
            } else {
                productVO.setProductDetails(List.of());
            }

            // 获取子产品列表
            try {
                LambdaQueryWrapper<InsProductSub> subQueryWrapper = new LambdaQueryWrapper<>();
                subQueryWrapper.eq(InsProductSub::getParentProductId, product.getId().intValue());
                
                // 添加子产品是否有现金价值的筛选条件
                if (queryDTO.getSubProductHasCashValue() != null) {
                    subQueryWrapper.eq(InsProductSub::getHasCashValue, queryDTO.getSubProductHasCashValue());
                }
                
                List<InsProductSub> subProducts = insProductSubMapper.selectList(subQueryWrapper);

                List<InsProductSubVO> subProductVOList = subProducts.stream().map(subProduct -> {
                    InsProductSubVO subProductVO = new InsProductSubVO();
                    BeanUtil.copyProperties(subProduct, subProductVO);
                    return subProductVO;
                }).collect(Collectors.toList());

                productVO.setSubProducts(subProductVOList);
            } catch (Exception e) {
                log.warn("获取子产品失败，产品ID: {}", product.getId(), e);
                productVO.setSubProducts(List.of());
            }

            return productVO;
        }).collect(Collectors.toList());

        // 构建返回的分页对象
        Page<InsProductVO> resultPage =
            new Page<>(productPage.getCurrent(), productPage.getSize(), productPage.getTotal());
        resultPage.setRecords(productVOList);

        return resultPage;
    }

    /**
     * 创建产品
     *
     * @param createDTO 创建参数
     * @return 创建的产品
     */
    public InsProduct createProduct(InsProductCreateDTO createDTO) {
        InsProduct product = new InsProduct();
        BeanUtil.copyProperties(createDTO, product);

        // 设置创建时间和更新时间
        long currentTime = System.currentTimeMillis();
        product.setCreatedAt(currentTime);
        product.setUpdatedAt(currentTime);
        insProductMapper.insert(product);
        return product;
    }

    /**
     * 更新产品
     *
     * @param updateDTO 更新参数
     * @return 更新后的产品
     */
    public InsProduct updateProduct(InsProductUpdateDTO updateDTO) {
        InsProduct existingProduct = insProductMapper.selectById(updateDTO.getId());
        if (existingProduct == null) {
            throw new RuntimeException("产品不存在，ID: " + updateDTO.getId());
        }

        // 复制非空字段
        BeanUtil.copyProperties(updateDTO, existingProduct, "id", "createdAt");

        // 更新时间
        existingProduct.setUpdatedAt(System.currentTimeMillis());

        insProductMapper.updateById(existingProduct);
        return existingProduct;
    }

    /**
     * 删除产品
     *
     * @param id 产品ID
     */
    public void deleteProduct(Long id) {
        InsProduct existingProduct = insProductMapper.selectById(id);
        if (existingProduct == null) {
            throw new RuntimeException("产品不存在，ID: " + id);
        }

        // 检查是否有子产品
        LambdaQueryWrapper<InsProductSub> subQueryWrapper = new LambdaQueryWrapper<>();
        subQueryWrapper.eq(InsProductSub::getParentProductId, id.intValue());
        Long subProductCount = insProductSubMapper.selectCount(subQueryWrapper);

        if (subProductCount > 0) {
            throw new RuntimeException("该产品下还有子产品，无法删除");
        }

        insProductMapper.deleteById(id);
    }

    /**
     * 获取可收益演算的产品列表
     * 
     * @return 可收益演算的产品列表
     */
    public List<ProposalProductConfig> getBenefitCalculateList() {
        return proposalProductConfigMapper
            .selectList(new LambdaQueryWrapper<ProposalProductConfig>().eq(ProposalProductConfig::getStatus, 1)
                .eq(ProposalProductConfig::getProductType, "SAVINGS").isNotNull(ProposalProductConfig::getBaseData));
    }

    /**
     * 获取可生成计划书产品配置列表
     * 
     * @return 可生成计划书产品配置列表
     */
    public List<ProposalProductConfig> getPlanBookList() {
        return proposalProductConfigMapper
            .selectList(new LambdaQueryWrapper<ProposalProductConfig>().eq(ProposalProductConfig::getStatus, 1));
    }

    /**
     * 用户收藏产品
     *
     * @param productId 产品ID
     * @return 操作结果
     */
    public boolean favoriteProduct(Long productId) {
        // 参数校验
        if (productId == null) {
            throw new BizException("产品ID不能为空");
        }

        // 获取当前登录用户ID
        Long userId = StpKit.USER.getLoginIdAsLong();
        // 检查产品是否存在
        InsProduct product = insProductMapper.selectById(productId);
        if (product == null) {
            throw new BizException("产品不存在");
        }

        // 检查是否已经收藏
        LambdaQueryWrapper<InsProductFavorites> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InsProductFavorites::getUserId, userId).eq(InsProductFavorites::getProductId, productId);
        InsProductFavorites existingFavorite = insProductFavoritesMapper.selectOne(queryWrapper);

        if (existingFavorite != null) {
            throw new BizException("您已经收藏过该产品");
        }

        // 创建收藏记录
        InsProductFavorites favorite = new InsProductFavorites();
        favorite.setUserId(userId);
        favorite.setProductId(productId);
        favorite.setCreatedAt(System.currentTimeMillis());
        favorite.setUpdatedAt(System.currentTimeMillis());

        int result = insProductFavoritesMapper.insert(favorite);

        log.info("用户{}收藏产品{}，结果: {}", userId, productId, result > 0 ? "成功" : "失败");
        return result > 0;
    }

    /**
     * 用户取消收藏产品
     *
     * @param productId 产品ID
     * @return 操作结果
     */
    public boolean unfavoriteProduct(Long productId) {
        // 参数校验
        if (productId == null) {
            throw new BizException("产品ID不能为空");
        }

        // 获取当前登录用户ID
        Long userId = StpKit.USER.getLoginIdAsLong();

        // 查找收藏记录
        LambdaQueryWrapper<InsProductFavorites> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InsProductFavorites::getUserId, userId).eq(InsProductFavorites::getProductId, productId);
        InsProductFavorites favorite = insProductFavoritesMapper.selectOne(queryWrapper);

        if (favorite == null) {
            throw new BizException("您还未收藏该产品");
        }

        // 删除收藏记录
        int result = insProductFavoritesMapper.deleteById(favorite.getId());

        log.info("用户{}取消收藏产品{}，结果: {}", userId, productId, result > 0 ? "成功" : "失败");
        return result > 0;
    }

    /**
     * 获取用户收藏的产品列表
     *
     * @return 收藏的产品列表
     */
    public List<InsProductVO> getUserFavoriteProducts() {
        // 获取当前登录用户ID
        Long userId = StpKit.USER.getLoginIdAsLong();

        // 查询用户收藏记录
        LambdaQueryWrapper<InsProductFavorites> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InsProductFavorites::getUserId, userId).orderByDesc(InsProductFavorites::getCreatedAt);
        List<InsProductFavorites> favorites = insProductFavoritesMapper.selectList(queryWrapper);

        if (favorites.isEmpty()) {
            return List.of();
        }

        // 获取产品ID列表
        List<Long> productIds = favorites.stream().map(InsProductFavorites::getProductId).collect(Collectors.toList());

        // 查询产品详情
        LambdaQueryWrapper<InsProduct> productQueryWrapper = new LambdaQueryWrapper<>();
        productQueryWrapper.in(InsProduct::getId, productIds);
        List<InsProduct> products = insProductMapper.selectList(productQueryWrapper);

        // 转换为VO并保持收藏时间的排序
        return favorites.stream().map(favorite -> {
            InsProduct product =
                products.stream().filter(p -> p.getId().equals(favorite.getProductId())).findFirst().orElse(null);

            if (product == null) {
                return null;
            }

            InsProductVO productVO = new InsProductVO();
            BeanUtil.copyProperties(product, productVO);
            productVO.setProductIntroduction(getProductIntroductionPage(product.getId()));
            return productVO;
        }).filter(productVO -> productVO != null).collect(Collectors.toList());
    }

    /**
     * 检查用户是否收藏了指定产品
     *
     * @param productId 产品ID
     * @return 是否已收藏
     */
    public boolean isProductFavorited(Long productId) {
        // 参数校验
        if (productId == null) {
            return false;
        }

        // 获取当前登录用户ID
        Long userId = StpKit.USER.getLoginIdAsLong();

        // 查询收藏记录
        LambdaQueryWrapper<InsProductFavorites> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InsProductFavorites::getUserId, userId).eq(InsProductFavorites::getProductId, productId);
        Long count = insProductFavoritesMapper.selectCount(queryWrapper);

        return count > 0;
    }

    /**
     * 获取产品介绍页信息
     *
     * @param productId 产品ID
     * @return 产品介绍页信息
     */
    public InsProductIntroductionVO getProductIntroductionPage(Long productId) {
        // 参数校验
        if (productId == null) {
            throw new BizException("产品ID不能为空");
        }

        // 查询基础产品信息
        InsProduct product = insProductMapper.selectById(productId);
        if (product == null) {
            throw new BizException("产品不存在");
        }

        // 构建产品介绍页VO
        InsProductIntroductionVO introPageVO = new InsProductIntroductionVO();

        // 设置基础信息
        introPageVO.setProductId(productId);
        introPageVO.setProductName(product.getProductName());

        // 从新的产品介绍表获取信息
        try {
            LambdaQueryWrapper<InsProductIntroduction> introQueryWrapper = new LambdaQueryWrapper<>();
            introQueryWrapper.eq(InsProductIntroduction::getProductId, productId);
            InsProductIntroduction productIntroduction = insProductIntroductionMapper.selectOne(introQueryWrapper);
            BeanUtil.copyProperties(productIntroduction, introPageVO);

            if (productIntroduction != null) {
                // 设置产品摘要
                if (StrUtil.isNotBlank(productIntroduction.getProductSummary())) {
                    introPageVO.setProductSummary(productIntroduction.getProductSummary());
                } else {
                    introPageVO.setProductSummary(product.getDescription()); // 使用产品描述作为默认摘要
                }

                // 设置banner图
                if (StrUtil.isNotBlank(productIntroduction.getBannerImageUrl())) {
                    introPageVO.setBannerImageUrl(productIntroduction.getBannerImageUrl());
                } else {
                    introPageVO.setBannerImageUrl(null);
                }

                // 设置产品特点
                if (productIntroduction.getProductFeatures() != null) {
                    List<InsProductIntroductionVO.ProductFeatureItemVO> features = productIntroduction.getProductFeatures().stream()
                        .map(feature -> {
                            InsProductIntroductionVO.ProductFeatureItemVO featureItem = new InsProductIntroductionVO.ProductFeatureItemVO();
                            BeanUtil.copyProperties(feature, featureItem);
                            return featureItem;
                        }).collect(Collectors.toList());
                    introPageVO.setProductFeatures(features);
                } else {
                    introPageVO.setProductFeatures(List.of());
                }

                // 设置产品概况
                if (StrUtil.isNotBlank(productIntroduction.getProductOverview())) {
                    introPageVO.setProductOverview(productIntroduction.getProductOverview());
                }
            } else {
                // 如果没有专门的产品介绍数据，设置默认值
                introPageVO.setProductSummary(product.getDescription());
                introPageVO.setBannerImageUrl(product.getLogoUrl());
                introPageVO.setProductFeatures(List.of());
            }
        } catch (Exception e) {
            log.warn("获取产品介绍信息失败，产品ID: {}", productId, e);
            introPageVO.setProductSummary(product.getDescription());
            introPageVO.setBannerImageUrl(product.getLogoUrl());
            introPageVO.setProductFeatures(List.of());
        }

        // 如果没有设置产品概况，尝试从产品详情中获取
        if (StrUtil.isBlank(introPageVO.getProductOverview()) && product.getCategoryId() != null) {
            try {
                List<ProductDetailStructuredVO> productDetails = insProductDetailService
                    .getProductDetailStructured(product.getCategoryId().intValue(), productId.intValue());

                // 将结构化的产品详情转换为HTML格式
                String htmlOverview = convertProductDetailsToHtml(productDetails);
                introPageVO.setProductOverview(htmlOverview);
            } catch (Exception e) {
                log.warn("获取产品详情失败，产品ID: {}", productId, e);
                introPageVO.setProductOverview("");
            }
        }

        log.info("成功获取产品介绍页信息，产品ID: {}, 产品名称: {}", productId, product.getProductName());
        return introPageVO;
    }

    /**
     * 将结构化的产品详情转换为HTML格式
     *
     * @param productDetails 产品详情结构列表
     * @return HTML格式的产品概况
     */
    private String convertProductDetailsToHtml(List<ProductDetailStructuredVO> productDetails) {
        if (productDetails == null || productDetails.isEmpty()) {
            return "";
        }

        StringBuilder htmlBuilder = new StringBuilder();
        htmlBuilder.append("<div class='product-overview'>");

        for (ProductDetailStructuredVO detail : productDetails) {
            htmlBuilder.append("<div class='detail-section'>");
            htmlBuilder.append("<h3>").append(detail.getName()).append("</h3>");

            if (detail.getAttributes() != null && !detail.getAttributes().isEmpty()) {
                htmlBuilder.append("<ul>");
                for (ProductDetailStructuredVO.AttributeInfo attr : detail.getAttributes()) {
                    htmlBuilder.append("<li>");
                    htmlBuilder.append("<strong>").append(attr.getName()).append(":</strong> ");
                    htmlBuilder.append(attr.getValue());
                    htmlBuilder.append("</li>");
                }
                htmlBuilder.append("</ul>");
            }

            htmlBuilder.append("</div>");
        }

        htmlBuilder.append("</div>");
        return htmlBuilder.toString();
    }

}
