package com.sub.baoxian.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.mapper.InsProductSubMapper;
import com.sub.baoxian.model.dto.InsProductSubCreateDTO;
import com.sub.baoxian.model.dto.InsProductSubQueryDTO;
import com.sub.baoxian.model.dto.InsProductSubUpdateDTO;
import com.sub.baoxian.model.entity.InsProductSub;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import org.springframework.stereotype.Service;

/**
 * 子产品服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InsProductSubService {

    private final InsProductSubMapper insProductSubMapper;

    /**
     * 分页查询子产品列表
     * 
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    public IPage<InsProductSub> getSubProductPage(InsProductSubQueryDTO queryDTO) {
        Page<InsProductSub> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        LambdaQueryWrapper<InsProductSub> queryWrapper = new LambdaQueryWrapper<>();

        // 父产品ID
        if (queryDTO.getParentProductId() != null) {
            queryWrapper.eq(InsProductSub::getParentProductId, queryDTO.getParentProductId());
        }

        // 产品名称模糊查询
        if (StrUtil.isNotBlank(queryDTO.getProductName())) {
            queryWrapper.like(InsProductSub::getProductName, queryDTO.getProductName());
        }

        // 分类ID
        if (queryDTO.getCategoryId() != null) {
            queryWrapper.eq(InsProductSub::getCategoryId, queryDTO.getCategoryId());
        }

        // 缴费期限
        if (StrUtil.isNotBlank(queryDTO.getPaymentTerm())) {
            queryWrapper.eq(InsProductSub::getPaymentTerm, queryDTO.getPaymentTerm());
        }

        // 币种
        if (StrUtil.isNotBlank(queryDTO.getCurrency())) {
            queryWrapper.eq(InsProductSub::getCurrency, queryDTO.getCurrency());
        }

        // 是否有现金价值
        if (queryDTO.getHasCashValue() != null) {
            queryWrapper.eq(InsProductSub::getHasCashValue, queryDTO.getHasCashValue());
        }

        // 状态
        if (queryDTO.getStatus() != null) {
            queryWrapper.eq(InsProductSub::getStatus, queryDTO.getStatus());
        }

        // 关键词搜索
        if (StrUtil.isNotBlank(queryDTO.getKeyword())) {
            queryWrapper.like(InsProductSub::getProductName, queryDTO.getKeyword());
        }

        // 按创建时间倒序
        queryWrapper.orderByDesc(InsProductSub::getCreatedAt);

        return insProductSubMapper.selectPage(page, queryWrapper);
    }

    /**
     * 根据父产品ID获取子产品列表
     * 
     * @param parentProductId 父产品ID
     * @return 子产品列表
     */
    public List<InsProductSub> getSubProductsByParentId(Integer parentProductId) {
        LambdaQueryWrapper<InsProductSub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InsProductSub::getParentProductId, parentProductId);
        queryWrapper.orderByDesc(InsProductSub::getCreatedAt);
        return insProductSubMapper.selectList(queryWrapper);
    }

    /**
     * 根据ID查询子产品详情
     * 
     * @param id 子产品ID
     * @return 子产品详情
     */
    public InsProductSub getSubProductById(Long id) {
        return insProductSubMapper.selectById(id);
    }

    /**
     * 创建子产品
     * 
     * @param createDTO 创建参数
     * @return 创建的子产品
     */
    public InsProductSub createSubProduct(InsProductSubCreateDTO createDTO) {
        InsProductSub subProduct = new InsProductSub();
        BeanUtil.copyProperties(createDTO, subProduct);

        // 设置创建时间和更新时间
        long currentTime = System.currentTimeMillis() / 1000;
        subProduct.setCreatedAt(currentTime);
        subProduct.setUpdatedAt(currentTime);

        insProductSubMapper.insert(subProduct);
        return subProduct;
    }

    /**
     * 更新子产品
     * 
     * @param updateDTO 更新参数
     * @return 更新后的子产品
     */
    public InsProductSub updateSubProduct(InsProductSubUpdateDTO updateDTO) {
        InsProductSub existingSubProduct = insProductSubMapper.selectById(updateDTO.getId());
        if (existingSubProduct == null) {
            throw new RuntimeException("子产品不存在，ID: " + updateDTO.getId());
        }

        // 复制非空字段
        BeanUtil.copyProperties(updateDTO, existingSubProduct, "id", "createdAt");

        // 更新时间
        existingSubProduct.setUpdatedAt(System.currentTimeMillis() / 1000);

        insProductSubMapper.updateById(existingSubProduct);
        return existingSubProduct;
    }

    /**
     * 删除子产品
     * 
     * @param id 子产品ID
     */
    public void deleteSubProduct(Long id) {
        InsProductSub existingSubProduct = insProductSubMapper.selectById(id);
        if (existingSubProduct == null) {
            throw new RuntimeException("子产品不存在，ID: " + id);
        }
        insProductSubMapper.deleteById(id);
    }

    /**
     * 根据父产品ID删除所有子产品
     * 
     * @param parentProductId 父产品ID
     */
    public void deleteSubProductsByParentId(Integer parentProductId) {
        LambdaQueryWrapper<InsProductSub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InsProductSub::getParentProductId, parentProductId);
        insProductSubMapper.delete(queryWrapper);
    }
}
