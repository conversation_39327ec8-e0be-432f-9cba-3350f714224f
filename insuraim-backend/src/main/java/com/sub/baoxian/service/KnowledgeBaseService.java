package com.sub.baoxian.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.sub.baoxian.mapper.KnowledgeBaseMapper;
import com.sub.baoxian.model.bo.getKnowledgeBaseByIdBO;
import com.sub.baoxian.model.entity.KnowledgeBase;
import com.sub.baoxian.model.entity.KnowledgeBaseCategory;
import com.sub.baoxian.model.entity.KnowledgeBaseCompany;
import com.sub.baoxian.model.vo.CategoryTreeVO;
import com.sub.baoxian.model.vo.getKnowledgeBaseByIdVO;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class KnowledgeBaseService {

    private final KnowledgeBaseMapper knowledgeBaseMapper;

    /**
     * 根据分类ID获取知识库信息列表
     * 
     * @param categoryId
     * @return
     */
    public List<KnowledgeBase> getLoreListByCategoryId(Long categoryId) {
        return knowledgeBaseMapper.getLoreListByCategoryId(categoryId);
    }

    /**
     * 获取知识库详情
     * 
     * @param id
     * @return
     */
    public getKnowledgeBaseByIdVO getKnowledgeBaseById(Long id) {
        // 更新浏览量
        knowledgeBaseMapper.updateKnowledgeBaseViewCount(id);

        getKnowledgeBaseByIdBO knowledgeBase = knowledgeBaseMapper.getKnowledgeBaseById(id);
        return getKnowledgeBaseByIdVO.builder().kbId(knowledgeBase.getKnowledgeBase().getId())
            .title(knowledgeBase.getKnowledgeBase().getTitle()).content(knowledgeBase.getKnowledgeBase().getContent())
            .category(knowledgeBase.getKnowledgeBase().getCategory())
            .status(knowledgeBase.getKnowledgeBase().getStatus())
            .createdAt(knowledgeBase.getKnowledgeBase().getCreatedAt())
            .updatedAt(knowledgeBase.getKnowledgeBase().getUpdatedAt())
            .authorId(knowledgeBase.getKnowledgeBase().getAuthorId())
            .summary(knowledgeBase.getKnowledgeBase().getSummary()).views(knowledgeBase.getKnowledgeBase().getViews())
            .tags(knowledgeBase.getKnowledgeBase().getTags())
            .categoryId(knowledgeBase.getKnowledgeBase().getCategoryId())
            .type(knowledgeBase.getKnowledgeBase().getType()).attachments(knowledgeBase.getKnowledgeAttachments())
            .build();
    }

    /**
     * 根据保险公司ID获取知识库专题列表
     * 
     * @param companyId
     * @return
     */
    public Map<String, Object> getCategoryListByCompanyId(Long companyId) {
        List<KnowledgeBaseCategory> categoryList = knowledgeBaseMapper.getCategoryListByCompanyId(companyId);
        KnowledgeBaseCompany company = knowledgeBaseMapper.getCompanyInfo(companyId);
        Map<String, Object> result = new HashMap<>();
        result.put("companyName", company.getCompanyName());
        result.put("categoryList", categoryList);
        return result;
    }

    /**
     * 获取保险公司列表
     * 
     * @return
     */
    public List<KnowledgeBaseCompany> getCompanyList() {
        return knowledgeBaseMapper.getCompanyList();
    }

    /**
     * 获取完整分类树
     * 
     * @return 分类树列表
     */
    public List<CategoryTreeVO> getCategoryTree() {
        // 获取所有分类
        List<KnowledgeBaseCategory> allCategories = knowledgeBaseMapper.getAllCategories();

        // 获取所有顶级分类（parent_id为0或null的分类）
        List<KnowledgeBaseCategory> rootCategories =
            allCategories.stream().filter(category -> category.getParentId() == null || category.getParentId() == 0)
                .collect(Collectors.toList());

        // 构建分类树
        List<CategoryTreeVO> result = new ArrayList<>();
        for (KnowledgeBaseCategory rootCategory : rootCategories) {
            result.add(buildCategoryTree(rootCategory, allCategories));
        }

        return result;
    }

    /**
     * 根据公司ID获取分类树
     * 
     * @param companyId 公司ID
     * @return 分类树列表
     */
    public List<CategoryTreeVO> getCategoryTreeByCompanyId(Long companyId) {
        // 获取所有分类
        List<KnowledgeBaseCategory> allCategories = knowledgeBaseMapper.getAllCategories();

        // 过滤出指定公司的分类
        List<KnowledgeBaseCategory> companyCategories = allCategories.stream()
            .filter(category -> category.getCompanyId() != null && category.getCompanyId().equals(companyId))
            .collect(Collectors.toList());

        // 获取顶级分类（parent_id为0或null的分类）
        List<KnowledgeBaseCategory> rootCategories =
            companyCategories.stream().filter(category -> category.getParentId() == null || category.getParentId() == 0)
                .collect(Collectors.toList());

        // 构建分类树
        List<CategoryTreeVO> result = new ArrayList<>();
        for (KnowledgeBaseCategory rootCategory : rootCategories) {
            result.add(buildCategoryTree(rootCategory, companyCategories));
        }

        return result;
    }

    /**
     * 根据分类ID获取分类树
     * 
     * @param categoryId 分类ID
     * @return 分类树
     */
    public CategoryTreeVO getCategoryTreeById(Long categoryId) {
        // 获取所有分类
        List<KnowledgeBaseCategory> allCategories = knowledgeBaseMapper.getAllCategories();

        // 查找目标分类
        KnowledgeBaseCategory targetCategory =
            allCategories.stream().filter(category -> category.getId().equals(categoryId)).findFirst().orElse(null);

        if (targetCategory == null) {
            return null;
        }

        // 构建分类树
        return buildCategoryTree(targetCategory, allCategories);
    }

    /**
     * 递归构建分类树
     * 
     * @param currentCategory 当前分类
     * @param allCategories 所有分类列表
     * @return 构建好的分类树节点
     */
    private CategoryTreeVO buildCategoryTree(KnowledgeBaseCategory currentCategory,
        List<KnowledgeBaseCategory> allCategories) {
        CategoryTreeVO treeNode = new CategoryTreeVO();

        // 复制属性
        treeNode.setId(currentCategory.getId());
        treeNode.setName(currentCategory.getCategoryName());
        treeNode.setDescription(currentCategory.getDescription());
        treeNode.setParentId(currentCategory.getParentId());
        treeNode.setCreatedAt(currentCategory.getCreatedAt());
        treeNode.setUpdatedAt(currentCategory.getUpdatedAt());
        treeNode.setCompanyId(currentCategory.getCompanyId());

        // 查找子分类
        List<KnowledgeBaseCategory> children = allCategories.stream().filter(category -> {
            Long parentId = category.getParentId();
            return parentId != null && parentId.equals(currentCategory.getId());
        }).collect(Collectors.toList());

        // 如果有子分类，递归构建子树
        if (!children.isEmpty()) {
            List<CategoryTreeVO> childrenTree = new ArrayList<>();
            for (KnowledgeBaseCategory child : children) {
                childrenTree.add(buildCategoryTree(child, allCategories));
            }
            treeNode.setChildren(childrenTree);
        } else {
            treeNode.setChildren(new ArrayList<>());
        }

        return treeNode;
    }
}
