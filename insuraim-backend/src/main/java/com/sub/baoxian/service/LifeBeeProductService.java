package com.sub.baoxian.service;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.mapper.LifeBeeProductsMapper;
import com.sub.baoxian.model.entity.Products;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 产品服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LifeBeeProductService {

    private final LifeBeeProductsMapper productsMapper;

    /**
     * 分页查询产品列表
     * 
     * @param page 分页参数
     * @param region 地区(可选)
     * @param categoryCode 产品类别代码(可选)
     * @param search 搜索关键词(可选，用于模糊查询)
     * @return 分页结果
     */
    public IPage<Products> getProductPage(Page<Products> page, String region, String categoryCode, String search,
        String companyCode, String companyName, boolean comparable) {

        LambdaQueryWrapper<Products> queryWrapper = new LambdaQueryWrapper<>();
        // 添加查询条件
        if (StrUtil.isNotBlank(region)) {
            queryWrapper.eq(Products::getRegion, region);
        }
        if (StrUtil.isNotBlank(categoryCode)) {
            queryWrapper.eq(Products::getCategoryCode, categoryCode);
        }
        if (StrUtil.isNotBlank(companyCode)) {
            queryWrapper.eq(Products::getCompanyCode, companyCode);
        }
        if (StrUtil.isNotBlank(companyName)) {
            queryWrapper.eq(Products::getCompanyName, companyName);
        }

        // 添加可比较条件
        if (comparable) {
            queryWrapper.eq(Products::getIsComparable, 1);
        }

        // 添加搜索条件
        if (StringUtils.hasText(search)) {
            queryWrapper.and(
                wrapper -> wrapper.like(Products::getProductName, search).or().like(Products::getCompanyName, search));
        }

        queryWrapper.orderByAsc(Products::getId);

        // 执行分页查询
        IPage<Products> result = productsMapper.selectPage(page, queryWrapper);

        return result;
    }

    /**
     * 根据产品代码查询产品详情
     * 
     * @param code 产品代码
     * @return 产品详情
     */
    public Products getProductByCode(String code) {
        LambdaQueryWrapper<Products> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Products::getProductCode, code);
        return productsMapper.selectOne(queryWrapper);
    }
}