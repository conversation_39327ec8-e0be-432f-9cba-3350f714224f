package com.sub.baoxian.service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sub.baoxian.common.exception.BizException;
import com.sub.baoxian.mapper.MenuMapper;
import com.sub.baoxian.mapper.RoleMenuMapper;
import com.sub.baoxian.model.dto.MenuCreateDTO;
import com.sub.baoxian.model.dto.MenuUpdateDTO;
import com.sub.baoxian.model.entity.Menu;
import com.sub.baoxian.model.relation.RoleMenu;
import com.sub.baoxian.model.vo.MenuTreeVO;

import lombok.RequiredArgsConstructor;

/**
 * 菜单服务类
 */
@Service
@RequiredArgsConstructor
public class MenuService {

    private final MenuMapper menuMapper;
    private final RoleMenuMapper roleMenuMapper;
    private final UserService userService;

    /**
     * 获取所有菜单列表
     * @return 菜单列表
     */
    public List<Menu> list() {
        LambdaQueryWrapper<Menu> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByAsc(Menu::getOrderRank);
        return menuMapper.selectList(wrapper);
    }

    /**
     * 根据ID获取菜单
     * @param id 菜单ID
     * @return 菜单信息
     */
    public Menu getById(Long id) {
        if (id == null) {
            throw new BizException("菜单ID不能为空");
        }
        Menu menu = menuMapper.selectById(id);
        if (menu == null) {
            throw new BizException("菜单不存在");
        }
        return menu;
    }

    /**
     * 根据ID获取菜单详情（返回MenuTreeVO）
     * @param id 菜单ID
     * @return 菜单详情VO
     */
    public MenuTreeVO getMenuById(Long id) {
        Menu menu = getById(id);
        return convertToTreeVO(menu);
    }

    /**
     * 新增菜单
     * @param createDTO 菜单创建DTO
     * @return 是否成功
     */
    public Boolean save(MenuCreateDTO createDTO) {
        if (createDTO == null) {
            throw new BizException("菜单信息不能为空");
        }

        // 检查父菜单是否存在
        if (createDTO.getParentId() != null && createDTO.getParentId() > 0) {
            Menu parentMenu = menuMapper.selectById(createDTO.getParentId());
            if (parentMenu == null) {
                throw new BizException("父菜单不存在");
            }
        }

        // 验证菜单类型
        if (createDTO.getMenuType() == null || createDTO.getMenuType() < 0 || createDTO.getMenuType() > 3) {
            throw new BizException("菜单类型无效，必须为0(目录)、1(菜单)、2(按钮)、3(页面)");
        }

        Menu menu = new Menu();
        BeanUtils.copyProperties(createDTO, menu);
        menu.setCreateTime(System.currentTimeMillis());
        menu.setUpdateTime(System.currentTimeMillis());

        return menuMapper.insert(menu) > 0;
    }

    /**
     * 更新菜单
     * @param updateDTO 菜单更新DTO
     * @return 是否成功
     */
    public Boolean update(MenuUpdateDTO updateDTO) {
        if (updateDTO == null || updateDTO.getId() == null) {
            throw new BizException("菜单ID不能为空");
        }

        // 检查菜单是否存在
        Menu existMenu = menuMapper.selectById(updateDTO.getId());
        if (existMenu == null) {
            throw new BizException("菜单不存在");
        }

        // 检查父菜单是否存在
        if (updateDTO.getParentId() != null && updateDTO.getParentId() > 0) {
            // 不能将自己设为父菜单
            if (updateDTO.getParentId().equals(updateDTO.getId())) {
                throw new BizException("不能将自己设为父菜单");
            }

            // 检查是否会形成循环引用
            if (isCircularReference(updateDTO.getId(), updateDTO.getParentId())) {
                throw new BizException("不能选择自己的子菜单作为父菜单");
            }

            Menu parentMenu = menuMapper.selectById(updateDTO.getParentId());
            if (parentMenu == null) {
                throw new BizException("父菜单不存在");
            }
        }

        // 验证菜单类型
        if (updateDTO.getMenuType() != null && (updateDTO.getMenuType() < 0 || updateDTO.getMenuType() > 3)) {
            throw new BizException("菜单类型无效，必须为0(目录)、1(菜单)、2(按钮)或3(页面)");
        }

        Menu menu = new Menu();
        BeanUtils.copyProperties(updateDTO, menu);
        menu.setUpdateTime(System.currentTimeMillis());

        return menuMapper.updateById(menu) > 0;
    }

    /**
     * 删除菜单
     * @param id 菜单ID
     * @return 是否成功
     */
    public Boolean removeById(Long id) {
        if (id == null) {
            throw new BizException("菜单ID不能为空");
        }

        // 检查菜单是否存在
        Menu menu = menuMapper.selectById(id);
        if (menu == null) {
            throw new BizException("菜单不存在");
        }

        // 检查是否有子菜单
        LambdaQueryWrapper<Menu> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Menu::getParentId, id);
        List<Menu> children = menuMapper.selectList(wrapper);
        if (!children.isEmpty()) {
            throw new BizException("该菜单下存在子菜单，请先删除子菜单");
        }

        return menuMapper.deleteById(id) > 0;
    }

    /**
     * 批量删除菜单
     * @param ids 菜单ID列表
     * @return 是否成功
     */
    public Boolean removeByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new BizException("菜单ID列表不能为空");
        }

        // 检查每个菜单是否有子菜单
        for (Long id : ids) {
            LambdaQueryWrapper<Menu> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Menu::getParentId, id);
            List<Menu> children = menuMapper.selectList(wrapper);
            if (!children.isEmpty()) {
                Menu menu = menuMapper.selectById(id);
                throw new BizException("菜单【" + (menu != null ? menu.getName() : id) + "】下存在子菜单，请先删除子菜单");
            }
        }

        return menuMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 获取菜单树形结构
     * @return 菜单树列表
     */
    public List<MenuTreeVO> getMenuTree() {
        // 获取所有菜单
        List<Menu> allMenus = list();

        // 转换为TreeVO
        List<MenuTreeVO> menuTreeList = allMenus.stream()
            .map(this::convertToTreeVO)
            .collect(Collectors.toList());

        // 构建树形结构
        return buildMenuTree(menuTreeList, 0L);
    }

    /**
     * 构建菜单树形结构
     * @param menuList 菜单列表
     * @param parentId 父菜单ID
     * @return 树形菜单列表
     */
    private List<MenuTreeVO> buildMenuTree(List<MenuTreeVO> menuList, Long parentId) {
        List<MenuTreeVO> result = new ArrayList<>();

        for (MenuTreeVO menu : menuList) {
            if (parentId.equals(menu.getParentId())) {
                // 递归查找子菜单
                List<MenuTreeVO> children = buildMenuTree(menuList, menu.getId());
                menu.setChildren(children);
                result.add(menu);
            }
        }

        return result;
    }

    /**
     * 将Menu转换为MenuTreeVO
     * @param menu 菜单实体
     * @return 菜单树VO
     */
    private MenuTreeVO convertToTreeVO(Menu menu) {
        MenuTreeVO treeVO = new MenuTreeVO();
        BeanUtils.copyProperties(menu, treeVO);
        return treeVO;
    }

    /**
     * 检查是否会形成循环引用
     * @param menuId 当前菜单ID
     * @param parentId 要设置的父菜单ID
     * @return 是否会形成循环引用
     */
    private boolean isCircularReference(Long menuId, Long parentId) {
        if (parentId == null || parentId == 0) {
            return false;
        }

        // 如果父菜单ID就是当前菜单ID，直接返回true
        if (parentId.equals(menuId)) {
            return true;
        }

        // 递归检查父菜单的父菜单
        Menu parentMenu = menuMapper.selectById(parentId);
        if (parentMenu != null && parentMenu.getParentId() != null && parentMenu.getParentId() > 0) {
            return isCircularReference(menuId, parentMenu.getParentId());
        }
        return false;
    }


    /**
     * 获取当前用户菜单列表
     * @param userId 用户ID
     * @return 菜单列表
     */
    public List<MenuTreeVO> getCurrentUserMenuList(Long userId) {
        if (userId == null) {
            throw new BizException("用户ID不能为空");
        }

        // 获取用户的角色ID
        Long roleId = userService.getRoleIdByUserId(userId);

        if (roleId == null) {
            // 用户没有分配角色，返回空列表
            return new ArrayList<>();
        }

        // 获取角色的菜单列表
        List<Menu> menuList = getRoleMenus(roleId);

        if (menuList.isEmpty()) {
            // 角色没有分配菜单，返回空列表
            return new ArrayList<>();
        }

        // 转换为MenuTreeVO
        List<MenuTreeVO> menuTreeList = menuList.stream()
            .map(this::convertToTreeVO)
            .collect(Collectors.toList());

        // 构建树形结构
        return buildMenuTree(menuTreeList, 0L);
    }

    /**
     * 为角色分配菜单
     * @param roleId 角色ID
     * @param menuIds 菜单ID列表
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean assignMenus(Long roleId, List<Long> menuIds) {
        if (roleId == null) {
            throw new BizException("角色ID不能为空");
        }

        // 先删除该角色的所有菜单关联
        roleMenuMapper.deleteByRoleId(roleId);

        // 如果菜单ID列表为空，则只删除不添加
        if (menuIds == null || menuIds.isEmpty()) {
            return true;
        }

        // 验证菜单ID是否存在
        for (Long menuId : menuIds) {
            Menu menu = menuMapper.selectById(menuId);
            if (menu == null) {
                throw new BizException("菜单ID " + menuId + " 不存在");
            }
        }

        // 批量插入新的角色菜单关联
        List<RoleMenu> roleMenus = new ArrayList<>();
        for (Long menuId : menuIds) {
            RoleMenu roleMenu = new RoleMenu();
            roleMenu.setRoleId(roleId);
            roleMenu.setMenuId(menuId);
            roleMenus.add(roleMenu);
        }
        
        // 批量插入
        for (RoleMenu roleMenu : roleMenus) {
            roleMenuMapper.insert(roleMenu);
        }

        return true;
        
    }

    /**
     * 获取角色已分配的菜单ID列表
     * @param roleId 角色ID
     * @return 菜单ID列表
     */
    public List<Long> getRoleMenuIds(Long roleId) {
        if (roleId == null) {
            throw new BizException("角色ID不能为空");
        }
        return roleMenuMapper.selectMenuIdsByRoleId(roleId);
    }

    /**
     * 获取角色的菜单列表
     * @param roleId 角色ID
     * @return 菜单列表
     */
    public List<Menu> getRoleMenus(Long roleId) {
        if (roleId == null) {
            throw new BizException("角色ID不能为空");
        }
        return roleMenuMapper.selectByRoleId(roleId);
    }

}