package com.sub.baoxian.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.common.constants.MessageType;
import com.sub.baoxian.common.exception.BizException;
import com.sub.baoxian.common.response.PageResult;
import com.sub.baoxian.mapper.MessageMapper;
import com.sub.baoxian.model.entity.Message;
import com.sub.baoxian.util.StpKit;
import com.sub.baoxian.websocket.service.WebSocketService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class MessageService {

    private final MessageMapper messageMapper;
    private final WebSocketService webSocketService;

    /**
     * 获取当前登录用户未读消息列表
     * 
     * @return 未读消息列表
     */
    public List<Message> getUserUnreadMessages() {

        try {
            Long userId = StpKit.USER.getLoginIdAsLong();

            // 查询当前登录用户未读信息
            List<Message> unreadMessages = messageMapper.selectList(new LambdaQueryWrapper<Message>()
                    .eq(Message::getUserId, userId)
                    .eq(Message::getStatus, 0));
            return unreadMessages;
        } catch (Exception e) {
            throw new BizException("获取未读消息失败");
        }
    }

    /**
     * 更新消息为已读状态
     * 
     * @param messageId 消息ID
     */
    public void readMessage(Long messageId) {
        try {
            Long userId = StpKit.USER.getLoginIdAsLong();
            messageMapper.update(new LambdaUpdateWrapper<Message>()
                    .eq(Message::getId, messageId)
                    .eq(Message::getUserId, userId)
                    .set(Message::getStatus, 1)
                    .set(Message::getReadAt, System.currentTimeMillis()));
        } catch (Exception e) {
            throw new BizException("更新消息为已读状态失败");
        }
    }

    /**
     * 获取当前用户未推送消息列表
     * 
     * @return 未推送消息列表
     */
    public List<Message> getUserUnpushMessage() {
        try {
            Long userId = StpKit.USER.getLoginIdAsLong();
            // 获取当前用户未推送消息数量
            List<Message> Messages = messageMapper
                    .selectList(new LambdaQueryWrapper<Message>().eq(Message::getUserId, userId)
                            .eq(Message::getIsPush, 0));
            if (Messages.isEmpty()) {
                return new ArrayList<>();
            }
            return Messages;
        } catch (Exception e) {
            throw new BizException("获取当前登录用户未推送信息数量失败");
        }
    }

    /**
     * 更新消息为已推送
     * 
     * @param messageId 消息ID
     */
    public void pushMessage(Long messageId) {
        try {
            Long userId = StpKit.USER.getLoginIdAsLong();
            messageMapper.update(new LambdaUpdateWrapper<Message>().eq(Message::getId, messageId)
                    .eq(Message::getUserId, userId)
                    .set(Message::getIsPush, 1));
        } catch (Exception e) {
            throw new BizException("更新消息为已推送失败");
        }
    }

    /**
     * 向指定用户发送消息
     * 
     * @param userId      用户ID
     * @param title       消息标题
     * @param content     消息内容
     * @param messageType 消息类型 1=系统消息,2=计划书信息
     */
    public void sendMessageToUser(Long userId, String title, String content, MessageType messageType) {
        try {
            Message message = Message.builder()
                    .userId(userId)
                    .title(title)
                    .content(content)
                    .messageType(messageType)
                    .status(0) // 未读
                    .isPush(0) // 未推送
                    .isImportant(0) // 非重要
                    .createdAt(System.currentTimeMillis())
                    .build();

            // 保存到数据库
            messageMapper.insert(message);

            // 通过WebSocket发送
            webSocketService.sendMessageToUser(userId, message);

            log.info("已向用户 {} 发送消息: {}", userId, title);
        } catch (Exception e) {
            log.error("向指定用户发送消息失败", e);
            throw new BizException("向指定用户发送消息失败");
        }
    }

    /**
     * 发送广播消息给所有用户
     * 
     * @param title       消息标题
     * @param content     消息内容
     * @param messageType 消息类型
     */
    public void sendBroadcastMessage(String title, String content, MessageType messageType) {
        try {
            // 广播消息通过WebSocket发送
            webSocketService.sendBroadcast(Map.of(
                    "title", title,
                    "content", content,
                    "type", messageType,
                    "timestamp", System.currentTimeMillis()));

            log.info("已发送广播消息: {}", title);
        } catch (Exception e) {
            log.error("发送广播消息失败", e);
            throw new BizException("发送广播消息失败");
        }
    }

    /**
     * 获取所有信息列表
     * 
     * @return 所有信息列表
     */
    public List<Message> getAllMessages() {
        try {
            Long userId = StpKit.USER.getLoginIdAsLong();
            return messageMapper.selectList(new LambdaQueryWrapper<Message>().eq(Message::getUserId, userId)
                    .orderByDesc(Message::getCreatedAt));
        } catch (Exception e) {
            throw new BizException("获取所有信息列表失败");
        }
    }

    /**
     * 获取所有未读消息
     * 
     * @return 未读消息列表
     */
    public List<Message> getAllUnreadMessages() {

        try {
            Long userId = StpKit.USER.getLoginIdAsLong();
            return messageMapper.selectList(new LambdaQueryWrapper<Message>().eq(Message::getUserId, userId)
                    .eq(Message::getStatus, 0));
        } catch (Exception e) {
            throw new BizException("获取所有未读消息失败");
        }
    }

    /**
     * 分页查询消息
     * 
     * @param page     页码
     * @param pageSize 每页大小
     * @return 分页消息结果
     */
    public PageResult<Message> getPagedMessages(Integer page, Integer pageSize) {
        try {
            Long userId = StpKit.USER.getLoginIdAsLong();

            // 使用MyBatisPlus分页查询
            Page<Message> messagePage = new Page<>(page, pageSize);
            IPage<Message> result = messageMapper.selectPage(messagePage,
                    new LambdaQueryWrapper<Message>()
                            .eq(Message::getUserId, userId)
                            .orderByDesc(Message::getCreatedAt));

            // 构建分页结果
            PageResult<Message> pageResult = new PageResult<>();
            pageResult.setTotal(result.getTotal());
            pageResult.setPages(result.getPages());
            pageResult.setCurrentPage(page);
            pageResult.setPageSize(pageSize);
            pageResult.setList(result.getRecords());
            pageResult.setHasNext(page < result.getPages());

            return pageResult;
        } catch (Exception e) {
            throw new BizException("分页查询消息失败");
        }
    }
}
