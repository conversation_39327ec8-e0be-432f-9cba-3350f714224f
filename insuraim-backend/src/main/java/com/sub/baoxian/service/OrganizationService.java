package com.sub.baoxian.service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.mapper.OrganizationMapper;
import com.sub.baoxian.mapper.UserMapper;
import com.sub.baoxian.model.entity.Organization;
import com.sub.baoxian.model.vo.OrganizationPageListVO;

import lombok.RequiredArgsConstructor;

/**
 * 组织服务类
 */
@Service
@RequiredArgsConstructor
public class OrganizationService {

    private final OrganizationMapper organizationMapper;

    private final UserMapper userMapper;

    /**
     * 分页查询组织列表
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param keyword 关键字
     * @param type 组织类型
     * @param status 状态
     * @return 分页结果
     */
    public Page<OrganizationPageListVO> page(Integer pageNum, Integer pageSize, String keyword, String type, Integer status) {
        // 构建查询条件
        LambdaQueryWrapper<Organization> wrapper = new LambdaQueryWrapper<>();
        
        // 关键字查询
        if (StringUtils.hasText(keyword)) {
            wrapper.like(Organization::getName, keyword)
                   .or()
                   .like(Organization::getCode, keyword);
        }
        
        // 类型查询
        if (StringUtils.hasText(type)) {
            wrapper.eq(Organization::getType, type);
        }
        
        // 状态查询
        if (status != null) {
            wrapper.eq(Organization::getStatus, status);
        }
        
        // 排序
        wrapper.orderByAsc(Organization::getSortOrder);
        
        // 分页查询
        Page<Organization> page = new Page<>(pageNum, pageSize);
        Page<Organization> organizationPage = organizationMapper.selectPage(page, wrapper);
        
        // 一次性查询所有组织的用户数量
        List<Map<String, Object>> userCounts = userMapper.getOrgUserCounts();
        
        // 创建组织ID到用户数量的映射
        Map<Long, Integer> orgUserCountMap = new java.util.HashMap<>();
        for (Map<String, Object> count : userCounts) {
            // 获取orgId并转换为Long类型
            Long orgId = ((Number) count.get("orgId")).longValue();
            Integer userCount = ((Number) count.get("userCount")).intValue();
            orgUserCountMap.put(orgId, userCount);
        }
        
        // 转换为VO对象
        Page<OrganizationPageListVO> voPage = new Page<>(pageNum, pageSize);
        voPage.setTotal(organizationPage.getTotal());
        voPage.setRecords(organizationPage.getRecords().stream().map(organization -> {
            OrganizationPageListVO vo = new OrganizationPageListVO();
            BeanUtils.copyProperties(organization, vo);
            
            // 从映射中获取用户数量，如果不存在则为0
            Long orgId = organization.getId();
            Integer userCount = orgUserCountMap.getOrDefault(orgId, 0);
            vo.setCurrentUser(userCount);
            
            return vo;
        }).collect(Collectors.toList()));
        
        return voPage;
    }

    /**
     * 新增组织
     * @param organization 组织信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean save(Organization organization) {
        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        organization.setCreatedAt(now);
        organization.setUpdatedAt(now);
        
        // 如果未设置排序号，则默认为0
        if (organization.getSortOrder() == null) {
            organization.setSortOrder(0);
        }
        
        // 插入数据
        return organizationMapper.insert(organization) > 0;
    }

    /**
     * 修改组织
     * @param organization 组织信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean update(Organization organization) {
        // 设置更新时间
        organization.setUpdatedAt(LocalDateTime.now());
        
        // 更新数据
        return organizationMapper.updateById(organization) > 0;
    }

    /**
     * 删除组织
     * @param id 组织ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean removeById(Long id) {
        // 删除组织
        Organization org = new Organization();
        org.setId(id);
        org.setDeletedAt(LocalDateTime.now());
        return organizationMapper.deleteById(id) > 0;
    }

    /**
     * 批量删除组织
     * @param ids 组织ID列表
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIds(List<Long> ids) {
        // 批量删除组织
        return organizationMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据ID获取组织信息
     * @param id 组织ID
     * @return 组织信息
     */
    public Organization getById(Long id) {
        return organizationMapper.selectById(id);
    }

    /**
     * 获取组织列表
     * @return 组织列表
     */
    public List<Organization> tree() {
        // 查询所有组织
        List<Organization> allOrgs = organizationMapper.selectList(
            new LambdaQueryWrapper<Organization>()
                .orderByAsc(Organization::getSortOrder)
        );
        
        
        return allOrgs;
    }
    
    /**
     * 获取指定类型的组织列表
     * @param type 组织类型
     * @return 组织列表
     */
    public List<Organization> listByType(String type) {
        LambdaQueryWrapper<Organization> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Organization::getType, type)
               .eq(Organization::getStatus, 1)
               .orderByAsc(Organization::getSortOrder);
        return organizationMapper.selectList(wrapper);
    }
    
} 