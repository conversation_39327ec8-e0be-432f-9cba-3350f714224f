package com.sub.baoxian.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.common.exception.BizException;
import com.sub.baoxian.mapper.PermissionMapper;
import com.sub.baoxian.mapper.RolePermissionMapper;
import com.sub.baoxian.model.entity.Permission;
import com.sub.baoxian.model.relation.RolePermission;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 权限服务类
 */
@Service
@RequiredArgsConstructor
public class PermissionService {

    private final PermissionMapper permissionMapper;
    private final RolePermissionMapper rolePermissionMapper;

    /**
     * 分页查询权限列表
     * 
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @param keyword  关键字
     * @return 分页结果
     */
    public Page<Permission> page(Integer pageNum, Integer pageSize, String keyword) {
        // 构建查询条件
        LambdaQueryWrapper<Permission> wrapper = new LambdaQueryWrapper<>();

        // 关键字查询
        if (StringUtils.hasText(keyword)) {
            wrapper.like(Permission::getName, keyword)
                    .or()
                    .like(Permission::getPermKey, keyword)
                    .or()
                    .like(Permission::getDescription, keyword);
        }

        // 分页查询
        Page<Permission> page = new Page<>(pageNum, pageSize);
        return permissionMapper.selectPage(page, wrapper);
    }

    /**
     * 获取所有权限列表
     * 
     * @return 权限列表
     */
    public List<Permission> list() {
        return permissionMapper.selectList(null);
    }

    /**
     * 根据ID获取权限信息
     * 
     * @param id 权限ID
     * @return 权限信息
     */
    public Permission getById(Long id) {
        return permissionMapper.selectById(id);
    }

    /**
     * 新增权限
     * 
     * @param permission 权限信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean save(Permission permission) {
        // 检查权限标识是否已存在
        LambdaQueryWrapper<Permission> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Permission::getPermKey, permission.getPermKey());
        if (permissionMapper.selectCount(wrapper) > 0) {
            throw new BizException("权限标识已存在");
        }

        return permissionMapper.insert(permission) > 0;
    }

    /**
     * 修改权限
     * 
     * @param permission 权限信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean update(Permission permission) {
        // 检查权限标识是否已存在（排除自身）
        LambdaQueryWrapper<Permission> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Permission::getPermKey, permission.getPermKey())
                .ne(Permission::getId, permission.getId());
        if (permissionMapper.selectCount(wrapper) > 0) {
            throw new BizException("权限标识已存在");
        }

        return permissionMapper.updateById(permission) > 0;
    }

    /**
     * 删除权限
     * 
     * @param id 权限ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean removeById(Long id) {
        return permissionMapper.deleteById(id) > 0;
    }

    /**
     * 批量删除权限
     * 
     * @param ids 权限ID列表
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIds(List<Long> ids) {
        return permissionMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据角色ID获取权限列表
     * 
     * @param roleId 角色ID
     * @return 权限列表
     */
    public List<Permission> getByRoleId(Long roleId) {
        return permissionMapper.selectByRoleId(roleId);
    }

    /**
     * 为角色分配权限
     * 
     * @param roleId        角色ID
     * @param permissionIds 权限ID列表
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean assignPermissions(Long roleId, List<Long> permissionIds) {

        // 先进行判断是否需要更新权限列表
        if (permissionIds == null || permissionIds.isEmpty()) {
            LambdaQueryWrapper<RolePermission> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(RolePermission::getRoleId, roleId);
            Long count = rolePermissionMapper.selectCount(wrapper);
            if (count > 0) {
                rolePermissionMapper.delete(wrapper);
            }
            return true;
        }

        // 先删除该角色的所有权限
        rolePermissionMapper.deleteByRoleId(roleId);

        // 批量插入新的权限关联
        List<RolePermission> rolePermissions = new ArrayList<>();
        for (Long permissionId : permissionIds) {
            RolePermission rolePermission = new RolePermission();
            rolePermission.setRoleId(roleId);
            rolePermission.setPermissionId(permissionId);
            rolePermissions.add(rolePermission);
        }

        for (RolePermission rolePermission : rolePermissions) {
            rolePermissionMapper.insert(rolePermission);
        }

        return true;
    }

}