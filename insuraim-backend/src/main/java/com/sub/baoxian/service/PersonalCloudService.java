package com.sub.baoxian.service;

import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import com.sub.baoxian.common.exception.BizException;
import com.sub.baoxian.mapper.PersonalCloudMapper;
import com.sub.baoxian.model.entity.PersonalCloud;
import com.sub.baoxian.util.OssUtil;
import com.sub.baoxian.util.StpKit;
import com.sub.baoxian.model.entity.PCFolder;
import com.sub.baoxian.model.entity.PCFile;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.List;

/**
 * 个人云盘服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PersonalCloudService {

    private final PersonalCloudMapper personalCloudMapper;

    /**
     * 获取用户云盘基本信息
     * 
     * @param userId 用户ID
     * @return 云盘信息
     */
    public Map<String, Object> getPersonalCloudInfo() {
        Map<String, Object> result = new HashMap<>();

        Long userId = StpKit.USER.getLoginIdAsLong();
        try {
            PersonalCloud cloudInfo = personalCloudMapper.getCloudInfoByUserId(userId);

            if (cloudInfo == null) {
                return null;
            }

            result.put("totalStorage", cloudInfo.getTotalStorage());
            result.put("usedStorage", cloudInfo.getUsedStorage());
            result.put("isActive", cloudInfo.getIsActive());
            result.put("createdAt", cloudInfo.getCreatedAt());
            result.put("userId", cloudInfo.getUserId());
        } catch (Exception e) {
            log.error("获取用户云盘信息失败: userId={}", userId, e);
            throw new BizException("获取用户云盘信息失败");
        }
        return result;
    }

    // /**
    // * 创建新用户的云盘记录
    // * @param userId 用户ID
    // * @return 云盘信息实体
    // */
    // private PersonalCloud createNewUserCloud(Long userId) {
    // long currentTime = System.currentTimeMillis();

    // PersonalCloud personalCloud = PersonalCloud.builder()
    // .userId(userId)
    // .totalStorage(DEFAULT_TOTAL_STORAGE)
    // .usedStorage(0L)
    // .isActive(true)
    // .createdAt(currentTime)
    // .updatedAt(currentTime)
    // .lastAccessedAt(currentTime)
    // .settings("{}")
    // .build();

    // personalCloudMapper.insertPersonalCloud(personalCloud);
    // return personalCloud;
    // }

    /**
     * 获取文件夹内容（文件夹和文件）
     * 
     * @param userId   用户ID
     * @param folderId 文件夹ID，null或0表示根目录
     * @param fileType 文件类型筛选，可选
     * @return 文件夹内容
     */
    public Map<String, Object> getFolderContent(Long folderId) {
        Map<String, Object> result = new HashMap<>();
        Long userId = StpKit.USER.getLoginIdAsLong();
        try {
            // 如果folderId为null或0，表示根目录
            Long parentId = (folderId == null || folderId == 0) ? 0L : folderId;

            // 获取文件夹列表
            List<PCFolder> folders = personalCloudMapper.getFoldersByParentId(userId, parentId);

            // 获取文件列表
            List<PCFile> files = personalCloudMapper.getFilesByFolderId(userId, parentId);

            // 当前文件夹信息
            PCFolder currentFolder = null;
            if (folderId != null && folderId > 0) {
                currentFolder = personalCloudMapper.getFolderById(folderId);
            }

            result.put("folders", folders);
            result.put("files", files);
            result.put("currentFolder", currentFolder);
        } catch (Exception e) {
            log.error("获取文件夹内容失败: userId={}, folderId={}", userId, folderId, e);
            throw new BizException("获取文件夹内容失败");
        }
        return result;
    }

    /**
     * 创建文件夹
     * 
     * @param folderData 文件夹数据
     * @return 创建结果
     */
    public void createFolder(Map<String, Object> folderData) {

        try {
            String name = (String) folderData.get("name");
            if (name == null || name.trim().isEmpty()) {
                throw new BizException("文件夹名称不能为空");
            }

            Long parentId = folderData.get("parentId") != null ? Long.valueOf(folderData.get("parentId").toString())
                    : 0L;
            Long userId = StpKit.USER.getLoginIdAsLong();
            Long creatorId = folderData.get("creatorId") != null ? Long.valueOf(folderData.get("creatorId").toString())
                    : userId;

            // 生成文件夹路径
            String path = buildFolderPath(parentId, name, userId);

            // 创建文件夹实体
            long currentTime = System.currentTimeMillis();
            PCFolder folder = PCFolder.builder()
                    .name(name)
                    .parentId(parentId)
                    .userId(userId)
                    .path(path)
                    .description((String) folderData.get("description"))
                    .createdAt(currentTime)
                    .updatedAt(currentTime)
                    .creatorId(creatorId)
                    .build();

            // 保存到数据库
            personalCloudMapper.insertFolder(folder);

        } catch (Exception e) {
            log.error("创建文件夹失败", e);
            throw new BizException("文件夹创建失败");
        }
    }

    /**
     * 构建文件夹路径
     * 
     * @param parentId   父文件夹ID
     * @param folderName 文件夹名称
     * @return 完整路径
     */
    private String buildFolderPath(Long parentId, String folderName, Long userId) {
        if (parentId == null || parentId == 0) {
            return "/根目录/" + folderName;
        } else {
            PCFolder parentFolder = personalCloudMapper.getFolderById(parentId);
            if (parentFolder != null) {
                return parentFolder.getPath() + "/" + folderName;
            } else {
                return "/根目录/" + folderName;
            }
        }
    }

    /**
     * 删除文件夹
     * 
     * @param folderId 文件夹ID
     * @return 删除结果
     */
    public Boolean deleteFolder(Long folderId) {
        try {
            // 检查是否有子文件夹
            int subFolderCount = personalCloudMapper.countSubFolders(folderId);
            if (subFolderCount > 0) {
                log.warn("文件夹下还有子文件夹，无法删除: folderId={}, subFolderCount={}", folderId, subFolderCount);
                throw new BizException("文件夹下还有子文件夹，无法删除");
            }

            // 检查是否有文件
            Long fileSize = personalCloudMapper.getFilesTotalSizeByFolderId(folderId);
            if (fileSize != null && fileSize > 0) {
                // 文件夹下有文件，无法删除
                log.warn("文件夹下还有文件，无法删除: folderId={}, totalSize={}", folderId, fileSize);
                throw new BizException("文件夹下还有文件，无法删除");
            }

            // 删除文件夹
            personalCloudMapper.deleteFolder(folderId);
            return true;
        } catch (Exception e) {
            log.error("删除文件夹失败: folderId={}", folderId, e);
            return false;
        }
    }

    /**
     * 上传文件
     * 
     * @param file     文件
     * @param folderId 文件夹ID
     * @param userId   用户ID
     * @return 上传结果
     */
    public void uploadFile(MultipartFile file, Long folderId) {
        Long userId = StpKit.USER.getLoginIdAsLong();
        try {
            if (file == null) {
                throw new BizException("上传失败，请选择文件");
            }

            // 获取用户云盘信息
            PersonalCloud cloudInfo = personalCloudMapper.getCloudInfoByUserId(userId);

            // 检查存储空间是否足够
            long fileSize = file.getSize();
            if (cloudInfo.getUsedStorage() + fileSize > cloudInfo.getTotalStorage()) {
                throw new BizException("存储空间不足");
            }

            // 处理文件夹ID，如果是null，设为根目录(0)
            Long actualFolderId = (folderId == null) ? 0L : folderId;

            // 处理文件名
            String filename = file.getOriginalFilename();
            String originalFilename = StringUtils.cleanPath(filename != null ? filename : "未命名文件");

            // 获取文件类型
            String fileType = "unknown";
            if (originalFilename.contains(".")) {
                fileType = originalFilename.substring(originalFilename.lastIndexOf(".") + 1).toLowerCase();
            }

            // 生成唯一文件名
            String uniqueFileName = UUID.randomUUID().toString() + "." + fileType;

            // 生成OSS URL
            String ossUrl = OssUtil.uploadFile(file, "Insuriam/personal-cloud", uniqueFileName);
            String ossBucketName = uniqueFileName;
            String md5 = null;
            try {
                md5 = DigestUtils.md5DigestAsHex(file.getBytes());
            } catch (Exception e) {
                log.error("计算文件MD5失败: fileName={}", filename, e);
            }

            // 创建文件记录
            long currentTime = System.currentTimeMillis();
            PCFile pcFile = PCFile.builder()
                    .name(originalFilename)
                    .folderId(actualFolderId)
                    .userId(userId)
                    .fileType(fileType)
                    .size(fileSize)
                    .ossUrl(ossUrl)
                    .ossBucketName(ossBucketName)
                    .md5(md5)
                    .createdAt(currentTime)
                    .updatedAt(currentTime)
                    .creatorId(userId)
                    .downloadCount(0)
                    .isFavorite(false)
                    .tag(null)
                    .build();

            // 保存到数据库
            personalCloudMapper.insertFile(pcFile);

            // 更新已使用空间
            cloudInfo.setUsedStorage(cloudInfo.getUsedStorage() + fileSize);
            cloudInfo.setUpdatedAt(currentTime);
            personalCloudMapper.updateUsedStorage(userId, cloudInfo.getUsedStorage(), currentTime);
        } catch (Exception e) {
            log.error("文件上传失败: userId={}, folderId={}", userId, folderId, e);
            throw new BizException("文件上传失败");
        }
    }

    /**
     * 删除文件
     * 
     * @param fileId 文件ID
     * @param userId 用户ID
     * @return 删除结果
     */
    public Boolean deleteFile(Long fileId) {
        Long userId = StpKit.USER.getLoginIdAsLong();
        try {
            // 获取文件信息
            PCFile file = personalCloudMapper.getFileById(fileId);
            if (file == null) {
                log.warn("文件不存在: fileId={}", fileId);
                throw new BizException("文件不存在");
            }

            // 验证文件所有权
            if (!file.getUserId().equals(userId)) {
                log.warn("无权删除该文件: fileId={}, fileUserId={}, requestUserId={}",
                        fileId, file.getUserId(), userId);
                throw new BizException("无权删除该文件");
            }

            // 删除数据库记录
            personalCloudMapper.deleteFile(fileId);
            // 删除OSS文件
            OssUtil.deleteFile(extractObjectNameFromUrl(file.getOssUrl()));

            Long currentTime = System.currentTimeMillis();
            // 更新已使用空间
            PersonalCloud cloudInfo = personalCloudMapper.getCloudInfoByUserId(userId);
            if (cloudInfo != null) {
                long newUsedStorage = Math.max(0, cloudInfo.getUsedStorage() - file.getSize());
                personalCloudMapper.updateUsedStorage(userId, newUsedStorage, currentTime);
            }

            return true;
        } catch (Exception e) {
            log.error("删除文件失败: fileId={}", fileId, e);
            throw new BizException("删除文件失败");
        }
    }

    /**
     * 获取文件下载URL
     * 
     * @param fileId 文件ID
     * @return 文件下载信息
     */
    public Map<String, Object> getFileDownloadUrl(Long fileId) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 获取文件信息
            PCFile file = personalCloudMapper.getFileById(fileId);

            if (file != null) {
                // 更新下载次数
                personalCloudMapper.incrementDownloadCount(fileId);

                result.put("id", file.getId());
                result.put("name", file.getName());
                result.put("size", file.getSize());
                result.put("downloadUrl", file.getOssUrl());
                result.put("bucketName", file.getOssBucketName());
                return result;
            } else {
                throw new BizException("文件不存在");
            }
        } catch (Exception e) {
            log.error("获取文件下载URL失败: fileId={}", fileId, e);
            throw new BizException("获取下载链接失败");
        }
    }

    /**
     * 从完整的OSS URL中提取对象名
     * 
     * @param ossUrl OSS完整URL
     * @return 对象名（包括路径）
     */
    private String extractObjectNameFromUrl(String ossUrl) {
        // 简单规则：通常OSS URL格式为https://bucket.endpoint/objectName
        // 我们尝试提取最后一部分作为objectName

        if (ossUrl == null || ossUrl.isEmpty()) {
            return "";
        }

        // 移除查询参数
        if (ossUrl.contains("?")) {
            ossUrl = ossUrl.substring(0, ossUrl.indexOf("?"));
        }

        // 如果URL包含协议，如http://或https://，移除该部分
        if (ossUrl.contains("://")) {
            ossUrl = ossUrl.substring(ossUrl.indexOf("://") + 3);
        }

        // 移除域名部分 (第一个斜杠之前的部分)
        if (ossUrl.contains("/")) {
            ossUrl = ossUrl.substring(ossUrl.indexOf("/") + 1);
        }

        return ossUrl;
    }
}