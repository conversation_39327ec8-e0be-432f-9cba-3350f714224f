package com.sub.baoxian.service;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.common.exception.BizException;
import com.sub.baoxian.mapper.ProductCategoriesMapper;
import com.sub.baoxian.model.entity.ProductCategories;
import com.sub.baoxian.model.vo.CategoriesVO;

import lombok.RequiredArgsConstructor;

/**
 * 分类服务类
 */
@Service
@RequiredArgsConstructor
public class ProductCategoryService {

    private final ProductCategoriesMapper productCategoriesMapper;

    /**
     * 分页查询分类列表
     *
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @param keyword 关键字
     * @return 分页结果
     */
    public Page<CategoriesVO> page(Integer pageNum, Integer pageSize, String keyword) {
        // 构建查询条件
        LambdaQueryWrapper<ProductCategories> wrapper = new LambdaQueryWrapper<>();

        // 关键字查询
        if (StringUtils.hasText(keyword)) {
            wrapper.like(ProductCategories::getName, keyword).or().like(ProductCategories::getCode, keyword);
        }

        // 排序
        wrapper.orderByAsc(ProductCategories::getId);

        // 分页查询
        Page<ProductCategories> page = new Page<>(pageNum, pageSize);
        Page<ProductCategories> categoryPage = productCategoriesMapper.selectPage(page, wrapper);

        // 转换为VO
        List<CategoriesVO> categoriesVOs = categoryPage.getRecords().stream().map(category -> {
            CategoriesVO categoriesVO = new CategoriesVO();
            BeanUtils.copyProperties(category, categoriesVO);

            categoriesVO.setCreateAt(category.getCreatedAt());
            categoriesVO.setUpdateAt(category.getUpdatedAt());
            return categoriesVO;
        }).collect(Collectors.toList());

        // 创建新的Page对象，并设置数据
        Page<CategoriesVO> resultPage = new Page<>(pageNum, pageSize);
        resultPage.setRecords(categoriesVOs);
        resultPage.setTotal(categoryPage.getTotal());

        return resultPage;
    }

    /**
     * 获取所有分类
     * 
     * @return 分类列表
     */
    public List<ProductCategories> list() {
        LambdaQueryWrapper<ProductCategories> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByAsc(ProductCategories::getId);
        return productCategoriesMapper.selectList(wrapper);
    }

    /**
     * 根据ID获取分类信息
     * 
     * @param id 分类ID
     * @return 分类信息
     */
    public ProductCategories getById(Long id) {
        return productCategoriesMapper.selectById(id);
    }

    /**
     * 新增分类
     * 
     * @param category 分类信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean save(ProductCategories category) {
        // 检查分类编码是否已存在
        LambdaQueryWrapper<ProductCategories> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductCategories::getCode, category.getCode());
        Long count = productCategoriesMapper.selectCount(wrapper);
        if (count > 0) {
            throw new BizException("分类编码已存在");
        }

        // 设置创建时间和更新时间
        category.setCreatedAt(System.currentTimeMillis());
        category.setUpdatedAt(System.currentTimeMillis());

        // 插入数据
        return productCategoriesMapper.insert(category) > 0;
    }

    /**
     * 修改分类
     * 
     * @param category 分类信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ProductCategories category) {
        // 检查分类是否存在
        ProductCategories existingCategory = productCategoriesMapper.selectById(category.getId());
        if (existingCategory == null) {
            throw new BizException("分类不存在");
        }

        // 检查分类编码是否已被其他分类使用
        if (!existingCategory.getCode().equals(category.getCode())) {
            LambdaQueryWrapper<ProductCategories> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ProductCategories::getCode, category.getCode()).ne(ProductCategories::getId, category.getId());
            Long count = productCategoriesMapper.selectCount(wrapper);
            if (count > 0) {
                throw new BizException("分类编码已被其他分类使用");
            }
        }

        // 设置更新时间
        category.setUpdatedAt(System.currentTimeMillis());

        // 更新数据
        return productCategoriesMapper.updateById(category) > 0;
    }

    /**
     * 删除分类
     * 
     * @param id 分类ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean removeById(Long id) {
        // 检查分类是否存在
        ProductCategories category = productCategoriesMapper.selectById(id);
        if (category == null) {
            throw new BizException("分类不存在");
        }

        // 删除分类
        return productCategoriesMapper.deleteById(id) > 0;
    }
}