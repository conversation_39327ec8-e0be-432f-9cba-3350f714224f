package com.sub.baoxian.service;

import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sub.baoxian.common.constants.MessageType;
import com.sub.baoxian.common.constants.ProposalStatus;
import com.sub.baoxian.common.exception.BizException;
import com.sub.baoxian.core.playwright.dto.ProposalRequestDTO;
import com.sub.baoxian.core.playwright.service.PlaywrightService;
import com.sub.baoxian.mapper.ProposalRecordMapper;
import com.sub.baoxian.model.entity.ProposalRecord;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 保险计划书异步服务
 * 专门处理异步生成计划书的任务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProposalAsyncService {

    private final PlaywrightService playwrightService;
    private final ProposalRecordMapper proposalRecordMapper;
    private final MessageService messageService;

    /**
     * 最大重试次数
     */
    private static final int MAX_RETRY_COUNT = 2;

    /**
     * 异步生成保险计划书
     *
     * @param proposalId      计划书ID
     * @param proposalRequest 请求参数
     */
    @Async("taskExecutor")
    public void generateProposalAsync(Long proposalId, ProposalRequestDTO proposalRequest) {
        log.info("开始异步生成计划书, ID: {}, 参数: {}", proposalId, proposalRequest);

        int retryCount = 0;
        BizException lastException = null;

        while (retryCount <= MAX_RETRY_COUNT) {
            try {
                // 如果不是第一次尝试，则记录重试日志
                if (retryCount > 0) {
                    log.info("重试生成计划书 第{}次, ID: {}", retryCount, proposalId);
                    // 重试前等待一段时间，避免立即重试
                    try {
                        Thread.sleep(1000); // 等待1秒再重试
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }

                // 调用Playwright服务生成PDF
                String pdfUrl = playwrightService.generateProposalPdf(proposalRequest);

                // 更新计划书记录
                LambdaUpdateWrapper<ProposalRecord> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(ProposalRecord::getId, proposalId)
                        .set(ProposalRecord::getOssFileUrl, pdfUrl)
                        .set(ProposalRecord::getStatus, ProposalStatus.COMPLETED)
                        .set(ProposalRecord::getStatusNote, "计划书生成成功");

                proposalRecordMapper.update(null, updateWrapper);
                // 发送消息
                ProposalRecord proposal = proposalRecordMapper.selectById(proposalId);
                messageService.sendMessageToUser(proposal.getUserId(), "计划书生成成功",
                        "您申请的计划书" + proposal.getProposalNo() + "已制作完成，请下载浏览", MessageType.PROPOSAL_MESSAGE);
                log.info("计划书生成成功, ID: {}, URL: {}", proposalId, pdfUrl);

                // 成功生成，跳出循环
                return;
            } catch (BizException e) {
                lastException = e;
                log.error("异步生成计划书失败 (尝试 {}/{}), ID: {}, 错误信息: {}",
                        retryCount + 1, MAX_RETRY_COUNT + 1, proposalId, e.getMessage());

                // 增加重试计数
                retryCount++;
            }
        }

        // 所有重试都失败，更新计划书记录为失败状态
        log.error("异步生成计划书失败，已重试{}次，ID: {}, 最终错误: {}",
                MAX_RETRY_COUNT, proposalId, lastException.getMessage());

        LambdaUpdateWrapper<ProposalRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProposalRecord::getId, proposalId)
                .set(ProposalRecord::getStatus, ProposalStatus.FAILED)
                .set(ProposalRecord::getStatusNote, lastException.getMessage());

        proposalRecordMapper.update(null, updateWrapper);
        // 发送消息
        ProposalRecord proposal = proposalRecordMapper.selectById(proposalId);
        messageService.sendMessageToUser(proposal.getUserId(), "计划书生成失败",
                "您申请的计划书" + proposal.getProposalNo() + "生成失败，请重新申请", MessageType.PROPOSAL_MESSAGE);
    }

}