package com.sub.baoxian.service;

import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.common.config.OssConfig;
import com.sub.baoxian.common.constants.ProposalStatus;
import com.sub.baoxian.common.exception.BizException;
import com.sub.baoxian.core.playwright.dto.ProposalRequestDTO;
import com.sub.baoxian.mapper.ProposalProductConfigMapper;
import com.sub.baoxian.mapper.ProposalRecordMapper;
import com.sub.baoxian.model.entity.ProposalProductConfig;
import com.sub.baoxian.model.entity.ProposalProductConfig.ProposalCalculateDataBO;
import com.sub.baoxian.model.entity.ProposalRecord;
import com.sub.baoxian.policymanagement.model.dto.ProposalCalculateRequestDTO;
import com.sub.baoxian.policymanagement.model.vo.ProposalCalculateVO;
import com.sub.baoxian.util.OssUtil;
import com.sub.baoxian.util.StpKit;

import cn.hutool.core.util.IdUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 保险计划书服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProposalService {

    private final ProposalRecordMapper proposalRecordMapper;
    private final ProposalProductConfigMapper proposalProductConfigMapper;
    private final ProposalAsyncService proposalAsyncService;
    private final OssConfig ossConfig;

    /**
     * 生成保险计划书（异步方式）
     * 
     * @param proposalRequest 请求参数
     * @return 创建的计划书ID
     */
    public Long generateProposal(ProposalRequestDTO proposalRequest) {

        Long productId = proposalRequest.getProductId();
        ProposalProductConfig product = proposalProductConfigMapper.selectById(productId);
        if (product == null) {
            throw new BizException("产品不存在");
        }

        if (product.getRegion() != null && !product.getRegion().isEmpty()) {
            proposalRequest.setRegion("HKG");
        }

        log.info("开始创建计划书任务, 参数: {}", proposalRequest);
        // 获取当前登录用户ID
        Long userId = StpKit.USER.getLoginIdAsLong();

        // 创建计划书记录
        ProposalRecord proposal = new ProposalRecord();
        proposal.setUserId(userId);
        proposal.setProductName(proposalRequest.getProductName());
        proposal.setCustomerName(proposalRequest.getCustomerName());
        proposal.setStatus(ProposalStatus.PROCESSING);
        proposal.setStatusNote("正在生成计划书，请稍后...");
        proposal.setProposalNo(generateProposalNo());

        // 保存计划书记录
        proposalRecordMapper.insert(proposal);
        Long proposalId = proposal.getId();
        if (proposalId == null) {
            throw new BizException("计划书记录创建失败：未能获取ID");
        }

        log.info("计划书记录已创建，ID: {}，现在委托异步服务生成计划书", proposalId);

        // 委托异步服务生成计划书
        proposalAsyncService.generateProposalAsync(proposalId, proposalRequest);

        return proposalId;
    }

    /**
     * 生成计划书编号
     * 
     * @return 计划书编号
     */
    private String generateProposalNo() {
        // 生成简单的计划书编号：P + 时间戳 + 随机数
        return "P" + System.currentTimeMillis() + IdUtil.fastSimpleUUID().substring(0, 8);
    }

    /**
     * 获取当前用户的计划书分页列表
     * 
     * @param pageNum 页码（从1开始）
     * @param pageSize 每页条数
     * @return 计划书分页列表
     */
    public Page<ProposalRecord> pageOfMine(Integer pageNum, Integer pageSize, String customerName, String status) {
        try {
            Long userId = StpKit.USER.getLoginIdAsLong();

            // 创建分页对象
            Page<ProposalRecord> page = new Page<>(pageNum, pageSize);

            // 构建查询条件：按用户ID过滤，按创建时间倒序排序
            LambdaQueryWrapper<ProposalRecord> queryWrapper = new LambdaQueryWrapper<ProposalRecord>()
                .eq(ProposalRecord::getUserId, userId).orderByDesc(ProposalRecord::getCreateAt);

            if (customerName != null && !customerName.isEmpty()) {
                queryWrapper.eq(ProposalRecord::getCustomerName, customerName);
            }

            if (status != null && !status.isEmpty()) {
                queryWrapper.eq(ProposalRecord::getStatus, status);
            }

            Page<ProposalRecord> result = proposalRecordMapper.selectPage(page, queryWrapper);
            return result;
        } catch (Exception e) {
            log.error("查询计划书列表失败", e);
            if (e instanceof BizException) {
                throw e;
            } else {
                throw new BizException("查询计划书列表失败: " + e.getMessage());
            }
        }
    }

    /**
     * 根据ID获取计划书详情
     * 
     * @param id 计划书ID
     * @return 计划书详情
     */
    public ProposalRecord getById(Long id) {
        log.info("查询计划书详情, ID: {}", id);

        try {
            // 获取当前登录用户ID
            Long userId = StpKit.USER.getLoginIdAsLong();

            // 查询计划书并验证所有权
            ProposalRecord proposal = proposalRecordMapper.selectById(id);
            if (proposal == null) {
                throw new BizException("计划书不存在");
            }

            // 验证计划书是否属于当前用户
            if (!userId.equals(proposal.getUserId())) {
                throw new BizException("无权访问该计划书");
            }

            return proposal;
        } catch (Exception e) {
            log.error("查询计划书详情失败", e);
            if (e instanceof BizException) {
                throw e;
            } else {
                throw new BizException("查询计划书详情失败: " + e.getMessage());
            }
        }
    }

    /**
     * 轮询计划书生成状态
     * 
     * @param id 计划书ID
     * @return 计划书生成状态
     */
    public String getStatus(Long id) {
        ProposalRecord proposal = proposalRecordMapper.selectById(id);
        if (proposal == null) {
            throw new BizException("计划书不存在");
        }
        return proposal.getStatus();
    }

    /**
     * 获取计划书PDF输入流
     * 
     * @param id 计划书ID
     * @param userId 当前用户ID
     * @return PDF输入流
     */
    public InputStream getProposalPdfStream(Long id, Long userId) {
        // 获取计划书信息
        ProposalRecord proposal = getById(id);

        // 验证计划书所有权
        if (proposal == null) {
            throw new BizException("计划书不存在");
        }

        if (!userId.equals(proposal.getUserId())) {
            throw new BizException("无权访问此计划书");
        }

        // 检查计划书PDF URL是否存在
        String pdfUrl = proposal.getOssFileUrl();
        if (pdfUrl == null || pdfUrl.isEmpty()) {
            throw new BizException("计划书PDF尚未生成");
        }

        // 从OSS URL中提取对象名
        String objectName = extractObjectNameFromUrl(pdfUrl);

        // 从OSS获取PDF内容
        return OssUtil.getFileInputStream(objectName);
    }

    /**
     * 从OSS URL中提取对象名
     */
    private String extractObjectNameFromUrl(String ossUrl) {
        String urlPrefix = ossConfig.getUrlPrefix();
        if (ossUrl.startsWith(urlPrefix)) {
            return ossUrl.substring(urlPrefix.length());
        }
        return ossUrl;
    }

    /**
     * 获取计划书文件名
     * 
     * @param id 计划书ID
     * @return 文件名
     */
    public String getProposalFileName(Long id) {
        ProposalRecord proposal = proposalRecordMapper.selectById(id);
        if (proposal == null) {
            throw new BizException("计划书不存在");
        }
        // 获取文件名，截取/到.pdf中间
        String url = proposal.getOssFileUrl();
        String fileName = url.substring(url.lastIndexOf("/") + 1, url.lastIndexOf("."));
        // 获取文件后缀(.pdf)
        String fileExtension = url.substring(url.lastIndexOf("."));
        return fileName + fileExtension;
    }

    /**
     * 计算计划书演算
     * 
     * @param request 请求参数
     * @return 计划书演算结果
     */
    public ProposalCalculateVO calculateForExcel(ProposalCalculateRequestDTO request) {
        LambdaQueryWrapper<ProposalProductConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProposalProductConfig::getId, request.getId()).eq(ProposalProductConfig::getStatus, 1)
            .eq(ProposalProductConfig::getPaymentYears, String.valueOf(request.getPaymentYear()))
            .like(ProposalProductConfig::getSupportCurrency, "\"" + request.getCurrency() + "\"");
        ProposalProductConfig product = proposalProductConfigMapper.selectOne(queryWrapper);
        if (product == null) {
            throw new BizException("产品不存在");
        }

        ProposalCalculateDataBO baseData = product.getBaseData();
        if (baseData == null || baseData.getBaseAmount() == null || baseData.getData() == null) {
            throw new BizException("产品的基准演算数据不存在或不完整");
        }

        Integer baseAmount = baseData.getBaseAmount();
        List<ProposalCalculateDataBO.ProposalCalculateData> data = baseData.getData();

        // 使用BigDecimal进行精确计算
        BigDecimal requestAmount = new BigDecimal(request.getPremiumAmount());
        BigDecimal baseAmountDecimal = new BigDecimal(baseAmount);
        // 计算缩放比例
        BigDecimal scale = requestAmount.divide(baseAmountDecimal, 4, RoundingMode.HALF_UP);

        List<ProposalCalculateVO.ProposalCalculateResultData> resultDataList = new ArrayList<>();

        for (ProposalCalculateDataBO.ProposalCalculateData originalData : data) {
            ProposalCalculateVO.ProposalCalculateResultData resultData =
                new ProposalCalculateVO.ProposalCalculateResultData();

            // 1. 复制保单年度并计算年龄
            resultData.setPolicyYear(originalData.getPolicyYear());
            resultData.setAge(request.getCustomerAge() + originalData.getPolicyYear());

            // 2. 缩放各项金额
            resultData.setTotalPremiumPaid(scaleAndRound(originalData.getTotalPremiumPaid(), scale));
            resultData.setSurrenderGuaranteed(scaleAndRound(originalData.getSurrenderGuaranteed(), scale));
            resultData.setTerminalBonus(scaleAndRound(originalData.getTerminalBonus(), scale));
            resultData.setTotalAmount(scaleAndRound(originalData.getTotalAmount(), scale));

            resultDataList.add(resultData);
        }

        ProposalCalculateVO result = new ProposalCalculateVO();
        result.setData(resultDataList);
        result.setProductName(product.getProductName());
        result.setCustomerAge(request.getCustomerAge());
        result.setCustomerGender(request.getCustomerGender());
        result.setPremiumAmount(request.getPremiumAmount());
        result.setPaymentYear(request.getPaymentYear());
        result.setCurrency(request.getCurrency());
        return result;
    }

    /**
     * 辅助方法：根据比例缩放并四舍五入为长整型
     * 
     * @param value a value to scale
     * @param scale the scale to apply
     * @return a scaled and rounded value
     */
    private Long scaleAndRound(Integer value, BigDecimal scale) {
        if (value == null) {
            return null;
        }
        return new BigDecimal(value).multiply(scale).setScale(0, RoundingMode.HALF_UP).longValue();
    }
}
