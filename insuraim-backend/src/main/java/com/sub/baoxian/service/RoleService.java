package com.sub.baoxian.service;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.mapper.RoleMapper;
import com.sub.baoxian.model.entity.Role;
import com.sub.baoxian.model.vo.RolePageListVO;

import lombok.RequiredArgsConstructor;

/**
 * 角色服务类
 */
@Service
@RequiredArgsConstructor
public class RoleService {

    private final RoleMapper roleMapper;

    /**
     * 分页查询角色列表
     * 
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param keyword 关键字
     * @return 分页结果
     */
    public Page<RolePageListVO> page(Integer pageNum, Integer pageSize, String keyword) {
        // 构建查询条件
        LambdaQueryWrapper<Role> wrapper = new LambdaQueryWrapper<>();

        // 关键字查询
        if (StringUtils.hasText(keyword)) {
            wrapper.like(Role::getRoleName, keyword).or().like(Role::getRoleCode, keyword);
        }

        // 排序
        wrapper.orderByDesc(Role::getCreatedAt);

        // 分页查询
        Page<Role> page = new Page<>(pageNum, pageSize);
        Page<Role> rolePage = roleMapper.selectPage(page, wrapper);

        // 转换为VO对象
        Page<RolePageListVO> voPage = new Page<>(pageNum, pageSize);
        voPage.setTotal(rolePage.getTotal());
        voPage.setRecords(rolePage.getRecords().stream().map(role -> {
            RolePageListVO vo = new RolePageListVO();
            BeanUtils.copyProperties(role, vo);
            return vo;
        }).collect(Collectors.toList()));

        return voPage;
    }

    /**
     * 获取所有角色列表
     * 
     * @return 角色列表
     */
    public List<Role> list() {
        // 构建查询条件
        LambdaQueryWrapper<Role> wrapper = new LambdaQueryWrapper<>();
        // 排序
        wrapper.orderByDesc(Role::getCreatedAt);
        // 查询所有数据
        return roleMapper.selectList(wrapper);
    }

    /**
     * 新增角色
     * 
     * @param role 角色信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean save(Role role) {
        // 插入数据
        return roleMapper.insert(role) > 0;
    }

    /**
     * 修改角色
     * 
     * @param role 角色信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean update(Role role) {
        // 更新数据
        return roleMapper.updateById(role) > 0;
    }

    /**
     * 删除角色
     * 
     * @param id 角色ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean removeById(Long id) {
        // 删除角色
        return roleMapper.deleteById(id) > 0;
    }

    /**
     * 批量删除角色
     * 
     * @param ids 角色ID列表
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIds(List<Long> ids) {
        // 批量删除角色
        return roleMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据ID获取角色信息
     * 
     * @param id 角色ID
     * @return 角色信息
     */
    public Role getById(Long id) {
        return roleMapper.selectById(id);
    }
}