package com.sub.baoxian.service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sub.baoxian.common.exception.BizException;
import com.sub.baoxian.mapper.OrganizationMapper;
import com.sub.baoxian.mapper.RoleMapper;
import com.sub.baoxian.mapper.UserMapper;
import com.sub.baoxian.model.dto.UserLoginDTO;
import com.sub.baoxian.model.entity.Organization;
import com.sub.baoxian.model.entity.Role;
import com.sub.baoxian.model.entity.User;
import com.sub.baoxian.model.vo.OrganizationPageUsersListVO;
import com.sub.baoxian.model.vo.UserLoginVO;
import com.sub.baoxian.util.IPUtil;
import com.sub.baoxian.util.StpKit;

import cn.hutool.crypto.digest.BCrypt;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;

/**
 * 用户服务
 */
@Service
@RequiredArgsConstructor
public class UserService {

    private final UserMapper userMapper;

    private final OrganizationMapper organizationMapper;

    private final RoleMapper roleMapper;

    /**
     * 用户注册
     * 
     * @param username 用户名
     * @param password 密码
     * @param email    邮箱
     * @return 注册结果
     */
    public boolean register(String username, String password, String email) {
        // 判断用户名是否已存在
        User existUser = userMapper.selectOne(new LambdaQueryWrapper<User>().eq(User::getUsername, username));
        if (existUser != null) {
            throw new BizException(400, "用户名已存在");
        }

        // 加密密码
        String encodedPassword = BCrypt.hashpw(password);

        // 创建用户对象
        User user = new User();
        user.setUsername(username);
        user.setPassword(encodedPassword);
        user.setEmail(email);
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());

        // 保存用户
        return userMapper.insert(user) > 0;
    }

    /**
     * 用户登录
     * 
     * @param userLoginDTO 用户登录信息
     * @return 登录成功返回用户信息，否则返回null
     */
    public UserLoginVO login(UserLoginDTO userLoginDTO, HttpServletRequest request) {
        User user = null;
        Organization organization = null;
        if (organizationMapper.selectOne(
                new LambdaQueryWrapper<Organization>().eq(Organization::getCode, userLoginDTO.getOrgCode())) == null) {
            throw new BizException("组织代码不存在");
        }
        if (StringUtils.hasText(userLoginDTO.getEmail())) {
            organization = organizationMapper
                    .selectOne(
                            new LambdaQueryWrapper<Organization>().eq(Organization::getCode,
                                    userLoginDTO.getOrgCode()));
            user = userMapper.selectOne(new LambdaQueryWrapper<User>().eq(User::getEmail, userLoginDTO.getEmail())
                    .eq(User::getOrgId, organization.getId()));
            if (user == null) {
                throw new BizException(400, "该组织下用户不存在");
            }
        }

        if (StringUtils.hasText(userLoginDTO.getUsername())) {
            organization = organizationMapper
                    .selectOne(
                            new LambdaQueryWrapper<Organization>().eq(Organization::getCode,
                                    userLoginDTO.getOrgCode()));
            user = userMapper
                    .selectOne(new LambdaQueryWrapper<User>().eq(User::getUsername, userLoginDTO.getUsername())
                            .eq(User::getOrgId, organization.getId()));
            if (user == null) {
                throw new BizException(400, "该组织下用户不存在");
            }
        }

        // 验证密码
        if (user != null) {
            boolean decodePassword = BCrypt.checkpw(userLoginDTO.getPassword(), user.getPassword());
            if (decodePassword) {
                userMapper.update(new LambdaUpdateWrapper<User>()
                        .set(User::getLastLoginTime, LocalDateTime.now())
                        .set(User::getLastLoginIp, IPUtil.getClientIp(request))
                        .eq(User::getId, user.getId()));
                user.setPassword(null);
                UserLoginVO userLoginVO = new UserLoginVO();
                BeanUtils.copyProperties(user, userLoginVO);
                userLoginVO.setOrgName(organization.getName());
                userLoginVO.setRoleName(
                        roleMapper.selectOne(new LambdaQueryWrapper<Role>().eq(Role::getId, user.getRoleId()))
                                .getRoleName());
                StpKit.USER.login(user.getId());
                return userLoginVO;
            }
        }
        throw new BizException(400, "用户名或密码错误");
    }

    public User getUserById(Integer userId) {
        return userMapper.selectById(userId);
    }

    /**
     * 分页查询用户列表
     * 
     * @param pageNum  页码
     * @param pageSize 每页记录数
     * @param keyword  关键字
     * @param roleId   角色ID
     * @param username 用户名
     * @param status   账号状态
     * @return 分页结果
     */
    public Page<OrganizationPageUsersListVO> page(Integer pageNum, Integer pageSize, String keyword, Long roleId,
            String username, Integer status) {
        // 构建查询条件
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();

        // 关键字查询（匹配用户名、姓名、邮箱、手机号）
        if (StringUtils.hasText(keyword)) {
            wrapper.like(User::getUsername, keyword)
                    .or()
                    .like(User::getName, keyword)
                    .or()
                    .like(User::getEmail, keyword)
                    .or()
                    .like(User::getPhone, keyword);
        }

        // 角色查询
        if (roleId != null) {
            wrapper.eq(User::getRoleId, roleId);
        }

        // 用户名查询
        if (StringUtils.hasText(username)) {
            wrapper.eq(User::getUsername, username);
        }

        // 状态查询
        if (status != null) {
            wrapper.eq(User::getStatus, status);
        }

        // 排序
        wrapper.orderByDesc(User::getCreateTime);

        // 分页查询
        Page<User> page = new Page<>(pageNum, pageSize);
        Page<User> result = userMapper.selectPage(page, wrapper);

        // 清除敏感信息
        result.getRecords().forEach(user -> user.setPassword(null));

        // 转换为VO对象
        Page<OrganizationPageUsersListVO> voPage = new Page<>(result.getCurrent(), result.getSize(), result.getTotal());
        List<OrganizationPageUsersListVO> voList = result.getRecords().stream().map(user -> {
            OrganizationPageUsersListVO vo = new OrganizationPageUsersListVO();
            vo.setId(user.getId());
            vo.setRoleName(roleMapper.selectOne(new LambdaQueryWrapper<Role>().eq(Role::getId, user.getRoleId()))
                    .getRoleName());
            vo.setOrgName(organizationMapper.selectOne(new LambdaQueryWrapper<Organization>().eq(Organization::getId,
                    user.getOrgId()))
                    .getName());
            vo.setUsername(user.getUsername());
            vo.setName(user.getName());
            vo.setAvatar(user.getAvatar());
            vo.setPhone(user.getPhone());
            vo.setEmail(user.getEmail());
            vo.setStatus(user.getStatus());
            vo.setLastLoginTime(user.getLastLoginTime());
            vo.setLastLoginIp(user.getLastLoginIp());
            return vo;
        }).collect(Collectors.toList());
        voPage.setRecords(voList);

        return voPage;
    }

    /**
     * 新增用户
     * 
     * @param user 用户信息
     * @return 操作结果
     */
    public boolean save(User user) {
        // 判断用户名是否已存在
        User existUser = userMapper.selectOne(new LambdaQueryWrapper<User>().eq(User::getUsername, user.getUsername()));
        if (existUser != null) {
            throw new BizException(400, "用户名已存在");
        }

        // 判断邮箱是否已存在
        if (StringUtils.hasText(user.getEmail())) {
            User existEmail = userMapper.selectOne(new LambdaQueryWrapper<User>().eq(User::getEmail, user.getEmail()));
            if (existEmail != null) {
                throw new BizException(400, "邮箱已被使用");
            }
        }

        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        user.setCreateTime(now);
        user.setUpdateTime(now);

        // 如果有密码，需要加密
        if (StringUtils.hasText(user.getPassword())) {
            user.setPassword(BCrypt.hashpw(user.getPassword()));
        }

        // 保存用户
        return userMapper.insert(user) > 0;
    }

    /**
     * 更新用户信息
     * 
     * @param user 用户信息
     * @return 操作结果
     */
    public boolean update(User user) {
        // 判断用户是否存在
        User existUser = userMapper.selectById(user.getId());
        if (existUser == null) {
            throw new BizException(400, "用户不存在");
        }

        // 判断用户名是否被其他用户使用
        if (StringUtils.hasText(user.getUsername())) {
            User sameNameUser = userMapper.selectOne(
                    new LambdaQueryWrapper<User>()
                            .eq(User::getUsername, user.getUsername())
                            .ne(User::getId, user.getId()));
            if (sameNameUser != null) {
                throw new BizException(400, "用户名已被其他用户使用");
            }
        }

        // 判断邮箱是否被其他用户使用
        if (StringUtils.hasText(user.getEmail())) {
            User sameEmailUser = userMapper.selectOne(
                    new LambdaQueryWrapper<User>()
                            .eq(User::getEmail, user.getEmail())
                            .ne(User::getId, user.getId()));
            if (sameEmailUser != null) {
                throw new BizException(400, "邮箱已被其他用户使用");
            }
        }

        // 设置更新时间
        user.setUpdateTime(LocalDateTime.now());

        // 如果密码字段不为空，则加密处理
        if (StringUtils.hasText(user.getPassword())) {
            user.setPassword(BCrypt.hashpw(user.getPassword()));
        } else {
            // 密码为空时不更新密码字段
            user.setPassword(null);
        }

        // 更新用户
        return userMapper.updateById(user) > 0;
    }

    /**
     * 删除用户
     * 
     * @param id 用户ID
     * @return 操作结果
     */
    public boolean removeById(Long id) {
        return userMapper.deleteById(id) > 0;
    }

    /**
     * 批量删除用户
     * 
     * @param ids 用户ID列表
     * @return 操作结果
     */
    public boolean removeByIds(List<Long> ids) {
        return userMapper.deleteBatchIds(ids) > 0;
    }

    public Long getRoleIdByUserId(Long userId) {
        return userMapper.selectOne(new LambdaQueryWrapper<User>().eq(User::getId, userId)).getRoleId();
    }
}