package com.sub.baoxian.service.product;

import com.sub.baoxian.mapper.ProductCalMapper;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;

@Service
public class FileImportService {
    @Autowired
    private ProductCalMapper yourMapper; // 替换为你的实际 Mapper 接口名

    private static final String FOLDER_PATH = "E:\\Insurance\\plan\\baoCheng\\saving";

    public void importFileNames() {
        File folder = new File(FOLDER_PATH);
        if (!folder.exists() || !folder.isDirectory()) {
            System.out.println("文件夹不存在或不是目录");
            return;
        }

        for (File file : folder.listFiles()) {
            if (file.isFile()) {
                String fileName = file.getName();
                System.out.println("正在处理文件: " + fileName);
                // 假设你已经知道 age 和 link 的值
                String age = "20"; // 示例值，你可以根据业务逻辑动态赋值
                String productLink = "http://suboga.oss-ap-southeast-1.aliyuncs.com/Insuriam/temPlan/saving_20/baoCheng/" + fileName; // 示例值，也可以是文件路径等

                // 插入数据库
                yourMapper.insertProductLink(fileName, age, productLink,"PRUDENTIAL");
            }
        }

        System.out.println("✅ 文件导入完成！");
    }
}
