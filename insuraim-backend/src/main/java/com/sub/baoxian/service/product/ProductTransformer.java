package com.sub.baoxian.service.product;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.mysql.cj.xdevapi.JsonArray;
import com.sub.baoxian.model.entity.ProductPo;
import com.sub.baoxian.util.ProductFeature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.List;

@Component
public class ProductTransformer {

    private static final ObjectMapper mapper = new ObjectMapper();

    public ObjectNode extractMinMaxAge(String minJson, String maxJson) throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode result = mapper.createObjectNode();

        // 最小年龄处理
        ObjectNode minNode = mapper.createObjectNode();
        JsonNode minArray = mapper.readTree(minJson);
        for (int i = 0; i < minArray.size(); i++) {
            JsonNode item = minArray.get(i);
            String suffix = (i == 0) ? "" : String.valueOf(i + 1);
            minNode.put("restriction" + suffix, item.path("restriction").asText());
            minNode.put("amount" + suffix, item.path("amount").asText());
        }
        result.set("minimumAge", minNode);

        // 最大年龄处理
        ObjectNode maxNode = mapper.createObjectNode();
        JsonNode maxArray = mapper.readTree(maxJson);
        for (int i = 0; i < maxArray.size(); i++) {
            JsonNode item = maxArray.get(i);
            String suffix = (i == 0) ? "" : String.valueOf(i + 1);
            maxNode.put("restriction" + suffix, item.path("restriction").asText());
            maxNode.put("amount" + suffix, item.path("amount").asText());
        }
        result.set("maximumAge", maxNode);

        return result;
    }
    public ObjectNode extractMinMaxSumInsured(String minJson, String maxJson) throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode result = mapper.createObjectNode();

        for (int i = 0; i < 2; i++) {
            ArrayNode arrayNode = mapper.createArrayNode();
            JsonNode jsonArray = (i == 0) ? mapper.readTree(minJson) : mapper.readTree(maxJson);
            if (jsonArray.isArray() && jsonArray.size() > 0) {
                for (JsonNode item : jsonArray) {
                    ObjectNode entry = mapper.createObjectNode();
                    entry.put("restriction", item.path("restriction").asText());

                    JsonNode amountLimitNode = item.path("amount_limit");
                    if (amountLimitNode.isObject()) {
                        entry.set("amount", amountLimitNode);
                    } else {
                        entry.put("amount", "无资料");
                    }
                    arrayNode.add(entry);
                }
            } else {
                ObjectNode entry = mapper.createObjectNode();
                entry.put("restriction", "无资料");
                entry.put("amount", "无资料");
                arrayNode.add(entry);
            }
            result.set((i == 0) ? "minimumSumInsured" : "maximumSumInsured", arrayNode);
        }

        return result;
    }
    //单个JSON格式处理
    public ObjectNode getSingleJson(String level1Json, String level2Json, String level3Json, String allLevelJson) throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode root = mapper.createObjectNode();

        // 读取 3 个等级的数组 JSON 字符串为 ArrayNode
        ArrayNode level1 = mapper.readValue(level1Json, ArrayNode.class);
        ArrayNode level2 = mapper.readValue(level2Json, ArrayNode.class);
        ArrayNode level3 = mapper.readValue(level3Json, ArrayNode.class);

        // 读取 All 层级为 ObjectNode
        ObjectNode allLevel = mapper.readValue(allLevelJson, ObjectNode.class);
        ObjectNode level1ALl= mapper.createObjectNode();
        level1ALl.set("level1Unique",  level1);
        level1ALl.setAll(allLevel);

        ObjectNode level2ALl= mapper.createObjectNode();
        level2ALl.set("level2Unique",  level2);
        level2ALl.setAll(allLevel);

        ObjectNode level3ALl= mapper.createObjectNode();
        level3ALl.set("level3Unique",  level3);
        level3ALl.setAll(allLevel);

        root.set("level1", level1ALl);
        root.set("level2", level2ALl);
        root.set("level3", level3ALl);



        return root;
    }

    // 工具方法：把 ArrayNode 中的 {key: value} 取出扁平合并到 root
    private void mergeLevelArrayToRoot(ArrayNode levelArray, ObjectNode root) {
        for (JsonNode node : levelArray) {
            node.fieldNames().forEachRemaining(key -> {
                root.set(key, node.get(key));
            });
        }
    }
    private String formatBooleanField(Integer value) {
        if (value == null) return "未知";
        return value == 1 ? "適用" : value == 0 ? "不適用" : "未知";
    }


    //产品特点
    public ObjectNode getStringToJson(ProductPo productPo) throws JsonProcessingException {
        ObjectNode result = mapper.createObjectNode();
        ObjectNode productDetails = mapper.createObjectNode();

        //产品的基本信息
        productDetails.put("productName", productPo.getName());
        productDetails.put("productType", productPo.getMainType());
        productDetails.put("productSubType", productPo.getSubType());
        productDetails.put("policyTerm", productPo.getPolicyTerm());
        productDetails.set("ageRestriction",extractMinMaxAge(productPo.getMinimumAge(),productPo.getMaximumAge()));
        productDetails.put("currency", productPo.getPolicyCurrency());
        productDetails.set("fee", extractMinMaxSumInsured(productPo.getMinimumSumInsured(),productPo.getMaximumSumInsured()));
        result.set("product_details", productDetails);

        ObjectNode productRelevance = mapper.createObjectNode();
        //产品相关

        productRelevance.put("medicalUnderwriting", formatBooleanField(productPo.getMedicalUnderwriting()));
        productRelevance.put("financialUnderwriting", formatBooleanField(productPo.getFinancialUnderwriting()));
        productRelevance.put("occupationalUnderwriting", formatBooleanField(productPo.getOccupationalUnderwriting()));
        productRelevance.put("geographicalUnderwriting", formatBooleanField(productPo.getGeographicalUnderwriting()));
        productRelevance.put("noMedicalExamLimit", formatBooleanField(productPo.getNoMedicalExamLimit()));
        productRelevance.put("additionalLoadingApplicable", formatBooleanField(productPo.getAdditionalLoadingApplicable()));
        productRelevance.put("specialDocumentationRequired", formatBooleanField(productPo.getSpecialDocumentationRequired()));
        productRelevance.put("criticalIllnessCoverage1", formatBooleanField(productPo.getCriticalIllnessCoverage1()));
        productRelevance.put("hospitalizationBenefitNonPremiumPlan", formatBooleanField(productPo.getHospitalizationBenefitNonPremiumPlan()));
        productRelevance.put("hospitalizationBenefitPremiumPlan", formatBooleanField(productPo.getHospitalizationBenefitPremiumPlan()));
        productRelevance.put("accidentalBenefit", formatBooleanField(productPo.getAccidentalBenefit()));
        productRelevance.put("premiumWaiverBenefit", formatBooleanField(productPo.getPremiumWaiverBenefit()));
        productRelevance.put("longTermSickLeaveBenefit", formatBooleanField(productPo.getLongTermSickLeaveBenefit()));
        productRelevance.put("outpatientBenefit", formatBooleanField(productPo.getOutpatientBenefit()));
        productRelevance.put("femaleSpecificBenefit", formatBooleanField(productPo.getFemaleSpecificBenefit()));
        productRelevance.put("babyRewardBenefit", formatBooleanField(productPo.getBabyRewardBenefit()));
        productRelevance.put("disabilityBenefit", formatBooleanField(productPo.getDisabilityBenefit()));
        productRelevance.put("increaseSumInsured", formatBooleanField(productPo.getIncreaseSumInsured()));
        productRelevance.put("decreaseSumInsured", formatBooleanField(productPo.getDecreaseSumInsured()));
        productRelevance.put("addBaseCoverage", formatBooleanField(productPo.getAddBaseCoverage()));
        productRelevance.put("reduceBaseCoverage", formatBooleanField(productPo.getReduceBaseCoverage()));
        productRelevance.put("addRider", formatBooleanField(productPo.getAddRider()));
        productRelevance.put("removeRider", formatBooleanField(productPo.getRemoveRider()));
        productRelevance.put("policyLoan", formatBooleanField(productPo.getPolicyLoan()));
        productRelevance.put("renewalPaymentMethod", formatBooleanField(productPo.getRenewalPaymentMethod()));
        productRelevance.put("prepaidPremium", formatBooleanField(productPo.getPrepaidPremium()));
        productRelevance.put("policyReinstatement", formatBooleanField(productPo.getPolicyReinstatement()));
        productRelevance.put("automaticPremiumLoan", formatBooleanField(productPo.getAutomaticPremiumLoan()));
        productRelevance.put("changePlan", formatBooleanField(productPo.getChangePlan()));
        productRelevance.put("reducedPaidUpOption", formatBooleanField(productPo.getReducedPaidUpOption()));
        productRelevance.put("changeInSmokingStatus", formatBooleanField(productPo.getChangeInSmokingStatus()));

        productDetails.set("InsuranceRelevance", productRelevance);
        result.set("product_details", productDetails);
        //产品特点
        if(productPo.getLevel1()!=null) {
            result.set("levels", getSingleJson(productPo.getLevel1(), productPo.getLevel2(), productPo.getLevel3(), productPo.getAllLevels()));
        }

        ObjectNode feature = mapper.createObjectNode();
        ObjectNode productFeatures = mapper.createObjectNode();

        Field[] fields = ProductPo.class.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            if (ProductFeature.contains(field.getName())) {
                try {
                    Object value = field.get(productPo);
                    if(value !=null && !value.toString().isEmpty()) {
                        productFeatures.put(field.getName(), value.toString());
                    } else {
                        productFeatures.put(field.getName(), "无资料");
                    }
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        feature.set("product_features", productFeatures);
        result.set("features", feature);
        return result;
    }


}
