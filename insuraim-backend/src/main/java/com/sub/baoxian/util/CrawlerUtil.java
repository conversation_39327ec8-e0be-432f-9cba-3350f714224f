package com.sub.baoxian.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 爬虫工具类
 * 
 * <p>提供HTTP请求功能封装，用于网页爬取和API调用。该工具类基于OkHttp客户端实现，
 * 支持常见的HTTP请求方法（GET、POST等），并提供了丰富的功能：</p>
 * 
 * <ul>
 *   <li>处理不同类型的请求体（JSON、表单）</li>
 *   <li>自定义请求头设置</li>
 *   <li>代理服务器配置</li>
 *   <li>超时设置</li>
 *   <li>响应结果转换（JSON解析）</li>
 *   <li>自动重试机制</li>
 * </ul>
 * 
 * <p>所有方法均为静态方法，使用前无需实例化。默认已配置合理的超时设置和并发参数，
 * 可根据需要调整。使用示例：</p>
 * 
 * <pre>
 * // 发送简单GET请求
 * String htmlContent = CrawlerUtil.get("https://example.com");
 * 
 * // 发送带参数的POST请求
 * Map&lt;String, Object&gt; jsonData = new HashMap&lt;&gt;();
 * jsonData.put("key", "value");
 * String result = CrawlerUtil.postJson("https://api.example.com/data", jsonData);
 * </pre>
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
public class CrawlerUtil {
    
    /**
     * 默认连接超时时间（毫秒）
     */
    private static final int DEFAULT_CONNECT_TIMEOUT = 120000;
    
    /**
     * 默认读取超时时间（毫秒）
     */
    private static final int DEFAULT_SOCKET_TIMEOUT = 120000;
    
    /**
     * 默认写入超时时间（毫秒）
     */
    private static final int DEFAULT_WRITE_TIMEOUT = 120000;
    
    /**
     * 默认用户代理
     */
    private static final String DEFAULT_USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36";
    
    /**
     * 默认重试次数
     */
    private static final int DEFAULT_RETRY_COUNT = 3;
    
    /**
     * JSON对象映射器
     */
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    
    /**
     * OkHttpClient实例，用于发送HTTP请求
     */
    private static OkHttpClient client;
    
    /**
     * 私有构造方法，防止实例化
     */
    private CrawlerUtil() {
        throw new IllegalStateException("工具类不能实例化");
    }
    
    static {
        // 初始化OkHttpClient
        client = new OkHttpClient.Builder()
            .connectTimeout(DEFAULT_CONNECT_TIMEOUT, TimeUnit.MILLISECONDS)
            .readTimeout(DEFAULT_SOCKET_TIMEOUT, TimeUnit.MILLISECONDS)
            .writeTimeout(DEFAULT_WRITE_TIMEOUT, TimeUnit.MILLISECONDS)
            .retryOnConnectionFailure(true)
            .build();
    }
    
    /**
     * 发送GET请求
     *
     * @param url 请求URL
     * @return 响应字符串
     * @throws IOException 请求异常
     */
    public static String get(String url) throws IOException {
        if (!StringUtils.hasText(url)) {
            throw new IllegalArgumentException("URL不能为空");
        }
        
        Request request = new Request.Builder()
                .url(url)
                .header("User-Agent", DEFAULT_USER_AGENT)
                .build();
        
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("请求失败: " + response);
            }
            return response.body() != null ? response.body().string() : "";
        }
    }
    
    /**
     * 发送带查询参数的GET请求
     *
     * @param url 请求URL
     * @param queryParams 查询参数
     * @return 响应字符串
     * @throws IOException 请求异常
     */
    public static String get(String url, Map<String, Object> queryParams) throws IOException {
        if (!StringUtils.hasText(url)) {
            throw new IllegalArgumentException("URL不能为空");
        }
        
        HttpUrl.Builder urlBuilder = HttpUrl.parse(url).newBuilder();
        
        // 添加查询参数
        if (queryParams != null && !queryParams.isEmpty()) {
            for (Map.Entry<String, Object> entry : queryParams.entrySet()) {
                urlBuilder.addQueryParameter(entry.getKey(), String.valueOf(entry.getValue()));
            }
        }
        
        Request request = new Request.Builder()
                .url(urlBuilder.build())
                .header("User-Agent", DEFAULT_USER_AGENT)
                .build();
        
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("请求失败: " + response);
            }
            return response.body() != null ? response.body().string() : "";
        }
    }
    
    /**
     * 发送带自定义请求头的GET请求
     *
     * @param url 请求URL
     * @param headers 请求头参数
     * @return 响应字符串
     * @throws IOException 请求异常
     */
    public static String getWithHeaders(String url, Map<String, String> headers) throws IOException {
        if (!StringUtils.hasText(url)) {
            throw new IllegalArgumentException("URL不能为空");
        }
        
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .header("User-Agent", DEFAULT_USER_AGENT);
        
        // 添加自定义请求头
        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                requestBuilder.header(entry.getKey(), entry.getValue());
            }
        }
        
        Request request = requestBuilder.build();
        
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("请求失败: " + response);
            }
            return response.body() != null ? response.body().string() : "";
        }
    }
    
    /**
     * 发送带查询参数和自定义请求头的GET请求
     *
     * @param url 请求URL
     * @param queryParams 查询参数
     * @param headers 请求头参数
     * @return 响应字符串
     * @throws IOException 请求异常
     */
    public static String get(String url, Map<String, Object> queryParams, Map<String, String> headers) throws IOException {
        if (!StringUtils.hasText(url)) {
            throw new IllegalArgumentException("URL不能为空");
        }
        
        HttpUrl.Builder urlBuilder = HttpUrl.parse(url).newBuilder();
        
        // 添加查询参数
        if (queryParams != null && !queryParams.isEmpty()) {
            for (Map.Entry<String, Object> entry : queryParams.entrySet()) {
                urlBuilder.addQueryParameter(entry.getKey(), String.valueOf(entry.getValue()));
            }
        }
        
        Request.Builder requestBuilder = new Request.Builder()
                .url(urlBuilder.build())
                .header("User-Agent", DEFAULT_USER_AGENT);
        
        // 添加自定义请求头
        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                requestBuilder.header(entry.getKey(), entry.getValue());
            }
        }
        
        Request request = requestBuilder.build();
        
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("请求失败: " + response);
            }
            return response.body() != null ? response.body().string() : "";
        }
    }
    
    /**
     * 发送POST请求（JSON格式）
     *
     * @param url 请求URL
     * @param jsonBody JSON格式的请求体
     * @return 响应字符串
     * @throws IOException 请求异常
     */
    public static String postJson(String url, Object jsonBody) throws IOException {
        if (!StringUtils.hasText(url)) {
            throw new IllegalArgumentException("URL不能为空");
        }
        
        // 将对象转换为JSON字符串
        String jsonString;
        if (jsonBody instanceof String) {
            jsonString = (String) jsonBody;
        } else {
            try {
                jsonString = OBJECT_MAPPER.writeValueAsString(jsonBody);
            } catch (JsonProcessingException e) {
                throw new IOException("JSON序列化失败", e);
            }
        }
        
        // 创建JSON类型的请求体
        MediaType JSON = MediaType.parse("application/json; charset=utf-8");
        RequestBody body = RequestBody.create(jsonString, JSON);
        
        Request request = new Request.Builder()
                .url(url)
                .header("Content-Type", "application/json")
                .header("User-Agent", DEFAULT_USER_AGENT)
                .post(body)
                .build();
        
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("请求失败: " + response);
            }
            return response.body() != null ? response.body().string() : "";
        }
    }
    
    /**
     * 发送POST请求（表单数据格式）
     *
     * @param url 请求URL
     * @param formParams 表单参数
     * @return 响应字符串
     * @throws IOException 请求异常
     */
    public static String postForm(String url, Map<String, Object> formParams) throws IOException {
        if (!StringUtils.hasText(url)) {
            throw new IllegalArgumentException("URL不能为空");
        }
        
        // 创建表单请求体
        FormBody.Builder formBodyBuilder = new FormBody.Builder();
        
        // 添加表单参数
        if (formParams != null && !formParams.isEmpty()) {
            for (Map.Entry<String, Object> entry : formParams.entrySet()) {
                formBodyBuilder.add(entry.getKey(), String.valueOf(entry.getValue()));
            }
        }
        
        RequestBody formBody = formBodyBuilder.build();
        
        Request request = new Request.Builder()
                .url(url)
                .header("Content-Type", "application/x-www-form-urlencoded")
                .header("User-Agent", DEFAULT_USER_AGENT)
                .post(formBody)
                .build();
        
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("请求失败: " + response);
            }
            return response.body() != null ? response.body().string() : "";
        }
    }
    
    /**
     * 发送POST请求（JSON格式）带自定义请求头
     *
     * @param url 请求URL
     * @param jsonBody JSON格式的请求体
     * @param headers 请求头参数
     * @return 响应字符串
     * @throws IOException 请求异常
     */
    public static String postJsonWithHeaders(String url, Object jsonBody, Map<String, String> headers) throws IOException {
        if (!StringUtils.hasText(url)) {
            throw new IllegalArgumentException("URL不能为空");
        }
        
        // 将对象转换为JSON字符串
        String jsonString;
        if (jsonBody instanceof String) {
            jsonString = (String) jsonBody;
        } else {
            try {
                jsonString = OBJECT_MAPPER.writeValueAsString(jsonBody);
            } catch (JsonProcessingException e) {
                throw new IOException("JSON序列化失败", e);
            }
        }
        
        // 创建JSON类型的请求体
        MediaType JSON = MediaType.parse("application/json; charset=utf-8");
        RequestBody body = RequestBody.create(jsonString, JSON);
        
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .header("Content-Type", "application/json")
                .header("User-Agent", DEFAULT_USER_AGENT)
                .post(body);
        
        // 添加自定义请求头
        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                requestBuilder.header(entry.getKey(), entry.getValue());
            }
        }
        
        Request request = requestBuilder.build();
        
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("请求失败: " + response);
            }
            return response.body() != null ? response.body().string() : "";
        }
    }
    
    /**
     * 设置HTTP代理
     *
     * @param host 代理主机
     * @param port 代理端口
     */
    public static void setProxy(String host, int port) {
        if (!StringUtils.hasText(host) || port <= 0) {
            throw new IllegalArgumentException("代理设置参数无效");
        }
        
        Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(host, port));
        client = client.newBuilder()
                .proxy(proxy)
                .build();
    }
    
    /**
     * 设置HTTP代理（带认证）
     *
     * @param host 代理主机
     * @param port 代理端口
     * @param username 用户名
     * @param password 密码
     */
    public static void setProxy(String host, int port, String username, String password) {
        if (!StringUtils.hasText(host) || port <= 0) {
            throw new IllegalArgumentException("代理设置参数无效");
        }
        
        if (!StringUtils.hasText(username) || !StringUtils.hasText(password)) {
            setProxy(host, port);
            return;
        }
        
        Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(host, port));
        
        Authenticator proxyAuthenticator = (route, response) -> {
            String credential = Credentials.basic(username, password);
            return response.request().newBuilder()
                    .header("Proxy-Authorization", credential)
                    .build();
        };
        
        client = client.newBuilder()
                .proxy(proxy)
                .proxyAuthenticator(proxyAuthenticator)
                .build();
    }
    
    /**
     * 清除代理设置
     */
    public static void clearProxy() {
        client = client.newBuilder()
                .proxy(null)
                .proxyAuthenticator(null)
                .build();
    }
    
    /**
     * 设置全局超时时间
     *
     * @param connectTimeout 连接超时时间（毫秒）
     * @param socketTimeout 读取超时时间（毫秒）
     */
    public static void setTimeout(int connectTimeout, int socketTimeout) {
        client = client.newBuilder()
                .connectTimeout(connectTimeout, TimeUnit.MILLISECONDS)
                .readTimeout(socketTimeout, TimeUnit.MILLISECONDS)
                .writeTimeout(DEFAULT_WRITE_TIMEOUT, TimeUnit.MILLISECONDS)
                .build();
    }
    
    /**
     * 重置为默认超时时间
     */
    public static void resetTimeout() {
        client = client.newBuilder()
                .connectTimeout(DEFAULT_CONNECT_TIMEOUT, TimeUnit.MILLISECONDS)
                .readTimeout(DEFAULT_SOCKET_TIMEOUT, TimeUnit.MILLISECONDS)
                .writeTimeout(DEFAULT_WRITE_TIMEOUT, TimeUnit.MILLISECONDS)
                .build();
    }
    
    /**
     * 将响应字符串转换为JsonNode对象
     *
     * @param jsonString JSON字符串
     * @return JsonNode对象
     * @throws JsonProcessingException JSON处理异常
     */
    public static JsonNode toJsonNode(String jsonString) throws JsonProcessingException {
        if (!StringUtils.hasText(jsonString)) {
            return OBJECT_MAPPER.createObjectNode();
        }
        return OBJECT_MAPPER.readTree(jsonString);
    }
    
    /**
     * 将响应字符串转换为指定类型的对象
     *
     * @param jsonString JSON字符串
     * @param valueType 目标类型
     * @param <T> 泛型类型
     * @return 转换后的对象
     * @throws JsonProcessingException JSON处理异常
     */
    public static <T> T toObject(String jsonString, Class<T> valueType) throws JsonProcessingException {
        if (!StringUtils.hasText(jsonString)) {
            return null;
        }
        return OBJECT_MAPPER.readValue(jsonString, valueType);
    }
    
    /**
     * 带重试机制的请求方法
     *
     * @param requestFunction 请求函数
     * @param retryCount 重试次数
     * @param <T> 返回类型
     * @return 请求结果
     * @throws IOException 请求异常
     */
    public static <T> T retryRequest(Supplier<T> requestFunction, int retryCount) throws IOException {
        int attempts = 0;
        IOException lastException = null;
        
        while (attempts < retryCount) {
            try {
                return requestFunction.get();
            } catch (Exception e) {
                attempts++;
                
                if (e instanceof IOException) {
                    lastException = (IOException) e;
                } else {
                    lastException = new IOException("请求执行异常", e);
                }
                
                log.warn("请求失败，正在进行第{}次重试，异常信息：{}", attempts, e.getMessage());
                
                if (attempts < retryCount) {
                    try {
                        // 指数退避策略
                        Thread.sleep((long) Math.pow(2, attempts) * 1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new IOException("重试过程中线程被中断", ie);
                    }
                }
            }
        }
        
        throw new IOException("请求失败，已重试" + retryCount + "次", lastException);
    }
    
    /**
     * 带重试机制的请求方法（使用默认重试次数）
     *
     * @param requestFunction 请求函数
     * @param <T> 返回类型
     * @return 请求结果
     * @throws IOException 请求异常
     */
    public static <T> T retryRequest(Supplier<T> requestFunction) throws IOException {
        return retryRequest(requestFunction, DEFAULT_RETRY_COUNT);
    }
} 