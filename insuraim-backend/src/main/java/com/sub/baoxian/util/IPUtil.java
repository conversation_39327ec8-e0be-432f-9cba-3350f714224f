package com.sub.baoxian.util;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.util.StringUtils;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * IP地址工具类
 * 用于获取客户端真实IP地址
 */
public class IPUtil {

    /**
     * 未知IP地址标识
     */
    private static final String UNKNOWN = "unknown";
    
    /**
     * 本地IP地址
     */
    private static final String LOCALHOST = "127.0.0.1";
    
    /**
     * 本地IPv6地址
     */
    private static final String LOCALHOST_IPV6 = "0:0:0:0:0:0:0:1";
    
    /**
     * 最大IP长度
     */
    private static final int IP_MAX_LENGTH = 15;

    /**
     * 私有构造方法，防止实例化
     */
    private IPUtil() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 获取客户端IP地址
     * 
     * @param request HTTP请求对象
     * @return 客户端IP地址
     */
    public static String getClientIp(HttpServletRequest request) {
        if (request == null) {
            return UNKNOWN;
        }
        
        String ip = request.getHeader("X-Forwarded-For");
        if (!isUnknownIp(ip) && ip.contains(",")) {
            // 多次反向代理后会有多个IP值，第一个为真实IP
            ip = ip.split(",")[0].trim();
        }
        
        if (isUnknownIp(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        
        if (isUnknownIp(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        
        if (isUnknownIp(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        
        if (isUnknownIp(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        
        if (isUnknownIp(ip)) {
            ip = request.getRemoteAddr();
            if (LOCALHOST.equals(ip) || LOCALHOST_IPV6.equals(ip)) {
                // 根据网卡取本机配置的IP
                try {
                    InetAddress inet = InetAddress.getLocalHost();
                    ip = inet.getHostAddress();
                } catch (UnknownHostException e) {
                    // 忽略异常，返回默认地址
                }
            }
        }
        
        return ip;
    }
    
    /**
     * 判断IP地址是否未知或无效
     * 
     * @param ip IP地址
     * @return 是否未知或无效
     */
    private static boolean isUnknownIp(String ip) {
        return !StringUtils.hasText(ip) || UNKNOWN.equalsIgnoreCase(ip);
    }
    
    /**
     * 校验IP地址有效性
     * 
     * @param ip IP地址
     * @return 是否有效
     */
    public static boolean isValidIp(String ip) {
        if (!StringUtils.hasText(ip)) {
            return false;
        }
        
        // 判断是否是本地IP
        if (LOCALHOST.equals(ip) || LOCALHOST_IPV6.equals(ip)) {
            return true;
        }
        
        // 判断IP地址长度
        if (ip.length() > IP_MAX_LENGTH) {
            return false;
        }
        
        // 简单IPv4检查，检查是否是4段数字，每段是否在0-255之间
        String[] segments = ip.split("\\.");
        if (segments.length != 4) {
            return false;
        }
        
        for (String segment : segments) {
            try {
                int value = Integer.parseInt(segment);
                if (value < 0 || value > 255) {
                    return false;
                }
            } catch (NumberFormatException e) {
                return false;
            }
        }
        
        return true;
    }
}