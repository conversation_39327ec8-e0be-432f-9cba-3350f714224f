package com.sub.baoxian.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.model.*;
import com.sub.baoxian.common.config.OssConfig;
import com.sub.baoxian.common.exception.BizException;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 阿里云OSS工具类
 */
@Slf4j
@Component
public class OssUtil {

    private static OSS ossClient;
    private static OssConfig ossConfig;

    @Autowired
    public void setOssClient(OSS ossClient) {
        OssUtil.ossClient = ossClient;
    }

    @Autowired
    public void setOssConfig(OssConfig ossConfig) {
        OssUtil.ossConfig = ossConfig;
    }

    /**
     * 上传文件（自定义文件名）
     * 
     * @param file     文件对象
     * @param fileName 自定义文件名
     * @return 文件访问路径
     */
    public static String uploadFileWithCustomName(MultipartFile file, String fileName) {
        try {
            return uploadInputStream(file.getInputStream(), ossConfig.getDir(), fileName);
        } catch (Exception e) {
            log.error("上传文件失败", e);
            throw new BizException("上传文件失败: " + e.getMessage());
        }
    }

    /**
     * 上传文件
     *
     * @param file     文件对象
     * @param dir      文件存储目录
     * @param fileName 文件名
     * @return 文件访问路径
     */
    public static String uploadFile(MultipartFile file, String dir, String fileName) {
        try {
            return uploadInputStream(file.getInputStream(), dir, fileName);
        } catch (Exception e) {
            log.error("上传文件失败", e);
            throw new BizException("上传文件失败: " + e.getMessage());
        }
    }

    /**
     * 上传文件（自动生成文件名）
     *
     * @param file 文件对象
     * @param dir  文件存储目录
     * @return 文件访问路径
     */
    public static String uploadFile(MultipartFile file, String dir) {
        String originalFilename = file.getOriginalFilename();
        String suffix = FileUtil.extName(originalFilename);
        String fileName = generateFileName(suffix);
        return uploadFile(file, dir, fileName);
    }

    /**
     * 上传文件（使用默认目录）
     *
     * @param file 文件对象
     * @return 文件访问路径
     */
    public static String uploadFile(MultipartFile file) {
        return uploadFile(file, ossConfig.getDir());
    }

    /**
     * 上传本地文件
     *
     * @param localFilePath 本地文件路径
     * @param dir           OSS存储目录
     * @param fileName      文件名
     * @return 文件访问路径
     */
    public static String uploadLocalFile(String localFilePath, String dir, String fileName) {
        try {
            return uploadInputStream(new FileInputStream(localFilePath), dir, fileName);
        } catch (Exception e) {
            log.error("上传本地文件失败", e);
            throw new BizException("上传本地文件失败: " + e.getMessage());
        }
    }

    /**
     * 上传本地文件（自动生成文件名）
     *
     * @param localFilePath 本地文件路径
     * @param dir           OSS存储目录
     * @return 文件访问路径
     */
    public static String uploadLocalFile(String localFilePath, String dir) {
        String suffix = FileUtil.extName(localFilePath);
        String fileName = generateFileName(suffix);
        return uploadLocalFile(localFilePath, dir, fileName);
    }

    /**
     * 上传本地文件（使用默认目录）
     *
     * @param localFilePath 本地文件路径
     * @return 文件访问路径
     */
    public static String uploadLocalFile(String localFilePath) {
        return uploadLocalFile(localFilePath, ossConfig.getDir());
    }

    /**
     * 上传字节数组
     *
     * @param data     字节数组
     * @param dir      OSS存储目录
     * @param fileName 文件名
     * @return 文件访问路径
     */
    public static String uploadBytes(byte[] data, String dir, String fileName) {
        try {
            return uploadInputStream(new ByteArrayInputStream(data), dir, fileName);
        } catch (Exception e) {
            log.error("上传字节数组失败", e);
            throw new BizException("上传字节数组失败: " + e.getMessage());
        }
    }

    /**
     * 上传输入流
     *
     * @param inputStream 输入流
     * @param dir         OSS存储目录
     * @param fileName    文件名
     * @return 文件访问路径
     */
    public static String uploadInputStream(InputStream inputStream, String dir, String fileName) {
        String objectName = getObjectName(dir, fileName);

        try {
            PutObjectRequest putObjectRequest = new PutObjectRequest(ossConfig.getBucketName(), objectName,
                    inputStream);

            // 上传文件
            ossClient.putObject(putObjectRequest);

            // 返回文件访问路径
            return getFileUrl(objectName);
        } catch (Exception e) {
            log.error("上传文件失败", e);
            throw new BizException("上传文件失败: " + e.getMessage());
        }
    }

    /**
     * 下载文件到本地
     *
     * @param objectName    对象名称
     * @param localFilePath 本地文件路径
     */
    public static void downloadFile(String objectName, String localFilePath) {
        try {
            // 创建下载请求
            GetObjectRequest getObjectRequest = new GetObjectRequest(ossConfig.getBucketName(), objectName);

            // 下载文件
            ossClient.getObject(getObjectRequest, new File(localFilePath));
        } catch (Exception e) {
            log.error("下载文件失败", e);
            throw new BizException("下载文件失败: " + e.getMessage());
        }
    }

    /**
     * 下载文件到字节数组
     *
     * @param objectName 对象名称
     * @return 字节数组
     */
    public static byte[] downloadToBytes(String objectName) {
        try {
            // 获取文件
            OSSObject ossObject = ossClient.getObject(ossConfig.getBucketName(), objectName);

            // 读取输入流到字节数组
            try (InputStream inputStream = ossObject.getObjectContent()) {
                return inputStream.readAllBytes();
            }
        } catch (Exception e) {
            log.error("下载文件到字节数组失败", e);
            throw new BizException("下载文件到字节数组失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件输入流
     *
     * @param objectName 对象名称
     * @return 输入流
     */
    public static InputStream getFileInputStream(String objectName) {
        try {
            OSSObject ossObject = ossClient.getObject(ossConfig.getBucketName(), objectName);
            return ossObject.getObjectContent();
        } catch (Exception e) {
            log.error("获取文件输入流失败", e);
            throw new BizException("获取文件输入流失败: " + e.getMessage());
        }
    }

    /**
     * 删除文件
     *
     * @param objectName 对象名称
     * @return 是否删除成功
     */
    public static boolean deleteFile(String objectName) {
        try {
            ossClient.deleteObject(ossConfig.getBucketName(), objectName);
            return true;
        } catch (Exception e) {
            log.error("删除文件失败", e);
            return false;
        }
    }

    /**
     * 批量删除文件
     *
     * @param objectNames 对象名称列表
     * @return 是否删除成功
     */
    public static boolean deleteFiles(List<String> objectNames) {
        try {
            DeleteObjectsRequest deleteObjectsRequest = new DeleteObjectsRequest(ossConfig.getBucketName());
            deleteObjectsRequest.setKeys(objectNames);
            ossClient.deleteObjects(deleteObjectsRequest);
            return true;
        } catch (Exception e) {
            log.error("批量删除文件失败", e);
            return false;
        }
    }

    /**
     * 判断文件是否存在
     *
     * @param objectName 对象名称
     * @return 是否存在
     */
    public static boolean doesObjectExist(String objectName) {
        try {
            return ossClient.doesObjectExist(ossConfig.getBucketName(), objectName);
        } catch (Exception e) {
            log.error("判断文件是否存在失败", e);
            return false;
        }
    }

    /**
     * 获取文件URL
     *
     * @param objectName 对象名称
     * @return 文件URL
     */
    public static String getFileUrl(String objectName) {
        return ossConfig.getUrlPrefix() + objectName;
    }

    /**
     * 获取私有文件临时访问URL
     *
     * @param objectName 对象名称
     * @return 临时访问URL
     */
    public static String getPrivateFileUrl(String objectName) {
        try {
            // 设置过期时间
            Date expiration = DateUtil.offsetSecond(new Date(), ossConfig.getExpireTime().intValue());

            // 生成临时访问URL
            URL url = ossClient.generatePresignedUrl(ossConfig.getBucketName(), objectName, expiration);

            return url.toString();
        } catch (Exception e) {
            log.error("获取私有文件临时访问URL失败", e);
            throw new BizException("获取私有文件临时访问URL失败: " + e.getMessage());
        }
    }

    /**
     * 列出指定前缀的文件
     *
     * @param prefix 前缀
     * @return 文件列表
     */
    public static List<String> listFiles(String prefix) {
        List<String> fileList = new ArrayList<>();
        try {
            ListObjectsRequest request = new ListObjectsRequest(ossConfig.getBucketName());
            request.setPrefix(prefix);

            ObjectListing objectListing;
            do {
                objectListing = ossClient.listObjects(request);

                for (OSSObjectSummary objectSummary : objectListing.getObjectSummaries()) {
                    fileList.add(objectSummary.getKey());
                }

                request.setMarker(objectListing.getNextMarker());
            } while (objectListing.isTruncated());

            return fileList;
        } catch (Exception e) {
            log.error("列出文件失败", e);
            throw new BizException("列出文件失败: " + e.getMessage());
        }
    }

    /**
     * 生成随机文件名
     *
     * @param suffix 文件后缀
     * @return 文件名
     */
    private static String generateFileName(String suffix) {
        // 使用UUID生成随机文件名，避免文件名冲突
        String uuid = IdUtil.fastSimpleUUID();
        // 加上日期前缀，方便管理
        String datePrefix = DateUtil.format(new Date(), "yyyyMMdd");

        return datePrefix + "_" + uuid + (StrUtil.isNotBlank(suffix) ? "." + suffix : "");
    }

    /**
     * 获取对象名称
     *
     * @param dir      文件存储目录
     * @param fileName 文件名
     * @return 对象名称
     */
    private static String getObjectName(String dir, String fileName) {
        // 确保目录以斜杠结尾
        if (StrUtil.isNotBlank(dir) && !dir.endsWith("/")) {
            dir = dir + "/";
        }

        return dir + fileName;
    }

    /**
     * 从完整的OSS URL中提取对象名
     * 
     * @param ossUrl OSS完整URL
     * @return 对象名（包括路径）
     */
    public static String extractObjectNameFromUrl(String ossUrl) {
        // 简单规则：通常OSS URL格式为https://bucket.endpoint/objectName
        // 我们尝试提取最后一部分作为objectName

        if (ossUrl == null || ossUrl.isEmpty()) {
            return "";
        }

        // 移除查询参数
        if (ossUrl.contains("?")) {
            ossUrl = ossUrl.substring(0, ossUrl.indexOf("?"));
        }

        // 如果URL包含协议，如http://或https://，移除该部分
        if (ossUrl.contains("://")) {
            ossUrl = ossUrl.substring(ossUrl.indexOf("://") + 3);
        }

        // 移除域名部分 (第一个斜杠之前的部分)
        if (ossUrl.contains("/")) {
            ossUrl = ossUrl.substring(ossUrl.indexOf("/") + 1);
        }

        return ossUrl;
    }
}