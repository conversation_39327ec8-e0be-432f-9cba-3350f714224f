package com.sub.baoxian.util;

import java.util.Set;

public class ProductFeature {
    private static final Set<String> FIELD_NAMES = Set.of(
            "productFeatures",
            "targetAudience",
            "premiumRate",
            "nonGuaranteedDividend",
            "terminalDividend",
            "terminalDividendNonGuarantee",
            "terminalDividendManagementBenefit",
            "guaranteedMonthlyAnnuityIncome",
            "nonGuaranteedMonthlyAnnuityIncome",
            "highCoverageDiscount",
            "specialDiseaseCoverage",
            "premiumHoliday",
            "policySplitRights",
            "currencyConversionRights",
            "additionalCriticalIllnessCoverageForChildren",
            "earlyStageIllnessCoverage",
            "criticalIllnessCoverage",
            "waiverOfPremiumOnCriticalIllnessBenefit",
            "additionalCriticalIllnessCoverage",
            "multipleCriticalIllnessCoverage",
            "familySharedCoverage",
            "allRoundProtectionPlan",
            "doubleIndemnityBenefit",
            "compassionateDeathBenefit",
            "additionalAllRoundProtectionPlan",
            "medicalExpensesCoverage",
            "rehabilitationSupportServices",
            "additionalHospitalCashBenefit",
            "hospitalSurgeryReimbursement",
            "noLifetimeLimitAndUnknownPreexistingCovered",
            "additionalDeathBenefit",
            "accidentalBurnBenefit",
            "flexibleDeathAndAccidentalPayoutMethod",
            "accidentalDeathBenefit",
            "cancerCoverage",
            "accidentalTotalAndPermanentDisability",
            "accidentalDeathAndDismembermentBenefit",
            "accidentalTpdWaiverOfPremium",
            "suddenDeathCoverage",
            "changeOfInsuredPerson",
            "substituteInsuredPerson",
            "parentalWaiverBenefit",
            "spousalWaiverBenefit",
            "extendedCoverageForChildren",
            "policyTermination",
            "otherBenefits",
            "specialRemarks",
            "policyReverseMortgageLoan",
            "rateTable",
            "productFactsheet",
            "benefitProvisions",
            "additionalPremium",
            "supportingDocuments",
            "waiverOfPremium",
            "planRelated",
            "training"
    );
    public static boolean contains(String field) {
        return FIELD_NAMES.contains(field);
    }
}
