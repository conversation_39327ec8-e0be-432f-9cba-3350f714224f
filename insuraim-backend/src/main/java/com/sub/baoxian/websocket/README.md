# WebSocket模块

本模块提供基于STOMP协议的WebSocket实时消息通知功能。

## 目录结构

```
websocket/
├── config/                 # WebSocket配置
│   ├── WebSocketAuthChannelInterceptor.java  # WebSocket认证拦截器
│   └── WebSocketConfig.java                  # WebSocket配置类
├── handler/                # WebSocket处理器
│   └── WebSocketEventListener.java           # WebSocket事件监听器
├── model/                  # WebSocket数据模型
│   └── WebSocketMessage.java                 # WebSocket消息实体类
├── service/                # WebSocket服务
│   └── WebSocketService.java                 # WebSocket消息服务
└── README.md               # 说明文档
```

## 功能说明

**STOMP协议WebSocket**：基于SockJS和STOMP协议的WebSocket实现
- 端点：`/ws`
- 个人消息订阅：`/user/queue/messages`
- 广播消息订阅：`/topic/broadcast`

## 使用方法

### 服务端发送消息

通过`MessageService`发送消息：

```java
// 注入MessageService
@Autowired
private MessageService messageService;

// 发送个人消息
messageService.sendMessageToUser(
    userId,          // 用户ID
    "消息标题",       // 消息标题
    "消息内容",       // 消息内容
    MessageType.SYSTEM // 消息类型：SYSTEM或BUSINESS
);

// 发送广播消息
messageService.sendBroadcastMessage(
    "广播标题",       // 广播标题
    "广播内容",       // 广播内容
    MessageType.SYSTEM // 消息类型：SYSTEM或BUSINESS
);
```

或者直接使用`WebSocketService`：

```java
// 注入WebSocketService
@Autowired
private WebSocketService webSocketService;

// 发送个人消息
webSocketService.sendMessageToUser(userId, messageObject);

// 发送广播消息
webSocketService.sendBroadcast(messageObject);
```

### 客户端示例

详细的客户端示例代码请参考：
- STOMP协议客户端示例：`src/main/resources/docs/websocket/websocket-client-example.js`
- 使用文档：`src/main/resources/docs/websocket/websocket-usage.md` 