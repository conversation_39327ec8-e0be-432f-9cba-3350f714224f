package com.sub.baoxian.websocket.config;

import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.simp.stomp.StompCommand;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.messaging.support.ChannelInterceptor;
import org.springframework.messaging.support.MessageHeaderAccessor;
import org.springframework.stereotype.Component;

import com.sub.baoxian.util.StpKit;

import lombok.extern.slf4j.Slf4j;

/**
 * WebSocket认证拦截器
 * 用于验证WebSocket连接的用户身份
 */
@Slf4j
@Component
public class WebSocketAuthChannelInterceptor implements ChannelInterceptor {

    @Override
    public Message<?> preSend(Message<?> message, MessageChannel channel) {
        StompHeaderAccessor accessor = MessageHeaderAccessor.getAccessor(message, StompHeaderAccessor.class);
        
        if (accessor != null && StompCommand.CONNECT.equals(accessor.getCommand())) {
            // 获取请求头中的token
            String token = accessor.getFirstNativeHeader("Authorization");
            log.debug("WebSocket连接收到的Authorization: {}", token);
            
            if (token != null) {
                // 如果token以"Bearer "开头，则去除前缀
                if (token.startsWith("Bearer ")) {
                    token = token.substring(7);
                }
                
                // 验证token
                try {
                    // 直接使用token进行登录验证
                    if (StpKit.USER.getLoginIdByToken(token) != null) {
                        Object loginId = StpKit.USER.getLoginIdByToken(token);
                        
                        // 将用户ID放入WebSocket会话属性中
                        accessor.setUser(() -> loginId.toString());
                        
                        log.debug("WebSocket连接认证成功: {}", loginId);
                        return message;
                    }
                } catch (Exception e) {
                    log.warn("WebSocket连接认证异常: {}", e.getMessage());
                }
            }
            
            log.warn("WebSocket连接认证失败");
            return null; // 阻止连接
        }
        
        return message;
    }
} 