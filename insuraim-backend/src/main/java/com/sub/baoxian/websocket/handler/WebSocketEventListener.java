package com.sub.baoxian.websocket.handler;

import org.springframework.context.event.EventListener;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.messaging.SessionConnectedEvent;
import org.springframework.web.socket.messaging.SessionDisconnectEvent;

import lombok.extern.slf4j.Slf4j;

/**
 * WebSocket事件监听器
 * 用于监听WebSocket连接和断开事件
 */
@Slf4j
@Component
public class WebSocketEventListener {

    /**
     * 处理WebSocket连接事件
     * 
     * @param event 连接事件
     */
    @EventListener
    public void handleWebSocketConnectListener(SessionConnectedEvent event) {
        StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
        String sessionId = headerAccessor.getSessionId();
        Object user = headerAccessor.getUser();
        log.info("WebSocket会话已连接: {}, 用户: {}", sessionId, user != null ? user.toString() : "未认证");
    }

    /**
     * 处理WebSocket断开事件
     * 
     * @param event 断开事件
     */
    @EventListener
    public void handleWebSocketDisconnectListener(SessionDisconnectEvent event) {
        StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
        String sessionId = headerAccessor.getSessionId();
        Object user = headerAccessor.getUser();
        log.info("WebSocket会话已断开: {}, 用户: {}", sessionId, user != null ? user.toString() : "未认证");
    }
}