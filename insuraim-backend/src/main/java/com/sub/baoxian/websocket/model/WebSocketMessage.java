package com.sub.baoxian.websocket.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * WebSocket消息实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WebSocketMessage {
    
    /**
     * 消息类型
     */
    private String type;
    
    /**
     * 消息内容
     */
    private Object payload;
    
    /**
     * 时间戳
     */
    private Long timestamp;
    
    /**
     * 创建WebSocket消息
     * 
     * @param type 消息类型
     * @param payload 消息内容
     * @return WebSocket消息
     */
    public static WebSocketMessage create(String type, Object payload) {
        return WebSocketMessage.builder()
                .type(type)
                .payload(payload)
                .timestamp(System.currentTimeMillis())
                .build();
    }
} 