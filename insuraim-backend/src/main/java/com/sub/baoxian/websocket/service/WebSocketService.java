package com.sub.baoxian.websocket.service;

import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import com.sub.baoxian.model.entity.Message;
import com.sub.baoxian.websocket.model.WebSocketMessage;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * WebSocket消息服务
 * 用于向客户端推送消息
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WebSocketService {

    private final SimpMessagingTemplate messagingTemplate;

    /**
     * 发送消息给特定用户
     * 
     * @param userId  用户ID
     * @param message 消息实体
     */
    public void sendMessageToUser(Long userId, Message message) {
        WebSocketMessage webSocketMessage = WebSocketMessage.create("USER_MESSAGE", message);
        messagingTemplate.convertAndSendToUser(
                userId.toString(),
                "/queue/messages",
                webSocketMessage);

        log.debug("已通过WebSocket发送消息给用户 {}: {}", userId, message.getTitle());
    }

    /**
     * 发送系统广播消息
     * 
     * @param message 广播消息内容
     */
    public void sendBroadcast(Object message) {
        WebSocketMessage webSocketMessage = WebSocketMessage.create("BROADCAST", message);
        messagingTemplate.convertAndSend("/topic/broadcast", webSocketMessage);

        log.debug("已发送广播消息: {}", message);
    }
}