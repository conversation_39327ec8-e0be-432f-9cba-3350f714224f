spring:
  application:
    name: baoxian
  servlet:
    multipart:
      enabled: true
      max-file-size: 100MB
      max-request-size: 100MB
      file-size-threshold: 2KB
  datasource:
    url: ***************************************************************************************************************************
    username: root
    password: Wechat73928487@123
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: true
      test-on-return: false
      # 保持连接池中的连接存活
      keep-alive: true
      # 启用PSCache，并指定每个连接上PSCache的大小
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
  data:
    redis:
      host: 127.0.0.1
      port: 6379
      password: 123456
      database: 4
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms
  cache:
    type: redis
    redis:
      time-to-live: 3600000

mybatis-plus:
  mapper-locations: classpath*:mapper/*.xml,classpath*:com/sub/baoxian/mapper/*.xml
  type-aliases-package: com.sub.baoxian.model.vo,com.sub.baoxian.model.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl



sa-token:
    # token 名称（同时也是 cookie 名称）
    token-name: Authorization
    # token 前缀
    token-prefix: Bearer
    # token 有效期（单位：秒） 默认30天，-1 代表永久有效
    timeout: 86400
    # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
    active-timeout: -1
    # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
    is-concurrent: true
    # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
    is-share: true
    # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
    token-style: uuid
    # 是否输出操作日志
    is-log: true

logging:
  charset:
    console: UTF-8
    file: UTF-8

openai:
  apiKey: sk-urdlcxuoyhgtxqruemzdogsahymqtncbnkljnwljbrkuzifv
  baseUrl: https://api.siliconflow.cn/v1
  model: deepseek-ai/DeepSeek-V3
  
# 阿里云OSS配置
aliyun:
  oss:
    endpoint: oss-ap-southeast-1.aliyuncs.com  # OSS服务的访问域名
    accessKeyId: LTAI5tLiHh9ar6U7Ejodt3fU         # 访问身份验证中用到的AccessKey ID
    accessKeySecret: ****************************** # 访问身份验证中用到的AccessKey Secret
    bucketName: suboga           # OSS的存储空间名称
    urlPrefix: https://resouce.insuraim.com/  # 访问文件的URL前缀
    maxSize: 100                             # 上传文件大小限制，单位MB
    dir: Insuriam/files               # 默认存储目录
    expireTime: 1800                        # 临时URL过期时间，单位秒
