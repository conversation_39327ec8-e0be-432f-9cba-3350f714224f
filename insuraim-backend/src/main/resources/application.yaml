spring:
  application:
    name: baoxian
  profiles:
    active: dev


# Knife4j配置
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: 'policy-management'
      paths-to-match: '/**'
      packages-to-scan: com.sub.baoxian.policymanagement.controller

#sql打印
logging:
  level:
    com.sub.baoxian.mapper: debug  # 你的 Mapper 包路径
    org.mybatis: debug             # MyBatis 执行过程
    org.apache.ibatis: debug       # 可选，更详细的 SQL 打印
