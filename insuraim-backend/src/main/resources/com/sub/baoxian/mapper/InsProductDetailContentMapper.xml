<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sub.baoxian.mapper.InsProductDetailContentMapper">

    <!-- 结果映射 -->
    <resultMap id="ContentVOResultMap" type="com.sub.baoxian.model.vo.InsProductDetailContentVO">
        <id column="id" property="id"/>
        <result column="product_id" property="productId"/>
        <result column="title_id" property="titleId"/>
        <result column="title_name" property="titleName"/>
        <result column="content_value" property="contentValue"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <!-- 根据产品ID查询内容列表 -->
    <select id="selectByProductId" resultMap="ContentVOResultMap">
        SELECT 
            c.id, 
            c.product_id, 
            c.title_id, 
            t.title_name,
            c.content_value, 
            c.created_at, 
            c.updated_at
        FROM ins_product_detail_contents c
        LEFT JOIN ins_product_detail_titles t ON c.title_id = t.id
        WHERE c.product_id = #{productId}
        ORDER BY t.rank_value ASC
    </select>

    <!-- 根据标题ID查询内容列表 -->
    <select id="selectByTitleId" resultMap="ContentVOResultMap">
        SELECT 
            c.id, 
            c.product_id, 
            c.title_id, 
            t.title_name,
            c.content_value, 
            c.created_at, 
            c.updated_at
        FROM ins_product_detail_contents c
        LEFT JOIN ins_product_detail_titles t ON c.title_id = t.id
        WHERE c.title_id = #{titleId}
        ORDER BY c.created_at ASC
    </select>

    <!-- 根据产品ID和标题ID查询内容 -->
    <select id="selectByProductIdAndTitleId" resultMap="ContentVOResultMap">
        SELECT 
            c.id, 
            c.product_id, 
            c.title_id, 
            t.title_name,
            c.content_value, 
            c.created_at, 
            c.updated_at
        FROM ins_product_detail_contents c
        LEFT JOIN ins_product_detail_titles t ON c.title_id = t.id
        WHERE c.product_id = #{productId} AND c.title_id = #{titleId}
    </select>

    <!-- 分页查询内容列表 -->
    <select id="selectContentPage" resultMap="ContentVOResultMap">
        SELECT 
            c.id, 
            c.product_id, 
            c.title_id, 
            t.title_name,
            c.content_value, 
            c.created_at, 
            c.updated_at
        FROM ins_product_detail_contents c
        LEFT JOIN ins_product_detail_titles t ON c.title_id = t.id
        <where>
            <if test="productId != null">
                AND c.product_id = #{productId}
            </if>
            <if test="titleId != null">
                AND c.title_id = #{titleId}
            </if>
            <if test="contentValue != null and contentValue != ''">
                AND c.content_value LIKE CONCAT('%', #{contentValue}, '%')
            </if>
        </where>
        ORDER BY t.rank_value ASC, c.created_at ASC
    </select>

    <!-- 批量查询多个产品的内容 -->
    <select id="selectByProductIdsAndTitleIds" resultMap="ContentVOResultMap">
        SELECT 
            c.id, 
            c.product_id, 
            c.title_id, 
            t.title_name,
            c.content_value, 
            c.created_at, 
            c.updated_at
        FROM ins_product_detail_contents c
        LEFT JOIN ins_product_detail_titles t ON c.title_id = t.id
        <where>
            <if test="productIds != null and productIds.size() > 0">
                AND c.product_id IN
                <foreach collection="productIds" item="productId" open="(" separator="," close=")">
                    #{productId}
                </foreach>
            </if>
            <if test="titleIds != null and titleIds.size() > 0">
                AND c.title_id IN
                <foreach collection="titleIds" item="titleId" open="(" separator="," close=")">
                    #{titleId}
                </foreach>
            </if>
        </where>
        ORDER BY c.product_id ASC, t.rank_value ASC
    </select>

</mapper>
