<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sub.baoxian.mapper.InsProductDetailTitleMapper">

    <!-- 结果映射 -->
    <resultMap id="TitleVOResultMap" type="com.sub.baoxian.model.vo.InsProductDetailTitleVO">
        <id column="id" property="id"/>
        <result column="category_id" property="categoryId"/>
        <result column="parent_id" property="parentId"/>
        <result column="title_name" property="titleName"/>
        <result column="rank_value" property="rankValue"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <!-- 根据分类ID查询标题列表 -->
    <select id="selectByCategoryId" resultMap="TitleVOResultMap">
        SELECT
            id,
            category_id,
            parent_id,
            title_name,
            rank_value,
            created_at,
            updated_at
        FROM ins_product_detail_titles
        WHERE category_id = #{categoryId}
        ORDER BY rank_value ASC
    </select>

    <!-- 根据父标题ID查询子标题列表 -->
    <select id="selectByParentId" resultMap="TitleVOResultMap">
        SELECT
            id,
            category_id,
            parent_id,
            title_name,
            rank_value,
            created_at,
            updated_at
        FROM ins_product_detail_titles
        WHERE parent_id = #{parentId}
        ORDER BY rank_value ASC
    </select>

    <!-- 分页查询标题列表 -->
    <select id="selectTitlePage" resultMap="TitleVOResultMap">
        SELECT
            id,
            category_id,
            parent_id,
            title_name,
            rank_value,
            created_at,
            updated_at
        FROM ins_product_detail_titles
        <where>
            <if test="categoryId != null">
                AND category_id = #{categoryId}
            </if>
            <if test="parentId != null">
                AND parent_id = #{parentId}
            </if>
            <if test="titleName != null and titleName != ''">
                AND title_name LIKE CONCAT('%', #{titleName}, '%')
            </if>
        </where>
        ORDER BY rank_value ASC
    </select>

    <!-- 获取同级标题的最大排序值 -->
    <select id="getMaxRank" resultType="java.lang.Integer">
        SELECT MAX(rank_value)
        FROM ins_product_detail_titles
        WHERE category_id = #{categoryId}
        <if test="parentId != null">
            AND parent_id = #{parentId}
        </if>
        <if test="parentId == null">
            AND parent_id IS NULL
        </if>
    </select>

</mapper>
