<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sub.baoxian.mapper.InsuranceNewsMapper">
    <sql id="Base_Column_List">
        title, content, img, link, create_time, pub_date, tags, status, popular, group_hash, `source`
    </sql>

    <select id="getInsuranceNews" parameterType="java.lang.String"
            resultType="com.sub.baoxian.model.entity.InsuranceNews">
        SELECT id, <include refid="Base_Column_List"></include>
        FROM insurance_news
        WHERE group_hash = #{groupHash}
    </select>

    <select id="getInsuranceNewsList" parameterType="com.sub.baoxian.model.entity.InsuranceNews"
            resultType="com.sub.baoxian.model.entity.InsuranceNews">
        SELECT id, <include refid="Base_Column_List"></include>
        FROM insurance_news
        <where>
            <if test="title != null and title != ''"> AND title LIKE CONCAT('%', #{title}, '%')</if>
            <if test="popular != null">AND popular = #{popular}</if>
            <if test="img != null">AND img IS NOT NULL</if>
            <if test="tags != null">AND tags LIKE CONCAT('%', #{tags}, '%')</if>
        </where>
    </select>

    <insert id="batchInsuranceNews">
        INSERT INTO insurance_news (
            <include refid="Base_Column_List"></include>
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.title}, #{item.content}, #{item.img}, #{item.link}, #{item.createTime},
             #{item.pubDate}, #{item.tags}, #{item.status}, #{item.popular}, #{item.groupHash}, #{item.source})
        </foreach>
    </insert>

    <update id="updateInsuranceNews">
        UPDATE insurance_news
        <set>
            <if test="tags != null">tags = #{tags}</if>
        </set>
        WHERE id = #{id}
    </update>

</mapper>
