<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sub.baoxian.mapper.KnowledgeBaseMapper">

    
    <!-- 根据分类ID获取知识库文章列表 -->
    <select id="getLoreListByCategoryId" parameterType="java.lang.Long" resultType="com.sub.baoxian.model.entity.KnowledgeBase">
        SELECT 
            id,
            title,
            category,
            status,
            created_at AS createdAt,
            updated_at AS updatedAt,
            author_id AS authorId,
            summary,
            views,
            tags,
            category_id AS categoryId,
            type
        FROM 
            knowledge_base
        WHERE 
            category_id = #{categoryId}
            AND status = 1
        ORDER BY
            id ASC
    </select>
    
   
    <!-- 根据文章ID获取文章详情 -->
    <resultMap id="KnowledgeBaseWithAttachmentsMap" type="com.sub.baoxian.model.bo.getKnowledgeBaseByIdBO">
        <association property="knowledgeBase" javaType="com.sub.baoxian.model.entity.KnowledgeBase">
            <id column="id" property="id"/>
            <result column="title" property="title"/>
            <result column="content" property="content"/>
            <result column="category" property="category"/>
            <result column="status" property="status"/>
            <result column="created_at" property="createdAt"/>
            <result column="updated_at" property="updatedAt"/>
            <result column="author_id" property="authorId"/>
            <result column="summary" property="summary"/>
            <result column="type" property="type"/>
            <result column="views" property="views"/>
            <result column="tags" property="tags"/>
            <result column="category_id" property="categoryId"/>
        </association>
        <collection property="knowledgeAttachments" column="id" ofType="com.sub.baoxian.model.entity.KnowledgeAttachment" 
                  select="getKnowledgeAttachmentsByKnowledgeId"/>
    </resultMap>

    <!-- 查询单个知识库文章 -->
    <select id="getKnowledgeBaseById" parameterType="java.lang.Long" resultMap="KnowledgeBaseWithAttachmentsMap">
        SELECT 
            id,
            title,
            content,
            category,
            status,
            created_at,
            updated_at,
            author_id,
            summary,
            type,
            views,
            tags,
            category_id
        FROM 
            knowledge_base
        WHERE 
            id = #{id}
            AND status = 1
    </select>
    
    <!-- 根据知识库ID获取附件列表 -->
    <select id="getKnowledgeAttachmentsByKnowledgeId" parameterType="java.lang.Long" 
            resultType="com.sub.baoxian.model.entity.KnowledgeAttachment">
        SELECT 
            id,
            knowledge_id AS knowledgeId,
            file_name AS fileName,
            file_path AS filePath,
            file_type AS fileType,
            file_size AS fileSize,
            upload_time AS uploadTime,
            description
        FROM 
            knowledge_attachments
        WHERE 
            knowledge_id = #{knowledgeId}
    </select>
    
    <!-- 更新文章浏览量 -->
    <update id="updateKnowledgeBaseViewCount" parameterType="java.lang.Long">
        UPDATE 
            knowledge_base
        SET 
            views = views + 1
        WHERE 
            id = #{id}
            AND status = 1
    </update>

    <!-- 获取知识库一级分类（保险公司）列表 -->
    <select id="getCompanyList" resultType="com.sub.baoxian.model.entity.KnowledgeBaseCompany">
        SELECT 
            id,
            company_name AS companyName,
            company_code AS companyCode,
            logo_url AS logoUrl
        FROM 
            knowledge_company
        ORDER BY 
            id ASC
    </select>

    <!-- 根据保险公司ID获取知识库二级分类（专题）列表 -->
    <select id="getCategoryListByCompanyId" parameterType="java.lang.Long" resultType="com.sub.baoxian.model.entity.KnowledgeBaseCategory">
        SELECT 
            id,
            category_name AS categoryName,
            description,
            created_at AS createdAt,
            updated_at AS updatedAt,
            company_id AS companyId
        FROM 
            knowledge_category
        WHERE 
            company_id = #{companyId}
        ORDER BY 
            id ASC
    </select>

    <!-- 根据分类ID获取分类信息 -->
    <select id="getCategoryInfo" parameterType="java.lang.Long" resultType="com.sub.baoxian.model.entity.KnowledgeBaseCategory">
        SELECT 
            id,
            category_name AS categoryName,
            description,
            created_at AS createdAt,
            updated_at AS updatedAt
        FROM 
            knowledge_category
        WHERE 
            id = #{categoryId}
    </select>

    <!-- 根据保险公司ID获取保险公司信息 -->
    <select id="getCompanyInfo" parameterType="java.lang.Long" resultType="com.sub.baoxian.model.entity.KnowledgeBaseCompany">
        SELECT 
            id,
            company_name AS companyName,
            company_code AS companyCode,
            logo_url AS logoUrl
        FROM 
            knowledge_company
        WHERE 
            id = #{companyId}
    </select>
    <select id="getProductById" resultType="com.sub.baoxian.model.entity.ProductPo">
        SELECT main_type
        FROM product_details pd
        JOIN product_common pc ON pc.product_id = pd.id
    </select>
    
    <!-- 获取所有分类信息 -->
    <select id="getAllCategories" resultType="com.sub.baoxian.model.entity.KnowledgeBaseCategory">
        SELECT 
            id,
            category_name AS categoryName,
            parent_id AS parentId,
            description,
            created_at AS createdAt,
            updated_at AS updatedAt,
            company_id AS companyId
        FROM 
            knowledge_category
        ORDER BY 
            id ASC
    </select>
    
    <!-- 根据父级ID获取子分类 -->
    <select id="getChildrenByParentId" parameterType="java.lang.Long" resultType="com.sub.baoxian.model.entity.KnowledgeBaseCategory">
        SELECT 
            id,
            category_name AS categoryName,
            parent_id AS parentId,
            description,
            created_at AS createdAt,
            updated_at AS updatedAt,
            company_id AS companyId
        FROM 
            knowledge_category
        WHERE 
            parent_id = #{parentId}
        ORDER BY 
            id ASC
    </select>
    
</mapper> 