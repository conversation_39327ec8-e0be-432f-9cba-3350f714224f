<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sub.baoxian.mapper.PersonalCloudMapper">
    
    <!-- 通过用户ID获取云盘基本信息 -->
    <select id="getCloudInfoByUserId" parameterType="java.lang.Long" resultType="com.sub.baoxian.model.entity.PersonalCloud">
        SELECT 
            id,
            user_id AS userId,
            total_storage AS totalStorage,
            used_storage AS usedStorage,
            is_active AS isActive,
            created_at AS createdAt,
            updated_at AS updatedAt,
            last_accessed_at AS lastAccessedAt,
            settings
        FROM 
            personal_cloud
        WHERE 
            user_id = #{userId}
    </select>
    
    <!-- 插入个人云盘记录 -->
    <insert id="insertPersonalCloud" parameterType="com.sub.baoxian.model.entity.PersonalCloud" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO personal_cloud (
            user_id,
            total_storage,
            used_storage,
            is_active,
            created_at,
            updated_at,
            last_accessed_at,
            settings
        ) VALUES (
            #{userId},
            #{totalStorage},
            #{usedStorage},
            #{isActive},
            #{createdAt},
            #{updatedAt},
            #{lastAccessedAt},
            #{settings}
        )
    </insert>
    
    <!-- 更新个人云盘信息 -->
    <update id="updatePersonalCloud" parameterType="com.sub.baoxian.model.entity.PersonalCloud">
        UPDATE personal_cloud
        SET 
            total_storage = #{totalStorage},
            used_storage = #{usedStorage},
            is_active = #{isActive},
            updated_at = #{updatedAt},
            last_accessed_at = #{lastAccessedAt},
            settings = #{settings}
        WHERE 
            id = #{id}
    </update>
    
    <!-- 更新已使用空间 -->
    <update id="updateUsedStorage">
        UPDATE personal_cloud
        SET 
            used_storage = #{usedStorage},
            updated_at = #{updatedAt, jdbcType=BIGINT}
        WHERE 
            user_id = #{userId}
    </update>
    
    <!-- 文件夹操作 -->
    
    <!-- 获取文件夹列表 -->
    <select id="getFoldersByParentId" resultType="com.sub.baoxian.model.entity.PCFolder">
        SELECT 
            id,
            name,
            parent_id AS parentId,
            user_id AS userId,
            path,
            description,
            created_at AS createdAt,
            updated_at AS updatedAt,
            creator_id AS creatorId
        FROM 
            personal_folders
        WHERE 
            user_id = #{userId}
            AND parent_id = #{parentId}
        ORDER BY 
            name ASC
    </select>
    
    <!-- 获取文件夹详情 -->
    <select id="getFolderById" parameterType="java.lang.Long" resultType="com.sub.baoxian.model.entity.PCFolder">
        SELECT 
            id,
            name,
            parent_id AS parentId,
            user_id AS userId,
            path,
            description,
            created_at AS createdAt,
            updated_at AS updatedAt,
            creator_id AS creatorId
        FROM 
            personal_folders
        WHERE 
            id = #{folderId}
    </select>
    
    <!-- 创建文件夹 -->
    <insert id="insertFolder" parameterType="com.sub.baoxian.model.entity.PCFolder" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO personal_folders (
            name,
            parent_id,
            user_id,
            path,
            description,
            created_at,
            updated_at,
            creator_id
        ) VALUES (
            #{name},
            #{parentId},
            #{userId},
            #{path},
            #{description},
            #{createdAt},
            #{updatedAt},
            #{creatorId}
        )
    </insert>
    
    <!-- 更新文件夹 -->
    <update id="updateFolder" parameterType="com.sub.baoxian.model.entity.PCFolder">
        UPDATE personal_folders
        SET 
            name = #{name},
            parent_id = #{parentId},
            path = #{path},
            description = #{description},
            updated_at = #{updatedAt}
        WHERE 
            id = #{id}
    </update>
    
    <!-- 删除文件夹 -->
    <delete id="deleteFolder" parameterType="java.lang.Long">
        DELETE FROM personal_folders
        WHERE id = #{folderId}
    </delete>
    
    <!-- 获取子文件夹数量 -->
    <select id="countSubFolders" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM personal_folders
        WHERE parent_id = #{folderId}
    </select>
    
    <!-- 文件操作 -->
    
    <!-- 获取文件夹内文件列表 -->
    <select id="getFilesByFolderId" resultType="com.sub.baoxian.model.entity.PCFile">
        SELECT 
            id,
            name,
            folder_id AS folderId,
            user_id AS userId,
            file_type AS fileType,
            size,
            oss_url AS ossUrl,
            md5,
            created_at AS createdAt,
            updated_at AS updatedAt,
            creator_id AS creatorId,
            download_count AS downloadCount,
            is_favorite AS isFavorite,
            tag
        FROM 
            personal_files
        WHERE 
            user_id = #{userId}
            AND folder_id = #{folderId}
        ORDER BY 
            created_at DESC
    </select>
    
    <!-- 获取文件详情 -->
    <select id="getFileById" parameterType="java.lang.Long" resultType="com.sub.baoxian.model.entity.PCFile">
        SELECT 
            id,
            name,
            folder_id AS folderId,
            user_id AS userId,
            file_type AS fileType,
            size,
            oss_url AS ossUrl,
            md5,
            created_at AS createdAt,
            updated_at AS updatedAt,
            creator_id AS creatorId,
            download_count AS downloadCount,
            is_favorite AS isFavorite,
            tag
        FROM 
            personal_files
        WHERE 
            id = #{fileId}
    </select>
    
    <!-- 插入文件记录 -->
    <insert id="insertFile" parameterType="com.sub.baoxian.model.entity.PCFile" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO personal_files (
            name,
            folder_id,
            user_id,
            file_type,
            size,
            oss_url,
            oss_bucket_name,
            md5,
            created_at,
            updated_at,
            creator_id,
            download_count,
            is_favorite,
            tag
        ) VALUES (
            #{name},
            #{folderId},
            #{userId},
            #{fileType},
            #{size},
            #{ossUrl},
            #{ossBucketName},
            #{md5},
            #{createdAt},
            #{updatedAt},
            #{creatorId},
            #{downloadCount},
            #{isFavorite},
            #{tag}
        )
    </insert>
    
    <!-- 更新文件信息 -->
    <update id="updateFile" parameterType="com.sub.baoxian.model.entity.PCFile">
        UPDATE personal_files
        SET 
            name = #{name},
            folder_id = #{folderId},
            oss_url = #{ossUrl},
            updated_at = #{updatedAt},
            is_favorite = #{isFavorite},
            tag = #{tag}
        WHERE 
            id = #{id}
    </update>
    
    <!-- 删除文件 -->
    <delete id="deleteFile" parameterType="java.lang.Long">
        DELETE FROM personal_files
        WHERE id = #{fileId}
    </delete>
    
    <!-- 获取文件夹内文件总大小 -->
    <select id="getFilesTotalSizeByFolderId" parameterType="java.lang.Long" resultType="java.lang.Long">
        SELECT SUM(size)
        FROM personal_files
        WHERE folder_id = #{folderId}
    </select>
    
    <!-- 更新文件下载次数 -->
    <update id="incrementDownloadCount" parameterType="java.lang.Long">
        UPDATE personal_files
        SET 
            download_count = download_count + 1,
            updated_at = #{updatedAt, jdbcType=BIGINT}
        WHERE 
            id = #{fileId}
    </update>
    
</mapper> 