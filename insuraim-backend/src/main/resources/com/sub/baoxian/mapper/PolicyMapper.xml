<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sub.baoxian.mapper.PolicyMapper">
    <!-- 保单订单表字段 -->
    <sql id="PolicyOrder_Column_List">
        order_no, customer_name, policyholder_name_cn, policyholder_name_en, insured_name_cn,
        insured_name_en, region, company, product, phone, email, team, referrer, sign_time,
        appointment_time, payment_term, currency, annual_premium, next_renewal_date, order_status, status_updated_at,
        user_id, created_id, created_at, updated_at, remark
    </sql>

    <!-- 保单表字段 -->
    <sql id="Policy_Column_List">
        order_id, policy_no, policy_status, company, product_name, currency, coverage_amount,
        annual_premium, payment_method, payment_term, first_premium_due_date, first_premium_amount, first_premium_payment_method, effective_date,
        cooling_period_start, cooling_period_end, next_renewal_date, next_renewal_amount, renewal_payment_method,
        created_at, updated_at
    </sql>

    <!-- 保单表字段（创建时不包含保单号、首年保费金额、续保金额） -->
    <sql id="Policy_Column_List_Without_PolicyNo">
        order_id, policy_status, company, product_name, currency, coverage_amount,
        annual_premium, payment_method, payment_term, first_premium_due_date, first_premium_payment_method, effective_date,
        cooling_period_start, cooling_period_end, next_renewal_date, renewal_payment_method,
        created_at, updated_at
    </sql>

    <!-- 保单持有人信息表字段 -->
    <sql id="PolicyholderInfo_Column_List">
        policy_id, name_cn, name_en, birth_date, gender, id_card_no, travel_permit_no,
        nationality, mobile, home_phone, email, birth_place, marital_status, education_level, height,
        weight, id_card_address, residential_address, mailing_address, is_smoker, company_name_cn,
        company_name_en, company_address, company_industry, position, annual_income,
        created_at, updated_at
    </sql>

    <!-- 受保人信息表字段 -->
    <sql id="InsuredInfo_Column_List">
        policy_id, name_cn, name_en, birth_date, gender, id_card_no, travel_permit_no,
        mobile, home_phone, email, nationality, marital_status, education_level, height, weight,
        id_card_address, is_smoker, created_at, updated_at
    </sql>

    <!-- 受益人信息表字段 -->
    <sql id="BeneficiaryInfo_Column_List">
        policy_id, name, gender, relationship, id_card_no, benefit_percentage, is_trustee,
        created_at, updated_at
    </sql>

    <!-- 保单状态历史表字段 -->
    <sql id="PolicyStatusHistory_Column_List">
        policy_id, status_code, record_status, updated_at, update_by, remark, created_at
    </sql>

    <!-- 保单状态附件表字段 -->
    <sql id="PolicyStatusAttachment_Column_List">
        status_history_id, file_name, oss_url, file_type, file_size, metadata, description,
        created_at, updated_at
    </sql>

    <!-- 创建保单订单 -->
    <insert id="createPolicyOrder" parameterType="com.sub.baoxian.policymanagement.model.entity.PolicyOrder" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ins_policy_order (
            <include refid="PolicyOrder_Column_List"/>
        ) VALUES (
            #{orderNo}, #{customerName}, #{policyholderNameCn}, #{policyholderNameEn}, #{insuredNameCn},
            #{insuredNameEn}, #{region}, #{company}, #{product}, #{phone}, #{email}, #{team},
            #{referrer}, #{signTime}, #{appointmentTime}, #{paymentTerm}, #{currency}, #{annualPremium}, #{nextRenewalDate},
            #{orderStatus}, #{statusUpdatedAt}, #{userId}, #{createdId}, #{createdAt}, #{updatedAt}, #{remark}
        )
    </insert>

    <!-- 创建保单（不包含保单号、首年保费金额、续保金额） -->
    <insert id="createPolicy" parameterType="com.sub.baoxian.policymanagement.model.entity.Policy" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ins_policy (
            <include refid="Policy_Column_List_Without_PolicyNo"/>
        ) VALUES (
            #{orderId}, #{policyStatus}, #{company}, #{productName}, #{currency}, #{coverageAmount},
            #{annualPremium}, #{paymentMethod}, #{paymentTerm}, #{firstPremiumDueDate}, #{firstPremiumPaymentMethod}, #{effectiveDate},
            #{coolingPeriodStart}, #{coolingPeriodEnd}, #{nextRenewalDate}, #{renewalPaymentMethod},
            #{createdAt}, #{updatedAt}
        )
    </insert>

    <!-- 创建保单持有人信息 -->
    <insert id="createPolicyholderInfo" parameterType="com.sub.baoxian.policymanagement.model.entity.PolicyholderInfo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ins_policy_policyholder_info (
            <include refid="PolicyholderInfo_Column_List"/>
        ) VALUES (
            #{policyId}, #{nameCn}, #{nameEn}, #{birthDate}, #{gender}, #{idCardNo},
            #{travelPermitNo}, #{nationality}, #{mobile}, #{homePhone}, #{email}, #{birthPlace},
            #{maritalStatus}, #{educationLevel}, #{height}, #{weight}, #{idCardAddress},
            #{residentialAddress}, #{mailingAddress}, #{isSmoker}, #{companyNameCn},
            #{companyNameEn}, #{companyAddress}, #{companyIndustry}, #{position},
            #{annualIncome}, #{createdAt}, #{updatedAt}
        )
    </insert>

    <!-- 创建受保人信息 -->
    <insert id="createInsuredInfo" parameterType="com.sub.baoxian.policymanagement.model.entity.InsuredInfo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ins_policy_insured_info (
            <include refid="InsuredInfo_Column_List"/>
        ) VALUES (
            #{policyId}, #{nameCn}, #{nameEn}, #{birthDate}, #{gender}, #{idCardNo},
            #{travelPermitNo}, #{mobile}, #{homePhone}, #{email}, #{nationality}, #{maritalStatus},
            #{educationLevel}, #{height}, #{weight}, #{idCardAddress}, #{isSmoker},
            #{createdAt}, #{updatedAt}
        )
    </insert>

    <!-- 创建受益人信息 -->
    <insert id="createBeneficiaryInfo" parameterType="com.sub.baoxian.policymanagement.model.entity.BeneficiaryInfo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ins_policy_beneficiary_info (
            <include refid="BeneficiaryInfo_Column_List"/>
        ) VALUES (
            #{policyId}, #{name}, #{gender}, #{relationship}, #{idCardNo}, #{benefitPercentage},
            #{isTrustee}, #{createdAt}, #{updatedAt}
        )
    </insert>

    <!-- 创建保单状态历史 -->
    <insert id="createPolicyStatusHistory" parameterType="com.sub.baoxian.policymanagement.model.entity.PolicyStatusHistory" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ins_policy_status_history (
            <include refid="PolicyStatusHistory_Column_List"/>
        ) VALUES (
            #{policyId}, #{statusCode}, #{recordStatus}, #{updatedAt}, #{updateBy},
            #{remark}, #{createdAt}
        )
    </insert>

    <!-- 创建保单状态附件 -->
    <insert id="createPolicyStatusAttachment" parameterType="com.sub.baoxian.policymanagement.model.entity.PolicyStatusAttachment" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ins_policy_status_attachment (
            <include refid="PolicyStatusAttachment_Column_List"/>
        ) VALUES (
            #{statusHistoryId}, #{fileName}, #{ossUrl}, #{fileType}, #{fileSize},
            #{metadata}, #{description}, #{createdAt}, #{updatedAt}
        )
    </insert>

    <!-- 获取保单的历史状态 -->
    <select id="getHistoryStatus" resultType="com.sub.baoxian.policymanagement.model.entity.PolicyStatusHistory" parameterType="java.lang.Long">
        SELECT
            id, <include refid="PolicyStatusHistory_Column_List"/>
        FROM
            ins_policy_status_history
        WHERE
            policy_id = #{policyId}
    </select>

    <!-- 根据订单ID查询保单信息 -->
    <select id="getPolicyByOrderId" resultType="com.sub.baoxian.policymanagement.model.entity.Policy">
        SELECT
            id, <include refid="Policy_Column_List"/>
        FROM
            ins_policy
        WHERE
            order_id = #{orderId}
        LIMIT 1
    </select>

    <!-- 根据保单ID查询投保人信息 -->
    <select id="getPolicyholderInfoByPolicyId" resultType="com.sub.baoxian.policymanagement.model.entity.PolicyholderInfo">
        SELECT
            id, <include refid="PolicyholderInfo_Column_List"/>
        FROM
            ins_policy_policyholder_info
        WHERE
            policy_id = #{policyId}
        LIMIT 1
    </select>

    <!-- 根据保单ID查询被保人信息 -->
    <select id="getInsuredInfoByPolicyId" resultType="com.sub.baoxian.policymanagement.model.entity.InsuredInfo">
        SELECT
            id, <include refid="InsuredInfo_Column_List"/>
        FROM
            ins_policy_insured_info
        WHERE
            policy_id = #{policyId}
        LIMIT 1
    </select>

    <!-- 根据保单ID查询受益人列表 -->
    <select id="getBeneficiaryInfosByPolicyId" resultType="com.sub.baoxian.policymanagement.model.entity.BeneficiaryInfo">
        SELECT
            id, <include refid="BeneficiaryInfo_Column_List"/>
        FROM
            ins_policy_beneficiary_info
        WHERE
            policy_id = #{policyId}
    </select>

    <!-- 根据保单ID查询状态历史列表 -->
    <select id="getPolicyStatusHistoriesByPolicyId" resultType="com.sub.baoxian.policymanagement.model.entity.PolicyStatusHistory">
        SELECT
            id, <include refid="PolicyStatusHistory_Column_List"/>
        FROM
            ins_policy_status_history
        WHERE
            policy_id = #{policyId}
    </select>

    <!-- 根据状态历史ID查询状态附件列表 -->
    <select id="getPolicyStatusAttachmentsByHistoryId" resultType="com.sub.baoxian.policymanagement.model.entity.PolicyStatusAttachment">
        SELECT
            id, <include refid="PolicyStatusAttachment_Column_List"/>
        FROM
            ins_policy_status_attachment
        WHERE
            status_history_id = #{statusHistoryId}
    </select>

    <!-- 根据保单ID查询所有状态附件列表 -->
    <select id="getPolicyStatusAttachmentsByPolicyId" resultType="com.sub.baoxian.policymanagement.model.entity.PolicyStatusAttachment">
        SELECT
            psa.id, psa.status_history_id, psa.file_name, psa.oss_url, psa.file_type,
            psa.file_size, psa.metadata, psa.description, psa.created_at, psa.updated_at
        FROM
            ins_policy_status_attachment psa
        JOIN
            ins_policy_status_history psh ON psa.status_history_id = psh.id
        WHERE
            psh.policy_id = #{policyId}
    </select>

    <!-- 更新保单状态 -->
    <update id="updatePolicyStatus">
        UPDATE ins_policy
        SET
            policy_status = #{status},
            updated_at = #{updatedAt}
            <if test="effectiveDate != null">
                , effective_date = #{effectiveDate}
            </if>
        WHERE
            id = #{policyId}
    </update>

    <!-- 更新保单号 -->
    <update id="updatePolicyNo">
        UPDATE ins_policy
        SET
            policy_no = #{policyNo},
            updated_at = #{updatedAt}
        WHERE
            id = #{policyId}
    </update>

    <!-- 更新保单状态历史记录的记录状态 -->
    <update id="updatePolicyStatusHistoryRecordStatus">
        UPDATE ins_policy_status_history
        SET
            record_status = #{recordStatus},
            updated_at = #{updatedAt}
        WHERE
            policy_id = #{policyId}
            AND status_code = #{statusCode}
    </update>


    <!-- 根据保单ID和状态码查询保单状态历史记录 -->
    <select id="getPolicyStatusHistoryByStatusCode" resultType="com.sub.baoxian.policymanagement.model.entity.PolicyStatusHistory">
        SELECT
            id, policy_id, status_code, record_status, updated_at, update_by, remark, created_at
        FROM
            ins_policy_status_history
        WHERE
            policy_id = #{policyId}
            AND status_code = #{statusCode}
        LIMIT 1
    </select>

    <!-- 根据保单ID查询保单订单信息 -->
    <select id="getPolicyOrderById" resultType="com.sub.baoxian.policymanagement.model.entity.PolicyOrder">
        SELECT
            po.id, po.order_no, po.customer_name, po.policyholder_name_cn, po.policyholder_name_en,
            po.insured_name_cn, po.insured_name_en, po.region, po.company, po.product,
            po.phone, po.email, po.team, po.referrer, po.sign_time, po.appointment_time, po.payment_term,
            po.currency, po.annual_premium, po.next_renewal_date, po.order_status,
            po.status_updated_at, po.user_id, po.created_id, po.created_at, po.updated_at,
            po.remark
        FROM
            ins_policy_order po
        JOIN
            ins_policy p ON po.id = p.order_id
        WHERE
            p.id = #{policyId}
        LIMIT 1
    </select>

    <!-- 更新保单持有人信息 -->
    <update id="updatePolicyholderInfo" parameterType="com.sub.baoxian.policymanagement.model.entity.PolicyholderInfo">
        UPDATE ins_policy_policyholder_info
        SET
            name_cn = #{nameCn},
            name_en = #{nameEn},
            birth_date = #{birthDate},
            gender = #{gender},
            id_card_no = #{idCardNo},
            travel_permit_no = #{travelPermitNo},
            nationality = #{nationality},
            mobile = #{mobile},
            home_phone = #{homePhone},
            email = #{email},
            birth_place = #{birthPlace},
            marital_status = #{maritalStatus},
            education_level = #{educationLevel},
            height = #{height},
            weight = #{weight},
            id_card_address = #{idCardAddress},
            residential_address = #{residentialAddress},
            mailing_address = #{mailingAddress},
            is_smoker = #{isSmoker},
            company_name_cn = #{companyNameCn},
            company_name_en = #{companyNameEn},
            company_address = #{companyAddress},
            company_industry = #{companyIndustry},
            position = #{position},
            annual_income = #{annualIncome},
            updated_at = #{updatedAt}
        WHERE
            policy_id = #{policyId}
    </update>

    <!-- 更新受保人信息 -->
    <update id="updateInsuredInfo" parameterType="com.sub.baoxian.policymanagement.model.entity.InsuredInfo">
        UPDATE ins_policy_insured_info
        SET
            name_cn = #{nameCn},
            name_en = #{nameEn},
            birth_date = #{birthDate},
            gender = #{gender},
            id_card_no = #{idCardNo},
            travel_permit_no = #{travelPermitNo},
            mobile = #{mobile},
            home_phone = #{homePhone},
            email = #{email},
            nationality = #{nationality},
            marital_status = #{maritalStatus},
            education_level = #{educationLevel},
            height = #{height},
            weight = #{weight},
            id_card_address = #{idCardAddress},
            is_smoker = #{isSmoker},
            updated_at = #{updatedAt}
        WHERE
            policy_id = #{policyId}
    </update>

    <!-- 更新受益人信息 -->
    <update id="updateBeneficiaryInfo" parameterType="com.sub.baoxian.policymanagement.model.entity.BeneficiaryInfo">
        UPDATE ins_policy_beneficiary_info
        SET
            name = #{name},
            gender = #{gender},
            relationship = #{relationship},
            id_card_no = #{idCardNo},
            benefit_percentage = #{benefitPercentage},
            is_trustee = #{isTrustee},
            updated_at = #{updatedAt}
        WHERE
            id = #{id}
    </update>

    <!-- 根据状态历史ID查询状态历史记录 -->
    <select id="getPolicyStatusHistoryByHistoryId" resultType="com.sub.baoxian.policymanagement.model.entity.PolicyStatusHistory">
        SELECT
            id, <include refid="PolicyStatusHistory_Column_List"/>
        FROM
            ins_policy_status_history
        WHERE
            id = #{statusHistoryId}
        LIMIT 1
    </select>

    <!-- 根据状态历史ID删除所有相关附件 -->
    <delete id="deletePolicyStatusAttachment" parameterType="java.lang.Long">
        DELETE FROM
            ins_policy_status_attachment
        WHERE
            status_history_id = #{statusHistoryId}
    </delete>

    <!-- 根据状态历史ID删除状态历史记录 -->
    <delete id="deletePolicyStatusHistory" parameterType="java.lang.Long">
        DELETE FROM
            ins_policy_status_history
        WHERE
            id = #{statusHistoryId}
    </delete>

    <!-- 根据订单号统计记录数 -->
    <select id="countByOrderNo" resultType="int">
        SELECT COUNT(*)
        FROM ins_policy_order
        WHERE order_no = #{orderNo}
    </select>

    <!-- 根据保单号统计记录数 -->
    <select id="countByPolicyNo" resultType="int">
        SELECT COUNT(*)
        FROM ins_policy
        WHERE policy_no = #{policyNo}
    </select>

    <!-- 更新保单号 -->
    <update id="updatePolicyNo" parameterType="java.lang.Long">
        UPDATE ins_policy
        SET
            policy_no = #{policyNo},
            updated_at = #{updatedAt}
        WHERE
            id = #{policyId}
    </update>

    <!-- 根据受益人ID查询受益人信息 -->
    <select id="getBeneficiaryInfoById" resultType="com.sub.baoxian.policymanagement.model.entity.BeneficiaryInfo">
        SELECT
            id, policy_id, name, gender, relationship, id_card_no, benefit_percentage, is_trustee, created_at, updated_at
        FROM
            ins_policy_beneficiary_info
        WHERE
            id = #{beneficiaryId}
        LIMIT 1
    </select>

    <!-- 删除受益人信息 -->
    <delete id="deleteBeneficiaryInfo" parameterType="java.lang.Long">
        DELETE FROM
            ins_policy_beneficiary_info
        WHERE
            id = #{beneficiaryId}
    </delete>

    <!-- 根据保单ID删除所有受益人信息 -->
    <delete id="deleteBeneficiaryInfoByPolicyId" parameterType="java.lang.Long">
        DELETE FROM
            ins_policy_beneficiary_info
        WHERE
            policy_id = #{policyId}
    </delete>

    <!-- 根据保单ID删除被保人信息 -->
    <delete id="deleteInsuredInfoByPolicyId" parameterType="java.lang.Long">
        DELETE FROM
            ins_policy_insured_info
        WHERE
            policy_id = #{policyId}
    </delete>

    <!-- 根据保单ID删除保单状态历史记录 -->
    <delete id="deletePolicyStatusHistoryByPolicyId" parameterType="java.lang.Long">
        DELETE FROM
            ins_policy_status_history
        WHERE
            policy_id = #{policyId}
    </delete>

    <!-- 根据保单ID删除保单状态附件 -->
    <delete id="deletePolicyStatusAttachmentByPolicyId" parameterType="java.lang.Long">
        DELETE FROM
            ins_policy_status_attachment
        WHERE
            status_history_id IN (
                SELECT id FROM ins_policy_status_history WHERE policy_id = #{policyId}
            )
    </delete>

</mapper>
