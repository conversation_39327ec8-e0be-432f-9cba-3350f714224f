<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sub.baoxian.mapper.PolicyOrderMapper">

    <!-- 根据订单ID查询订单详情 -->
    <select id="getDetailByOrderId" resultType="com.sub.baoxian.policymanagement.model.entity.PolicyOrder">
        SELECT
            id, order_no, customer_name, policyholder_name_cn, policyholder_name_en,
            insured_name_cn, insured_name_en, region, company, product,
            phone, email, team, referrer, sign_time, payment_term,
            currency, annual_premium, next_renewal_date, order_status,
            status_updated_at, user_id, created_id, created_at, updated_at,
            remark
        FROM
            ins_policy_order
        WHERE
            id = #{orderId}
    </select>

    <!-- 分页查询订单列表 -->
    <select id="pageList" resultMap="PageListResultMap">
        SELECT
            po.id, po.order_no, po.customer_name, po.policyholder_name_cn, po.policyholder_name_en,
            po.insured_name_cn, po.insured_name_en, po.region, po.company, po.product,
            po.phone, po.email, po.team, po.referrer, po.sign_time, po.payment_term,
            po.currency, po.annual_premium, po.next_renewal_date, po.order_status,
            po.status_updated_at, po.user_id, po.created_id, po.created_at, po.updated_at,
            po.remark,

            p.id as p_id, p.order_id as p_order_id, p.policy_no as p_policy_no,
            p.policy_status as p_policy_status, p.company as p_company, p.product_name as p_product_name,
            p.currency as p_currency, p.coverage_amount as p_coverage_amount,
            p.annual_premium as p_annual_premium, p.payment_method as p_payment_method,
            p.first_premium_due_date as p_first_premium_due_date, p.first_premium_amount as p_first_premium_amount,
            p.effective_date as p_effective_date, p.cooling_period_start as p_cooling_period_start,
            p.cooling_period_end as p_cooling_period_end, p.next_renewal_date as p_next_renewal_date,
            p.next_renewal_amount as p_next_renewal_amount, p.created_at as p_created_at,
            p.updated_at as p_updated_at
        FROM
            ins_policy_order po
        LEFT JOIN
            ins_policy p ON po.id = p.order_id
        <where>
            <if test="userId != null">
                AND po.user_id = #{userId}
            </if>
            <if test="search != null and search != ''">
                AND (
                    po.order_no LIKE CONCAT('%', #{search}, '%') OR
                    p.policy_no LIKE CONCAT('%', #{search}, '%') OR
                    po.policyholder_name_cn LIKE CONCAT('%', #{search}, '%') OR
                    po.insured_name_cn LIKE CONCAT('%', #{search}, '%')
                )
            </if>
            <if test="policyStatus != null">
                AND p.policy_status = #{policyStatus}
            </if>
            <if test="status != null">
                AND po.order_status = #{status}
            </if>
            <if test="createdAt != null and createdAt != ''">
                AND po.created_at = #{createdAt}
            </if>
            <if test="updatedAt != null and updatedAt != ''">
                AND po.updated_at = #{updatedAt}
            </if>
        </where>
        ORDER BY po.created_at DESC
    </select>

    <!-- 分页列表结果映射 -->
    <resultMap id="PageListResultMap" type="com.sub.baoxian.policymanagement.model.vo.GetPageListVO">
        <association property="policyOrder" javaType="com.sub.baoxian.policymanagement.model.entity.PolicyOrder">
            <id column="id" property="id" />
            <result column="order_no" property="orderNo" />
            <result column="customer_name" property="customerName" />
            <result column="policyholder_name_cn" property="policyholderNameCn" />
            <result column="policyholder_name_en" property="policyholderNameEn" />
            <result column="insured_name_cn" property="insuredNameCn" />
            <result column="insured_name_en" property="insuredNameEn" />
            <result column="region" property="region" />
            <result column="company" property="company" />
            <result column="product" property="product" />
            <result column="phone" property="phone" />
            <result column="email" property="email" />
            <result column="team" property="team" />
            <result column="referrer" property="referrer" />
            <result column="sign_time" property="signTime" />
            <result column="payment_term" property="paymentTerm" />
            <result column="currency" property="currency" />
            <result column="annual_premium" property="annualPremium" />
            <result column="next_renewal_date" property="nextRenewalDate" />
            <result column="order_status" property="orderStatus" typeHandler="org.apache.ibatis.type.EnumTypeHandler" />
            <result column="status_updated_at" property="statusUpdatedAt" />
            <result column="user_id" property="userId" />
            <result column="created_id" property="createdId" />
            <result column="created_at" property="createdAt" />
            <result column="updated_at" property="updatedAt" />
            <result column="remark" property="remark" />
        </association>

        <association property="policy" javaType="com.sub.baoxian.policymanagement.model.entity.Policy">
            <id column="p_id" property="id" />
            <result column="p_order_id" property="orderId" />
            <result column="p_policy_no" property="policyNo" />
            <result column="p_policy_status" property="policyStatus" typeHandler="org.apache.ibatis.type.EnumTypeHandler" />
            <result column="p_company" property="company" />
            <result column="p_product_name" property="productName" />
            <result column="p_currency" property="currency" />
            <result column="p_coverage_amount" property="coverageAmount" />
            <result column="p_annual_premium" property="annualPremium" />
            <result column="p_payment_method" property="paymentMethod" />
            <result column="p_first_premium_due_date" property="firstPremiumDueDate" />
            <result column="p_first_premium_amount" property="firstPremiumAmount" />
            <result column="p_effective_date" property="effectiveDate" />
            <result column="p_cooling_period_start" property="coolingPeriodStart" />
            <result column="p_cooling_period_end" property="coolingPeriodEnd" />
            <result column="p_next_renewal_date" property="nextRenewalDate" />
            <result column="p_next_renewal_amount" property="nextRenewalAmount" />
            <result column="p_created_at" property="createdAt" />
            <result column="p_updated_at" property="updatedAt" />
        </association>
    </resultMap>
</mapper>
