<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sub.baoxian.mapper.ProductDao">
    <sql id="product_info">pd.name, pd.main_type, pd.sub_type, pd.common_id, pd.policy_currency, pd.policy_currency_details, pd.premium_payment_period,
        pd.policy_term, pd.minimum_age, pd.maximum_age, pd.minimum_sum_insured, pd.maximum_sum_insured, pd.sell_date, pd.policy_date,
        pd.annual_prepayment_interest_rate, pd.underwriting_method_or_underwriting_process, pd.medical_underwriting, pd.financial_underwriting,
        pd.occupational_underwriting, pd.geographical_underwriting, pd.no_medical_exam_limit, pd.additional_loading_applicable,
        pd.special_documentation_required, pd.critical_illness_coverage1, pd.hospitalization_benefit_non_premium_plan, pd.hospitalization_benefit_premium_plan,
        pd.accidental_benefit, pd.premium_waiver_benefit, pd.long_term_sick_leave_benefit, pd.outpatient_benefit, pd.female_specific_benefit, pd.baby_reward_benefit,
        pd.disability_benefit, pd.increase_sum_insured, pd.decrease_sum_insured, pd.add_base_coverage, pd.reduce_base_coverage, pd.add_rider, pd.remove_rider,
        pd.policy_loan, pd.renewal_payment_method, pd.prepaid_premium, pd.policy_reinstatement, pd.automatic_premium_loan, pd.change_plan, pd.reduced_paid_up_option,
        pd.change_in_smoking_status, pd.others
    </sql>

    <sql id="product_common">
        pc.product_features, pc.target_audience, pc.premium_rate, pc.non_guaranteed_dividend, pc.terminal_dividend,
        pc.terminal_dividend_non_guarantee, pc.terminal_dividend_management_benefit, pc.guaranteed_monthly_annuity_income,
        pc.non_guaranteed_monthly_annuity_income, pc.high_coverage_discount, pc.special_disease_coverage,
        pc.premium_holiday, pc.policy_split_rights as policy_split_rights, pc.currency_conversion_rights, pc.additional_critical_illness_coverage_for_children,
        pc.early_stage_illness_coverage, pc.critical_illness_coverage, pc.waiver_of_premium_on_critical_illness_benefit, pc.additional_critical_illness_coverage,
        pc.multiple_critical_illness_coverage, pc.family_shared_coverage, pc.all_round_protection_plan, pc.level1, pc.level2, pc.level3, pc.all_levels,
        pc.double_indemnity_benefit, pc.compassionate_death_benefit, pc.additional_all_round_protection_plan, pc.medical_expenses_coverage,
        pc.rehabilitation_support_services, pc.additional_hospital_cash_benefit, pc.hospital_surgery_reimbursement, pc.no_lifetime_limit_and_unknown_preexisting_covered,
        pc.additional_death_benefit, pc.accidental_burn_benefit, pc.flexible_death_and_accidental_payout_method, pc.accidental_death_benefit, pc.cancer_coverage,
        pc.accidental_total_and_permanent_disability, pc.accidental_death_and_dismemberment_benefit, pc.accidental_TPD_waiver_of_premium as accidental_tpd_waiver_of_premium,
        pc.sudden_death_coverage, pc.change_of_insured_person, pc.substitute_insured_person, pc.parental_waiver_benefit, pc.spousal_waiver_benefit, pc.extended_coverage_for_children,
        pc.policy_termination, pc.other_benefits, pc.special_remarks, pc.policy_reverse_mortgage_loan, pc.rate_table, pc.product_factsheet,
        pc.benefit_provisions, pc.additional_premium, pc.supporting_documents, pc.waiver_of_premium, pc.plan_related, pc.training
    </sql>

<!--    获取产品信息通过id-->
    <select id="getProductById" resultType="com.sub.baoxian.model.entity.ProductPo">
        SELECT <include refid="product_info"/>, <include refid="product_common"/>
        FROM product_details pd
        JOIN product_common pc ON pc.product_id = pd.id
        WHERE pd.product_id = #{id}
    </select>
    <!--    show basic info of products 产品介绍和搜索-->
    <select id="getProductDisplay"
            resultType="com.sub.baoxian.productInfoService.entity.ProductIntroPO">
        SELECT ip.id AS product_id, product_name AS name, company_name, category_name AS main_type, region
        FROM ins_product ip
        LEFT JOIN ins_product_category pc ON ip.category_id = pc.id
        <where>
            <trim prefixOverrides="AND |OR">
                <if test="search != null and search != ''">
                    (
                    product_name LIKE CONCAT('%', #{search}, '%')
                    OR company_name LIkE CONCAT('%', #{search}, '%')
                    )
                </if>
                <if test="mainType != null and mainType != ''">
                    AND pc.code = #{mainType}
                </if>
                <if test="insurer!=null">
                    AND company_name LIkE CONCAT('%', #{insurer}, '%')
                </if>
                <if test="region !=null">
                    AND region LIkE CONCAT('%', #{region}, '%')
                </if>
            </trim>
        </where>
        ORDER BY product_id ASC
    </select>

    <select id="getProductDisplayIRR" resultType="com.sub.baoxian.productInfoService.entity.ProductIntroPO">
        SELECT pi.id AS product_id, product_name AS name, currency,company_name, pc.name AS main_type, region
        FROM ins_product_interest pi
        LEFT JOIN ins_product_category pc ON pi.category_id = pc.id
        <where>
            <trim prefixOverrides="AND |OR">
                <if test="productDisplayDTO.age == 20">
                    AND caseAge20 IS NOT NULL
                </if>
                <if test="productDisplayDTO.age == 40">
                    AND caseAge40 IS NOT NULL
                </if>
                <if test="productDisplayDTO.search != null and productDisplayDTO.search != ''">
                    (
                    pc.name LIKE CONCAT('%', #{productDisplayDTO.search}, '%')
                    OR product_name LIKE CONCAT('%', #{productDisplayDTO.search}, '%')
                    OR company_name LIKE CONCAT('%', #{productDisplayDTO.search}, '%')
                    )
                </if>
                <if test="productDisplayDTO.type != null and productDisplayDTO.type != ''">
                    AND pc.code = #{productDisplayDTO.type}
                </if>
                <if test="productDisplayDTO.insurer != null">
                    AND company_name LIKE CONCAT('%', #{productDisplayDTO.insurer}, '%')
                </if>
                <if test="productDisplayDTO.region != null">
                    AND region LIKE CONCAT('%', #{productDisplayDTO.region}, '%')
                </if>
                <if test="productDisplayDTO.currency != null">
                    AND currency LIKE CONCAT('%', #{productDisplayDTO.currency}, '%')
                </if>
            </trim>
        </where>
        ORDER BY product_id ASC
    </select>

    <select id="getProductDisplayIRR2" resultType="com.sub.baoxian.productInfoService.entity.ProductCalDisplayPo">
        SELECT ip.id AS product_id, product_name, company_name, category_name, region, payment_term,currencies,guarantee_period
        FROM ins_product ip
        <where>
            <trim prefixOverrides="AND |OR">
                <if test="productDisplayDTO.search != null and productDisplayDTO.search != ''">
                    product_name LIKE CONCAT('%', #{productDisplayDTO.search}, '%')
                    category_name LIKE CONCAT('%', #{productDisplayDTO.search}, '%')
                </if>
                <if test="productDisplayDTO.type != null and productDisplayDTO.type != ''">
                    AND category_code = #{productDisplayDTO.type}
                </if>
                <if test="productDisplayDTO.insurer != null">
                    AND company_name LIKE CONCAT('%', #{productDisplayDTO.insurer}, '%')
                </if>
                <if test="productDisplayDTO.region != null">
                    AND region LIKE CONCAT('%', #{productDisplayDTO.region}, '%')
                </if>
                <if test="productDisplayDTO.currency != null">
                    AND JSON_CONTAINS(currencies, '"USD"', '$')
               </if>
                AND ip.has_cash_value = 1
            </trim>
        </where>

        ORDER BY id ASC
    </select>


    <select id="getIRRProductCurrency" resultType="String">
        SELECT DISTINCT currency
        FROM ins_product_interest
    </select>
<!--   获取储蓄类产品id -->
    <select id="lifeProductIds" resultType="java.lang.Integer">
        SELECT id
        FROM ins_product_sub
        WHERE category_id = 15
    </select>

    <!--   获取产品介绍 -->
    <select id="getProductIntro" resultType="com.sub.baoxian.productInfoService.entity.ProductIntroPO">
        SELECT id,product_id, name, main_type,product_intro, download,image_address, note
<!--        试用查看效果-->
        FROM product_introduction
        WHERE product_id = #{productId}
    </select>

    <select id="productCategoryList" resultType="String">
        SELECT DISTINCT main_type
        FROM product_introduction
        ORDER BY main_type ASC
    </select>

<!--    拿到产品代码-->
    <select id="getInsuranceCode" parameterType="Integer" resultType="com.sub.baoxian.productInfoService.entity.ProductAttributeAO"  >
        SELECT id as product_id, code, name
        FROM product_details
        WHERE id = #{productId}
    </select>
<!--    拿到产品小册子-->
    <select id="getInsuranceDownload" parameterType="Integer" resultType="com.sub.baoxian.productInfoService.entity.ProductAttributeAO">
        SELECT product_id, name, download
        FROM product_introduction
        WHERE product_id = #{productId}
    </select>
    <select id="getProductIRRAndDividendDynamic" parameterType="Integer" resultType="com.sub.baoxian.productInfoService.entity.ProductAttributePo">
        SELECT sub_product_id, product_name,
        <trim suffixOverrides=",">
            <choose>
                <when test="age == 20">
                    caseAge20 AS IRR, caseAge20_nonInterest AS IRRWithoutInterest,
                </when>
                <when test="age == 40">
                    caseAge40 AS IRR, caseAge40_nonInterest AS IRRWithoutInterest,
                </when>
                <otherwise>
                    caseAge20 AS IRR, caseAge20_nonInterest AS IRRWithoutInterest,
                </otherwise>
            </choose>
        </trim>
        FROM ins_product_interest
        <where>
            sub_product_id = #{productId}
        </where>
    </select>

    <select id="getProductIRRAndDividend" parameterType="Integer" resultType="com.sub.baoxian.productInfoService.entity.ProductAttributePo">
        SELECT product_id, name, product_intro, IRR, dividend_realization_rate, download
        FROM product_introduction
        WHERE product_id = #{productId}
    </select>

    <select id="ProductListOfInsuranceFee" resultType="com.sub.baoxian.productInfoService.entity.InsuranceFeePo">
        SELECT id, product_name, type,max_age,min_age, insured_related, region
        FROM product_fee_calculation
        <where>
            <!-- 模糊搜索 product_name -->
            <if test="search != null and search != ''">
                AND product_name LIKE CONCAT('%', #{search}, '%')
            </if>
            <if test="insurer">
                AND insurer LIKE CONCAT('%', #{insurer}, '%')
            </if>
            <!-- 精确匹配 type -->
            <if test="type != null and type != ''">
                AND type = #{type}
            </if>
            <if test="region">
                AND region = #{region}
            </if>
        </where>
    </select>

    <select id="getCalculationFactors" parameterType="integer" resultType="com.sub.baoxian.productInfoService.entity.InsuranceFeePo">
        select * from product_fee_calculation where id = #{id}
    </select>

    <select id="displayCalculationType" resultType="String">
        select DISTINCT type
        from product_fee_calculation
    </select>
    <select id="getRealizationProductsName" resultType="com.sub.baoxian.productInfoService.entity.RealizationRatePo">
        select DISTINCT product_name
        from product_dividend_realization_rate
    </select>

   
    <select id="getProductRealizationRateByName" resultType="com.sub.baoxian.productInfoService.entity.RealizationRatePo">
        select product_name, currency,dividend_type,issue_year, dividend_realization_rate_2022,  dividend_realization_rate_2023
        from product_dividend_realization_rate
        <where>
            <if test="search!=null">
                AND product_name Like concat('%',#{search},'%')
            </if>

        </where>

    </select>
    <select id="getProductRealizationRateByNameDistinct" resultType="com.sub.baoxian.productInfoService.entity.RealizationRatePo">
        select product_name, currency,dividend_type,issue_year, dividend_realization_rate_2022,  dividend_realization_rate_2023
        from product_dividend_realization_rate
        <where>
            <if test="search!=null">
                AND product_name Like concat('%',#{search},'%')
            </if>
        </where>

    </select>

    <select id="getDetailsOfDividendProduct" resultType="com.sub.baoxian.productInfoService.entity.RealizationRatePo">
        SELECT pdr.product_name, pdr.currency, pdr.dividend_type, pdr.issue_year, pdr.dividend_realization_rate_2022, pdr.dividend_realization_rate_2023
        <!-- pdr.product_id, if needed in RealizationRatePo -->
        FROM
        product_dividend_realization_rate pdr
        JOIN
        product_realization_rate prr ON pdr.product_id = prr.id
        <where>
            <if test="search!=null and search!=''">
                AND pdr.product_name = #{search}
            </if>
            <if test="insurer!=null">
                AND pdr.insurer Like concat('%',#{insurer},'%')
            </if>
            <if test="region!=null">
                AND pdr.region = #{region}
            </if>
        </where>

    </select>

    <select id="getRealizationProductsName1" resultType="String">
        select DISTINCT product_name
        from product_dividend_realization_rate
    </select>
    <insert id="insertss" parameterType="com.sub.baoxian.productInfoService.entity.InsurancePostPo">
        insert into product_post (product_code,product_name,introduction, product_feature, product_basic, interest_rate)
        values (#{productCode},#{productName},#{introduction},#{productFeature},#{productBasic},#{interestRate})
    </insert>
    <update id="update111">
        update product_dividend_realization_rate
        set product_id = #{id}
        where product_name = #{productName}
    </update>
    <select id="getProductIRRAndDividend111111" resultType="com.sub.baoxian.productInfoService.entity.ProductAttributePo">
        SELECT product_id, name, IRR, dividend_realization_rate, download
        FROM product_introduction

    </select>

    <select id ="getPostInfoOfInsurance"  resultType="com.sub.baoxian.productInfoService.entity.InsurancePostPo">
        select id, product_code, product_name, introduction, product_feature, product_basic, interest_rate
        from product_post
        where id = #{id}
    </select>
    <select id="displayAvailablePost" resultType="com.sub.baoxian.productInfoService.entity.InsurancePostPo">
        select id, product_code, product_name, introduction, product_basic
        from product_post
        <where>
            <if test="search != null and search != ''">
                AND product_name LIKE CONCAT('%', #{search}, '%')
            </if>
        </where>

    </select>
    <select id="getDividendProductStar"
            resultType="com.sub.baoxian.productInfoService.entity.RealizationRatePo">
        select product_name, currency, dividend_type, issue_year, dividend_realization_rate_2022, dividend_realization_rate_2023
        from product_dividend_realization_rate
    </select>
    <select id="getDynamicIRR" resultType="java.lang.String">
        SELECT caseAge40
        FROM ins_product_interest
        WHERE sub_product_id = #{productId}
    </select>
    <select id="getProductIRRAndDividend2"
            resultType="com.sub.baoxian.productInfoService.BO.CalculationDataBO">
        SELECT case
        FROM ins_product_profit
        WHERE sub_product_id = #{productId}
    </select>


</mapper>