<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sub.baoxian.mapper.ProductCalMapper">
    <resultMap id="BaseResultMap" type="com.sub.baoxian.productInfoService.BO.CalculationDataBO" >
        <!-- 这里不需要任何 result 标签，因为整个对象由 TypeHandler 处理 -->
    </resultMap>

    <select id="profitCalculator" resultMap="BaseResultMap">
        SELECT `case`
        FROM ins_product_profit
        WHERE sub_product_id = #{productId} AND age = #{age} AND case_type = #{type}
    </select>

    <select id="selectLink" resultType="com.sub.baoxian.productInfoService.entity.FileLinkPo">
        SELECT *
        FROM ins_product_link
        <where>
            <trim prefixOverrides="AND |OR">
                <if test="productName!=null">
                    product_name Like concat('%',#{productName},'%')
                </if>
                <if test="age!=null">
                    AND age = #{age}
                </if>
                <if test="company!=null">
                    AND company = #{company}
                </if>
            </trim>
        </where>
    </select>
    <select id="selectCompany" resultType="com.sub.baoxian.productInfoService.entity.FileLinkPo">
        SELECT DISTINCT company
        FROM ins_product_link
    </select>


    <insert id="insertProductLink" parameterType="String">
        INSERT INTO ins_product_link(product_name, age,link, company) VALUES (#{productName},#{age}, #{productLink},#{company})
    </insert>

</mapper>