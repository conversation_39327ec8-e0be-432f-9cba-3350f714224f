/**
 * WebSocket客户端示例代码
 * 
 * 使用SockJS和STOMP客户端连接WebSocket服务器
 * 
 * 前置依赖：
 * - sockjs-client
 * - @stomp/stompjs
 * 
 * 安装依赖：
 * npm install sockjs-client @stomp/stompjs
 */

// 引入SockJS和STOMP客户端
import SockJS from 'sockjs-client';
import { Client } from '@stomp/stompjs';

/**
 * 创建WebSocket连接
 * 
 * @param {string} baseUrl - 服务器基础URL，例如：'http://localhost:8080'
 * @param {string} token - 认证令牌
 * @param {function} onMessageCallback - 收到消息的回调函数
 * @param {function} onBroadcastCallback - 收到广播的回调函数
 * @param {function} onConnectCallback - 连接成功的回调函数
 * @param {function} onErrorCallback - 连接错误的回调函数
 * @returns {Object} - STOMP客户端实例
 */
export function createWebSocketConnection(
    baseUrl, 
    token, 
    onMessageCallback, 
    onBroadcastCallback,
    onConnectCallback,
    onErrorCallback
) {
    // 创建STOMP客户端
    const stompClient = new Client({
        // 创建WebSocket连接
        webSocketFactory: () => new SockJS(`${baseUrl}/ws`),
        
        // 连接头信息，包含认证令牌
        connectHeaders: {
            Authorization: `Bearer ${token}`
        },
        
        // 调试日志
        debug: function (str) {
            console.log('STOMP: ' + str);
        },
        
        // 重连延迟（毫秒）
        reconnectDelay: 5000,
        
        // 心跳间隔（毫秒）
        heartbeatIncoming: 4000,
        heartbeatOutgoing: 4000
    });

    // 连接成功回调
    stompClient.onConnect = (frame) => {
        console.log('WebSocket连接成功: ' + frame);
        
        // 订阅个人消息
        stompClient.subscribe('/user/queue/messages', (message) => {
            try {
                const messageData = JSON.parse(message.body);
                console.log('收到个人消息:', messageData);
                
                // 调用消息回调
                if (onMessageCallback) {
                    onMessageCallback(messageData);
                }
            } catch (error) {
                console.error('解析消息失败:', error);
            }
        });
        
        // 订阅广播消息
        stompClient.subscribe('/topic/broadcast', (message) => {
            try {
                const messageData = JSON.parse(message.body);
                console.log('收到广播消息:', messageData);
                
                // 调用广播回调
                if (onBroadcastCallback) {
                    onBroadcastCallback(messageData);
                }
            } catch (error) {
                console.error('解析广播消息失败:', error);
            }
        });
        
        // 调用连接成功回调
        if (onConnectCallback) {
            onConnectCallback();
        }
    };

    // 连接错误回调
    stompClient.onStompError = (frame) => {
        console.error('WebSocket连接错误: ' + frame.headers['message']);
        console.error('错误详情: ' + frame.body);
        
        // 调用错误回调
        if (onErrorCallback) {
            onErrorCallback(frame);
        }
    };

    // 激活连接
    stompClient.activate();
    
    return stompClient;
}

/**
 * 使用示例
 */
function example() {
    // 服务器基础URL
    const baseUrl = 'http://localhost:8080';
    
    // 认证令牌
    const token = 'your-auth-token';
    
    // 创建WebSocket连接
    const stompClient = createWebSocketConnection(
        baseUrl,
        token,
        
        // 收到个人消息的回调
        (message) => {
            console.log('处理个人消息:', message);
            
            // 显示消息通知
            showNotification(message.payload.title, message.payload.content);
            
            // 更新未读消息数量
            updateUnreadCount();
        },
        
        // 收到广播消息的回调
        (broadcast) => {
            console.log('处理广播消息:', broadcast);
            
            // 显示广播通知
            showBroadcastNotification(broadcast.payload.title, broadcast.payload.content);
        },
        
        // 连接成功的回调
        () => {
            console.log('WebSocket连接已建立，可以接收实时消息');
            
            // 更新连接状态
            updateConnectionStatus(true);
        },
        
        // 连接错误的回调
        (error) => {
            console.error('WebSocket连接错误:', error);
            
            // 更新连接状态
            updateConnectionStatus(false);
        }
    );
    
    // 断开连接
    function disconnect() {
        if (stompClient) {
            stompClient.deactivate();
            console.log('WebSocket连接已断开');
            
            // 更新连接状态
            updateConnectionStatus(false);
        }
    }
    
    // 返回连接控制对象
    return {
        client: stompClient,
        disconnect
    };
}

// 辅助函数示例（实际项目中根据UI框架实现）
function showNotification(title, content) {
    // 实现消息通知显示
    console.log(`通知: ${title} - ${content}`);
}

function showBroadcastNotification(title, content) {
    // 实现广播通知显示
    console.log(`广播: ${title} - ${content}`);
}

function updateUnreadCount() {
    // 实现未读消息数量更新
    console.log('更新未读消息数量');
}

function updateConnectionStatus(isConnected) {
    // 实现连接状态更新
    console.log(`连接状态: ${isConnected ? '已连接' : '已断开'}`);
}

// 导出示例函数
export { example }; 