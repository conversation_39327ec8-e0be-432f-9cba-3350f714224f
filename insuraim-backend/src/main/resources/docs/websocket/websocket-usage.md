# WebSocket 实时消息通知使用文档

本文档介绍如何使用WebSocket实时消息通知功能，包括服务端和客户端的配置和使用方法。

## 1. 概述

WebSocket实时消息通知功能允许服务器向客户端推送消息，无需客户端轮询。系统支持两种消息推送方式：

1. **个人消息**：向特定用户发送消息
2. **广播消息**：向所有已连接的用户发送消息

系统使用基于SockJS和STOMP协议的WebSocket实现。

## 2. 服务端API

### 2.1 发送个人消息

通过`MessageService`的`sendMessageToUser`方法发送个人消息：

```java
@Autowired
private MessageService messageService;

// 发送个人消息
messageService.sendMessageToUser(
    userId,          // 用户ID
    "消息标题",       // 消息标题
    "消息内容",       // 消息内容
    MessageType.SYSTEM // 消息类型：SYSTEM或BUSINESS
);
```

### 2.2 发送广播消息

通过`MessageService`的`sendBroadcastMessage`方法发送广播消息：

```java
@Autowired
private MessageService messageService;

// 发送广播消息
messageService.sendBroadcastMessage(
    "广播标题",       // 广播标题
    "广播内容",       // 广播内容
    MessageType.SYSTEM // 消息类型：SYSTEM或BUSINESS
);
```

### 2.3 直接使用WebSocketService

如果需要更灵活的消息发送，可以直接使用`WebSocketService`：

```java
@Autowired
private WebSocketService webSocketService;

// 发送个人消息
webSocketService.sendMessageToUser(userId, messageObject);

// 发送广播消息
webSocketService.sendBroadcast(messageObject);
```

## 3. 客户端集成

### 3.1 安装依赖

```bash
npm install sockjs-client @stomp/stompjs
```

### 3.2 创建WebSocket连接

```javascript
import SockJS from 'sockjs-client';
import { Client } from '@stomp/stompjs';

// 创建STOMP客户端
const stompClient = new Client({
  // 创建WebSocket连接
  webSocketFactory: () => new SockJS('/ws'),
  
  // 连接头信息，包含认证令牌
  connectHeaders: {
    Authorization: `Bearer ${token}` // 传递认证token
  },
  
  // 调试日志
  debug: function (str) {
    console.log('STOMP: ' + str);
  },
  
  // 重连延迟（毫秒）
  reconnectDelay: 5000,
  
  // 心跳间隔（毫秒）
  heartbeatIncoming: 4000,
  heartbeatOutgoing: 4000
});
```

### 3.3 订阅消息

```javascript
// 连接成功回调
stompClient.onConnect = (frame) => {
  console.log('WebSocket连接成功: ' + frame);
  
  // 订阅个人消息
  stompClient.subscribe('/user/queue/messages', (message) => {
    const messageData = JSON.parse(message.body);
    console.log('收到个人消息:', messageData);
    // 处理消息...
  });
  
  // 订阅广播消息
  stompClient.subscribe('/topic/broadcast', (message) => {
    const messageData = JSON.parse(message.body);
    console.log('收到广播消息:', messageData);
    // 处理广播...
  });
};
```

### 3.4 处理连接错误

```javascript
// 连接错误回调
stompClient.onStompError = (frame) => {
  console.error('WebSocket连接错误: ' + frame.headers['message']);
  console.error('错误详情: ' + frame.body);
};
```

### 3.5 激活连接

```javascript
// 激活连接
stompClient.activate();
```

### 3.6 断开连接

```javascript
// 断开连接
function disconnect() {
  if (stompClient) {
    stompClient.deactivate();
    console.log('WebSocket连接已断开');
  }
}
```

## 4. 消息格式

### 4.1 个人消息格式

```json
{
  "type": "BIZ_MESSAGE",
  "payload": {
    "id": 123,
    "userId": 456,
    "title": "消息标题",
    "content": "消息内容",
    "messageType": 1,
    "status": 0,
    "isPush": 0,
    "isImportant": 0,
    "createdAt": 1636123456789
  },
  "timestamp": 1636123456789
}
```

### 4.2 广播消息格式

```json
{
  "type": "BROADCAST",
  "payload": {
    "title": "广播标题",
    "content": "广播内容",
    "type": 1,
    "timestamp": 1636123456789
  },
  "timestamp": 1636123456789
}
```

## 5. 认证机制

WebSocket连接使用与REST API相同的认证机制，通过在连接时提供Authorization头部进行认证：

```javascript
connectHeaders: {
  Authorization: `Bearer ${token}` // 传递认证token
}
```

服务器会验证token，只有认证成功的用户才能建立WebSocket连接。

## 6. 最佳实践

1. **连接管理**：在用户登录后建立WebSocket连接，登出时断开连接
2. **重连机制**：利用STOMP客户端的重连机制处理网络波动
3. **错误处理**：妥善处理连接错误和消息解析错误
4. **消息去重**：客户端应实现消息去重机制，避免重复显示消息
5. **离线处理**：用户离线时，消息仍会存储在数据库中，用户重新连接后可以通过API获取未读消息

## 7. 完整示例

详细的客户端示例代码请参考 [websocket-client-example.js](./websocket-client-example.js)。

## 8. 故障排查

1. **连接失败**：检查认证token是否有效，网络连接是否正常
2. **消息接收失败**：检查订阅路径是否正确，消息格式是否符合预期
3. **消息发送失败**：检查服务端日志，确认用户ID是否正确

如有其他问题，请联系系统管理员或开发团队。 