package com.sub.baoxian;

import com.alibaba.fastjson2.JSONObject;
import com.github.houbb.opencc4j.util.ZhConverterUtil;
import com.sub.baoxian.mapper.InsuranceNewsMapper;
import com.sub.baoxian.model.entity.InsuranceNews;
import com.sub.baoxian.news.service.NewsContent;
import com.sub.baoxian.news.utils.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.internal.StringUtil;
import org.jsoup.nodes.Document;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;


@SpringBootTest()
class BaoxianApplicationTests {

    @Autowired
    private NewsContent newsContent;
    @Autowired
    private InsuranceNewsMapper insuranceNewsMapper;

    @Test
    void contextLoads() {
//        List<InsuranceNews> list = insuranceNewsMapper.getInsuranceNewsList(new InsuranceNews());
//        for (InsuranceNews insuranceNews : list) {
//            if (StringUtils.isNotEmpty(insuranceNews.getContent())) {
//                String[] targs = insuranceNews.getTags().split(",");
//                String tagsList = targs[0];
//                tagsList = tagsList + "," + targs[targs.length - 1];
//
//                insuranceNews.setTags(tagsList);
//                insuranceNewsMapper.updateInsuranceNews(insuranceNews);
//            }
//        }
        newsContent.insuranceNewsDb("rthk");

    }
}
