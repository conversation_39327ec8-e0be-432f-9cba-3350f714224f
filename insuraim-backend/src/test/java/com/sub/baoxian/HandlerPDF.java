package com.sub.baoxian;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.InputStream;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import com.sub.baoxian.core.pdfhandler.PdfHandler;

@SpringBootTest
public class HandlerPDF {

    @Autowired
    private PdfHandler pdfHandler;

    @Test
    public void testHandlerPDF() {
        InputStream inputStream;
        try {
            inputStream = new FileInputStream("/Users/<USER>/Downloads/test.pdf");
            InputStream outputStream = pdfHandler.processPdf(inputStream, "MEDICAL");
            // 保存文件
            FileOutputStream fileOutputStream = new FileOutputStream("/Users/<USER>/Downloads/test_output.pdf");
            outputStream.transferTo(fileOutputStream);
            fileOutputStream.close();
            outputStream.close();
            inputStream.close();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
