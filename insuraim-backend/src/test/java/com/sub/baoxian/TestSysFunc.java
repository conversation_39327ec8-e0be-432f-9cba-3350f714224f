package com.sub.baoxian;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.sub.baoxian.mapper.KnowledgeBaseMapper;
import com.sub.baoxian.mapper.ProductCalMapper;
import com.sub.baoxian.mapper.ProductDao;
import com.sub.baoxian.productInfoService.Ao.ProductCalculateAo;
import com.sub.baoxian.productInfoService.Ao.ProductIntroAO;
import com.sub.baoxian.productInfoService.DTO.DiscountDTO;
import com.sub.baoxian.productInfoService.DTO.ProductDisplayDTO;
import com.sub.baoxian.productInfoService.DTO.ProfitCalculationRequestDTO;
import com.sub.baoxian.productInfoService.utils.FinancialCalculator;
import com.sub.baoxian.productInfoService.utils.FinancialCalculationsV3;
import com.sub.baoxian.productInfoService.ProductInfoProviderSecond;
import com.sub.baoxian.productInfoService.ProductInfoProviderService;
import com.sub.baoxian.productInfoService.SavingDynamicCalService;
import com.sub.baoxian.productInfoService.entity.*;
import com.sub.baoxian.productInfoService.utils.InsuranceCost;
import com.sub.baoxian.productInfoService.utils.PageResult;
import com.sub.baoxian.productInfoService.BO.CalculationDataBO;
import com.sub.baoxian.productInfoService.vo.CalculationDataVO;
import com.sub.baoxian.service.product.FileImportService;
import com.sub.baoxian.service.product.ProductTransformer;
import com.sub.baoxian.util.ProductFeature;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.sub.baoxian.productInfoService.utils.FinancialCalculationsV3.calculateIRR;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

@SpringBootTest
public class TestSysFunc {
    @Autowired
    ProductDao productDao;
    @Autowired
    KnowledgeBaseMapper knowledgeBaseMapper;
    @Test
    public void testSysFunc(){
//        List<ProductPo> list  =productDao.getProductById();
//        for(ProductPo a:list) {
//            System.out.println(a.getMainType());
//        }
//        System.out.println("test");
    }

    @Test
    public void testSysFunc2() throws Exception {
//        knowledgeBaseMapper.getProductById();
        ProductTransformer c = new ProductTransformer();
//        List<ProductPo> list  =productDao.getProductById(2);


    }
    @Test
    public void testSysFunc3() throws Exception {
        ProductTransformer c = new ProductTransformer();
//        List<ProductPo> list  =productDao.getProductById(2);

    }
    @Test
    public void testSysFunc4() throws Exception {
        if(ProductFeature.contains("subType")){
            System.out.println("true");
        } else {
            System.out.println("false");
        }

    }

    @Autowired
    ProductInfoProviderService ppd;
    @Test
    public void testSysFunc5() throws Exception {
//        System.out.println(ppd.getInfoOfProducts(14));
//        List<ProductPo> list  =productDao.getProductById(2);
        System.out.println(ppd.getBasicInfoOfProducts(1,10,"","儲蓄",null,null));
    }

    @Test
    public void TestSearchFunc() throws Exception {
//        List<ProductAttributeAO> list = ppd.(60);
//        for(ProductAttributeAO a:list) {
//            System.out.println(a);
//        }
    }
    //test paging
    @Test
    public void TestSearchFunc2() throws Exception {
        System.out.println(ppd.getSingleAttributeOfProduct(14));
    }

    @Test
    public void TestSearchFunc3() throws Exception {
        System.out.println(ppd.getBasicInfoOfProducts(1,10,"中國人壽","",null,null));
    }
    // test search function
    @Autowired
    ProductInfoProviderService productInfoProviderService;
    @Test
    public void TestSearchFunc4() throws Exception {
//        ProductAttributeAO list = productInfoProviderService.getIRRAndDividendOfProduct(54);
        ProductIntroAO a = productInfoProviderService.getIntroInfoOfProducts(2);
        System.out.println(a);

    }
    @Test
    public void TestSearchFunc5() throws Exception {
        List<Double> cashFlows = Arrays.asList(-32000.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 360115.4);

        double irr = FinancialCalculator.calculateIRR(cashFlows);
        System.out.println("IRR: " + irr + "%");

//        int signletonVal = 84;
//        List<Integer> netOutflows = Arrays.asList(-3032, -3032, -3032);
//        List<Integer> years = Arrays.asList(1, 2,3);
//        int currentYear = 3;

//        double simpleRate = FinancialCalculator.calculateSimpleInterestRate(signletonVal, netOutflows, years, currentYear);
//        System.out.println("Single Interest Rate: " + simpleRate + "%");
    }
    @Autowired
    InsuranceCost insuranceCost;
    @Test
    public void TestSearchFunc6() throws Exception {
//        List<InsuranceFeePo> list = insuranceCost.getCalculationProducts("國壽海外",null);
//
//        for(InsuranceFeePo a:list) {
//            System.out.println(a);
//        }
//        List<String> lisr2 = insuranceCost.getCalculationType();
//        for(String a:lisr2) {
//            System.out.println(a);
//        }
        float number  =  insuranceCost.getInsuranceCost(1, 25, 1000000, 1);
        System.out.println(number);
    }
    @Test
    public void test() throws JsonProcessingException {

        System.out.println(productInfoProviderService.getSpecificYearIrr(73,10, 20));
//        System.out.println(productDao.getProductIRRAndDividend(73));
    }
    @Test
    public void test2() throws JsonProcessingException {
        System.out.println(productInfoProviderSecond.getRealizationRateProductStar());
    }


    @Autowired
    ProductInfoProviderSecond productInfoProviderSecond;
    @Test
    public void test3() throws JsonProcessingException {
        System.out.println(productInfoProviderSecond.getProductRealizationDetails(1,10,"優暇人生延期年金計劃"));
    }
    @Test
    public void test5() throws JsonProcessingException {
//        System.out.println(productInfoProviderSecond.getProductRealizationDetailsRe(1,10,null));
    }
    @Test
    public void test4() throws JsonProcessingException {
//        List<String> list = productDao.getRealizationProductsName1();
        List<ProductAttributePo> list11  = productDao.getProductIRRAndDividend111111();
        Pattern pattern = Pattern.compile("^\\(([A-Za-z]\\d+)\\)");

        for(ProductAttributePo a:list11) {
            ProductAttributeAO p= productInfoProviderService.getIRRAndDividendOfProduct(a.getProductId());

            JSONObject p1 = p.getProductIntro();
            JSONObject productInfo  = p1.getJSONObject("product_info");
            String summary = productInfo.getString("summary");

            JSONArray features = productInfo.getJSONArray("features");
            System.out.println(features);

            String table = p1.getString("policy_details");
            if (p.getIRR() != null && !p.getIRR().isEmpty()) {
                for (Object obj : p.getIRR()) {
                    JSONObject b = (JSONObject) obj;
                    InsurancePostPo aa = new InsurancePostPo();
                    String productName = b.getString("productName");
                    System.out.println(productName);

                    Matcher matcher = pattern.matcher(productName);
                    if (matcher.find()) {
                        String productCode = matcher.group(1);
                        aa.setProductName(productName);
                        aa.setProductCode(productCode);
//                    System.out.println(productCode);
                        aa.setIntroduction(summary);
                        aa.setProductFeature(features.toString());
                        aa.setProductBasic(String.valueOf(table));
//                    System.out.println(table);
                        aa.setInterestRate(((JSONObject) obj).toString());

                    } else {
                        System.out.println("No match found for product name: " + productName);
                    }
                }
            } else {
                InsurancePostPo t = new InsurancePostPo();
                t.setProductName(a.getName());
                t.setProductFeature(features.toString());
                t.setProductBasic(String.valueOf(table));
                t.setIntroduction(summary);
                productDao.insertss(t);
            }
        }
    }

    @Test
    public void test6() throws JsonProcessingException {
        PageResult<InsuranceFeePo> list11  = insuranceCost.getCalculationProducts(1,10,null,null,null,"香港");
        System.out.println(list11);
    }
    @Test
    public void test7() throws JsonProcessingException {
        ProductDisplayDTO p = new ProductDisplayDTO();
//        p.setAge(40);
//        p.setType("SAVINGS");
        p.setRegion("香港");
//        p.setInsurer("中國人壽");
//        p.setCurrency("USD");
        p.setPageNum(1);
        p.setPageSize(10);
        PageResult<ProductCalDisplayPo> list11  = productInfoProviderService.ProductsOfCashValue(p);
        System.out.println(list11);
    }
    @Test
    public void test7434() throws JsonProcessingException {
        System.out.println(productInfoProviderService.productCurrencyList());
    }
    @Test
    public void test8() throws JsonProcessingException {
        ProductCalculateAo list11  = productInfoProviderService.getProfitCalculate(3, BigDecimal.valueOf(100000),10,20);
        System.out.println(list11);
    }


    // 測試 productId: 1 ~ 20, 46 ~ 56
    @ParameterizedTest
    @ValueSource(ints = {
            1, 2, 3, 4, 5, 6, 7, 8, 9, 10,
            11, 12, 13, 14, 15, 16,
            46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56
    })
    public void testGetProfitCalculate_DoesNotThrow(int productId) {
        // Arrange
        BigDecimal annualPremium = BigDecimal.valueOf(100_000);
        int paymentTerm = 10;
        int coverageTerm = 20;
        List<DiscountDTO> list = new ArrayList<>();
        list.add(new DiscountDTO(1, 0.02));

        // Act + Assert：确保不会抛出异常
        assertDoesNotThrow(() -> {
            CalculationDataBO lit = calMapper.profitCalculator(50, 30, "standard");
            savingDynamicCalService.getDynamicSaving(list, 10000.0, 20, lit);
            // 如果还有其他操作也可以继续写在这里
        });
    }
    @Autowired
    ProductCalMapper calMapper;
    @Test
    public void test9() throws JsonProcessingException {
//        ProductCalculateAo list11  = productInfoProviderService.getProfitCalculate(5, BigDecimal.valueOf(100000),60,20);
//        System.out.println(list11);
        CalculationDataBO lit =  calMapper.profitCalculator(3,20,"standard");
        System.out.println(lit);
    }
    @Autowired
    SavingDynamicCalService savingDynamicCalService;
    @Test
    public void test10() {
        DiscountDTO d = new DiscountDTO();
        List<DiscountDTO> list = new ArrayList<>();
        list.add(d);
        List<Double> net = new ArrayList<>();
        ProfitCalculationRequestDTO profitCalculationRequestDTO = new ProfitCalculationRequestDTO();
        profitCalculationRequestDTO.setProductId(219);
        profitCalculationRequestDTO.setDiscounts(list);
//        profitCalculationRequestDTO.setPremium(40000.0);
        profitCalculationRequestDTO.setStartAge(30);
        profitCalculationRequestDTO.setType("standard");

        CalculationDataVO a = savingDynamicCalService.classifyAgeGetProduct(profitCalculationRequestDTO);
//        CalculationDataVO b = savingDynamicCalService.classifyAgeGetProduct(profitCalculationRequestDTO);
        for(int i = 0; i < a.getCalculationBaseDataItemBOList().size(); i++) {
            CalculationDataVO.CalculationBaseDataItemBO itemVO = a.getCalculationBaseDataItemBOList().get(i);
//            CalculationDataVO.CalculationBaseDataItemBO itemVO2 = b.getCalculationBaseDataItemBOList().get(i);
            System.out.println( itemVO.getSurrender());
        }
    }

    @Test
    public void testIRRCalculation() {
        // 测试IRR计算 - 模拟47年的现金流
        List<BigDecimal> cashFlowsFor6_64 = new ArrayList<>();
        cashFlowsFor6_64.add(new BigDecimal("-40000.0"));
        for (int i = 0; i < 46; i++) { // 46 zeros for t=1 to t=46
            cashFlowsFor6_64.add(BigDecimal.ZERO);
        }
        cashFlowsFor6_64.add(new BigDecimal("821942.2")); // At t=47
// This list has 1 + 46 + 1 = 48 elements.
        System.out.println("IRR (for t=47 final flow): " + calculateIRR(cashFlowsFor6_64));
    }
    @Autowired
    FileImportService fileImportService;
    @Test
    public void testIRRCalculation2() {
        fileImportService.importFileNames();
    }

}
