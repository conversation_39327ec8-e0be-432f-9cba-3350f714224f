package com.sub.baoxian;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import dev.langchain4j.model.chat.StreamingChatModel;
import dev.langchain4j.model.embedding.EmbeddingModel;

@SpringBootTest
public class langchain {

        // @Autowired
        // private AnalyzeAssistant assistant;

        @Autowired
        private StreamingChatModel streamingChatModel;

        @Autowired
        private EmbeddingModel embeddingModel;

        // @Test
        // public void test() throws Exception {
        // // 创建CountDownLatch，初始计数为1
        // CountDownLatch latch = new CountDownLatch(1);

        // // Assistant assistant = AiServices.create(Assistant.class,
        // streamingChatModel);

        // System.out.println("开始发送请求...");
        // TokenStream tokenStream = assistant.analyzePDF("北京有什么好吃的？");

        // tokenStream.onPartialResponse(response -> {
        // System.out.print(response);
        // })
        // .onCompleteResponse(response -> {
        // System.out.println("完整响应: " + response);
        // // 完成时减少计数
        // latch.countDown();
        // })
        // .onError(throwable -> {
        // System.err.println("发生错误: " + throwable.getMessage());
        // throwable.printStackTrace();
        // // 错误时也减少计数
        // latch.countDown();
        // })
        // .start();

        // // 等待异步操作完成，最多等待30秒
        // System.out.println("等待响应...");
        // boolean completed = latch.await(30, TimeUnit.SECONDS);

        // if (!completed) {
        // System.err.println("操作超时，未在30秒内完成");
        // }
        // }

        // @Test
        // public void test3() throws Exception {

        //         Document document = ClassPathDocumentLoader.loadDocument("/Users/<USER>/Desktop/123.pdf",
        //                         new ApachePdfBoxDocumentParser());
        //         InMemoryEmbeddingStore<TextSegment> embeddingStore = new InMemoryEmbeddingStore<>();

        //         // 2. 选用分割器，比如按段落且每块不超过1024 token
        //         DocumentByParagraphSplitter splitter = new DocumentByParagraphSplitter(1024, 0);

        //         // 3. 将原始 Document 切分
        //         List<TextSegment> segments = splitter.split(document);

        //         // 4. 对所有 TextSegment 批量处理
        //         EmbeddingStoreIngestor ingestor = EmbeddingStoreIngestor.builder()
        //                         .embeddingModel(embeddingModel)
        //                         .embeddingStore(embeddingStore)
        //                         .build();

        //         List<Document> documents = new ArrayList<>();
        //         for (TextSegment segment : segments) {
        //                 System.out.println(segment.text());
        //                 Document chunkDoc = Document.from(segment.text(), segment.metadata());
        //                 documents.add(chunkDoc);
        //         }
        //         // 批量处理所有文档
        //         ingestor.ingest(documents);

        //         AssistantEmbedding embeddingAssistant = AiServices.builder(AssistantEmbedding.class)
        //                         .streamingChatModel(streamingChatModel)
        //                         .contentRetriever(EmbeddingStoreContentRetriever.builder()
        //                                         .embeddingStore(embeddingStore)
        //                                         .embeddingModel(embeddingModel)
        //                                         .build())
        //                         .build();

                // CountDownLatch latch = new CountDownLatch(1);
                // TokenStream result = embeddingAssistant.chat("到70岁我的保证金额是多少");
                // result.onPartialResponse(response -> {
                // System.out.print(response);
                // })
                // .onCompleteResponse(response -> {
                // })
                // .onError(throwable -> {
                // System.err.println("发生错误: " + throwable.getMessage());
                // throwable.printStackTrace();
                // })
                // .start();
                // latch.await(30, TimeUnit.SECONDS);
        // }

        @Test
        public void test4() throws Exception {
                System.out.println(System.currentTimeMillis());
        }

}
