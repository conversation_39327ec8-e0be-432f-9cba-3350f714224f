import { get, post, put, del } from '@/utils/request';

/**
 * 管理员登录
 * @param {Object} data 登录数据
 * @returns {Promise} 登录结果
 */
export function login(data) {
  return post('/backend/admin/login', data);
}

/**
 * 获取当前登录管理员信息
 * @returns {Promise} 管理员信息
 */
export function getAdminInfo() {
  return get('/backend/admin/info');
}

/**
 * 管理员登出
 * @returns {Promise} 登出结果
 */
export function logout() {
  return post('/backend/admin/logout');
}

/**
 * 检查登录状态
 * @returns {Promise} 登录状态
 */
export function checkLogin() {
  return get('/backend/admin/checkLogin');
}

/**
 * 分页查询管理员列表
 * @param {Object} params 查询参数
 * @returns {Promise} 分页结果
 */
export function getAdminPage(params) {
  return get('/backend/admin/page', params);
}

/**
 * 获取管理员详情
 * @param {Number} id 管理员ID
 * @returns {Promise} 管理员详情
 */
export function getAdmin(id) {
  return get(`/backend/admin/${id}`);
}

/**
 * 新增管理员
 * @param {Object} data 管理员数据
 * @returns {Promise} 操作结果
 */
export function addAdmin(data) {
  return post('/backend/admin', data);
}

/**
 * 更新管理员
 * @param {Object} data 管理员数据
 * @returns {Promise} 操作结果
 */
export function updateAdmin(data) {
  return put('/backend/admin', data);
}

/**
 * 删除管理员
 * @param {Number} id 管理员ID
 * @returns {Promise} 操作结果
 */
export function deleteAdmin(id) {
  return del(`/backend/admin/${id}`);
}

/**
 * 批量删除管理员
 * @param {Array} ids 管理员ID数组
 * @returns {Promise} 操作结果
 */
export function batchDeleteAdmins(ids) {
  return del('/backend/admin/batch', { ids });
}

/**
 * 修改管理员状态
 * @param {Number} id 管理员ID
 * @param {Number} status 状态值
 * @returns {Promise} 操作结果
 */
export function updateAdminStatus(id, status) {
  return put(`/backend/admin/status/${id}`, { status });
}

/**
 * 重置管理员密码
 * @param {Number} id 管理员ID
 * @param {String} newPassword 新密码
 * @returns {Promise} 操作结果
 */
export function resetAdminPassword(id, newPassword) {
  return put(`/backend/admin/resetPassword/${id}`, { newPassword });
}



