import { get, post, put, del } from '@/utils/request';

/**
 * 分页查询分类列表
 * @param {Object} params 查询参数
 * @returns {Promise} 分页结果
 */
export function getCategoryPage(params) {
  return get('backend/category/page', params);
}

/**
 * 获取所有分类列表
 * @param {Object} params 查询参数（可选）
 * @returns {Promise} 分类列表
 */
export function getCategoryList(params) {
  return get('backend/category/list', params);
}

/**
 * 获取分类详情
 * @param {Number} id 分类ID
 * @returns {Promise} 分类详情
 */
export function getCategoryById(id) {
  return get(`backend/category/${id}`);
}

/**
 * 新增分类
 * @param {Object} data 分类信息
 * @returns {Promise} 操作结果
 */
export function saveCategory(data) {
  return post('backend/category', data);
}

/**
 * 修改分类
 * @param {Object} data 分类信息
 * @returns {Promise} 操作结果
 */
export function updateCategory(data) {
  return put('backend/category', data);
}

/**
 * 删除分类
 * @param {Number} id 分类ID
 * @returns {Promise} 操作结果
 */
export function deleteCategory(id) {
  return del(`backend/category/${id}`);
}

