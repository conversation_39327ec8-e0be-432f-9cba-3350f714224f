import { get, post } from '@/utils/request';
import axios from 'axios';

/**
 * 分页查询保险公司列表（管理端）
 * @param {Object} params 查询参数
 * @param {number} params.pageNum 页码（默认1）
 * @param {number} params.pageSize 每页数量（默认10）
 * @param {string} params.name 公司名称（可选，用于模糊查询）
 * @param {string} params.code 公司代码（可选，用于精确查询）
 * @param {string} params.region 地区（可选，用于精确查询）
 * @param {string} params.status 公司状态（可选，用于精确查询）
 * @param {string} params.keyword 关键词搜索（可选）
 * @returns {Promise} 分页结果
 */
export function getCompanyPage(params) {
  return get('backend/ins-company/page', params);
}

/**
 * 获取保险公司列表（不分页）
 * @param {string} search 搜索关键词（可选）
 * @param {string} region 地区（可选）
 * @returns {Promise} 公司列表
 */
export function getCompanyList(search, region) {
  const params = {};
  if (search) params.search = search;
  if (region) params.region = region;
  return get('backend/ins-company/list', params);
}

/**
 * 根据ID获取保险公司详情
 * @param {number} id 公司ID
 * @returns {Promise} 公司详情
 */
export function getCompanyById(id) {
  return get(`backend/ins-company/${id}`);
}

/**
 * 根据代码获取保险公司详情（包含关联数据）
 * @param {string} code 公司代码
 * @returns {Promise} 公司详情
 */
export function getCompanyDetail(code) {
  return get(`backend/ins-company/detail/${code}`);
}

/**
 * 创建保险公司
 * @param {Object} data 公司信息
 * @returns {Promise} 操作结果
 */
export function createCompany(data) {
  return post('backend/ins-company', data);
}

/**
 * 更新保险公司
 * @param {number} id 公司ID
 * @param {Object} data 公司信息
 * @returns {Promise} 操作结果
 */
export function updateCompany(id, data) {
  return post(`backend/ins-company/update/${id}`, data);
}

/**
 * 删除保险公司
 * @param {number} id 公司ID
 * @returns {Promise} 操作结果
 */
export function deleteCompany(id) {
  return post(`backend/ins-company/delete/${id}`);
}

/**
 * 批量删除保险公司
 * @param {Array<number>} ids 公司ID数组
 * @returns {Promise} 操作结果
 */
export function batchDeleteCompanies(ids) {
  return post('backend/ins-company/batch/delete', { ids });
}

// ==================== 信用评级管理 ====================

/**
 * 获取保险公司信用评级列表
 * @param {string} code 公司代码
 * @returns {Promise} 信用评级列表
 */
export function getCreditRatings(code) {
  return get(`backend/ins-company/${code}/credit-ratings`);
}

/**
 * 添加信用评级
 * @param {Object} data 信用评级信息
 * @returns {Promise} 操作结果
 */
export function addCreditRating(data) {
  return post('backend/ins-company/credit-rating', data);
}

/**
 * 更新信用评级
 * @param {number} id 评级ID
 * @param {Object} data 信用评级信息
 * @returns {Promise} 操作结果
 */
export function updateCreditRating(id, data) {
  return post(`backend/ins-company/credit-rating/update/${id}`, data);
}

/**
 * 删除信用评级
 * @param {number} id 评级ID
 * @returns {Promise} 操作结果
 */
export function deleteCreditRating(id) {
  return post(`backend/ins-company/credit-rating/delete/${id}`);
}

// ==================== 文件管理 ====================

/**
 * 获取保险公司文件列表
 * @param {string} code 公司代码
 * @returns {Promise} 文件列表
 */
export function getCompanyFiles(code) {
  return get(`backend/ins-company/${code}/files`);
}

/**
 * 上传文件
 * @param {File} file 文件对象
 * @param {string} code 公司代码
 * @param {string} author 上传者
 * @returns {Promise} 操作结果
 */
export function uploadCompanyFile(file, code, author) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('code', code);
  formData.append('author', author);

  // 使用axios直接调用，因为需要设置特殊的Content-Type
  const service = axios.create({
    baseURL: import.meta.env.VITE_API_BASE_URL + '/api',
    timeout: 15000,
  });

  // 添加token
  const token = localStorage.getItem('token');
  const headers = {
    'Content-Type': 'multipart/form-data',
  };
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  return service.post('backend/ins-company/file/upload', formData, { headers });
}

/**
 * 添加文件（通过DTO）
 * @param {Object} data 文件信息
 * @returns {Promise} 操作结果
 */
export function addCompanyFile(data) {
  return post('backend/ins-company/file', data);
}

/**
 * 获取文件详情
 * @param {number} id 文件ID
 * @returns {Promise} 文件详情
 */
export function getFileById(id) {
  return get(`backend/ins-company/file/${id}`);
}

/**
 * 更新文件信息
 * @param {number} id 文件ID
 * @param {Object} data 文件信息
 * @returns {Promise} 操作结果
 */
export function updateCompanyFile(id, data) {
  return post(`backend/ins-company/file/update/${id}`, data);
}

/**
 * 删除文件
 * @param {number} id 文件ID
 * @returns {Promise} 操作结果
 */
export function deleteCompanyFile(id) {
  return post(`backend/ins-company/file/delete/${id}`);
}

// ==================== 链接管理 ====================

/**
 * 获取保险公司链接列表
 * @param {string} code 公司代码
 * @returns {Promise} 链接列表
 */
export function getCompanyLinks(code) {
  return get(`backend/ins-company/${code}/links`);
}

/**
 * 添加链接
 * @param {Object} data 链接信息
 * @returns {Promise} 操作结果
 */
export function addCompanyLink(data) {
  return post('backend/ins-company/link', data);
}

/**
 * 更新链接
 * @param {number} id 链接ID
 * @param {Object} data 链接信息
 * @returns {Promise} 操作结果
 */
export function updateCompanyLink(id, data) {
  return post(`backend/ins-company/link/update/${id}`, data);
}

/**
 * 删除链接
 * @param {number} id 链接ID
 * @returns {Promise} 操作结果
 */
export function deleteCompanyLink(id) {
  return post(`backend/ins-company/link/delete/${id}`);
}

