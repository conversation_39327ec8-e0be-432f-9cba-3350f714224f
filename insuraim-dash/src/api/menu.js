import { get, post, put, del } from "@/utils/request";

/**
 * 分页查询菜单列表
 * @param {Object} params 查询参数
 * @param {number} params.pageNum 页码（默认1）
 * @param {number} params.pageSize 每页数量（默认10）
 * @param {string} params.keyword 关键词搜索（可选）
 * @returns {Promise} 分页结果
 */
export function getMenuPage(params) {
  return get("backend/menu/page", params);
}

/**
 * 获取所有菜单列表
 * @returns {Promise} 菜单列表
 */
export function getMenuList() {
  return get("backend/menu/list");
}

/**
 * 获取菜单树形结构
 * @returns {Promise} 菜单树
 */
export function getMenuTree() {
  return get("backend/menu/tree");
}

/**
 * 获取菜单详情
 * @param {Number} id 菜单ID
 * @returns {Promise} 菜单详情
 */
export function getMenu(id) {
  return get(`backend/menu/${id}`);
}

/**
 * 添加菜单
 * @param {Object} data 菜单数据
 * @param {string} data.name 菜单名称
 * @param {number} data.parentId 父菜单ID（0为顶级菜单）
 * @param {number} data.orderRank 显示顺序
 * @param {string} data.path 路由地址
 * @param {string} data.component 组件路径
 * @param {string} data.routeName 路由名称
 * @param {number} data.status 菜单状态（0停用 1启用）
 * @param {string} data.icon 菜单图标
 * @param {string} data.remark 备注
 * @returns {Promise} 操作结果
 */
export function addMenu(data) {
  return post("backend/menu", data);
}

/**
 * 更新菜单
 * @param {Object} data 菜单数据
 * @param {number} data.id 菜单ID
 * @param {string} data.name 菜单名称
 * @param {number} data.parentId 父菜单ID
 * @param {number} data.orderRank 显示顺序
 * @param {string} data.path 路由地址
 * @param {string} data.component 组件路径
 * @param {string} data.routeName 路由名称
 * @param {number} data.status 菜单状态（0停用 1启用）
 * @param {string} data.icon 菜单图标
 * @param {string} data.remark 备注
 * @returns {Promise} 操作结果
 */
export function updateMenu(data) {
  return put("backend/menu", data);
}

/**
 * 删除菜单
 * @param {Number} id 菜单ID
 * @returns {Promise} 操作结果
 */
export function deleteMenu(id) {
  return del(`backend/menu/${id}`);
}

/**
 * 批量删除菜单
 * @param {Array} ids 菜单ID数组
 * @returns {Promise} 操作结果
 */
export function batchDeleteMenus(ids) {
  return del("backend/menu/batch", ids);
}

/**
 * 为角色分配菜单
 * @param {Number} roleId 角色ID
 * @param {Array} menuIds 菜单ID数组
 * @returns {Promise} 操作结果
 */
export function assignMenusToRole(roleId, menuIds) {
  return post(`backend/menu/role/${roleId}`, menuIds);
}

/**
 * 获取角色已分配的菜单ID列表
 * @param {Number} roleId 角色ID
 * @returns {Promise} 菜单ID列表
 */
export function getRoleMenuIds(roleId) {
  return get(`backend/menu/role/${roleId}`);
}
