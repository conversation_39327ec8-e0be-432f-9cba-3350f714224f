import { get, post, put, del } from '@/utils/request';

/**
 * 分页查询组织列表
 * @param {Object} params 查询参数
 * @returns {Promise} 分页结果
 */
export function getOrganizationPage(params) {
  return get('/backend/organization/page', params);
}

/**
 * 获取组织树结构
 * @returns {Promise} 组织树
 */
export function getOrganizationTree() {
  return get('backend/organization/tree');
}

/**
 * 根据类型获取组织列表
 * @param {String} type 组织类型
 * @returns {Promise} 组织列表
 */
export function getOrganizationsByType(type) {
  return get(`backend/organization/list/${type}`);
}

/**
 * 获取组织详情
 * @param {Number} id 组织ID
 * @returns {Promise} 组织详情
 */
export function getOrganization(id) {
  return get(`backend/organization/${id}`);
}

/**
 * 添加组织
 * @param {Object} data 组织数据
 * @returns {Promise} 操作结果
 */
export function addOrganization(data) {
  return post('backend/organization', data);
}

/**
 * 更新组织
 * @param {Object} data 组织数据
 * @returns {Promise} 操作结果
 */
export function updateOrganization(data) {
  return put('backend/organization', data);
}

/**
 * 删除组织
 * @param {Number} id 组织ID
 * @returns {Promise} 操作结果
 */
export function deleteOrganization(id) {
  return del(`backend/organization/${id}`);
}

/**
 * 批量删除组织
 * @param {Array} ids 组织ID数组
 * @returns {Promise} 操作结果
 */
export function batchDeleteOrganizations(ids) {
  return post('backend/organization/batch', ids);
} 