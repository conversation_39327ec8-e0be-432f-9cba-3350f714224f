import { get, post, put, del } from '@/utils/request';

/**
 * 分页查询权限列表
 * @param {Object} params 查询参数
 * @returns {Promise} 分页结果
 */
export function getPermissionPage(params) {
  return get('/backend/permission/page', params);
}

/**
 * 获取所有权限列表
 * @returns {Promise} 权限列表
 */
export function getPermissionList() {
  return get('/backend/permission/list');
}

/**
 * 获取权限详情
 * @param {Number} id 权限ID
 * @returns {Promise} 权限详情
 */
export function getPermission(id) {
  return get(`/backend/permission/${id}`);
}

/**
 * 添加权限
 * @param {Object} data 权限数据
 * @returns {Promise} 操作结果
 */
export function addPermission(data) {
  return post('/backend/permission', data);
}

/**
 * 更新权限
 * @param {Object} data 权限数据
 * @returns {Promise} 操作结果
 */
export function updatePermission(data) {
  return put('/backend/permission', data);
}

/**
 * 删除权限
 * @param {Number} id 权限ID
 * @returns {Promise} 操作结果
 */
export function deletePermission(id) {
  return del(`/backend/permission/${id}`);
}

/**
 * 批量删除权限
 * @param {Array} ids 权限ID数组
 * @returns {Promise} 操作结果
 */
export function batchDeletePermissions(ids) {
  return del('/backend/permission/batch', { ids });
}

/**
 * 获取角色的权限列表
 * @param {Number} roleId 角色ID
 * @returns {Promise} 权限列表
 */
export function getPermissionsByRoleId(roleId) {
  return get(`/backend/permission/role/${roleId}`);
}

/**
 * 为角色分配权限
 * @param {Number} roleId 角色ID
 * @param {Array} permissionIds 权限ID数组
 * @returns {Promise} 操作结果
 */
export function assignPermissions(roleId, permissionIds) {
  return post(`/backend/permission/role/${roleId}`, permissionIds);
}

/**
 * 取消角色的某个权限
 * @param {Number} roleId 角色ID
 * @param {Number} permissionId 权限ID
 * @returns {Promise} 操作结果
 */
export function removePermissionFromRole(roleId, permissionId) {
  return del(`/backend/permission/role/${roleId}/${permissionId}`);
} 