import { get, post, put, del } from "@/utils/request";

/**
 * 分页查询产品列表
 * @param {Object} params 查询参数
 * @param {number} params.pageNum 页码（默认1）
 * @param {number} params.pageSize 每页数量（默认10）
 * @param {string} params.productName 产品名称（可选，模糊查询）
 * @param {string} params.companyCode 保险公司代码（可选）
 * @param {string} params.companyName 保险公司名称（可选，模糊查询）
 * @param {string} params.categoryCode 产品类别代码（可选）
 * @param {string} params.categoryName 产品类别名称（可选）
 * @param {string} params.region 销售地区（可选）
 * @param {number} params.status 启用状态（可选）
 * @param {number} params.isPrepayment 是否允许预缴（可选）
 * @param {string} params.keyword 关键词搜索（可选，搜索产品名称或公司名称）
 * @returns {Promise} 分页结果
 */
export function getProductPage(params) {
  return get("common/ins-product/page", params);
}

/**
 * 获取产品列表（不分页）
 * @param {number} categoryId 分类ID（可选）
 * @returns {Promise} 产品列表
 */
export function getProductList(categoryId) {
  const params = categoryId ? { categoryId } : {};
  return get("common/ins-product/list", params);
}

/**
 * 获取产品详情
 * @param {number} id 产品ID
 * @returns {Promise} 产品详情
 */
export function getProductDetail(id) {
  return get(`common/ins-product/${id}`);
}

/**
 * 保存产品（新增）
 * @param {Object} data 产品信息
 * @returns {Promise} 操作结果
 */
export function saveProduct(data) {
  // 注意：此API接口需要后端实现，当前InsProductController中暂未提供
  return Promise.reject(new Error("新增产品功能暂未实现"));
}

/**
 * 更新产品
 * @param {Object} data 产品信息
 * @returns {Promise} 操作结果
 */
export function updateProduct(data) {
  // 注意：此API接口需要后端实现，当前InsProductController中暂未提供
  return Promise.reject(new Error("更新产品功能暂未实现"));
}

/**
 * 删除产品
 * @param {number} id 产品ID
 * @returns {Promise} 操作结果
 */
export function deleteProduct(id) {
  // 注意：此API接口需要后端实现，当前InsProductController中暂未提供
  return Promise.reject(new Error("删除产品功能暂未实现"));
}

/**
 * 批量更新产品状态
 * @param {Array} ids 产品ID列表
 * @param {number} status 状态值（0-下线，1-上线）
 * @returns {Promise} 操作结果
 */
export function batchUpdateProductStatus(ids, status) {
  // 注意：此API接口需要后端实现，当前InsProductController中暂未提供
  return Promise.reject(new Error("批量更新产品状态功能暂未实现"));
}

/**
 * 获取产品统计数据
 * @returns {Promise} 产品统计数据
 */
export function getProductStats() {
  // 注意：此API接口需要后端实现，当前InsProductController中暂未提供
  return Promise.reject(new Error("获取产品统计数据功能暂未实现"));
}

// ==================== 产品详情管理相关接口 ====================

/**
 * 创建标题
 * @param {Object} data 标题信息
 * @param {number} data.categoryId 分类ID
 * @param {number} data.parentId 父标题ID（可选）
 * @param {string} data.titleName 标题名称
 * @param {number} data.rank 排序
 * @returns {Promise} 创建结果
 */
export function createTitle(data) {
  return post("common/product-detail/titles", data);
}

/**
 * 更新标题
 * @param {Object} data 标题信息
 * @param {number} data.id 标题ID
 * @param {number} data.categoryId 分类ID
 * @param {number} data.parentId 父标题ID（可选）
 * @param {string} data.titleName 标题名称
 * @param {number} data.rank 排序
 * @returns {Promise} 更新结果
 */
export function updateTitle(data) {
  return post("common/product-detail/titles/update", data);
}

/**
 * 删除标题
 * @param {number} id 标题ID
 * @returns {Promise} 删除结果
 */
export function deleteTitle(id) {
  return post(`common/product-detail/titles/${id}/delete`);
}

/**
 * 根据ID查询标题
 * @param {number} id 标题ID
 * @returns {Promise} 标题详情
 */
export function getTitleById(id) {
  return get(`common/product-detail/titles/${id}`);
}

/**
 * 根据分类ID查询标题树形结构
 * @param {number} categoryId 分类ID
 * @returns {Promise} 标题树形结构
 */
export function getTitleTree(categoryId) {
  return get("common/product-detail/titles/tree", { categoryId });
}

/**
 * 分页查询标题
 * @param {Object} params 查询参数
 * @param {number} params.categoryId 分类ID（可选）
 * @param {number} params.parentId 父标题ID（可选）
 * @param {string} params.titleName 标题名称（可选）
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 页大小
 * @returns {Promise} 分页结果
 */
export function getTitlePage(params) {
  return get("common/product-detail/titles", params);
}

/**
 * 保存内容（创建或更新）
 * @param {Object} data 内容信息
 * @param {number} data.id 内容ID（更新时需要）
 * @param {number} data.productId 产品ID
 * @param {number} data.titleId 标题ID
 * @param {string} data.contentValue 内容值
 * @returns {Promise} 保存结果
 */
export function saveContent(data) {
  return post("common/product-detail/contents", data);
}

/**
 * 删除内容
 * @param {number} id 内容ID
 * @returns {Promise} 删除结果
 */
export function deleteContent(id) {
  return post(`common/product-detail/contents/${id}/delete`);
}

/**
 * 根据产品ID和标题ID删除内容
 * @param {number} productId 产品ID
 * @param {number} titleId 标题ID
 * @returns {Promise} 删除结果
 */
export function deleteContentByProductIdAndTitleId(productId, titleId) {
  return post(
    `common/product-detail/contents/delete-by-product-title?productId=${productId}&titleId=${titleId}`
  );
}

/**
 * 根据产品ID查询内容列表
 * @param {number} productId 产品ID
 * @returns {Promise} 内容列表
 */
export function getContentsByProductId(productId) {
  return get("common/product-detail/contents/by-product", { productId });
}

/**
 * 根据标题ID查询内容列表
 * @param {number} titleId 标题ID
 * @returns {Promise} 内容列表
 */
export function getContentsByTitleId(titleId) {
  return get("common/product-detail/contents/by-title", { titleId });
}

/**
 * 分页查询内容
 * @param {Object} params 查询参数
 * @param {number} params.productId 产品ID（可选）
 * @param {number} params.titleId 标题ID（可选）
 * @param {string} params.contentValue 内容值（可选）
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 页大小
 * @returns {Promise} 分页结果
 */
export function getContentPage(params) {
  return get("common/product-detail/contents", params);
}

/**
 * 根据分类ID和产品ID查询完整的产品详情树形结构
 * @param {number} categoryId 分类ID
 * @param {number} productId 产品ID
 * @returns {Promise} 产品详情树形结构
 */
export function getProductDetailTree(categoryId, productId) {
  return get("common/product-detail/tree", { categoryId, productId });
}

/**
 * 根据分类ID和产品ID查询原始的产品详情树形结构
 * @param {number} categoryId 分类ID
 * @param {number} productId 产品ID
 * @returns {Promise} 原始产品详情树形结构
 */
export function getProductDetailTreeOriginal(categoryId, productId) {
  return get("common/product-detail/tree/original", { categoryId, productId });
}

/**
 * 根据分类ID和产品ID查询真正的产品详情树形结构（用于内容管理）
 * @param {number} categoryId 分类ID
 * @param {number} productId 产品ID
 * @returns {Promise} 真正的产品详情树形结构
 */
export function getProductContentTree(categoryId, productId) {
  // 暂时使用现有的API，后续可能需要后端提供专门的API
  return get("common/product-detail/tree", { categoryId, productId });
}
