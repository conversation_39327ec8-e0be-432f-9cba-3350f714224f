import { get, post, put, del } from '@/utils/request';

// ==================== 产品介绍管理接口 ====================

/**
 * 分页查询产品介绍列表
 * @param {Object} params 查询参数
 * @param {number} params.pageNum 页码（默认1）
 * @param {number} params.pageSize 每页数量（默认10）
 * @param {number} params.productId 产品ID（可选）
 * @param {string} params.productName 产品名称（可选，模糊查询）
 * @param {number} params.status 启用状态（可选）
 * @param {string} params.keyword 关键词搜索（可选，搜索产品摘要）
 * @returns {Promise} 分页结果
 */
export function getProductIntroductionPage(params) {
  return get('backend/ins-product-introduction/page', params);
}

/**
 * 根据ID查询产品介绍详情
 * @param {number} id 产品介绍ID
 * @returns {Promise} 产品介绍详情
 */
export function getProductIntroductionById(id) {
  return get(`backend/ins-product-introduction/${id}`);
}

/**
 * 根据产品ID查询产品介绍
 * @param {number} productId 产品ID
 * @returns {Promise} 产品介绍信息
 */
export function getProductIntroductionByProductId(productId) {
  return get(`backend/ins-product-introduction/product/${productId}`);
}

/**
 * 创建产品介绍
 * @param {Object} data 产品介绍数据
 * @param {number} data.productId 产品ID（必填）
 * @param {string} data.productSummary 产品摘要/简介
 * @param {string} data.bannerImageUrl 产品banner图URL
 * @param {Array} data.productFeatures 产品特点列表
 * @param {string} data.productOverview 产品概况（HTML格式）
 * @param {number} data.status 是否启用（1-启用，0-禁用）
 * @returns {Promise} 创建结果
 */
export function createProductIntroduction(data) {
  return post('backend/ins-product-introduction', data);
}

/**
 * 更新产品介绍
 * @param {number} id 产品介绍ID
 * @param {Object} data 产品介绍数据
 * @returns {Promise} 更新结果
 */
export function updateProductIntroduction(id, data) {
  return put(`backend/ins-product-introduction/${id}`, data);
}

/**
 * 删除产品介绍
 * @param {number} id 产品介绍ID
 * @returns {Promise} 删除结果
 */
export function deleteProductIntroduction(id) {
  return del(`backend/ins-product-introduction/${id}`);
}

/**
 * 根据产品ID删除产品介绍
 * @param {number} productId 产品ID
 * @returns {Promise} 删除结果
 */
export function deleteProductIntroductionByProductId(productId) {
  return del(`backend/ins-product-introduction/product/${productId}`);
}

// ==================== 辅助接口 ====================

/**
 * 获取产品列表（用于下拉选择）
 * @param {number} categoryId 分类ID（可选）
 * @returns {Promise} 产品列表
 */
export function getProductListForSelect(categoryId) {
  const params = categoryId ? { categoryId } : {};
  return get('backend/ins-product/list', params);
}
