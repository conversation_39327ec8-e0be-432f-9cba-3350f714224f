import { get, post, put, del } from "@/utils/request";

// ==================== 产品管理接口 ====================

/**
 * 分页查询产品列表（包含产品详情和子产品）
 * @param {Object} params 查询参数
 * @returns {Promise} 分页结果
 */
export function getProductPageWithDetails(params) {
  return get("backend/ins-product/page", params);
}

/**
 * 分页查询产品列表（基础信息）
 * @param {Object} params 查询参数
 * @returns {Promise} 分页结果
 */
export function getProductPageBasic(params) {
  return get("backend/ins-product/page-basic", params);
}

/**
 * 获取产品列表（不分页）
 * @param {number} categoryId 分类ID（可选）
 * @returns {Promise} 产品列表
 */
export function getProductList(categoryId) {
  const params = categoryId ? { categoryId } : {};
  return get("backend/ins-product/list", params);
}

/**
 * 获取产品基础列表（不分页，不包含详情）
 * @param {number} categoryId 分类ID（可选）
 * @returns {Promise} 产品基础列表
 */
export function getProductListBasic(categoryId) {
  const params = categoryId ? { categoryId } : {};
  return get("backend/ins-product/list-basic", params);
}

/**
 * 根据ID查询产品详情
 * @param {number} id 产品ID
 * @returns {Promise} 产品详情
 */
export function getProductById(id) {
  return get(`backend/ins-product/${id}`);
}

/**
 * 创建产品
 * @param {Object} data 产品数据
 * @returns {Promise} 创建结果
 */
export function createProduct(data) {
  return post("backend/ins-product", data);
}

/**
 * 更新产品
 * @param {number} id 产品ID
 * @param {Object} data 产品数据
 * @returns {Promise} 更新结果
 */
export function updateProduct(id, data) {
  return put(`backend/ins-product/${id}`, data);
}

/**
 * 删除产品
 * @param {number} id 产品ID
 * @returns {Promise} 删除结果
 */
export function deleteProduct(id) {
  return del(`backend/ins-product/${id}`);
}

// ==================== 子产品管理接口 ====================

/**
 * 分页查询子产品列表
 * @param {Object} params 查询参数
 * @returns {Promise} 分页结果
 */
export function getSubProductPage(params) {
  return get("backend/ins-product-sub/page", params);
}

/**
 * 根据父产品ID获取子产品列表
 * @param {number} parentProductId 父产品ID
 * @returns {Promise} 子产品列表
 */
export function getSubProductsByParentId(parentProductId) {
  return get("backend/ins-product-sub/list", { parentProductId });
}

/**
 * 根据ID查询子产品详情
 * @param {number} id 子产品ID
 * @returns {Promise} 子产品详情
 */
export function getSubProductById(id) {
  return get(`backend/ins-product-sub/${id}`);
}

/**
 * 创建子产品
 * @param {Object} data 子产品数据
 * @returns {Promise} 创建结果
 */
export function createSubProduct(data) {
  return post("backend/ins-product-sub", data);
}

/**
 * 更新子产品
 * @param {number} id 子产品ID
 * @param {Object} data 子产品数据
 * @returns {Promise} 更新结果
 */
export function updateSubProduct(id, data) {
  return put(`backend/ins-product-sub/${id}`, data);
}

/**
 * 删除子产品
 * @param {number} id 子产品ID
 * @returns {Promise} 删除结果
 */
export function deleteSubProduct(id) {
  return del(`backend/ins-product-sub/${id}`);
}

/**
 * 根据父产品ID删除所有子产品
 * @param {number} parentProductId 父产品ID
 * @returns {Promise} 删除结果
 */
export function deleteSubProductsByParentId(parentProductId) {
  return del(`backend/ins-product-sub/parent/${parentProductId}`);
}
