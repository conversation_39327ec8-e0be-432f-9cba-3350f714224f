import { get, post, put, del } from '@/utils/request';

/**
 * 分页查询角色列表
 * @param {Object} params 查询参数
 * @returns {Promise} 分页结果
 */
export function getRolePage(params) {
  return get('backend/role/page', params);
}

/**
 * 获取所有角色列表
 * @returns {Promise} 角色列表
 */
export function getRoleList() {
  return get('backend/role/list');
}

/**
 * 获取角色详情
 * @param {Number} id 角色ID
 * @returns {Promise} 角色详情
 */
export function getRole(id) {
  return get(`backend/role/${id}`);
}

/**
 * 添加角色
 * @param {Object} data 角色数据
 * @returns {Promise} 操作结果
 */
export function addRole(data) {
  return post('backend/role', data);
}

/**
 * 更新角色
 * @param {Object} data 角色数据
 * @returns {Promise} 操作结果
 */
export function updateRole(data) {
  return put('backend/role', data);
}

/**
 * 删除角色
 * @param {Number} id 角色ID
 * @returns {Promise} 操作结果
 */
export function deleteRole(id) {
  return del(`backend/role/${id}`);
}

/**
 * 批量删除角色
 * @param {Array} ids 角色ID数组
 * @returns {Promise} 操作结果
 */
export function batchDeleteRoles(ids) {
  return del('backend/role/batch', ids);
} 