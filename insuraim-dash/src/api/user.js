import { get, post, put, del } from '@/utils/request';

/**
 * 分页查询用户列表
 * @param {Object} params 查询参数
 * @returns {Promise} 分页结果
 */
export function getUserPage(params) {
  return get('backend/user/page', params);
}

/**
 * 获取用户详情
 * @param {Number} id 用户ID
 * @returns {Promise} 用户详情
 */
export function getUser(id) {
  return get(`backend/user/${id}`);
}

/**
 * 获取当前登录用户信息
 * @returns {Promise} 用户信息
 */
export function getUserInfo() {
  return get('backend/user/info');
}

/**
 * 用户登录
 * @param {Object} data 登录数据
 * @returns {Promise} 登录结果
 */
export function login(data) {
  return post('backend/user/login', data);
}

/**
 * 用户注册
 * @param {Object} data 注册数据
 * @returns {Promise} 注册结果
 */
export function register(data) {
  return post('backend/user/register', data);
}

/**
 * 用户登出
 * @returns {Promise} 登出结果
 */
export function logout() {
  return post('backend/user/logout');
}

/**
 * 检查登录状态
 * @returns {Promise} 登录状态
 */
export function checkLogin() {
  return get('backend/user/checkLogin');
}

/**
 * 新增用户
 * @param {Object} data 用户数据
 * @returns {Promise} 操作结果
 */
export function addUser(data) {
  return post('backend/user', data);
}

/**
 * 更新用户
 * @param {Object} data 用户数据
 * @returns {Promise} 操作结果
 */
export function updateUser(data) {
  return put('backend/user', data);
}

/**
 * 删除用户
 * @param {Number} id 用户ID
 * @returns {Promise} 操作结果
 */
export function deleteUser(id) {
  return del(`backend/user/${id}`);
}

/**
 * 批量删除用户
 * @param {Array} ids 用户ID数组
 * @returns {Promise} 操作结果
 */
export function batchDeleteUsers(ids) {
  return del('backend/user/batch', { ids });
}

/**
 * 重置用户密码
 * @param {Number} id 用户ID
 * @param {String} newPassword 新密码
 * @returns {Promise} 操作结果
 */
export function resetPassword(id, newPassword) {
  return put(`backend/user/resetPassword/${id}`, { newPassword });
}

/**
 * 修改用户状态
 * @param {Number} id 用户ID
 * @param {Number} status 状态值
 * @returns {Promise} 操作结果
 */
export function updateUserStatus(id, status) {
  return put(`backend/user/status/${id}`, { status });
} 