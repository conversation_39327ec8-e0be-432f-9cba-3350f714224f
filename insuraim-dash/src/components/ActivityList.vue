<template>
  <div class="bg-white rounded-lg shadow-md p-6">
    <div class="flex justify-between items-center mb-6">
      <h3 class="text-lg font-medium">{{ title }}</h3>
      <a :href="moreLink" class="text-blue-500 text-sm hover:text-blue-700 transition-colors">更多 ></a>
    </div>
    <div class="relative">
      <div v-if="loading" class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-70 z-10">
        <div class="animate-pulse flex flex-col w-full space-y-4">
          <div v-for="i in 4" :key="i" class="flex items-start">
            <div class="w-8 h-8 rounded-full bg-gray-200 mr-3"></div>
            <div class="flex-1">
              <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div class="h-3 bg-gray-200 rounded w-1/4"></div>
            </div>
          </div>
        </div>
      </div>
      <div v-else-if="activities.length === 0" class="flex flex-col items-center justify-center py-8">
        <Icon icon="material-symbols:info-outline" class="w-10 h-10 text-gray-300 mb-2" />
        <p class="text-gray-500">暂无活动记录</p>
      </div>
      <div v-else class="space-y-4">
        <div v-for="(activity, index) in activities" :key="index" class="flex items-start">
          <div class="flex-shrink-0 mt-1 mr-3">
            <div class="w-8 h-8 rounded-full flex items-center justify-center" :class="activity.bgColor">
              <Icon :icon="activity.icon" class="w-4 h-4 text-white" />
            </div>
          </div>
          <div>
            <p class="text-sm font-medium">{{ activity.title }}</p>
            <p class="text-xs text-gray-500 mt-1">{{ activity.time }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';
import { ref, onMounted } from 'vue';

const props = defineProps({
  title: {
    type: String,
    default: '最近活动'
  },
  activities: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  moreLink: {
    type: String,
    default: '#'
  }
});

// 定义公开的方法
defineExpose({
  // 可以在父组件中调用的方法
});
</script> 