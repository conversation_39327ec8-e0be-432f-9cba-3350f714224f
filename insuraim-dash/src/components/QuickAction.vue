<template>
  <div class="bg-white rounded-lg shadow-md p-6">
    <h3 class="text-lg font-medium mb-6">{{ title }}</h3>
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
      <button 
        v-for="(action, index) in actions" 
        :key="index" 
        class="flex flex-col items-center p-4 rounded-lg hover:bg-gray-50 transition-colors"
        @click="handleAction(action)"
      >
        <div class="w-12 h-12 rounded-full flex items-center justify-center mb-2" :class="action.bgColor">
          <Icon :icon="action.icon" class="w-6 h-6" :class="action.iconColor" />
        </div>
        <span class="text-sm">{{ action.title }}</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';

const props = defineProps({
  title: {
    type: String,
    default: '快速操作'
  },
  actions: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['action']);

const handleAction = (action) => {
  emit('action', action);
};
</script> 