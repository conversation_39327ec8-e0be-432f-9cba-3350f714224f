<template>
  <div class="bg-white rounded-lg shadow-md p-6 transition-all duration-300 hover:shadow-lg">
    <div class="flex items-center">
      <div class="p-3 rounded-full mr-4" :class="bgColorClass">
        <Icon :icon="icon" class="w-6 h-6" :class="iconColorClass" />
      </div>
      <div>
        <p class="text-gray-500 text-sm">{{ title }}</p>
        <p class="text-2xl font-semibold">
          <template v-if="loading">
            <span class="inline-block w-16 h-8 bg-gray-200 animate-pulse rounded"></span>
          </template>
          <template v-else>
            {{ value }}
          </template>
        </p>
      </div>
    </div>
    <div v-if="showTrend" class="mt-4 pt-4 border-t border-gray-100">
      <div class="flex items-center">
        <Icon :icon="trendIcon" class="w-4 h-4 mr-1" :class="trendColorClass" />
        <span class="text-xs" :class="trendColorClass">{{ trendValue }}</span>
        <span class="text-xs text-gray-500 ml-1">较上月</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';
import { computed } from 'vue';

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  value: {
    type: [Number, String],
    default: '0'
  },
  icon: {
    type: String,
    required: true
  },
  color: {
    type: String,
    default: 'blue'
  },
  loading: {
    type: Boolean,
    default: false
  },
  showTrend: {
    type: Boolean,
    default: false
  },
  trendValue: {
    type: String,
    default: '0%'
  },
  trendDirection: {
    type: String,
    default: 'up', // 'up' or 'down'
    validator: (val) => ['up', 'down'].includes(val)
  }
});

const bgColorClass = computed(() => `bg-${props.color}-100`);
const iconColorClass = computed(() => `text-${props.color}-500`);

const trendIcon = computed(() => {
  return props.trendDirection === 'up' 
    ? 'material-symbols:trending-up' 
    : 'material-symbols:trending-down';
});

const trendColorClass = computed(() => {
  return props.trendDirection === 'up' 
    ? 'text-green-500' 
    : 'text-red-500';
});
</script> 