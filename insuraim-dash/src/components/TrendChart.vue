<template>
  <div class="bg-white rounded-lg shadow-md p-6">
    <div class="flex justify-between items-center mb-6">
      <h3 class="text-lg font-medium">{{ title }}</h3>
      <div class="flex space-x-2">
        <button 
          v-for="period in periods" 
          :key="period.value"
          class="px-3 py-1 text-sm rounded-md transition-colors"
          :class="activePeriod === period.value ? activeClass : inactiveClass"
          @click="changePeriod(period.value)"
        >
          {{ period.label }}
        </button>
      </div>
    </div>
    <div class="relative">
      <div v-if="loading" class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-70 z-10">
        <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
      </div>
      <div ref="chartRef" class="h-64 w-full"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import * as echarts from 'echarts/core';
import { LineChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DataZoomComponent,
  LegendComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';

// 注册必要的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DataZoomComponent,
  LegendComponent,
  LineChart,
  CanvasRenderer
]);

const props = defineProps({
  title: {
    type: String,
    default: '数据趋势'
  },
  loading: {
    type: Boolean,
    default: false
  },
  chartData: {
    type: Object,
    default: () => ({
      xAxis: [],
      series: []
    })
  }
});

const chartRef = ref(null);
const chartInstance = ref(null);
const activePeriod = ref('month');

const periods = [
  { label: '本月', value: 'month' },
  { label: '本年', value: 'year' }
];

const activeClass = 'bg-blue-100 text-blue-600';
const inactiveClass = 'text-gray-500 hover:bg-gray-100';

const emit = defineEmits(['period-change']);

const changePeriod = (period) => {
  activePeriod.value = period;
  emit('period-change', period);
};

const initChart = () => {
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
  
  if (!chartRef.value) return;
  
  chartInstance.value = echarts.init(chartRef.value);
  updateChart();
};

const updateChart = () => {
  if (!chartInstance.value) return;

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: props.chartData.xAxis || [],
      axisLine: {
        lineStyle: {
          color: '#eaeaea'
        }
      },
      axisLabel: {
        color: '#666'
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#f5f5f5'
        }
      },
      axisLabel: {
        color: '#666'
      }
    },
    series: props.chartData.series || []
  };

  chartInstance.value.setOption(option);
};

watch(() => props.chartData, () => {
  updateChart();
}, { deep: true });

watch(() => props.loading, (val) => {
  if (!val) {
    updateChart();
  }
});

onMounted(() => {
  initChart();

  window.addEventListener('resize', () => {
    if (chartInstance.value) {
      chartInstance.value.resize();
    }
  });
});
</script> 