<template>
  <div class="admin-layout">
    <!-- 侧边栏 -->
    <AdminSidebar />

    <!-- 主要内容区域 -->
    <div class="main-content">
      <router-view></router-view>
    </div>
  </div>
</template>

<script setup>
import AdminSidebar from '@/layouts/AdminSidebar.vue';
</script>

<style scoped>
.admin-layout {
  min-height: 100vh;
  display: flex;
}

.main-content {
  flex: 1;
  margin-left: 240px;
  padding: 24px;
  transition: margin-left 0.3s ease;
  min-height: 100vh;
  /* background-color: #f5f5f5; */
}

/* 当侧边栏折叠时的样式 */
:deep(.admin-sidebar.collapsed)+.main-content {
  margin-left: 64px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    margin-left: 0;
    padding: 16px;
  }

  :deep(.admin-sidebar.collapsed)+.main-content {
    margin-left: 64px;
  }
}
</style>