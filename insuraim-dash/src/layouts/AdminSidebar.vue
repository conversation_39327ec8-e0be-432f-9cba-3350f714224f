<template>
  <div class="admin-sidebar">
    <!-- Logo和标题区域 -->
    <div class="sidebar-header">
      <div class="logo-container">
        <Icon icon="mdi:security" class="logo-icon" />
        <h1 class="logo-text">保险管理系统</h1>
      </div>
    </div>

    <!-- 用户信息 -->
    <div class="user-info">
      <div class="user-avatar">
        <Icon icon="mdi:account-circle" class="w-8 h-8" />
      </div>
      <div class="user-details">
        <div class="user-name">{{ userName }}</div>
        <div class="user-role">管理员</div>
      </div>
      <div class="logout-button" @click="handleLogout">
        <Icon icon="mdi:logout" class="w-5 h-5" />
      </div>
    </div>

    <!-- 菜单项 -->
    <a-menu v-model:openKeys="openKeys" v-model:selectedKeys="selectedKeys" mode="inline" theme="light">
      <template v-for="item in menuItems" :key="item.key">
        <a-sub-menu v-if="item.children" :key="item.key">
          <template #icon>
            <Icon :icon="item.icon" />
          </template>
          <template #title>{{ item.label }}</template>
          <a-menu-item v-for="child in item.children" :key="child.key" @click="navigateTo(child.route)">
            <Icon :icon="child.icon" />
            <span class="ml-2">{{ child.label }}</span>
          </a-menu-item>
        </a-sub-menu>
        <a-menu-item v-else :key="'item-' + item.key" @click="navigateTo(item.route)">
          <Icon :icon="item.icon" />
          <span class="ml-2">{{ item.label }}</span>
        </a-menu-item>
      </template>
    </a-menu>
  </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';
import { ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { logout } from '@/api/admin';

const route = useRoute();
const router = useRouter();

// 菜单状态
const openKeys = ref([]);
const selectedKeys = ref([]);

// 用户信息
const userName = ref('管理员');

// 菜单项配置
const menuItems = [
  {
    key: 'dashboard',
    label: '仪表盘',
    icon: 'mdi:view-dashboard',
    route: '/dashboard'
  },
  {
    key: 'tenant',
    label: '组织管理',
    icon: 'mdi:city',
    children: [
      {
        key: 'organization-list',
        label: '组织列表',
        icon: 'mdi:view-list',
        route: '/organization'
      },
      {
        key: 'organization-users',
        label: '用户列表',
        icon: 'mdi:view-list',
        route: '/organization/users'
      }
    ]
  },
  {
    key: 'policies',
    label: '保单管理',
    icon: 'mdi:file-document-multiple',
    children: [
      {
        key: 'policy-list',
        label: '保单列表',
        icon: 'mdi:format-list-bulleted',
        route: '/policies/list'
      },
      {
        key: 'policy-approval',
        label: '保单审核',
        icon: 'mdi:check-decagram',
        route: '/policies/approval'
      },
      {
        key: 'policy-renewal',
        label: '保单续期',
        icon: 'mdi:refresh',
        route: '/policies/renewal'
      }
    ]
  },
  {
    key: 'customers',
    label: '客户管理',
    icon: 'mdi:account-group',
    children: [
      {
        key: 'customer-list',
        label: '客户列表',
        icon: 'mdi:account-box-multiple',
        route: '/customers/list'
      },
      {
        key: 'customer-analysis',
        label: '客户分析',
        icon: 'mdi:chart-line',
        route: '/customers/analysis'
      },
      {
        key: 'customer-groups',
        label: '客户群组',
        icon: 'mdi:account-group-outline',
        route: '/customers/groups'
      }
    ]
  },
  {
    key: 'products',
    label: '产品管理',
    icon: 'mdi:package-variant',
    children: [
      {
        key: 'product-manage',
        label: '产品管理',
        icon: 'mdi:cog',
        route: '/products/manage'
      },
      {
        key: 'product-categories',
        label: '产品分类',
        icon: 'mdi:shape',
        route: '/products/categories'
      },
      {
        key: 'product-detail-manage',
        label: '产品信息页管理',
        icon: 'mdi:file-document-edit',
        route: '/products/detail-manage'
      },
      {
        key: 'product-introduction-manage',
        label: '产品介绍页管理',
        icon: 'mdi:file-presentation-box',
        route: '/products/introduction-manage'
      }
    ]
  },
  {
    key: 'companies',
    label: '保司管理',
    icon: 'mdi:office-building',
    children: [
      {
        key: 'company-list',
        label: '公司列表',
        icon: 'mdi:domain',
        route: '/companies'
      },
      {
        key: 'company-cooperation',
        label: '合作管理',
        icon: 'mdi:handshake',
        route: '/companies/cooperation'
      }
    ]
  },
  {
    key: 'settings',
    label: '系统配置',
    icon: 'mdi:cog',
    children: [
      {
        key: 'menus',
        label: '菜单管理',
        icon: 'mdi:menu',
        route: '/settings/menus'
      },
      {
        key: 'users',
        label: '用户管理',
        icon: 'mdi:account-details',
        route: '/settings/users'
      },
      {
        key: 'roles',
        label: '角色管理',
        icon: 'mdi:shield-account',
        route: '/settings/roles'
      },
      {
        key: 'permissions',
        label: '权限配置',
        icon: 'mdi:shield-lock',
        route: '/settings/permissions'
      },
      {
        key: 'system-params',
        label: '系统参数',
        icon: 'mdi:tune',
        route: '/settings/params'
      }
    ]
  }
];

// 监听路由变化更新菜单选中状态
watch(() => route.path, (newPath) => {
  // 寻找匹配的菜单项
  let matchedItemKey = null;
  let matchedSubItemKey = null;

  // 遍历一级菜单
  for (const item of menuItems) {
    // 检查是否直接匹配一级菜单
    if (item.route === newPath) {
      matchedItemKey = item.key;
      break;
    }

    // 检查二级菜单
    if (item.children) {
      const matchedChild = item.children.find(child => child.route === newPath);
      if (matchedChild) {
        matchedItemKey = item.key;
        matchedSubItemKey = matchedChild.key;
        break;
      }
    }
  }

  // 更新菜单状态
  if (matchedItemKey) {
    // 如果找到了子菜单项，则选中子菜单项，同时展开父菜单
    if (matchedSubItemKey) {
      selectedKeys.value = [matchedSubItemKey];
      openKeys.value = [matchedItemKey];
    } else {
      // 如果只找到了一级菜单项，则只选中该菜单项
      selectedKeys.value = [matchedItemKey];
    }
  }
}, { immediate: true });

// 导航处理
const navigateTo = (route) => {
  router.push(route);
};

// 退出登录处理
const handleLogout = async () => {
  try {
    await logout();
    // 清除本地存储的token和用户信息
    localStorage.removeItem('token');
    // 跳转到登录页面
    router.push('/login');
  } catch (error) {
    console.error('退出登录失败:', error);
  }
};
</script>

<style scoped>
.admin-sidebar {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  width: 240px;
  background-color: #ffffff;
  color: #333;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  z-index: 100;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 16px;
  display: flex;
  align-items: center;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo-icon {
  width: 28px;
  height: 28px;
  color: #1890ff;
}

.logo-text {
  margin-left: 12px;
  font-size: 16px;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  color: #333;
}

.user-info {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.user-avatar {
  color: #1890ff;
}

.user-details {
  margin-left: 12px;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.user-role {
  font-size: 12px;
  color: #666;
}

.logout-button {
  margin-left: auto;
  cursor: pointer;
  color: #666;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s;
}

.logout-button:hover {
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
}

.menu-items {
  flex: 1;
  padding: 16px 0;
  overflow-y: auto;
}

.menu-item {
  display: flex;
  align-items: center;
}

.menu-item .ant-menu-title-content {
  display: flex;
  align-items: center;
}

.menu-item .anticon {
  margin-right: 8px;
}

/* 二级菜单样式调整 */
:deep(.ant-menu-item) {
  display: flex;
  align-items: center;
}

:deep(.ant-menu-item .iconify-inline) {
  margin-right: 10px;
  vertical-align: middle;
  display: inline-flex;
  align-items: center;
  font-size: 16px;
}

:deep(.ant-menu-item span) {
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
}

/* 确保一级和二级菜单的图标大小一致 */
:deep(.ant-menu-submenu-title .iconify-inline),
:deep(.ant-menu-item .iconify-inline) {
  width: 16px;
  height: 16px;
  font-size: 16px;
}

/* 统一子菜单缩进 */
:deep(.ant-menu-sub.ant-menu-inline > .ant-menu-item) {
  padding-left: 48px !important;
}

/* 确保菜单项的文本内容对齐 */
:deep(.ant-menu-title-content) {
  display: flex;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-sidebar {
    transform: translateX(-100%);
  }
}
</style>