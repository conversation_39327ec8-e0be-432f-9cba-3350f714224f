import { createRouter, createWebHistory } from "vue-router";

const routes = [
  {
    path: "/",
    component: () => import("../layouts/AdminLayout.vue"),
    redirect: "/dashboard",
    children: [
      {
        path: "dashboard",
        name: "AdminDashboard",
        component: () => import("../views/DashboardView.vue"),
      },
      {
        path: "organization",
        name: "AdminOrganization",
        component: () => import("../views/organization/OrganizationView.vue"),
      },
      {
        path: "organization/users",
        name: "AdminOrganizationUsers",
        component: () =>
          import("../views/organization/OrganzationUsersView.vue"),
      },
      {
        path: "companies",
        name: "AdminCompanies",
        component: () => import("../views/companies/CompaniesView.vue"),
      },
      {
        path: "companies/detail/:code",
        name: "AdminCompanyDetail",
        component: () => import("../views/companies/CompanyDetailView.vue"),
      },
      {
        path: "policies",
        name: "AdminPolicies",
        component: () => import("../views/PoliciesView.vue"),
      },
      {
        path: "products/categories",
        name: "AdminProductCategories",
        component: () => import("../views/products/CategoriesView.vue"),
      },
      {
        path: "products/detail-manage",
        name: "AdminProductDetailManage",
        component: () =>
          import("../views/products/ProductDetailManageView.vue"),
      },
      {
        path: "products/manage",
        name: "AdminProductManage",
        component: () => import("../views/products/ProductManageView.vue"),
      },
      {
        path: "products/introduction-manage",
        name: "AdminProductIntroductionManage",
        component: () =>
          import("../views/products/ProductIntroductionManageView.vue"),
      },
      {
        path: "settings",
        name: "AdminSettings",
        component: () => import("../views/SettingsView.vue"),
      },
      {
        path: "settings/menus",
        name: "AdminMenus",
        component: () => import("../views/settings/MenusView.vue"),
      },
      {
        path: "settings/roles",
        name: "AdminRoles",
        component: () => import("../views/settings/RolesView.vue"),
      },
      {
        path: "settings/users",
        name: "AdminUsers",
        component: () => import("../views/settings/UsersView.vue"),
      },
      {
        path: "settings/permissions",
        name: "AdminPermissions",
        component: () => import("../views/settings/PermissionsView.vue"),
      },
      {
        path: "reports",
        name: "AdminReports",
        component: () => import("../views/ReportsView.vue"),
      },
    ],
  },
  {
    path: "/login",
    name: "Login",
    component: () => import("../views/LoginView.vue"),
  },
  // 404页面
  {
    path: "/:pathMatch(.*)*",
    name: "NotFound",
    component: () => import("../views/404.vue"),
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

export default router;
