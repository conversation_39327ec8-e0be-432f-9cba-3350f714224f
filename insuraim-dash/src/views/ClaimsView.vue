<template>
  <div class="claims-view">
    <!-- 页面标题和操作按钮 -->
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-semibold text-gray-800">理赔管理</h1>
      <div class="flex gap-4">
        <button class="btn-primary" @click="handleAddClaim">
          <Icon icon="material-symbols:add" class="mr-2" />
          新增理赔
        </button>
        <button class="btn-secondary" @click="handleExport">
          <Icon icon="material-symbols:download" class="mr-2" />
          导出数据
        </button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <div class="bg-white p-4 rounded-lg shadow-sm">
        <div class="text-sm text-gray-500">待处理理赔</div>
        <div class="text-2xl font-semibold text-blue-500">{{ stats.pending }}</div>
      </div>
      <div class="bg-white p-4 rounded-lg shadow-sm">
        <div class="text-sm text-gray-500">处理中理赔</div>
        <div class="text-2xl font-semibold text-yellow-500">{{ stats.processing }}</div>
      </div>
      <div class="bg-white p-4 rounded-lg shadow-sm">
        <div class="text-sm text-gray-500">已完成理赔</div>
        <div class="text-2xl font-semibold text-green-500">{{ stats.completed }}</div>
      </div>
      <div class="bg-white p-4 rounded-lg shadow-sm">
        <div class="text-sm text-gray-500">理赔总金额</div>
        <div class="text-2xl font-semibold text-red-500">¥{{ stats.totalAmount }}</div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="form-group">
          <label class="form-label">理赔单号</label>
          <input type="text" class="form-input" v-model="searchForm.claimNo" placeholder="请输入理赔单号" />
        </div>
        <div class="form-group">
          <label class="form-label">保单号</label>
          <input type="text" class="form-input" v-model="searchForm.policyNo" placeholder="请输入保单号" />
        </div>
        <div class="form-group">
          <label class="form-label">理赔状态</label>
          <select class="form-input" v-model="searchForm.status">
            <option value="">全部</option>
            <option value="pending">待处理</option>
            <option value="processing">处理中</option>
            <option value="completed">已完成</option>
            <option value="rejected">已拒绝</option>
          </select>
        </div>
        <div class="form-group">
          <label class="form-label">申请日期</label>
          <input type="date" class="form-input" v-model="searchForm.applyDate" />
        </div>
      </div>
      <div class="flex justify-end mt-4">
        <button class="btn-secondary mr-2" @click="handleReset">重置</button>
        <button class="btn-primary" @click="handleSearch">搜索</button>
      </div>
    </div>

    <!-- 理赔列表 -->
    <div class="bg-white rounded-lg shadow-sm">
      <div class="overflow-x-auto">
        <table class="min-w-full">
          <thead>
            <tr class="bg-gray-50">
              <th class="table-header">理赔单号</th>
              <th class="table-header">保单号</th>
              <th class="table-header">申请人</th>
              <th class="table-header">理赔类型</th>
              <th class="table-header">申请金额</th>
              <th class="table-header">申请日期</th>
              <th class="table-header">状态</th>
              <th class="table-header">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="claim in claims" :key="claim.id" class="border-t">
              <td class="table-cell">{{ claim.claimNo }}</td>
              <td class="table-cell">{{ claim.policyNo }}</td>
              <td class="table-cell">{{ claim.applicant }}</td>
              <td class="table-cell">{{ getClaimTypeText(claim.type) }}</td>
              <td class="table-cell">¥{{ claim.amount }}</td>
              <td class="table-cell">{{ claim.applyDate }}</td>
              <td class="table-cell">
                <span :class="getStatusClass(claim.status)">
                  {{ getStatusText(claim.status) }}
                </span>
              </td>
              <td class="table-cell">
                <div class="flex gap-2">
                  <button class="btn-icon" @click="handleView(claim)">
                    <Icon icon="material-symbols:visibility" />
                  </button>
                  <button class="btn-icon" @click="handleProcess(claim)">
                    <Icon icon="material-symbols:edit" />
                  </button>
                  <button class="btn-icon" @click="handleDelete(claim)">
                    <Icon icon="material-symbols:delete" />
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div class="flex justify-between items-center p-4 border-t">
        <div class="text-sm text-gray-600">
          共 {{ total }} 条记录
        </div>
        <div class="flex gap-2">
          <button 
            class="btn-pagination" 
            :disabled="currentPage === 1"
            @click="handlePageChange(currentPage - 1)"
          >
            上一页
          </button>
          <button 
            class="btn-pagination" 
            :disabled="currentPage === totalPages"
            @click="handlePageChange(currentPage + 1)"
          >
            下一页
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { Icon } from '@iconify/vue';
import { message } from 'ant-design-vue';

// 统计信息
const stats = ref({
  pending: 5,
  processing: 3,
  completed: 12,
  totalAmount: '125,000'
});

// 搜索表单
const searchForm = ref({
  claimNo: '',
  policyNo: '',
  status: '',
  applyDate: ''
});

// 理赔列表
const claims = ref([
  {
    id: 1,
    claimNo: 'CLM20240001',
    policyNo: 'POL20240001',
    applicant: '张三',
    type: 'medical',
    amount: '5,000',
    applyDate: '2024-03-15',
    status: 'pending'
  },
  {
    id: 2,
    claimNo: 'CLM20240002',
    policyNo: 'POL20240002',
    applicant: '李四',
    type: 'accident',
    amount: '10,000',
    applyDate: '2024-03-10',
    status: 'processing'
  }
]);

// 分页
const currentPage = ref(1);
const total = ref(100);
const totalPages = ref(10);

// 获取状态样式
const getStatusClass = (status) => {
  const classes = {
    pending: 'bg-yellow-100 text-yellow-800',
    processing: 'bg-blue-100 text-blue-800',
    completed: 'bg-green-100 text-green-800',
    rejected: 'bg-red-100 text-red-800'
  };
  return `px-2 py-1 rounded-full text-xs ${classes[status]}`;
};

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    rejected: '已拒绝'
  };
  return texts[status];
};

// 获取理赔类型文本
const getClaimTypeText = (type) => {
  const texts = {
    medical: '医疗理赔',
    accident: '意外理赔',
    property: '财产理赔',
    life: '寿险理赔'
  };
  return texts[type];
};

// 处理新增理赔
const handleAddClaim = () => {
  message.info('新增理赔');
};

// 处理导出数据
const handleExport = () => {
  message.info('导出数据');
};

// 处理搜索
const handleSearch = () => {
  message.info('搜索');
};

// 处理重置
const handleReset = () => {
  searchForm.value = {
    claimNo: '',
    policyNo: '',
    status: '',
    applyDate: ''
  };
};

// 处理查看
const handleView = (claim) => {
  message.info(`查看理赔 ${claim.claimNo}`);
};

// 处理处理
const handleProcess = (claim) => {
  message.info(`处理理赔 ${claim.claimNo}`);
};

// 处理删除
const handleDelete = (claim) => {
  message.info(`删除理赔 ${claim.claimNo}`);
};

// 处理分页
const handlePageChange = (page) => {
  currentPage.value = page;
  // TODO: 加载对应页的数据
};

onMounted(() => {
  // TODO: 加载初始数据
});
</script>

<style scoped>
.claims-view {
  min-height: 100%;
}

</style> 