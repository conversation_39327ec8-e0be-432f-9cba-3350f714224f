<template>
  <div class="dashboard-view">
    <!-- 页面标题和欢迎语 -->
    <div class="mb-8">
      <h1 class="text-2xl font-semibold text-gray-800">仪表盘</h1>
      <p class="text-gray-600 mt-1">欢迎回来，{{ userName }}！今天是 {{ currentDate }}</p>
    </div>

    <!-- 数据统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <StatCard title="总保单数" :value="stats.totalPolicies" icon="material-symbols:description" color="blue"
        :loading="loading" show-trend trend-value="+5.2%" trend-direction="up" />
      <StatCard title="总被保人" :value="stats.totalInsured" icon="material-symbols:person" color="green" :loading="loading"
        show-trend trend-value="+3.1%" trend-direction="up" />
      <StatCard title="本月新增" :value="stats.monthlyNew" icon="material-symbols:receipt" color="purple" :loading="loading"
        show-trend trend-value="-2.5%" trend-direction="down" />
      <StatCard title="总保费" :value="stats.totalPremium" icon="material-symbols:attach-money" color="yellow"
        :loading="loading" show-trend trend-value="+8.3%" trend-direction="up" />
    </div>

    <!-- 图表和活动区域 -->
    <div class="flex flex-col lg:flex-row gap-6 mb-8">
      <!-- 图表区域 -->
      <div class="lg:w-2/3">
        <TrendChart title="保单趋势" :loading="chartLoading" :chart-data="chartData" @period-change="handlePeriodChange" />
      </div>

      <!-- 最近活动 -->
      <div class="lg:w-1/3">
        <ActivityList title="最近活动" :activities="activities" :loading="activitiesLoading" more-link="/activities" />
      </div>
    </div>

    <!-- 快速操作 -->
    <QuickAction title="快速操作" :actions="quickActions" @action="handleQuickAction" />
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import StatCard from '@/components/StatCard.vue';
import TrendChart from '@/components/TrendChart.vue';
import ActivityList from '@/components/ActivityList.vue';
import QuickAction from '@/components/QuickAction.vue';
import { getDashboardStats, getTrendData, getRecentActivities } from '@/api/dashboard';

// 当前日期
const currentDate = computed(() => {
  const date = new Date();
  return date.toLocaleDateString('zh-CN', { year: 'numeric', month: 'long', day: 'numeric' });
});

// 用户信息
const userName = ref('管理员');

// 加载状态
const loading = ref(true);
const chartLoading = ref(true);
const activitiesLoading = ref(true);

// 统计数据
const stats = ref({
  totalPolicies: 0,
  totalInsured: 0,
  monthlyNew: 0,
  totalPremium: '¥0'
});

// 图表数据
const chartData = ref({
  xAxis: [],
  series: []
});

// 当前选择的时间周期
const currentPeriod = ref('month');

// 最近活动
const activities = ref([]);

// 快速操作
const quickActions = [
  {
    title: '新增保单',
    icon: 'material-symbols:add',
    bgColor: 'bg-blue-100',
    iconColor: 'text-blue-500',
    action: 'add-policy'
  },
  {
    title: '查询保单',
    icon: 'material-symbols:search',
    bgColor: 'bg-green-100',
    iconColor: 'text-green-500',
    action: 'search-policy'
  },
  {
    title: '生成计划书',
    icon: 'material-symbols:receipt',
    bgColor: 'bg-purple-100',
    iconColor: 'text-purple-500',
    action: 'generate-proposal'
  },
  {
    title: '生成报表',
    icon: 'material-symbols:report',
    bgColor: 'bg-yellow-100',
    iconColor: 'text-yellow-500',
    action: 'generate-report'
  }
];

// 加载仪表盘数据
const loadDashboardData = async () => {
  try {
    loading.value = true;
    const result = await dashboardAPI.getDashboardStats();
    stats.value = result;
  } catch (error) {
    console.error('加载仪表盘数据失败:', error);
    // 使用模拟数据
    stats.value = {
      totalPolicies: 128,
      totalInsured: 256,
      monthlyNew: 24,
      totalPremium: '¥1,280,000'
    };
  } finally {
    loading.value = false;
  }
};

// 加载趋势数据
const loadTrendData = async () => {
  try {
    chartLoading.value = true;
    const result = await dashboardAPI.getTrendData({ period: currentPeriod.value });
    chartData.value = result;
  } catch (error) {
    console.error('加载趋势数据失败:', error);
    // 使用模拟数据
    const mockMonths = ['1月', '2月', '3月', '4月', '5月', '6月'];
    const mockValues = [10, 25, 18, 30, 22, 40];

    chartData.value = {
      xAxis: mockMonths,
      series: [
        {
          name: '保单数',
          type: 'line',
          smooth: true,
          data: mockValues,
          itemStyle: {
            color: '#3B82F6'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(59, 130, 246, 0.3)' },
                { offset: 1, color: 'rgba(59, 130, 246, 0.1)' }
              ]
            }
          }
        }
      ]
    };
  } finally {
    chartLoading.value = false;
  }
};

// 加载最近活动
const loadRecentActivities = async () => {
  try {
    activitiesLoading.value = true;
    const result = await dashboardAPI.getRecentActivities({ limit: 4 });
    activities.value = result;
  } catch (error) {
    console.error('加载最近活动失败:', error);
    // 使用模拟数据
    activities.value = [
      {
        title: '新增了一份保单 #20250001',
        time: '10分钟前',
        icon: 'material-symbols:description',
        bgColor: 'bg-blue-500'
      },
      {
        title: '用户张先生提交了计划书申请',
        time: '30分钟前',
        icon: 'material-symbols:receipt',
        bgColor: 'bg-purple-500'
      },
      {
        title: '保单 #20249999 已生效',
        time: '2小时前',
        icon: 'material-symbols:check-circle',
        bgColor: 'bg-green-500'
      },
      {
        title: '系统维护通知',
        time: '昨天',
        icon: 'material-symbols:warning',
        bgColor: 'bg-yellow-500'
      }
    ];
  } finally {
    activitiesLoading.value = false;
  }
};

// 切换趋势图的时间周期
const handlePeriodChange = (period) => {
  currentPeriod.value = period;
  loadTrendData();
};

// 处理快速操作按钮点击
const handleQuickAction = (action) => {
  console.log('执行操作:', action);

  switch (action.action) {
    case 'add-policy':
      // 跳转到添加保单页面
      // router.push('/policies/new');
      break;

    case 'search-policy':
      // 跳转到保单查询页面
      // router.push('/policies/search');
      break;

    case 'generate-proposal':
      // 跳转到生成计划书页面
      // router.push('/proposals/new');
      break;

    case 'generate-report':
      // 跳转到报表页面
      // router.push('/reports');
      break;
  }
};

// 页面加载时获取数据
onMounted(() => {
  Promise.all([
    loadDashboardData(),
    loadTrendData(),
    loadRecentActivities()
  ]);
});
</script>

<style scoped>
.dashboard-view {
  min-height: 100%;
}
</style>