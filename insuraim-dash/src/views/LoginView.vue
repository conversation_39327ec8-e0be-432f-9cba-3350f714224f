<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-100 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-4 bg-white p-10 rounded-xl shadow-lg">
      <div class="flex items-center justify-center space-x-4">
        <img class="h-14 w-auto" src="https://wp-cdn.yukuii.top/v2/Cfo6zjv.png" alt="BIS系统" />
        <span class="text-2xl font-bold">Insuraim</span>
      </div>
      <p class="mt-2 text-center text-sm text-gray-600">
        保险信息管理系统后台
      </p>

      <a-form :model="formState" class="mt-8 space-y-6">
        <div class="rounded-md -space-y-px">
          <div class="mb-6">
            <a-form-item name="username">
              <a-input v-model:value="formState.username" size="large" placeholder="管理员账号">
                <template #prefix>
                  <Icon icon="material-symbols:admin-panel-settings" class="text-gray-400" />
                </template>
              </a-input>
            </a-form-item>
          </div>

          <div class="mb-6">
            <a-form-item name="password">
              <a-input-password v-model:value="formState.password" size="large" placeholder="密码">
                <template #prefix>
                  <Icon icon="material-symbols:lock" class="text-gray-400" />
                </template>
              </a-input-password>
            </a-form-item>
          </div>
        </div>

        <div class="flex items-center justify-between mb-2">
          <a-checkbox v-model:checked="formState.remember">记住我</a-checkbox>
          <a href="#" class="text-sm text-blue-500 hover:text-blue-700">
            忘记密码?
          </a>
        </div>

        <div>
          <a-button type="primary" size="large" block :loading="loading" @click="handleSubmit" class="h-12 bg-red-500">
            管理员登录
          </a-button>
        </div>
      </a-form>

      <div class="mt-8 pt-6 border-t border-gray-200">
        <div class="text-center text-sm text-gray-600">
          Copyright © {{ new Date().getFullYear() }} Insuraim 保险信息系统
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { login } from '@/api/admin';

const router = useRouter();
const loading = ref(false);

// 表单数据
const formState = reactive({
  username: '',
  password: '',
  remember: true,
});

// 登录处理
const handleSubmit = async () => {
  if (!formState.username || !formState.password) {
    message.warning('请输入管理员账号和密码');
    return;
  }

  loading.value = true;

  try {
    const result = await login({
      username: formState.username,
      password: formState.password
    });

    if (result.data.token) {
      localStorage.setItem('token', result.data.token);
      message.success('管理员登录成功');
      router.push('/dashboard');
    }
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
:deep(.ant-input-affix-wrapper) {
  padding: 12px 16px;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-checkbox-wrapper) {
  font-size: 14px;
}

:deep(.ant-btn-primary) {
  background-color: #1890ff;
}

:deep(.ant-btn-primary:hover) {
  background-color: #40a9ff;
}
</style>