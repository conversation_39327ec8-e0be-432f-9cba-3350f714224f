<template>
  <div class="policies-view">
    <!-- 页面标题和操作按钮 -->
    <a-page-header
      title="保单管理"
      sub-title="管理所有保单信息，支持新增、编辑、删除等操作"
    >
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleAddPolicy">
            <template #icon><Icon icon="material-symbols:add" /></template>
            新增保单
          </a-button>
          <a-button @click="handleImport">
            <template #icon><Icon icon="material-symbols:upload" /></template>
            批量导入
          </a-button>
        </a-space>
      </template>
    </a-page-header>

    <!-- 数据统计卡片 -->
    <a-row :gutter="16" class="mb-6">
      <a-col :span="6">
        <a-card hoverable>
          <template #title>
            <div class="stat-card-title">
              <Icon icon="material-symbols:assignment" class="stat-icon" />
              <span>总保单数</span>
            </div>
          </template>
          <div class="stat-card-content">
            <div class="stat-number">1,234</div>
            <div class="stat-trend up">
              <Icon icon="material-symbols:trending-up" />
              <span>12.5%</span>
            </div>
          </div>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card hoverable>
          <template #title>
            <div class="stat-card-title">
              <Icon icon="material-symbols:check-circle" class="stat-icon" />
              <span>有效保单</span>
            </div>
          </template>
          <div class="stat-card-content">
            <div class="stat-number">856</div>
            <div class="stat-trend up">
              <Icon icon="material-symbols:trending-up" />
              <span>8.2%</span>
            </div>
          </div>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card hoverable>
          <template #title>
            <div class="stat-card-title">
              <Icon icon="material-symbols:warning" class="stat-icon" />
              <span>即将到期</span>
            </div>
          </template>
          <div class="stat-card-content">
            <div class="stat-number">45</div>
            <div class="stat-trend down">
              <Icon icon="material-symbols:trending-down" />
              <span>3.1%</span>
            </div>
          </div>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card hoverable>
          <template #title>
            <div class="stat-card-title">
              <Icon icon="material-symbols:attach-money" class="stat-icon" />
              <span>总保费</span>
            </div>
          </template>
          <div class="stat-card-content">
            <div class="stat-number">¥2,345,678</div>
            <div class="stat-trend up">
              <Icon icon="material-symbols:trending-up" />
              <span>15.3%</span>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 搜索和筛选区域 -->
    <a-card title="搜索条件" class="mb-6">
      <a-form :model="searchForm" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="保单号">
              <a-input v-model:value="searchForm.policyNo" placeholder="请输入保单号" @pressEnter="handleSearch">
                <template #prefix>
                  <Icon icon="material-symbols:search" />
                </template>
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="投保人">
              <a-input v-model:value="searchForm.policyholder" placeholder="请输入投保人姓名" @pressEnter="handleSearch">
                <template #prefix>
                  <Icon icon="material-symbols:person" />
                </template>
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="保单状态">
              <a-select v-model:value="searchForm.status" placeholder="请选择状态">
                <a-select-option value="">全部</a-select-option>
                <a-select-option value="active">有效</a-select-option>
                <a-select-option value="expired">已过期</a-select-option>
                <a-select-option value="cancelled">已取消</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="保险公司">
              <a-select v-model:value="searchForm.company" placeholder="请选择保险公司">
                <a-select-option value="">全部</a-select-option>
                <a-select-option v-for="company in companies" :key="company.id" :value="company.id">
                  {{ company.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <transition name="fade">
          <a-form-item v-if="showAdvancedSearch">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="保费范围">
                  <a-input-group compact>
                    <a-input v-model:value="searchForm.minPremium" placeholder="最小值" style="width: 50%" />
                    <a-input v-model:value="searchForm.maxPremium" placeholder="最大值" style="width: 50%" />
                  </a-input-group>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="生效日期">
                  <a-range-picker v-model:value="searchForm.dateRange" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="产品类型">
                  <a-select v-model:value="searchForm.productType" placeholder="请选择产品类型">
                    <a-select-option value="">全部</a-select-option>
                    <a-select-option value="health">健康险</a-select-option>
                    <a-select-option value="life">寿险</a-select-option>
                    <a-select-option value="accident">意外险</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form-item>
        </transition>
        <a-form-item>
          <a-space>
            <a-button @click="handleReset">重置</a-button>
            <a-button type="primary" @click="handleSearch">搜索</a-button>
            <a-button type="link" @click="toggleAdvancedSearch">
              {{ showAdvancedSearch ? '收起' : '展开' }}高级筛选
              <Icon :icon="showAdvancedSearch ? 'material-symbols:keyboard-arrow-up' : 'material-symbols:keyboard-arrow-down'" />
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 批量操作区域 -->
    <transition name="fade">
      <a-alert
        v-if="selectedPolicies.length > 0"
        message="已选择记录"
        :description="`已选择 ${selectedPolicies.length} 条记录`"
        type="info"
        show-icon
        class="mb-6"
      >
        <template #action>
          <a-space>
            <a-button @click="handleBatchExport">导出选中</a-button>
            <a-button danger @click="handleBatchDelete">批量删除</a-button>
          </a-space>
        </template>
      </a-alert>
    </transition>

    <!-- 保单列表 -->
    <a-card>
      <a-table
        :columns="columns"
        :data-source="policies"
        :row-selection="{ selectedRowKeys: selectedPolicyIds, onChange: onSelectChange }"
        :pagination="false"
        :scroll="{ x: 1200 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" @click="handleView(record)">
                <template #icon><Icon icon="material-symbols:visibility" /></template>
                查看
              </a-button>
              <a-button type="link" @click="handleEdit(record)">
                <template #icon><Icon icon="material-symbols:edit" /></template>
                编辑
              </a-button>
              <a-button type="link" danger @click="handleDelete(record)">
                <template #icon><Icon icon="material-symbols:delete" /></template>
                删除
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>

      <!-- 空状态 -->
      <a-empty v-if="policies.length === 0" description="暂无保单数据" />

      <!-- 分页 -->
      <div class="mt-4">
        <a-pagination
          v-model:current="currentPage"
          :total="total"
          :page-size="pageSize"
          show-size-changer
          show-quick-jumper
          show-total
          @change="handlePageChange"
        />
      </div>
    </a-card>

    <!-- 删除确认对话框 -->
    <a-modal
      v-model:visible="deleteModalVisible"
      title="确认删除"
      @ok="confirmDelete"
      @cancel="cancelDelete"
    >
      <p>确定要删除选中的保单吗？此操作不可恢复。</p>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { Icon } from '@iconify/vue';
import { message, Modal } from 'ant-design-vue';

// 搜索表单
const searchForm = ref({
  policyNo: '',
  policyholder: '',
  status: '',
  company: '',
  minPremium: '',
  maxPremium: '',
  dateRange: [],
  productType: ''
});

// 高级筛选显示状态
const showAdvancedSearch = ref(false);

// 保险公司列表
const companies = ref([
  { id: 1, name: '平安保险' },
  { id: 2, name: '中国人寿' },
  { id: 3, name: '太平洋保险' }
]);

// 表格列定义
const columns = [
  {
    title: '保单号',
    dataIndex: 'policyNo',
    key: 'policyNo',
    sorter: true
  },
  {
    title: '投保人',
    dataIndex: 'policyholder',
    key: 'policyholder',
    sorter: true
  },
  {
    title: '被保人',
    dataIndex: 'insured',
    key: 'insured'
  },
  {
    title: '保险公司',
    dataIndex: 'company',
    key: 'company'
  },
  {
    title: '产品名称',
    dataIndex: 'product',
    key: 'product'
  },
  {
    title: '保费',
    dataIndex: 'premium',
    key: 'premium',
    sorter: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status'
  },
  {
    title: '操作',
    key: 'action',
    width: 200
  }
];

// 保单列表
const policies = ref([
  {
    id: 1,
    policyNo: 'POL20240001',
    policyholder: '张三',
    insured: '李四',
    company: '平安保险',
    product: '重疾险',
    premium: '¥5,000',
    status: 'active'
  },
  {
    id: 2,
    policyNo: 'POL20240002',
    policyholder: '王五',
    insured: '赵六',
    company: '中国人寿',
    product: '医疗险',
    premium: '¥3,000',
    status: 'expired'
  }
]);

// 分页
const currentPage = ref(1);
const total = ref(100);
const totalPages = ref(10);
const pageSize = ref(10);

// 选中状态
const selectedPolicies = ref([]);
const selectedPolicyIds = computed(() => selectedPolicies.value.map(policy => policy.id));

// 删除确认对话框
const deleteModalVisible = ref(false);
const policyToDelete = ref(null);

// 计算属性
const isAllSelected = computed(() => {
  return policies.value.length > 0 && selectedPolicies.value.length === policies.value.length;
});

// 方法
const toggleAdvancedSearch = () => {
  showAdvancedSearch.value = !showAdvancedSearch.value;
};

const onSelectChange = (selectedRowKeys, selectedRows) => {
  selectedPolicies.value = selectedRows;
};

// 获取状态颜色
const getStatusColor = (status) => {
  const colors = {
    active: 'green',
    expired: 'gray',
    cancelled: 'red'
  };
  return colors[status];
};

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    active: '有效',
    expired: '已过期',
    cancelled: '已取消'
  };
  return texts[status];
};

// 处理新增保单
const handleAddPolicy = () => {
  message.info('新增保单');
};

// 处理批量导入
const handleImport = () => {
  message.info('批量导入');
};

// 处理批量导出
const handleBatchExport = () => {
  message.info('批量导出');
};

// 处理批量删除
const handleBatchDelete = () => {
  if (selectedPolicies.value.length === 0) {
    message.warning('请选择要删除的保单');
    return;
  }
  deleteModalVisible.value = true;
};

// 处理搜索
const handleSearch = () => {
  message.info('搜索');
};

// 处理重置
const handleReset = () => {
  searchForm.value = {
    policyNo: '',
    policyholder: '',
    status: '',
    company: '',
    minPremium: '',
    maxPremium: '',
    dateRange: [],
    productType: ''
  };
};

// 处理查看
const handleView = (policy) => {
  message.info(`查看保单 ${policy.policyNo}`);
};

// 处理编辑
const handleEdit = (policy) => {
  message.info(`编辑保单 ${policy.policyNo}`);
};

// 处理删除
const handleDelete = (policy) => {
  policyToDelete.value = policy;
  deleteModalVisible.value = true;
};

// 确认删除
const confirmDelete = () => {
  if (policyToDelete.value) {
    message.success(`删除保单 ${policyToDelete.value.policyNo} 成功`);
  } else if (selectedPolicies.value.length > 0) {
    message.success(`批量删除 ${selectedPolicies.value.length} 条保单成功`);
  }
  deleteModalVisible.value = false;
  policyToDelete.value = null;
  selectedPolicies.value = [];
};

// 取消删除
const cancelDelete = () => {
  deleteModalVisible.value = false;
  policyToDelete.value = null;
};

// 处理分页
const handlePageChange = (page) => {
  currentPage.value = page;
  // TODO: 加载对应页的数据
};

onMounted(() => {
  // TODO: 加载初始数据
});
</script>

<style scoped>
.policies-view {
  min-height: 100%;
  padding: 24px;
  background-color: #f5f7fa;
}

:deep(.ant-page-header) {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

:deep(.ant-card) {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s;
}

:deep(.ant-card):hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:deep(.ant-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f5f7fa;
}

:deep(.ant-btn) {
  border-radius: 6px;
  transition: all 0.3s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

:deep(.ant-btn .anticon) {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

:deep(.ant-btn-primary) {
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.2);
}

:deep(.ant-btn-primary:hover) {
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

:deep(.ant-tag) {
  border-radius: 4px;
  padding: 2px 8px;
}

:deep(.ant-pagination) {
  margin-top: 24px;
  text-align: right;
}

:deep(.ant-alert) {
  border-radius: 8px;
  margin-bottom: 24px;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-input),
:deep(.ant-select-selector) {
  border-radius: 6px;
}

:deep(.ant-space) {
  display: flex;
  align-items: center;
}

.mb-6 {
  margin-bottom: 24px;
}

.mt-4 {
  margin-top: 16px;
}

/* 统计卡片样式 */
.stat-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(0, 0, 0, 0.45);
}

.stat-icon {
  font-size: 20px;
  color: #1890ff;
}

.stat-card-content {
  margin-top: 8px;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
  line-height: 1.5;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  margin-top: 4px;
}

.stat-trend.up {
  color: #52c41a;
}

.stat-trend.down {
  color: #f5222d;
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease;
  max-height: 500px;
  overflow: hidden;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  max-height: 0;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .policies-view {
    padding: 16px;
  }
  
  :deep(.ant-page-header) {
    padding: 16px;
  }
  
  :deep(.ant-col) {
    width: 100%;
  }
}
</style> 