<template>
  <div class="reports-view">
    <!-- 页面标题和操作按钮 -->
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-semibold text-gray-800">报表管理</h1>
      <div class="flex gap-4">
        <button class="btn-primary" @click="handleExport">
          <Icon icon="material-symbols:download" class="mr-2" />
          导出报表
        </button>
        <button class="btn-secondary" @click="handleSchedule">
          <Icon icon="material-symbols:schedule" class="mr-2" />
          定时任务
        </button>
      </div>
    </div>

    <!-- 报表类型选择 -->
    <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="form-group">
          <label class="form-label">报表类型</label>
          <select class="form-input" v-model="reportType">
            <option value="sales">销售报表</option>
            <option value="claims">理赔报表</option>
            <option value="customer">客户报表</option>
            <option value="agent">代理人报表</option>
            <option value="financial">财务报表</option>
          </select>
        </div>
        <div class="form-group">
          <label class="form-label">时间范围</label>
          <select class="form-input" v-model="timeRange">
            <option value="today">今日</option>
            <option value="week">本周</option>
            <option value="month">本月</option>
            <option value="quarter">本季度</option>
            <option value="year">本年</option>
            <option value="custom">自定义</option>
          </select>
        </div>
        <div class="form-group" v-if="timeRange === 'custom'">
          <label class="form-label">开始日期</label>
          <input type="date" class="form-input" v-model="startDate" />
        </div>
        <div class="form-group" v-if="timeRange === 'custom'">
          <label class="form-label">结束日期</label>
          <input type="date" class="form-input" v-model="endDate" />
        </div>
      </div>
      <div class="flex justify-end mt-4">
        <button class="btn-primary" @click="handleGenerate">生成报表</button>
      </div>
    </div>

    <!-- 报表预览 -->
    <div class="bg-white rounded-lg shadow-sm mb-6">
      <div class="p-4 border-b">
        <h2 class="text-lg font-medium text-gray-800">报表预览</h2>
      </div>
      <div class="p-4">
        <!-- 销售报表 -->
        <div v-if="reportType === 'sales'" class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="bg-gray-50 p-4 rounded-lg">
              <div class="text-sm text-gray-500">总销售额</div>
              <div class="text-2xl font-semibold text-blue-500">¥1,234,567</div>
            </div>
            <div class="bg-gray-50 p-4 rounded-lg">
              <div class="text-sm text-gray-500">保单数量</div>
              <div class="text-2xl font-semibold text-green-500">1,234</div>
            </div>
            <div class="bg-gray-50 p-4 rounded-lg">
              <div class="text-sm text-gray-500">平均保费</div>
              <div class="text-2xl font-semibold text-yellow-500">¥1,000</div>
            </div>
            <div class="bg-gray-50 p-4 rounded-lg">
              <div class="text-sm text-gray-500">转化率</div>
              <div class="text-2xl font-semibold text-purple-500">45%</div>
            </div>
          </div>
          <div class="h-80 bg-gray-50 rounded-lg flex items-center justify-center">
            <span class="text-gray-400">销售趋势图表</span>
          </div>
        </div>

        <!-- 理赔报表 -->
        <div v-if="reportType === 'claims'" class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="bg-gray-50 p-4 rounded-lg">
              <div class="text-sm text-gray-500">理赔总数</div>
              <div class="text-2xl font-semibold text-blue-500">123</div>
            </div>
            <div class="bg-gray-50 p-4 rounded-lg">
              <div class="text-sm text-gray-500">理赔金额</div>
              <div class="text-2xl font-semibold text-green-500">¥456,789</div>
            </div>
            <div class="bg-gray-50 p-4 rounded-lg">
              <div class="text-sm text-gray-500">平均处理时间</div>
              <div class="text-2xl font-semibold text-yellow-500">3.5天</div>
            </div>
            <div class="bg-gray-50 p-4 rounded-lg">
              <div class="text-sm text-gray-500">结案率</div>
              <div class="text-2xl font-semibold text-purple-500">85%</div>
            </div>
          </div>
          <div class="h-80 bg-gray-50 rounded-lg flex items-center justify-center">
            <span class="text-gray-400">理赔分析图表</span>
          </div>
        </div>

        <!-- 其他报表类型的内容类似 -->
      </div>
    </div>

    <!-- 报表列表 -->
    <div class="bg-white rounded-lg shadow-sm">
      <div class="p-4 border-b">
        <h2 class="text-lg font-medium text-gray-800">历史报表</h2>
      </div>
      <div class="overflow-x-auto">
        <table class="min-w-full">
          <thead>
            <tr class="bg-gray-50">
              <th class="table-header">报表名称</th>
              <th class="table-header">类型</th>
              <th class="table-header">时间范围</th>
              <th class="table-header">生成时间</th>
              <th class="table-header">生成人</th>
              <th class="table-header">状态</th>
              <th class="table-header">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="report in reports" :key="report.id" class="border-t">
              <td class="table-cell">{{ report.name }}</td>
              <td class="table-cell">
                <span :class="getTypeClass(report.type)">
                  {{ getTypeText(report.type) }}
                </span>
              </td>
              <td class="table-cell">{{ report.timeRange }}</td>
              <td class="table-cell">{{ report.createdAt }}</td>
              <td class="table-cell">{{ report.creator }}</td>
              <td class="table-cell">
                <span :class="getStatusClass(report.status)">
                  {{ getStatusText(report.status) }}
                </span>
              </td>
              <td class="table-cell">
                <div class="flex gap-2">
                  <button class="btn-icon" @click="handleView(report)">
                    <Icon icon="material-symbols:visibility" />
                  </button>
                  <button class="btn-icon" @click="handleDownload(report)">
                    <Icon icon="material-symbols:download" />
                  </button>
                  <button class="btn-icon" @click="handleDelete(report)">
                    <Icon icon="material-symbols:delete" />
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div class="flex justify-between items-center p-4 border-t">
        <div class="text-sm text-gray-600">
          共 {{ total }} 条记录
        </div>
        <div class="flex gap-2">
          <button 
            class="btn-pagination" 
            :disabled="currentPage === 1"
            @click="handlePageChange(currentPage - 1)"
          >
            上一页
          </button>
          <button 
            class="btn-pagination" 
            :disabled="currentPage === totalPages"
            @click="handlePageChange(currentPage + 1)"
          >
            下一页
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { Icon } from '@iconify/vue';
import { message } from 'ant-design-vue';

// 报表类型
const reportType = ref('sales');
const timeRange = ref('month');
const startDate = ref('');
const endDate = ref('');

// 报表列表
const reports = ref([
  {
    id: 1,
    name: '2024年3月销售报表',
    type: 'sales',
    timeRange: '2024-03-01 至 2024-03-31',
    createdAt: '2024-03-31 23:59:59',
    creator: '管理员',
    status: 'completed'
  },
  {
    id: 2,
    name: '2024年第一季度理赔分析',
    type: 'claims',
    timeRange: '2024-01-01 至 2024-03-31',
    createdAt: '2024-04-01 10:00:00',
    creator: '张三',
    status: 'completed'
  }
]);

// 分页
const currentPage = ref(1);
const total = ref(100);
const totalPages = ref(10);

// 获取类型样式
const getTypeClass = (type) => {
  const classes = {
    sales: 'bg-blue-100 text-blue-800',
    claims: 'bg-green-100 text-green-800',
    customer: 'bg-purple-100 text-purple-800',
    agent: 'bg-yellow-100 text-yellow-800',
    financial: 'bg-red-100 text-red-800'
  };
  return `px-2 py-1 rounded-full text-xs ${classes[type]}`;
};

// 获取类型文本
const getTypeText = (type) => {
  const texts = {
    sales: '销售报表',
    claims: '理赔报表',
    customer: '客户报表',
    agent: '代理人报表',
    financial: '财务报表'
  };
  return texts[type];
};

// 获取状态样式
const getStatusClass = (status) => {
  const classes = {
    completed: 'bg-green-100 text-green-800',
    processing: 'bg-yellow-100 text-yellow-800',
    failed: 'bg-red-100 text-red-800'
  };
  return `px-2 py-1 rounded-full text-xs ${classes[status]}`;
};

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    completed: '已完成',
    processing: '处理中',
    failed: '失败'
  };
  return texts[status];
};

// 处理导出
const handleExport = () => {
  message.info('导出报表');
};

// 处理定时任务
const handleSchedule = () => {
  message.info('定时任务');
};

// 处理生成报表
const handleGenerate = () => {
  message.info('生成报表');
};

// 处理查看
const handleView = (report) => {
  message.info(`查看报表 ${report.name}`);
};

// 处理下载
const handleDownload = (report) => {
  message.info(`下载报表 ${report.name}`);
};

// 处理删除
const handleDelete = (report) => {
  message.info(`删除报表 ${report.name}`);
};

// 处理分页
const handlePageChange = (page) => {
  currentPage.value = page;
  // TODO: 加载对应页的数据
};
</script>

<style scoped>
.reports-view {
  min-height: 100%;
}

</style> 