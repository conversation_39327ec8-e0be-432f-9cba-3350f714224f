<template>
  <div class="settings-view">
    <!-- 页面标题 -->
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-semibold text-gray-800">系统设置</h1>
      <div class="flex gap-4">
        <button class="btn-primary" @click="handleSave">
          <Icon icon="material-symbols:save" class="mr-2" />
          保存设置
        </button>
      </div>
    </div>

    <!-- 设置内容 -->
    <div class="bg-white rounded-lg shadow-sm">
      <!-- 标签页导航 -->
      <div class="border-b">
        <div class="flex">
          <button 
            v-for="tab in tabs" 
            :key="tab.key"
            class="px-6 py-3 text-sm font-medium border-b-2 -mb-px"
            :class="[
              currentTab === tab.key 
                ? 'text-blue-600 border-blue-600' 
                : 'text-gray-500 border-transparent hover:text-gray-700 hover:border-gray-300'
            ]"
            @click="currentTab = tab.key"
          >
            <Icon :icon="tab.icon" class="mr-2" />
            {{ tab.name }}
          </button>
        </div>
      </div>

      <!-- 基础设置 -->
      <div v-show="currentTab === 'basic'" class="p-6">
        <div class="max-w-2xl space-y-6">
          <!-- 网站信息 -->
          <div class="space-y-4">
            <h2 class="text-lg font-medium text-gray-900">网站信息</h2>
            <div class="form-group">
              <label class="form-label">网站名称</label>
              <input type="text" class="form-input" v-model="settings.basic.siteName" />
            </div>
            <div class="form-group">
              <label class="form-label">网站描述</label>
              <textarea class="form-input" v-model="settings.basic.siteDescription" rows="3"></textarea>
            </div>
            <div class="form-group">
              <label class="form-label">网站Logo</label>
              <div class="flex items-center gap-4">
                <img :src="settings.basic.logo" class="w-12 h-12 rounded" />
                <button class="btn-secondary">
                  <Icon icon="material-symbols:upload" class="mr-2" />
                  上传Logo
                </button>
              </div>
            </div>
            <div class="form-group">
              <label class="form-label">联系电话</label>
              <input type="text" class="form-input" v-model="settings.basic.phone" />
            </div>
            <div class="form-group">
              <label class="form-label">联系邮箱</label>
              <input type="email" class="form-input" v-model="settings.basic.email" />
            </div>
          </div>

          <!-- 版权信息 -->
          <div class="space-y-4 pt-6 border-t">
            <h2 class="text-lg font-medium text-gray-900">版权信息</h2>
            <div class="form-group">
              <label class="form-label">版权所有</label>
              <input type="text" class="form-input" v-model="settings.basic.copyright" />
            </div>
            <div class="form-group">
              <label class="form-label">备案信息</label>
              <input type="text" class="form-input" v-model="settings.basic.icp" />
            </div>
          </div>
        </div>
      </div>

      <!-- 安全设置 -->
      <div v-show="currentTab === 'security'" class="p-6">
        <div class="max-w-2xl space-y-6">
          <!-- 密码策略 -->
          <div class="space-y-4">
            <h2 class="text-lg font-medium text-gray-900">密码策略</h2>
            <div class="form-group">
              <label class="form-label">最小密码长度</label>
              <input type="number" class="form-input" v-model="settings.security.minPasswordLength" />
            </div>
            <div class="space-y-2">
              <label class="flex items-center">
                <input type="checkbox" v-model="settings.security.requireUppercase" class="mr-2" />
                必须包含大写字母
              </label>
              <label class="flex items-center">
                <input type="checkbox" v-model="settings.security.requireNumbers" class="mr-2" />
                必须包含数字
              </label>
              <label class="flex items-center">
                <input type="checkbox" v-model="settings.security.requireSpecialChars" class="mr-2" />
                必须包含特殊字符
              </label>
            </div>
          </div>

          <!-- 登录设置 -->
          <div class="space-y-4 pt-6 border-t">
            <h2 class="text-lg font-medium text-gray-900">登录设置</h2>
            <div class="form-group">
              <label class="form-label">登录失败次数限制</label>
              <input type="number" class="form-input" v-model="settings.security.maxLoginAttempts" />
            </div>
            <div class="form-group">
              <label class="form-label">锁定时间（分钟）</label>
              <input type="number" class="form-input" v-model="settings.security.lockoutDuration" />
            </div>
            <div class="space-y-2">
              <label class="flex items-center">
                <input type="checkbox" v-model="settings.security.enableCaptcha" class="mr-2" />
                启用验证码
              </label>
              <label class="flex items-center">
                <input type="checkbox" v-model="settings.security.enable2FA" class="mr-2" />
                启用双因素认证
              </label>
            </div>
          </div>
        </div>
      </div>

      <!-- 通知设置 -->
      <div v-show="currentTab === 'notification'" class="p-6">
        <div class="max-w-2xl space-y-6">
          <!-- 邮件配置 -->
          <div class="space-y-4">
            <h2 class="text-lg font-medium text-gray-900">邮件配置</h2>
            <div class="form-group">
              <label class="form-label">SMTP服务器</label>
              <input type="text" class="form-input" v-model="settings.notification.smtpHost" />
            </div>
            <div class="form-group">
              <label class="form-label">SMTP端口</label>
              <input type="number" class="form-input" v-model="settings.notification.smtpPort" />
            </div>
            <div class="form-group">
              <label class="form-label">发件人邮箱</label>
              <input type="email" class="form-input" v-model="settings.notification.smtpEmail" />
            </div>
            <div class="form-group">
              <label class="form-label">发件人密码</label>
              <input type="password" class="form-input" v-model="settings.notification.smtpPassword" />
            </div>
            <button class="btn-secondary" @click="handleTestEmail">
              <Icon icon="material-symbols:mail" class="mr-2" />
              发送测试邮件
            </button>
          </div>

          <!-- 短信配置 -->
          <div class="space-y-4 pt-6 border-t">
            <h2 class="text-lg font-medium text-gray-900">短信配置</h2>
            <div class="form-group">
              <label class="form-label">短信服务商</label>
              <select class="form-input" v-model="settings.notification.smsProvider">
                <option value="aliyun">阿里云</option>
                <option value="tencent">腾讯云</option>
              </select>
            </div>
            <div class="form-group">
              <label class="form-label">AccessKey ID</label>
              <input type="text" class="form-input" v-model="settings.notification.smsAccessKeyId" />
            </div>
            <div class="form-group">
              <label class="form-label">AccessKey Secret</label>
              <input type="password" class="form-input" v-model="settings.notification.smsAccessKeySecret" />
            </div>
            <div class="form-group">
              <label class="form-label">短信签名</label>
              <input type="text" class="form-input" v-model="settings.notification.smsSignName" />
            </div>
            <button class="btn-secondary" @click="handleTestSMS">
              <Icon icon="material-symbols:message" class="mr-2" />
              发送测试短信
            </button>
          </div>
        </div>
      </div>

      <!-- 佣金设置 -->
      <div v-show="currentTab === 'commission'" class="p-6">
        <div class="max-w-2xl space-y-6">
          <!-- 基础佣金设置 -->
          <div class="space-y-4">
            <h2 class="text-lg font-medium text-gray-900">基础佣金设置</h2>
            <div class="form-group">
              <label class="form-label">默认佣金比例（%）</label>
              <input type="number" class="form-input" v-model="settings.commission.defaultRate" />
            </div>
            <div class="form-group">
              <label class="form-label">最低佣金比例（%）</label>
              <input type="number" class="form-input" v-model="settings.commission.minRate" />
            </div>
            <div class="form-group">
              <label class="form-label">最高佣金比例（%）</label>
              <input type="number" class="form-input" v-model="settings.commission.maxRate" />
            </div>
          </div>

          <!-- 奖励佣金设置 -->
          <div class="space-y-4 pt-6 border-t">
            <h2 class="text-lg font-medium text-gray-900">奖励佣金设置</h2>
            <div v-for="(tier, index) in settings.commission.bonusTiers" :key="index" class="space-y-2">
              <div class="flex items-center gap-4">
                <div class="form-group flex-1">
                  <label class="form-label">业绩目标（元）</label>
                  <input type="number" class="form-input" v-model="tier.target" />
                </div>
                <div class="form-group flex-1">
                  <label class="form-label">奖励比例（%）</label>
                  <input type="number" class="form-input" v-model="tier.rate" />
                </div>
                <button class="btn-icon self-end" @click="handleRemoveTier(index)">
                  <Icon icon="material-symbols:delete" />
                </button>
              </div>
            </div>
            <button class="btn-secondary" @click="handleAddTier">
              <Icon icon="material-symbols:add" class="mr-2" />
              添加奖励等级
            </button>
          </div>

          <!-- 结算规则 -->
          <div class="space-y-4 pt-6 border-t">
            <h2 class="text-lg font-medium text-gray-900">结算规则</h2>
            <div class="form-group">
              <label class="form-label">结算周期</label>
              <select class="form-input" v-model="settings.commission.settlementPeriod">
                <option value="monthly">每月</option>
                <option value="quarterly">每季度</option>
              </select>
            </div>
            <div class="form-group">
              <label class="form-label">最低结算金额（元）</label>
              <input type="number" class="form-input" v-model="settings.commission.minSettlementAmount" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { Icon } from '@iconify/vue';
import { message } from 'ant-design-vue';

// 标签页配置
const tabs = [
  { key: 'basic', name: '基础设置', icon: 'material-symbols:settings' },
  { key: 'security', name: '安全设置', icon: 'material-symbols:security' },
  { key: 'notification', name: '通知设置', icon: 'material-symbols:notifications' },
  { key: 'commission', name: '佣金设置', icon: 'material-symbols:payments' }
];

// 当前标签页
const currentTab = ref('basic');

// 设置数据
const settings = ref({
  basic: {
    siteName: '保险代理管理系统',
    siteDescription: '专业的保险代理管理平台',
    logo: 'https://example.com/logo.png',
    phone: '************',
    email: '<EMAIL>',
    copyright: '© 2024 保险代理管理系统',
    icp: '京ICP备12345678号'
  },
  security: {
    minPasswordLength: 8,
    requireUppercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    maxLoginAttempts: 5,
    lockoutDuration: 30,
    enableCaptcha: true,
    enable2FA: false
  },
  notification: {
    smtpHost: 'smtp.example.com',
    smtpPort: 465,
    smtpEmail: '<EMAIL>',
    smtpPassword: '',
    smsProvider: 'aliyun',
    smsAccessKeyId: '',
    smsAccessKeySecret: '',
    smsSignName: '保险代理管理系统'
  },
  commission: {
    defaultRate: 10,
    minRate: 5,
    maxRate: 20,
    bonusTiers: [
      { target: 100000, rate: 2 },
      { target: 500000, rate: 3 },
      { target: 1000000, rate: 5 }
    ],
    settlementPeriod: 'monthly',
    minSettlementAmount: 1000
  }
});

// 保存设置
const handleSave = () => {
  message.success('设置已保存');
};

// 发送测试邮件
const handleTestEmail = () => {
  message.info('测试邮件已发送');
};

// 发送测试短信
const handleTestSMS = () => {
  message.info('测试短信已发送');
};

// 添加奖励等级
const handleAddTier = () => {
  settings.value.commission.bonusTiers.push({
    target: 0,
    rate: 0
  });
};

// 删除奖励等级
const handleRemoveTier = (index) => {
  settings.value.commission.bonusTiers.splice(index, 1);
};
</script>

<style scoped>
.settings-view {
  min-height: 100%;
}

</style> 