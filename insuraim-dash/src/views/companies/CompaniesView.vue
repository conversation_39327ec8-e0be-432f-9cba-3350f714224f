<template>
  <div class="companies-view">
    <!-- 页面标题和操作按钮 -->
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-semibold text-gray-800">保险公司管理</h1>
      <div class="flex gap-4">
        <a-button type="primary" @click="handleAddCompany" class="icon-align-button">
          <template #icon>
            <Icon icon="material-symbols:add" />
          </template>
          新增公司
        </a-button>
        <a-button @click="handleBatchOperation" class="icon-align-button">
          <template #icon>
            <Icon icon="material-symbols:published-with-changes" />
          </template>
          批量操作
        </a-button>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <a-card class="mb-6">
      <a-form layout="inline" :model="searchForm">
        <a-row :gutter="16" style="width: 100%;">
          <a-col :span="6">
            <a-form-item label="公司名称">
              <a-input v-model:value="searchForm.name" placeholder="请输入公司名称" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="公司代码">
              <a-input v-model:value="searchForm.code" placeholder="请输入公司代码" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="公司类型">
              <a-select v-model:value="searchForm.type" placeholder="请选择公司类型" style="width: 100%" allow-clear>
                <a-select-option value="domestic">国内公司</a-select-option>
                <a-select-option value="foreign">国外公司</a-select-option>
                <a-select-option value="joint">合资公司</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="状态">
              <a-select v-model:value="searchForm.status" placeholder="请选择状态" style="width: 100%" allow-clear>
                <a-select-option value="1">合作中</a-select-option>
                <a-select-option value="0">已终止</a-select-option>
                <a-select-option value="2">备选中</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24" style="text-align: right;">
            <a-space class="mt-4">
              <a-button @click="handleReset">重置</a-button>
              <a-button type="primary" @click="handleSearch" class="icon-align-button">
                <template #icon>
                  <Icon icon="material-symbols:search" />
                </template>
                搜索
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <!-- 公司列表表格 -->
    <a-card>
      <a-table :columns="columns" :data-source="companies" :row-key="record => record.id" :row-selection="{
        selectedRowKeys: selectedRowKeys,
        onChange: handleSelectionChange
      }" :pagination="{
        current: currentPage,
        pageSize: pageSize,
        total: total,
        showSizeChanger: true,
        showTotal: total => `共 ${total} 条记录`
      }" @change="handleTableChange" :loading="tableLoading">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'name'">
            <div class="company-info">
              <div v-if="record.logoUrl" class="company-logo">
                <img :src="record.logoUrl" :alt="record.name" class="company-logo-img" />
              </div>
              <div v-else class="company-icon">
                <Icon :icon="getCompanyIcon(record.type)" />
              </div>
              <span class="company-name">{{ record.name }}</span>
            </div>
          </template>

          <template v-else-if="column.key === 'type'">
            <a-tag :color="getTypeColor(record.type)">{{ getTypeText(record.type) }}</a-tag>
          </template>

          <template v-else-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">{{ getStatusText(record.status) }}</a-tag>
          </template>

          <template v-else-if="column.key === 'action'">
            <div class="action-btns">
              <a-button type="link" @click="handleViewDetails(record)" size="small">
                <Icon icon="mdi:eye" />
                <span>查看</span>
              </a-button>
              <a-button type="link" @click="handleEdit(record)" size="small">
                <Icon icon="mdi:pencil" />
                <span>编辑</span>
              </a-button>
              <template v-if="record.status === 1">
                <a-button type="link" @click="handleToggleStatus(record)" size="small">
                  <Icon icon="mdi:pause-circle" />
                  <span>终止合作</span>
                </a-button>
              </template>
              <template v-else-if="record.status === 0">
                <a-button type="link" @click="handleToggleStatus(record)" size="small">
                  <Icon icon="mdi:play-circle" />
                  <span>恢复合作</span>
                </a-button>
              </template>
              <template v-else-if="record.status === 2">
                <a-button type="link" @click="handleToggleStatus(record)" size="small">
                  <Icon icon="mdi:check-circle" />
                  <span>确认合作</span>
                </a-button>
              </template>
              <a-button type="link" @click="handleDelete(record)" danger size="small">
                <Icon icon="mdi:delete" />
                <span>删除</span>
              </a-button>
            </div>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 公司表单对话框 -->
    <a-modal v-model:visible="companyModalVisible" :title="isEdit ? '编辑公司' : '添加公司'" width="600px" :maskClosable="false"
      @cancel="handleModalCancel">
      <a-form ref="companyFormRef" :model="companyForm" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="公司名称" required>
              <a-input v-model:value="companyForm.name" placeholder="请输入公司名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="公司代码" required>
              <a-input v-model:value="companyForm.code" placeholder="请输入公司代码" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="所在地区" required>
              <a-input v-model:value="companyForm.region" placeholder="请输入所在地区" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="公司Logo URL">
              <a-input v-model:value="companyForm.logoUrl" placeholder="请输入Logo图片地址" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="Banner图片URL">
              <a-input v-model:value="companyForm.bannerUrl" placeholder="请输入Banner图片地址" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="公司排名">
              <a-input-number v-model:value="companyForm.companyRank" placeholder="请输入排名" style="width: 100%"
                :precision="0" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="官网地址">
              <a-input v-model:value="companyForm.websiteUrl" placeholder="请输入官网地址" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="征费率">
              <a-input-number v-model:value="companyForm.levyRate" placeholder="请输入征费率" style="width: 100%"
                :precision="4" :step="0.0001" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="汇率货币">
              <a-input v-model:value="companyForm.exchangeCurrency" placeholder="请输入汇率货币" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="汇率">
              <a-input-number v-model:value="companyForm.exchangeRate" placeholder="请输入汇率" style="width: 100%"
                :precision="4" :step="0.0001" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="汇率来源URL">
              <a-input v-model:value="companyForm.exchangeRateUrl" placeholder="请输入汇率来源URL" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="公司介绍">
          <a-textarea v-model:value="companyForm.description" placeholder="请输入公司介绍" :rows="4" />
        </a-form-item>

        <a-form-item label="公司状态">
          <a-radio-group v-model:value="companyForm.status">
            <a-radio :value="1">合作中</a-radio>
            <a-radio :value="0">已终止</a-radio>
            <a-radio :value="2">备选中</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>

      <template #footer>
        <a-button @click="handleModalCancel">取消</a-button>
        <a-button type="primary" @click="handleSaveCompany">保存</a-button>
      </template>
    </a-modal>

    <!-- 公司详情对话框 -->
    <a-modal v-model:visible="detailsModalVisible" title="公司详情" width="700px" @cancel="detailsModalVisible = false">
      <template v-if="currentCompany">
        <div class="company-details">
          <div class="company-details-header">
            <div class="company-details-logo">
              <img v-if="currentCompany.logoUrl" :src="currentCompany.logoUrl" :alt="currentCompany.name"
                class="company-logo-img" />
              <Icon v-else :icon="getCompanyIcon(currentCompany.type)"
                :style="{ fontSize: '48px', color: getTypeColor(currentCompany.type) }" />
            </div>
            <div class="company-details-title">
              <div class="title-row">
                <h2>{{ currentCompany.name }}</h2>
                <div class="company-code">
                  <span class="code-label">公司代码:</span>
                  <span class="code-value">{{ currentCompany.code }}</span>
                </div>
              </div>
              <div class="company-details-tags">
                <a-tag :color="getTypeColor(currentCompany.type)">
                  {{ getTypeText(currentCompany.type) }}
                </a-tag>
                <a-tag :color="getStatusColor(currentCompany.status)">
                  {{ getStatusText(currentCompany.status) }}
                </a-tag>
                <a-tag v-if="currentCompany.isCandidate" color="warning">备选公司</a-tag>
              </div>
            </div>
          </div>

          <a-divider />

          <div class="company-details-info">
            <a-descriptions :column="2" bordered>
              <a-descriptions-item label="所在地区">{{ currentCompany.region }}</a-descriptions-item>
              <a-descriptions-item label="公司排名">
                {{ currentCompany.companyRank || '-' }}
              </a-descriptions-item>
              <a-descriptions-item label="公司介绍" :span="2">
                {{ currentCompany.description || '暂无介绍' }}
              </a-descriptions-item>
            </a-descriptions>
          </div>
        </div>
      </template>

      <template #footer>
        <a-button @click="detailsModalVisible = false">关闭</a-button>
        <a-button type="primary" @click="handleEditFromDetails">编辑</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { Icon } from '@iconify/vue';
import { message, Modal } from 'ant-design-vue';
import {
  getCompanyPage,
  createCompany,
  updateCompany,
  deleteCompany,
  batchDeleteCompanies
} from '@/api/company';

const router = useRouter();

// 搜索表单
const searchForm = reactive({
  name: '',
  code: '',
  type: '',
  status: ''
});

// 公司列表
const companies = ref([]);

// 分页
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 全选
const selectAll = ref(false);

// 表格加载状态
const tableLoading = ref(false);

// 初始化选中的行
const selectedRowKeys = ref([]);

// 表格列定义
const columns = [
  {
    title: '公司名称',
    key: 'name',
    dataIndex: 'name',
    ellipsis: true
  },
  {
    title: '公司代码',
    dataIndex: 'code',
    key: 'code',
    ellipsis: true
  },
  {
    title: '公司类型',
    key: 'type',
    dataIndex: 'type',
    width: 100
  },
  {
    title: '所在地区',
    dataIndex: 'region',
    key: 'region',
    width: 100
  },
  {
    title: '状态',
    key: 'status',
    dataIndex: 'status',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 280
  }
];

// 根据region判断公司类型
const getCompanyTypeByRegion = (region) => {
  return region === '香港' || region === '澳门' ? 'domestic' : 'foreign';
};

// 获取公司图标
const getCompanyIcon = (type) => {
  const icons = {
    'domestic': 'mdi:home-city',
    'foreign': 'mdi:earth',
    'joint': 'mdi:handshake'
  };
  return icons[type] || 'mdi:office-building';
};

// 获取公司类型文本
const getTypeText = (type) => {
  const types = {
    'domestic': '国内公司',
    'foreign': '国外公司',
    'joint': '合资公司'
  };
  return types[type] || '其他';
};

// 获取公司类型颜色
const getTypeColor = (type) => {
  const colors = {
    'domestic': 'blue',
    'foreign': 'orange',
    'joint': 'purple'
  };
  return colors[type] || 'default';
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    0: '已终止',
    1: '合作中',
    2: '备选中'
  };
  return statusMap[status] || '未知';
};

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    0: 'error',
    1: 'success',
    2: 'warning'
  };
  return colorMap[status] || 'default';
};

// 行选择变化处理
const handleSelectionChange = (selectedKeys) => {
  selectedRowKeys.value = selectedKeys;
  // 更新全选状态
  selectAll.value = selectedKeys.length > 0 && selectedKeys.length === companies.value.length;
};

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1; // 搜索时重置到第一页
  loadCompanies({
    pageNum: 1,
    pageSize: pageSize.value,
    ...searchForm
  });
};

// 处理重置
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = '';
  });
  currentPage.value = 1; // 重置时回到第一页
  loadCompanies({
    pageNum: 1,
    pageSize: pageSize.value
  });
};

// 公司表单对话框
const companyModalVisible = ref(false);
const isEdit = ref(false);
const companyFormRef = ref(null);
const companyForm = ref({
  id: null,
  name: '',
  code: '',
  region: '',
  logoUrl: '',
  bannerUrl: '',
  description: '',
  websiteUrl: '',
  companyRank: 0,
  levyRate: 0,
  exchangeCurrency: '',
  exchangeRate: 0,
  exchangeRateUrl: '',
  status: 1
});

// 公司详情对话框
const detailsModalVisible = ref(false);
const currentCompany = ref(null);

// 处理新增公司
const handleAddCompany = () => {
  isEdit.value = false;
  resetCompanyForm();
  companyModalVisible.value = true;
};

// 处理编辑公司
const handleEdit = (company) => {
  isEdit.value = true;
  resetCompanyForm();

  // 复制当前记录到表单
  companyForm.value = {
    id: company.id,
    name: company.name,
    code: company.code,
    region: company.region || '',
    logoUrl: company.logoUrl || '',
    bannerUrl: company.bannerUrl || '',
    description: company.description || '',
    websiteUrl: company.websiteUrl || '',
    companyRank: company.companyRank || 0,
    levyRate: company.levyRate || 0,
    exchangeCurrency: company.exchangeCurrency || '',
    exchangeRate: company.exchangeRate || 0,
    exchangeRateUrl: company.exchangeRateUrl || '',
    status: company.status || 1
  };

  companyModalVisible.value = true;
};

// 从详情页编辑公司
const handleEditFromDetails = () => {
  if (currentCompany.value) {
    handleEdit(currentCompany.value);
    detailsModalVisible.value = false;
  }
};

// 处理查看公司详情
const handleViewDetails = (company) => {
  // 跳转到详情管理页面
  router.push(`/companies/detail/${company.code}`);
};

// 重置公司表单
const resetCompanyForm = () => {
  companyForm.value = {
    id: null,
    name: '',
    code: '',
    region: '',
    logoUrl: '',
    bannerUrl: '',
    description: '',
    websiteUrl: '',
    companyRank: 0,
    levyRate: 0,
    exchangeCurrency: '',
    exchangeRate: 0,
    exchangeRateUrl: '',
    status: 1
  };
  if (companyFormRef.value) {
    companyFormRef.value.resetFields();
  }
};

// 处理对话框取消
const handleModalCancel = () => {
  companyModalVisible.value = false;
};

// 处理保存公司
const handleSaveCompany = async () => {
  // 表单验证
  if (!companyForm.value.name) {
    message.error('请输入公司名称');
    return;
  }
  if (!companyForm.value.code) {
    message.error('请输入公司代码');
    return;
  }
  if (!companyForm.value.region) {
    message.error('请输入所在地区');
    return;
  }

  try {
    // 准备提交的数据
    const companyData = {
      ...companyForm.value,
      status: Number(companyForm.value.status)
    };

    let result;
    if (isEdit.value && companyData.id) {
      // 编辑现有公司
      result = await updateCompany(companyData.id, companyData);
      if (result.code === 200) {
        message.success(`公司"${companyData.name}"已更新`);
      } else {
        throw new Error(result.msg || '更新失败');
      }
    } else {
      // 新增公司
      result = await createCompany(companyData);
      if (result.code === 200) {
        message.success(`公司"${companyData.name}"已创建`);
      } else {
        throw new Error(result.msg || '创建失败');
      }
    }

    companyModalVisible.value = false;
    loadCompanies({
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      ...searchForm
    }); // 重新加载数据
  } catch (error) {
    console.error('保存公司出错:', error);
    message.error('操作失败：' + (error.message || '未知错误'));
  }
};

// 处理删除公司
const handleDelete = async (company) => {
  try {
    // 显示确认对话框
    const confirmed = await new Promise((resolve) => {
      Modal.confirm({
        title: '确认删除',
        content: `确定要删除公司 "${company.name}" 吗？此操作不可恢复。`,
        okText: '确定删除',
        okType: 'danger',
        cancelText: '取消',
        onOk: () => resolve(true),
        onCancel: () => resolve(false)
      });
    });

    if (!confirmed) return;

    const result = await deleteCompany(company.id);
    if (result.code === 200) {
      message.success(`公司"${company.name}"已删除`);
      loadCompanies({
        pageNum: currentPage.value,
        pageSize: pageSize.value,
        ...searchForm
      }); // 重新加载数据
    } else {
      throw new Error(result.msg || '删除失败');
    }
  } catch (error) {
    console.error('删除公司出错:', error);
    message.error('删除失败：' + (error.message || '未知错误'));
  }
};

// 处理切换公司状态
const handleToggleStatus = async (company) => {
  // 当前状态
  const oldStatus = company.status;
  // 目标状态
  let newStatus;

  if (oldStatus === 1) {
    // 合作中 -> 已终止
    newStatus = 0;
  } else if (oldStatus === 0) {
    // 已终止 -> 合作中
    newStatus = 1;
  } else if (oldStatus === 2) {
    // 备选中 -> 合作中
    newStatus = 1;
  }

  // 状态文本
  const statusText = getStatusText(newStatus);

  try {
    const result = await updateCompany(company.id, {
      ...company,
      status: newStatus
    });

    if (result.code === 200) {
      // 更新本地状态
      company.status = newStatus;

      message.success(`公司"${company.name}"状态已更新为${statusText}`);
    } else {
      throw new Error(result.msg || '状态更新失败');
    }
  } catch (error) {
    console.error('切换公司状态出错:', error);
    message.error('状态更新失败：' + (error.message || '未知错误'));
  }
};

// 处理批量操作
const handleBatchOperation = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择公司');
    return;
  }

  Modal.confirm({
    title: '批量操作',
    content: `已选择${selectedRowKeys.value.length}个公司，请选择要执行的操作`,
    okText: '批量删除',
    okType: 'danger',
    cancelText: '取消',
    onOk: () => handleBatchDelete()
  });
};

// 处理批量删除
const handleBatchDelete = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择要删除的公司');
    return;
  }

  try {
    const confirmed = await new Promise((resolve) => {
      Modal.confirm({
        title: '批量删除',
        content: `确定要删除选中的 ${selectedRowKeys.value.length} 家公司吗？此操作不可恢复。`,
        okText: '确定删除',
        okType: 'danger',
        cancelText: '取消',
        onOk: () => resolve(true),
        onCancel: () => resolve(false)
      });
    });

    if (!confirmed) return;

    // 使用批量删除API
    await batchDeleteCompanies(selectedRowKeys.value);

    message.success(`成功删除 ${selectedRowKeys.value.length} 家公司`);
    selectedRowKeys.value = []; // 清空选择
    loadCompanies({
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      ...searchForm
    }); // 重新加载数据
  } catch (error) {
    console.error('批量删除出错:', error);
    message.error('批量删除失败：' + (error.message || '未知错误'));
  }
};

// 处理表格变化
const handleTableChange = (pagination) => {
  currentPage.value = pagination.current;
  pageSize.value = pagination.pageSize;
  // 调用API获取对应页的数据
  loadCompanies({
    pageNum: pagination.current,
    pageSize: pagination.pageSize,
    ...searchForm
  });
};

// 加载数据
const loadCompanies = async (params = {}) => {
  tableLoading.value = true;

  try {
    // 设置默认分页参数
    const requestParams = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      ...params
    };

    const result = await getCompanyPage(requestParams);

    // 处理返回的数据
    if (result && result.data) {
      const data = result.data.records || result.data;
      companies.value = data.map(item => ({
        ...item,
        type: getCompanyTypeByRegion(item.region),
        selected: false,
        logoUrl: item.logoUrl || '', // 确保有logoUrl
      }));

      // 更新总数和当前页码
      total.value = result.data.total || companies.value.length;
      if (result.data.current) {
        currentPage.value = result.data.current;
      }
    } else {
      companies.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error('加载数据失败:', error);
    message.error('加载数据失败：' + (error.message || '未知错误'));
    companies.value = [];
    total.value = 0;
  } finally {
    tableLoading.value = false;
  }
};

// 页面加载时获取数据
onMounted(() => {
  loadCompanies({
    pageNum: 1,
    pageSize: pageSize.value
  });
});
</script>

<style scoped>
.companies-view {
  padding: 24px;
}

.icon-align-button {
  display: inline-flex;
  align-items: center;
}

:deep(.icon-align-button .iconify-inline) {
  font-size: 16px;
  height: 16px;
  width: 16px;
  display: inline-flex;
  align-items: center;
  margin-right: 4px;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-statistic-title) {
  font-size: 14px;
}

:deep(.ant-table-wrapper) {
  margin-top: 16px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-1 {
  margin-right: 0.25rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-4 {
  gap: 1rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-4 {
  margin-top: 1rem;
}

.text-red-500 {
  color: #f56565;
}

.font-medium {
  font-weight: 500;
}

.font-bold {
  font-weight: 700;
}

.text-xl {
  font-size: 1.25rem;
}

.text-lg {
  font-size: 1.125rem;
}

.text-gray-600 {
  color: #718096;
}

.text-gray-800 {
  color: #2d3748;
}

/* 公司详情样式 */
.company-details {
  padding: 0 1rem;
}

.company-details-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.company-details-logo {
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #f0f0f0;
}

.company-details-title {
  flex: 1;
}

.title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.company-code {
  display: flex;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.03);
  padding: 2px 8px;
  border-radius: 4px;
}

.code-label {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
  margin-right: 6px;
}

.code-value {
  font-size: 14px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
}

.company-details-title h2 {
  margin: 0;
  font-size: 18px;
}

.company-details-tags {
  display: flex;
  gap: 8px;
}

.company-details-info {
  margin-top: 16px;
}

/* 公司代码横向布局样式 - 不再需要 */
.company-code-section {
  display: none;
}

.code-item {
  display: none;
}

/* 公司列表样式 */
.company-info {
  display: flex;
  align-items: center;
}

.company-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  background-color: #f0f0f0;
  margin-right: 8px;
  font-size: 18px;
}

.company-logo {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  background-color: #ffffff;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #f0f0f0;
}

.company-logo-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.company-name {
  font-weight: 500;
}

/* 操作按钮样式 */
.action-btns {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

:deep(.action-btns .ant-btn-link) {
  display: inline-flex;
  align-items: center;
  padding: 0 8px;
  height: 28px;
  line-height: 1;
}

:deep(.action-btns .ant-btn-link .iconify-inline) {
  font-size: 16px;
  height: 16px;
  width: 16px;
  margin-right: 4px;
  vertical-align: middle;
}

:deep(.action-btns .ant-btn-link span) {
  line-height: 1;
  vertical-align: middle;
}

/* 按钮通用样式调整 */
:deep(.ant-btn) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

:deep(.ant-btn .iconify-inline) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
}
</style>