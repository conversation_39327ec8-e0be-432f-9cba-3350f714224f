<template>
  <div class="company-detail-view">
    <!-- 页面标题和操作按钮 -->
    <div class="flex justify-between items-center mb-6">
      <div class="flex items-center">
        <a-button @click="goBack" class="mr-4">
          <template #icon>
            <Icon icon="material-symbols:arrow-back" />
          </template>
          返回
        </a-button>
        <h1 class="text-2xl font-semibold text-gray-800">保险公司详情管理</h1>
      </div>
      <div class="flex gap-4">
        <a-button type="primary" @click="handleEditCompany">
          <template #icon>
            <Icon icon="mdi:pencil" />
          </template>
          编辑公司信息
        </a-button>
      </div>
    </div>

    <!-- 公司基本信息 -->
    <a-card class="mb-6" title="基本信息" v-if="companyDetail">
      <div class="company-info-grid">
        <div class="info-item">
          <span class="label">公司名称:</span>
          <span class="value">{{ companyDetail.name }}</span>
        </div>
        <div class="info-item">
          <span class="label">公司代码:</span>
          <span class="value">{{ companyDetail.code }}</span>
        </div>
        <div class="info-item">
          <span class="label">所在地区:</span>
          <span class="value">{{ companyDetail.region }}</span>
        </div>
        <div class="info-item">
          <span class="label">公司排名:</span>
          <span class="value">{{ companyDetail.companyRank || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="label">征费率:</span>
          <span class="value">{{ companyDetail.levyRate || '-' }}</span>
        </div>
        <div class="info-item">
          <span class="label">状态:</span>
          <a-tag :color="getStatusColor(companyDetail.status)">
            {{ getStatusText(companyDetail.status) }}
          </a-tag>
        </div>
        <div class="info-item full-width" v-if="companyDetail.description">
          <span class="label">公司介绍:</span>
          <span class="value">{{ companyDetail.description }}</span>
        </div>
      </div>
    </a-card>

    <!-- 标签页 -->
    <a-card>
      <a-tabs v-model:activeKey="activeTab" type="card">
        <!-- 信用评级标签页 -->
        <a-tab-pane key="ratings" tab="信用评级">
          <div class="flex justify-between items-center mb-4">
            <h3>信用评级列表</h3>
            <a-button type="primary" @click="handleAddRating">
              <template #icon>
                <Icon icon="material-symbols:add" />
              </template>
              添加评级
            </a-button>
          </div>

          <a-table :columns="ratingColumns" :data-source="creditRatings" :row-key="record => record.id"
            :loading="ratingsLoading" :pagination="false">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'time'">
                {{ formatTime(record.time) }}
              </template>
              <template v-else-if="column.key === 'action'">
                <a-space>
                  <a-button type="link" size="small" @click="handleEditRating(record)">编辑</a-button>
                  <a-button type="link" size="small" danger @click="handleDeleteRating(record.id)">删除</a-button>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-tab-pane>

        <!-- 文件管理标签页 -->
        <a-tab-pane key="files" tab="文件管理">
          <div class="flex justify-between items-center mb-4">
            <h3>文件列表</h3>
            <a-space>
              <a-upload :before-upload="handleFileUpload" :show-upload-list="false"
                accept=".pdf,.doc,.docx,.xls,.xlsx,.png,.jpg,.jpeg">
                <a-button type="primary">
                  <template #icon>
                    <Icon icon="material-symbols:upload" />
                  </template>
                  上传文件
                </a-button>
              </a-upload>
            </a-space>
          </div>

          <a-table :columns="fileColumns" :data-source="companyFiles" :row-key="record => record.id"
            :loading="filesLoading" :pagination="false">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'fileName'">
                <a :href="record.filePath" target="_blank" class="file-link">
                  {{ record.fileName }}
                </a>
              </template>
              <template v-else-if="column.key === 'uploadTime'">
                {{ formatTime(record.uploadTime) }}
              </template>
              <template v-else-if="column.key === 'action'">
                <a-space>
                  <a-button type="link" size="small" @click="handleEditFile(record)">编辑</a-button>
                  <a-button type="link" size="small" danger @click="handleDeleteFile(record.id)">删除</a-button>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-tab-pane>

        <!-- 链接管理标签页 -->
        <a-tab-pane key="links" tab="链接管理">
          <div class="flex justify-between items-center mb-4">
            <h3>链接列表</h3>
            <a-button type="primary" @click="handleAddLink">
              <template #icon>
                <Icon icon="material-symbols:add" />
              </template>
              添加链接
            </a-button>
          </div>

          <a-table :columns="linkColumns" :data-source="companyLinks" :row-key="record => record.id"
            :loading="linksLoading" :pagination="false">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'linkUrl'">
                <a :href="record.linkUrl" target="_blank" class="link-url">
                  {{ record.linkUrl }}
                </a>
              </template>
              <template v-else-if="column.key === 'action'">
                <a-space>
                  <a-button type="link" size="small" @click="handleEditLink(record)">编辑</a-button>
                  <a-button type="link" size="small" danger @click="handleDeleteLink(record.id)">删除</a-button>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <!-- 信用评级表单对话框 -->
    <a-modal v-model:visible="ratingModalVisible" :title="isEditRating ? '编辑信用评级' : '添加信用评级'" width="500px"
      @ok="handleSaveRating" @cancel="ratingModalVisible = false">
      <a-form :model="ratingForm" layout="vertical">
        <a-form-item label="评级项目" required>
          <a-input v-model:value="ratingForm.project" placeholder="请输入评级项目" />
        </a-form-item>
        <a-form-item label="评级结果" required>
          <a-input v-model:value="ratingForm.rating" placeholder="请输入评级结果" />
        </a-form-item>
        <a-form-item label="评级机构" required>
          <a-input v-model:value="ratingForm.ratingAgency" placeholder="请输入评级机构" />
        </a-form-item>
        <a-form-item label="评级时间" required>
          <a-date-picker v-model:value="ratingForm.time" style="width: 100%" placeholder="请选择评级时间" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 文件编辑对话框 -->
    <a-modal v-model:visible="fileModalVisible" title="编辑文件信息" width="500px" @ok="handleSaveFile"
      @cancel="fileModalVisible = false">
      <a-form :model="fileForm" layout="vertical">
        <a-form-item label="文件名" required>
          <a-input v-model:value="fileForm.fileName" placeholder="请输入文件名" />
        </a-form-item>
        <a-form-item label="上传者" required>
          <a-input v-model:value="fileForm.author" placeholder="请输入上传者" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 链接表单对话框 -->
    <a-modal v-model:visible="linkModalVisible" :title="isEditLink ? '编辑链接' : '添加链接'" width="500px" @ok="handleSaveLink"
      @cancel="linkModalVisible = false">
      <a-form :model="linkForm" layout="vertical">
        <a-form-item label="链接描述" required>
          <a-input v-model:value="linkForm.description" placeholder="请输入链接描述" />
        </a-form-item>
        <a-form-item label="链接地址" required>
          <a-input v-model:value="linkForm.linkUrl" placeholder="请输入链接地址" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Icon } from '@iconify/vue';
import { message, Modal } from 'ant-design-vue';
import dayjs from 'dayjs';
import {
  getCompanyDetail,
  getCreditRatings,
  addCreditRating,
  updateCreditRating,
  deleteCreditRating,
  getCompanyFiles,
  uploadCompanyFile,
  updateCompanyFile,
  deleteCompanyFile,
  getCompanyLinks,
  addCompanyLink,
  updateCompanyLink,
  deleteCompanyLink
} from '@/api/company';

const route = useRoute();
const router = useRouter();

// 公司代码
const companyCode = computed(() => route.params.code);

// 当前标签页
const activeTab = ref('ratings');

// 公司详情
const companyDetail = ref(null);

// 信用评级相关
const creditRatings = ref([]);
const ratingsLoading = ref(false);
const ratingModalVisible = ref(false);
const isEditRating = ref(false);
const ratingForm = reactive({
  id: null,
  project: '',
  rating: '',
  ratingAgency: '',
  time: null
});

// 文件相关
const companyFiles = ref([]);
const filesLoading = ref(false);
const fileModalVisible = ref(false);
const fileForm = reactive({
  id: null,
  fileName: '',
  author: ''
});

// 链接相关
const companyLinks = ref([]);
const linksLoading = ref(false);
const linkModalVisible = ref(false);
const isEditLink = ref(false);
const linkForm = reactive({
  id: null,
  description: '',
  linkUrl: ''
});

// 表格列定义
const ratingColumns = [
  { title: '评级项目', dataIndex: 'project', key: 'project' },
  { title: '评级结果', dataIndex: 'rating', key: 'rating' },
  { title: '评级机构', dataIndex: 'ratingAgency', key: 'ratingAgency' },
  { title: '评级时间', key: 'time', width: 150 },
  { title: '操作', key: 'action', width: 120 }
];

const fileColumns = [
  { title: '文件名', key: 'fileName', ellipsis: true },
  { title: '上传者', dataIndex: 'author', key: 'author', width: 120 },
  { title: '上传时间', key: 'uploadTime', width: 150 },
  { title: '操作', key: 'action', width: 120 }
];

const linkColumns = [
  { title: '链接描述', dataIndex: 'description', key: 'description' },
  { title: '链接地址', key: 'linkUrl', ellipsis: true },
  { title: '操作', key: 'action', width: 120 }
];

// 工具函数
const formatTime = (timestamp) => {
  if (!timestamp) return '-';
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss');
};

const getStatusText = (status) => {
  const statusMap = {
    0: '已终止',
    1: '合作中',
    2: '备选中'
  };
  return statusMap[status] || '未知';
};

const getStatusColor = (status) => {
  const colorMap = {
    0: 'error',
    1: 'success',
    2: 'warning'
  };
  return colorMap[status] || 'default';
};

// 页面操作
const goBack = () => {
  router.push('/companies');
};

const handleEditCompany = () => {
  router.push(`/companies/edit/${companyCode.value}`);
};

// 加载数据
const loadCompanyDetail = async () => {
  try {
    const result = await getCompanyDetail(companyCode.value);
    if (result.code === 200) {
      companyDetail.value = result.data;
    }
  } catch (error) {
    console.error('加载公司详情失败:', error);
    message.error('加载公司详情失败');
  }
};

const loadCreditRatings = async () => {
  ratingsLoading.value = true;
  try {
    const result = await getCreditRatings(companyCode.value);
    if (result.code === 200) {
      creditRatings.value = result.data || [];
    }
  } catch (error) {
    console.error('加载信用评级失败:', error);
    message.error('加载信用评级失败');
  } finally {
    ratingsLoading.value = false;
  }
};

const loadCompanyFiles = async () => {
  filesLoading.value = true;
  try {
    const result = await getCompanyFiles(companyCode.value);
    if (result.code === 200) {
      companyFiles.value = result.data || [];
    }
  } catch (error) {
    console.error('加载文件列表失败:', error);
    message.error('加载文件列表失败');
  } finally {
    filesLoading.value = false;
  }
};

const loadCompanyLinks = async () => {
  linksLoading.value = true;
  try {
    const result = await getCompanyLinks(companyCode.value);
    if (result.code === 200) {
      companyLinks.value = result.data || [];
    }
  } catch (error) {
    console.error('加载链接列表失败:', error);
    message.error('加载链接列表失败');
  } finally {
    linksLoading.value = false;
  }
};

// 信用评级操作
const handleAddRating = () => {
  isEditRating.value = false;
  Object.assign(ratingForm, {
    id: null,
    project: '',
    rating: '',
    ratingAgency: '',
    time: null
  });
  ratingModalVisible.value = true;
};

const handleEditRating = (record) => {
  isEditRating.value = true;
  Object.assign(ratingForm, {
    id: record.id,
    project: record.project,
    rating: record.rating,
    ratingAgency: record.ratingAgency,
    time: dayjs(record.time)
  });
  ratingModalVisible.value = true;
};

const handleSaveRating = async () => {
  try {
    const data = {
      code: companyCode.value,
      project: ratingForm.project,
      rating: ratingForm.rating,
      ratingAgency: ratingForm.ratingAgency,
      time: ratingForm.time ? ratingForm.time.valueOf() : Date.now()
    };

    if (isEditRating.value) {
      await updateCreditRating(ratingForm.id, data);
      message.success('信用评级更新成功');
    } else {
      await addCreditRating(data);
      message.success('信用评级添加成功');
    }

    ratingModalVisible.value = false;
    loadCreditRatings();
  } catch (error) {
    console.error('保存信用评级失败:', error);
    message.error('保存信用评级失败');
  }
};

const handleDeleteRating = (id) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除这条信用评级记录吗？',
    onOk: async () => {
      try {
        await deleteCreditRating(id);
        message.success('信用评级删除成功');
        loadCreditRatings();
      } catch (error) {
        console.error('删除信用评级失败:', error);
        message.error('删除信用评级失败');
      }
    }
  });
};

// 文件操作
const handleFileUpload = async (file) => {
  try {
    const author = localStorage.getItem('username') || '管理员';
    const result = await uploadCompanyFile(file, companyCode.value, author);
    if (result.code === 200) {
      message.success('文件上传成功');
      loadCompanyFiles();
    }
  } catch (error) {
    console.error('文件上传失败:', error);
    message.error('文件上传失败');
  }
  return false; // 阻止默认上传行为
};

const handleEditFile = (record) => {
  Object.assign(fileForm, {
    id: record.id,
    fileName: record.fileName,
    author: record.author
  });
  fileModalVisible.value = true;
};

const handleSaveFile = async () => {
  try {
    await updateCompanyFile(fileForm.id, {
      fileName: fileForm.fileName,
      author: fileForm.author
    });
    message.success('文件信息更新成功');
    fileModalVisible.value = false;
    loadCompanyFiles();
  } catch (error) {
    console.error('更新文件信息失败:', error);
    message.error('更新文件信息失败');
  }
};

const handleDeleteFile = (id) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除这个文件吗？',
    onOk: async () => {
      try {
        await deleteCompanyFile(id);
        message.success('文件删除成功');
        loadCompanyFiles();
      } catch (error) {
        console.error('删除文件失败:', error);
        message.error('删除文件失败');
      }
    }
  });
};

// 链接操作
const handleAddLink = () => {
  isEditLink.value = false;
  Object.assign(linkForm, {
    id: null,
    description: '',
    linkUrl: ''
  });
  linkModalVisible.value = true;
};

const handleEditLink = (record) => {
  isEditLink.value = true;
  Object.assign(linkForm, {
    id: record.id,
    description: record.description,
    linkUrl: record.linkUrl
  });
  linkModalVisible.value = true;
};

const handleSaveLink = async () => {
  try {
    const data = {
      code: companyCode.value,
      description: linkForm.description,
      linkUrl: linkForm.linkUrl
    };

    if (isEditLink.value) {
      await updateCompanyLink(linkForm.id, data);
      message.success('链接更新成功');
    } else {
      await addCompanyLink(data);
      message.success('链接添加成功');
    }

    linkModalVisible.value = false;
    loadCompanyLinks();
  } catch (error) {
    console.error('保存链接失败:', error);
    message.error('保存链接失败');
  }
};

const handleDeleteLink = (id) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除这个链接吗？',
    onOk: async () => {
      try {
        await deleteCompanyLink(id);
        message.success('链接删除成功');
        loadCompanyLinks();
      } catch (error) {
        console.error('删除链接失败:', error);
        message.error('删除链接失败');
      }
    }
  });
};

// 页面初始化
onMounted(() => {
  loadCompanyDetail();
  loadCreditRatings();
  loadCompanyFiles();
  loadCompanyLinks();
});
</script>

<style scoped>
.company-detail-view {
  padding: 24px;
}

.company-info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.label {
  font-weight: 500;
  color: #666;
  margin-bottom: 4px;
}

.value {
  color: #333;
}

.file-link,
.link-url {
  color: #1890ff;
  text-decoration: none;
}

.file-link:hover,
.link-url:hover {
  text-decoration: underline;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mr-4 {
  margin-right: 1rem;
}

.gap-4 {
  gap: 1rem;
}

.text-2xl {
  font-size: 1.5rem;
}

.font-semibold {
  font-weight: 600;
}

.text-gray-800 {
  color: #2d3748;
}
</style>
