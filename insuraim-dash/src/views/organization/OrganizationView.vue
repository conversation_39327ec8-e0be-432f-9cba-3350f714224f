<template>
  <div class="tenant-view">
    <!-- 页面标题和操作按钮 -->
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-semibold text-gray-800">组织管理</h1>
      <div class="flex gap-4">
        <a-button type="primary" @click="handleAddTenant" class="icon-align-button">
          <template #icon><Icon icon="material-symbols:add" /></template>
          新增组织
        </a-button>
        <a-button @click="handleBatchOperation" class="icon-align-button">
          <template #icon><Icon icon="material-symbols:published-with-changes" /></template>
          批量操作
        </a-button>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <a-card class="mb-6">
      <a-form layout="inline" :model="searchForm">
        <a-row :gutter="16" style="width: 100%;">
          <a-col :span="6">
            <a-form-item label="关键字">
              <a-input v-model:value="searchForm.keyword" placeholder="请输入组织名称或代码" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="组织类型">
              <a-select 
                v-model:value="searchForm.type" 
                placeholder="请选择组织类型"
                style="width: 100%"
                allow-clear
              >
                <a-select-option value="INSURANCE_COMPANY">保险公司</a-select-option>
                <a-select-option value="INSURANCE_AGENT">保险代理人</a-select-option>
                <a-select-option value="INSURANCE_BROKER">保险经纪人</a-select-option>
                <a-select-option value="INSURANCE_AGENCY">保险中介机构</a-select-option>
                <a-select-option value="SALES_TEAM">保险销售团队</a-select-option>
                <a-select-option value="OTHER">其他</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="状态">
              <a-select 
                v-model:value="searchForm.status" 
                placeholder="请选择状态"
                style="width: 100%"
                allow-clear
              >
                <a-select-option value="1">活跃</a-select-option>
                <a-select-option value="0">非活跃</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24" style="text-align: right;">
            <a-space class="mt-4">
              <a-button @click="handleReset" class="icon-align-button">重置</a-button>
              <a-button type="primary" @click="handleSearch" class="icon-align-button">
                <template #icon><Icon icon="material-symbols:search" /></template>
                搜索
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <!-- 组织列表 -->
    <a-card>
      <a-table
        :columns="columns"
        :data-source="tenants"
        :row-key="record => record.id"
        :pagination="{
          current: currentPage,
          total: total,
          showSizeChanger: true,
          showTotal: total => `共 ${total} 条记录`
        }"
        @change="handleTableChange"
        :loading="tableLoading"
      >
        <template #headerCell="{ column }">
          <template v-if="column.key === 'selection'">
            <a-checkbox v-model:checked="selectAll" @change="handleSelectAll" />
          </template>
        </template>

        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'selection'">
            <a-checkbox v-model:checked="record.selected" />
          </template>

          <template v-if="column.key === 'name'">
            <div class="flex items-center">
              <Icon :icon="getTenantIcon(record.type)" class="mr-2 text-gray-600" style="font-size: 18px;" />
              <span>{{ record.name }}</span>
            </div>
          </template>

          <template v-if="column.key === 'type'">
            <a-tag :color="getTypeColor(record.type)">
              {{ getTypeText(record.type) }}
            </a-tag>
          </template>

          <template v-if="column.key === 'status'">
            <a-tag :color="record.status === 1 ? 'success' : 'error'">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <template v-if="column.key === 'phone'">
            <span>{{ record.phone }}</span>
          </template>

          <template v-if="column.key === 'currentUser'">
            <span>{{ record.currentUser }}</span>
          </template>

          <template v-if="column.key === 'maxUser'">
            <span>{{ record.maxUser }}</span>
          </template>

          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleEdit(record)" class="icon-align-button">
                <template #icon><Icon icon="material-symbols:edit" /></template>
                编辑
              </a-button>
              
              <a-button 
                type="link" 
                size="small"
                @click="handleViewDetails(record)"
                class="icon-align-button"
              >
                <template #icon><Icon icon="material-symbols:visibility" /></template>
                查看
              </a-button>
              
              <a-button 
                type="link" 
                size="small"
                @click="handleToggleStatus(record)"
                class="icon-align-button"
              >
                <template #icon>
                  <Icon :icon="record.status === 1 ? 'material-symbols:unpublished' : 'material-symbols:publish'" />
                </template>
                {{ record.status === 1 ? '禁用' : '启用' }}
              </a-button>
              
              <a-popconfirm
                title="确定要删除此组织吗?"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record)"
              >
                <a-button type="link" danger size="small" class="icon-align-button">
                  <template #icon><Icon icon="material-symbols:delete" /></template>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 组织表单对话框 -->
    <a-modal 
      v-model:visible="tenantModalVisible" 
      :title="isEdit ? '编辑组织' : '新增组织'"
      @cancel="handleModalCancel"
      :mask-closable="true"
      :footer="null"
      width="700px"
    >
      <a-form 
        :model="tenantForm" 
        :label-col="{ span: 4 }" 
        :wrapper-col="{ span: 20 }"
        layout="horizontal"
        ref="tenantFormRef"
      >
        <a-form-item 
          label="组织名称" 
          name="name" 
          :rules="[{ required: true, message: '请输入组织名称' }]"
        >
          <a-input v-model:value="tenantForm.name" placeholder="请输入组织名称" />
        </a-form-item>
        
        <a-form-item 
          label="组织代码" 
          name="code" 
          :rules="[{ required: true, message: '请输入组织代码' }]"
        >
          <a-input v-model:value="tenantForm.code" placeholder="请输入组织代码" />
        </a-form-item>
        
        <a-form-item 
          label="组织类型" 
          name="type"
          :rules="[{ required: true, message: '请选择组织类型' }]"
        >
          <a-select v-model:value="tenantForm.type" placeholder="请选择组织类型" style="width: 100%">
            <a-select-option value="INSURANCE_COMPANY">保险公司</a-select-option>
            <a-select-option value="INSURANCE_AGENT">保险代理人</a-select-option>
            <a-select-option value="INSURANCE_BROKER">保险经纪人</a-select-option>
            <a-select-option value="INSURANCE_AGENCY">保险中介机构</a-select-option>
            <a-select-option value="SALES_TEAM">保险销售团队</a-select-option>
            <a-select-option value="OTHER">其他</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item 
          label="组织描述" 
          name="description"
        >
          <a-textarea 
            v-model:value="tenantForm.description" 
            placeholder="请输入组织描述" 
            :rows="2" 
          />
        </a-form-item>
        
        <a-form-item 
          label="排序号" 
          name="sortOrder"
        >
          <a-input-number 
            v-model:value="tenantForm.sortOrder" 
            placeholder="请输入排序号" 
            :min="0" 
            style="width: 100%" 
          />
        </a-form-item>
        
        <a-form-item 
          label="负责人" 
          name="leader"
        >
          <a-input v-model:value="tenantForm.leader" placeholder="请输入负责人" />
        </a-form-item>
        
        <a-form-item 
          label="联系电话" 
          name="phone"
        >
          <a-input v-model:value="tenantForm.phone" placeholder="请输入联系电话" />
        </a-form-item>
        
        <a-form-item 
          label="联系邮箱" 
          name="email"
        >
          <a-input v-model:value="tenantForm.email" placeholder="请输入联系邮箱" />
        </a-form-item>
        
        <a-form-item 
          label="地址" 
          name="address"
        >
          <a-textarea 
            v-model:value="tenantForm.address" 
            placeholder="请输入地址" 
            :rows="2" 
          />
        </a-form-item>
        
        <a-form-item 
          label="最大用户数" 
          name="maxUser"
          :rules="[{ required: true, message: '请输入最大用户数' }]"
        >
          <a-input-number 
            v-model:value="tenantForm.maxUser" 
            placeholder="请输入最大用户数" 
            :min="1" 
            style="width: 100%" 
          />
        </a-form-item>
        
        <a-form-item 
          label="备注" 
          name="remark"
        >
          <a-textarea 
            v-model:value="tenantForm.remark" 
            placeholder="请输入备注" 
            :rows="4" 
          />
        </a-form-item>
        
        <a-form-item label="状态" name="status">
          <a-switch 
            v-model:checked="tenantStatusEnabled" 
            checked-children="活跃" 
            un-checked-children="非活跃" 
          />
        </a-form-item>
        
        <a-form-item :wrapper-col="{ span: 20, offset: 4 }">
          <a-space>
            <a-button @click="handleModalCancel" class="icon-align-button">取消</a-button>
            <a-button type="primary" @click="handleSaveTenant" class="icon-align-button">保存</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 组织详情对话框 -->
    <a-modal
      v-model:visible="detailsModalVisible"
      title="组织详情"
      @cancel="detailsModalVisible = false"
      :mask-closable="true"
      :footer="null"
      width="700px"
    >
      <div v-if="currentTenant" class="tenant-details">
        <div class="detail-header">
          <div class="flex items-center gap-2">
            <Icon :icon="getTenantIcon(currentTenant.type)" class="text-gray-600" style="font-size: 28px;" />
            <h2 class="text-xl font-bold text-gray-800">{{ currentTenant.name }}</h2>
            <a-tag :color="currentTenant.status === 1 ? 'success' : 'error'">
              {{ getStatusText(currentTenant.status) }}
            </a-tag>
          </div>
          <div class="text-gray-500 mt-2">代码: {{ currentTenant.code }}</div>
        </div>
        
        <a-divider />
        
        <div class="detail-content">
          <div class="detail-item">
            <span class="detail-label">组织类型：</span>
            <span class="detail-value">
              <a-tag :color="getTypeColor(currentTenant.type)">
                {{ getTypeText(currentTenant.type) }}
              </a-tag>
            </span>
          </div>
          <div class="detail-item">
            <span class="detail-label">负责人：</span>
            <span class="detail-value">{{ currentTenant.leader }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">联系电话：</span>
            <span class="detail-value">{{ currentTenant.phone }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">联系邮箱：</span>
            <span class="detail-value">{{ currentTenant.email }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">排序号：</span>
            <span class="detail-value">{{ currentTenant.sortOrder }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">当前用户数：</span>
            <span class="detail-value">{{ currentTenant.currentUser }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">最大用户数：</span>
            <span class="detail-value">{{ currentTenant.maxUser }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">创建时间：</span>
            <span class="detail-value">{{ currentTenant.createdAt }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">更新时间：</span>
            <span class="detail-value">{{ currentTenant.updatedAt }}</span>
          </div>
          <div class="detail-item full-width">
            <span class="detail-label">地址：</span>
            <p class="detail-value-block">{{ currentTenant.address }}</p>
          </div>
          <div class="detail-item full-width">
            <span class="detail-label">描述：</span>
            <p class="detail-value-block">{{ currentTenant.description }}</p>
          </div>
        </div>
        
        <a-divider />
        
        <div class="detail-footer">
          <a-space>
            <a-button @click="detailsModalVisible = false">关闭</a-button>
            <a-button type="primary" @click="handleEditFromDetails">编辑组织</a-button>
          </a-space>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { Icon } from '@iconify/vue';
import { message } from 'ant-design-vue';
import { 
  getOrganizationPage, 
  getOrganization, 
  addOrganization, 
  updateOrganization, 
  deleteOrganization,
  batchDeleteOrganizations 
} from '@/api/organization';

// 搜索表单
const searchForm = reactive({
  keyword: '', // 对应后端的keyword参数
  type: '',
  status: ''
});

// 组织列表
const tenants = ref([]);

// 分页
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 全选
const selectAll = ref(false);

// 表格加载状态
const tableLoading = ref(false);

// 表格列定义
const columns = [
  {
    title: '',
    key: 'selection',
    width: 50
  },
  {
    title: '组织名称',
    key: 'name',
    dataIndex: 'name',
    ellipsis: true
  },
  {
    title: '组织代码',
    dataIndex: 'code',
    key: 'code',
    ellipsis: true
  },
  {
    title: '组织类型',
    key: 'type',
    dataIndex: 'type',
    width: 120
  },
  {
    title: '负责人',
    dataIndex: 'leader',
    key: 'leader',
    ellipsis: true
  },
  {
    title: '联系电话',
    key: 'phone',
    dataIndex: 'phone',
    width: 150
  },
  {
    title: '当前用户数',
    key: 'currentUser',
    dataIndex: 'currentUser',
    width: 120
  },
  {
    title: '最大用户数',
    key: 'maxUser',
    dataIndex: 'maxUser',
    width: 120
  },
  {
    title: '状态',
    key: 'status',
    dataIndex: 'status',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 280
  }
];

// 组织表单对话框
const tenantModalVisible = ref(false);
const isEdit = ref(false);
const tenantFormRef = ref(null);
const tenantForm = ref({
  id: null,
  name: '',
  code: '',
  description: '',
  sortOrder: 0,
  type: 'INSURANCE_COMPANY',
  status: 1,
  address: '',
  phone: '',
  email: '',
  leader: '',
  maxUser: 100,
  currentUser: 0,
  createdAt: null,
  updatedAt: null
});

// 组织状态开关
const tenantStatusEnabled = computed({
  get: () => tenantForm.value.status === 1,
  set: (val) => {
    tenantForm.value.status = val ? 1 : 0;
  }
});

// 组织详情对话框
const detailsModalVisible = ref(false);
const currentTenant = ref(null);

// 获取组织类型对应的图标
const getTenantIcon = (type) => {
  const icons = {
    'INSURANCE_COMPANY': 'mdi:bank',
    'INSURANCE_AGENT': 'mdi:account-tie',
    'INSURANCE_BROKER': 'mdi:badge-account',
    'INSURANCE_AGENCY': 'mdi:office-building',
    'SALES_TEAM': 'mdi:account-group',
    'OTHER': 'mdi:office-building'
  };
  return icons[type] || 'mdi:office-building';
};

// 获取类型文本
const getTypeText = (type) => {
  const types = {
    'INSURANCE_COMPANY': '保险公司',
    'INSURANCE_AGENT': '保险代理人',
    'INSURANCE_BROKER': '保险经纪人',
    'INSURANCE_AGENCY': '保险中介机构',
    'SALES_TEAM': '保险销售团队',
    'OTHER': '其他'
  };
  return types[type] || '其他';
};

// 获取类型颜色
const getTypeColor = (type) => {
  const colors = {
    'INSURANCE_COMPANY': 'blue',
    'INSURANCE_AGENT': 'green',
    'INSURANCE_BROKER': 'cyan',
    'INSURANCE_AGENCY': 'purple',
    'SALES_TEAM': 'orange',
    'OTHER': 'default'
  };
  return colors[type] || 'default';
};

// 获取状态文本
const getStatusText = (status) => {
  return status === 1 ? '活跃' : '非活跃';
};


// 处理全选
const handleSelectAll = () => {
  tenants.value.forEach(tenant => {
    tenant.selected = selectAll.value;
  });
};

// 加载组织列表数据
const loadOrganizations = async () => {
  tableLoading.value = true;
  try {
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      keyword: searchForm.keyword,
      type: searchForm.type,
      status: searchForm.status
    };
    
    const data = await getOrganizationPage(params);
    tenants.value = data.data.records.map(item => ({
      ...item,
      selected: false,
      createdAt: item.createdAt ? new Date(item.createdAt).toLocaleString() : '',
      updatedAt: item.updatedAt ? new Date(item.updatedAt).toLocaleString() : ''
    }));
    
    total.value = data.total;
    
    tableLoading.value = false;
  } catch (error) {
    console.error("加载组织列表失败:", error);
    message.error('加载组织列表失败');
    tableLoading.value = false;
  }
};

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1; // 重置到第一页
  loadOrganizations();
};

// 处理重置
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = '';
  });
  currentPage.value = 1; // 重置到第一页
  loadOrganizations();
};

// 处理新增组织
const handleAddTenant = () => {
  isEdit.value = false;
  resetTenantForm();
  tenantModalVisible.value = true;
};

// 处理编辑组织
const handleEdit = (tenant) => {
  isEdit.value = true;
  resetTenantForm();
  Object.keys(tenantForm.value).forEach(key => {
    if (key in tenant) {
      tenantForm.value[key] = tenant[key];
    }
  });
  tenantModalVisible.value = true;
};

// 从详情页编辑组织
const handleEditFromDetails = () => {
  if (currentTenant.value) {
    handleEdit(currentTenant.value);
    detailsModalVisible.value = false;
  }
};

// 处理查看组织详情
const handleViewDetails = async (tenant) => {
  try {
    const data = await getOrganization(tenant.id);
    currentTenant.value = {
      ...data,
      createdAt: data.createdAt ? new Date(data.createdAt).toLocaleString() : '',
      updatedAt: data.updatedAt ? new Date(data.updatedAt).toLocaleString() : ''
    };
    detailsModalVisible.value = true;
  } catch (error) {
    console.error('获取组织详情失败:', error);
    message.error('获取组织详情失败: ' + (error.message || '未知错误'));
  }
};

// 重置组织表单
const resetTenantForm = () => {
  tenantForm.value = {
    id: null,
    name: '',
    code: '',
    description: '',
    sortOrder: 0,
    type: 'INSURANCE_COMPANY',
    status: 1,
    address: '',
    phone: '',
    email: '',
    leader: '',
    maxUser: 100,
    currentUser: 0,
    createdAt: null,
    updatedAt: null
  };
  if (tenantFormRef.value) {
    tenantFormRef.value.resetFields();
  }
};

// 处理对话框取消
const handleModalCancel = () => {
  tenantModalVisible.value = false;
};

// 处理保存组织
const handleSaveTenant = async () => {
  // 表单验证
  if (!tenantForm.value.name) {
    message.error('请输入组织名称');
    return;
  }
  if (!tenantForm.value.code) {
    message.error('请输入组织代码');
    return;
  }
  if (!tenantForm.value.type) {
    message.error('请选择组织类型');
    return;
  }
  
  try {
    if (isEdit.value) {
      // 编辑现有组织
      await updateOrganization(tenantForm.value);
      message.success(`组织"${tenantForm.value.name}"已更新`);
    } else {
      // 新增组织
      await addOrganization(tenantForm.value);
      message.success(`组织"${tenantForm.value.name}"已创建`);
    }
    
    // 刷新列表
    tenantModalVisible.value = false;
    loadOrganizations();
  } catch (error) {
    console.error('保存组织失败:', error);
    message.error('保存组织失败: ' + (error.message || '未知错误'));
  }
};

// 处理删除组织
const handleDelete = async (tenant) => {
  try {
    await deleteOrganization(tenant.id);
    message.success(`组织"${tenant.name}"已删除`);
    // 刷新列表
    loadOrganizations();
  } catch (error) {
    console.error('删除组织失败:', error);
    message.error('删除组织失败: ' + (error.message || '未知错误'));
  }
};

// 处理启用/禁用组织
const handleToggleStatus = async (tenant) => {
  const newStatus = tenant.status === 1 ? 0 : 1;
  const statusText = newStatus === 1 ? '启用' : '禁用';
  
  try {
    // 复制一份数据进行修改
    const updatedTenant = { ...tenant, status: newStatus };
    await updateOrganization(updatedTenant);
    message.success(`组织"${tenant.name}"已${statusText}`);
    // 刷新列表
    loadOrganizations();
  } catch (error) {
    console.error(`${statusText}组织失败:`, error);
    message.error(`${statusText}组织失败: ` + (error.message || '未知错误'));
  }
};

// 处理批量操作
const handleBatchOperation = async () => {
  const selectedTenants = tenants.value.filter(tenant => tenant.selected);
  if (selectedTenants.length === 0) {
    message.warning('请先选择组织');
    return;
  }
  
  try {
    const ids = selectedTenants.map(item => item.id);
    await batchDeleteOrganizations(ids);
    message.success(`成功删除${selectedTenants.length}个组织`);
    // 刷新列表
    loadOrganizations();
  } catch (error) {
    console.error('批量删除组织失败:', error);
    message.error('批量删除组织失败: ' + (error.message || '未知错误'));
  }
};

// 处理表格变化
const handleTableChange = (pagination) => {
  currentPage.value = pagination.current;
  pageSize.value = pagination.pageSize;
  loadOrganizations();
};

// 初始化数据
onMounted(() => {
  loadOrganizations();
});
</script>

<style scoped>
.tenant-view {
  padding: 24px;
}

.icon-align-button {
  display: flex;
  align-items: center;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-statistic-title) {
  font-size: 14px;
}

:deep(.ant-table-wrapper) {
  margin-top: 16px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-1 {
  margin-right: 0.25rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-4 {
  gap: 1rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-4 {
  margin-top: 1rem;
}

.text-red-500 {
  color: #f56565;
}

.font-medium {
  font-weight: 500;
}

.font-bold {
  font-weight: 700;
}

.text-xl {
  font-size: 1.25rem;
}

.text-lg {
  font-size: 1.125rem;
}

.text-gray-600 {
  color: #718096;
}

.text-gray-800 {
  color: #2d3748;
}

/* 组织详情样式 */
.detail-header {
  margin-bottom: 1rem;
}

.detail-content {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.detail-item {
  margin-bottom: 0.75rem;
}

.detail-item.full-width {
  grid-column: span 2;
}

.detail-label {
  color: #718096;
  margin-right: 0.5rem;
  font-weight: 500;
}

.detail-value {
  color: #2d3748;
}

.detail-value-block {
  margin-top: 0.5rem;
  color: #2d3748;
  white-space: pre-line;
  line-height: 1.6;
}

.detail-footer {
  margin-top: 1rem;
  display: flex;
  justify-content: flex-end;
}
</style> 