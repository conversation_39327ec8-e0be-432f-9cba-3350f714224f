<template>
  <div class="users-view">
    <!-- 页面标题和操作按钮 -->
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-semibold text-gray-800">用户管理</h1>
      <div class="flex gap-4">
        <a-button type="primary" @click="handleAddUser" class="icon-align-button">
          <template #icon><Icon icon="material-symbols:add" /></template>
          新增用户
        </a-button>
        <a-button @click="handleImport" class="icon-align-button">
          <template #icon><Icon icon="material-symbols:upload" /></template>
          批量导入
        </a-button>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <a-card class="mb-6">
      <a-form layout="inline" :model="searchForm">
        <a-row :gutter="16" style="width: 100%;">
          <a-col :span="8">
            <a-form-item label="用户名/姓名">
              <a-input v-model:value="searchForm.keyword" placeholder="请输入用户名或姓名" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="用户角色">
              <a-select 
                v-model:value="searchForm.roleName" 
                placeholder="请选择角色"
                style="width: 100%"
                allow-clear
              >
                <a-select-option value="">全部</a-select-option>
                <a-select-option v-for="role in roleList" :key="role.id" :value="role.roleName">
                  {{ role.roleName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="账号状态">
              <a-select 
                v-model:value="searchForm.status" 
                placeholder="请选择状态"
                style="width: 100%"
                allow-clear
              >
                <a-select-option value="">全部</a-select-option>
                <a-select-option value="1">正常</a-select-option>
                <a-select-option value="0">禁用</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24" style="text-align: right;">
            <a-space class="mt-4">
              <a-button @click="handleReset" class="icon-align-button">重置</a-button>
              <a-button type="primary" @click="handleSearch" class="icon-align-button">
                <template #icon><Icon icon="material-symbols:search" /></template>
                搜索
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <!-- 用户列表 -->
    <a-card>
      <a-table
        :columns="columns"
        :data-source="users"
        :row-key="record => record.id"
        :pagination="{
          current: currentPage,
          total: total,
          showSizeChanger: true,
          showTotal: total => `共 ${total} 条记录`
        }"
        @change="handleTableChange"
        :loading="tableLoading"
      >
        <template #headerCell="{ column }">
          <template v-if="column.key === 'selection'">
            <a-checkbox v-model:checked="selectAll" @change="handleSelectAll" />
          </template>
        </template>

        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'selection'">
            <a-checkbox v-model:checked="record.selected" />
          </template>

          <template v-if="column.key === 'user'">
            <div class="flex items-center">
              <img :src="record.avatar" class="w-8 h-8 rounded-full mr-3" />
              <div>
                <div class="font-medium">{{ record.name }}</div>
                <div class="text-xs text-gray-500">{{ record.username }}</div>
              </div>
            </div>
          </template>

          <template v-if="column.key === 'role'">
            <a-tag :color="getRoleColor(record.roleName)">
              {{ record.roleName }}
            </a-tag>
          </template>

          <template v-if="column.key === 'contact'">
            <div>{{ record.phone }}</div>
            <div class="text-xs text-gray-500">{{ record.email }}</div>
          </template>

          <template v-if="column.key === 'lastLogin'">
            <div>{{ record.lastLoginTime }}</div>
            <div class="text-xs text-gray-500">{{ record.lastLoginIp }}</div>
          </template>

          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleEdit(record)" class="icon-align-button">
                <template #icon><Icon icon="material-symbols:edit" /></template>
                编辑
              </a-button>
              
              <a-button 
                type="link" 
                size="small"
                @click="handleViewDetails(record)"
                class="icon-align-button"
              >
                <template #icon><Icon icon="material-symbols:visibility" /></template>
                查看
              </a-button>
              
              <a-button 
                type="link" 
                size="small"
                @click="handleResetPassword(record)"
                class="icon-align-button"
              >
                <template #icon><Icon icon="material-symbols:lock-reset" /></template>
                重置密码
              </a-button>
              
              <a-button 
                type="link" 
                size="small"
                @click="handleToggleStatus(record)"
                class="icon-align-button"
              >
                <template #icon>
                  <Icon :icon="record.status === 1 ? 'material-symbols:lock' : 'material-symbols:lock-open'" />
                </template>
                {{ record.status === 1 ? '锁定' : '解锁' }}
              </a-button>
              
              <a-popconfirm
                title="确定要删除此用户吗?"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record)"
              >
                <a-button type="link" danger size="small" class="icon-align-button">
                  <template #icon><Icon icon="material-symbols:delete" /></template>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 用户表单对话框 -->
    <a-modal 
      v-model:visible="userModalVisible" 
      :title="isEdit ? '编辑用户' : '新增用户'" 
      @cancel="handleModalCancel"
      :mask-closable="true"
      :footer="null"
      width="700px"
    >
      <a-form 
        :model="userForm" 
        :label-col="{ span: 4 }" 
        :wrapper-col="{ span: 20 }"
        layout="horizontal"
        ref="userFormRef"
      >
        <a-form-item 
          label="姓名" 
          name="name" 
          :rules="[{ required: true, message: '请输入姓名' }]"
        >
          <a-input v-model:value="userForm.name" placeholder="请输入姓名" />
        </a-form-item>
        
        <a-form-item 
          label="用户名" 
          name="username"
          :rules="[{ required: true, message: '请输入用户名' }]"
        >
          <a-input v-model:value="userForm.username" placeholder="请输入用户名" />
        </a-form-item>
        
        <a-form-item 
          label="角色" 
          name="roleName"
          :rules="[{ required: true, message: '请选择角色' }]"
        >
          <a-select v-model:value="userForm.roleName" placeholder="请选择角色" style="width: 100%">
            <a-select-option v-for="role in roleList" :key="role.id" :value="role.roleName">
              {{ role.roleName }}
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item 
          label="手机号码" 
          name="phone"
          :rules="[{ required: true, message: '请输入手机号码' }]"
        >
          <a-input v-model:value="userForm.phone" placeholder="请输入手机号码" />
        </a-form-item>
        
        <a-form-item 
          label="邮箱" 
          name="email"
        >
          <a-input v-model:value="userForm.email" placeholder="请输入邮箱" />
        </a-form-item>
        
        <a-form-item label="状态" name="status">
          <a-switch 
            v-model:checked="userStatusEnabled" 
            checked-children="正常" 
            un-checked-children="禁用" 
          />
        </a-form-item>
        
        <a-form-item :wrapper-col="{ span: 20, offset: 4 }">
          <a-space>
            <a-button @click="handleModalCancel">取消</a-button>
            <a-button type="primary" @click="handleSaveUser">保存</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 用户详情对话框 -->
    <a-modal
      v-model:visible="detailsModalVisible"
      title="用户详情"
      @cancel="detailsModalVisible = false"
      :mask-closable="true"
      :footer="null"
      width="700px"
    >
      <div v-if="currentUser" class="user-details">
        <div class="detail-header">
          <div class="flex items-center gap-2">
            <img :src="currentUser.avatar" class="w-16 h-16 rounded-full" />
            <div>
              <h2 class="text-xl font-bold text-gray-800">{{ currentUser.name }}</h2>
              <div class="flex items-center gap-2 mt-1">
                <a-tag :color="getRoleColor(currentUser.roleName)">
                  {{ currentUser.roleName }}
                </a-tag>
                <a-tag :color="getStatusColor(currentUser.status)">
                  {{ getStatusText(currentUser.status) }}
                </a-tag>
              </div>
            </div>
          </div>
        </div>
        
        <a-divider />
        
        <div class="detail-content">
          <div class="detail-item">
            <span class="detail-label">用户名：</span>
            <span class="detail-value">{{ currentUser.username }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">所属组织：</span>
            <span class="detail-value">{{ currentUser.orgName }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">手机号码：</span>
            <span class="detail-value">{{ currentUser.phone }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">邮箱：</span>
            <span class="detail-value">{{ currentUser.email }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">最近登录时间：</span>
            <span class="detail-value">{{ currentUser.lastLoginTime }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">最近登录IP：</span>
            <span class="detail-value">{{ currentUser.lastLoginIp }}</span>
          </div>
        </div>
        
        <a-divider />
        
        <div class="detail-footer">
          <a-space>
            <a-button @click="detailsModalVisible = false">关闭</a-button>
            <a-button type="primary" @click="handleEditFromDetails">编辑用户</a-button>
          </a-space>
        </div>
      </div>
    </a-modal>

    <!-- 重置密码对话框 -->
    <a-modal
      v-model:visible="resetPasswordModalVisible"
      title="重置密码"
      @cancel="cancelResetPassword"
      :mask-closable="true"
      :footer="null"
      width="700px"
    >
      <a-form 
        :model="resetPasswordForm" 
        :label-col="{ span: 4 }" 
        :wrapper-col="{ span: 20 }"
        layout="horizontal"
        ref="resetPasswordFormRef"
      >
        <a-form-item 
          label="用户名" 
          name="username"
        >
          <a-input v-model:value="resetPasswordForm.username" placeholder="请输入用户名" disabled />
        </a-form-item>
        
        <a-form-item 
          label="姓名" 
          name="name"
        >
          <a-input v-model:value="resetPasswordForm.name" placeholder="请输入姓名" disabled />
        </a-form-item>
        
        <a-form-item 
          label="新密码" 
          name="newPassword"
          :rules="[{ required: true, message: '请输入新密码' }]"
        >
          <a-input-password v-model:value="resetPasswordForm.newPassword" placeholder="请输入新密码" />
        </a-form-item>
        
        <a-form-item 
          label="确认密码" 
          name="confirmPassword"
          :rules="[{ required: true, message: '请确认密码' }]"
        >
          <a-input-password v-model:value="resetPasswordForm.confirmPassword" placeholder="请确认密码" />
        </a-form-item>
        
        <a-form-item :wrapper-col="{ span: 20, offset: 4 }">
          <a-space>
            <a-button @click="cancelResetPassword">取消</a-button>
            <a-button type="primary" @click="doResetPassword" :loading="resetPasswordLoading">
              确认
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { Icon } from '@iconify/vue';
import { message } from 'ant-design-vue';
import { 
  getUserPage, 
  addUser, 
  updateUser, 
  deleteUser, 
  batchDeleteUsers, 
  updateUserStatus,
  resetPassword
} from '@/api/user';
import { getRoleList } from '@/api/role';

// 搜索表单
const searchForm = reactive({
  keyword: '',
  roleName: '',
  status: ''
});

// 用户列表
const users = ref([]);

// 角色列表
const roleList = ref([]);

// 分页
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const selectAll = ref(false);

// 表格加载状态
const tableLoading = ref(false);

// 表格列定义
const columns = [
  {
    title: '',
    key: 'selection',
    width: 50
  },
  {
    title: '用户信息',
    key: 'user',
    dataIndex: 'name',
    ellipsis: true
  },
  {
    title: '角色',
    key: 'role',
    dataIndex: 'roleName',
    width: 120
  },
  {
    title: '所属组织',
    dataIndex: 'orgName',
    key: 'organization',
    ellipsis: true
  },
  {
    title: '联系方式',
    key: 'contact',
    dataIndex: 'phone',
    ellipsis: true
  },
  {
    title: '最近登录',
    key: 'lastLogin',
    dataIndex: 'lastLoginTime',
    ellipsis: true
  },
  {
    title: '状态',
    key: 'status',
    dataIndex: 'status',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 340
  }
];

// 用户表单对话框
const userModalVisible = ref(false);
const isEdit = ref(false);
const userFormRef = ref(null);
const userForm = ref({
  id: null,
  username: '',
  name: '',
  avatar: '',
  roleName: '',
  phone: '',
  email: '',
  status: 1,
  password: ''
});

// 用户状态开关
const userStatusEnabled = computed({
  get: () => userForm.value.status === 1,
  set: (val) => {
    userForm.value.status = val ? 1 : 0;
  }
});

// 用户详情对话框
const detailsModalVisible = ref(false);
const currentUser = ref(null);

// 重置密码对话框
const resetPasswordModalVisible = ref(false);
const resetPasswordForm = reactive({
  id: null,
  username: '',
  name: '',
  newPassword: '',
  confirmPassword: ''
});
const resetPasswordLoading = ref(false);

// 加载数据
const loadData = async () => {
  tableLoading.value = true;
  try {
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      keyword: searchForm.keyword || undefined,
      roleName: searchForm.roleName || undefined,
      username: undefined,
      status: searchForm.status || undefined
    };
    
    const result = await getUserPage(params);
    users.value = result.data.records.map(user => ({
      ...user,
      selected: false
    }));
    total.value = result.total;
  } catch (error) {
    console.error('加载用户数据失败', error);
    message.error('加载用户数据失败');
  } finally {
    tableLoading.value = false;
  }
};

// 处理全选
const handleSelectAll = () => {
  users.value.forEach(user => {
    user.selected = selectAll.value;
  });
};

// 处理新增用户
const handleAddUser = () => {
  isEdit.value = false;
  resetUserForm();
  userModalVisible.value = true;
};

// 处理编辑用户
const handleEdit = (user) => {
  isEdit.value = true;
  resetUserForm();
  // 拷贝用户数据到表单
  userForm.value = {
    id: user.id,
    username: user.username,
    name: user.name,
    avatar: user.avatar,
    roleName: user.roleName,
    phone: user.phone,
    email: user.email,
    status: user.status,
    password: '' // 编辑时不需要填密码
  };
  userStatusEnabled.value = user.status === 1;
  userModalVisible.value = true;
};

// 从详情编辑用户
const handleEditFromDetails = () => {
  if (currentUser.value) {
    handleEdit(currentUser.value);
    detailsModalVisible.value = false;
  }
};

// 处理查看用户详情
const handleViewDetails = (user) => {
  currentUser.value = user;
  detailsModalVisible.value = true;
};

// 重置用户表单
const resetUserForm = () => {
  userForm.value = {
    id: null,
    username: '',
    name: '',
    avatar: '',
    roleName: '',
    phone: '',
    email: '',
    status: 1,
    password: ''
  };
  userStatusEnabled.value = true;
  if (userFormRef.value) {
    userFormRef.value.resetFields();
  }
};

// 处理对话框取消
const handleModalCancel = () => {
  userModalVisible.value = false;
};

// 处理保存用户
const handleSaveUser = async () => {
  // 表单验证
  if (!userForm.value.name) {
    message.error('请输入姓名');
    return;
  }
  if (!userForm.value.username) {
    message.error('请输入用户名');
    return;
  }
  if (!userForm.value.roleName) {
    message.error('请选择角色');
    return;
  }
  if (!userForm.value.phone) {
    message.error('请输入手机号码');
    return;
  }
  
  // 设置用户状态
  userForm.value.status = userStatusEnabled.value ? 1 : 0;
  
  try {
    if (isEdit.value) {
      // 编辑现有用户
      await updateUser(userForm.value);
      message.success(`用户"${userForm.value.name}"已更新`);
    } else {
      // 新增用户
      if (!userForm.value.password) {
        message.error('请输入密码');
        return;
      }
      await addUser(userForm.value);
      message.success(`用户"${userForm.value.name}"已创建`);
    }
    
    userModalVisible.value = false;
    loadData(); // 重新加载数据
  } catch (error) {
    console.error('保存用户失败', error);
    message.error('保存用户失败：'+error.message);
  }
};

// 处理导入
const handleImport = () => {
  message.info('批量导入功能暂未实现');
};

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1;
  loadData();
};

// 处理重置
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = '';
  });
  currentPage.value = 1;
  loadData();
};

// 处理重置密码
const handleResetPassword = (user) => {
  resetPasswordForm.id = user.id;
  resetPasswordForm.username = user.username;
  resetPasswordForm.name = user.name;
  resetPasswordForm.newPassword = '';
  resetPasswordForm.confirmPassword = '';
  resetPasswordModalVisible.value = true;
};

// 执行密码重置
const doResetPassword = async () => {
  // 表单验证
  if (!resetPasswordForm.newPassword) {
    message.error('请输入新密码');
    return;
  }
  if (resetPasswordForm.newPassword.length < 6) {
    message.error('密码长度不能少于6位');
    return;
  }
  if (resetPasswordForm.newPassword !== resetPasswordForm.confirmPassword) {
    message.error('两次输入的密码不一致');
    return;
  }
  
  resetPasswordLoading.value = true;
  try {
    await resetPassword(resetPasswordForm.id, resetPasswordForm.newPassword);
    message.success(`用户"${resetPasswordForm.name}"的密码已重置`);
    resetPasswordModalVisible.value = false;
  } catch (error) {
    console.error('重置密码失败', error);
    message.error('重置密码失败：' + (error.message || '未知错误'));
  } finally {
    resetPasswordLoading.value = false;
  }
};

// 取消密码重置
const cancelResetPassword = () => {
  resetPasswordModalVisible.value = false;
};

// 处理状态切换
const handleToggleStatus = async (user) => {
  const newStatus = user.status === 1 ? 0 : 1;
  const statusText = newStatus === 1 ? '启用' : '禁用';
  
  try {
    await updateUserStatus(user.id, newStatus);
    user.status = newStatus;
    
    message.success(`用户"${user.name}"已${statusText}`);
  } catch (error) {
    console.error('更新用户状态失败', error);
    message.error('更新用户状态失败');
  }
};

// 处理删除
const handleDelete = async (user) => {
  try {
    await deleteUser(user.id);
    message.success(`用户"${user.name}"已删除`);
    loadData(); // 重新加载数据
  } catch (error) {
    console.error('删除用户失败', error);
    message.error('删除用户失败');
  }
};

// 处理表格变化
const handleTableChange = (pagination) => {
  currentPage.value = pagination.current;
  pageSize.value = pagination.pageSize;
  loadData();
};

// 获取角色颜色
const getRoleColor = (roleName) => {
  const colors = {
    '超级管理员': 'red',
    '组织管理员': 'purple',
    '代理人': 'green',
    '员工': 'default'
  };
  return colors[roleName] || 'default';
};

// 获取状态颜色
const getStatusColor = (status) => {
  const colors = {
    1: 'success', // 正常
    0: 'error'    // 禁用
  };
  return colors[status] || 'default';
};

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    1: '正常',
    0: '禁用'
  };
  return texts[status] || '未知';
};

// 加载角色列表
const loadRoleList = () => {
  getRoleList().then(res => {
    roleList.value = res;
  }).catch(error => {
    console.error('获取角色列表失败', error);
    message.error('获取角色列表失败');
  });
};

// 加载数据
onMounted(() => {
  loadData();
  loadRoleList();
});
</script>

<style scoped>
.users-view {
  padding: 24px;
}

.icon-align-button {
  display: flex;
  align-items: center;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-table-wrapper) {
  margin-top: 16px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mr-3 {
  margin-right: 0.75rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-4 {
  gap: 1rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-1 {
  margin-top: 0.25rem;
}

.text-red-500 {
  color: #f56565;
}

.font-medium {
  font-weight: 500;
}

.font-bold {
  font-weight: 700;
}

.text-xl {
  font-size: 1.25rem;
}

.text-lg {
  font-size: 1.125rem;
}

.text-gray-600 {
  color: #718096;
}

.text-gray-800 {
  color: #2d3748;
}

.w-8 {
  width: 2rem;
}

.h-8 {
  height: 2rem;
}

.w-16 {
  width: 4rem;
}

.h-16 {
  height: 4rem;
}

.rounded-full {
  border-radius: 9999px;
}

.text-xs {
  font-size: 0.75rem;
}

.text-gray-500 {
  color: #a0aec0;
}

/* 用户详情样式 */
.user-details {
  padding: 0 1rem;
}

.detail-header {
  margin-bottom: 1rem;
}

.detail-content {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.detail-item {
  margin-bottom: 0.75rem;
}

.detail-item.full-width {
  grid-column: span 2;
}

.detail-label {
  color: #718096;
  margin-right: 0.5rem;
  font-weight: 500;
}

.detail-value {
  color: #2d3748;
}

.detail-value-block {
  margin-top: 0.5rem;
  color: #2d3748;
  white-space: pre-line;
  line-height: 1.6;
}

.detail-footer {
  margin-top: 1rem;
  display: flex;
  justify-content: flex-end;
}
</style> 