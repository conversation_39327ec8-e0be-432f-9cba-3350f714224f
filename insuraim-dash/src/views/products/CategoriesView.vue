<template>
  <div class="categories-view">
    <!-- 页面标题和操作按钮 -->
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-semibold text-gray-800">产品分类管理</h1>
      <div class="flex gap-4">
        <a-button type="primary" @click="handleAddCategory" class="icon-align-button">
          <template #icon>
            <Icon icon="material-symbols:add" />
          </template>
          新增分类
        </a-button>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <a-card class="mb-6">
      <a-form layout="inline" :model="searchForm">
        <a-row :gutter="16" style="width: 100%;">
          <a-col :span="6">
            <a-form-item label="分类名称">
              <a-input v-model:value="searchForm.name" placeholder="请输入分类名称" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="分类编码">
              <a-input v-model:value="searchForm.code" placeholder="请输入分类编码" allow-clear />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24" style="text-align: right;">
            <a-space class="mt-4">
              <a-button @click="handleReset" class="icon-align-button">重置</a-button>
              <a-button type="primary" @click="handleSearch" class="icon-align-button">
                <template #icon>
                  <Icon icon="material-symbols:search" />
                </template>
                搜索
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <!-- 分类列表 -->
    <a-card>
      <a-table :columns="columns" :data-source="categories" :row-key="record => record.id" :pagination="{
        current: currentPage,
        total: total,
        showSizeChanger: true,
        showTotal: total => `共 ${total} 条记录`
      }" @change="handleTableChange" :loading="tableLoading">
        <template #bodyCell="{ column, record }">

          <template v-if="column.key === 'name'">
            <div class="flex items-center">
              <Icon icon="mdi:folder" class="mr-2 text-gray-600" style="font-size: 18px;" />
              <span>{{ record.name }}</span>
            </div>
          </template>

          <template v-if="column.key === 'productCount'">
            <a-badge :count="record.productCount" :number-style="{ backgroundColor: '#108ee9' }" />
          </template>

          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleEdit(record)" class="icon-align-button">
                <template #icon>
                  <Icon icon="material-symbols:edit" />
                </template>
                编辑
              </a-button>

              <a-button type="link" size="small" @click="handleViewDetails(record)" class="icon-align-button">
                <template #icon>
                  <Icon icon="material-symbols:visibility" />
                </template>
                查看
              </a-button>

              <a-popconfirm title="确定要删除此分类吗?" ok-text="确定" cancel-text="取消" @confirm="handleDelete(record)">
                <a-button type="link" danger size="small" class="icon-align-button">
                  <template #icon>
                    <Icon icon="material-symbols:delete" />
                  </template>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 分类表单对话框 -->
    <a-modal v-model:visible="categoryModalVisible" :title="isEdit ? '编辑分类' : '新增分类'" @cancel="handleModalCancel"
      :mask-closable="true" :footer="null" width="700px">
      <a-form :model="categoryForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" layout="horizontal"
        ref="categoryFormRef">
        <a-form-item label="分类名称" name="name" :rules="[{ required: true, message: '请输入分类名称' }]">
          <a-input v-model:value="categoryForm.name" placeholder="请输入分类名称" />
        </a-form-item>

        <a-form-item label="分类编码" name="code" :rules="[{ required: true, message: '请输入分类编码' }]">
          <a-input v-model:value="categoryForm.code" placeholder="请输入分类编码" />
        </a-form-item>

        <a-form-item label="分类描述" name="description">
          <a-textarea v-model:value="categoryForm.description" placeholder="请输入分类描述" :rows="4" />
        </a-form-item>

        <a-form-item :wrapper-col="{ span: 20, offset: 4 }">
          <a-space>
            <a-button @click="handleModalCancel" class="icon-align-button">取消</a-button>
            <a-button type="primary" @click="handleSaveCategory" class="icon-align-button">保存</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 分类详情对话框 -->
    <a-modal v-model:visible="detailsModalVisible" title="分类详情" @cancel="detailsModalVisible = false"
      :mask-closable="true" :footer="null" width="700px">
      <div v-if="currentCategory" class="category-details">
        <div class="detail-header">
          <div class="flex items-center gap-2">
            <Icon icon="mdi:folder" class="text-gray-600" style="font-size: 28px;" />
            <h2 class="text-xl font-bold text-gray-800">{{ currentCategory.name }}</h2>
          </div>
          <div class="text-gray-600 text-lg font-medium mt-2">编码: {{ currentCategory.code }}</div>
        </div>

        <a-divider />

        <div class="detail-content">
          <div class="detail-item">
            <span class="detail-label">创建时间：</span>
            <span class="detail-value">{{ currentCategory.createAt }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">更新时间：</span>
            <span class="detail-value">{{ currentCategory.updateAt }}</span>
          </div>
          <div class="detail-item full-width">
            <span class="detail-label">分类描述：</span>
            <p class="detail-value-block">{{ currentCategory.description }}</p>
          </div>
        </div>

        <a-divider />

        <div class="detail-footer">
          <a-space>
            <a-button @click="detailsModalVisible = false">关闭</a-button>
            <a-button type="primary" @click="handleEditFromDetails">编辑分类</a-button>
          </a-space>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { Icon } from '@iconify/vue';
import { message } from 'ant-design-vue';
import { getCategoryPage, saveCategory, updateCategory, deleteCategory } from '@/api/category';

// 搜索表单
const searchForm = reactive({
  name: '',
  code: ''
});

// 分类列表
const categories = ref([]);

// 分页
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 表格加载状态
const tableLoading = ref(false);

// 表格列定义
const columns = [
  {
    title: '分类名称',
    key: 'name',
    dataIndex: 'name',
    ellipsis: true
  },
  {
    title: '分类编码',
    dataIndex: 'code',
    key: 'code',
    ellipsis: true
  },
  {
    title: '操作',
    key: 'action',
    width: 280
  }
];

// 加载分类数据
const loadCategoryData = async () => {
  tableLoading.value = true;
  try {
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      keyword: searchForm.name || searchForm.code || undefined
    };

    const response = await getCategoryPage(params);
    categories.value = response.data.records.map(item => ({
      ...item,
      createAt: item.createAt,
      updateAt: item.updateAt
    }));
    total.value = response.total;
  } catch (error) {
    console.error('获取分类列表失败:', error);
    message.error('获取分类列表失败');
  } finally {
    tableLoading.value = false;
  }
};

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1;
  loadCategoryData();
};

// 处理重置
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = '';
  });
  currentPage.value = 1;
  loadCategoryData();
};

// 处理表格变化
const handleTableChange = (pagination) => {
  currentPage.value = pagination.current;
  pageSize.value = pagination.pageSize;
  loadCategoryData();
};

// 分类表单对话框
const categoryModalVisible = ref(false);
const isEdit = ref(false);
const categoryFormRef = ref(null);
const categoryForm = ref({
  id: null,
  name: '',
  code: '',
  description: '',
  createAt: '',
  updateAt: ''
});

// 分类详情对话框
const detailsModalVisible = ref(false);
const currentCategory = ref(null);

// 处理新增分类
const handleAddCategory = () => {
  isEdit.value = false;
  resetCategoryForm();
  categoryModalVisible.value = true;
};

// 处理编辑分类
const handleEdit = (category) => {
  isEdit.value = true;
  resetCategoryForm();
  Object.keys(categoryForm.value).forEach(key => {
    if (key in category) {
      categoryForm.value[key] = category[key];
    }
  });
  categoryModalVisible.value = true;
};

// 从详情页编辑分类
const handleEditFromDetails = () => {
  if (currentCategory.value) {
    handleEdit(currentCategory.value);
    detailsModalVisible.value = false;
  }
};

// 处理查看详情
const handleViewDetails = (category) => {
  currentCategory.value = category;
  detailsModalVisible.value = true;
};

// 重置分类表单
const resetCategoryForm = () => {
  categoryForm.value = {
    id: null,
    name: '',
    code: '',
    description: '',
    createAt: '',
    updateAt: ''
  };
  if (categoryFormRef.value) {
    categoryFormRef.value.resetFields();
  }
};

// 处理对话框取消
const handleModalCancel = () => {
  categoryModalVisible.value = false;
};

// 处理保存分类
const handleSaveCategory = async () => {
  // 表单验证
  if (!categoryForm.value.name) {
    message.error('请输入分类名称');
    return;
  }
  if (!categoryForm.value.code) {
    message.error('请输入分类编码');
    return;
  }

  try {
    let response;
    if (isEdit.value) {
      // 编辑现有分类
      response = await updateCategory(categoryForm.value);
      if (response && response.code === 200) {
        message.success(`分类"${categoryForm.value.name}"已更新`);
      } else {
        message.error(response?.message || '更新分类失败');
        return;
      }
    } else {
      // 新增分类
      response = await saveCategory(categoryForm.value);
      if (response && response.code === 200) {
        message.success(`分类"${categoryForm.value.name}"已创建`);
      } else {
        message.error(response?.message || '创建分类失败');
        return;
      }
    }

    // 重新加载数据
    categoryModalVisible.value = false;
    loadCategoryData();
  } catch (error) {
    console.error('保存分类失败:', error);
    message.error('保存分类失败: ' + (error.message || '未知错误'));
  }
};

// 处理删除分类
const handleDelete = async (category) => {
  try {
    const response = await deleteCategory(category.id);
    if (response && response.code === 200) {
      message.success(`分类"${category.name}"已删除`);
      loadCategoryData();
    } else {
      message.error(response?.message || '删除分类失败');
    }
  } catch (error) {
    console.error('删除分类失败:', error);
    message.error('删除分类失败: ' + (error.message || '未知错误'));
  }
};

// 初始化数据
onMounted(() => {
  loadCategoryData();
});
</script>

<style scoped>
.categories-view {
  padding: 24px;
}

.icon-align-button {
  display: flex;
  align-items: center;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-statistic-title) {
  font-size: 14px;
}

:deep(.ant-table-wrapper) {
  margin-top: 16px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-1 {
  margin-right: 0.25rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-4 {
  gap: 1rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-4 {
  margin-top: 1rem;
}

.text-red-500 {
  color: #f56565;
}

.font-medium {
  font-weight: 500;
}

.font-bold {
  font-weight: 700;
}

.text-xl {
  font-size: 1.25rem;
}

.text-lg {
  font-size: 1.125rem;
}

.text-gray-600 {
  color: #718096;
}

.text-gray-800 {
  color: #2d3748;
}

/* 分类详情样式 */
.category-details {
  padding: 0 1rem;
}

.detail-header {
  margin-bottom: 1rem;
}

.detail-content {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.detail-item {
  margin-bottom: 0.75rem;
}

.detail-item.full-width {
  grid-column: span 2;
}

.detail-label {
  color: #718096;
  margin-right: 0.5rem;
  font-weight: 500;
}

.detail-value {
  color: #2d3748;
}

.detail-value-block {
  margin-top: 0.5rem;
  color: #2d3748;
  white-space: pre-line;
  line-height: 1.6;
}

.detail-footer {
  margin-top: 1rem;
  display: flex;
  justify-content: flex-end;
}
</style>