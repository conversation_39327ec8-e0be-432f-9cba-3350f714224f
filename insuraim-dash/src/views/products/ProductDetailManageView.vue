<template>
  <div class="product-detail-manage">
    <!-- 页面标题 -->
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-semibold text-gray-800">产品信息管理</h1>
      <div class="flex gap-4">
      </div>
    </div>

    <!-- 分类和产品选择区域 - 左右布局 -->
    <a-row :gutter="16" class="mb-6">
      <!-- 左侧：分类管理 -->
      <a-col :span="12">
        <a-card title="分类管理">
          <template #extra>
            <a-space>
              <a-button type="default" @click="handleLoadTitleTree" :disabled="!filterForm.categoryId"
                class="icon-align-button">
                <template #icon>
                  <Icon icon="material-symbols:refresh" />
                </template>
                刷新标题结构
              </a-button>
            </a-space>
          </template>

          <a-form :model="filterForm">
            <a-form-item label="产品分类">
              <a-select v-model:value="filterForm.categoryId" placeholder="请选择产品分类" style="width: 100%" allow-clear
                :loading="titleTreeLoading" @change="handleCategoryChange">
                <a-select-option v-for="category in categories" :key="category.id" :value="category.id">
                  {{ category.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-form>
        </a-card>
      </a-col>

      <!-- 右侧：产品管理 -->
      <a-col :span="12">
        <a-card title="产品管理">
          <template #extra>
            <a-space>
              <a-button type="primary" @click="handleLoadProductDetail"
                :disabled="!filterForm.categoryId || !filterForm.productId" class="icon-align-button">
                <template #icon>
                  <Icon icon="material-symbols:visibility" />
                </template>
                查看产品详情
              </a-button>
            </a-space>
          </template>

          <a-form :model="filterForm">
            <a-form-item label="选择产品">
              <a-select v-model:value="filterForm.productId" placeholder="请选择产品" style="width: 100%" allow-clear
                :loading="contentLoading" @change="handleProductChange" :disabled="!filterForm.categoryId">
                <a-select-option v-for="product in products" :key="product.id" :value="product.id">
                  <div class="product-option">
                    <div class="product-logo">
                      <img v-if="product.logoUrl" :src="product.logoUrl" :alt="product.productName" class="logo-image"
                        @error="handleLogoError" />
                      <div v-else class="logo-placeholder">
                        <Icon icon="material-symbols:image" />
                      </div>
                    </div>
                    <span class="product-name">{{ product.productName }}</span>
                  </div>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-form>
        </a-card>
      </a-col>
    </a-row>

    <!-- 主要内容区域 -->
    <a-row :gutter="16">
      <!-- 左侧：标题树形结构 -->
      <a-col :span="12">
        <a-card title="标题结构管理" class="h-full">
          <template #extra>
            <a-space>
              <a-button size="small" @click="handleCreateTitle" class="icon-align-button">
                <template #icon>
                  <Icon icon="material-symbols:add" />
                </template>
                新增标题
              </a-button>
              <a-button size="small" @click="handleExpandAll" class="icon-align-button">
                <template #icon>
                  <Icon icon="material-symbols:unfold-more" />
                </template>
                展开全部
              </a-button>
              <a-button size="small" @click="handleCollapseAll" class="icon-align-button">
                <template #icon>
                  <Icon icon="material-symbols:unfold-less" />
                </template>
                收起全部
              </a-button>
            </a-space>
          </template>

          <div v-if="titleTreeLoading" class="text-center py-8">
            <a-spin size="large" />
            <p class="mt-2 text-gray-500">加载中...</p>
          </div>

          <div v-else-if="titleTree.length === 0" class="text-center py-8 text-gray-500">
            <p>暂无标题结构</p>
            <p class="text-sm">请先选择分类并创建标题</p>
          </div>

          <a-tree v-else :tree-data="titleTree" :field-names="{ children: 'children', title: 'titleName', key: 'id' }"
            :expanded-keys="expandedKeys" @expand="handleTreeExpand" show-line block-node>
            <template #title="{ titleName, id, rankValue }">
              <div class="flex justify-between items-center w-full">
                <span class="flex items-center">
                  {{ titleName }}
                  <a-tag size="small" color="blue" class="ml-2">{{ rankValue }}</a-tag>
                </span>
                <a-space size="small" @click.stop>
                  <a-button type="text" size="small" @click="handleEditTitle(id)" class="icon-align-button">
                    <template #icon>
                      <Icon icon="material-symbols:edit" />
                    </template>
                  </a-button>
                  <a-button type="text" size="small" @click="handleAddChildTitle(id)" class="icon-align-button">
                    <template #icon>
                      <Icon icon="material-symbols:add" />
                    </template>
                  </a-button>
                  <a-popconfirm title="确定要删除此标题吗？" @confirm="handleDeleteTitle(id)">
                    <a-button type="text" size="small" danger class="icon-align-button">
                      <template #icon>
                        <Icon icon="material-symbols:delete" />
                      </template>
                    </a-button>
                  </a-popconfirm>
                </a-space>
              </div>
            </template>
          </a-tree>
        </a-card>
      </a-col>

      <!-- 右侧：内容管理 -->
      <a-col :span="12">
        <a-card title="内容管理" class="h-full">
          <template #extra>
            <a-button type="primary" size="small" @click="handleAddContent" :disabled="!filterForm.productId"
              class="icon-align-button">
              <template #icon>
                <Icon icon="material-symbols:add" />
              </template>
              添加内容
            </a-button>
          </template>

          <div v-if="contentTreeLoading" class="text-center py-8">
            <a-spin size="large" />
            <p class="mt-2 text-gray-500">加载中...</p>
          </div>

          <div v-else-if="productContentTree.length === 0" class="text-center py-8 text-gray-500">
            <p>暂无产品内容</p>
            <p class="text-sm">请先选择产品并添加内容</p>
          </div>

          <div v-else class="content-tree" ref="contentTreeRef">
            <div v-for="section in productContentTree" :key="section.titleId" class="content-section">
              <div class="section-header">
                <h4 class="section-title">{{ section.titleName }}</h4>
                <a-tag size="small" color="blue">{{ section.rankValue }}</a-tag>
              </div>

              <div v-if="section.children && section.children.length > 0" class="section-children">
                <div v-for="child in section.children" :key="child.titleId" class="content-item">
                  <div class="content-header">
                    <span class="content-title">{{ child.titleName }}</span>
                    <a-space size="small">
                      <a-button type="text" size="small" @click="handleEditContentByTitle(child)"
                        class="icon-align-button">
                        <template #icon>
                          <Icon icon="material-symbols:edit" />
                        </template>
                      </a-button>
                      <a-popconfirm title="确定要删除此内容吗？" @confirm="handleDeleteContentByTitle(child)">
                        <a-button type="text" size="small" danger class="icon-align-button">
                          <template #icon>
                            <Icon icon="material-symbols:delete" />
                          </template>
                        </a-button>
                      </a-popconfirm>
                    </a-space>
                  </div>
                  <div class="content-value">
                    {{ (child.contents && child.contents.length > 0) ? child.contents[0].contentValue : '暂无内容' }}
                  </div>
                </div>
              </div>

              <div v-else class="no-children">
                <p class="text-gray-500 text-sm">该分类下暂无内容</p>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 标题表单对话框 -->
    <a-modal v-model:visible="titleModalVisible" :title="titleModalTitle" @cancel="handleTitleModalCancel"
      :mask-closable="false" width="600px">
      <a-form :model="titleForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }" ref="titleFormRef">
        <a-form-item label="分类" name="categoryId">
          <a-select v-model:value="titleForm.categoryId" placeholder="请选择分类" disabled>
            <a-select-option v-for="category in categories" :key="category.id" :value="category.id">
              {{ category.name }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="父标题" name="parentId">
          <a-tree-select v-model:value="titleForm.parentId" :tree-data="titleTreeForSelect"
            :field-names="{ children: 'children', label: 'titleName', value: 'id' }" placeholder="请选择父标题（可选）"
            allow-clear tree-default-expand-all />
        </a-form-item>

        <a-form-item label="标题名称" name="titleName" :rules="[{ required: true, message: '请输入标题名称' }]">
          <a-input v-model:value="titleForm.titleName" placeholder="请输入标题名称" />
        </a-form-item>

        <a-form-item label="排序" name="rankValue" :rules="[{ required: true, message: '请输入排序' }]">
          <a-input-number v-model:value="titleForm.rankValue" :min="1" placeholder="请输入排序" style="width: 100%" />
        </a-form-item>
      </a-form>

      <template #footer>
        <a-space>
          <a-button @click="handleTitleModalCancel">取消</a-button>
          <a-button type="primary" @click="handleSaveTitle" :loading="titleSaving">保存</a-button>
        </a-space>
      </template>
    </a-modal>

    <!-- 内容表单对话框 -->
    <a-modal v-model:visible="contentModalVisible" :title="contentModalTitle" @cancel="handleContentModalCancel"
      :mask-closable="false" width="700px">
      <a-form :model="contentForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" ref="contentFormRef">
        <a-form-item label="产品" name="productId">
          <a-select v-model:value="contentForm.productId" placeholder="请选择产品" disabled>
            <a-select-option v-for="product in products" :key="product.id" :value="product.id">
              <div class="product-option">
                <div class="product-logo">
                  <img v-if="product.logoUrl" :src="product.logoUrl" :alt="product.productName" class="logo-image"
                    @error="handleLogoError" />
                  <div v-else class="logo-placeholder">
                    <Icon icon="material-symbols:image" />
                  </div>
                </div>
                <span class="product-name">{{ product.productName }}</span>
              </div>
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="标题" name="titleId" :rules="[{ required: true, message: '请选择标题' }]">
          <a-select v-model:value="contentForm.titleId" placeholder="请选择标题（只能选择子标题）" style="width: 100%">
            <a-select-option v-for="title in leafTitlesForContent" :key="title.id" :value="title.id">
              {{ title.titleName }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="内容" name="contentValue">
          <a-textarea v-model:value="contentForm.contentValue" placeholder="请输入内容" :rows="6" show-count
            :maxlength="2000" />
        </a-form-item>
      </a-form>

      <template #footer>
        <a-space>
          <a-button @click="handleContentModalCancel">取消</a-button>
          <a-button type="primary" @click="handleSaveContent" :loading="contentSaving">保存</a-button>
        </a-space>
      </template>
    </a-modal>

    <!-- 产品详情查看对话框 -->
    <a-modal v-model:visible="detailModalVisible" title="产品详情" @cancel="detailModalVisible = false"
      :mask-closable="true" :footer="null" width="900px">
      <div v-if="productDetailLoading" class="text-center py-8">
        <a-spin size="large" />
        <p class="mt-2 text-gray-500">加载中...</p>
      </div>

      <div v-else-if="productDetailTree.length === 0" class="text-center py-8 text-gray-500">
        <p>暂无产品详情</p>
      </div>

      <div v-else class="product-detail-tree">
        <div v-for="(section, index) in productDetailTree" :key="index" class="detail-section">
          <h3 class="section-title">{{ section.name }}</h3>
          <div v-if="section.attributes && section.attributes.length > 0" class="attributes-grid">
            <div v-for="attr in section.attributes" :key="attr.attribute" class="attribute-item">
              <span class="attribute-name">{{ attr.name }}：</span>
              <span class="attribute-value">{{ attr.value || '暂无内容' }}</span>
            </div>
          </div>
          <div v-else class="no-attributes">
            <p class="text-gray-500">暂无属性信息</p>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { Icon } from '@iconify/vue';
import { message } from 'ant-design-vue';
import {
  getTitleTree,
  createTitle,
  updateTitle,
  deleteTitle,
  getTitleById,
  saveContent,
  deleteContent,
  getContentsByProductId,
  getProductDetailTree,
  getProductDetailTreeOriginal,
  getProductList
} from '@/api/product';
import { getCategoryList } from '@/api/category';

// 筛选表单
const filterForm = reactive({
  categoryId: null,
  productId: null
});

// 分类列表
const categories = ref([]);
// 产品列表
const products = ref([]);

// 标题树相关
const titleTree = ref([]);
const titleTreeLoading = ref(false);
const expandedKeys = ref([]);

// 内容相关
const productContents = ref([]);
const contentLoading = ref(false);

// 树形结构的产品内容
const productContentTree = ref([]);
const contentTreeLoading = ref(false);

// 标题表单对话框
const titleModalVisible = ref(false);
const titleModalTitle = ref('');
const titleForm = ref({
  id: null,
  categoryId: null,
  parentId: null,
  titleName: '',
  rankValue: 1
});
const titleFormRef = ref(null);
const titleSaving = ref(false);
const isEditingTitle = ref(false);

// 内容表单对话框
const contentModalVisible = ref(false);
const contentModalTitle = ref('');
const contentForm = ref({
  id: null,
  productId: null,
  titleId: null,
  contentValue: ''
});
const contentFormRef = ref(null);
const contentSaving = ref(false);
const isEditingContent = ref(false);

// 产品详情查看对话框
const detailModalVisible = ref(false);
const productDetailTree = ref([]);
const productDetailLoading = ref(false);

// 内容树滚动位置
const contentTreeRef = ref(null);

// 计算属性：用于选择的标题树（过滤掉当前编辑的标题及其子标题）
const titleTreeForSelect = computed(() => {
  if (isEditingTitle.value && titleForm.value.id) {
    return filterTitleTree(titleTree.value, titleForm.value.id);
  }
  return titleTree.value;
});

// 计算属性：用于内容模块的标题选择（只显示子标题，不包括一级标题）
const leafTitlesForContent = computed(() => {
  const getChildTitles = (nodes, isTopLevel = true) => {
    let childTitles = [];
    nodes.forEach(node => {
      if (!isTopLevel) {
        // 不是顶级节点，说明是子标题，可以添加到选择列表
        childTitles.push({
          id: node.id,
          titleName: node.titleName,
          rankValue: node.rankValue
        });
      }

      // 递归处理子节点
      if (node.children && node.children.length > 0) {
        childTitles = childTitles.concat(getChildTitles(node.children, false));
      }
    });
    return childTitles;
  };

  return getChildTitles(titleTree.value);
});

// 过滤标题树（用于父标题选择时排除自己、子标题和同级标题）
const filterTitleTree = (tree, excludeId) => {
  // 首先找到要排除的标题及其父标题ID
  const findNodeInfo = (nodes, targetId, parentId = null) => {
    for (const node of nodes) {
      if (node.id === targetId) {
        return { node, parentId };
      }
      if (node.children) {
        const result = findNodeInfo(node.children, targetId, node.id);
        if (result) return result;
      }
    }
    return null;
  };

  const nodeInfo = findNodeInfo(tree, excludeId);
  const excludeParentId = nodeInfo?.parentId;

  const filterNodes = (nodes, currentParentId = null) => {
    return nodes.filter(node => {
      // 排除自己
      if (node.id === excludeId) {
        return false;
      }
      // 如果当前节点和要排除的节点有相同的父节点，说明是同级，也要排除
      if (currentParentId === excludeParentId && excludeParentId !== null) {
        return false;
      }
      return true;
    }).map(node => {
      // 创建节点的深拷贝，避免修改原始数据
      const newNode = { ...node };
      if (node.children && node.children.length > 0) {
        newNode.children = filterNodes(node.children, node.id);
      }
      return newNode;
    });
  };

  return filterNodes(tree);
};

// 加载分类列表
const loadCategories = async () => {
  try {
    const res = await getCategoryList();
    categories.value = res.data
  } catch (error) {
    console.error('加载分类列表失败:', error);
    message.error('加载分类列表失败');
  }
};

// 加载产品列表
const loadProducts = async (categoryId = null) => {
  try {
    const res = await getProductList(categoryId);
    products.value = res.data || [];
  } catch (error) {
    console.error('加载产品列表失败:', error);
    message.error('加载产品列表失败');
  }
};

// 处理分类变化
const handleCategoryChange = () => {
  filterForm.productId = null;
  titleTree.value = [];
  productContents.value = [];
  productContentTree.value = [];

  // 自动加载标题结构和产品列表
  if (filterForm.categoryId) {
    handleLoadTitleTree();
    loadProducts(filterForm.categoryId);
  }
};

// 处理产品变化
const handleProductChange = () => {
  productContents.value = [];
  productContentTree.value = [];
  if (filterForm.productId) {
    loadProductContents();
    loadProductContentTree();
  }
};



// 加载标题树
const handleLoadTitleTree = async () => {
  if (!filterForm.categoryId) {
    message.warning('请先选择分类');
    return;
  }

  titleTreeLoading.value = true;
  try {
    const res = await getTitleTree(filterForm.categoryId);
    titleTree.value = res.data || [];
    // 默认展开第一层
    expandedKeys.value = titleTree.value.map(item => item.id);
  } catch (error) {
    console.error('加载标题树失败:', error);
    message.error('加载标题树失败');
  } finally {
    titleTreeLoading.value = false;
  }
};

// 加载产品内容
const loadProductContents = async () => {
  if (!filterForm.productId) return;

  contentLoading.value = true;
  try {
    const res = await getContentsByProductId(filterForm.productId);
    productContents.value = res.data || [];
  } catch (error) {
    console.error('加载产品内容失败:', error);
    message.error('加载产品内容失败');
  } finally {
    contentLoading.value = false;
  }
};

// 加载树形结构的产品内容
const loadProductContentTree = async (preserveScrollPosition = false) => {
  if (!filterForm.categoryId || !filterForm.productId) return;

  // 保存当前滚动位置
  let scrollTop = 0;
  if (preserveScrollPosition && contentTreeRef.value) {
    scrollTop = contentTreeRef.value.scrollTop;
  }

  contentTreeLoading.value = true;
  try {
    const res = await getProductDetailTreeOriginal(filterForm.categoryId, filterForm.productId);
    productContentTree.value = res.data || [];

    // 恢复滚动位置
    if (preserveScrollPosition && contentTreeRef.value) {
      // 使用 nextTick 确保DOM已更新
      await new Promise(resolve => {
        setTimeout(() => {
          if (contentTreeRef.value) {
            contentTreeRef.value.scrollTop = scrollTop;
          }
          resolve();
        }, 50); // 给一点延迟确保内容已渲染
      });
    }
  } catch (error) {
    console.error('加载产品内容树失败:', error);
    message.error('加载产品内容树失败');
  } finally {
    contentTreeLoading.value = false;
  }
};

// 展开全部
const handleExpandAll = () => {
  const getAllKeys = (tree) => {
    let keys = [];
    tree.forEach(node => {
      keys.push(node.id);
      if (node.children) {
        keys = keys.concat(getAllKeys(node.children));
      }
    });
    return keys;
  };
  expandedKeys.value = getAllKeys(titleTree.value);
};

// 收起全部
const handleCollapseAll = () => {
  expandedKeys.value = [];
};

// 处理树展开
const handleTreeExpand = (keys) => {
  expandedKeys.value = keys;
};

// 创建标题
const handleCreateTitle = () => {
  if (!filterForm.categoryId) {
    message.warning('请先选择分类');
    return;
  }

  isEditingTitle.value = false;
  titleModalTitle.value = '新增标题';
  titleForm.value = {
    id: null,
    categoryId: filterForm.categoryId,
    parentId: null,
    titleName: '',
    rankValue: 1
  };
  titleModalVisible.value = true;
};

// 编辑标题
const handleEditTitle = async (id) => {
  try {
    const res = await getTitleById(id);
    const titleData = res.data;

    isEditingTitle.value = true;
    titleModalTitle.value = '编辑标题';
    titleForm.value = {
      id: titleData.id,
      categoryId: titleData.categoryId,
      parentId: titleData.parentId,
      titleName: titleData.titleName,
      rankValue: titleData.rankValue
    };
    titleModalVisible.value = true;
  } catch (error) {
    console.error('获取标题详情失败:', error);
    message.error('获取标题详情失败');
  }
};

// 添加子标题
const handleAddChildTitle = (parentId) => {
  if (!filterForm.categoryId) {
    message.warning('请先选择分类');
    return;
  }

  isEditingTitle.value = false;
  titleModalTitle.value = '新增子标题';
  titleForm.value = {
    id: null,
    categoryId: filterForm.categoryId,
    parentId: parentId,
    titleName: '',
    rankValue: 1
  };
  titleModalVisible.value = true;
};

// 删除标题
const handleDeleteTitle = async (id) => {
  try {
    await deleteTitle(id);
    message.success('删除标题成功');
    handleLoadTitleTree();
  } catch (error) {
    console.error('删除标题失败:', error);
    message.error('删除标题失败');
  }
};

// 保存标题
const handleSaveTitle = async () => {
  try {
    await titleFormRef.value.validate();

    titleSaving.value = true;

    if (isEditingTitle.value) {
      await updateTitle(titleForm.value);
      message.success('更新标题成功');
    } else {
      await createTitle(titleForm.value);
      message.success('创建标题成功');
    }

    titleModalVisible.value = false;
    handleLoadTitleTree();
  } catch (error) {
    if (error.errorFields) {
      // 表单验证错误
      return;
    }
    console.error('保存标题失败:', error);
    message.error('保存标题失败');
  } finally {
    titleSaving.value = false;
  }
};

// 标题对话框取消
const handleTitleModalCancel = () => {
  titleModalVisible.value = false;
  if (titleFormRef.value) {
    titleFormRef.value.resetFields();
  }
};

// 添加内容
const handleAddContent = () => {
  if (!filterForm.productId) {
    message.warning('请先选择产品');
    return;
  }

  isEditingContent.value = false;
  contentModalTitle.value = '添加内容';
  contentForm.value = {
    id: null,
    productId: filterForm.productId,
    titleId: null,
    contentValue: ''
  };
  contentModalVisible.value = true;
};

// 编辑内容
const handleEditContent = (content) => {
  isEditingContent.value = true;
  contentModalTitle.value = '编辑内容';
  contentForm.value = {
    id: content.id,
    productId: content.productId,
    titleId: content.titleId,
    contentValue: content.contentValue
  };
  contentModalVisible.value = true;
};

// 删除内容
const handleDeleteContent = async (id) => {
  try {
    await deleteContent(id);
    message.success('删除内容成功');
    loadProductContents();
  } catch (error) {
    console.error('删除内容失败:', error);
    message.error('删除内容失败');
  }
};

// 根据标题信息编辑内容
const handleEditContentByTitle = (titleInfo) => {
  // 检查是否有内容
  const hasContent = titleInfo.contents && titleInfo.contents.length > 0;

  if (hasContent) {
    const content = titleInfo.contents[0]; // 取第一个内容
    isEditingContent.value = true;
    contentModalTitle.value = '编辑内容';
    contentForm.value = {
      id: content.id,
      productId: content.productId,
      titleId: content.titleId,
      contentValue: content.contentValue
    };
  } else {
    // 如果没有找到内容，创建新内容
    isEditingContent.value = false;
    contentModalTitle.value = '添加内容';
    contentForm.value = {
      id: null,
      productId: filterForm.productId,
      titleId: titleInfo.titleId,
      contentValue: ''
    };
  }
  contentModalVisible.value = true;
};

// 根据标题信息删除内容
const handleDeleteContentByTitle = async (titleInfo) => {
  // 检查是否有内容
  const hasContent = titleInfo.contents && titleInfo.contents.length > 0;

  if (hasContent) {
    const content = titleInfo.contents[0]; // 取第一个内容
    try {
      await deleteContent(content.id);
      message.success('删除内容成功');
      loadProductContentTree(true); // 保持滚动位置重新加载树形结构
    } catch (error) {
      console.error('删除内容失败:', error);
      message.error('删除内容失败');
    }
  } else {
    message.warning('未找到要删除的内容');
  }
};

// 保存内容
const handleSaveContent = async () => {
  try {
    await contentFormRef.value.validate();

    contentSaving.value = true;

    await saveContent(contentForm.value);
    message.success(isEditingContent.value ? '更新内容成功' : '添加内容成功');

    contentModalVisible.value = false;
    // loadProductContents();
    // loadProductContentTree(true); // 保持滚动位置更新树形结构
  } catch (error) {
    if (error.errorFields) {
      // 表单验证错误
      return;
    }
    console.error('保存内容失败:', error);
    // 显示具体的错误信息
    const errorMessage = error.response?.data?.message || error.message || '保存内容失败';
    message.error(errorMessage);
  } finally {
    contentSaving.value = false;
  }
};

// 内容对话框取消
const handleContentModalCancel = () => {
  contentModalVisible.value = false;
  if (contentFormRef.value) {
    contentFormRef.value.resetFields();
  }
};

// 查看产品详情
const handleLoadProductDetail = async () => {
  if (!filterForm.categoryId || !filterForm.productId) {
    message.warning('请先选择分类和产品');
    return;
  }

  productDetailLoading.value = true;
  detailModalVisible.value = true;

  try {
    const res = await getProductDetailTree(filterForm.categoryId, filterForm.productId);
    productDetailTree.value = res.data || [];
  } catch (error) {
    console.error('加载产品详情失败:', error);
    message.error('加载产品详情失败');
  } finally {
    productDetailLoading.value = false;
  }
};

// Logo加载错误处理
const handleLogoError = (event) => {
  event.target.style.display = 'none';
  const placeholder = event.target.parentElement.querySelector('.logo-placeholder');
  if (placeholder) {
    placeholder.style.display = 'flex';
  }
};

// 初始化
onMounted(async () => {
  await loadCategories();
  await loadProducts(); // 初始加载所有产品

  // 如果已经选择了分类，自动加载标题结构
  if (filterForm.categoryId) {
    handleLoadTitleTree();
  }
});
</script>

<style scoped>
.product-detail-manage {
  padding: 24px;
}

.icon-align-button {
  display: flex;
  align-items: center;
}

.h-full {
  height: 600px;
}



/* 工具类样式 */
.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.gap-4 {
  gap: 1rem;
}

.text-2xl {
  font-size: 1.5rem;
}

.font-semibold {
  font-weight: 600;
}

.text-gray-800 {
  color: #2d3748;
}

.text-gray-500 {
  color: #718096;
}

.text-xs {
  font-size: 0.75rem;
}

.text-gray-400 {
  color: #9ca3af;
}

/* 信息展示区域样式 */
.category-info,
.product-info {
  padding: 16px;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  margin-top: 16px;
  flex: 1;
}

.category-placeholder,
.product-placeholder {
  padding: 24px;
  text-align: center;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px dashed #d1d5db;
  margin-top: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-weight: 500;
  color: #4a5568;
  min-width: 80px;
  flex-shrink: 0;
}

.info-value {
  color: #2d3748;
  font-weight: 600;
}

.text-center {
  text-align: center;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.text-blue-500 {
  color: #3182ce;
}

/* 内容列表样式 */
.content-list {
  max-height: 500px;
  overflow-y: auto;
}

/* 树形内容样式 */
.content-tree {
  max-height: 500px;
  overflow-y: auto;
}

.content-section {
  margin-bottom: 20px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.section-header {
  background: #f7fafc;
  padding: 12px 16px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
}

.section-children {
  padding: 8px;
}

.no-children {
  padding: 16px;
  text-align: center;
}

.content-item {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
  background: #f7fafc;
}

.content-item:last-child {
  margin-bottom: 0;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.content-title {
  font-weight: 500;
  color: #2d3748;
}

.content-value {
  color: #4a5568;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-word;
}

/* 产品详情树样式 */
.product-detail-tree {
  max-height: 600px;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.section-title {
  background: #f7fafc;
  padding: 12px 16px;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  border-bottom: 1px solid #e2e8f0;
}

.attributes-grid {
  padding: 16px;
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.attribute-item {
  display: flex;
  align-items: flex-start;
  padding: 8px 0;
  border-bottom: 1px solid #f1f5f9;
}

.attribute-item:last-child {
  border-bottom: none;
}

.attribute-name {
  font-weight: 500;
  color: #4a5568;
  min-width: 120px;
  flex-shrink: 0;
}

.attribute-value {
  color: #2d3748;
  flex: 1;
  word-break: break-word;
}

.no-attributes {
  padding: 24px;
  text-align: center;
}

/* 树形组件样式调整 */
:deep(.ant-tree .ant-tree-node-content-wrapper) {
  width: 100%;
}

:deep(.ant-tree .ant-tree-title) {
  width: 100%;
}

/* 表单样式调整 */
:deep(.ant-form-item) {
  margin-bottom: 16px;
}

/* 卡片样式调整 */
:deep(.ant-card-body) {
  padding: 16px;
}

:deep(.ant-card-head) {
  padding: 0 16px;
}

/* 产品选项样式 */
.product-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
}

.product-logo {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 4px;
}

.logo-placeholder {
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #bfbfbf;
  font-size: 12px;
}

.product-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 滚动条样式 */
.content-list::-webkit-scrollbar,
.content-tree::-webkit-scrollbar,
.product-detail-tree::-webkit-scrollbar {
  width: 6px;
}

.content-list::-webkit-scrollbar-track,
.content-tree::-webkit-scrollbar-track,
.product-detail-tree::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.content-list::-webkit-scrollbar-thumb,
.content-tree::-webkit-scrollbar-thumb,
.product-detail-tree::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.content-list::-webkit-scrollbar-thumb:hover,
.content-tree::-webkit-scrollbar-thumb:hover,
.product-detail-tree::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
