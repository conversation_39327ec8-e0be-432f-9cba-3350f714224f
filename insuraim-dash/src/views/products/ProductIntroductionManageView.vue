<template>
  <div class="product-introduction-manage">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>产品介绍管理</h2>
      <p>管理产品介绍页面的内容，包括产品摘要、特点、概况等信息</p>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-section">
      <a-card>
        <a-form layout="inline" :model="searchForm">
          <a-form-item label="产品名称">
            <a-input v-model:value="searchForm.productName" placeholder="请输入产品名称" allow-clear />
          </a-form-item>
          <a-form-item label="介绍页状态">
            <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear style="width: 140px">
              <a-select-option :value="1">已创建</a-select-option>
              <a-select-option :value="0">未创建</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="关键词">
            <a-input v-model:value="searchForm.keyword" placeholder="搜索产品摘要" allow-clear />
          </a-form-item>
          <a-form-item>
            <a-space>
              <a-button type="primary" @click="handleSearch">
                <SearchOutlined /> 搜索
              </a-button>
              <a-button @click="handleReset">
                <ReloadOutlined /> 重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-section">
      <a-card>
        <a-space>
          <a-button @click="handleRefresh">
            <ReloadOutlined /> 刷新
          </a-button>
        </a-space>
      </a-card>
    </div>

    <!-- 产品介绍列表 -->
    <div class="table-section">
      <a-card>
        <a-table :columns="columns" :data-source="introductions" :loading="loading" :pagination="pagination"
          row-key="id" @change="handleTableChange">
          <template #bodyCell="{ column, record }">
            <!-- 产品信息列 -->
            <template v-if="column.key === 'productInfo'">
              <div class="product-info">
                <div class="product-name">{{ record.productName }}</div>
                <div class="product-id">ID: {{ record.id }}</div>
              </div>
            </template>

            <!-- 公司信息列 -->
            <template v-else-if="column.key === 'companyInfo'">
              <div class="company-info">
                <div class="company-name">{{ record.companyName }}</div>
                <div class="company-code">{{ record.companyCode }}</div>
              </div>
            </template>

            <!-- 介绍页状态列 -->
            <template v-else-if="column.key === 'introductionStatus'">
              <div class="status-indicator">
                <a-tag v-if="record.hasIntroduction" color="green">已创建</a-tag>
                <a-tag v-else color="orange">未创建</a-tag>
              </div>
            </template>

            <!-- 产品摘要列 -->
            <template v-else-if="column.key === 'productSummary'">
              <div class="summary-content">
                {{ record.productSummary || '暂无摘要' }}
              </div>
            </template>

            <!-- Banner图列 -->
            <template v-else-if="column.key === 'bannerImageUrl'">
              <div class="banner-preview">
                <img v-if="record.bannerImageUrl" :src="record.bannerImageUrl" alt="Banner" class="banner-image"
                  @error="handleImageError" />
                <div v-else class="no-image">暂无图片</div>
              </div>
            </template>

            <!-- 产品特点列 -->
            <template v-else-if="column.key === 'productFeatures'">
              <div class="features-list">
                <a-tag v-for="(feature, index) in (record.productFeatures || []).slice(0, 3)" :key="index" color="blue"
                  class="feature-tag">
                  {{ feature.name }}
                </a-tag>
                <span v-if="(record.productFeatures || []).length > 3" class="more-features">
                  +{{ (record.productFeatures || []).length - 3 }}
                </span>
              </div>
            </template>

            <!-- 产品概况列 -->
            <template v-else-if="column.key === 'productOverview'">
              <div class="overview-content">
                <div v-if="record.productOverview" v-html="record.productOverview" class="overview-html"></div>
                <span v-else class="no-overview">暂无概况</span>
              </div>
            </template>

            <!-- 创建时间列 -->
            <template v-else-if="column.key === 'createdAt'">
              <span>{{ record.createdAt ? new Date(record.createdAt).toLocaleString() : '-' }}</span>
            </template>

            <!-- 操作列 -->
            <template v-else-if="column.key === 'action'">
              <div class="action-buttons">
                <template v-if="record.hasIntroduction">
                  <a-button type="link" size="small" @click="handleViewIntroduction(record)">
                    <EyeOutlined /> 查看
                  </a-button>
                  <a-button type="link" size="small" @click="handleEditIntroduction(record)">
                    <EditOutlined /> 编辑
                  </a-button>
                  <a-switch :checked="record.introductionStatus === 1"
                    @change="(checked) => handleStatusChange(record, checked)" :loading="record.statusLoading"
                    size="small" />
                  <a-popconfirm title="确定要删除这个产品介绍吗？" @confirm="handleDeleteIntroduction(record.introductionId)">
                    <a-button type="link" size="small" danger>
                      <DeleteOutlined /> 删除
                    </a-button>
                  </a-popconfirm>
                </template>
                <template v-else>
                  <a-button type="primary" size="small" @click="handleCreateIntroduction(record)">
                    <PlusOutlined /> 创建介绍页
                  </a-button>
                </template>
              </div>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 产品介绍编辑弹窗 -->
    <ProductIntroductionModal v-model:visible="introductionModalVisible" :introduction="currentIntroduction"
      @success="handleIntroductionSuccess" />

    <!-- 产品介绍查看弹窗 -->
    <ProductIntroductionViewModal v-model:visible="viewModalVisible" :introduction="currentIntroduction" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue';

import {
  getProductIntroductionByProductId,
  deleteProductIntroduction,
  updateProductIntroduction
} from '@/api/productIntroduction';
import { getProductListBasic } from '@/api/productManage';

import ProductIntroductionModal from './components/ProductIntroductionModal.vue';
import ProductIntroductionViewModal from './components/ProductIntroductionViewModal.vue';

// 响应式数据
const loading = ref(false);
const introductions = ref([]);
const allProducts = ref([]); // 所有产品列表
const filteredProducts = ref([]); // 过滤后的产品列表

const introductionModalVisible = ref(false);
const viewModalVisible = ref(false);
const currentIntroduction = ref(null);

// 搜索表单
const searchForm = reactive({
  productName: '',
  status: null,
  keyword: ''
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: (total) => `共 ${total} 条记录`
});

// 表格列配置
const columns = [
  {
    title: '产品信息',
    key: 'productInfo',
    width: 200
  },
  {
    title: '公司信息',
    key: 'companyInfo',
    width: 150
  },
  {
    title: '产品类型',
    dataIndex: 'productType',
    width: 120
  },
  {
    title: '介绍页状态',
    key: 'introductionStatus',
    width: 120,
    align: 'center'
  },
  {
    title: '产品摘要',
    key: 'productSummary',
    width: 300,
    ellipsis: true
  },
  {
    title: 'Banner图',
    key: 'bannerImageUrl',
    width: 120,
    align: 'center'
  },
  {
    title: '产品特点',
    key: 'productFeatures',
    width: 200
  },
  {
    title: '创建时间',
    key: 'createdAt',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 250,
    fixed: 'right'
  }
];

// 页面初始化
onMounted(() => {
  loadProductsWithIntroductions();
});

// 加载产品基础列表
const loadProducts = async () => {
  try {
    const productsRes = await getProductListBasic();
    allProducts.value = productsRes.data || [];

    // 应用搜索过滤
    applyFilters();
  } catch (error) {
    console.error('加载产品列表失败:', error);
    message.error('加载产品列表失败');
    throw error;
  }
};

// 为指定的产品列表按需加载介绍信息
const loadIntroductionForProducts = async (products) => {
  if (!products || products.length === 0) {
    return [];
  }

  try {
    // 并发获取所有产品的介绍信息
    const introductionPromises = products.map(async (product) => {
      try {
        const introRes = await getProductIntroductionByProductId(product.id);
        const introduction = introRes.data;

        return {
          ...product,
          // 产品介绍相关字段
          introductionId: introduction?.id || null,
          productSummary: introduction?.productSummary || '',
          bannerImageUrl: introduction?.bannerImageUrl || '',
          productFeatures: introduction?.productFeatures || [],
          productOverview: introduction?.productOverview || '',
          introductionStatus: introduction?.status || null,
          createdAt: introduction?.createdAt || null,
          hasIntroduction: !!introduction
        };
      } catch (error) {
        // 如果某个产品的介绍获取失败（比如404），返回没有介绍的产品信息
        console.warn(`获取产品 ${product.id} 的介绍信息失败:`, error);
        return {
          ...product,
          introductionId: null,
          productSummary: '',
          bannerImageUrl: '',
          productFeatures: [],
          productOverview: '',
          introductionStatus: null,
          createdAt: null,
          hasIntroduction: false
        };
      }
    });

    return await Promise.all(introductionPromises);
  } catch (error) {
    console.error('批量加载产品介绍失败:', error);
    throw error;
  }
};

// 主加载函数
const loadProductsWithIntroductions = async () => {
  loading.value = true;
  try {
    // 1. 加载产品基础列表
    await loadProducts();

    // 2. 加载当前页的介绍信息
    await loadCurrentPageIntroductions();
  } catch (error) {
    console.error('加载数据失败:', error);
    message.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

// 应用搜索过滤（只基于产品基础信息进行过滤）
const applyFilters = () => {
  filteredProducts.value = allProducts.value.filter(product => {
    // 产品名称过滤
    if (searchForm.productName && !product.productName.includes(searchForm.productName)) {
      return false;
    }
    return true;
  });

  // 更新分页信息
  pagination.total = filteredProducts.value.length;
  pagination.current = 1; // 重置到第一页
};

// 加载当前页的介绍信息
const loadCurrentPageIntroductions = async () => {
  try {
    // 计算当前页的产品
    const startIndex = (pagination.current - 1) * pagination.pageSize;
    const endIndex = startIndex + pagination.pageSize;
    const currentPageProducts = filteredProducts.value.slice(startIndex, endIndex);

    // 为当前页产品加载介绍信息
    const productsWithIntroductions = await loadIntroductionForProducts(currentPageProducts);

    // 应用介绍页相关的过滤
    introductions.value = productsWithIntroductions.filter(product => {
      // 状态过滤 - 这里过滤介绍页状态
      if (searchForm.status !== null) {
        if (searchForm.status === 1 && !product.hasIntroduction) {
          return false;
        }
        if (searchForm.status === 0 && product.hasIntroduction) {
          return false;
        }
      }
      // 关键词过滤
      if (searchForm.keyword && !product.productSummary.includes(searchForm.keyword)) {
        return false;
      }
      return true;
    });
  } catch (error) {
    console.error('加载当前页介绍信息失败:', error);
    message.error('加载介绍信息失败');
  }
};

// 更新分页信息并加载当前页数据
const updatePagination = async () => {
  await loadCurrentPageIntroductions();
};

// 刷新数据
const handleRefresh = async () => {
  await loadProductsWithIntroductions();
};

// 搜索
const handleSearch = async () => {
  applyFilters();
  await updatePagination();
};

// 重置搜索
const handleReset = async () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = key === 'status' ? null : '';
  });
  applyFilters();
  await updatePagination();
};

// 表格变化处理
const handleTableChange = async (pag) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  await updatePagination();
};

// 创建产品介绍
const handleCreateIntroduction = (product = null) => {
  if (product) {
    // 为特定产品创建介绍页，传递产品信息用于自动填充
    currentIntroduction.value = {
      productId: product.id,
      productName: product.productName,
      productSummary: product.description || '', // 使用产品描述自动填充产品摘要
      companyName: product.companyName,
      productType: product.productType
    };
  } else {
    // 通用创建
    currentIntroduction.value = null;
  }
  introductionModalVisible.value = true;
};

// 查看产品介绍
const handleViewIntroduction = (record) => {
  // 确保传递正确的介绍页ID而不是产品ID
  currentIntroduction.value = {
    ...record,
    id: record.introductionId, // 使用介绍页ID而不是产品ID
    productId: record.id // 明确设置产品ID
  };
  viewModalVisible.value = true;
};

// 编辑产品介绍
const handleEditIntroduction = (record) => {
  // 确保传递正确的介绍页ID而不是产品ID
  currentIntroduction.value = {
    ...record,
    id: record.introductionId, // 使用介绍页ID而不是产品ID
    productId: record.id // 明确设置产品ID
  };
  introductionModalVisible.value = true;
};

// 产品介绍操作成功回调
const handleIntroductionSuccess = () => {
  introductionModalVisible.value = false;
  loadProductsWithIntroductions();
};

// 删除产品介绍
const handleDeleteIntroduction = async (id) => {
  try {
    await deleteProductIntroduction(id);
    message.success('删除成功');
    loadProductsWithIntroductions();
  } catch (error) {
    console.error('删除产品介绍失败:', error);
    message.error('删除失败');
  }
};

// 状态切换
const handleStatusChange = async (record, checked) => {
  if (!record.introductionId) {
    message.error('该产品还没有介绍页');
    return;
  }

  record.statusLoading = true;
  try {
    await updateProductIntroduction(record.introductionId, {
      status: checked ? 1 : 0
    });
    record.introductionStatus = checked ? 1 : 0;
    message.success('状态更新成功');
  } catch (error) {
    console.error('状态更新失败:', error);
    message.error('状态更新失败');
  } finally {
    record.statusLoading = false;
  }
};

// 图片加载错误处理
const handleImageError = (event) => {
  event.target.style.display = 'none';
  event.target.nextElementSibling.style.display = 'block';
};
</script>

<style scoped>
.product-introduction-manage {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.page-header p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.search-section,
.action-section,
.table-section {
  margin-bottom: 16px;
}

.product-info {
  display: flex;
  flex-direction: column;
}

.product-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.product-id {
  font-size: 12px;
  color: #8c8c8c;
}

.company-info {
  display: flex;
  flex-direction: column;
}

.company-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.company-code {
  font-size: 12px;
  color: #8c8c8c;
}

.status-indicator {
  display: flex;
  justify-content: center;
}

.summary-content {
  max-width: 280px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #595959;
}

.banner-preview {
  display: flex;
  justify-content: center;
  align-items: center;
}

.banner-image {
  width: 60px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
}

.no-image {
  width: 60px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 12px;
  color: #8c8c8c;
}

.features-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.feature-tag {
  margin: 0;
  font-size: 12px;
}

.more-features {
  font-size: 12px;
  color: #8c8c8c;
  margin-left: 4px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-buttons .ant-btn-link {
  padding: 0;
  height: auto;
}
</style>
