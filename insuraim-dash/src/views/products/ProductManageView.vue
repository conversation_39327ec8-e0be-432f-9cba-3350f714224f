<template>
  <div class="product-manage-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>产品管理</h2>
      <p>管理保险产品及其子产品信息</p>
    </div>

    <!-- 搜索和操作区域 -->
    <div class="search-section">
      <a-card>
        <a-form layout="inline" :model="searchForm" @submit="handleSearch">
          <a-form-item label="产品名称">
            <a-input v-model:value="searchForm.productName" placeholder="请输入产品名称" style="width: 200px" />
          </a-form-item>
          <a-form-item label="保险公司">
            <a-input v-model:value="searchForm.companyName" placeholder="请输入保险公司名称" style="width: 200px" />
          </a-form-item>
          <a-form-item label="分类">
            <a-select v-model:value="searchForm.categoryId" placeholder="请选择分类" style="width: 150px" allow-clear>
              <a-select-option v-for="category in categories" :key="category.id" :value="category.id">
                {{ category.name }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="产品类型">
            <a-input v-model:value="searchForm.productType" placeholder="请输入产品类型" style="width: 150px" />
          </a-form-item>
          <a-form-item label="保障年限">
            <a-input v-model:value="searchForm.guaranteePeriod" placeholder="请输入保障年限" style="width: 150px" />
          </a-form-item>
          <a-form-item label="状态">
            <a-select v-model:value="searchForm.status" placeholder="请选择状态" style="width: 120px" allow-clear>
              <a-select-option :value="1">启用</a-select-option>
              <a-select-option :value="0">禁用</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" html-type="submit">
              <SearchOutlined /> 搜索
            </a-button>
            <a-button @click="handleReset" style="margin-left: 8px">
              <ReloadOutlined /> 重置
            </a-button>
          </a-form-item>
        </a-form>

        <div class="action-buttons">
          <a-button type="primary" @click="handleCreateProduct">
            <PlusOutlined /> 新增产品
          </a-button>
        </div>
      </a-card>
    </div>

    <!-- 产品列表 -->
    <div class="table-section">
      <a-card>
        <a-table :columns="columns" :data-source="products" :loading="loading" :pagination="pagination"
          :expandable="expandableConfig" row-key="id" @change="handleTableChange">
          <template #bodyCell="{ column, record }">
            <!-- 产品名称列 -->
            <template v-if="column.key === 'productName'">
              <div class="product-info">
                <img v-if="record.logoUrl" :src="record.logoUrl" alt="logo" class="product-logo" />
                <div>
                  <div class="product-name">{{ record.productName }}</div>
                  <div class="product-company">{{ record.companyName }}</div>
                </div>
              </div>
            </template>

            <template v-else-if="column.key === 'productType'">
              <a-tag color="blue">{{ getProductTypeLabel(record.productType) }}</a-tag>
            </template>

            <!-- 分类列 -->
            <template v-else-if="column.key === 'categoryName'">
              <a-tag color="blue">{{ record.categoryName }}</a-tag>
            </template>

            <!-- 状态列 -->
            <template v-else-if="column.key === 'status'">
              <a-switch :checked="record.status === 1" @change="(checked) => handleStatusChange(record, checked)"
                :loading="record.statusLoading" />
            </template>

            <!-- 支持计划书列 -->
            <template v-else-if="column.key === 'isProposal'">
              <a-switch :checked="record.isProposal === 1" @change="(checked) => handleProposalChange(record, checked)"
                :loading="record.proposalLoading" />
            </template>

            <!-- 允许预缴列 -->
            <template v-else-if="column.key === 'isPrepayment'">
              <a-switch :checked="record.isPrepayment === 1"
                @change="(checked) => handlePrepaymentChange(record, checked)" :loading="record.prepaymentLoading" />
            </template>

            <!-- 操作列 -->
            <template v-else-if="column.key === 'action'">
              <div class="action-buttons-inline">
                <a-button type="link" size="small" @click="handleEditProduct(record)">
                  <EditOutlined /> 编辑
                </a-button>
                <a-popconfirm title="确定要删除这个产品吗？" @confirm="handleDeleteProduct(record.id)">
                  <a-button type="link" size="small" danger>
                    <DeleteOutlined /> 删除
                  </a-button>
                </a-popconfirm>
              </div>
            </template>
          </template>

          <!-- 展开的子产品列表 -->
          <template #expandedRowRender="{ record }">
            <SubProductTable :parent-product="record" @refresh="loadProducts" />
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 产品编辑弹窗 -->
    <ProductModal v-model:visible="productModalVisible" :product="currentProduct" :categories="categories"
      @success="handleProductSuccess" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue';

import {
  getProductPageWithDetails,
  deleteProduct,
  updateProduct
} from '@/api/productManage';
import { getCategoryList } from '@/api/category';
import ProductModal from './components/ProductModal.vue';
import SubProductTable from './components/SubProductTable.vue';

// 产品类型映射
const productTypeMap = {
  'ADDITIONAL_PROTECTION': '附加计划',
  'SPECIAL': '特级计划',
  'BASIC': '基础计划'
};

// 响应式数据
const loading = ref(false);
const products = ref([]);
const categories = ref([]);

const productModalVisible = ref(false);
const currentProduct = ref(null);

// 搜索表单
const searchForm = reactive({
  productName: '',
  companyName: '',
  categoryId: null,
  productType: '',
  guaranteePeriod: '',
  status: null
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: (total) => `共 ${total} 条记录`
});

// 表格列配置
const columns = [
  {
    title: '产品名称',
    dataIndex: 'productName',
    key: 'productName',
    width: 300
  },
  {
    title: '分类',
    dataIndex: 'categoryName',
    key: 'categoryName',
    width: 120
  },
  {
    title: '产品类型',
    dataIndex: 'productType',
    key: 'productType',
    width: 120
  },
  {
    title: '保障年限',
    dataIndex: 'guaranteePeriod',
    key: 'guaranteePeriod',
    width: 120
  },
  {
    title: '地区',
    dataIndex: 'region',
    key: 'region',
    width: 120
  },
  {
    title: '子产品数量',
    key: 'subProductCount',
    width: 120,
    customRender: ({ record }) => record.subProducts?.length || 0
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '支持计划书',
    dataIndex: 'isProposal',
    key: 'isProposal',
    width: 120
  },
  {
    title: '允许预缴',
    dataIndex: 'isPrepayment',
    key: 'isPrepayment',
    width: 120
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
    width: 180,
    customRender: ({ text }) => text ? new Date(text).toLocaleString() : '-'
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
];



// 展开配置
const expandableConfig = {
  slots: { expandedRowRender: 'expandedRowRender' },
  rowExpandable: (record) => record.subProducts && record.subProducts.length > 0
};

// 加载分类列表
const loadCategories = async () => {
  try {
    const res = await getCategoryList();
    categories.value = res.data || [];
  } catch (error) {
    console.error('加载分类列表失败:', error);
    message.error('加载分类列表失败');
  }
};

// 加载产品列表
const loadProducts = async () => {
  loading.value = true;
  try {
    const params = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      ...searchForm
    };

    const res = await getProductPageWithDetails(params);
    products.value = res.data?.records || [];
    pagination.total = res.data?.total || 0;
  } catch (error) {
    console.error('加载产品列表失败:', error);
    message.error('加载产品列表失败');
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  loadProducts();
};

// 重置搜索
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = key === 'status' || key === 'categoryId' ? null : '';
  });
  pagination.current = 1;
  loadProducts();
};

// 表格变化处理
const handleTableChange = (pag) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  loadProducts();
};

// 创建产品
const handleCreateProduct = () => {
  currentProduct.value = null;
  productModalVisible.value = true;
};

// 编辑产品
const handleEditProduct = (product) => {
  currentProduct.value = { ...product };
  productModalVisible.value = true;
};

// 产品操作成功回调
const handleProductSuccess = () => {
  productModalVisible.value = false;
  loadProducts();
};

// 状态切换
const handleStatusChange = async (product, checked) => {
  product.statusLoading = true;
  try {
    await updateProduct(product.id, {
      ...product,
      status: checked ? 1 : 0
    });
    product.status = checked ? 1 : 0;
    message.success('状态更新成功');
  } catch (error) {
    console.error('状态更新失败:', error);
    message.error('状态更新失败');
  } finally {
    product.statusLoading = false;
  }
};

// 支持计划书切换
const handleProposalChange = async (product, checked) => {
  product.proposalLoading = true;
  try {
    await updateProduct(product.id, {
      ...product,
      isProposal: checked ? 1 : 0
    });
    product.isProposal = checked ? 1 : 0;
    message.success('计划书支持状态更新成功');
  } catch (error) {
    console.error('计划书支持状态更新失败:', error);
    message.error('计划书支持状态更新失败');
  } finally {
    product.proposalLoading = false;
  }
};

// 允许预缴切换
const handlePrepaymentChange = async (product, checked) => {
  product.prepaymentLoading = true;
  try {
    await updateProduct(product.id, {
      ...product,
      isPrepayment: checked ? 1 : 0
    });
    product.isPrepayment = checked ? 1 : 0;
    message.success('预缴允许状态更新成功');
  } catch (error) {
    console.error('预缴允许状态更新失败:', error);
    message.error('预缴允许状态更新失败');
  } finally {
    product.prepaymentLoading = false;
  }
};

// 删除产品
const handleDeleteProduct = async (id) => {
  try {
    await deleteProduct(id);
    message.success('删除成功');
    loadProducts();
  } catch (error) {
    console.error('删除失败:', error);
    message.error('删除失败: ' + (error.response?.data?.message || error.message));
  }
};

// 获取产品类型中文标签
const getProductTypeLabel = (productType) => {
  return productTypeMap[productType] || productType || '-';
};



// 初始化
onMounted(async () => {
  await loadCategories();
  await loadProducts();
});
</script>

<style scoped>
.product-manage-container {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
}

.search-section {
  margin-bottom: 16px;
}

.action-buttons {
  margin-top: 16px;
  display: flex;
  gap: 8px;
}

.table-section {
  background: #fff;
}

.product-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.product-logo {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  object-fit: cover;
}

.product-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.product-company {
  font-size: 12px;
}
</style>
