<template>
  <a-modal :visible="visible" :title="isEdit ? '编辑产品介绍' : '新建产品介绍'" width="1500px" :confirm-loading="confirmLoading"
    @ok="handleSubmit" @cancel="handleCancel">
    <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
      <!-- 产品信息显示 -->
      <a-form-item label="产品信息" name="productId">
        <div v-if="formData.productId" class="product-display">
          <div class="product-main-info">
            <span class="product-name">{{ formData.productName }}</span>
            <span class="product-id">ID: {{ formData.productId }}</span>
          </div>
          <div v-if="currentIntroduction?.companyName || currentIntroduction?.productType" class="product-extra-info">
            <span v-if="currentIntroduction?.companyName" class="company-info">{{ currentIntroduction.companyName
              }}</span>
            <span v-if="currentIntroduction?.productType" class="product-type">{{ currentIntroduction.productType
              }}</span>
          </div>
        </div>
        <a-alert v-else message="请从产品列表中选择要创建介绍页的产品" type="warning" show-icon />
      </a-form-item>

      <!-- 产品摘要 -->
      <a-form-item label="产品摘要" name="productSummary">
        <a-textarea v-model:value="formData.productSummary" placeholder="请输入产品摘要/简介" :rows="3" :maxlength="500"
          show-count />
        <div v-if="!isEdit && formData.productSummary && currentIntroduction?.productSummary" class="auto-fill-tip">
          <a-tag color="green" size="small">
            <template #icon>
              <InfoCircleOutlined />
            </template>
            已自动填充产品描述，您可以根据需要进行修改
          </a-tag>
        </div>
      </a-form-item>

      <!-- Banner图URL -->
      <a-form-item label="Banner图">
        <a-input v-model:value="formData.bannerImageUrl" placeholder="请输入Banner图URL" addon-before="URL" />
        <div v-if="formData.bannerImageUrl" class="image-preview">
          <img :src="formData.bannerImageUrl" alt="Banner预览" class="preview-image" @error="handleImageError" />
        </div>
      </a-form-item>

      <!-- 产品特点 -->
      <a-form-item label="产品特点">
        <div class="features-section">
          <div v-for="(feature, index) in formData.productFeatures" :key="index" class="feature-item">
            <a-row :gutter="8">
              <a-col :span="8">
                <a-input v-model:value="feature.name" placeholder="特点名称" :maxlength="50" />
              </a-col>
              <a-col :span="10">
                <a-textarea v-model:value="feature.value" placeholder="特点描述" :rows="2"
                  :auto-size="{ minRows: 2, maxRows: 4 }" />
              </a-col>
              <a-col :span="4">
                <a-input-number v-model:value="feature.sortOrder" placeholder="排序" :min="1" :max="999"
                  style="width: 100%" />
              </a-col>
              <a-col :span="2">
                <a-button type="text" danger @click="removeFeature(index)"
                  :disabled="formData.productFeatures.length <= 1">
                  <DeleteOutlined />
                </a-button>
              </a-col>
            </a-row>
            <a-row :gutter="8" style="margin-top: 8px">
              <a-col :span="22">
                <a-input v-model:value="feature.iconUrl" placeholder="图标URL（可选）" addon-before="图标" />
              </a-col>
            </a-row>
          </div>
          <a-button type="dashed" block @click="addFeature" style="margin-top: 8px">
            <PlusOutlined /> 添加特点
          </a-button>
        </div>
      </a-form-item>

      <!-- 产品概况 -->
      <a-form-item label="产品概况">
        <div class="overview-section">
          <a-tabs v-model:activeKey="overviewTab" class="overview-tabs">
            <a-tab-pane key="edit" tab="编辑">
              <div class="edit-container">
                <div class="edit-toolbar">
                  <a-space>
                    <a-tooltip title="支持HTML标签，如 &lt;h1&gt;、&lt;p&gt;、&lt;ul&gt;、&lt;strong&gt; 等">
                      <a-tag color="blue" class="html-tip">
                        <template #icon>
                          <InfoCircleOutlined />
                        </template>
                        HTML格式支持
                      </a-tag>
                    </a-tooltip>
                    <a-button type="text" size="small" @click="insertHtmlTemplate('heading')">
                      <template #icon>
                        <FontSizeOutlined />
                      </template>
                      标题
                    </a-button>
                    <a-button type="text" size="small" @click="insertHtmlTemplate('list')">
                      <template #icon>
                        <UnorderedListOutlined />
                      </template>
                      列表
                    </a-button>
                    <a-button type="text" size="small" @click="insertHtmlTemplate('bold')">
                      <template #icon>
                        <BoldOutlined />
                      </template>
                      加粗
                    </a-button>
                  </a-space>
                </div>
                <a-textarea v-model:value="formData.productOverview"
                  placeholder="请输入产品概况内容...&#10;&#10;支持HTML格式，例如：&#10;&lt;h2&gt;产品亮点&lt;/h2&gt;&#10;&lt;p&gt;这是一款优秀的保险产品...&lt;/p&gt;&#10;&lt;ul&gt;&#10;  &lt;li&gt;保障全面&lt;/li&gt;&#10;  &lt;li&gt;理赔快速&lt;/li&gt;&#10;&lt;/ul&gt;"
                  :rows="10" class="overview-textarea" />
              </div>
            </a-tab-pane>
            <a-tab-pane key="preview" tab="预览">
              <div class="preview-container">
                <div class="preview-header">
                  <a-space>
                    <a-tag color="green">实时预览</a-tag>
                    <span class="preview-tip">以下是用户看到的最终效果</span>
                  </a-space>
                </div>
                <div class="overview-preview"
                  v-html="formData.productOverview || '<div class=&quot;empty-preview&quot;><p>暂无内容</p><p class=&quot;empty-tip&quot;>请在编辑标签页中输入产品概况内容</p></div>'">
                </div>
              </div>
            </a-tab-pane>
          </a-tabs>
        </div>
      </a-form-item>

      <!-- 启用状态 -->
      <a-form-item label="启用状态">
        <a-switch v-model:checked="formData.status" :checked-value="1" :un-checked-value="0" />
        <span style="margin-left: 8px">
          {{ formData.status === 1 ? '启用' : '禁用' }}
        </span>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import {
  PlusOutlined,
  DeleteOutlined,
  InfoCircleOutlined,
  FontSizeOutlined,
  UnorderedListOutlined,
  BoldOutlined
} from '@ant-design/icons-vue';

import {
  createProductIntroduction,
  updateProductIntroduction
} from '@/api/productIntroduction';

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  introduction: {
    type: Object,
    default: null
  }
});

// Emits
const emit = defineEmits(['update:visible', 'success']);

// 响应式数据
const formRef = ref();
const confirmLoading = ref(false);
const overviewTab = ref('edit');

// 表单数据
const formData = reactive({
  productId: null,
  productName: '',
  productSummary: '',
  bannerImageUrl: '',
  productFeatures: [
    {
      name: '',
      value: '',
      iconUrl: '',
      sortOrder: 1
    }
  ],
  productOverview: '',
  status: 1
});

// 表单验证规则
const rules = {
  productId: [
    { required: true, message: '请选择产品', trigger: 'change' }
  ]
};

// 计算属性
const isEdit = computed(() => !!props.introduction?.id);
const currentIntroduction = computed(() => props.introduction);

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    if (props.introduction) {
      if (props.introduction.id) {
        // 编辑模式，填充已有的介绍页数据
        Object.assign(formData, {
          ...props.introduction,
          productFeatures: props.introduction.productFeatures?.length > 0
            ? props.introduction.productFeatures
            : [{ name: '', value: '', iconUrl: '', sortOrder: 1 }]
        });
      } else {
        // 新建模式，但有产品信息传递，进行自动填充
        resetForm();
        Object.assign(formData, {
          productId: props.introduction.productId,
          productName: props.introduction.productName,
          productSummary: props.introduction.productSummary || '', // 自动填充产品描述
          status: 1
        });
      }
    } else {
      // 新建模式，重置表单
      resetForm();
    }
  }
});

// 页面初始化 - 移除 onMounted 中的产品列表加载，避免与父页面重复请求
// onMounted(() => {
//   loadProductList();
// });

// 产品列表加载函数已移除，现在直接从父组件传递产品信息

// 产品选择相关函数已移除，现在直接从父组件传递产品信息

// 添加产品特点
const addFeature = () => {
  formData.productFeatures.push({
    name: '',
    value: '',
    iconUrl: '',
    sortOrder: formData.productFeatures.length + 1
  });
};

// 删除产品特点
const removeFeature = (index) => {
  if (formData.productFeatures.length > 1) {
    formData.productFeatures.splice(index, 1);
    // 重新排序
    formData.productFeatures.forEach((feature, idx) => {
      if (!feature.sortOrder) {
        feature.sortOrder = idx + 1;
      }
    });
  }
};

// 图片加载错误处理
const handleImageError = (event) => {
  event.target.style.display = 'none';
};

// HTML模板插入功能
const insertHtmlTemplate = (type) => {
  const templates = {
    heading: '<h2>标题</h2>\n<p>内容描述...</p>\n',
    list: '<ul>\n  <li>项目一</li>\n  <li>项目二</li>\n  <li>项目三</li>\n</ul>\n',
    bold: '<p><strong>重要内容</strong></p>\n'
  };

  const template = templates[type];
  if (template) {
    const currentValue = formData.productOverview || '';
    formData.productOverview = currentValue + template;
  }
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    productId: null,
    productName: '',
    productSummary: '',
    bannerImageUrl: '',
    productFeatures: [
      {
        name: '',
        value: '',
        iconUrl: '',
        sortOrder: 1
      }
    ],
    productOverview: '',
    status: 1
  });
  formRef.value?.resetFields();
};

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate();

    confirmLoading.value = true;

    // 过滤空的产品特点
    const filteredFeatures = formData.productFeatures.filter(
      feature => feature.name && feature.value
    );

    const submitData = {
      ...formData,
      productFeatures: filteredFeatures
    };

    if (isEdit.value) {
      await updateProductIntroduction(props.introduction.id, submitData);
      message.success('更新成功');
    } else {
      await createProductIntroduction(submitData);
      message.success('创建成功');
    }

    emit('success');
  } catch (error) {
    if (error.errorFields) {
      // 表单验证错误
      return;
    }
    console.error('保存失败:', error);
    message.error('保存失败');
  } finally {
    confirmLoading.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  emit('update:visible', false);
};
</script>

<style scoped>
.product-display {
  padding: 12px 16px;
  background: #f0f8ff;
  border: 1px solid #d4edda;
  border-radius: 8px;
}

.product-main-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.product-name {
  font-weight: 500;
  color: #1890ff;
  font-size: 16px;
}

.product-id {
  font-size: 12px;
  color: #666;
  background: rgba(24, 144, 255, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
}

.product-extra-info {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 13px;
}

.company-info {
  color: #52c41a;
  background: rgba(82, 196, 26, 0.1);
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.product-type {
  color: #722ed1;
  background: rgba(114, 46, 209, 0.1);
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.auto-fill-tip {
  margin-top: 8px;
}

.image-preview {
  margin-top: 8px;
}

.preview-image {
  max-width: 200px;
  max-height: 120px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
}

.features-section {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  background-color: #fafafa;
}

.feature-item {
  margin-bottom: 12px;
  padding: 12px;
  background-color: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
}

.feature-item:last-child {
  margin-bottom: 0;
}

.overview-section {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  background: #fff;
}

.overview-tabs {
  background: #fff;
}

.overview-tabs :deep(.ant-tabs-nav) {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  margin: 0;
  padding: 0 16px;
}

.overview-tabs :deep(.ant-tabs-tab) {
  margin: 8px 8px 0 0;
  padding: 8px 16px;
  border-radius: 6px 6px 0 0;
  background: rgba(255, 255, 255, 0.7);
  border: 1px solid #e8e8e8;
  border-bottom: none;
  transition: all 0.3s ease;
}

.overview-tabs :deep(.ant-tabs-tab:hover) {
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(-1px);
}

.overview-tabs :deep(.ant-tabs-tab-active) {
  background: #fff !important;
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.overview-tabs :deep(.ant-tabs-content-holder) {
  background: #fff;
}

.edit-container {
  padding: 16px;
}

.edit-toolbar {
  margin-bottom: 12px;
  padding: 12px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
}

.html-tip {
  border: none;
  background: #e6f7ff;
  color: #1890ff;
  font-weight: 500;
}

.edit-toolbar .ant-btn {
  color: #666;
  border-color: #d9d9d9;
  background: #fff;
  transition: all 0.3s ease;
}

.edit-toolbar .ant-btn:hover {
  color: #1890ff;
  border-color: #1890ff;
  background: #f0f8ff;
}

.preview-container {
  padding: 16px;
}

.preview-header {
  margin-bottom: 12px;
  padding: 12px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
}

.preview-tip {
  color: #666;
  font-size: 12px;
  font-weight: 500;
}

.overview-preview {
  min-height: 250px;
  padding: 20px;
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow-y: auto;
  max-height: 400px;
  position: relative;
}

.empty-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;
  text-align: center;
}

.empty-preview p {
  margin: 8px 0;
  font-size: 16px;
}

.empty-tip {
  font-size: 12px !important;
  opacity: 0.7;
}

.overview-preview :deep(h1),
.overview-preview :deep(h2),
.overview-preview :deep(h3) {
  margin-top: 20px;
  margin-bottom: 12px;
  color: #1a1a1a;
  font-weight: 600;
  position: relative;
}

.overview-preview :deep(h1) {
  font-size: 24px;
  color: #1890ff;
  border-bottom: 2px solid #1890ff;
  padding-bottom: 8px;
}

.overview-preview :deep(h2) {
  font-size: 20px;
  color: #722ed1;
  padding-left: 12px;
  border-left: 4px solid #722ed1;
}

.overview-preview :deep(h3) {
  font-size: 18px;
  color: #13c2c2;
}

.overview-preview :deep(ul),
.overview-preview :deep(ol) {
  padding-left: 24px;
  margin: 12px 0;
  background: rgba(24, 144, 255, 0.05);
  border-radius: 6px;
  padding: 12px 24px;
}

.overview-preview :deep(li) {
  margin: 8px 0;
  line-height: 1.6;
  position: relative;
}

.overview-preview :deep(ul li::marker) {
  color: #1890ff;
}

.overview-preview :deep(p) {
  margin: 12px 0;
  line-height: 1.8;
  color: #333;
  text-align: justify;
}

.overview-preview :deep(strong) {
  font-weight: 600;
  color: #d4380d;
  background: rgba(212, 56, 13, 0.1);
  padding: 2px 4px;
  border-radius: 3px;
}

.overview-preview :deep(em) {
  font-style: italic;
  color: #722ed1;
  background: rgba(114, 46, 209, 0.1);
  padding: 2px 4px;
  border-radius: 3px;
}

.overview-preview :deep(code) {
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 3px;
  padding: 2px 6px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #d73a49;
}
</style>
