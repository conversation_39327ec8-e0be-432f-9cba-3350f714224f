<template>
  <a-modal :visible="visible" title="产品介绍详情" width="1500px" :footer="null" @cancel="handleCancel">
    <div v-if="introduction" class="introduction-detail">
      <!-- 基础信息 -->
      <div class="section">
        <h3 class="section-title">基础信息</h3>
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="产品名称">
            {{ introduction.productName }}
          </a-descriptions-item>
          <a-descriptions-item label="产品ID">
            {{ introduction.productId }}
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="introduction.status === 1 ? 'green' : 'red'">
              {{ introduction.status === 1 ? '启用' : '禁用' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ introduction.createdAt ? new Date(introduction.createdAt).toLocaleString() : '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="更新时间">
            {{ introduction.updatedAt ? new Date(introduction.updatedAt).toLocaleString() : '-' }}
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 产品摘要 -->
      <div class="section">
        <h3 class="section-title">产品摘要</h3>
        <div class="summary-content">
          {{ introduction.productSummary || '暂无摘要' }}
        </div>
      </div>

      <!-- Banner图 -->
      <div class="section" v-if="introduction.bannerImageUrl">
        <h3 class="section-title">Banner图</h3>
        <div class="banner-container">
          <img :src="introduction.bannerImageUrl" alt="Banner图" class="banner-image" @error="handleImageError" />
        </div>
      </div>

      <!-- 产品特点 -->
      <div class="section" v-if="introduction.productFeatures && introduction.productFeatures.length > 0">
        <h3 class="section-title">产品特点</h3>
        <div class="features-grid">
          <div v-for="(feature, index) in introduction.productFeatures" :key="index" class="feature-card">
            <div class="feature-header">
              <img v-if="feature.iconUrl" :src="feature.iconUrl" alt="图标" class="feature-icon"
                @error="handleIconError" />
              <div v-else class="feature-icon-placeholder">
                <Icon icon="mdi:star" />
              </div>
              <div class="feature-info">
                <div class="feature-name">{{ feature.name }}</div>
                <div class="feature-order">排序: {{ feature.sortOrder || '-' }}</div>
              </div>
            </div>
            <div class="feature-value">
              {{ feature.value }}
            </div>
          </div>
        </div>
      </div>

      <!-- 产品概况 -->
      <div class="section" v-if="introduction.productOverview">
        <h3 class="section-title">产品概况</h3>
        <div class="overview-content" v-html="introduction.productOverview"></div>
      </div>

      <!-- 空状态 -->
      <div v-if="!hasContent" class="empty-state">
        <a-empty description="暂无详细内容" />
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { computed } from 'vue';
import { Icon } from '@iconify/vue';

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  introduction: {
    type: Object,
    default: null
  }
});

// Emits
const emit = defineEmits(['update:visible']);

// 计算属性
const hasContent = computed(() => {
  if (!props.introduction) return false;

  return !!(
    props.introduction.productSummary ||
    props.introduction.bannerImageUrl ||
    (props.introduction.productFeatures && props.introduction.productFeatures.length > 0) ||
    props.introduction.productOverview
  );
});

// 图片加载错误处理
const handleImageError = (event) => {
  event.target.style.display = 'none';
};

// 图标加载错误处理
const handleIconError = (event) => {
  event.target.style.display = 'none';
  event.target.nextElementSibling.style.display = 'flex';
};

// 取消操作
const handleCancel = () => {
  emit('update:visible', false);
};
</script>

<style scoped>
.introduction-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.section {
  margin-bottom: 24px;
}

.section:last-child {
  margin-bottom: 0;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  border-bottom: 2px solid #1890ff;
  padding-bottom: 8px;
}

.summary-content {
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
  line-height: 1.6;
  color: #595959;
}

.banner-container {
  text-align: center;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.banner-image {
  max-width: 100%;
  max-height: 300px;
  object-fit: contain;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.feature-card {
  padding: 16px;
  background-color: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.3s ease;
}

.feature-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.feature-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.feature-icon {
  width: 32px;
  height: 32px;
  object-fit: cover;
  border-radius: 4px;
  margin-right: 12px;
}

.feature-icon-placeholder {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  border-radius: 4px;
  margin-right: 12px;
  color: #8c8c8c;
  font-size: 18px;
}

.feature-info {
  flex: 1;
}

.feature-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.feature-order {
  font-size: 12px;
  color: #8c8c8c;
}

.feature-value {
  color: #595959;
  line-height: 1.5;
}

.overview-content {
  padding: 16px;
  background-color: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  line-height: 1.6;
}

.overview-content :deep(h1),
.overview-content :deep(h2),
.overview-content :deep(h3) {
  margin-top: 16px;
  margin-bottom: 8px;
  color: #262626;
}

.overview-content :deep(h1) {
  font-size: 20px;
}

.overview-content :deep(h2) {
  font-size: 18px;
}

.overview-content :deep(h3) {
  font-size: 16px;
}

.overview-content :deep(ul),
.overview-content :deep(ol) {
  padding-left: 20px;
  margin: 8px 0;
}

.overview-content :deep(li) {
  margin: 4px 0;
}

.overview-content :deep(p) {
  margin: 8px 0;
}

.overview-content :deep(strong) {
  font-weight: 600;
  color: #262626;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}
</style>
