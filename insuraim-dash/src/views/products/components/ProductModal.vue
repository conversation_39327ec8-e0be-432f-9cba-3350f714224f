<template>
  <a-modal :visible="visible" :title="isEdit ? '编辑产品' : '新增产品'" width="900px" @ok="handleSubmit" @cancel="handleCancel"
    :confirm-loading="loading">

    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="产品名称" name="productName">
            <a-input v-model:value="form.productName" placeholder="请输入产品名称" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="分类" name="categoryId">
            <a-select v-model:value="form.categoryId" placeholder="请选择分类">
              <a-select-option v-for="category in categories" :key="category.id" :value="category.id">
                {{ category.name }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="保险公司代码" name="companyCode">
            <a-input v-model:value="form.companyCode" placeholder="请输入保险公司代码" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="保险公司名称" name="companyName">
            <a-input v-model:value="form.companyName" placeholder="请输入保险公司名称" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="产品类别代码" name="categoryCode">
            <a-input v-model:value="form.categoryCode" placeholder="请输入产品类别代码" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="产品类别名称" name="categoryName">
            <a-input v-model:value="form.categoryName" placeholder="请输入产品类别名称" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="销售地区" name="region">
            <a-input v-model:value="form.region" placeholder="请输入销售地区" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="产品类型" name="productType">
            <a-select v-model:value="form.productType" placeholder="请选择产品类型">
              <a-select-option value="BASIC">基本计划(BASIC)</a-select-option>
              <a-select-option value="SPECIAL">特级计划(SPECIAL)</a-select-option>
              <a-select-option value="ADDITIONAL_PROTECTION">附加保障(ADDITIONAL_PROTECTION)</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="状态" name="status">
            <a-select v-model:value="form.status" placeholder="请选择状态">
              <a-select-option :value="1">启用</a-select-option>
              <a-select-option :value="0">禁用</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="保障年限" name="guaranteePeriod">
            <a-input v-model:value="form.guaranteePeriod" placeholder="请输入保障年限" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="产品Logo URL" name="logoUrl">
        <a-input v-model:value="form.logoUrl" placeholder="请输入产品Logo URL" />
      </a-form-item>

      <a-form-item label="产品小册子" name="productBrochure">
        <a-input v-model:value="form.productBrochure" placeholder="请输入产品小册子URL" />
      </a-form-item>

      <a-form-item label="产品描述" name="description">
        <a-textarea v-model:value="form.description" placeholder="请输入产品描述" :rows="2" />
      </a-form-item>

      <a-form-item label="产品标签" name="tags">
        <a-select v-model:value="form.tags" mode="tags" placeholder="请输入产品标签" />
      </a-form-item>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="缴费期限" name="paymentTerm">
            <a-select v-model:value="form.paymentTerm" mode="tags" placeholder="请输入缴费期限" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="支持币种" name="currencies">
            <a-select v-model:value="form.currencies" mode="multiple" placeholder="请选择支持币种">
              <a-select-option value="USD">美元(USD)</a-select-option>
              <a-select-option value="HKD">港币(HKD)</a-select-option>
              <a-select-option value="MOP">澳门币(MOP)</a-select-option>
              <a-select-option value="RMB">人民币(RMB)</a-select-option>
              <a-select-option value="AUD">澳元(AUD)</a-select-option>
              <a-select-option value="CAD">加拿大元(CAD)</a-select-option>
              <a-select-option value="CHF">瑞士法郎(CHF)</a-select-option>
              <a-select-option value="EUR">欧元(EUR)</a-select-option>
              <a-select-option value="GBP">英镑(GBP)</a-select-option>
              <a-select-option value="NZD">新西兰元(NZD)</a-select-option>
              <a-select-option value="SGD">新加坡元(SGD)</a-select-option>
              <a-select-option value="JPY">日元(JPY)</a-select-option>
              <a-select-option value="KRW">韩元(KRW)</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="缴费方式" name="paymentMethods">
        <a-select v-model:value="form.paymentMethods" mode="multiple" placeholder="请选择缴费方式">
          <a-select-option value="ANNUALLY">年缴</a-select-option>
          <a-select-option value="SEMI_ANNUALLY">半年缴</a-select-option>
          <a-select-option value="QUARTERLY">季缴</a-select-option>
          <a-select-option value="MONTHLY">月缴</a-select-option>
          <a-select-option value="SINGLE_PAYMENT">趸缴</a-select-option>
        </a-select>
      </a-form-item>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="年龄范围">
            <a-input-group compact>
              <a-input-number v-model:value="ageRangeMin" placeholder="最小" :min="0" :max="150" style="width: 40%" />
              <a-input style="width: 20%; text-align: center" placeholder="至" disabled />
              <a-input-number v-model:value="ageRangeMax" placeholder="最大" :min="0" :max="150" style="width: 40%" />
            </a-input-group>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="是否支持生成计划书" name="isProposal">
            <a-select v-model:value="form.isProposal" placeholder="请选择">
              <a-select-option :value="1">支持</a-select-option>
              <a-select-option :value="0">不支持</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="是否允许预缴" name="isPrepayment">
        <a-select v-model:value="form.isPrepayment" placeholder="请选择">
          <a-select-option :value="1">允许</a-select-option>
          <a-select-option :value="0">不允许</a-select-option>
        </a-select>
      </a-form-item>

    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import { createProduct, updateProduct } from '@/api/productManage';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  product: {
    type: Object,
    default: null
  },
  categories: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:visible', 'success']);

const loading = ref(false);
const formRef = ref();
const ageRangeMin = ref(null);
const ageRangeMax = ref(null);

// 表单数据
const form = reactive({
  productName: '',
  categoryId: null,
  companyCode: '',
  companyName: '',
  categoryCode: '',
  categoryName: '',
  region: '',
  productType: '',
  guaranteePeriod: '',
  logoUrl: '',
  productBrochure: '',
  description: '',
  tags: [],
  paymentTerm: [],
  currencies: [],
  paymentMethods: [],
  isProposal: null,
  isPrepayment: null,
  status: 1
});

// 表单验证规则
const rules = {
  productName: [{ required: true, message: '请输入产品名称' }],
  categoryId: [{ required: true, message: '请选择分类' }],
  companyCode: [{ required: true, message: '请输入保险公司代码' }],
  companyName: [{ required: true, message: '请输入保险公司名称' }],
  status: [{ required: true, message: '请选择状态' }]
};

// 重置表单
const resetForm = () => {
  Object.keys(form).forEach(key => {
    if (Array.isArray(form[key])) {
      form[key] = [];
    } else if (typeof form[key] === 'number') {
      form[key] = key === 'status' ? 1 : null;
    } else {
      form[key] = '';
    }
  });
  ageRangeMin.value = null;
  ageRangeMax.value = null;
};

// 是否编辑模式
const isEdit = computed(() => !!props.product);

// 监听产品数据变化
watch(() => props.product, (newProduct) => {
  if (newProduct) {
    // 编辑模式，填充表单
    Object.keys(form).forEach(key => {
      if (newProduct[key] !== undefined) {
        form[key] = newProduct[key];
      }
    });

    // 处理年龄范围
    if (newProduct.ageRange) {
      ageRangeMin.value = newProduct.ageRange.min !== undefined ? newProduct.ageRange.min : null;
      ageRangeMax.value = newProduct.ageRange.max !== undefined ? newProduct.ageRange.max : null;
    }
  } else {
    // 新增模式，重置表单
    resetForm();
  }
}, { immediate: true });

// 监听分类选择变化，自动填充产品类别代码和名称
watch(() => form.categoryId, (newCategoryId) => {
  if (newCategoryId && props.categories.length > 0) {
    // 根据选中的分类ID找到对应的分类对象
    const selectedCategory = props.categories.find(category => category.id === newCategoryId);
    if (selectedCategory) {
      // 自动填充产品类别代码和名称
      form.categoryCode = selectedCategory.code || '';
      form.categoryName = selectedCategory.name || '';
    }
  } else {
    // 如果没有选择分类，清空相关字段
    form.categoryCode = '';
    form.categoryName = '';
  }
});

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate();

    loading.value = true;

    // 构建提交数据
    const submitData = { ...form };

    // 处理年龄范围
    if (ageRangeMin.value !== null || ageRangeMax.value !== null) {
      submitData.ageRange = {
        min: ageRangeMin.value,
        max: ageRangeMax.value
      };
    }

    if (isEdit.value) {
      await updateProduct(props.product.id, submitData);
      message.success('更新成功');
    } else {
      await createProduct(submitData);
      message.success('创建成功');
    }

    emit('success');
  } catch (error) {
    if (error.errorFields) {
      // 表单验证错误
      return;
    }
    console.error('提交失败:', error);
    message.error('提交失败: ' + (error.response?.data?.message || error.message));
  } finally {
    loading.value = false;
  }
};

// 取消
const handleCancel = () => {
  emit('update:visible', false);
};
</script>

<style scoped>
.ant-form-item {
  margin-bottom: 16px;
}
</style>
