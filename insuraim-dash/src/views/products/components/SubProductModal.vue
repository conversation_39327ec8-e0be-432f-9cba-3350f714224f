<template>
  <a-modal :visible="visible" :title="isEdit ? '编辑子产品' : '新增子产品'" width="700px" @ok="handleSubmit"
    @cancel="handleCancel" :confirm-loading="loading">
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
      <a-form-item label="父产品">
        <a-input :value="parentProduct?.productName" disabled />
      </a-form-item>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="子产品名称" name="productName">
            <a-input-group compact>
              <a-input v-model:value="form.productName" placeholder="将根据父产品名称、币种和缴费期限自动生成"
                style="width: calc(100% - 80px)" />
              <a-button @click="resetToAutoGenerated" :disabled="!isProductNameManuallyEdited" style="width: 80px">
                重新生成
              </a-button>
            </a-input-group>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="状态" name="status">
            <a-select v-model:value="form.status" placeholder="请选择状态">
              <a-select-option :value="1">启用</a-select-option>
              <a-select-option :value="0">禁用</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="缴费期限" name="paymentTerm">
            <a-input v-model:value="form.paymentTerm" placeholder="请输入缴费期限" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="币种" name="currency">
            <a-select v-model:value="form.currency" placeholder="请选择币种">
              <a-select-option value="USD">美元 (USD)</a-select-option>
              <a-select-option value="HKD">港元 (HKD)</a-select-option>
              <a-select-option value="MOP">澳門元 (MOP)</a-select-option>
              <a-select-option value="CNY">人民幣 (CNY)</a-select-option>
              <a-select-option value="GBP">英鎊 (GBP)</a-select-option>
              <a-select-option value="SGD">新加坡元 (SGD)</a-select-option>
              <a-select-option value="AUD">澳元 (AUD)</a-select-option>
              <a-select-option value="CAD">加元 (CAD)</a-select-option>
              <a-select-option value="CHF">瑞士法郎 (CHF)</a-select-option>
              <a-select-option value="EUR">歐元 (EUR)</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 年龄范围 -->
      <a-form-item label="年龄范围">
        <a-row :gutter="8">
          <a-col :span="11">
            <a-input v-model:value="ageRangeMin" placeholder="最小年龄（如：18、出生30天等）" style="width: 100%" />
          </a-col>
          <a-col :span="2" style="text-align: center">
            <span>至</span>
          </a-col>
          <a-col :span="11">
            <a-input v-model:value="ageRangeMax" placeholder="最大年龄（如：65、终身等）" style="width: 100%" />
          </a-col>
        </a-row>
      </a-form-item>

      <!-- 保额范围 -->
      <a-form-item label="保额范围">
        <a-row :gutter="8">
          <a-col :span="11">
            <a-input-number v-model:value="sumInsuredMin" placeholder="最小保额" :min="0"
              :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
              :parser="value => value.replace(/\$\s?|(,*)/g, '')" style="width: 100%" />
          </a-col>
          <a-col :span="2" style="text-align: center">
            <span>至</span>
          </a-col>
          <a-col :span="11">
            <a-input-number v-model:value="sumInsuredMax" placeholder="最大保额" :min="0"
              :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
              :parser="value => value.replace(/\$\s?|(,*)/g, '')" style="width: 100%" />
          </a-col>
        </a-row>
      </a-form-item>

      <!-- 保费范围 -->
      <a-form-item label="保费范围">
        <a-row :gutter="8">
          <a-col :span="11">
            <a-input v-model:value="premiumMin" placeholder="最小保费（如：1000 或 面议）" style="width: 100%" />
          </a-col>
          <a-col :span="2" style="text-align: center">
            <span>至</span>
          </a-col>
          <a-col :span="11">
            <a-input v-model:value="premiumMax" placeholder="最大保费（如：50000 或 无上限）" style="width: 100%" />
          </a-col>
        </a-row>
      </a-form-item>

      <a-form-item label="预缴年数" name="prepaymentYears">
        <a-select v-model:value="form.prepaymentYears" mode="tags" placeholder="请输入预缴年数" style="width: 100%">
          <a-select-option :key="year" :value="year">
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="缴费方式" name="paymentMethods">
        <a-select v-model:value="form.paymentMethods" mode="multiple" placeholder="请选择缴费方式" style="width: 100%">
          <a-select-option value="ANNUALLY">年缴</a-select-option>
          <a-select-option value="SEMI_ANNUALLY">半年缴</a-select-option>
          <a-select-option value="QUARTERLY">季缴</a-select-option>
          <a-select-option value="MONTHLY">月缴</a-select-option>
          <a-select-option value="SINGLE_PAYMENT">趸缴</a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import { createSubProduct, updateSubProduct } from '@/api/productManage';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  subProduct: {
    type: Object,
    default: null
  },
  parentProduct: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update:visible', 'success']);

const loading = ref(false);
const formRef = ref();

// 范围值
const ageRangeMin = ref('');
const ageRangeMax = ref('');
const sumInsuredMin = ref(null);
const sumInsuredMax = ref(null);
const premiumMin = ref('');
const premiumMax = ref('');

// 表单数据
const form = reactive({
  productName: '',
  paymentTerm: '',
  currency: '',
  prepaymentYears: [],
  paymentMethods: [],
  status: 1
});

// 标记用户是否手动修改过产品名称
const isProductNameManuallyEdited = ref(false);

// 币种映射
const currencyMap = {
  'USD': '美元',
  'HKD': '港元',
  'MOP': '澳門元',
  'CNY': '人民幣',
  'GBP': '英鎊',
  'SGD': '新加坡元',
  'AUD': '澳元',
  'CAD': '加元',
  'CHF': '瑞士法郎',
  'EUR': '歐元'
};

// 自动生成产品名称
const displayProductName = computed(() => {
  let name = props.parentProduct?.productName || '';

  if (!name) {
    return '';
  }

  // 添加币种后缀
  if (form.currency && currencyMap[form.currency]) {
    name += `(${currencyMap[form.currency]})`;
  }

  // 添加缴费期限后缀
  if (form.paymentTerm) {
    name += `(${form.paymentTerm})`;
  }

  return name;
});

// 监听显示名称变化，只在用户没有手动编辑时才自动更新
watch(displayProductName, (newName) => {
  if (!isProductNameManuallyEdited.value) {
    form.productName = newName;
  }
});

// 监听产品名称的手动修改
watch(() => form.productName, (newValue, oldValue) => {
  // 如果新值不等于自动生成的值，说明用户手动修改了
  if (newValue !== displayProductName.value && oldValue !== undefined) {
    isProductNameManuallyEdited.value = true;
  }
});

// 重新启用自动生成
const resetToAutoGenerated = () => {
  isProductNameManuallyEdited.value = false;
  form.productName = displayProductName.value;
};

// 表单验证规则
const rules = {
  productName: [{ required: true, message: '请输入子产品名称' }],
  status: [{ required: true, message: '请选择状态' }]
};

// 重置表单
const resetForm = () => {
  Object.keys(form).forEach(key => {
    if (Array.isArray(form[key])) {
      form[key] = [];
    } else if (typeof form[key] === 'number') {
      form[key] = key === 'status' ? 1 : null;
    } else {
      form[key] = '';
    }
  });

  // 重置范围值
  ageRangeMin.value = '';
  ageRangeMax.value = '';
  sumInsuredMin.value = null;
  sumInsuredMax.value = null;
  premiumMin.value = '';
  premiumMax.value = '';

  // 重置手动编辑标记
  isProductNameManuallyEdited.value = false;
};

// 是否编辑模式
const isEdit = computed(() => !!props.subProduct);

// 监听子产品数据变化
watch(() => props.subProduct, (newSubProduct) => {
  if (newSubProduct) {
    // 编辑模式，填充表单
    Object.keys(form).forEach(key => {
      if (newSubProduct[key] !== undefined) {
        form[key] = newSubProduct[key];
      }
    });

    // 在编辑模式下，如果产品名称不等于自动生成的名称，标记为手动编辑
    if (newSubProduct.productName && newSubProduct.productName !== displayProductName.value) {
      isProductNameManuallyEdited.value = true;
    }

    // 处理范围值
    if (newSubProduct.ageRange) {
      ageRangeMin.value = newSubProduct.ageRange.min || '';
      ageRangeMax.value = newSubProduct.ageRange.max || '';
    }
    if (newSubProduct.sumInsuredRange) {
      if (Array.isArray(newSubProduct.sumInsuredRange) && newSubProduct.sumInsuredRange.length === 2) {
        sumInsuredMin.value = newSubProduct.sumInsuredRange[0] || null;
        sumInsuredMax.value = newSubProduct.sumInsuredRange[1] || null;
      } else {
        sumInsuredMin.value = null;
        sumInsuredMax.value = null;
      }
    }
    if (newSubProduct.premiumRange) {
      if (Array.isArray(newSubProduct.premiumRange) && newSubProduct.premiumRange.length === 2) {
        premiumMin.value = newSubProduct.premiumRange[0] || '';
        premiumMax.value = newSubProduct.premiumRange[1] || '';
      } else {
        premiumMin.value = '';
        premiumMax.value = '';
      }
    }
  } else {
    // 新增模式，重置表单
    resetForm();
  }
}, { immediate: true });

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate();

    loading.value = true;

    // 构建提交数据
    const submitData = {
      ...form,
      parentProductId: props.parentProduct.id,
      categoryId: props.parentProduct.categoryId
    };

    // 确保预缴年数是整数列表
    if (form.prepaymentYears && Array.isArray(form.prepaymentYears)) {
      submitData.prepaymentYears = form.prepaymentYears.map(year => {
        const parsed = parseInt(year);
        return isNaN(parsed) ? 0 : parsed;
      });
    }

    // 处理范围值
    if (ageRangeMin.value !== '' || ageRangeMax.value !== '') {
      submitData.ageRange = {
        min: ageRangeMin.value || '',
        max: ageRangeMax.value || ''
      };
    }

    if (sumInsuredMin.value !== null || sumInsuredMax.value !== null) {
      submitData.sumInsuredRange = [
        sumInsuredMin.value?.toString() || '0',
        sumInsuredMax.value?.toString() || '0'
      ];
    }

    if (premiumMin.value !== '' || premiumMax.value !== '') {
      submitData.premiumRange = [
        premiumMin.value || '',
        premiumMax.value || ''
      ];
    }

    if (isEdit.value) {
      await updateSubProduct(props.subProduct.id, submitData);
      message.success('更新成功');
    } else {
      await createSubProduct(submitData);
      message.success('创建成功');
    }

    emit('success');
  } catch (error) {
    if (error.errorFields) {
      // 表单验证错误
      return;
    }
    console.error('提交失败:', error);
    message.error('提交失败: ' + (error.response?.data?.message || error.message));
  } finally {
    loading.value = false;
  }
};

// 取消
const handleCancel = () => {
  emit('update:visible', false);
};
</script>

<style scoped>
.ant-form-item {
  margin-bottom: 16px;
}
</style>
