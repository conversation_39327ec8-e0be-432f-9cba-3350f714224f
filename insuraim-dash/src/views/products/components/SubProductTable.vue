<template>
  <div class="sub-product-table">
    <div class="sub-product-header">
      <h4>子产品列表</h4>
      <a-button type="primary" size="small" @click="handleAddSubProduct">
        <PlusOutlined /> 添加子产品
      </a-button>
    </div>

    <a-table :columns="subProductColumns" :data-source="subProducts" :loading="loading" :pagination="false" row-key="id"
      size="small">
      <!-- 使用新的插槽语法 -->
      <template #bodyCell="{ column, record }">
        <!-- 产品名称列 -->
        <template v-if="column.key === 'productName'">
          <div class="sub-product-name">{{ record.productName }}</div>
        </template>

        <!-- 缴费期限列 -->
        <template v-else-if="column.key === 'paymentTerm'">
          <p v-if="record.paymentTerm" color="blue">{{ formatPaymentTerm(record.paymentTerm) }}</p>
          <span v-else>-</span>
        </template>

        <!-- 币种列 -->
        <template v-else-if="column.key === 'currency'">
          <a-tag v-if="record.currency" color="green">{{ getCurrencyName(record.currency) }}</a-tag>
          <span v-else>-</span>
        </template>

        <!-- 年龄范围列 -->
        <template v-else-if="column.key === 'ageRange'">
          <span v-if="record.ageRange && (record.ageRange.min || record.ageRange.max)">
            {{ record.ageRange.min || '-' }} - {{ record.ageRange.max || '-' }}
          </span>
          <span v-else>-</span>
        </template>

        <!-- 保额范围列 -->
        <template v-else-if="column.key === 'sumInsuredRange'">
          <span
            v-if="record.sumInsuredRange && Array.isArray(record.sumInsuredRange) && record.sumInsuredRange.length === 2">
            {{ formatAmount(record.sumInsuredRange[0]) }} - {{ formatAmount(record.sumInsuredRange[1]) }}
          </span>
          <span v-else>-</span>
        </template>

        <!-- 保费范围列 -->
        <template v-else-if="column.key === 'premiumRange'">
          <span v-if="record.premiumRange && Array.isArray(record.premiumRange) && record.premiumRange.length === 2">
            {{ formatPremiumRange(record.premiumRange[0]) }} - {{ formatPremiumRange(record.premiumRange[1]) }}
          </span>
          <span v-else>-</span>
        </template>

        <!-- 状态列 -->
        <template v-else-if="column.key === 'status'">
          <a-switch :checked="record.status === 1" @change="(checked) => handleStatusChange(record, checked)"
            :loading="record.statusLoading" size="small" />
        </template>

        <!-- 操作列 -->
        <template v-else-if="column.key === 'action'">
          <div class="action-buttons-inline">
            <a-button type="link" size="small" @click="handleEditSubProduct(record)">
              <EditOutlined /> 编辑
            </a-button>
            <a-popconfirm title="确定要删除这个子产品吗？" @confirm="handleDeleteSubProduct(record.id)">
              <a-button type="link" size="small" danger>
                <DeleteOutlined /> 删除
              </a-button>
            </a-popconfirm>
          </div>
        </template>
      </template>


    </a-table>

    <!-- 子产品编辑弹窗 -->
    <SubProductModal v-model:visible="subProductModalVisible" :sub-product="currentSubProduct"
      :parent-product="parentProduct" @success="handleSubProductSuccess" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue';

import {
  getSubProductsByParentId,
  updateSubProduct,
  deleteSubProduct
} from '@/api/productManage';

import SubProductModal from './SubProductModal.vue';

const props = defineProps({
  parentProduct: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['refresh']);

// 响应式数据
const loading = ref(false);
const subProducts = ref([]);
const subProductModalVisible = ref(false);
const currentSubProduct = ref(null);

// 子产品表格列配置
const subProductColumns = [
  {
    title: '子产品名称',
    dataIndex: 'productName',
    key: 'productName',
    width: 200
  },
  {
    title: '缴费期限',
    dataIndex: 'paymentTerm',
    key: 'paymentTerm',
    width: 100
  },
  {
    title: '币种',
    dataIndex: 'currency',
    key: 'currency',
    width: 80
  },
  {
    title: '年龄范围',
    key: 'ageRange',
    width: 120
  },
  {
    title: '保额范围',
    key: 'sumInsuredRange',
    width: 150
  },
  {
    title: '保费范围',
    key: 'premiumRange',
    width: 150
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80
  },
  {
    title: '操作',
    key: 'action',
    width: 120
  }
];

// 币种映射
const currencyMap = {
  'USD': '美元',
  'HKD': '港元',
  'MOP': '澳門元',
  'CNY': '人民幣',
  'GBP': '英鎊',
  'SGD': '新加坡元',
  'AUD': '澳元',
  'CAD': '加元',
  'CHF': '瑞士法郎',
  'EUR': '歐元'
};

// 获取币种中文名称
const getCurrencyName = (currencyCode) => {
  return currencyMap[currencyCode] || currencyCode;
};

// 格式化金额
const formatAmount = (amount) => {
  if (!amount) return '0';
  return new Intl.NumberFormat('zh-CN').format(amount);
};

// 格式化保费范围（支持字符串和数字）
const formatPremiumRange = (premium) => {
  if (!premium) return '-';

  // 如果是数字或者可以转换为数字，则格式化为金额
  const numValue = Number(premium);
  if (!isNaN(numValue) && isFinite(numValue)) {
    return new Intl.NumberFormat('zh-CN').format(numValue);
  }

  // 如果是字符串，直接返回
  return premium;
};

// 格式化缴费期限
const formatPaymentTerm = (paymentTerm) => {
  if (!paymentTerm) return '-';

  // 如果是数字或者可以转换为数字，则添加"年"
  const numValue = Number(paymentTerm);
  if (!isNaN(numValue) && isFinite(numValue)) {
    return `${paymentTerm}年`;
  }

  // 如果是字符串，直接返回
  return paymentTerm;
};

// 加载子产品列表
const loadSubProducts = async () => {
  if (!props.parentProduct?.id) return;

  loading.value = true;
  try {
    const res = await getSubProductsByParentId(props.parentProduct.id);
    subProducts.value = res.data || [];
  } catch (error) {
    console.error('加载子产品列表失败:', error);
    message.error('加载子产品列表失败');
  } finally {
    loading.value = false;
  }
};

// 添加子产品
const handleAddSubProduct = () => {
  currentSubProduct.value = null;
  subProductModalVisible.value = true;
};

// 编辑子产品
const handleEditSubProduct = (subProduct) => {
  currentSubProduct.value = { ...subProduct };
  subProductModalVisible.value = true;
};

// 子产品操作成功回调
const handleSubProductSuccess = () => {
  subProductModalVisible.value = false;
  loadSubProducts();
  emit('refresh'); // 刷新父组件的产品列表
};

// 状态切换
const handleStatusChange = async (subProduct, checked) => {
  subProduct.statusLoading = true;
  try {
    await updateSubProduct(subProduct.id, {
      ...subProduct,
      status: checked ? 1 : 0
    });
    subProduct.status = checked ? 1 : 0;
    message.success('状态更新成功');
  } catch (error) {
    console.error('状态更新失败:', error);
    message.error('状态更新失败');
  } finally {
    subProduct.statusLoading = false;
  }
};

// 删除子产品
const handleDeleteSubProduct = async (id) => {
  try {
    await deleteSubProduct(id);
    message.success('删除成功');
    loadSubProducts();
    emit('refresh'); // 刷新父组件的产品列表
  } catch (error) {
    console.error('删除失败:', error);
    message.error('删除失败: ' + (error.response?.data?.message || error.message));
  }
};

// 初始化
onMounted(() => {
  loadSubProducts();
});
</script>

<style scoped>
.sub-product-table {
  margin: 16px 0;
  padding: 16px;
  border-radius: 6px;
}

.sub-product-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.sub-product-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.sub-product-name {
  font-weight: 500;
}
</style>
