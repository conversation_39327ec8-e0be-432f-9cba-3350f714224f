<template>
  <div class="menus-view">
    <!-- 页面标题和操作按钮 -->
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-semibold text-gray-800">菜单管理</h1>
      <div class="flex gap-4">
        <a-button type="primary" @click="handleAddMenu" class="icon-align-button">
          <template #icon>
            <Icon icon="material-symbols:add" />
          </template>
          新增菜单
        </a-button>
        <a-button @click="handleBatchOperation" class="icon-align-button">
          <template #icon>
            <Icon icon="material-symbols:published-with-changes" />
          </template>
          批量操作
        </a-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <a-row :gutter="16" class="mb-6">
      <a-col :span="6">
        <a-card>
          <a-statistic title="菜单总数" :value="stats.total" :value-style="{ color: '#1890ff' }">
            <template #prefix>
              <Icon icon="mdi:menu" class="mr-1" />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic title="启用菜单" :value="stats.enabled" :value-style="{ color: '#52c41a' }">
            <template #prefix>
              <Icon icon="mdi:check-circle" class="mr-1" />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic title="禁用菜单" :value="stats.disabled" :value-style="{ color: '#f5222d' }">
            <template #prefix>
              <Icon icon="mdi:close-circle" class="mr-1" />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic title="顶级菜单" :value="stats.topLevel" :value-style="{ color: '#722ed1' }">
            <template #prefix>
              <Icon icon="mdi:format-list-bulleted" class="mr-1" />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>

    <!-- 搜索和筛选区域 -->
    <a-card class="mb-6">
      <a-form layout="inline" :model="searchForm">
        <a-row :gutter="16" style="width: 100%;">
          <a-col :span="6">
            <a-form-item label="菜单名称">
              <a-input v-model:value="searchForm.name" placeholder="请输入菜单名称" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="路由路径">
              <a-input v-model:value="searchForm.path" placeholder="请输入路由路径" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="菜单类型">
              <a-select v-model:value="searchForm.menuType" placeholder="请选择菜单类型" style="width: 100%" allow-clear>
                <a-select-option value="">全部</a-select-option>
                <a-select-option value="0">目录</a-select-option>
                <a-select-option value="1">菜单</a-select-option>
                <a-select-option value="2">按钮</a-select-option>
                <a-select-option value="3">页面</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="状态">
              <a-select v-model:value="searchForm.status" placeholder="请选择状态" style="width: 100%" allow-clear>
                <a-select-option value="1">启用</a-select-option>
                <a-select-option value="0">禁用</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24" style="text-align: right;">
            <a-space class="mt-4">
              <a-button @click="handleReset" class="icon-align-button">重置</a-button>
              <a-button type="primary" @click="handleSearch" class="icon-align-button">
                <template #icon>
                  <Icon icon="material-symbols:search" />
                </template>
                搜索
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <!-- 菜单列表 -->
    <a-card>
      <template #extra>
        <a-space>
          <a-button size="small" @click="expandAll">
            <Icon icon="mdi:unfold-more-horizontal" class="mr-1" />
            展开所有
          </a-button>
          <a-button size="small" @click="collapseAll">
            <Icon icon="mdi:unfold-less-horizontal" class="mr-1" />
            收起所有
          </a-button>
        </a-space>
      </template>

      <a-table ref="tableRef" :columns="columns" :data-source="treeMenus" :row-key="record => record.id"
        :pagination="false" :loading="tableLoading" :expand-icon-column-index="1" :default-expand-all-rows="false"
        :child-row-key="children">
        <template #headerCell="{ column }">
          <template v-if="column.key === 'selection'">
            <a-checkbox v-model:checked="selectAll" @change="handleSelectAll" />
          </template>
        </template>

        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'selection'">
            <a-checkbox v-model:checked="record.selected" />
          </template>

          <template v-if="column.key === 'name'">
            <div class="flex items-center">
              <Icon :icon="record.icon || 'mdi:menu'" class="mr-2 text-gray-600" style="font-size: 18px;" />
              <span>{{ record.name }}</span>
            </div>
          </template>

          <template v-if="column.key === 'icon'">
            <Icon v-if="record.icon" :icon="record.icon" class="text-gray-600" style="font-size: 18px;" />
            <span v-else class="text-gray-400">-</span>
          </template>

          <template v-if="column.key === 'status'">
            <a-tag :color="record.status === 1 ? 'success' : 'error'">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleEdit(record)" class="icon-align-button">
                <template #icon>
                  <Icon icon="material-symbols:edit" />
                </template>
                编辑
              </a-button>

              <a-button type="link" size="small" @click="handleToggleStatus(record)" class="icon-align-button">
                <template #icon>
                  <Icon :icon="record.status === 1 ? 'material-symbols:unpublished' : 'material-symbols:publish'" />
                </template>
                {{ record.status === 1 ? '禁用' : '启用' }}
              </a-button>

              <a-popconfirm title="确定要删除此菜单吗?" ok-text="确定" cancel-text="取消" @confirm="handleDelete(record)">
                <a-button type="link" danger size="small" class="icon-align-button">
                  <template #icon>
                    <Icon icon="material-symbols:delete" />
                  </template>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 菜单表单对话框 -->
    <a-modal v-model:visible="menuModalVisible" :title="isEdit ? '编辑菜单' : '新增菜单'" @cancel="handleModalCancel"
      :mask-closable="false" :footer="null" width="600px">
      <a-form :model="menuForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" layout="horizontal">
        <a-form-item label="菜单名称" name="name" :rules="[{ required: true, message: '请输入菜单名称' }]">
          <a-input v-model:value="menuForm.name" placeholder="请输入菜单名称" />
        </a-form-item>

        <a-form-item label="组件路径" name="component">
          <a-input v-model:value="menuForm.component" placeholder="请输入组件路径" />
        </a-form-item>

        <a-form-item label="路由名称" name="routeName">
          <a-input v-model:value="menuForm.routeName" placeholder="请输入路由名称" />
        </a-form-item>

        <a-form-item label="菜单图标" name="icon">
          <div class="flex items-center gap-2">
            <a-input v-model:value="menuForm.icon" placeholder="请输入图标名称" />
            <div class="w-10 h-10 flex items-center justify-center bg-gray-100 rounded">
              <Icon v-if="menuForm.icon" :icon="menuForm.icon" style="font-size: 24px;" class="text-gray-600" />
            </div>
          </div>
          <div class="text-xs text-gray-500 mt-1">图标使用iconify格式，如: mdi:home</div>
        </a-form-item>

        <a-form-item label="路由路径" name="path">
          <a-input v-model:value="menuForm.path" placeholder="请输入路由路径" />
        </a-form-item>

        <a-form-item label="父级菜单" name="parentId">
          <a-tree-select v-model:value="menuForm.parentId" style="width: 100%" :tree-data="parentMenuTreeData"
            placeholder="请选择父级菜单" tree-default-expand-all :show-search="true" :filter-tree-node="filterTreeNode"
            allow-clear>
            <template #title="{ title, menuType }">
              <span>
                {{ title }}
                <a-tag v-if="menuType !== null" size="small" :color="getMenuTypeColor(menuType)">
                  {{ getMenuTypeText(menuType) }}
                </a-tag>
              </span>
            </template>
          </a-tree-select>
        </a-form-item>

        <a-form-item label="菜单类型" name="menuType" :rules="[{ required: true, message: '请选择菜单类型' }]">
          <a-select v-model:value="menuForm.menuType" style="width: 100%" placeholder="请选择菜单类型">
            <a-select-option :value="0">目录</a-select-option>
            <a-select-option :value="1">菜单</a-select-option>
            <a-select-option :value="2">按钮</a-select-option>
            <a-select-option :value="3">页面</a-select-option>
          </a-select>
          <div class="text-xs text-gray-500 mt-1">目录：用于分组；菜单：可访问的页面；按钮：页面内的操作按钮；页面：独立页面组件</div>
        </a-form-item>

        <a-form-item label="排序号" name="orderRank">
          <a-input-number v-model:value="menuForm.orderRank" :min="0" style="width: 100%" />
        </a-form-item>

        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="menuForm.remark" placeholder="请输入备注" :rows="3" />
        </a-form-item>

        <a-form-item label="状态" name="status">
          <a-switch v-model:checked="menuStatusEnabled" checked-children="启用" un-checked-children="禁用" />
        </a-form-item>

        <a-form-item :wrapper-col="{ span: 20, offset: 4 }">
          <a-space>
            <a-button @click="handleModalCancel" class="icon-align-button">取消</a-button>
            <a-button type="primary" @click="handleSaveMenu" class="icon-align-button">保存</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { Icon } from '@iconify/vue';
import {
  message,
} from 'ant-design-vue';
import {
  getMenuList,
  getMenuTree,
  addMenu,
  updateMenu,
  deleteMenu,
  batchDeleteMenus
} from '@/api/menu';

// 统计信息
const stats = ref({
  total: 0,
  enabled: 0,
  disabled: 0,
  topLevel: 0
});

// 搜索表单
const searchForm = ref({
  name: '',
  path: '',
  menuType: '',
  status: ''
});

// 所有菜单列表（用于父级菜单选择）
const allMenus = ref([]);

// 树形菜单数据（从后端获取）
const treeMenusData = ref([]);



// 父级菜单树形数据
const parentMenuTreeData = computed(() => {
  // 添加顶级选项
  const topLevelOption = {
    value: 0,
    title: '无父级(顶级菜单)',
    key: 0,
    menuType: null
  };

  // 构建可用的父级菜单树形数据
  const buildParentMenuTree = (menuList, parentId = 0) => {
    const result = [];

    for (const menu of menuList) {
      if (menu.parentId === parentId) {
        // 只显示目录(0)、菜单(1)和页面(3)类型，按钮(2)不能作为父级菜单
        if (menu.menuType !== 0 && menu.menuType !== 1 && menu.menuType !== 3) {
          continue;
        }

        // 如果是编辑模式，排除自己
        if (isEdit.value && menu.id === menuForm.value.id) {
          continue;
        }

        const menuItem = {
          value: menu.id,
          title: menu.name,
          key: menu.id,
          menuType: menu.menuType,
          children: buildParentMenuTree(menuList, menu.id)
        };

        result.push(menuItem);
      }
    }

    return result.sort((a, b) => {
      // 根据原始菜单的排序号排序
      const menuA = menuList.find(m => m.id === a.value);
      const menuB = menuList.find(m => m.id === b.value);
      return (menuA?.orderRank || 0) - (menuB?.orderRank || 0);
    });
  };

  if (!Array.isArray(allMenus.value)) {
    return [topLevelOption];
  }

  const treeData = buildParentMenuTree(allMenus.value);
  return [topLevelOption, ...treeData];
});



// 树形菜单数据（添加选择状态）
const treeMenus = computed(() => {
  return addSelectedState(treeMenusData.value);
});

// 为树形菜单数据添加选择状态
const addSelectedState = (menuList) => {
  if (!Array.isArray(menuList)) {
    return [];
  }

  return menuList.map(menu => ({
    ...menu,
    selected: false,
    children: menu.children ? addSelectedState(menu.children) : []
  }));
};

// 全选
const selectAll = ref(false);

// 表格引用
const tableRef = ref();

// 菜单表单对话框
const menuModalVisible = ref(false);
const isEdit = ref(false);
const menuForm = ref({
  id: null,
  name: '',
  icon: '',
  path: '',
  component: '',
  routeName: '',
  parentId: 0,
  orderRank: 0,
  status: 1,
  menuType: 1, // 默认为菜单类型
  remark: ''
});

// Switch状态控制
const menuStatusEnabled = computed({
  get: () => menuForm.value.status === 1,
  set: (val) => {
    menuForm.value.status = val ? 1 : 0;
  }
});

// 表格加载状态
const tableLoading = ref(false);

// 表格列定义
const columns = [
  {
    title: '',
    key: 'selection',
    width: 50
  },
  {
    title: '菜单名称',
    key: 'name',
    dataIndex: 'name',
    ellipsis: true
  },
  {
    title: '路由路径',
    dataIndex: 'path',
    key: 'path',
    ellipsis: true
  },
  {
    title: '组件路径',
    dataIndex: 'component',
    key: 'component',
    ellipsis: true
  },
  {
    title: '菜单类型',
    key: 'menuType',
    dataIndex: 'menuType',
    width: 120,
    customRender: ({ text }) => getMenuTypeText(text)
  },
  {
    title: '排序号',
    dataIndex: 'orderRank',
    key: 'orderRank',
    width: 80
  },
  {
    title: '状态',
    key: 'status',
    dataIndex: 'status',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 250
  }
];

// 获取状态文本
const getStatusText = (status) => {
  return status === 1 ? '启用' : '禁用';
};

// 获取菜单类型文本
const getMenuTypeText = (menuType) => {
  const typeMap = {
    0: '目录',
    1: '菜单',
    2: '按钮',
    3: '页面'
  };
  return typeMap[menuType] || '-';
};

// 获取菜单类型颜色
const getMenuTypeColor = (menuType) => {
  const colorMap = {
    0: 'blue',     // 目录
    1: 'green',    // 菜单
    2: 'orange',   // 按钮
    3: 'purple'    // 页面
  };
  return colorMap[menuType] || 'default';
};

// 树形选择器搜索过滤
const filterTreeNode = (inputValue, treeNode) => {
  return treeNode.title.toLowerCase().includes(inputValue.toLowerCase());
};



// 处理全选
const handleSelectAll = () => {
  const setSelected = (menuList, selected) => {
    menuList.forEach(menu => {
      menu.selected = selected;
      if (menu.children && menu.children.length > 0) {
        setSelected(menu.children, selected);
      }
    });
  };

  setSelected(treeMenus.value, selectAll.value);
};

// 展开所有
const expandAll = () => {
  const getAllKeys = (menuList) => {
    let keys = [];
    menuList.forEach(menu => {
      if (menu.children && menu.children.length > 0) {
        keys.push(menu.id);
        keys = keys.concat(getAllKeys(menu.children));
      }
    });
    return keys;
  };

  const expandedKeys = getAllKeys(treeMenus.value);
  if (tableRef.value) {
    tableRef.value.expandedRowKeys = expandedKeys;
  }
};

// 收起所有
const collapseAll = () => {
  if (tableRef.value) {
    tableRef.value.expandedRowKeys = [];
  }
};



// 加载所有菜单（用于父级菜单选择）
const loadAllMenus = async () => {
  try {
    const response = await getMenuList();
    allMenus.value = response.data || [];

    // 更新统计信息
    updateStats();
  } catch (error) {
    message.error('加载菜单列表失败：' + error.message);
    allMenus.value = [];
  }
};

// 加载树形菜单数据
const loadTreeMenus = async () => {
  try {
    tableLoading.value = true;
    const response = await getMenuTree();
    treeMenusData.value = response.data || [];
  } catch (error) {
    message.error('加载菜单树失败：' + error.message);
    treeMenusData.value = [];
  } finally {
    tableLoading.value = false;
  }
};

// 更新统计信息
const updateStats = () => {
  stats.value.total = allMenus.value.length;
  stats.value.enabled = allMenus.value.filter(menu => menu.status === 1).length;
  stats.value.disabled = allMenus.value.filter(menu => menu.status === 0).length;
  stats.value.topLevel = allMenus.value.filter(menu => menu.parentId === 0).length;
};

// 处理搜索
const handleSearch = async () => {
  // 树形表格中搜索功能可以通过过滤treeMenus实现
  // 这里暂时重新加载数据，后续可以优化为前端过滤
  await Promise.all([loadAllMenus(), loadTreeMenus()]);
};

// 处理重置
const handleReset = async () => {
  searchForm.value = {
    name: '',
    path: '',
    menuType: '',
    status: ''
  };
  await Promise.all([loadAllMenus(), loadTreeMenus()]);
};

// 处理新增菜单
const handleAddMenu = () => {
  isEdit.value = false;
  menuForm.value = {
    id: null,
    name: '',
    icon: '',
    path: '',
    component: '',
    routeName: '',
    parentId: 0,
    orderRank: 0,
    status: 1,
    menuType: 1, // 默认为菜单类型
    remark: ''
  };
  menuModalVisible.value = true;
};

// 处理编辑菜单
const handleEdit = (menu) => {
  isEdit.value = true;
  menuForm.value = { ...menu };
  menuModalVisible.value = true;
};

// 处理删除菜单
const handleDelete = async (menu) => {
  try {
    await deleteMenu(menu.id);
    message.success(`菜单"${menu.name}"已删除`);

    // 重新加载数据
    await Promise.all([loadAllMenus(), loadTreeMenus()]);
  } catch (error) {
    message.error('删除菜单失败：' + error.message);
  }
};

// 处理启用/禁用菜单
const handleToggleStatus = async (menu) => {
  const newStatus = menu.status === 1 ? 0 : 1;
  const statusText = newStatus === 1 ? '启用' : '禁用';

  try {
    await updateMenu({
      id: menu.id,
      status: newStatus
    });

    message.success(`菜单"${menu.name}"已${statusText}`);

    // 重新加载数据
    await Promise.all([loadAllMenus(), loadTreeMenus()]);
  } catch (error) {
    message.error(`${statusText}菜单失败：` + error.message);
  }
};

// 处理批量操作
const handleBatchOperation = async () => {
  const getSelectedMenus = (menuList) => {
    let selected = [];
    menuList.forEach(menu => {
      if (menu.selected) {
        selected.push(menu);
      }
      if (menu.children && menu.children.length > 0) {
        selected = selected.concat(getSelectedMenus(menu.children));
      }
    });
    return selected;
  };

  const selectedMenus = getSelectedMenus(treeMenus.value);
  if (selectedMenus.length === 0) {
    message.warning('请先选择菜单');
    return;
  }

  try {
    const ids = selectedMenus.map(menu => menu.id);
    await batchDeleteMenus(ids);
    message.success(`已删除${selectedMenus.length}个菜单`);

    // 重新加载数据
    await Promise.all([loadAllMenus(), loadTreeMenus()]);
  } catch (error) {
    message.error('批量删除菜单失败：' + error.message);
  }
};



// 处理对话框取消
const handleModalCancel = () => {
  menuModalVisible.value = false;
};

// 处理保存菜单
const handleSaveMenu = async () => {
  // 表单验证
  if (!menuForm.value.name) {
    message.error('请输入菜单名称');
    return;
  }

  try {
    if (isEdit.value) {
      // 编辑现有菜单
      await updateMenu(menuForm.value);
      message.success(`菜单"${menuForm.value.name}"已更新`);
    } else {
      // 新增菜单
      await addMenu(menuForm.value);
      message.success(`菜单"${menuForm.value.name}"已创建`);
    }

    menuModalVisible.value = false;

    // 重新加载数据
    await Promise.all([loadAllMenus(), loadTreeMenus()]);
  } catch (error) {
    message.error('保存菜单失败：' + error.message);
  }
};

// 加载数据
onMounted(async () => {
  await Promise.all([loadAllMenus(), loadTreeMenus()]);
});
</script>

<style scoped>
/* 保留必要的样式 */
.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-1 {
  margin-right: 0.25rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-4 {
  gap: 1rem;
}

.menus-view {
  padding: 20px;
}

/* 按钮图标对齐样式 */
.icon-align-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.icon-align-button :deep(.anticon) {
  display: flex;
  align-items: center;
}

.icon-align-button :deep(.ant-btn-icon) {
  display: flex;
  align-items: center;
}
</style>