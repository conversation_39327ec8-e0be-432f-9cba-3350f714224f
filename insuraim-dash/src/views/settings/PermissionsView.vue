<template>
  <div class="permissions-view">
    <!-- 页面标题和操作按钮 -->
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-semibold text-gray-800">权限管理</h1>
      <div class="flex gap-4">
        <a-button type="primary" @click="handleAddPermission" class="icon-align-button">
          <template #icon><Icon icon="material-symbols:add" /></template>
          新增权限
        </a-button>
        <a-button @click="handleBatchOperation" class="icon-align-button">
          <template #icon><Icon icon="material-symbols:published-with-changes" /></template>
          批量操作
        </a-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <a-row :gutter="16" class="mb-6">
      <a-col :span="8">
        <a-card>
          <a-statistic title="权限总数" :value="stats.total" :value-style="{ color: '#1890ff' }">
            <template #prefix>
              <Icon icon="mdi:shield-key" class="mr-1" />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="8">
        <a-card>
          <a-statistic title="系统权限" :value="stats.system" :value-style="{ color: '#722ed1' }">
            <template #prefix>
              <Icon icon="mdi:cog" class="mr-1" />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="8">
        <a-card>
          <a-statistic title="业务权限" :value="stats.business" :value-style="{ color: '#13c2c2' }">
            <template #prefix>
              <Icon icon="mdi:briefcase" class="mr-1" />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>

    <!-- 搜索和筛选区域 -->
    <a-card class="mb-6">
      <a-form layout="inline" :model="searchForm">
        <a-row :gutter="16" style="width: 100%;">
          <a-col :span="6">
            <a-form-item label="权限名称">
              <a-input v-model:value="searchForm.name" placeholder="请输入权限名称" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="权限标识">
              <a-input v-model:value="searchForm.permKey" placeholder="请输入权限标识" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="权限类型">
              <a-select v-model:value="searchForm.type" placeholder="请选择权限类型" style="width: 100%" allow-clear>
                <a-select-option :value="1">系统权限</a-select-option>
                <a-select-option :value="2">业务权限</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24" style="text-align: right;">
            <a-space class="mt-4">
              <a-button @click="handleReset" class="icon-align-button">重置</a-button>
              <a-button type="primary" @click="handleSearch" class="icon-align-button">
                <template #icon><Icon icon="material-symbols:search" /></template>
                搜索
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <!-- 权限列表 -->
    <a-card>
      <a-table
        :columns="columns"
        :data-source="permissions"
        :row-key="record => record.id"
        :pagination="{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showSizeChanger: true,
          showTotal: total => `共 ${total} 条记录`
        }"
        @change="handleTableChange"
        :loading="tableLoading"
      >
        <template #headerCell="{ column }">
          <template v-if="column.key === 'selection'">
            <a-checkbox v-model:checked="selectAll" @change="handleSelectAll" />
          </template>
        </template>

        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'selection'">
            <a-checkbox v-model:checked="record.selected" />
          </template>

          <template v-if="column.key === 'name'">
            <div class="flex items-center">
              <Icon icon="mdi:shield-key" class="mr-2 text-gray-600" style="font-size: 18px;" />
              <span>{{ record.name }}</span>
            </div>
          </template>

          <template v-if="column.key === 'type'">
            <a-tag :color="record.type === 1 ? 'purple' : 'cyan'">
              {{ getTypeText(record.type) }}
            </a-tag>
          </template>

          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleEdit(record)" class="icon-align-button">
                <template #icon>
                  <Icon icon="material-symbols:edit" />
                </template>
                编辑
              </a-button>

              <a-popconfirm title="确定要删除此权限吗?" ok-text="确定" cancel-text="取消" @confirm="handleDelete(record)">
                <a-button type="link" danger size="small" class="icon-align-button">
                  <template #icon>
                    <Icon icon="material-symbols:delete" />
                  </template>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 添加/编辑权限对话框 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      width="600px"
    >
      <a-form
        :model="formData"
        :rules="rules"
        ref="formRef"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
      >
        <a-form-item label="权限名称" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入权限名称" />
        </a-form-item>
        <a-form-item label="权限标识" name="permKey">
          <a-input v-model:value="formData.permKey" placeholder="请输入权限标识" />
          <div class="text-xs text-gray-500 mt-1">例如：system:user:add</div>
        </a-form-item>
        <a-form-item label="权限类型" name="type">
          <a-select v-model:value="formData.type" placeholder="请选择权限类型" style="width: 100%">
            <a-select-option :value="1">系统权限</a-select-option>
            <a-select-option :value="2">业务权限</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="权限描述" name="description">
          <a-textarea v-model:value="formData.description" placeholder="请输入权限描述" :rows="4" />
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button @click="handleModalCancel">取消</a-button>
        <a-button type="primary" @click="handleModalOk" :loading="submitLoading">确定</a-button>
      </template>
    </a-modal>

    <!-- 批量操作对话框 -->
    <a-modal
      v-model:visible="batchModalVisible"
      title="批量操作"
      @cancel="handleBatchModalCancel"
      :footer="null"
      width="500px"
    >
      <div class="flex flex-col gap-4">
        <a-button danger @click="handleBatchDelete" class="icon-align-button">
          <template #icon>
            <Icon icon="material-symbols:delete" />
          </template>
          批量删除
        </a-button>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { Icon } from '@iconify/vue';
import { message } from 'ant-design-vue';
// 导入API，后续接入API时使用
import { 
  getPermissionPage, 
  getPermission, 
  addPermission, 
  updatePermission, 
  deletePermission, 
  batchDeletePermissions 
} from '@/api/permission';

// 权限统计数据
const stats = reactive({
  total: 0,
  system: 0,
  business: 0
});

// 搜索表单
const searchForm = reactive({
  name: '',
  permKey: '',
  type: undefined
});

// 分页信息
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
});

// 表格加载状态
const tableLoading = ref(false);

// 全选状态
const selectAll = ref(false);

// 表格列配置
const columns = [
  {
    title: '',
    key: 'selection',
    width: '40px'
  },
  {
    title: '权限名称',
    dataIndex: 'name',
    key: 'name',
    ellipsis: true
  },
  {
    title: '权限标识',
    dataIndex: 'permKey',
    key: 'permKey',
    ellipsis: true
  },
  {
    title: '权限类型',
    dataIndex: 'type',
    key: 'type',
    width: '100px'
  },
  {
    title: '权限描述',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true
  },
  {
    title: '操作',
    key: 'action',
    width: '180px'
  }
];

// 模拟权限数据
const permissions = ref([]);

// 对话框相关
const modalVisible = ref(false);
const modalTitle = computed(() => isEdit.value ? '编辑权限' : '新增权限');
const isEdit = ref(false);
const submitLoading = ref(false);

// 表单数据
const formRef = ref(null);
const formData = reactive({
  id: undefined,
  name: '',
  permKey: '',
  type: 1,
  description: ''
});

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入权限名称', trigger: 'blur' },
    { max: 50, message: '权限名称不能超过50个字符', trigger: 'blur' }
  ],
  permKey: [
    { required: true, message: '请输入权限标识', trigger: 'blur' },
    { max: 100, message: '权限标识不能超过100个字符', trigger: 'blur' },
    { pattern: /^[a-z][\w.:]*$/, message: '权限标识格式不正确', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择权限类型', trigger: 'change' }
  ]
};

// 批量操作对话框
const batchModalVisible = ref(false);

// 初始化
onMounted(() => {
  fetchPermissions();
});

// 获取权限类型文本
const getTypeText = (type) => {
  const typeMap = {
    1: '系统权限',
    2: '业务权限'
  };
  return typeMap[type] || '未知类型';
};

// 获取权限列表
const fetchPermissions = async () => {
  try {
    tableLoading.value = true;
    const response = await getPermissionPage({
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      ...searchForm
    });
    
    // 根据后端API的实际返回结构调整
    pagination.total = response.data.total || 0;
    permissions.value = (response.data.records || []).map(item => ({
      ...item,
      selected: false
    }));
    
    // 更新统计数据
    updateStats();
    
    tableLoading.value = false;
  } catch (error) {
    console.error('获取权限列表失败:', error);
    message.error('获取权限列表失败');
    tableLoading.value = false;
  }
};

// 更新统计数据
const updateStats = () => {
  stats.total = permissions.value.length;
  stats.system = permissions.value.filter(item => item.type === 1).length;
  stats.business = permissions.value.filter(item => item.type === 2).length;
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchPermissions();
};

// 重置搜索条件
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = '';
  });
  searchForm.type = undefined;
  pagination.current = 1;
  fetchPermissions();
};

// 表格变化
const handleTableChange = (pag) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchPermissions();
};

// 全选/取消全选
const handleSelectAll = (e) => {
  const checked = e.target.checked;
  permissions.value.forEach(item => {
    item.selected = checked;
  });
};

// 添加权限
const handleAddPermission = () => {
  isEdit.value = false;
  formData.id = undefined;
  formData.name = '';
  formData.permKey = '';
  formData.type = 1;
  formData.description = '';
  modalVisible.value = true;
  
  // 等待DOM更新后重置表单验证状态
  setTimeout(() => {
    formRef.value?.resetFields();
  }, 0);
};

// 编辑权限
const handleEdit = (record) => {
  isEdit.value = true;
  Object.keys(formData).forEach(key => {
    formData[key] = record[key];
  });
  modalVisible.value = true;
  
  // 等待DOM更新后重置表单验证状态
  setTimeout(() => {
    formRef.value?.resetFields();
  }, 0);
};

// 删除权限
const handleDelete = async (record) => {
  try {
    await deletePermission(record.id);
    message.success('删除成功');
    
    // 重新加载权限列表
    fetchPermissions();
  } catch (error) {
    console.error('删除失败:', error);
    message.error('删除失败: ' + (error.message || '未知错误'));
  }
};

// 批量操作按钮点击
const handleBatchOperation = () => {
  const selectedCount = permissions.value.filter(item => item.selected).length;
  if (selectedCount === 0) {
    message.warning('请至少选择一条记录');
    return;
  }
  batchModalVisible.value = true;
};

// 批量删除
const handleBatchDelete = async () => {
  const selectedIds = permissions.value.filter(item => item.selected).map(item => item.id);
  if (selectedIds.length === 0) {
    message.warning('请至少选择一条记录');
    return;
  }

  try {
    await batchDeletePermissions(selectedIds);
    message.success('批量删除成功');
    
    // 关闭对话框
    batchModalVisible.value = false;
    
    // 重新加载权限列表
    fetchPermissions();
  } catch (error) {
    console.error('批量删除失败:', error);
    message.error('批量删除失败: ' + (error.message || '未知错误'));
  }
};

// 对话框确认
const handleModalOk = async () => {
  try {
    await formRef.value.validate();
    
    submitLoading.value = true;
    
    if (isEdit.value) {
      await updatePermission(formData);
      message.success('更新成功');
    } else {
      await addPermission(formData);
      message.success('添加成功');
    }
    
    // 关闭对话框
    modalVisible.value = false;
    submitLoading.value = false;
    
    // 重新加载权限列表
    fetchPermissions();
  } catch (error) {
    console.error('操作失败:', error);
    message.error('操作失败: ' + (error.message || '未知错误'));
    submitLoading.value = false;
  }
};

// 对话框取消
const handleModalCancel = () => {
  modalVisible.value = false;
};

// 批量操作对话框取消
const handleBatchModalCancel = () => {
  batchModalVisible.value = false;
};
</script>

<style scoped>
.icon-align-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.icon-align-button :deep(.anticon) {
  display: flex;
  align-items: center;
}

.icon-align-button :deep(.ant-btn-icon) {
  display: flex;
  align-items: center;
}
</style> 