<template>
  <div class="roles-view">
    <!-- 页面标题和操作按钮 -->
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-semibold text-gray-800">角色管理</h1>
      <div class="flex gap-4">
        <a-button type="primary" @click="handleAddRole" class="icon-align-button">
          <template #icon>
            <Icon icon="material-symbols:add" />
          </template>
          新增角色
        </a-button>
        <a-button @click="handleBatchOperation" class="icon-align-button">
          <template #icon>
            <Icon icon="material-symbols:published-with-changes" />
          </template>
          批量操作
        </a-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <a-row :gutter="16" class="mb-6">
      <a-col :span="8">
        <a-card>
          <a-statistic title="角色总数" :value="stats.total" :value-style="{ color: '#1890ff' }">
            <template #prefix>
              <Icon icon="mdi:shield-account" class="mr-1" />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="8">
        <a-card>
          <a-statistic title="启用角色" :value="stats.enabled" :value-style="{ color: '#52c41a' }">
            <template #prefix>
              <Icon icon="mdi:check-circle" class="mr-1" />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="8">
        <a-card>
          <a-statistic title="禁用角色" :value="stats.disabled" :value-style="{ color: '#f5222d' }">
            <template #prefix>
              <Icon icon="mdi:close-circle" class="mr-1" />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>

    <!-- 搜索和筛选区域 -->
    <a-card class="mb-6">
      <a-form layout="inline" :model="searchForm">
        <a-row :gutter="16" style="width: 100%;">
          <a-col :span="6">
            <a-form-item label="角色名称">
              <a-input v-model:value="searchForm.name" placeholder="请输入角色名称" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="角色编码">
              <a-input v-model:value="searchForm.code" placeholder="请输入角色编码" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="状态">
              <a-select v-model:value="searchForm.status" placeholder="请选择状态" style="width: 100%" allow-clear>
                <a-select-option value="1">启用</a-select-option>
                <a-select-option value="0">禁用</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24" style="text-align: right;">
            <a-space>
              <a-button @click="handleReset" class="icon-align-button">重置</a-button>
              <a-button type="primary" @click="handleSearch" class="icon-align-button">
                <template #icon>
                  <Icon icon="material-symbols:search" />
                </template>
                搜索
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <!-- 角色列表 -->
    <a-card>
      <a-table :columns="columns" :data-source="roles" :row-key="record => record.id" :pagination="{
        current: pagination.current,
        pageSize: pagination.pageSize,
        total: pagination.total,
        showSizeChanger: true,
        showTotal: total => `共 ${total} 条记录`
      }" @change="handleTableChange" :loading="tableLoading">
        <template #headerCell="{ column }">
          <template v-if="column.key === 'selection'">
            <a-checkbox v-model:checked="selectAll" @change="handleSelectAll" />
          </template>
        </template>

        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'selection'">
            <a-checkbox v-model:checked="record.selected" />
          </template>

          <template v-if="column.key === 'name'">
            <div class="flex items-center">
              <Icon icon="mdi:shield-account" class="mr-2 text-gray-600" style="font-size: 18px;" />
              <span>{{ record.roleName }}</span>
            </div>
          </template>

          <template v-if="column.key === 'status'">
            <a-tag :color="record.status === 1 ? 'success' : 'error'">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleEdit(record)" class="icon-align-button">
                <template #icon>
                  <Icon icon="material-symbols:edit" />
                </template>
                编辑
              </a-button>

              <a-button type="link" size="small" @click="handlePermission(record)" class="icon-align-button">
                <template #icon>
                  <Icon icon="material-symbols:tune" />
                </template>
                权限配置
              </a-button>

              <a-button type="link" size="small" @click="handleMenuConfig(record)" class="icon-align-button">
                <template #icon>
                  <Icon icon="material-symbols:menu" />
                </template>
                菜单配置
              </a-button>

              <a-button type="link" size="small" @click="handleToggleStatus(record)" class="icon-align-button">
                <template #icon>
                  <Icon :icon="record.status === 1 ? 'material-symbols:unpublished' : 'material-symbols:publish'" />
                </template>
                {{ record.status === 1 ? '禁用' : '启用' }}
              </a-button>

              <a-popconfirm title="确定要删除此角色吗?" ok-text="确定" cancel-text="取消" @confirm="handleDelete(record)">
                <a-button type="link" danger size="small" class="icon-align-button">
                  <template #icon>
                    <Icon icon="material-symbols:delete" />
                  </template>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 添加/编辑角色对话框 -->
    <a-modal v-model:visible="modalVisible" :title="modalTitle" @ok="handleModalOk" @cancel="handleModalCancel"
      width="600px">
      <a-form :model="formData" :rules="rules" ref="formRef" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-form-item label="角色名称" name="roleName">
          <a-input v-model:value="formData.roleName" placeholder="请输入角色名称" />
        </a-form-item>
        <a-form-item label="角色编码" name="roleCode">
          <a-input v-model:value="formData.roleCode" placeholder="请输入角色编码" />
        </a-form-item>
        <a-form-item label="角色描述" name="description">
          <a-textarea v-model:value="formData.description" placeholder="请输入角色描述" :rows="4" />
        </a-form-item>
        <a-form-item label="状态" name="status">
          <a-switch v-model:checked="statusChecked" checked-children="启用" un-checked-children="禁用" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 权限配置对话框 -->
    <a-modal v-model:visible="permissionModalVisible" title="权限配置" @ok="handlePermissionOk"
      @cancel="handlePermissionCancel" width="800px">
      <div v-if="currentRole">
        <h3 class="mb-4">为 "{{ currentRole.roleName }}" 配置权限</h3>
        <div v-if="permissionTree.length === 0" class="text-center my-4">
          <p class="text-gray-500">暂无权限数据，请确保权限数据已正确加载</p>
        </div>
        <a-spin :spinning="permissionLoading">
          <a-tree v-model:checkedKeys="checkedPermissions" :treeData="permissionTree" checkable checkStrictly
            :defaultExpandAll="true" />
        </a-spin>
      </div>
      <template #footer>
        <a-button @click="handlePermissionCancel">取消</a-button>
        <a-button type="primary" @click="handlePermissionOk" :loading="permissionSubmitLoading">确定</a-button>
      </template>
    </a-modal>

    <!-- 菜单配置对话框 -->
    <a-modal v-model:visible="menuModalVisible" title="菜单配置" @ok="handleMenuOk" @cancel="handleMenuCancel"
      width="800px">
      <div v-if="currentRole">
        <h3 class="mb-4">为 "{{ currentRole.roleName }}" 配置菜单</h3>
        <div v-if="menuTree.length === 0" class="text-center my-4">
          <p class="text-gray-500">暂无菜单数据，请确保菜单数据已正确加载</p>
        </div>
        <a-spin :spinning="menuLoading">
          <a-tree v-model:checkedKeys="checkedMenus" :treeData="menuTree" checkable :defaultExpandAll="true"
            @check="handleMenuCheck" />
        </a-spin>
      </div>
      <template #footer>
        <a-button @click="handleMenuCancel">取消</a-button>
        <a-button type="primary" @click="handleMenuOk" :loading="menuSubmitLoading">确定</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { Icon } from '@iconify/vue';
import { message } from 'ant-design-vue';
import { getRolePage, getRoleList, addRole, updateRole, deleteRole, batchDeleteRoles } from '@/api/role';
import { getPermissionsByRoleId, assignPermissions, getPermissionList } from '@/api/permission';
import { getMenuTree, assignMenusToRole, getRoleMenuIds } from '@/api/menu';

// 角色统计数据
const stats = reactive({
  total: 0,
  enabled: 0,
  disabled: 0
});

// 搜索表单
const searchForm = reactive({
  keyword: '',
});

// 分页信息
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
});

// 表格相关
const columns = [
  {
    title: '',
    key: 'selection',
    width: '40px'
  },
  {
    title: '角色名称',
    dataIndex: 'roleName',
    key: 'name',
    ellipsis: true
  },
  {
    title: '角色编码',
    dataIndex: 'roleCode',
    key: 'code',
    ellipsis: true
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: '80px'
  },
  {
    title: '操作',
    key: 'action',
    width: '360px'
  }
];

// 角色数据
const roles = ref([]);

// 表格状态
const tableLoading = ref(false);
const selectAll = ref(false);

const roleList = ref([]);
// 表单相关
const modalVisible = ref(false);
const modalTitle = ref('新增角色');
const isEdit = ref(false);
const formRef = ref(null);
const formData = reactive({
  id: null,
  roleName: '',
  roleCode: '',
  description: '',
  status: 1
});

const rules = {
  roleName: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
  roleCode: [{ required: true, message: '请输入角色编码', trigger: 'blur' }]
};

// 权限配置相关
const permissionModalVisible = ref(false);
const currentRole = ref(null);
const checkedPermissions = ref([]);
const permissionLoading = ref(false);
const permissionSubmitLoading = ref(false);
const permissionMap = reactive(new Map()); // 用于保存权限key和id的映射关系
const allPermissions = ref([]); // 存储所有权限
const permissionTree = ref([]); // 权限树数据，改为响应式数组

// 菜单配置相关
const menuModalVisible = ref(false);
const checkedMenus = ref([]);
const menuLoading = ref(false);
const menuSubmitLoading = ref(false);

const menuTree = ref([]); // 菜单树数据

// 状态开关的计算属性
const statusChecked = computed({
  get: () => formData.status === 1,
  set: (val) => {
    formData.status = val ? 1 : 0;
  }
});

// 生命周期
onMounted(async () => {
  loadRoles();
  loadRoleList();

  // 确保权限树加载
  try {
    await loadAllPermissions();
    console.log('初始化权限树完成');
  } catch (error) {
    console.error('初始化权限树失败', error);
    message.error('初始化权限数据失败，可能影响权限配置功能');
  }

  // 确保菜单树加载
  try {
    await loadAllMenus();
    console.log('初始化菜单树完成');
  } catch (error) {
    console.error('初始化菜单树失败', error);
    message.error('初始化菜单数据失败，可能影响菜单配置功能');
  }
});

// 方法
const loadRoles = () => {
  tableLoading.value = true;

  const params = {
    pageNum: pagination.current,
    pageSize: pagination.pageSize,
    keyword: searchForm.keyword
  };

  getRolePage(params).then(res => {
    roles.value = res.data.records.map(role => {
      return {
        ...role,
        selected: false
      };
    });

    pagination.total = res.total;

    // 更新统计信息
    updateStats();
  }).catch(error => {
    console.error('加载角色列表失败', error);
    message.error('加载角色列表失败');
  }).finally(() => {
    tableLoading.value = false;
  });
};

const loadRoleList = () => {
  getRoleList().then(res => {
    roleList.value = res;
  }).catch(error => {
    console.error('获取角色列表失败', error);
    message.error('获取角色列表失败');
  });
};

// 更新统计信息
const updateStats = () => {
  stats.total = roles.value.length;
  stats.enabled = roles.value.filter(role => role.status === 1).length;
  stats.disabled = roles.value.filter(role => role.status === 0).length;
};

const handleSearch = () => {
  pagination.current = 1;
  loadRoles();
};

const handleReset = () => {
  searchForm.keyword = '';
  pagination.current = 1;
  loadRoles();
};

const handleSelectAll = (e) => {
  roles.value.forEach(role => {
    role.selected = e.target.checked;
  });
};

const handleTableChange = (pag) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  loadRoles();
};

const handleAddRole = () => {
  resetForm();
  modalTitle.value = '新增角色';
  isEdit.value = false;
  modalVisible.value = true;
};

const handleEdit = (record) => {
  resetForm();
  modalTitle.value = '编辑角色';
  isEdit.value = true;
  formData.id = record.id;
  formData.roleName = record.roleName;
  formData.roleCode = record.roleCode;
  formData.description = record.description;
  formData.status = record.status;
  modalVisible.value = true;
};

const resetForm = () => {
  formData.id = null;
  formData.roleName = '';
  formData.roleCode = '';
  formData.description = '';
  formData.status = 1;

  if (formRef.value) {
    formRef.value.resetFields();
  }
};

const handleModalOk = () => {
  formRef.value.validate().then(() => {
    // 提交表单
    if (isEdit.value) {
      // 编辑角色
      updateRole(formData).then(() => {
        message.success('角色更新成功');
        modalVisible.value = false;
        loadRoles();
      }).catch(error => {
        console.error('更新角色失败', error);
        message.error('更新角色失败');
      });
    } else {
      // 新增角色
      addRole(formData).then(() => {
        message.success('角色添加成功');
        modalVisible.value = false;
        loadRoles();
      }).catch(error => {
        console.error('添加角色失败', error);
        message.error('添加角色失败');
      });
    }
  }).catch(error => {
    console.log('表单验证失败', error);
  });
};

const handleModalCancel = () => {
  modalVisible.value = false;
};

const handleToggleStatus = (record) => {
  const newStatus = record.status === 1 ? 0 : 1;

  // 准备更新对象
  const updateData = {
    id: record.id,
    status: newStatus
  };

  updateRole(updateData).then(() => {
    message.success(`角色状态已${newStatus === 1 ? '启用' : '禁用'}`);
    // 更新本地数据
    record.status = newStatus;
    updateStats();
  }).catch(error => {
    console.error('更新角色状态失败', error);
    message.error('更新角色状态失败');
  });
};

const handleDelete = (record) => {
  deleteRole(record.id).then(() => {
    message.success('角色删除成功');
    loadRoles();
  }).catch(error => {
    console.error('删除角色失败', error);
    message.error('删除角色失败');
  });
};

const handlePermission = (record) => {
  console.log('点击权限配置按钮，角色:', record);
  currentRole.value = record;
  permissionLoading.value = true;
  permissionModalVisible.value = true;

  // 首先检查权限树是否已加载
  console.log('当前权限树状态, 长度:', permissionTree.value.length);
  if (permissionTree.value.length === 0) {
    console.log('权限树为空，开始加载所有权限');
    // 如果权限树为空，先加载所有权限
    loadAllPermissions().then(() => {
      console.log('权限树加载完成，开始加载角色权限');
      // 权限树加载完成后，获取角色权限
      loadRolePermissions(record.id);
    }).catch(() => {
      permissionLoading.value = false;
    });
  } else {
    console.log('权限树已加载，直接加载角色权限');
    loadRolePermissions(record.id);
  }
};

// 加载角色权限
const loadRolePermissions = (roleId) => {
  console.log('开始加载角色权限, 角色ID:', roleId);
  // 获取角色的权限列表
  getPermissionsByRoleId(roleId)
    .then(res => {
      console.log('获取角色权限成功，数据:', res);

      // 提取权限标识列表
      const permKeyList = res.data.map(item => item.permKey);
      console.log('权限标识列表:', permKeyList);

      // 设置已选中的权限
      // 在checkStrictly模式下，需要设置为 {checked: [], halfChecked: []}
      checkedPermissions.value = {
        checked: permKeyList,
        halfChecked: []
      };
      console.log('设置选中权限为:', checkedPermissions.value);
    })
    .catch(error => {
      console.error('获取角色权限失败', error);
      message.error('获取角色权限失败');
    })
    .finally(() => {
      permissionLoading.value = false;
    });
};

const handlePermissionOk = () => {
  if (!currentRole.value) {
    return;
  }

  permissionSubmitLoading.value = true;

  // 获取所有选中的权限ids
  const permissionIds = [];

  console.log('选中的权限数据:', checkedPermissions.value);

  // 在checkStrictly模式下，checkedPermissions.value可能是一个对象 {checked: [], halfChecked: []}
  const checkedKeys = Array.isArray(checkedPermissions.value)
    ? checkedPermissions.value  // 如果是数组，直接使用
    : (checkedPermissions.value.checked || []); // 如果是对象，使用checked属性

  console.log('处理后的选中权限:', checkedKeys);

  // 遍历选中的权限key，根据permissionMap找到对应的权限id
  checkedKeys.forEach(permKey => {
    // 在allPermissions中查找权限
    const permission = allPermissions.value.find(p => p.permKey === permKey);
    if (permission) {
      permissionIds.push(permission.id);
    }
  });

  console.log('最终提交的权限IDs:', permissionIds);

  // 调用API保存权限配置
  assignPermissions(currentRole.value.id, permissionIds)
    .then(() => {
      message.success('权限配置保存成功');
      permissionModalVisible.value = false;
    })
    .catch(error => {
      console.error('保存权限配置失败', error);
      message.error('保存权限配置失败');
    })
    .finally(() => {
      permissionSubmitLoading.value = false;
    });
};

const handlePermissionCancel = () => {
  permissionModalVisible.value = false;
  currentRole.value = null;
  checkedPermissions.value = [];
};

// 菜单配置相关方法
const handleMenuConfig = (record) => {
  console.log('点击菜单配置按钮，角色:', record);
  currentRole.value = record;
  menuLoading.value = true;
  menuModalVisible.value = true;

  // 首先检查菜单树是否已加载
  console.log('当前菜单树状态, 长度:', menuTree.value.length);
  if (menuTree.value.length === 0) {
    console.log('菜单树为空，开始加载所有菜单');
    // 如果菜单树为空，先加载所有菜单
    loadAllMenus().then(() => {
      console.log('菜单树加载完成，开始加载角色菜单');
      // 菜单树加载完成后，获取角色菜单
      loadRoleMenus(record.id);
    }).catch(() => {
      menuLoading.value = false;
    });
  } else {
    console.log('菜单树已加载，直接加载角色菜单');
    loadRoleMenus(record.id);
  }
};

// 加载角色菜单
const loadRoleMenus = (roleId) => {
  console.log('开始加载角色菜单, 角色ID:', roleId);
  // 获取角色的菜单ID列表
  getRoleMenuIds(roleId)
    .then(res => {
      console.log('获取角色菜单成功，数据:', res);

      // 设置已选中的菜单
      // 移除checkStrictly后，直接使用数组格式
      checkedMenus.value = res.data || [];
      console.log('设置选中菜单为:', checkedMenus.value);
    })
    .catch(error => {
      console.error('获取角色菜单失败', error);
      message.error('获取角色菜单失败');
    })
    .finally(() => {
      menuLoading.value = false;
    });
};

const handleMenuOk = () => {
  if (!currentRole.value) {
    return;
  }

  menuSubmitLoading.value = true;

  // 获取所有选中的菜单ids
  console.log('选中的菜单数据:', checkedMenus.value);

  // 移除checkStrictly后，checkedMenus.value直接是数组格式
  const checkedKeys = Array.isArray(checkedMenus.value) ? checkedMenus.value : [];

  console.log('处理后的选中菜单:', checkedKeys);

  // 调用API保存菜单配置
  assignMenusToRole(currentRole.value.id, checkedKeys)
    .then(() => {
      message.success('菜单配置保存成功');
      menuModalVisible.value = false;
    })
    .catch(error => {
      console.error('保存菜单配置失败', error);
      message.error('保存菜单配置失败');
    })
    .finally(() => {
      menuSubmitLoading.value = false;
    });
};

const handleMenuCancel = () => {
  menuModalVisible.value = false;
  currentRole.value = null;
  checkedMenus.value = [];
};

// 处理菜单选择事件
const handleMenuCheck = (checkedKeys, info) => {
  console.log('菜单选择变化:', checkedKeys, info);
  // 在非严格模式下，ant-design-vue会自动处理父子联动
  // 这里可以添加额外的业务逻辑，比如记录选择状态等
};

const handleBatchOperation = () => {
  const selectedRoles = roles.value.filter(role => role.selected);
  if (selectedRoles.length === 0) {
    message.warning('请至少选择一个角色');
    return;
  }

  // 获取选中角色的ID列表
  const ids = selectedRoles.map(role => role.id);

  // 批量删除
  batchDeleteRoles(ids).then(() => {
    message.success('批量删除成功');
    loadRoles();
  }).catch(error => {
    console.error('批量删除失败', error);
    message.error('批量删除失败');
  });
};

// 辅助方法
const getStatusText = (status) => {
  return status === 1 ? '启用' : '禁用';
};

// 加载所有权限
const loadAllPermissions = () => {
  console.log('开始加载所有权限');
  return new Promise((resolve, reject) => {
    getPermissionList()
      .then(res => {
        console.log('获取权限列表成功，数据:', res);
        allPermissions.value = res.data;

        // 构建权限id和permKey的映射关系
        permissionMap.clear();
        allPermissions.value.forEach(item => {
          permissionMap.set(item.permKey, item.id);
        });

        // 根据permKey前缀动态构建权限树
        generatePermissionTree();

        resolve();
      })
      .catch(error => {
        console.error('获取权限列表失败', error);
        message.error('获取权限列表失败');
        reject(error);
      });
  });
};

// 加载所有菜单
const loadAllMenus = () => {
  console.log('开始加载所有菜单');
  return new Promise((resolve, reject) => {
    getMenuTree()
      .then(res => {
        console.log('获取菜单树成功，数据:', res);

        // 转换菜单树数据格式，使其适配ant-design-vue的Tree组件
        menuTree.value = convertMenuTreeForAntd(res.data);

        resolve();
      })
      .catch(error => {
        console.error('获取菜单树失败', error);
        message.error('获取菜单树失败');
        reject(error);
      });
  });
};

// 转换菜单树数据格式，适配ant-design-vue的Tree组件
const convertMenuTreeForAntd = (menuList) => {
  if (!menuList || !Array.isArray(menuList)) {
    return [];
  }

  return menuList.map(menu => {
    const treeNode = {
      title: menu.name,
      key: menu.id,
      id: menu.id,
      checkable: true
    };

    // 如果有子菜单，递归处理
    if (menu.children && menu.children.length > 0) {
      treeNode.children = convertMenuTreeForAntd(menu.children);
    }

    return treeNode;
  });
};

// 根据permKey前缀动态生成权限树
const generatePermissionTree = () => {
  console.log('开始生成权限树，权限总数:', allPermissions.value.length);

  // 创建一个映射来存储权限类别
  const categoryMap = new Map();

  // 先创建一个系统分类
  categoryMap.set('system', {
    title: '系统',
    key: 'system',
    children: [],
    checkable: false // 分类节点不可选
  });

  // 遍历所有权限，按前缀分组
  allPermissions.value.forEach(permission => {
    // 获取权限前缀（第一个冒号前的部分）
    let prefix = permission.permKey;

    // 特殊处理"*"权限，将其放入系统分类
    if (permission.permKey === '*') {
      // 将上帝权限添加到系统分类下
      categoryMap.get('system').children.push({
        title: permission.name,
        key: permission.permKey,
        id: permission.id,
        checkable: true // 具体权限项可选
      });
      return; // 跳过后续处理
    }

    if (permission.permKey.includes(':')) {
      prefix = permission.permKey.split(':')[0];
    }

    console.log('处理权限:', permission.name, '标识:', permission.permKey, '前缀:', prefix);

    if (!categoryMap.has(prefix)) {
      categoryMap.set(prefix, {
        title: getCategoryTitle(prefix),
        key: prefix,
        children: [],
        checkable: false // 分类节点不可选
      });
    }

    // 添加权限到对应分类
    categoryMap.get(prefix).children.push({
      title: permission.name,
      key: permission.permKey,
      id: permission.id,
      checkable: true // 具体权限项可选
    });
  });

  // 如果系统分类没有子项，则移除它
  if (categoryMap.get('system').children.length === 0) {
    categoryMap.delete('system');
  }

  // 将Map转换为数组
  permissionTree.value.length = 0; // 清空原有树
  categoryMap.forEach(category => {
    permissionTree.value.push(category);
  });

  console.log('权限树生成完成，分类数:', permissionTree.value.length);
  console.log('权限树数据:', JSON.stringify(permissionTree.value));
};

// 根据前缀获取权限分类标题
const getCategoryTitle = (prefix) => {
  const titleMap = {
    'admin': '管理员管理',
    'user': '用户管理',
    'role': '角色管理',
    'permission': '权限管理',
    'menu': '菜单管理',
    'org': '组织管理',
    'category': '分类管理',
    'system': '系统管理',
    '*': '系统管理'
  };

  return titleMap[prefix] || prefix + '管理';
};


</script>

<style scoped>
.roles-view {
  padding: 24px;
}

.icon-align-button {
  display: flex;
  align-items: center;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-statistic-title) {
  font-size: 14px;
}

:deep(.ant-table-wrapper) {
  margin-top: 16px;
}
</style>