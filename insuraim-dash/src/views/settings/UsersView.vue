<template>
  <div class="users-view">
    <!-- 页面标题和操作按钮 -->
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-semibold text-gray-800">管理员管理</h1>
      <div class="flex gap-4">
        <a-button type="primary" @click="handleAddUser" class="icon-align-button">
          <template #icon>
            <Icon icon="material-symbols:add" />
          </template>
          新增管理员
        </a-button>
        <a-button @click="handleBatchOperation" class="icon-align-button">
          <template #icon>
            <Icon icon="material-symbols:published-with-changes" />
          </template>
          批量操作
        </a-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <a-row :gutter="16" class="mb-6">
      <a-col :span="8">
        <a-card>
          <a-statistic title="管理员总数" :value="stats.total" :value-style="{ color: '#1890ff' }">
            <template #prefix>
              <Icon icon="mdi:account-group" class="mr-1" />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="8">
        <a-card>
          <a-statistic title="启用管理员" :value="stats.enabled" :value-style="{ color: '#52c41a' }">
            <template #prefix>
              <Icon icon="mdi:check-circle" class="mr-1" />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      <a-col :span="8">
        <a-card>
          <a-statistic title="禁用管理员" :value="stats.disabled" :value-style="{ color: '#f5222d' }">
            <template #prefix>
              <Icon icon="mdi:close-circle" class="mr-1" />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>

    <!-- 搜索和筛选区域 -->
    <a-card class="mb-6">
      <a-form layout="inline" :model="searchForm">
        <a-row :gutter="16" style="width: 100%;">
          <a-col :span="6">
            <a-form-item label="用户名">
              <a-input v-model:value="searchForm.username" placeholder="请输入用户名" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="邮箱">
              <a-input v-model:value="searchForm.email" placeholder="请输入邮箱" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="状态">
              <a-select v-model:value="searchForm.status" placeholder="请选择状态" style="width: 100%" allow-clear>
                <a-select-option value="1">启用</a-select-option>
                <a-select-option value="0">禁用</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24" style="text-align: right;">
            <a-space>
              <a-button @click="handleReset" class="icon-align-button">重置</a-button>
              <a-button type="primary" @click="handleSearch" class="icon-align-button">
                <template #icon>
                  <Icon icon="material-symbols:search" />
                </template>
                搜索
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <!-- 管理员列表 -->
    <a-card>
      <a-table :columns="columns" :data-source="users" :row-key="record => record.id" :pagination="{
        current: pagination.current,
        pageSize: pagination.pageSize,
        total: pagination.total,
        showSizeChanger: true,
        showTotal: total => `共 ${total} 条记录`
      }" @change="handleTableChange" :loading="tableLoading">
        <template #headerCell="{ column }">
          <template v-if="column.key === 'selection'">
            <a-checkbox v-model:checked="selectAll" @change="handleSelectAll" />
          </template>
        </template>

        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'selection'">
            <a-checkbox v-model:checked="record.selected" />
          </template>

          <template v-if="column.key === 'username'">
            <div class="flex items-center">
              <Icon icon="mdi:account" class="mr-2 text-gray-600" style="font-size: 18px;" />
              <span>{{ record.username }}</span>
            </div>
          </template>

          <template v-if="column.key === 'avatar'">
            <div class="flex items-center justify-start">
              <a-avatar :src="record.avatar" :alt="record.username + '的头像'" :size="40" shape="circle"
                @error="handleAvatarError">
                <template #icon>
                  <Icon icon="mdi:account" />
                </template>
              </a-avatar>
            </div>
          </template>

          <template v-if="column.key === 'status'">
            <a-tag :color="record.status === 1 ? 'success' : 'error'">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleEdit(record)" class="icon-align-button">
                <template #icon>
                  <Icon icon="material-symbols:edit" />
                </template>
                编辑
              </a-button>

              <a-button type="link" size="small" @click="handleResetPassword(record)" class="icon-align-button">
                <template #icon>
                  <Icon icon="material-symbols:lock-reset" />
                </template>
                重置密码
              </a-button>

              <a-button type="link" size="small" @click="handleToggleStatus(record)" class="icon-align-button">
                <template #icon>
                  <Icon :icon="record.status === 1 ? 'material-symbols:unpublished' : 'material-symbols:publish'" />
                </template>
                {{ record.status === 1 ? '禁用' : '启用' }}
              </a-button>

              <a-popconfirm title="确定要删除此管理员吗?" ok-text="确定" cancel-text="取消" @confirm="handleDelete(record)">
                <a-button type="link" danger size="small" class="icon-align-button">
                  <template #icon>
                    <Icon icon="material-symbols:delete" />
                  </template>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 添加/编辑管理员对话框 -->
    <a-modal v-model:visible="modalVisible" :title="modalTitle" @ok="handleModalOk" @cancel="handleModalCancel"
      width="600px">
      <a-form :model="formData" :rules="rules" ref="formRef" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-form-item label="用户名" name="username">
          <a-input v-model:value="formData.username" placeholder="请输入用户名" :disabled="isEdit" />
        </a-form-item>
        <a-form-item label="邮箱" name="email">
          <a-input v-model:value="formData.email" placeholder="请输入邮箱" />
        </a-form-item>
        <a-form-item v-if="!isEdit" label="密码" name="password">
          <a-input-password v-model:value="formData.password" placeholder="请输入密码" />
        </a-form-item>
        <a-form-item label="头像URL" name="avatar">
          <a-input v-model:value="formData.avatar" placeholder="请输入头像URL" />
        </a-form-item>
        <a-form-item label="角色" name="role_id">
          <a-select v-model:value="formData.role_id" placeholder="请选择角色" style="width: 100%" allow-clear>
            <a-select-option v-for="role in roleList" :key="role.id" :value="role.id">
              {{ role.roleName }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="状态" name="status">
          <a-switch v-model:checked="statusChecked" checked-children="启用" un-checked-children="禁用" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 重置密码对话框 -->
    <a-modal v-model:visible="passwordModalVisible" title="重置密码" @ok="handlePasswordOk" @cancel="handlePasswordCancel"
      width="400px">
      <p class="mb-4">正在为管理员 <strong>{{ currentUser?.username }}</strong> 重置密码</p>
      <a-form :model="passwordForm" ref="passwordFormRef" :rules="passwordRules">
        <a-form-item label="新密码" name="newPassword">
          <a-input-password v-model:value="passwordForm.newPassword" placeholder="请输入新密码" />
        </a-form-item>
        <a-form-item label="确认密码" name="confirmPassword">
          <a-input-password v-model:value="passwordForm.confirmPassword" placeholder="请再次输入新密码" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { Icon } from '@iconify/vue';
import { message } from 'ant-design-vue';
import {
  getAdminPage,
  getAdmin,
  addAdmin,
  updateAdmin,
  deleteAdmin,
  batchDeleteAdmins,
  updateAdminStatus,
  resetAdminPassword
} from '@/api/admin';
import { getRoleList } from '@/api/role';

// 管理员统计数据
const stats = reactive({
  total: 0,
  enabled: 0,
  disabled: 0
});

// 搜索表单
const searchForm = reactive({
  username: '',
  email: '',
  status: undefined
});

// 分页信息
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
});

// 表格相关
const columns = [
  {
    title: '',
    key: 'selection',
    width: '40px'
  },
  {
    title: '用户名',
    dataIndex: 'username',
    key: 'username',
    ellipsis: true
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    key: 'email',
    ellipsis: true
  },
  {
    title: '头像',
    dataIndex: 'avatar',
    key: 'avatar',
    ellipsis: true,
    width: '80px'
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: '80px'
  },
  {
    title: '操作',
    key: 'action',
    width: '280px'
  }
];

// 管理员数据
const users = ref([]);

// 角色列表
const roleList = ref([]);

// 表格状态
const tableLoading = ref(false);
const selectAll = ref(false);

// 表单相关
const modalVisible = ref(false);
const modalTitle = ref('新增管理员');
const isEdit = ref(false);
const formRef = ref(null);
const formData = reactive({
  id: null,
  username: '',
  email: '',
  password: '',
  avatar: '',
  status: 1,
  role_id: null
});

const rules = {
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  email: [{ required: true, message: '请输入邮箱', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  role_id: [{ required: true, message: '请选择角色', trigger: 'change' }]
};

// 密码重置相关
const passwordModalVisible = ref(false);
const currentUser = ref(null);
const passwordFormRef = ref(null);
const passwordForm = reactive({
  newPassword: '',
  confirmPassword: ''
});

const passwordRules = {
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (_, value) => {
        if (value !== passwordForm.newPassword) {
          return Promise.reject('两次输入的密码不一致');
        }
        return Promise.resolve();
      },
      trigger: 'blur'
    }
  ]
};

// 状态开关的计算属性
const statusChecked = computed({
  get: () => formData.status === 1,
  set: (val) => {
    formData.status = val ? 1 : 0;
  }
});

// 生命周期
onMounted(() => {
  loadUsers();
  loadRoles();
});

// 方法
const loadRoles = () => {
  getRoleList().then(res => {
    roleList.value = res.data || res || [];
  }).catch(error => {
    console.error('加载角色列表失败', error);
    message.error('加载角色列表失败');
  });
};

const loadUsers = () => {
  tableLoading.value = true;

  const params = {
    pageNum: pagination.current,
    pageSize: pagination.pageSize,
    keyword: searchForm.username || searchForm.email || undefined,
    status: searchForm.status
  };

  getAdminPage(params).then(res => {
    users.value = res.data.records.map(admin => {
      return {
        ...admin,
        selected: false
      };
    });

    pagination.total = res.total;

    // 更新统计信息
    updateStats();
  }).catch(error => {
    console.error('加载管理员列表失败', error);
    message.error('加载管理员列表失败');
  }).finally(() => {
    tableLoading.value = false;
  });
};

// 更新统计信息
const updateStats = () => {
  stats.total = users.value.length;
  stats.enabled = users.value.filter(user => user.status === 1).length;
  stats.disabled = users.value.filter(user => user.status === 0).length;

  // 如果使用的是分页，则需要从后端获取总数量的统计信息
  // 这里简化处理，仅统计当前页的数据
};

// 表格事件
const handleTableChange = (pag) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  loadUsers();
};

const handleSelectAll = (e) => {
  const checked = e.target.checked;
  users.value.forEach(user => {
    user.selected = checked;
  });
  selectAll.value = checked;
};

// 搜索和重置
const handleSearch = () => {
  pagination.current = 1;
  loadUsers();
};

const handleReset = () => {
  // 重置搜索表单
  searchForm.username = '';
  searchForm.email = '';
  searchForm.status = undefined;

  // 重新加载数据
  handleSearch();
};

// 管理员操作
const handleAddUser = () => {
  // 先清理表单状态
  if (formRef.value) {
    formRef.value.resetFields();
  }

  // 重置表单数据
  Object.assign(formData, {
    id: null,
    username: '',
    email: '',
    password: '',
    avatar: '',
    status: 1,
    role_id: null
  });

  isEdit.value = false;
  modalTitle.value = '新增管理员';
  modalVisible.value = true;
};

const handleEdit = (record) => {
  // 先清理表单状态
  if (formRef.value) {
    formRef.value.resetFields();
  }

  // 获取最新的管理员数据
  getAdmin(record.id).then(res => {
    // 处理API响应数据结构，可能包装在data属性中
    const adminData = res.data || res;

    // 填充表单数据
    Object.assign(formData, {
      id: adminData.id,
      username: adminData.username,
      email: adminData.email,
      avatar: adminData.avatar,
      status: adminData.status,
      role_id: adminData.role_id
    });

    isEdit.value = true;
    modalTitle.value = '编辑管理员';
    modalVisible.value = true;
  }).catch(error => {
    console.error('获取管理员详情失败', error);
    message.error('获取管理员详情失败:' + error.message);
  });
};

const handleModalOk = () => {
  formRef.value.validate().then(() => {
    // 提交表单
    if (isEdit.value) {
      // 编辑管理员
      updateAdmin(formData).then(() => {
        message.success('管理员更新成功');
        modalVisible.value = false;
        // 清理表单状态
        if (formRef.value) {
          formRef.value.resetFields();
        }
        loadUsers();
      }).catch(error => {
        console.error('更新管理员失败', error);
        message.error('更新管理员失败');
      });
    } else {
      // 新增管理员
      addAdmin(formData).then(() => {
        message.success('管理员添加成功');
        modalVisible.value = false;
        // 清理表单状态
        if (formRef.value) {
          formRef.value.resetFields();
        }
        loadUsers();
      }).catch(error => {
        console.error('添加管理员失败', error);
        message.error('添加管理员失败');
      });
    }
  }).catch(error => {
    console.log('表单验证失败', error);
  });
};

const handleModalCancel = () => {
  modalVisible.value = false;
  // 清理表单状态
  if (formRef.value) {
    formRef.value.resetFields();
  }
};

const handleResetPassword = (record) => {
  currentUser.value = record;
  passwordForm.newPassword = '';
  passwordForm.confirmPassword = '';
  passwordModalVisible.value = true;
};

const handlePasswordOk = () => {
  passwordFormRef.value.validate().then(() => {
    resetAdminPassword(currentUser.value.id, passwordForm.newPassword).then(() => {
      message.success('密码重置成功');
      passwordModalVisible.value = false;
    }).catch(error => {
      console.error('密码重置失败', error);
      message.error('密码重置失败');
    });
  }).catch(error => {
    console.log('表单验证失败', error);
  });
};

const handlePasswordCancel = () => {
  passwordModalVisible.value = false;
};

const handleToggleStatus = (record) => {
  const newStatus = record.status === 1 ? 0 : 1;

  updateAdminStatus(record.id, newStatus).then(() => {
    message.success(`管理员状态已${newStatus === 1 ? '启用' : '禁用'}`);
    // 更新本地数据
    record.status = newStatus;
    updateStats();
  }).catch(error => {
    console.error('更新管理员状态失败', error);
    message.error('更新管理员状态失败');
  });
};

const handleDelete = (record) => {
  deleteAdmin(record.id).then(() => {
    message.success('管理员删除成功');
    loadUsers();
  }).catch(error => {
    console.error('删除管理员失败', error);
    message.error('删除管理员失败');
  });
};

const handleBatchOperation = () => {
  const selectedUsers = users.value.filter(user => user.selected);
  if (selectedUsers.length === 0) {
    message.warning('请至少选择一个管理员');
    return;
  }

  // 获取选中管理员的ID列表
  const ids = selectedUsers.map(user => user.id);

  // 批量删除
  batchDeleteAdmins(ids).then(() => {
    message.success('批量删除成功');
    loadUsers();
  }).catch(error => {
    console.error('批量删除失败', error);
    message.error('批量删除失败');
  });
};

// 辅助方法
const getStatusText = (status) => {
  return status === 1 ? '启用' : '禁用';
};

// 处理头像加载错误
const handleAvatarError = () => {
  // 如果头像加载失败，使用默认图标
  return true; // 返回true表示使用默认图标
};
</script>

<style scoped>
.users-view {
  padding: 24px;
}

.icon-align-button {
  display: flex;
  align-items: center;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-statistic-title) {
  font-size: 14px;
}

:deep(.ant-table-wrapper) {
  margin-top: 16px;
}
</style>