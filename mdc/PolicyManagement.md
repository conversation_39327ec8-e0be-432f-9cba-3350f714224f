## 保单管理需求

### 保单管理订单页面
#### 表格字段
| 字段 | 说明 | 示例 |
|------|------|------|
| （系统）订单号 | 录入年月日+00XX | 202505090001 |
| 投保人姓名 | 中文名<br>大写英文名（小字斜体） | 张三<br>*ZHANG SAN* |
| 被保险人姓名 | 中文名<br>大写英文名（小字斜体） | 李四<br>*LI SI* |
| 地区 | 香港/澳门/新加坡/中国大陆/百慕大 | 新加坡 |
| 保险公司 | 简称即可 | 国寿（新加坡） |
| 签约产品 | 签约产品全称 | 中国人寿福满终身保险计划至尊版（美元） |
| 保单号 | 保单号 | ********* |
| 缴费期限 | N年 | 5年 |
| 年缴保费 | USD XXX.00 | USD 10,000.00 |
| 生效日期 | 年-月-日 | 2025/6/1 |
| 下次续保 | 应缴保费<br>年-月-日（小字斜体） | USD 10,000.00<br>*2026/6/1* |
| 状态 | 核保中/冷静期/已生效/Pending/已失效 | 冷静期 |

### 订单详情页面
每个订单一共有9种状态，分别为：
1. **预约**
2. **签约**
3. **首年缴费**
4. **保单生效**
5. **保单冷静期**
6. **保单回访**
7. **保单签收**
8. **纸质保单寄送**
9. **保单冷静期结束**
10. 第10种状态及以后为缴费状态，例如第二年缴费、第三年缴费等等以此类推

### 实体：
订单实体、保单实体、受保人实体、持有人实体、状态实体、状态附件实体

## 数据库设计
1、订单表
2、保单表
3、保单历史状态表
4、保单状态附件表

## 订单表(ins_policy_order)

| 字段名                   | 数据类型 | 长度 | 是否必填 | 默认值 | 索引  | 说明                        | 示例                                   |
| ------------------------ | -------- | ---- | -------- | ------ | ----- | --------------------------- | -------------------------------------- |
| id                       | bigint   | -    | 是       | 自增   | PK    | 主键ID                      | 1                                      |
| order_no                 | varchar  | 32   | 否       | null   | UK    | 系统订单号(录入年月日+00XX) | 202505090001                           |
| customer_name            | varchar  | 100  | 否       | null   | -     | 客户姓名                    | 张三                                   |
| policyholder_name_cn     | varchar  | 100  | 否       | null   | -     | 投保人中文名                | 张三                                   |
| policyholder_name_en     | varchar  | 100  | 否       | null   | -     | 投保人英文名(大写)          | ZHANG SAN                              |
| insured_name_cn          | varchar  | 100  | 否       | null   | -     | 被保险人中文名              | 李四                                   |
| insured_name_en          | varchar  | 100  | 否       | null   | -     | 被保险人英文名(大写)        | LI SI                                  |
| region                   | varchar  | 32   | 否       | null   | INDEX | 地区                        | 新加坡                                 |
| company                  | varchar  | 100  | 否       | null   | INDEX | 保险公司                    | 国寿（新加坡）                         |
| product                  | varchar  | 200  | 否       | null   | -     | 签约产品全称                | 中国人寿福满终身保险计划至尊版（美元） |
| phone                    | varchar  | 20   | 否       | null   | -     | 顾问联络电话                | 13800138000                            |
| email                    | varchar  | 100  | 否       | null   | -     | 顾问邮箱                    | <EMAIL>                       |
| team                     | varchar  | 100  | 否       | null   | -     | 团队/主管                   | 销售一组                               |
| referrer                 | varchar  | 100  | 否       | null   | -     | 介绍人                      | 王五                                   |
| sign_time                | bigint   | -    | 否       | null   | -     | 签约时间(时间戳)            | 1715212800000                          |
| payment_term             | varchar  | 32   | 否       | null   | -     | 缴费期限                    | 5年                                    |
| currency                 | varchar  | 10   | 否       | null   | -     | 货币类型                    | USD                                    |
| annual_premium           | decimal  | 20,2 | 否       | null   | -     | 年缴保费                    | 10000.00                               |
| next_renewal_date        | bigint   | -    | 否       | null   | INDEX | 下次续保日期(时间戳)        | 1748880000000                          |
| order_status             | varchar  | 32   | 否       | null   | INDEX | 订单状态(枚举)              | APPOINTMENT                            |
| status_updated_at        | bigint   | -    | 否       | null   | -     | 状态更新时间(时间戳)        | 1715212800000                          |
| user_id                  | bigint   | -    | 否       | null   | INDEX | 关联用户ID                  | 10001                                  |
| created_id               | bigint   | -    | 否       | null   | -     | 订单创建人ID                | 10001                                  |
| created_at               | bigint   | -    | 否       | null   | INDEX | 创建时间(时间戳)            | 1715212800000                          |
| updated_at               | bigint   | -    | 否       | null   | -     | 更新时间(时间戳)            | 1715212800000                          |
| remark                   | text     | -    | 否       | null   | -     | 备注                        | 特殊情况说明                           |

```sql
CREATE TABLE `ins_policy_order` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_no` varchar(32) DEFAULT NULL COMMENT '系统订单号(录入年月日+00XX)',
  `customer_name` varchar(100) DEFAULT NULL COMMENT '客户姓名',
  `policyholder_name_cn` varchar(100) DEFAULT NULL COMMENT '投保人中文名',
  `policyholder_name_en` varchar(100) DEFAULT NULL COMMENT '投保人英文名(大写)',
  `insured_name_cn` varchar(100) DEFAULT NULL COMMENT '被保险人中文名',
  `insured_name_en` varchar(100) DEFAULT NULL COMMENT '被保险人英文名(大写)',
  `region` varchar(32) DEFAULT NULL COMMENT '地区',
  `company` varchar(100) DEFAULT NULL COMMENT '保险公司',
  `product` varchar(200) DEFAULT NULL COMMENT '签约产品全称',
  `phone` varchar(20) DEFAULT NULL COMMENT '顾问联络电话',
  `email` varchar(100) DEFAULT NULL COMMENT '顾问邮箱',
  `team` varchar(100) DEFAULT NULL COMMENT '团队/主管',
  `referrer` varchar(100) DEFAULT NULL COMMENT '介绍人',
  `sign_time` bigint DEFAULT NULL COMMENT '签约时间(时间戳)',
  `payment_term` varchar(32) DEFAULT NULL COMMENT '缴费期限',
  `currency` varchar(10) DEFAULT NULL COMMENT '货币类型',
  `annual_premium` decimal(20,2) DEFAULT NULL COMMENT '年缴保费',
  `next_renewal_date` bigint DEFAULT NULL COMMENT '下次续保日期(时间戳)',
  `order_status` varchar(32) DEFAULT NULL COMMENT '订单状态(枚举)',
  `status_updated_at` bigint DEFAULT NULL COMMENT '状态更新时间(时间戳)',
  `user_id` bigint DEFAULT NULL COMMENT '关联用户ID',
  `created_id` bigint DEFAULT NULL COMMENT '订单创建人ID',
  `created_at` bigint DEFAULT NULL COMMENT '创建时间(时间戳)',
  `updated_at` bigint DEFAULT NULL COMMENT '更新时间(时间戳)',
  `remark` text COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_region` (`region`),
  KEY `idx_company` (`company`),
  KEY `idx_next_renewal_date` (`next_renewal_date`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='保单订单表';
```



## 保单表（ins_policy）

| 字段名 | 数据类型 | 长度 | 是否必填 | 默认值 | 索引 | 说明 | 示例 |
|--------|----------|------|----------|--------|------|------|------|
| id | bigint | - | 是 | 自增 | PK | 主键ID | 1 |
| order_id | bigint | - | 否 | null | FK | 关联订单ID | 1 |
| policy_no | varchar | 32 | 否 | null | UK | 保单号 | ********* |
| policy_status | varchar | 32 | 否 | null | INDEX | 保单状态(枚举) | APPOINTMENT |
| company | varchar | 100 | 否 | null | INDEX | 保险公司 | 国寿（新加坡） |
| product_name | varchar | 200 | 否 | null | - | 产品名称 | 中国人寿福满终身保险计划至尊版 |
| currency | varchar | 10 | 否 | null | - | 货币类型 | USD |
| coverage_amount | decimal | 20,2 | 否 | null | - | 保额 | 100000.00 |
| annual_premium | decimal | 20,2 | 否 | null | - | 年供保费 | 10000.00 |
| payment_method | varchar | 32 | 否 | null | - | 供款方式 | 年缴 |
| first_premium_due_date | bigint | - | 否 | null | INDEX | 首年保费缴费日期(时间戳) | 1717344000000 |
| first_premium_amount | decimal | 20,2 | 否 | null | - | 首年缴费金额 | 10000.00 |
| effective_date | bigint | - | 否 | null | INDEX | 生效日期(时间戳) | 1717344000000 |
| cooling_period_start | bigint | - | 否 | null | - | 冷静期开始时间(时间戳) | 1717344000000 |
| cooling_period_end | bigint | - | 否 | null | INDEX | 冷静期结束时间(时间戳) | 1719158400000 |
| next_renewal_date | bigint | - | 否 | null | INDEX | 下次续保日期(时间戳) | 1748880000000 |
| next_renewal_amount | decimal | 20,2 | 否 | null | - | 下次续保金额 | 10000.00 |
| created_at | bigint | - | 否 | null | INDEX | 创建时间(时间戳) | 1715212800000 |
| updated_at | bigint | - | 否 | null | - | 更新时间(时间戳) | 1715212800000 |

```sql
CREATE TABLE `ins_policy` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` bigint DEFAULT NULL COMMENT '关联订单ID',
  `policy_no` varchar(32) DEFAULT NULL COMMENT '保单号',
  `policy_status` varchar(32) DEFAULT NULL COMMENT '保单状态(枚举)',
  `company` varchar(100) DEFAULT NULL COMMENT '保险公司',
  `product_name` varchar(200) DEFAULT NULL COMMENT '产品名称',
  `currency` varchar(10) DEFAULT NULL COMMENT '货币类型',
  `coverage_amount` decimal(20,2) DEFAULT NULL COMMENT '保额',                             
  `annual_premium` decimal(20,2) DEFAULT NULL COMMENT '年供保费',
  `payment_method` varchar(32) DEFAULT NULL COMMENT '供款方式',
  `first_premium_due_date` bigint DEFAULT NULL COMMENT '首年保费缴费日期(时间戳)',
  `first_premium_amount` decimal(20,2) DEFAULT NULL COMMENT '首年缴费金额',
  `effective_date` bigint DEFAULT NULL COMMENT '生效日期(时间戳)',
  `cooling_period_start` bigint DEFAULT NULL COMMENT '冷静期开始时间(时间戳)',
  `cooling_period_end` bigint DEFAULT NULL COMMENT '冷静期结束时间(时间戳)',
  `next_renewal_date` bigint DEFAULT NULL COMMENT '下次续保日期(时间戳)',
  `next_renewal_amount` decimal(20,2) DEFAULT NULL COMMENT '下次续保金额',
  `created_at` bigint DEFAULT NULL COMMENT '创建时间(时间戳)',
  `updated_at` bigint DEFAULT NULL COMMENT '更新时间(时间戳)',

  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_policy_no` (`policy_no`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_policy_status` (`policy_status`),
  KEY `idx_company` (`company`),
  KEY `idx_first_premium_due_date` (`first_premium_due_date`),
  KEY `idx_effective_date` (`effective_date`),
  KEY `idx_cooling_period_end` (`cooling_period_end`),
  KEY `idx_next_renewal_date` (`next_renewal_date`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='保单表';
```



## 保单状态历史表(ins_policy_status_history)

| 字段名 | 数据类型 | 长度 | 是否必填 | 默认值 | 索引 | 说明 | 示例 |
|--------|----------|------|----------|--------|------|------|------|
| id | bigint | - | 是 | 自增 | PK | 主键ID | 1 |
| policy_id | bigint | - | 否 | null | FK | 保单ID | 1 |
| status_code | varchar | 32 | 否 | null | INDEX | 状态码 | APPOINTMENT |
| record_status | varchar | 32 | 否 | null | INDEX | 保单记录状态（已完成/待完成/pending） | PENDING_COMPLETION |
| updated_at | bigint | - | 否 | null | INDEX | 变更时间(时间戳) | 1715212800000 |
| update_by | bigint | - | 否 | null | - | 操作人ID | 10001 |
| remark | text | - | 否 | null | - | 变更备注 | 客户主动申请状态变更 |
| created_at | bigint | - | 否 | null | INDEX | 创建时间(时间戳) | 1715212800000 |

```sql
CREATE TABLE `ins_policy_status_history` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `policy_id` bigint DEFAULT NULL COMMENT '保单ID',
  `status_code` varchar(32) DEFAULT NULL COMMENT '状态码',
  `record_status` varchar(32) DEFAULT NULL COMMENT '保单记录状态（已完成/待完成/pending）',
  `updated_at` bigint DEFAULT NULL COMMENT '变更时间(时间戳)',
  `update_by` bigint DEFAULT NULL COMMENT '操作人ID',
  `remark` text DEFAULT NULL COMMENT '变更备注',
  `created_at` bigint DEFAULT NULL COMMENT '创建时间(时间戳)',

  PRIMARY KEY (`id`),
  KEY `idx_policy_id` (`policy_id`),
  KEY `idx_status_code` (`status_code`),
  KEY `idx_updated_at` (`updated_at`),
  KEY `idx_record_status` (`record_status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='保单状态历史表';
```



## 保单状态附件表(ins_policy_status_attachment)

| 字段名 | 数据类型 | 长度 | 是否必填 | 默认值 | 索引 | 说明 | 示例 |
|--------|----------|------|----------|--------|------|------|------|
| id | bigint | - | 是 | 自增 | PK | 主键ID | 1 |
| status_history_id | bigint | - | 否 | null | FK | 状态历史ID | 1 |
| file_name | varchar | 255 | 否 | null | - | 文件名 | idcard_front.jpg |
| oss_url | varchar | 500 | 否 | null | - | OSS地址 | https://oss-bucket.com/path/to/file.jpg |
| file_type | varchar | 20 | 否 | null | INDEX | 文件类型(jpg/pdf/doc等) | jpg |
| file_size | bigint | - | 否 | null | - | 文件大小(字节) | 2048576 |
| metadata | varchar | 500 | 否 | null | - | 元数据(JSON格式) | {"width":1920,"height":1080} |
| description | text | - | 否 | null | - | 描述 | 客户身份证正面照片 |
| created_at | bigint | - | 否 | null | INDEX | 创建时间(时间戳) | 1715212800000 |
| updated_at | bigint | - | 否 | null | - | 更新时间(时间戳) | 1715212800000 |

```sql
CREATE TABLE `ins_policy_status_attachment` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `status_history_id` bigint DEFAULT NULL COMMENT '状态历史ID',
  `file_name` varchar(255) DEFAULT NULL COMMENT '文件名',
  `oss_url` varchar(500) DEFAULT NULL COMMENT 'OSS地址',
  `file_type` varchar(20) DEFAULT NULL COMMENT '文件类型(jpg/pdf/doc等)',
  `file_size` bigint DEFAULT NULL COMMENT '文件大小(字节)',
  `metadata` varchar(500) DEFAULT NULL COMMENT '元数据(JSON格式)',
  `description` text DEFAULT NULL COMMENT '描述',
  `created_at` bigint DEFAULT NULL COMMENT '创建时间(时间戳)',
  `updated_at` bigint DEFAULT NULL COMMENT '更新时间(时间戳)',

  PRIMARY KEY (`id`),
  KEY `idx_status_history_id` (`status_history_id`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='保单状态附件表';
```



## 持有人信息表（ins_policy_policyholder_info）

| 字段名               | 数据类型 | 长度 | 是否必填 | 默认值 | 索引  | 说明                    | 示例                            |
| -------------------- | -------- | ---- | -------- | ------ | ----- | ----------------------- | ------------------------------- |
| id                   | bigint   | -    | 是       | 自增   | PK    | 主键ID                  | 1                               |
| policy_id            | bigint   | -    | 否       | null   | INDEX | 关联保单ID              | 1                               |
| name_cn              | varchar  | 100  | 否       | null   | INDEX | 中文姓名                | 张三                            |
| name_en              | varchar  | 100  | 否       | null   | -     | 中文（护照）拼音        | ZHANG SAN                       |
| birth_date           | bigint   | -    | 否       | null   | -     | 出生日期(时间戳)        | 631123200000                    |
| gender               | tinyint  | -    | 否       | null   | -     | 性别(0-女,1-男)         | 1                               |
| id_card_no           | varchar  | 50   | 否       | null   | INDEX | 身份证号码              | 110101199001011234              |
| travel_permit_no     | varchar  | 50   | 否       | null   | -     | 通行证号码              | H12345678                       |
| mobile               | varchar  | 20   | 否       | null   | INDEX | 手机号码                | 13800138000                     |
| email                | varchar  | 100  | 否       | null   | -     | 邮箱                    | <EMAIL>                |
| nationality          | varchar  | 50   | 否       | null   | -     | 国籍                    | 中国                            |
| birth_place          | varchar  | 100  | 否       | null   | -     | 出生地                  | 北京                            |
| marital_status       | varchar  | 20   | 否       | null   | -     | 婚姻状况                | 已婚                            |
| education_level      | varchar  | 50   | 否       | null   | -     | 教育程度                | 本科                            |
| height               | decimal  | 10,2 | 否       | null   | -     | 身高(厘米)              | 175.50                          |
| weight               | decimal  | 10,2 | 否       | null   | -     | 体重(公斤)              | 70.50                           |
| id_card_address      | varchar  | 255  | 否       | null   | -     | 身份证地址              | 北京市朝阳区XX街XX号            |
| residential_address  | varchar  | 255  | 否       | null   | -     | 居住地地址              | 北京市海淀区XX路XX号            |
| mailing_address      | varchar  | 255  | 否       | null   | -     | 通讯地址                | 北京市海淀区XX路XX号            |
| is_smoker            | tinyint  | -    | 否       | null   | -     | 是否吸烟(0-否,1-是)     | 0                               |
| company_name_cn      | varchar  | 200  | 否       | null   | -     | 公司中文名称            | 北京XX科技有限公司              |
| company_name_en      | varchar  | 200  | 否       | null   | -     | 公司英文名称            | Beijing XX Technology Co., Ltd. |
| company_address      | varchar  | 255  | 否       | null   | -     | 公司地址                | 北京市海淀区中关村XX号          |
| company_industry     | varchar  | 100  | 否       | null   | -     | 公司行业                | 互联网                          |
| position             | varchar  | 100  | 否       | null   | -     | 职位                    | 高级工程师                      |
| annual_income        | decimal  | 20,2 | 否       | null   | -     | 年收入                  | 300000.00                       |
| created_at           | bigint   | -    | 否       | null   | INDEX | 创建时间(时间戳)        | 1715212800000                   |
| updated_at           | bigint   | -    | 否       | null   | -     | 更新时间(时间戳)        | 1715212800000                   |

``` sql
CREATE TABLE `ins_policy_policyholder_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `policy_id` bigint DEFAULT NULL COMMENT '关联保单ID',

  -- 个人基本信息
  `name_cn` varchar(100) DEFAULT NULL COMMENT '中文姓名',
  `name_en` varchar(100) DEFAULT NULL COMMENT '中文（护照）拼音',
  `birth_date` bigint DEFAULT NULL COMMENT '出生日期(时间戳)',
  `gender` tinyint DEFAULT NULL COMMENT '性别(0-女,1-男)',
  `id_card_no` varchar(50) DEFAULT NULL COMMENT '身份证号码',
  `travel_permit_no` varchar(50) DEFAULT NULL COMMENT '通行证号码',
  `mobile` varchar(20) DEFAULT NULL COMMENT '手机号码',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `nationality` varchar(50) DEFAULT NULL COMMENT '国籍',
  `birth_place` varchar(100) DEFAULT NULL COMMENT '出生地',
  `marital_status` varchar(20) DEFAULT NULL COMMENT '婚姻状况',
  `education_level` varchar(50) DEFAULT NULL COMMENT '教育程度',
  `height` decimal(10,2) DEFAULT NULL COMMENT '身高(厘米)',
  `weight` decimal(10,2) DEFAULT NULL COMMENT '体重(公斤)',
  `id_card_address` varchar(255) DEFAULT NULL COMMENT '身份证地址',
  `residential_address` varchar(255) DEFAULT NULL COMMENT '居住地地址',
  `mailing_address` varchar(255) DEFAULT NULL COMMENT '通讯地址',
  `is_smoker` tinyint DEFAULT NULL COMMENT '是否吸烟(0-否,1-是)',

  -- 公司信息
  `company_name_cn` varchar(200) DEFAULT NULL COMMENT '公司中文名称',
  `company_name_en` varchar(200) DEFAULT NULL COMMENT '公司英文名称',
  `company_address` varchar(255) DEFAULT NULL COMMENT '公司地址',
  `company_industry` varchar(100) DEFAULT NULL COMMENT '公司行业',
  `position` varchar(100) DEFAULT NULL COMMENT '职位',
  `annual_income` decimal(20,2) DEFAULT NULL COMMENT '年收入',

  -- 审计字段
  `created_at` bigint DEFAULT NULL COMMENT '创建时间(时间戳)',
  `updated_at` bigint DEFAULT NULL COMMENT '更新时间(时间戳)',

  PRIMARY KEY (`id`),
  KEY `idx_policy_id` (`policy_id`),
  KEY `idx_name_cn` (`name_cn`),
  KEY `idx_id_card_no` (`id_card_no`),
  KEY `idx_mobile` (`mobile`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='保单持有人信息表';
```

## 受保人信息表(ins_policy_insured_info)

| 字段名               | 数据类型 | 长度 | 是否必填 | 默认值 | 索引  | 说明                    | 示例                 |
| -------------------- | -------- | ---- | -------- | ------ | ----- | ----------------------- | -------------------- |
| id                   | bigint   | -    | 是       | 自增   | PK    | 主键ID                  | 1                    |
| policy_id            | bigint   | -    | 否       | null   | INDEX | 关联保单ID              | 1                    |
| name_cn              | varchar  | 100  | 否       | null   | INDEX | 中文姓名                | 李四                 |
| name_en              | varchar  | 100  | 否       | null   | -     | 中文（护照）拼音        | LI SI                |
| birth_date           | bigint   | -    | 否       | null   | -     | 出生日期(时间戳)        | 631123200000         |
| gender               | tinyint  | -    | 否       | null   | -     | 性别(0-女,1-男)         | 1                    |
| id_card_no           | varchar  | 50   | 否       | null   | INDEX | 身份证号码              | 110101199001011234   |
| travel_permit_no     | varchar  | 50   | 否       | null   | -     | 通行证号码              | H12345678            |
| mobile               | varchar  | 20   | 否       | null   | INDEX | 手机号码                | 13800138000          |
| email                | varchar  | 100  | 否       | null   | -     | 邮箱                    | <EMAIL>     |
| nationality          | varchar  | 50   | 否       | null   | -     | 国籍                    | 中国                 |
| marital_status       | varchar  | 20   | 否       | null   | -     | 婚姻状况                | 已婚                 |
| education_level      | varchar  | 50   | 否       | null   | -     | 教育程度                | 本科                 |
| height               | decimal  | 10,2 | 否       | null   | -     | 身高(厘米)              | 175.50               |
| weight               | decimal  | 10,2 | 否       | null   | -     | 体重(公斤)              | 70.50                |
| id_card_address      | varchar  | 255  | 否       | null   | -     | 身份证地址              | 北京市朝阳区XX街XX号 |
| is_smoker            | tinyint  | -    | 否       | null   | -     | 是否吸烟(0-否,1-是)     | 0                    |
| created_at           | bigint   | -    | 否       | null   | INDEX | 创建时间(时间戳)        | 1715212800000        |
| updated_at           | bigint   | -    | 否       | null   | -     | 更新时间(时间戳)        | 1715212800000        |

```sql
CREATE TABLE `ins_policy_insured_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `policy_id` bigint DEFAULT NULL COMMENT '关联保单ID',

  -- 个人基本信息
  `name_cn` varchar(100) DEFAULT NULL COMMENT '中文姓名',
  `name_en` varchar(100) DEFAULT NULL COMMENT '中文（护照）拼音',
  `birth_date` bigint DEFAULT NULL COMMENT '出生日期(时间戳)',
  `gender` tinyint DEFAULT NULL COMMENT '性别(0-女,1-男)',
  `id_card_no` varchar(50) DEFAULT NULL COMMENT '身份证号码',
  `travel_permit_no` varchar(50) DEFAULT NULL COMMENT '通行证号码',
  `mobile` varchar(20) DEFAULT NULL COMMENT '手机号码',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `nationality` varchar(50) DEFAULT NULL COMMENT '国籍',
  `marital_status` varchar(20) DEFAULT NULL COMMENT '婚姻状况',
  `education_level` varchar(50) DEFAULT NULL COMMENT '教育程度',
  `height` decimal(10,2) DEFAULT NULL COMMENT '身高(厘米)',
  `weight` decimal(10,2) DEFAULT NULL COMMENT '体重(公斤)',
  `id_card_address` varchar(255) DEFAULT NULL COMMENT '身份证地址',
  `is_smoker` tinyint DEFAULT NULL COMMENT '是否吸烟(0-否,1-是)',

  -- 审计字段
  `created_at` bigint DEFAULT NULL COMMENT '创建时间(时间戳)',
  `updated_at` bigint DEFAULT NULL COMMENT '更新时间(时间戳)',

  PRIMARY KEY (`id`),
  KEY `idx_policy_id` (`policy_id`),
  KEY `idx_name_cn` (`name_cn`),
  KEY `idx_id_card_no` (`id_card_no`),
  KEY `idx_mobile` (`mobile`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='受保人信息表';
```

## 受益人/信托人信息表(ins_policy_beneficiary_info)

| 字段名             | 数据类型 | 长度 | 是否必填 | 默认值 | 索引  | 说明                    | 示例               |
| ------------------ | -------- | ---- | -------- | ------ | ----- | ----------------------- | ------------------ |
| id                 | bigint   | -    | 是       | 自增   | PK    | 主键ID                  | 1                  |
| policy_id          | bigint   | -    | 否       | null   | INDEX | 关联保单ID              | 1                  |
| name               | varchar  | 100  | 否       | null   | INDEX | 受益人姓名              | 王五               |
| gender             | tinyint  | -    | 否       | null   | -     | 性别(0-女,1-男)         | 1                  |
| relationship       | varchar  | 50   | 否       | null   | -     | 与受保人关系            | 父亲               |
| id_card_no         | varchar  | 50   | 否       | null   | INDEX | 身份证号码              | 110101196001011234 |
| benefit_percentage | decimal  | 5,2  | 否       | null   | -     | 受益比例(%)             | 50.00              |
| is_trustee         | tinyint  | -    | 否       | null   | -     | 是否为信托人(0-否,1-是) | 1                  |
| created_at         | bigint   | -    | 否       | null   | INDEX | 创建时间(时间戳)        | 1715212800000      |
| updated_at         | bigint   | -    | 否       | null   | -     | 更新时间(时间戳)        | 1715212800000      |

```sql
CREATE TABLE `ins_policy_beneficiary_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `policy_id` bigint DEFAULT NULL COMMENT '关联保单ID',
  `name` varchar(100) DEFAULT NULL COMMENT '受益人姓名',
  `gender` tinyint DEFAULT NULL COMMENT '性别(0-女,1-男)',
  `relationship` varchar(50) DEFAULT NULL COMMENT '与受保人关系',
  `id_card_no` varchar(50) DEFAULT NULL COMMENT '身份证号码',
  `benefit_percentage` decimal(5,2) DEFAULT NULL COMMENT '受益比例(%)',
  `is_trustee` tinyint DEFAULT NULL COMMENT '是否为信托人(0-否,1-是)',
  `created_at` bigint DEFAULT NULL COMMENT '创建时间(时间戳)',
  `updated_at` bigint DEFAULT NULL COMMENT '更新时间(时间戳)',

  PRIMARY KEY (`id`),
  KEY `idx_policy_id` (`policy_id`),
  KEY `idx_name` (`name`),
  KEY `idx_id_card_no` (`id_card_no`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='受益人/信托人信息表';
```

