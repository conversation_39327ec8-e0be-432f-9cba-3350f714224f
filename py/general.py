import pdfplumber
import re
import json
import os

# 配置
config = {
    "path": './123.pdf',
    "section_definitions":
        {
            "name": "退保收益",
            "start": "補充利益說明",
            "end": ""
        },
    "target_pages": [18, 19, 20],  # 指定要提取的页数，如果为None则使用关键字提取
    "output_path_template": 'output.json'
}

def _parse_data_rows(section_text):
    # 直接处理每行数据，不使用rows列表存储
    data_rows = []

    for line in section_text.splitlines():
        if re.match(r"^\d+", line) and not re.match(r"^\d+歲", line):
            nums = re.findall(r"[\d,]+", line)

            try:
                if len(nums) < 2:  # 至少需要保单年度和一个数值
                    continue

                policy_year = int(nums[0].replace(",", ""))
                # 直接构建该行数据
                row_data = {"policyYear": policy_year}  # 保单年度

                # 定义要提取的列索引（除了第0列保单年度）
                target_columns = [1, 3, 4, 5, 6]
                column_names = [
                    "totalPremiumPaid",      # 缴付保费总额
                    "totalWithdrawnBonus",   # 已提取红利总额
                    "surrenderGuaranteed",    # 退保发还保证金额
                    "terminalBonus",          # 终期红利
                    "totalAmount"             # 总额
                ]

                # 提取指定列的数据
                for i, col_idx in enumerate(target_columns):
                    if col_idx < len(nums) and i < len(column_names):
                        try:
                            row_data[column_names[i]] = int(nums[col_idx].replace(",", ""))
                        except ValueError:
                            row_data[column_names[i]] = 0

                # 直接将row_data添加到结果中
                data_rows.append(row_data)

            except (ValueError, IndexError) as e:
                continue

    return data_rows

def extract_age_from_pdf(pdf_text):
    # 针对"年齡 : 10"格式的正则表达式
    age_pattern = r'年齡\s*[:：]\s*(\d+)'
    match = re.search(age_pattern, pdf_text)
    if match:
        return match.group(1)
    return None

def process_pdf(pdf_path, section, target_pages=None):
    # 从文件路径中提取基本文件名（不含扩展名）
    base_name = re.sub(r"\.\w+$", "", os.path.basename(pdf_path))

    with pdfplumber.open(pdf_path) as pdf:
        # 如果指定了目标页数，只提取这些页的文本
        if target_pages:
            target_text = ""
            for page_num in target_pages:
                # 页数从0开始，所以需要减1
                if 0 <= page_num - 1 < len(pdf.pages):
                    page_text = pdf.pages[page_num - 1].extract_text()
                    if page_text:
                        target_text += page_text + "\n"
            full_text = target_text
        else:
            # 原有逻辑：提取所有页面文本
            full_text = "\n".join(page.extract_text() for page in pdf.pages if page.extract_text())

    # 尝试从PDF中提取年龄（从所有页面中提取，不限制页数）
    with pdfplumber.open(pdf_path) as pdf:
        all_text = "\n".join(page.extract_text() for page in pdf.pages if page.extract_text())
    extracted_age = extract_age_from_pdf(all_text)

    product_data = None

    if target_pages:
        # 基于页数的提取：直接解析指定页面的文本
        data_rows = _parse_data_rows(full_text)
        if data_rows:
            # 直接返回单个对象，不包装在列表中
            product_data = {
                "baseAmount": 4000,
                "data": data_rows
            }
    else:
        # 原有逻辑：基于关键字的提取
        start_idx = full_text.find(section["start"])
        if start_idx != -1:
            end_keyword = section.get("end", "")  # 安全获取 end 字段，默认为空字符串
            if not end_keyword or end_keyword.strip() == "":
                section_text = full_text[start_idx:]
            else:
                end_idx = full_text.find(end_keyword, start_idx)
                if end_idx == -1:
                    print(f"End keyword '{end_keyword}' not found. Using full text after start.")
                    section_text = full_text[start_idx:]
                else:
                    section_text = full_text[start_idx:end_idx]

            # 解析数据并获取所有行
            data_rows = _parse_data_rows(section_text)

            if data_rows:
                # 直接返回单个对象，不包装在列表中
                product_data = {
                    "productName": base_name,
                    "data": data_rows
                }

    return product_data, extracted_age

# 主流程
def main():
    print(f"\nProcessing PDF: {config['path']}")
    pdf_path = config['path']

    # 获取目标页数配置
    target_pages = config.get('target_pages', None)

    if target_pages:
        print(f"Using page-based extraction for pages: {target_pages}")
    else:
        print("Using keyword-based extraction")

    # 处理PDF并获取提取的年龄
    product_data, extracted_age = process_pdf(pdf_path, config['section_definitions'], target_pages)

    # 根据提取的年龄生成输出文件名
    output_path = config['output_path_template'].format(age=extracted_age if extracted_age else "unknown")

    if product_data:
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(product_data, f, ensure_ascii=False, indent=2)
        print(f"\nAll data saved to {output_path}")
    else:
        print("\nNo data was processed.")

if __name__ == "__main__":
    main()
